"""
Basic tests for the kaggle-data-project package.
"""

import sys
from pathlib import Path
import pytest
import pandas as pd
import numpy as np

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from kaggle_data_project import DataProcessor, Visualizer, PDFHandler


class TestDataProcessor:
    """Test cases for DataProcessor."""
    
    def setup_method(self):
        """Set up test data."""
        self.processor = DataProcessor()
        self.sample_data = pd.DataFrame({
            'A': [1, 2, 3, 4, 5],
            'B': [10, 20, 30, 40, 50],
            'C': ['x', 'y', 'z', 'x', 'y']
        })
        self.processor.data = self.sample_data
    
    def test_basic_info(self):
        """Test basic_info method."""
        info = self.processor.basic_info()
        
        assert 'shape' in info
        assert 'columns' in info
        assert 'dtypes' in info
        assert 'missing_values' in info
        
        assert info['shape'] == (5, 3)
        assert len(info['columns']) == 3
    
    def test_numerical_summary(self):
        """Test get_numerical_summary method."""
        summary = self.processor.get_numerical_summary()
        
        assert isinstance(summary, pd.DataFrame)
        assert 'A' in summary.columns
        assert 'B' in summary.columns
        assert 'C' not in summary.columns  # Should exclude non-numeric
    
    def test_categorical_summary(self):
        """Test get_categorical_summary method."""
        summary = self.processor.get_categorical_summary()
        
        assert isinstance(summary, dict)
        assert 'C' in summary
        assert isinstance(summary['C'], pd.Series)
    
    def test_filter_data(self):
        """Test filter_data method."""
        # Test simple filter
        filtered = self.processor.filter_data({'C': 'x'})
        assert len(filtered) == 2
        assert all(filtered['C'] == 'x')
        
        # Test range filter
        filtered = self.processor.filter_data({'A': {'min': 3, 'max': 4}})
        assert len(filtered) == 2
        assert all(filtered['A'].between(3, 4))


class TestVisualizer:
    """Test cases for Visualizer."""
    
    def setup_method(self):
        """Set up test data."""
        self.viz = Visualizer()
        self.sample_data = pd.DataFrame({
            'x': np.linspace(0, 10, 50),
            'y': np.sin(np.linspace(0, 10, 50)),
            'category': np.random.choice(['A', 'B'], 50)
        })
    
    def test_visualizer_init(self):
        """Test Visualizer initialization."""
        assert self.viz.figsize == (10, 6)
        assert self.viz.current_fig is None
    
    def test_line_plot_creation(self):
        """Test line plot creation."""
        fig = self.viz.line_plot(self.sample_data, 'x', 'y', title='Test Plot')
        
        assert fig is not None
        assert self.viz.current_fig is not None
        
        # Clean up
        self.viz.close()
    
    def test_scatter_plot_creation(self):
        """Test scatter plot creation."""
        fig = self.viz.scatter_plot(self.sample_data, 'x', 'y', title='Test Scatter')
        
        assert fig is not None
        assert self.viz.current_fig is not None
        
        # Clean up
        self.viz.close()


class TestPDFHandler:
    """Test cases for PDFHandler."""
    
    def setup_method(self):
        """Set up PDF handler."""
        self.pdf_handler = PDFHandler()
    
    def test_pdf_handler_init(self):
        """Test PDFHandler initialization."""
        assert self.pdf_handler.current_pdf is None
        assert self.pdf_handler.current_path is None
    
    def test_load_nonexistent_pdf(self):
        """Test loading a non-existent PDF."""
        with pytest.raises(FileNotFoundError):
            self.pdf_handler.load_pdf('nonexistent.pdf')
    
    def test_operations_without_loaded_pdf(self):
        """Test that operations fail without loaded PDF."""
        with pytest.raises(ValueError):
            self.pdf_handler.get_pdf_info()
        
        with pytest.raises(ValueError):
            self.pdf_handler.extract_text()


def test_imports():
    """Test that all modules can be imported."""
    from kaggle_data_project import DataProcessor, Visualizer, PDFHandler
    
    # Test that classes can be instantiated
    processor = DataProcessor()
    visualizer = Visualizer()
    pdf_handler = PDFHandler()
    
    assert processor is not None
    assert visualizer is not None
    assert pdf_handler is not None


if __name__ == "__main__":
    pytest.main([__file__])
