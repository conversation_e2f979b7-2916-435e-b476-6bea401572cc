"""
PDF handling utilities using pypdf.
"""

from pypdf import <PERSON>d<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from pathlib import Path
from typing import List, Optional, Dict, Any
import re


class PDFHandler:
    """A class for handling PDF operations."""
    
    def __init__(self):
        self.current_pdf: Optional[PdfReader] = None
        self.current_path: Optional[str] = None
    
    def load_pdf(self, file_path: str) -> PdfReader:
        """
        Load a PDF file.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            PdfReader object
        """
        if not Path(file_path).exists():
            raise FileNotFoundError(f"PDF file not found: {file_path}")
        
        self.current_pdf = PdfReader(file_path)
        self.current_path = file_path
        return self.current_pdf
    
    def get_pdf_info(self) -> Dict[str, Any]:
        """
        Get basic information about the loaded PDF.
        
        Returns:
            Dictionary with PDF information
        """
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        info = {
            'num_pages': len(self.current_pdf.pages),
            'file_path': self.current_path,
        }
        
        # Try to get metadata
        try:
            metadata = self.current_pdf.metadata
            if metadata:
                info.update({
                    'title': metadata.get('/Title', 'N/A'),
                    'author': metadata.get('/Author', 'N/A'),
                    'subject': metadata.get('/Subject', 'N/A'),
                    'creator': metadata.get('/Creator', 'N/A'),
                    'producer': metadata.get('/Producer', 'N/A'),
                    'creation_date': metadata.get('/CreationDate', 'N/A'),
                    'modification_date': metadata.get('/ModDate', 'N/A'),
                })
        except Exception as e:
            info['metadata_error'] = str(e)
        
        return info
    
    def extract_text(self, page_numbers: Optional[List[int]] = None) -> str:
        """
        Extract text from PDF pages.
        
        Args:
            page_numbers: List of page numbers to extract (0-indexed). 
                         If None, extracts from all pages.
            
        Returns:
            Extracted text
        """
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        if page_numbers is None:
            page_numbers = list(range(len(self.current_pdf.pages)))
        
        extracted_text = []
        
        for page_num in page_numbers:
            if 0 <= page_num < len(self.current_pdf.pages):
                page = self.current_pdf.pages[page_num]
                try:
                    text = page.extract_text()
                    extracted_text.append(f"--- Page {page_num + 1} ---\n{text}\n")
                except Exception as e:
                    extracted_text.append(f"--- Page {page_num + 1} (Error) ---\nError extracting text: {str(e)}\n")
            else:
                extracted_text.append(f"--- Page {page_num + 1} (Invalid) ---\nPage number out of range\n")
        
        return "\n".join(extracted_text)
    
    def extract_text_by_page(self) -> Dict[int, str]:
        """
        Extract text from all pages, returning a dictionary with page numbers as keys.
        
        Returns:
            Dictionary mapping page numbers (1-indexed) to extracted text
        """
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        text_by_page = {}
        
        for i, page in enumerate(self.current_pdf.pages):
            try:
                text = page.extract_text()
                text_by_page[i + 1] = text
            except Exception as e:
                text_by_page[i + 1] = f"Error extracting text: {str(e)}"
        
        return text_by_page
    
    def search_text(self, pattern: str, case_sensitive: bool = False) -> List[Dict[str, Any]]:
        """
        Search for text patterns in the PDF.
        
        Args:
            pattern: Text pattern to search for (supports regex)
            case_sensitive: Whether the search should be case-sensitive
            
        Returns:
            List of dictionaries with search results
        """
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        flags = 0 if case_sensitive else re.IGNORECASE
        results = []
        
        for i, page in enumerate(self.current_pdf.pages):
            try:
                text = page.extract_text()
                matches = re.finditer(pattern, text, flags)
                
                for match in matches:
                    results.append({
                        'page': i + 1,
                        'match': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'context': self._get_context(text, match.start(), match.end())
                    })
            except Exception as e:
                results.append({
                    'page': i + 1,
                    'error': f"Error searching page: {str(e)}"
                })
        
        return results
    
    def split_pdf(self, output_dir: str, pages_per_file: int = 1) -> List[str]:
        """
        Split PDF into smaller files.
        
        Args:
            output_dir: Directory to save split files
            pages_per_file: Number of pages per output file
            
        Returns:
            List of created file paths
        """
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        created_files = []
        total_pages = len(self.current_pdf.pages)
        
        for start_page in range(0, total_pages, pages_per_file):
            end_page = min(start_page + pages_per_file, total_pages)
            
            writer = PdfWriter()
            
            for page_num in range(start_page, end_page):
                writer.add_page(self.current_pdf.pages[page_num])
            
            # Create filename
            if pages_per_file == 1:
                filename = f"page_{start_page + 1}.pdf"
            else:
                filename = f"pages_{start_page + 1}_to_{end_page}.pdf"
            
            output_file = output_path / filename
            
            with open(output_file, 'wb') as output_pdf:
                writer.write(output_pdf)
            
            created_files.append(str(output_file))
        
        return created_files
    
    def merge_pdfs(self, pdf_paths: List[str], output_path: str) -> str:
        """
        Merge multiple PDF files into one.
        
        Args:
            pdf_paths: List of PDF file paths to merge
            output_path: Path for the merged PDF
            
        Returns:
            Path of the created merged PDF
        """
        writer = PdfWriter()
        
        for pdf_path in pdf_paths:
            if not Path(pdf_path).exists():
                raise FileNotFoundError(f"PDF file not found: {pdf_path}")
            
            reader = PdfReader(pdf_path)
            for page in reader.pages:
                writer.add_page(page)
        
        # Ensure output directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'wb') as output_pdf:
            writer.write(output_pdf)
        
        return output_path
    
    def save_text_to_file(self, output_path: str, page_numbers: Optional[List[int]] = None) -> str:
        """
        Extract text and save to a text file.
        
        Args:
            output_path: Path to save the text file
            page_numbers: List of page numbers to extract (0-indexed)
            
        Returns:
            Path of the created text file
        """
        text = self.extract_text(page_numbers)
        
        # Ensure output directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)
        
        return output_path
    
    def _get_context(self, text: str, start: int, end: int, context_length: int = 50) -> str:
        """
        Get context around a match.
        
        Args:
            text: Full text
            start: Start position of match
            end: End position of match
            context_length: Number of characters to include before and after
            
        Returns:
            Context string
        """
        context_start = max(0, start - context_length)
        context_end = min(len(text), end + context_length)
        
        context = text[context_start:context_end]
        
        # Add ellipsis if we truncated
        if context_start > 0:
            context = "..." + context
        if context_end < len(text):
            context = context + "..."
        
        return context
