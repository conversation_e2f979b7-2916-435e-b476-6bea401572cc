<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-archivearticle1-mathml3.dtd?><?SourceDTD.Version 1.2?><?ConverterInfo.XSLTName jats2jats3.xsl?><?ConverterInfo.Version 1?><?subarticle sa0?><?properties open_access?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">eLife</journal-id><journal-id journal-id-type="iso-abbrev">Elife</journal-id><journal-id journal-id-type="publisher-id">eLife</journal-id><journal-title-group><journal-title>eLife</journal-title></journal-title-group><issn pub-type="epub">2050-084X</issn><publisher><publisher-name>eLife Sciences Publications, Ltd</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">9018074</article-id><article-id pub-id-type="pmid">35164900</article-id><article-id pub-id-type="publisher-id">72626</article-id><article-id pub-id-type="doi">10.7554/eLife.72626</article-id><article-categories><subj-group subj-group-type="display-channel"><subject>Research Article</subject></subj-group><subj-group subj-group-type="heading"><subject>Computational and Systems Biology</subject></subj-group></article-categories><title-group><article-title>Patient-specific Boolean models of signalling networks guide personalised treatments</article-title></title-group><contrib-group><contrib id="author-247852" contrib-type="author" corresp="yes"><name><surname>Montagud</surname><given-names>Arnau</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-7696-1241</contrib-id><email><EMAIL></email><xref rid="aff1" ref-type="aff">1</xref><xref rid="aff2" ref-type="aff">2</xref><xref rid="aff3" ref-type="aff">3</xref><xref rid="aff4" ref-type="aff">4</xref><xref rid="fund1" ref-type="other"/><xref rid="fund2" ref-type="other"/><xref rid="fund3" ref-type="other"/><xref rid="con1" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-247831" contrib-type="author"><name><surname>B&#x000e9;al</surname><given-names>Jonas</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0003-1949-9801</contrib-id><xref rid="aff1" ref-type="aff">1</xref><xref rid="aff2" ref-type="aff">2</xref><xref rid="aff3" ref-type="aff">3</xref><xref rid="fund1" ref-type="other"/><xref rid="con2" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-247832" contrib-type="author"><name><surname>Tobalina</surname><given-names>Luis</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-1947-8309</contrib-id><xref rid="aff5" ref-type="aff">5</xref><xref rid="pa1" ref-type="author-notes">&#x02021;</xref><xref rid="fund1" ref-type="other"/><xref rid="con3" ref-type="fn"/><xref rid="conf2" ref-type="fn"/></contrib><contrib id="author-247833" contrib-type="author"><name><surname>Traynard</surname><given-names>Pauline</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-4835-9114</contrib-id><xref rid="aff1" ref-type="aff">1</xref><xref rid="aff2" ref-type="aff">2</xref><xref rid="aff3" ref-type="aff">3</xref><xref rid="fund1" ref-type="other"/><xref rid="con4" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-247834" contrib-type="author"><name><surname>Subramanian</surname><given-names>Vigneshwari</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-7319-8885</contrib-id><xref rid="aff5" ref-type="aff">5</xref><xref rid="pa2" ref-type="author-notes">&#x000a7;</xref><xref rid="fund1" ref-type="other"/><xref rid="con5" ref-type="fn"/><xref rid="conf3" ref-type="fn"/></contrib><contrib id="author-247835" contrib-type="author"><name><surname>Szalai</surname><given-names>Bence</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-9320-5704</contrib-id><xref rid="aff5" ref-type="aff">5</xref><xref rid="aff6" ref-type="aff">6</xref><xref rid="fund1" ref-type="other"/><xref rid="con6" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-247836" contrib-type="author"><name><surname>Alf&#x000f6;ldi</surname><given-names>R&#x000f3;bert</given-names></name><xref rid="aff7" ref-type="aff">7</xref><xref rid="fund1" ref-type="other"/><xref rid="con7" ref-type="fn"/><xref rid="conf4" ref-type="fn"/></contrib><contrib id="author-247837" contrib-type="author"><name><surname>Pusk&#x000e1;s</surname><given-names>L&#x000e1;szl&#x000f3;</given-names></name><xref rid="aff7" ref-type="aff">7</xref><xref rid="fund1" ref-type="other"/><xref rid="con8" ref-type="fn"/><xref rid="conf5" ref-type="fn"/></contrib><contrib id="author-7284" contrib-type="author"><name><surname>Valencia</surname><given-names>Alfonso</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-8937-6789</contrib-id><xref rid="aff4" ref-type="aff">4</xref><xref rid="aff8" ref-type="aff">8</xref><xref rid="fund2" ref-type="other"/><xref rid="fund3" ref-type="other"/><xref rid="con9" ref-type="fn"/><xref rid="conf6" ref-type="fn"/></contrib><contrib id="author-34780" contrib-type="author"><name><surname>Barillot</surname><given-names>Emmanuel</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0003-2724-2002</contrib-id><xref rid="aff1" ref-type="aff">1</xref><xref rid="aff2" ref-type="aff">2</xref><xref rid="aff3" ref-type="aff">3</xref><xref rid="fund1" ref-type="other"/><xref rid="fund3" ref-type="other"/><xref rid="con10" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-156760" contrib-type="author" equal-contrib="yes"><name><surname>Saez-Rodriguez</surname><given-names>Julio</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-8552-8976</contrib-id><xref rid="aff5" ref-type="aff">5</xref><xref rid="aff9" ref-type="aff">9</xref><xref rid="equal-contrib1" ref-type="author-notes">&#x02020;</xref><xref rid="fund1" ref-type="other"/><xref rid="fund3" ref-type="other"/><xref rid="con11" ref-type="fn"/><xref rid="conf7" ref-type="fn"/></contrib><contrib id="author-148074" contrib-type="author" corresp="yes" equal-contrib="yes"><name><surname>Calzone</surname><given-names>Laurence</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-7835-1148</contrib-id><email><EMAIL></email><xref rid="aff1" ref-type="aff">1</xref><xref rid="aff2" ref-type="aff">2</xref><xref rid="aff3" ref-type="aff">3</xref><xref rid="equal-contrib1" ref-type="author-notes">&#x02020;</xref><xref rid="fund1" ref-type="other"/><xref rid="fund3" ref-type="other"/><xref rid="con12" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><aff id="aff1">
<label>1</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/04t0gwh46</institution-id><institution>Institut Curie, PSL Research University</institution></institution-wrap>
<addr-line>Paris</addr-line>
<country>France</country>
</aff><aff id="aff2">
<label>2</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/02vjkv261</institution-id><institution>INSERM, U900</institution></institution-wrap>
<addr-line>Paris</addr-line>
<country>France</country>
</aff><aff id="aff3">
<label>3</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/04y8cs423</institution-id><institution>MINES ParisTech, PSL Research University, CBIO-Centre for Computational Biology</institution></institution-wrap>
<addr-line>Paris</addr-line>
<country>France</country>
</aff><aff id="aff4">
<label>4</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/05sd8tv96</institution-id><institution>Barcelona Supercomputing Center (BSC), Pla&#x000e7;a Eusebi G&#x000fc;ell, 1-3</institution></institution-wrap>
<addr-line>Barcelona</addr-line>
<country>Spain</country>
</aff><aff id="aff5">
<label>5</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/04xfq0f34</institution-id><institution>Faculty of Medicine, Joint Research Centre for Computational Biomedicine (JRC-COMBINE), RWTH Aachen University</institution></institution-wrap>
<addr-line>Aachen</addr-line>
<country>Germany</country>
</aff><aff id="aff6">
<label>6</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/01g9ty582</institution-id><institution>Semmelweis University, Faculty of Medicine, Department of Physiology</institution></institution-wrap>
<addr-line>Budapest</addr-line>
<country>Hungary</country>
</aff><aff id="aff7">
<label>7</label>
<institution>Astridbio Technologies Ltd</institution>
<addr-line>Szeged</addr-line>
<country>Hungary</country>
</aff><aff id="aff8">
<label>8</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/0371hy230</institution-id><institution>ICREA, Pg. Llu&#x000ed;s Companys 23</institution></institution-wrap>
<addr-line>Barcelona</addr-line>
<country>Spain</country>
</aff><aff id="aff9">
<label>9</label>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/038t36y30</institution-id><institution>Faculty of Medicine and Heidelberg University Hospital, Institute of Computational Biomedicine, Heidelberg University</institution></institution-wrap>
<addr-line>Heidelberg</addr-line>
<country>Germany</country>
</aff></contrib-group><contrib-group><contrib contrib-type="editor"><name><surname>Flegg</surname><given-names>Jennifer</given-names></name><role>Reviewing Editor</role><aff>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/01ej9dk98</institution-id><institution>The University of Melbourne</institution></institution-wrap>
<country>Australia</country>
</aff></contrib><contrib contrib-type="editor"><name><surname>Walczak</surname><given-names>Aleksandra M</given-names></name><role>Senior Editor</role><aff>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/05a0dhs15</institution-id><institution>&#x000c9;cole Normale Sup&#x000e9;rieure</institution></institution-wrap>
<country>France</country>
</aff></contrib></contrib-group><author-notes><fn fn-type="con" id="equal-contrib1"><label>&#x02020;</label><p>These authors contributed equally to this work.</p></fn><fn fn-type="present-address" id="pa1"><label>&#x02021;</label><p>Bioinformatics and Data Science, Research and Early Development, Oncology R&#x00026;D, AstraZeneca, Cambridge, United Kingdom.</p></fn><fn fn-type="present-address" id="pa2"><label>&#x000a7;</label><p>Data Science &#x00026; Artificial Intelligence, Imaging &#x00026; Data Analytics, Clinical Pharmacology &#x00026; Safety Sciences, R&#x00026;D, AstraZeneca, Gothenburg, Sweden.</p></fn></author-notes><pub-date date-type="pub" publication-format="electronic"><day>15</day><month>2</month><year>2022</year></pub-date><pub-date pub-type="collection"><year>2022</year></pub-date><volume>11</volume><elocation-id>e72626</elocation-id><history>
<date date-type="received" iso-8601-date="2021-07-30"><day>30</day><month>7</month><year>2021</year></date>
<date date-type="accepted" iso-8601-date="2022-01-27"><day>27</day><month>1</month><year>2022</year></date>
</history><pub-history><event><event-desc>This manuscript was published as a preprint at .</event-desc><date date-type="preprint" iso-8601-date="2021-07-29"><day>29</day><month>7</month><year>2021</year></date><self-uri content-type="preprint" xlink:href="https://doi.org/10.1101/2021.07.28.454126"/></event></pub-history><permissions><copyright-statement>&#x000a9; 2022, Montagud et al</copyright-statement><copyright-year>2022</copyright-year><copyright-holder>Montagud et al</copyright-holder><ali:free_to_read xmlns:ali="http://www.niso.org/schemas/ali/1.0/"/><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This article is distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use and redistribution provided that the original author and source are credited.</license-p></license></permissions><self-uri content-type="pdf" xlink:href="elife-72626.pdf"/><abstract><p>Prostate cancer is the second most occurring cancer in men worldwide. To better understand the mechanisms of tumorigenesis and possible treatment responses, we developed a mathematical model of prostate cancer which considers the major signalling pathways known to be deregulated. We personalised this Boolean model to molecular data to reflect the heterogeneity and specific response to perturbations of cancer patients. A total of 488 prostate samples were used to build patient-specific models and compared to available clinical data. Additionally, eight prostate cell line-specific models were built to validate our approach with dose-response data of several drugs. The effects of single and combined drugs were tested in these models under different growth conditions. We identified 15 actionable points of interventions in one cell line-specific model whose inactivation hinders tumorigenesis. To validate these results, we tested nine small molecule inhibitors of five of those putative targets and found a dose-dependent effect on four of them, notably those targeting HSP90 and PI3K. These results highlight the predictive power of our personalised Boolean models and illustrate how they can be used for precision oncology.</p></abstract><kwd-group kwd-group-type="author-keywords"><kwd>personalised medicine</kwd><kwd>logical modelling</kwd><kwd>prostate cancer</kwd><kwd>personalised drug</kwd><kwd>simulations</kwd><kwd>drug combinations</kwd></kwd-group><kwd-group kwd-group-type="research-organism"><title>Research organism</title><kwd>Human</kwd></kwd-group><funding-group><award-group id="fund1"><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000780</institution-id><institution>European Commission</institution></institution-wrap>
</funding-source><award-id>H2020-PHC-668858</award-id><principal-award-recipient>
<name><surname>Montagud</surname><given-names>Arnau</given-names></name>
<name><surname>B&#x000e9;al</surname><given-names>Jonas</given-names></name>
<name><surname>Tobalina</surname><given-names>Luis</given-names></name>
<name><surname>Traynard</surname><given-names>Pauline</given-names></name>
<name><surname>Subramanian</surname><given-names>Vigneshwari</given-names></name>
<name><surname>Szalai</surname><given-names>Bence</given-names></name>
<name><surname>Alf&#x000f6;ldi</surname><given-names>R&#x000f3;bert</given-names></name>
<name><surname>Pusk&#x000e1;s</surname><given-names>L&#x000e1;szl&#x000f3;</given-names></name>
<name><surname>Barillot</surname><given-names>Emmanuel</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>Julio</given-names></name>
<name><surname>Calzone</surname><given-names>Laurence</given-names></name>
</principal-award-recipient></award-group><award-group id="fund2"><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000780</institution-id><institution>European Commission</institution></institution-wrap>
</funding-source><award-id>H2020-ICT-825070</award-id><principal-award-recipient>
<name><surname>Montagud</surname><given-names>Arnau</given-names></name>
<name><surname>Valencia</surname><given-names>Alfonso</given-names></name>
</principal-award-recipient></award-group><award-group id="fund3"><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000780</institution-id><institution>European Commission</institution></institution-wrap>
</funding-source><award-id>H2020-ICT-951773</award-id><principal-award-recipient>
<name><surname>Montagud</surname><given-names>Arnau</given-names></name>
<name><surname>Valencia</surname><given-names>Alfonso</given-names></name>
<name><surname>Barillot</surname><given-names>Emmanuel</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>Julio</given-names></name>
<name><surname>Calzone</surname><given-names>Laurence</given-names></name>
</principal-award-recipient></award-group><funding-statement>The funders had no role in study design, data collection and interpretation, or the decision to submit the work for publication.</funding-statement></funding-group><custom-meta-group><custom-meta specific-use="meta-only"><meta-name>Author impact statement</meta-name><meta-value>Tailoring Boolean models to 488 prostate cancer patients and 8 cell lines data allows for the experimentally validated personalisation of drug treatments.</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec sec-type="intro" id="s1"><title>Introduction</title><p>Like most cancers, prostate cancer arises from mutations in single somatic cells that induce deregulations in processes such as proliferation, invasion of adjacent tissues and metastasis. Not all prostate patients respond to the treatments in the same way, depending on the stage and type of their tumour (<xref rid="bib19" ref-type="bibr">Chen and Zhou, 2016</xref>) and differences in their genetic and epigenetic profiles (<xref rid="bib108" ref-type="bibr">Toth et al., 2019</xref>; <xref rid="bib116" ref-type="bibr">Yang et al., 2018</xref>). The high heterogeneity of these profiles can be explained by a large number of interacting proteins and the complex cross-talks between the cell signalling pathways that can be altered in cancer cells. Because of this complexity, understanding the process of tumorigenesis and tumour growth would benefit from a systemic and dynamical description of the disease. At the molecular level, this can be tackled by a simplified mechanistic cell-wide model of protein interactions of the underlying pathways, dependent on external environmental signals.</p><p>Although continuous mathematical modelling has been widely used to study cellular biochemistry dynamics (e.g. ordinary differential equations) (<xref rid="bib44" ref-type="bibr">Goldbeter, 2002</xref>; <xref rid="bib58" ref-type="bibr">Kholodenko et al., 1995</xref>; <xref rid="bib69" ref-type="bibr">Le Nov&#x000e8;re, 2015</xref>; <xref rid="bib99" ref-type="bibr">Sible and Tyson, 2007</xref>; <xref rid="bib112" ref-type="bibr">Tyson et al., 2019</xref>), this formalism does not scale up well to large signalling networks, due to the difficulty of estimating kinetic parameter values (<xref rid="bib6" ref-type="bibr">Babtie and Stumpf, 2017</xref>). In contrast, the logical (or logic) modelling formalism represents a simpler means of abstraction where the causal relationships between proteins (or genes) are encoded with logic statements, and dynamical behaviours are represented by transitions between discrete states of the system (<xref rid="bib57" ref-type="bibr">Kauffman, 1969</xref>; <xref rid="bib107" ref-type="bibr">Thomas, 1973</xref>). In particular, Boolean models, the simplest implementation of logical models, describe each protein as a binary variable (ON/OFF). This framework is flexible, requires in principle no quantitative information, can be hence applied to large networks combining multiple pathways, and can also provide a qualitative understanding of molecular systems lacking detailed mechanistic information.</p><p>In the last years, logical and, in particular, Boolean modelling has been successfully used to describe the dynamics of human cellular signal transduction and gene regulations (<xref rid="bib13" ref-type="bibr">Calzone et al., 2010</xref>; <xref rid="bib21" ref-type="bibr">Cho et al., 2016</xref>; <xref rid="bib35" ref-type="bibr">Flobak et al., 2015</xref>; <xref rid="bib46" ref-type="bibr">Grieco et al., 2013</xref>; <xref rid="bib48" ref-type="bibr">Helikar et al., 2008</xref>; <xref rid="bib109" ref-type="bibr">Traynard et al., 2016</xref>) and their deregulation in cancer (<xref rid="bib39" ref-type="bibr">Fumi&#x000e3; and Martins, 2013</xref>; <xref rid="bib52" ref-type="bibr">Hu et al., 2015</xref>). Numerous applications of logical modelling have shown that this framework is able to delineate the main dynamical properties of complex biological regulatory networks (<xref rid="bib1" ref-type="bibr">Abou-Jaoud&#x000e9; et al., 2011</xref>; <xref rid="bib34" ref-type="bibr">Faur&#x000e9; et al., 2006</xref>).</p><p>However, the Boolean approach is purely qualitative and does not consider the real time of cellular events (half time of proteins, triggering of apoptosis, etc.). To cope with this issue, we developed the MaBoSS software to compute continuous Markov Chain simulations on the model state transition graph (STG), in which a model state is defined as a vector of nodes that are either active or inactive. In practice, MaBoSS associates transition rates for activation and inhibition of each node of the network, enabling it to account for different time scales of the processes described by the model. Given some initial conditions, MaBoSS applies a Monte-Carlo kinetic algorithm (or Gillespie algorithm) to the STG to produce time trajectories (<xref rid="bib104" ref-type="bibr">Stoll et al., 2017</xref>; <xref rid="bib103" ref-type="bibr">Stoll et al., 2012</xref>) such that the time evolution of the model state probabilities can be estimated. Stochastic simulations can easily explore the model dynamics with different initial conditions by varying the probability of having a node active at the beginning of the simulations and by modifying the model such that it accounts for genetic and environmental perturbations (e.g. presence or absence of growth factors or death receptors). For each case, the effect on the probabilities of selected read-outs can be measured (<xref rid="bib23" ref-type="bibr">Cohen et al., 2015</xref>; <xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>).</p><p>When summarising the biological knowledge into a network and translating it into logical terms, the obtained model is generic and cannot explain the differences and heterogeneity between patients&#x02019; responses to treatments. Models can be trained with dedicated perturbation experiments (<xref rid="bib30" ref-type="bibr">Dorier et al., 2016</xref>; <xref rid="bib93" ref-type="bibr">Saez-Rodriguez et al., 2009</xref>), but such data can only be obtained with non-standard procedures such as microfluidics from patients&#x02019; material (<xref rid="bib32" ref-type="bibr">Eduati et al., 2020</xref>). To address this limitation, we developed a methodology to use different omics data that are more commonly available to personalise generic models to individual cancer patients or cell lines and verified that the obtained models correlated with clinical results such as patient survival information (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>). In the present work, we apply this approach to prostate cancer to suggest targeted therapy to patients based on their omics profile (<xref rid="fig1" ref-type="fig">Figure 1</xref>). We first built 488 patient- and eight cell line prostate-specific models using data from The Cancer Genome Atlas (TCGA) and the Genomics of Drug Sensitivity in Cancer (GDSC) projects, respectively. Simulating these models with the MaBoSS framework, we identified points of intervention that diminish the probability of reaching pro-tumorigenic phenotypes. Lastly, we developed a new methodology to simulate drug effects on these data-tailored Boolean models and present a list of viable drugs and treatments that could be used on these patient- and cell line-specific models for optimal results. Experimental validations were performed on the LNCaP prostate cell line with two predicted targets, confirming the predictions of the model.</p><fig position="float" id="fig1"><label>Figure 1.</label><caption><title>Workflow to build patient-specific Boolean models and to uncover personalised drug treatments from present work.</title><p>We gathered data from <xref rid="bib39" ref-type="bibr">Fumi&#x000e3; and Martins, 2013</xref> Boolean model, Omnipath (<xref rid="bib111" ref-type="bibr">T&#x000fc;rei et al., 2021</xref>) and pathways identified with ROMA (<xref rid="bib74" ref-type="bibr">Martignetti et al., 2016</xref>) on the TCGA data to build a prostate-specific prior knowledge network. This network was manually converted into a prostate Boolean model that could be stochastically simulated using MaBoSS (<xref rid="bib104" ref-type="bibr">Stoll et al., 2017</xref>) and tailored to different TCGA and GDSC datasets using our PROFILE tool to have personalised Boolean models. Then, we studied all the possible single and double mutants on these tailored models using our logical pipeline of tools (<xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>). Using these personalised models and our PROFILE_v2 tool presented in this work, we obtained tailored drug simulations and drug treatments for 488 TCGA patients and eight prostate cell lines. Lastly, we performed drug-dose experiments on a shortlist of candidate drugs that were particularly interesting in the LNCaP prostate cell line. Created with <ext-link xlink:href="https://biorender.com/" ext-link-type="uri">BioRender.com</ext-link>.</p></caption><graphic xlink:href="elife-72626-fig1" position="float"/></fig></sec><sec sec-type="results" id="s2"><title>Results</title><sec id="s2-1"><title>Prostate Boolean model construction</title><p>A network of signalling pathways and genes relevant for prostate cancer progression was assembled to recapitulate the potential deregulations that lead to high-grade tumours. Dynamical properties were added onto this network to perform simulations, uncover therapeutic targets and explore drug combinations. The model was built upon a generic cancer Boolean model by <xref rid="bib39" ref-type="bibr">Fumi&#x000e3; and Martins, 2013</xref>, which integrates major signalling pathways and their substantial cross-talks. The pathways include the regulation of cell death and proliferation in many tumours.</p><p>This initial generic network was extended to include prostate cancer-specific genes (e.g. SPOP, AR, etc.), pathways identified using ROMA (<xref rid="bib74" ref-type="bibr">Martignetti et al., 2016</xref>), OmniPath (<xref rid="bib111" ref-type="bibr">T&#x000fc;rei et al., 2021</xref>), and up-to-date literature. ROMA is applied on omics data, either transcriptomics or proteomics. In each pathway, the genes that contribute the most to the overdispersion are selected. ROMA was applied to the TCGA transcriptomics data using gene sets from cancer pathway databases (Appendix 1, Section 1.1.3, <xref rid="app1fig1" ref-type="fig">Appendix 1&#x02014;figure 1</xref>). These results were used as guidelines to extend the network to fully cover the alterations found in prostate cancer patients. OmniPath was used to complete our network finding connections between the proteins of interest known to play a role in the prostate and the ones identified with ROMA, and the list of genes already present in the model (Appendix 1, Sections 1.1.3 and 1.1.4, <xref rid="app1fig2" ref-type="fig">Appendix 1&#x02014;figures 2</xref> and <xref rid="app1fig3" ref-type="fig">3</xref>). The final network includes pathways such as androgen receptor, MAPK, Wnt, NFkB, PI3K/AKT, MAPK, mTOR, SHH, the cell cycle, the epithelial-mesenchymal transition (EMT), apoptosis and DNA damage pathways.</p><p>This network was then converted into a Boolean model where variables can take two values: 0 (inactivate or absent) or 1 (activate or present). Our model aims at predicting prostate phenotypic behaviours for healthy and cancer cells in different conditions. Nine inputs that represent some of these physiological conditions of interest were considered: <italic toggle="yes">Epithelial Growth Factor (EGF</italic>), <italic toggle="yes">Fibroblast Growth Factor (FGF</italic>), <italic toggle="yes">Transforming Growth Factor beta (TGFbeta)</italic>, <italic toggle="yes">Nutrients, Hypoxia, Acidosis, Androgen, Tumour Necrosis Factor alpha (TNF alpha</italic>), and <italic toggle="yes">Carcinogen</italic>. These input nodes have no regulation. Their value is fixed according to the simulated experiment to represent the status of the microenvironmental characteristics (e.g. the presence or absence of growth factors, oxygen, etc.). A more complex multiscale approach would be required to consider the dynamical interaction with other cell types and the environment.</p><p>We defined six variables as output nodes that allow the integration of multiple phenotypic signals and simplify the analysis of the model. Two of these phenotypes represent the possible growth status of the cell: <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis. Apoptosis</italic> is activated by Caspase 8 or Caspase 9, while <italic toggle="yes">Proliferation</italic> is activated by cyclins D and B (read-outs of the G1 and M phases, respectively). The <italic toggle="yes">Proliferation</italic> output is described in published models as specific stationary protein activation patterns, namely the following sequence of activation of cyclins: Cyclin D, then Cyclin E, then Cyclin A, and finally Cyclin B (<xref rid="bib109" ref-type="bibr">Traynard et al., 2016</xref>). Here, we considered a proper sequence when Cyclin D activates first, allowing the release of the transcriptional factor E2F1 from the inhibitory complex it was forming with the RB (retinoblastoma protein), and then triggering a series of events leading to the activation of Cyclin B, responsible for the cell&#x02019;s entry into mitosis (Appendix 1, Section 2.2, <xref rid="app1fig5" ref-type="fig">Appendix 1&#x02014;figure 5</xref>). We also define several phenotypic outputs that are readouts of cancer hallmarks: <italic toggle="yes">Invasion, Migration,</italic> (bone) <italic toggle="yes">Metastasis</italic> and <italic toggle="yes">DNA repair</italic>. The final model accounts for 133 nodes and 449 edges (<xref rid="fig2" ref-type="fig">Figure 2</xref>, <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref>, and in GINsim format at the address: <ext-link xlink:href="http://ginsim.org/model/signalling-prostate-cancer" ext-link-type="uri">http://ginsim.org/model/signalling-prostate-cancer</ext-link>).</p><fig position="float" id="fig2"><label>Figure 2.</label><caption><title>Prostate Boolean model used in present work.</title><p>Nodes (ellipses) represent biological entities, and arcs are positive (green) or negative (red) influences of one entity on another one. Orange rectangles correspond to inputs (from left to right: Epithelial Growth Factor (EGF), Fibroblast Growth Factor (FGF), Transforming Growth Factor beta (TGFbeta), Nutrients, Hypoxia, Acidosis, Androgen, fused_event, Tumour Necrosis Factor alpha (TNFalpha), SPOP, Carcinogen) and dark blue rectangles to outputs that represent biological phenotypes (from left to right: Proliferation, Migration, Invasion, Metastasis, Apoptosis, DNA_repair), the read-outs of the model. This network is available to be inspected as a Cytoscape file in the <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref>.</p></caption><graphic xlink:href="elife-72626-fig2" position="float"/></fig></sec><sec id="s2-2"><title>Prostate Boolean model simulation</title><p>The model can be considered as a model of healthy prostate cells when no mutants (or fused genes) are present. We refer to this model as the wild type model. These healthy cells mostly exhibit quiescence (neither proliferation nor apoptosis) in the absence of any input (<xref rid="fig3" ref-type="fig">Figure 3A</xref>). When <italic toggle="yes">Nutrients</italic> and growth factors (<italic toggle="yes">EGF</italic> or <italic toggle="yes">FGF</italic>) are present, <italic toggle="yes">Proliferation</italic> is activated (<xref rid="fig3" ref-type="fig">Figure 3B</xref>). <italic toggle="yes">Androgen</italic> is necessary for AR activation and helps in the activation of <italic toggle="yes">Proliferation</italic>, even though it is not necessary when <italic toggle="yes">Nutrients</italic> or growth factors are present. Cell death factors (such as Caspase 8 or 9) trigger <italic toggle="yes">Apoptosis</italic> in the absence of <italic toggle="yes">SPOP</italic>, while <italic toggle="yes">Hypoxia</italic> and <italic toggle="yes">Carcinogen</italic> facilitate apoptosis but are not necessary if cell death factors are present (<xref rid="fig3" ref-type="fig">Figure 3C</xref>).</p><fig position="float" id="fig3"><label>Figure 3.</label><caption><title>Prostate Boolean model MaBoSS simulations.</title><p>(<bold>A</bold>) The model was simulated with all initial inputs set to 0 and all other variables random. All phenotypes are 0 at the end of the simulations, which should be understood as a quiescent state, where neither proliferation nor apoptosis is active. (<bold>B</bold>) The model was simulated with growth factors (<italic toggle="yes">EGF</italic> and <italic toggle="yes">FGF</italic>), <italic toggle="yes">Nutrients</italic> and <italic toggle="yes">Androgen</italic> ON. (<bold>C</bold>) The model was simulated with <italic toggle="yes">Carcinogen</italic>, <italic toggle="yes">Androgen</italic>, <italic toggle="yes">TNFalpha</italic>, <italic toggle="yes">Acidosis</italic>, and <italic toggle="yes">Hypoxia</italic> ON.</p></caption><graphic xlink:href="elife-72626-fig3" position="float"/></fig><p>In our model, the progression towards metastasis is described as a stepwise process. <italic toggle="yes">Invasion</italic> is first activated by known pro-invasive proteins: either &#x003b2;-catenin (<xref rid="bib38" ref-type="bibr">Francis et al., 2013</xref>) or a combination of <italic toggle="yes">CDH2</italic> (<xref rid="bib29" ref-type="bibr">De Wever et al., 2004</xref>), <italic toggle="yes">SMAD</italic> (<xref rid="bib27" ref-type="bibr">Daroqui et al., 2012</xref>), or <italic toggle="yes">EZH2</italic> (<xref rid="bib88" ref-type="bibr">Ren et al., 2012</xref>). <italic toggle="yes">Migration</italic> is then activated by <italic toggle="yes">Invasion</italic> and <italic toggle="yes">EMT</italic> and with either <italic toggle="yes">AKT</italic> or <italic toggle="yes">AR</italic> (<xref rid="bib17" ref-type="bibr">Castoria et al., 2011</xref>). Lastly, (bone) <italic toggle="yes">Metastasis</italic> is activated by <italic toggle="yes">Migration</italic> and one of three nodes: <italic toggle="yes">RUNX2</italic> (<xref rid="bib5" ref-type="bibr">Altieri et al., 2009</xref>), <italic toggle="yes">ERG</italic> (<xref rid="bib3" ref-type="bibr">Adamo and Ladomery, 2016</xref>) or ERG fused with TMPRSS2 (<xref rid="bib102" ref-type="bibr">St John et al., 2012</xref>), FLI1, ETV1 or ETV4 (<xref rid="bib15" ref-type="bibr">Cancer Genome Atlas Research Network, 2015</xref>).</p><p>This prostate Boolean model was simulated stochastically using MaBoSS (<xref rid="bib104" ref-type="bibr">Stoll et al., 2017</xref>; <xref rid="bib103" ref-type="bibr">Stoll et al., 2012</xref>) and validated by recapitulating known phenotypes of prostate cells under physiological conditions (<xref rid="fig3" ref-type="fig">Figure 3</xref> and Appendix 1, Sections 2.2 and 2.3, <xref rid="app1fig5" ref-type="fig">Appendix 1&#x02014;figures 5</xref>&#x02013;<xref rid="app1fig7" ref-type="fig">7</xref>). In particular, we tested that combinations of inputs lead to non-aberrant phenotypes such as growth factors leading to apoptosis in wild type conditions; we also verified that the cell cycle events occur in proper order: as CyclinD gets activated, RB1 is phosphorylated and turned OFF, allowing E2F1 to mediate the synthesis of CyclinB (see <xref rid="supp2" ref-type="supplementary-material">Supplementary file 2</xref> for the jupyter notebook and the simulation of diverse cellular conditions).</p></sec><sec id="s2-3"><title>Personalisation of the prostate Boolean model</title><sec id="s2-3-1"><title>Personalised TCGA prostate cancer patient Boolean models</title><p>We tailored the generic prostate Boolean model to a set of 488 TCGA prostate cancer patients (Appendix 1, Section 4, <xref rid="app1fig9" ref-type="fig">Appendix 1&#x02014;figure 9</xref>) using our personalisation method (PROFILE) (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>), constructing 488 individual Boolean models, one for each patient. Personalised models were built using three types of data: discrete data such as mutations and copy number alterations (CNA) and continuous data such as RNAseq data. For discrete data, the nodes corresponding to the mutations or the CNA were forced to 0 or 1 according to the effect of alterations, based on a priori knowledge (i.e. if the mutation was reported to be activating or inhibiting the gene&#x02019;s activity). For continuous data, the personalisation method modifies the value for the transition rates of model variables and their initial conditions to influence the probability of some transitions. This corresponds, in a biologically meaningful way, to translating genetic mutations as lasting modifications making the gene independent of regulation, and to translating RNA expression levels as modulation of a signal but not changing the regulation rules (see Materials and methods and in Appendix 1, Section 4.1, <xref rid="app1fig10" ref-type="fig">Appendix 1&#x02014;figures 10</xref>&#x02013;<xref rid="app1fig11" ref-type="fig">14</xref>).</p><p>We assess the general behaviour of the individual patient-specific models by comparing the model outputs (i.e. probabilities to reach certain phenotypes) with clinical data. Here, the clinical data consist of a Gleason grade score associated with each patient, which in turn corresponds to the gravity of the tumour based on its appearance and the stage of invasion (<xref rid="bib19" ref-type="bibr">Chen and Zhou, 2016</xref>; <xref rid="bib43" ref-type="bibr">Gleason, 1992</xref>; <xref rid="bib42" ref-type="bibr">Gleason, 1977</xref>). We gathered the output probabilities for all patient-specific models and confronted them to their Gleason scores. The phenotype <italic toggle="yes">DNA_repair</italic>, which can be interpreted as a sensor of DNA damage and genome integrity which could lead to DNA repair, seems to separate low and high Gleason scores (<xref rid="fig4" ref-type="fig">Figure 4A</xref> and Appendix 1, Section 4.1, <xref rid="app1fig15" ref-type="fig">Appendix 1&#x02014;figures 15</xref>&#x02013;<xref rid="app1fig18" ref-type="fig">18</xref>), confirming that DNA damage pathways are activated in patients (<xref rid="bib73" ref-type="bibr">Marshall et al., 2019</xref>) but may not lead to the triggering of apoptosis in this model (Appendix 1, Section 4.1, <xref rid="app1fig11" ref-type="fig">Appendix 1&#x02014;figure 11</xref>). Also, the centroids of Gleason grades tend to move following <italic toggle="yes">Proliferation</italic>, <italic toggle="yes">Migration</italic> and <italic toggle="yes">Invasion</italic> variables. We then looked at the profiles of the phenotype scores across patients and their Gleason grade and found that the density of high <italic toggle="yes">Proliferation</italic> score (close to 1, <xref rid="fig4" ref-type="fig">Figure 4B</xref>) tends to increase as the Gleason score increases (from low to intermediate to high) and these distributions are significantly different (Kruskal-Wallis rank sum test, p-value = 0.00207; Appendix 1, Section 4.1). The <italic toggle="yes">Apoptosis</italic> phenotype probabilities, however, do not have a clear trend across grades (<xref rid="fig4" ref-type="fig">Figure 4C</xref>), even though the distributions are significantly different (Kruskal-Wallis rank sum test, p-value = 2.83E-6; Appendix 1, Section 4.1).</p><fig position="float" id="fig4"><label>Figure 4.</label><caption><title>Associations between simulations and Gleason grades (GG).</title><p>(<bold>A</bold>) Centroids of the Principal Component Analysis of the samples according to their Gleason grades (GG). The personalisation recipe used was mutations and copy number alterations (CNA) as discrete data and RNAseq as continuous data. Density plots of <italic toggle="yes">Proliferation</italic> (<bold>B</bold>) and <italic toggle="yes">Apoptosis</italic> (<bold>C</bold>) scores according to GG; each vignette corresponds to a specific sub-cohort with a given GG. Kruskal-Wallis rank sum test across GG is significant for Proliferation (p-value = 0.00207) and Apoptosis (p-value = 2.83E-6).</p><p>
<supplementary-material id="fig4scode1" position="float" content-type="local-data"><label>Figure 4&#x02014;source code 1.</label><caption><title>R code needed to obtain <xref rid="fig4" ref-type="fig">Figure 4</xref>.</title><p>Processed datasets needed are <xref rid="fig4sdata1" ref-type="supplementary-material">Figure 4&#x02014;source data 1</xref> and <xref rid="fig4sdata2" ref-type="supplementary-material">Figure 4&#x02014;source data 2</xref> are located in the corresponding folder of the repository: <ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2/tree/main/Analysis%20of%20TCGA%20patients'%20simulations" ext-link-type="uri">here</ext-link>.</p></caption><media xlink:href="elife-72626-fig4-code1.zip" id="d64e1040" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig4sdata1" position="float" content-type="local-data"><label>Figure 4&#x02014;source data 1.</label><caption><title>Processed dataset needed to obtain the phenotype distributions of <xref rid="fig4" ref-type="fig">Figure 4B, C</xref>, with Figure 4&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig4-data1.txt" id="d64e1052" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig4sdata2" position="float" content-type="local-data"><label>Figure 4&#x02014;source data 2.</label><caption><title>Processed dataset needed to obtain the PCA of <xref rid="fig4" ref-type="fig">Figure 4A</xref>, with Figure 4&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig4-data2.txt" id="d64e1064" position="anchor"/></supplementary-material>
</p></caption><graphic xlink:href="elife-72626-fig4" position="float"/></fig></sec></sec><sec id="s2-4"><title>Personalised drug predictions of TCGA Boolean models</title><p>Using the 488 TCGA patient-specific models, we looked in each patient for genes that, when inhibited, hamper <italic toggle="yes">Proliferation</italic> or promote <italic toggle="yes">Apoptosis</italic> in the model. We focused on these inhibitions as most drugs interfere with the protein activity related to these genes, even though our methodology allows us to study increased protein activity related to over-expression of genes as well (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>; <xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>). Interestingly, we found several genes that were found as suitable points of intervention in most of the patients (MYC_MAX complex and SPOP were identified in more than 80% of the cases) (Appendix 1, Section 4.2, <xref rid="app1fig19" ref-type="fig">Appendix 1&#x02014;figures 19</xref> and <xref rid="app1fig20" ref-type="fig">20</xref>), but others were specific to only some of the patients (MXI1 was identified in only 4 patients, 1% of the total, GLI in only 7% and WNT in 8% of patients). All the TCGA-specific personalised models can be found in <xref rid="supp3" ref-type="supplementary-material">Supplementary file 3</xref>, and the TCGA mutants and their phenotype scores can be found in <xref rid="supp4" ref-type="supplementary-material">Supplementary file 4</xref>.</p><p>Furthermore, we explored the possibility of finding combinations of treatments that could reduce the <italic toggle="yes">Proliferation</italic> phenotype or increase the <italic toggle="yes">Apoptosis</italic> one. To lower the computational power need, we narrowed down the list of potential candidates to a set of selected genes that are targets of already-developed drugs relevant in cancer progression (<xref rid="table1" ref-type="table">Table 1</xref>) and analysed the simulations of the models with all the single and combined perturbations.</p><table-wrap position="float" id="table1"><label>Table 1.</label><caption><title>List of selected nodes, their corresponding genes and drugs that were included in the drug analysis of the models tailored for TCGA patients and LNCaP cell line.</title></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" valign="bottom" rowspan="1" colspan="1">Node</th><th align="left" valign="bottom" rowspan="1" colspan="1">Gene</th><th align="left" valign="bottom" rowspan="1" colspan="1">Compound / Inhibitor name</th><th align="left" valign="bottom" rowspan="1" colspan="1">Clinical stage</th><th align="left" valign="bottom" rowspan="1" colspan="1">Source</th></tr></thead><tbody><tr><td align="left" rowspan="3" valign="bottom" colspan="1">AKT</td><td align="left" rowspan="3" valign="bottom" colspan="1">AKT1, AKT2, AKT3</td><td align="left" valign="bottom" rowspan="1" colspan="1">PI-103</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Enzastaurin</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 3</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Archexin, Pictilisib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="2" valign="bottom" colspan="1">AR</td><td align="left" rowspan="2" valign="bottom" colspan="1">AR</td><td align="left" valign="bottom" rowspan="1" colspan="1">Abiraterone,Enzalutamide, Formestane, Testosterone propionate</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">5alpha-androstan-3beta-ol</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Caspase8</td><td align="left" valign="bottom" rowspan="1" colspan="1">CASP8</td><td align="left" valign="bottom" rowspan="1" colspan="1">Bardoxolone</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">cFLAR</td><td align="left" valign="bottom" rowspan="1" colspan="1">CFLAR</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td></tr><tr><td align="left" rowspan="3" valign="bottom" colspan="1">EGFR</td><td align="left" rowspan="3" valign="bottom" colspan="1">EGFR</td><td align="left" valign="bottom" rowspan="1" colspan="1">Afatinib, Osimertinib, Neratinib, Erlotinib, Gefitinib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Varlitinib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 3</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Olmutinib, Pelitinib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="8" valign="bottom" colspan="1">ERK</td><td align="left" rowspan="3" valign="bottom" colspan="1">MAPK1</td><td align="left" valign="bottom" rowspan="1" colspan="1">Isoprenaline</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Perifosine</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 3</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Turpentine, SB220025, Olomoucine, Phosphonothreonine</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="3" valign="bottom" colspan="1">MAPK3, MAPK1</td><td align="left" valign="bottom" rowspan="1" colspan="1">Arsenic trioxide</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Ulixertinib, Seliciclib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Purvalanol</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="2" valign="bottom" colspan="1">MAPK3</td><td align="left" valign="bottom" rowspan="1" colspan="1">Sulindac, Cholecystokinin</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">5-iodotubercidin</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">GLUT1</td><td align="left" valign="bottom" rowspan="1" colspan="1">SLC2A1</td><td align="left" valign="bottom" rowspan="1" colspan="1">Resveratrol</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 4</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">HIF-1</td><td align="left" valign="bottom" rowspan="1" colspan="1">HIF1A</td><td align="left" valign="bottom" rowspan="1" colspan="1">CAY-10585</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="3" valign="bottom" colspan="1">HSPs</td><td align="left" rowspan="3" valign="bottom" colspan="1">HSP90AA1, HSP90AB1, HSP90B1, HSPA1A, HSPA1B, HSPB1</td><td align="left" valign="bottom" rowspan="1" colspan="1">Cladribine</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="char" char="hyphen" valign="bottom" rowspan="1" colspan="1">17-DMAG</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">NMS-E973</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="3" valign="bottom" colspan="1">MEK1_2</td><td align="left" rowspan="3" valign="bottom" colspan="1">MAP2K1, MAP2K2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Trametinib, Selumetinib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Perifosine</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 3</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">PD184352 (CI-1040)</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">MYC_MAX</td><td align="left" valign="bottom" rowspan="1" colspan="1">complex of MYC and MAX</td><td align="char" char="hyphen" valign="bottom" rowspan="1" colspan="1">10058-F4 (for MAX)</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">p14ARF</td><td align="left" valign="bottom" rowspan="1" colspan="1">CDKN2A</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td></tr><tr><td align="left" rowspan="2" valign="bottom" colspan="1">PI3K</td><td align="left" rowspan="2" valign="bottom" colspan="1">PIK3CA, PIK3CB, PIK3CG, PIK3CD, PIK3R1, PIK3R2, PIK3R3, PIK3R4, PIK3R5, PIK3R6, PIK3C2A, PIK3C2B, PIK3C2G, PIK3C3</td><td align="left" valign="bottom" rowspan="1" colspan="1">PI-103</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Pictilisib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="3" valign="bottom" colspan="1">ROS</td><td align="left" valign="bottom" rowspan="1" colspan="1">NOX1, NOX3, NOX4</td><td align="left" valign="bottom" rowspan="1" colspan="1">Fostamatinib</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" rowspan="2" valign="bottom" colspan="1">NOX2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Dextromethorphan</td><td align="left" valign="bottom" rowspan="1" colspan="1">Approved</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Tetrahydroisoquinolines (CHEMBL3733336, CHEMBL3347550, CHEMBL3347551)</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">ChEMBL</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">SPOP</td><td align="left" valign="bottom" rowspan="1" colspan="1">SPOP</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td><td align="left" valign="bottom" rowspan="1" colspan="1">-</td></tr><tr><td align="left" rowspan="2" valign="bottom" colspan="1">TERT</td><td align="left" rowspan="2" valign="bottom" colspan="1">TERT</td><td align="left" valign="bottom" rowspan="1" colspan="1">Grn163l</td><td align="left" valign="bottom" rowspan="1" colspan="1">Phase 2</td><td align="left" valign="bottom" rowspan="1" colspan="1">Drug Bank</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">BIBR 1532</td><td align="left" valign="bottom" rowspan="1" colspan="1">Preclinical</td><td align="left" valign="bottom" rowspan="1" colspan="1">ChEMBL</td></tr></tbody></table></table-wrap><p>We used the models to grade the effect that the combined treatments have in each one of the 488 TCGA patient-specific models&#x02019; phenotypes. This list of combinations of treatments can be used to compare the effects of drugs on each TCGA patient and allows us to propose some of them for individual patients and to suggest drugs suitable to groups of patients (<xref rid="supp4" ref-type="supplementary-material">Supplementary file 4</xref>). Indeed, the inactivation of some of the targeted genes had a greater effect in some patients than in others, suggesting the possibility for the design of personalised drug treatments. For instance, for the TCGA-EJ-5527 patient, the use of MYC_MAX complex inhibitor reduced <italic toggle="yes">Proliferation</italic> to 66%. For this patient, combining MYC_MAX with other inhibitors, such as AR or AKT, did not further reduce the <italic toggle="yes">Proliferation</italic> score (67% in these cases). Other patients have MYC_MAX as an interesting drug target, but the inhibition of this complex did not have such a dramatic effect on their <italic toggle="yes">Proliferation</italic> scores as in the case of TCGA-EJ-5527. Likewise, for the TCGA-H9-A6BX patient, the use of SPOP inhibitor increased <italic toggle="yes">Apoptosis</italic> by 87%, while the use of a combination of cFLAR and SPOP inhibitors further increased <italic toggle="yes">Apoptosis</italic> by 89%. For the rest of this section, we focus on the analysis of clinical groups rather than individuals.</p><p>Studying the decrease of <italic toggle="yes">Proliferation</italic>, we found that AKT is the top hit in Gleason Grades 1, 2, 3, and 4, seconded by EGFR and SPOP in Grade 1, by SPOP and PIP3 in Grade 2, by PIP3 and AR in Grade 3, and by CyclinD and MYC_MAX in Grade 4. MYC_MAX is the top hit in Grade 5, seconded by AR (Appendix 1, Section 4.2, <xref rid="app1fig19" ref-type="fig">Appendix 1&#x02014;figure 19</xref>). In regard to the increase of <italic toggle="yes">Apoptosis</italic>, SPOP is the top hit in all grades, seconded by SSH in Grades 1, 2, and 3 and by AKT in Grade 4 (Appendix 1, Section 4.2, <xref rid="app1fig20" ref-type="fig">Appendix 1&#x02014;figure 20</xref>). It is interesting to note here that many of these genes are targeted by drugs (<xref rid="table1" ref-type="table">Table 1</xref>). Notably, AR is the target of the drug Enzalutamide, which is indicated for men with an advanced stage of the disease (<xref rid="bib97" ref-type="bibr">Scott, 2018</xref>), or that MYC is the target of BET bromodomain inhibitors and are generally effective in castration-resistant prostate cancer cases (<xref rid="bib24" ref-type="bibr">Coleman et al., 2019</xref>).</p><p>The work on patient data provided possible insights and suggested patient- and grade-specific potential targets. To validate our approach experimentally, we personalised the prostate model to different prostate cell lines, where we performed drug assays to confirm the predictions of the model.</p></sec><sec id="s2-5"><title>Personalised drug predictions of LNCaP Boolean model</title><p>We applied the methodology for personalisation of the prostate model to eight prostate cell lines available in GDSC (<xref rid="bib53" ref-type="bibr">Iorio et al., 2016</xref>): 22RV1, BPH-1, DU-145, NCI-H660, PC-3, PWR-1E, and VCaP (results in Appendix 1, Section 5 and are publicly available in <xref rid="supp5" ref-type="supplementary-material">Supplementary file 5</xref>). We decided to focus the validation on one cell line, LNCaP.</p><p>LNCaP, first isolated from a human metastatic prostate adenocarcinoma found in a lymph node (<xref rid="bib51" ref-type="bibr">Horoszewicz et al., 1983</xref>), is one of the most widely used cell lines for prostate cancer studies. Androgen-sensitive LNCaP cells are representative of patients sensitive to treatments as opposed to resistant cell lines such as DU-145. Additionally, LNCaP cells have been used to obtain numerous subsequent derivatives with different characteristics (<xref rid="bib26" ref-type="bibr">Cunningham and You, 2015</xref>).</p><p>The LNCaP personalisation was performed based on mutations as discrete data and RNA-Seq as continuous data. The resulting LNCaP-specific Boolean model was then used to identify all possible combinations of mutations (interpreted as effects of therapies) and to study the synergy of these perturbations. For that purpose, we automatically performed single and double mutant analyses on the LNCaP-specific model (knock-out and overexpression) (<xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>) and focused on the model phenotype probabilities as read-outs of the simulations. The analysis of the complete set of simulations for the 32,258 mutants can be found in the Appendix 1, Section 6.1 and in <xref rid="supp6" ref-type="supplementary-material">Supplementary file 6</xref>, where the LNCaP cell line-specific mutants and their phenotype scores are reported for all mutants. Among all combinations, we identified the top 20 knock-out mutations that depleted <italic toggle="yes">Proliferation</italic> or increased <italic toggle="yes">Apoptosis</italic> the most. As some of them overlapped, we ended up with 29 nodes: <italic toggle="yes">AKT, AR, ATR, AXIN1, Bak, BIRC5, CDH2, cFLAR, CyclinB, CyclinD, E2F1, eEF2K, eEF2, eEF2K, EGFR, ERK, HSPs, MED12, mTORC1, mTORC2, MYC, MYC_MAX, PHDs, PI3K, PIP3, SPOP, TAK1, TWIST1, and VHL</italic>. We used the scores of these nodes to further trim down the list to have 10 final nodes (<italic toggle="yes">AKT, AR, cFLAR, EGFR, ERK, HSPs, MYC_MAX, SPOP,</italic> and <italic toggle="yes">PI3K</italic>) and added seven other nodes whose genes are considered relevant in cancer biology, such as <italic toggle="yes">AR_ERG</italic> fusion, <italic toggle="yes">Caspase8</italic>, <italic toggle="yes">HIF1</italic>, <italic toggle="yes">GLUT1, MEK1_2</italic>, <italic toggle="yes">p14ARF</italic>, <italic toggle="yes">ROS,</italic> and <italic toggle="yes">TERT</italic> (<xref rid="table1" ref-type="table">Table 1</xref>). We did not consider the overexpression mutants as they have a very difficult translation to drug uses and clinical practices.</p><p>To further analyse the mutant effects, we simulated the LNCaP model with increasing node inhibition values to mimic the effect of drugs&#x02019; dosages using a methodology we specifically developed for this purpose (PROFILE_v2 and available at <ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2" ext-link-type="uri">https://github.com/ArnauMontagud/PROFILE_v2</ext-link>; <xref rid="bib79" ref-type="bibr">Montagud, 2022a</xref>). Six simulations were done for each inhibited node, with 100% of node inhibition (proper knock-out), 80%, 60%, 40%, 20% and 0% (no inhibition) (see Materials and methods). A nutrient-rich media with EGF was used for these simulations that correspond to experimental conditions that are tested here. We show results on three additional sets of initial conditions in the Appendix 1, Section 6, <xref rid="app1fig27" ref-type="fig">Appendix 1&#x02014;figure 27</xref>: a nutrient-rich media with androgen, with androgen and EGF, and with none, . We applied this gradual inhibition, using increasing drugs&#x02019; concentrations, to a reduced list of drug-targeted genes relevant for cancer progression (<xref rid="table1" ref-type="table">Table 1</xref>). We confirmed that the inhibition of different nodes affected differently the probabilities of the outputs (Appendix 1, Section 7.3.1, <xref rid="app1fig34" ref-type="fig">Appendix 1&#x02014;figures 34</xref> and <xref rid="app1fig35" ref-type="fig">35</xref>). Notably, the <italic toggle="yes">Apoptosis</italic> score was slightly promoted when knocking out <italic toggle="yes">SPOP</italic> under all growth conditions (Appendix 1, Section 7.3.1, <xref rid="app1fig35" ref-type="fig">Appendix 1&#x02014;figure 35</xref>). Likewise, <italic toggle="yes">Proliferation</italic> depletion was accomplished when <italic toggle="yes">HSPs</italic> or <italic toggle="yes">MYC_MAX</italic> were inhibited under all conditions and, less notably, when <italic toggle="yes">ERK, EGFR</italic>, <italic toggle="yes">SPOP,</italic> or <italic toggle="yes">PI3K</italic> were inhibited (Appendix 1, Section 7.3.1, <xref rid="app1fig35" ref-type="fig">Appendix 1&#x02014;figure 35</xref>).</p><p>Additionally, these gradual inhibition analyses can be combined to study the interaction of two simultaneously inhibiting nodes (Appendix 1, Section 7.3.2, <xref rid="app1fig36" ref-type="fig">Appendix 1&#x02014;figures 36</xref> and <xref rid="app1fig37" ref-type="fig">37</xref>). For instance, the combined gradual inhibition of <italic toggle="yes">ERK</italic> and <italic toggle="yes">MYC_MAX</italic> nodes affects the <italic toggle="yes">Proliferation</italic> score in a balanced manner (<xref rid="fig5" ref-type="fig">Figure 5A</xref>) even though <italic toggle="yes">MYC_MAX</italic> seems to affect this phenotype more, notably at low activity levels. By extracting subnetworks of interaction around <italic toggle="yes">ERK</italic> and <italic toggle="yes">MYC_MAX</italic> and comparing them, we found that the pathways they belong to have complementary downstream targets participating in cell proliferation through targets in MAPK and cell cycle pathways. This complementarity could explain the synergistic effects observed (<xref rid="fig5" ref-type="fig">Figure 5A and C</xref>).</p><fig position="float" id="fig5"><label>Figure 5.</label><caption><title>Phenotype score variations and synergy upon combined ERK and MYC_MAX (<bold>A and C</bold>) and HSPs and PI3K (<bold>B and D</bold>) inhibition under <italic toggle="yes">EGF</italic> growth condition.</title><p>Proliferation score variation (<bold>A</bold>) and Bliss Independence synergy score (<bold>C</bold>) with increased node activation of nodes ERK and MYC_MAX. Proliferation score variation (<bold>B</bold>) and Bliss Independence synergy score (<bold>D</bold>) with increased node activation of nodes HSPs and PI3K. Bliss Independence synergy score &#x0003c;1 is characteristic of drug synergy, grey colour means one of the drugs is absent and thus no synergy score is available.</p><p>
<supplementary-material id="fig5scode1" position="float" content-type="local-data"><label>Figure 5&#x02014;source code 1.</label><caption><title>R code needed to perform the drug dosage experiments and obtain <xref rid="fig5" ref-type="fig">Figure 5</xref> from the main text and <xref rid="app1fig27" ref-type="fig">Appendix 1&#x02014;figures 27, 34</xref>&#x02013;<xref rid="app1fig39" ref-type="fig">39</xref>.</title><p>Processed datasets needed is <xref rid="fig5sdata1" ref-type="supplementary-material">Figure 5&#x02014;source data 1</xref> and is located in the corresponding folder of the repository: <ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2/tree/main/Gradient%20inhibition%20of%20nodes" ext-link-type="uri">here</ext-link>.</p></caption><media xlink:href="elife-72626-fig5-code1.zip" id="d64e1694" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig5sdata1" position="float" content-type="local-data"><label>Figure 5&#x02014;source data 1.</label><caption><title>Processed datasets needed to obtain the phenotype score variations and synergy values of <xref rid="fig5" ref-type="fig">Figure 5</xref> with Figure 5&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig5-data1.txt" id="d64e1706" position="anchor"/></supplementary-material>
</p></caption><graphic xlink:href="elife-72626-fig5" position="float"/></fig><p>Lastly, drug synergies can be studied using Bliss Independence using the results from single and combined simulations with gradual inhibitions. This score compares the combined effect of two drugs with the effect of each one of them, with a synergy when the value of this score is lower than 1. We found that the combined inhibition of <italic toggle="yes">ERK</italic> and <italic toggle="yes">MYC_MAX</italic> nodes on the <italic toggle="yes">Proliferation</italic> score was synergistic (<xref rid="fig5" ref-type="fig">Figure 5C</xref>). Another synergistic pair is the combined gradual inhibition of <italic toggle="yes">HSPs</italic> and <italic toggle="yes">PI3K</italic> nodes that also affects the <italic toggle="yes">Proliferation</italic> score in a joint manner (<xref rid="fig5" ref-type="fig">Figure 5B</xref>), with some Bliss Independence synergy found (<xref rid="fig5" ref-type="fig">Figure 5D</xref>). A complete study on the Bliss Independence synergy of all the drugs considered in the present work on <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis</italic> phenotypes can be found in Appendix 1, Section 7.3.2, <xref rid="app1fig38" ref-type="fig">Appendix 1&#x02014;figures 38</xref> and <xref rid="app1fig39" ref-type="fig">39</xref>.</p></sec><sec id="s2-6"><title>Experimental validation of predicted targets</title><sec id="s2-6-1"><title>Drugs associated with the proposed targets</title><p>To identify drugs that could act as potential inhibitors of the genes identified with the Boolean model, we explored the drug-target associations in DrugBank (<xref rid="bib115" ref-type="bibr">Wishart et al., 2018</xref>) and ChEMBL (<xref rid="bib40" ref-type="bibr">Gaulton et al., 2017</xref>). We found drugs that targeted almost all genes corresponding to the nodes of interest in <xref rid="table1" ref-type="table">Table 1</xref>, except for cFLAR, p14ARF, and SPOP. However, we could not identify experimental cases where drugs targeting both members of the proposed combinations were available (Appendix 1, Section 7.1 and in <xref rid="supp6" ref-type="supplementary-material">Supplementary file 6</xref>). One possible explanation is that the combinations predicted by the model suggest, in some cases, to overexpress the potential target and most of the drugs available act as inhibitors of their targets.</p><p>Using the cell line-specific models, we tested if the LNCaP cell line was more sensitive than the rest of the prostate cell lines to the LNCaP-specific drugs identified in <xref rid="table1" ref-type="table">Table 1</xref>. We compared GDSC&#x02019;s Z-score of these drugs in LNCaP with their Z-scores in all GDSC cell lines (<xref rid="fig6" ref-type="fig">Figure 6</xref> and Appendix 1, Section 7.2, <xref rid="app1fig33" ref-type="fig">Appendix 1&#x02014;figure 33</xref>). We observed that LNCaP is more sensitive to drugs targeting AKT or TERT than the rest of the studied prostate cell lines. Furthermore, we saw that the drugs that targeted the genes included in the model allowed the identification of cell line specificities (Appendix 1, Section 7.1). For instance, target enrichment analysis showed that LNCaP cell lines are especially sensitive to drugs targeting PI3K/AKT/mTOR, hormone-related (AR targeting) and Chromatin (bromodomain inhibitors, regulating Myc) pathways (adjusted p-values from target enrichment: 0.001, 0.001, and 0.032, respectively, Appendix 1, Section 7.1, <xref rid="app1table2" ref-type="table">Appendix 1&#x02014;table 2</xref>), which corresponds to the model predictions (<xref rid="table1" ref-type="table">Table 1</xref>). Also, the LNCaP cell line is more sensitive to drugs targeting model-identified nodes than to drugs targeting other proteins (Appendix 1, Section 7.1, <xref rid="app1fig32" ref-type="fig">Appendix 1&#x02014;figure 32</xref>, Mann-Whitney U p-value 0.00041), and this effect is specific for LNCaP cell line (Mann-Whitney U p-values ranging from 0.0033 to 0.38 for other prostate cancer cell lines).</p><fig position="float" id="fig6"><label>Figure 6.</label><caption><title>Model-targeting drugs&#x02019; sensitivities across prostate cell lines.</title><p>GDSC z-score was obtained for all the drugs targeting genes included in the model for all the prostate cell lines in GDSC. Negative values mean that the cell line is more sensitive to the drug. Drugs included in <xref rid="table1" ref-type="table">Table 1</xref> were highlighted. &#x02018;Other targets&#x02019; are drugs targeting model-related genes that are not part of <xref rid="table1" ref-type="table">Table 1</xref>.</p><p>
<supplementary-material id="fig6scode1" position="float" content-type="local-data"><label>Figure 6&#x02014;source code 1.</label><caption><title>R code needed to obtain <xref rid="fig6" ref-type="fig">Figure 6</xref>.</title><p>Processed datasets needed are <xref rid="fig6sdata1" ref-type="supplementary-material">Figure 6&#x02014;source data 1</xref> and <xref rid="fig6sdata2" ref-type="supplementary-material">Figure 6&#x02014;source data 2</xref> are located in the corresponding folder of the repository: <ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2/tree/main/Analysis%20of%20drug%20sensitivities%20across%20cell%20lines" ext-link-type="uri">here</ext-link>.</p></caption><media xlink:href="elife-72626-fig6-code1.zip" id="d64e1824" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig6sdata1" position="float" content-type="local-data"><label>Figure 6&#x02014;source data 1.</label><caption><title>Processed dataset needed to obtain <xref rid="fig6" ref-type="fig">Figure 6</xref> with Figure 6&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig6-data1.txt" id="d64e1836" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig6sdata2" position="float" content-type="local-data"><label>Figure 6&#x02014;source data 2.</label><caption><title>Processed dataset needed to obtain <xref rid="fig6" ref-type="fig">Figure 6</xref> with Figure 6&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig6-data2.txt" id="d64e1848" position="anchor"/></supplementary-material>
</p></caption><graphic xlink:href="elife-72626-fig6" position="float"/></fig><p>Overall, the drugs proposed through this analysis suggest the possibility to repurpose drugs that are used in treating other forms of cancer for prostate cancer and open the avenue for further experimental validations based on these suggestions.</p></sec></sec><sec id="s2-7"><title>Experimental validation of drugs in LNCaP</title><p>To validate the model predictions of the candidate drugs, we selected four drugs that target HSPs and PI3K and tested them in LNCaP cell line experiments by using endpoint cell viability measurement assays and real-time cell survival assays using the xCELLigence system (see Materials and methods). The drug selection was a compromise between the drugs identified by our analyses (<xref rid="table1" ref-type="table">Table 1</xref>) and their effect in diminishing LNCaP&#x02019;s proliferation (see the previous section). In both assays, drugs that target HSP90AA1 and PI3K/AKT pathway genes retrieved from the model analyses were found to be effective against cell proliferation.</p><p>The Hsp90 chaperone is expressed abundantly and plays a crucial role in the correct folding of a wide variety of proteins such as protein kinases and steroid hormone receptors (<xref rid="bib96" ref-type="bibr">Schopf et al., 2017</xref>). Hsp90 can act as a protector of less stable proteins produced by DNA mutations in cancer cells (<xref rid="bib7" ref-type="bibr">Barrott and Haystead, 2013</xref>; <xref rid="bib49" ref-type="bibr">Hessenkemper and Baniahmad, 2013</xref>). Currently, Hsp90 inhibitors are in clinical trials for multiple indications in cancer (<xref rid="bib20" ref-type="bibr">Chen et al., 2020</xref>; <xref rid="bib54" ref-type="bibr">Iwai et al., 2012</xref>; <xref rid="bib68" ref-type="bibr">Le et al., 2017</xref>). The PI3K/AKT signalling pathway controls many different cellular processes such as cell growth, motility, proliferation, and apoptosis and is frequently altered in different cancer cells (<xref rid="bib16" ref-type="bibr">Carceles-Cordon et al., 2020</xref>; <xref rid="bib98" ref-type="bibr">Shorning et al., 2020</xref>). Many PI3K/AKT inhibitors are in different stages of clinical development, and some of them are approved for clinical use (<xref rid="table1" ref-type="table">Table 1</xref>).</p><p>Notably, Hsp90 (NMS-E973,17-DMAG) and PI3K/AKT pathway (PI-103, Pictilisib) inhibitors showed a dose-dependent activity in the endpoint cell viability assay determined by the fluorescent resazurin after a 48 hr incubation (<xref rid="fig7" ref-type="fig">Figure 7</xref>). This dose-dependent activity is more notable in Hsp90 drugs (NMS-E973,17-DMAG) than in PI3K/AKT pathway (Pictilisib) ones and very modest for PI-103.</p><fig position="float" id="fig7"><label>Figure 7.</label><caption><title>Cell viability assay determined by the fluorescent resazurin after a 48 hours incubation showed a dose-dependent response to different inhibitors.</title><p>(<bold>A</bold>) Cell viability assay of LNCaP cell line response to 17-DMAG HSP90 inhibitor. (<bold>B</bold>) Cell viability assay of LNCaP cell line response to PI-103 PI3K/AKT pathway inhibitor. (<bold>C</bold>) Cell viability assay of LNCaP cell line response to NMS-E973 HSP90 inhibitor. (<bold>D</bold>) Cell viability assay of LNCaP cell line response to Pictilisib PI3K/AKT pathway inhibitor. Concentrations of drugs were selected to capture their drug-dose response curves. The concentrations for the NMS-E973 are different from the rest as this drug is more potent than the rest (see Materials and methods).</p><p>
<supplementary-material id="fig7scode1" position="float" content-type="local-data"><label>Figure 7&#x02014;source code 1.</label><caption><title>R code needed to obtain <xref rid="fig7" ref-type="fig">Figure 7</xref>.</title><p>Processed datasets needed are <xref rid="fig7sdata1 fig7sdata2" ref-type="supplementary-material">Figure 7&#x02014;source data 1 and 2</xref> and are located in the corresponding folder of the repository: <ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2/tree/main/Analysis%20of%20experimental%20validation" ext-link-type="uri">here</ext-link>.</p></caption><media xlink:href="elife-72626-fig7-code1.zip" id="d64e1926" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig7sdata1" position="float" content-type="local-data"><label>Figure 7&#x02014;source data 1.</label><caption><title>Processed dataset needed to obtain <xref rid="fig7" ref-type="fig">Figure 7</xref> with Figure 7&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig7-data1.txt" id="d64e1938" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig7sdata2" position="float" content-type="local-data"><label>Figure 7&#x02014;source data 2.</label><caption><title>Processed dataset needed to obtain with Figure 7&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig7-data2.txt" id="d64e1946" position="anchor"/></supplementary-material>
</p></caption><graphic xlink:href="elife-72626-fig7" position="float"/></fig><p>We studied the real-time response of LNCaP cell viability upon drug addition and saw that the LNCaP cell line is sensitive to Hsp90 and PI3K/AKT pathway inhibitors (<xref rid="fig8" ref-type="fig">Figures 8</xref> and <xref rid="fig9" ref-type="fig">9</xref>, respectively). Both Hsp90 inhibitors tested, 17-DMAG and NMS-E973, reduced the cell viability 12 hr after drug supplementation (<xref rid="fig8" ref-type="fig">Figure 8A</xref> for 17-DMAG and <xref rid="fig8" ref-type="fig">Figure 8B</xref> for NMS-E973), with 17-DMAG having a stronger effect and in a more clear concentration-dependent manner than NMS-E973 (Appendix 1, Section 8, <xref rid="app1fig40" ref-type="fig">Appendix 1&#x02014;figure 40</xref>, panels B-D for 17-DMAG and panels F-H for NMS-E973).</p><fig position="float" id="fig8"><label>Figure 8.</label><caption><title>Hsp90 inhibitors resulted in dose-dependent changes in the LNCaP cell line growth.</title><p>(<bold>A</bold>) Real-time cell electronic sensing (RT-CES) cytotoxicity assay of Hsp90 inhibitor, 17-DMAG, that uses the Cell Index as a measurement of the cell growth rate (see the Materials and methods section). The yellow dotted line represents the 17-DMAG addition. (<bold>B</bold>) RT-CES cytotoxicity assay of Hsp90 inhibitor, NMS-E973. The yellow dotted line represents the NMS-E973 addition.</p><p>
<supplementary-material id="fig8sdata1" position="float" content-type="local-data"><label>Figure 8&#x02014;source data 1.</label><caption><title>Processed dataset to obtain <xref rid="fig8" ref-type="fig">Figures 8</xref> and <xref rid="fig9" ref-type="fig">9</xref> with Figure 8&#x02014;source code 1.</title></caption><media xlink:href="elife-72626-fig8-data1.txt" id="d64e1989" position="anchor"/></supplementary-material>
</p><p>
<supplementary-material id="fig8scode1" position="float" content-type="local-data"><label>Figure 8&#x02014;source code 1.</label><caption><title>R code needed to obtain <xref rid="fig8" ref-type="fig">Figures 8</xref> and <xref rid="fig9" ref-type="fig">9</xref> with Figure 8&#x02014;source data 1.</title><p>Processed dataset needed is <xref rid="fig8sdata1" ref-type="supplementary-material">Figure 8&#x02014;source data 1</xref> and is located in the corresponding folder of the repository: <ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2/tree/main/Analysis%20of%20experimental%20validation" ext-link-type="uri">here</ext-link>.</p></caption><media xlink:href="elife-72626-fig8-code1.zip" id="d64e2012" position="anchor"/></supplementary-material>
</p></caption><graphic xlink:href="elife-72626-fig8" position="float"/></fig><fig position="float" id="fig9"><label>Figure 9.</label><caption><title>PI3K/AKT pathway inhibition with different PI3K/AKT inhibitors shows the dose-dependent response in LNCaP cell line growth.</title><p>(<bold>A</bold>) Real-time cell electronic sensing (RT-CES) cytotoxicity assay of PI3K/AKT pathway inhibitor, PI-103, that uses the Cell Index as a measurement of the cell growth rate (see the Materials and methods section). The yellow dotted line represents the PI-103 addition. (<bold>B</bold>) RT-CES cytotoxicity assay of PI3K/AKT pathway inhibitor, Pictilisib. The yellow dotted line represents the Pictilisib addition.</p></caption><graphic xlink:href="elife-72626-fig9" position="float"/></fig><p>Likewise, both PI3K/AKT pathway inhibitors tested, Pictilisib and PI-103, reduced the cell viability immediately after drug supplementation (<xref rid="fig9" ref-type="fig">Figure 9A</xref> for Pictilisib and <xref rid="fig9" ref-type="fig">Figure 9B</xref> for PI-103), in a concentration-dependent manner (Appendix 1, Section 8, <xref rid="app1fig41" ref-type="fig">Appendix 1&#x02014;figure 41B-D</xref>, for Pictilisib and panels F-H for PI-103). In addition, Hsp90 inhibitors had a more prolonged effect on the cells&#x02019; proliferation than PI3K/AKT pathway inhibitors.</p></sec></sec><sec sec-type="discussion" id="s3"><title>Discussion</title><p>Clinical assessment of cancers is moving toward more precise, personalised treatments, as the times of one-size-fits-all treatments are no longer appropriate, and patient-tailored models could boost the success rate of these treatments in clinical practice. In this study, we set out to develop a methodology to investigate drug treatments using personalised Boolean models. Our approach consists of building a model that represents the patient-specific disease status and retrieving a list of proposed interventions that affect this disease status, notably by reducing its pro-cancerous behaviours. In this work, we have showcased this methodology by applying it to TCGA prostate cancer patients and to GDSC prostate cancer cell lines, finding patient- and cell line-specific targets and validating selected cell line-specific predicted targets (<xref rid="fig1" ref-type="fig">Figure 1</xref>).</p><p>First, a prostate cancer Boolean model that encompasses relevant signalling pathways in cancer was constructed based on already published models, experimental data analyses and pathway databases (<xref rid="fig2" ref-type="fig">Figure 2</xref>). The influence network and the assignment of logical rules for each node of this network were obtained from known interactions described in the literature (<xref rid="fig3" ref-type="fig">Figure 3</xref>). This model describes the regulation of invasion, migration, cell cycle, apoptosis, androgen, and growth factors signalling in prostate cancer (Appendix 1, Section 1).</p><p>Second, from this generic Boolean model, we constructed personalised models using the different datasets, that is 488 patients from TCGA and eight cell lines from GDSC. We obtained Gleason score-specific behaviours for TCGA&#x02019;s patients when studying their <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis</italic> scores, observing that high <italic toggle="yes">Proliferation</italic> scores are higher in high Gleason grades (<xref rid="fig4" ref-type="fig">Figure 4</xref>). Thus, the use of these personalised models can help rationalise the relationship of Gleason grading with some of these phenotypes.</p><p>Likewise, GDSC data was used with the prostate model to obtain cell line-specific prostate models (<xref rid="fig6" ref-type="fig">Figure 6</xref>). These models show differential behaviours, notably in terms of <italic toggle="yes">Invasion</italic> and <italic toggle="yes">Proliferation</italic> phenotypes (Appendix 1, Section 5, <xref rid="app1fig21" ref-type="fig">Appendix 1&#x02014;figure 21</xref>). One of these cell line-specific models, LNCaP, was chosen, and the effects of all its genetic perturbations were thoroughly studied. We studied 32,258 mutants, including single and double mutants, knock-out and over-expressed, and their phenotypes (Appendix 1, Section 6.1, <xref rid="app1fig28" ref-type="fig">Appendix 1&#x02014;figures 28</xref> and <xref rid="app1fig29" ref-type="fig">29</xref>). Thirty-two knock-out perturbations that depleted <italic toggle="yes">Proliferation</italic> and/or increased <italic toggle="yes">Apoptosis</italic> were identified, and 16 of them were selected for further analyses (<xref rid="table1" ref-type="table">Table 1</xref>). The LNCaP-specific model was simulated using different initial conditions that capture different growth media&#x02019;s specificities, such as RPMI media with and without androgen or epidermal growth factor (Appendix 1, Section 6, <xref rid="app1fig27" ref-type="fig">Appendix 1&#x02014;figure 27</xref>).</p><p>Third, these personalised models were used to simulate the inhibition of druggable genes and proteins, uncovering new treatment&#x02019;s combination and their synergies. We developed a methodology to simulate drug inhibitions in Boolean models, termed PROFILE_v2, as an extension of previous works (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>). The LNCaP-specific model was used to obtain simulations with nodes and pairs of nodes corresponding to the genes of interest inhibited with varying strengths. This study allowed us to compile a list of potential targets (<xref rid="table1" ref-type="table">Table 1</xref>) and to identify potential synergies among genes in the model (<xref rid="fig5" ref-type="fig">Figure 5</xref>). Some of the drugs that targeted these genes, such as AKT and TERT, were identified in GDSC as having more sensitivity in LNCaP than in the rest of the prostate cancer cell lines (<xref rid="fig6" ref-type="fig">Figure 6</xref>). In addition, drugs that targeted genes included in the model allowed the identification of cell line specificities (Appendix 1, Section 5).</p><p>Fourth, we validated the effect of Hsp90 and PI3K/AKT pathway inhibitors on the LNCaP cell line experimentally, finding a concentration-dependent inhibition of the cell line viability as predicted, confirming the role of the drugs targeting these proteins in reducing LNCaP&#x02019;s proliferation (<xref rid="fig7" ref-type="fig">Figures 7</xref> and <xref rid="fig8" ref-type="fig">8</xref>). Notably, these targets have been studied in other works on prostate cancer (<xref rid="bib20" ref-type="bibr">Chen et al., 2020</xref>; <xref rid="bib68" ref-type="bibr">Le et al., 2017</xref>).</p><p>The study presented here enables the study of drug combinations and their synergies. One reason for searching for combinations of drugs is that these have been described for allowing the use of lower doses of each of the two drugs reducing their toxicity (<xref rid="bib8" ref-type="bibr">Bayat Mokhtari et al., 2017</xref>), evading compensatory mechanisms and combating drug resistances (<xref rid="bib4" ref-type="bibr">Al-Lazikani et al., 2012</xref>; <xref rid="bib63" ref-type="bibr">Krzyszczyk et al., 2018</xref>).</p><p>Even if this approach is attractive and promising, it has some limitations. The scope of present work is to test this methodology on a prostate model and infer patient-specific prostate cancer treatments. The method need to be adapted if it were to be expanded to study other cancers by using other models and target lists. The analyses performed with the mathematical model do not aim to predict drug dosages per se but to help in the identification of potential candidates. The patient-specific changes in <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis</italic> scores upon mutation are maximal theoretical yields that are used to rank the different potential treatments and should not be used as a direct target for experimental results or clinical trials. Our methodology suggests treatments for individual patients, but the obtained results vary greatly from patient to patient, which is not an uncommon issue of personalised medicine (<xref rid="bib22" ref-type="bibr">Ciccarese et al., 2017</xref>; <xref rid="bib76" ref-type="bibr">Molinari et al., 2018</xref>). This variability is an economic challenge for labs and companies to pursue true patient-specific treatments and also poses challenges in clinical trial designs aimed at validating the model based on the selection of treatments (<xref rid="bib25" ref-type="bibr">Cunanan et al., 2017</xref>). Nowadays, and because of these constraints, it might be more commercially interesting to target group-specific treatments, which can be more easily related to clinical stages of the disease.</p><p>Mathematical modelling of patient profiles helps to classify them in groups with differential characteristics, providing, in essence, a grade-specific treatment. We, therefore, based our analysis on clinical grouping defined by the Gleason grades, but some works have emphasised the difficulty to properly assess them (<xref rid="bib19" ref-type="bibr">Chen and Zhou, 2016</xref>) and, as a result, may not be the perfect predictor for the patient subgrouping in this analysis, even though it is the only available one for these datasets. The lack of subgrouping that stratifies patients adequately may undermine the analysis of our results and could explain the <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis</italic> scores of high-grade and low-grade Gleason patients.</p><p>Moreover, the behaviours observed in the simulations of the cell line-specific models do not always correspond to what is reported in the literature. The differences between simulation results and biological characteristics could be addressed in further studies by including other pathways, for example, better describing the DNA repair mechanisms, or by tailoring the model with different sets of data, as the data used to personalise these models do not allow for clustering these cell lines according to their different characteristics (Appendix 1, Section 5, <xref rid="app1fig24" ref-type="fig">Appendix 1&#x02014;figures 24</xref> and <xref rid="app1fig25" ref-type="fig">25</xref>). In this sense, another limitation is that we use static data or a snapshot of dynamic data to build dynamic models and to study its stochastic results. Thus, these personalised models would likely improve their performance if they were fitted to dynamic data (<xref rid="bib94" ref-type="bibr">Saez-Rodriguez and Bl&#x000fc;thgen, 2020</xref>) or quantitative versions of the models were built, such as ODE-based, that may capture more fine differences among cell lines. As perspectives, we are working on integrating these models in multiscale models to study the effect of the tumour microenvironment (<xref rid="bib84" ref-type="bibr">Ponce-de-Leon et al., 2021</xref>; <xref rid="bib85" ref-type="bibr">Ponce-de-Leon et al., 2022</xref>), on including information to simulate multiple reagents targeting a single node of the model, on scaling these multiscale models to exascale high-performance computing clusters (<xref rid="bib78" ref-type="bibr">Montagud et al., 2021</xref>; <xref rid="bib95" ref-type="bibr">Saxena et al., 2021</xref>), and on streamlining these studies using workflows in computing clusters to fasten the processing of new, bigger cohorts, as in the PerMedCoE project (<ext-link xlink:href="https://permedcoe.eu/" ext-link-type="uri">https://permedcoe.eu/</ext-link>).</p><p>The present work contributes to efforts aimed at using modelling (<xref rid="bib32" ref-type="bibr">Eduati et al., 2020</xref>; <xref rid="bib89" ref-type="bibr">Rivas-Barragan et al., 2020</xref>; <xref rid="bib45" ref-type="bibr">G&#x000f3;mez Tejeda Za&#x000f1;udo et al., 2017</xref>) and other computational methods (<xref rid="bib71" ref-type="bibr">Madani Tonekaboni et al., 2018</xref>; <xref rid="bib75" ref-type="bibr">Menden et al., 2019</xref>) for the discovery of novel drug targets and combinatorial strategies. Our study expands the prostate drug catalogue and improves predictions of the impact of these in clinical strategies for prostate cancer by proposing and grading the effectiveness of a set of drugs that could be used off-label or repurposed. The insights gained from this study present the potential of using personalised models to obtain precise, personalised drug treatments for cancer patients.</p></sec><sec sec-type="materials|methods" id="s4"><title>Materials and methods</title><sec id="s4-1"><title>Data acquisition</title><p>Publicly available data of 489 human prostate cancer patients from TCGA described in <xref rid="bib50" ref-type="bibr">Hoadley et al., 2018</xref> were used in the present work. We gathered mutations, CNA, RNA and clinical data from cBioPortal (<ext-link xlink:href="https://www.cbioportal.org/study/summary?id=prad_tcga_pan_can_atlas_2018" ext-link-type="uri">https://www.cbioportal.org/study/summary?id=prad_tcga_pan_can_atlas_2018</ext-link>) for all of these samples resulting in 488 with complete omics datasets.</p><p>Publicly available data of cell lines used in the present work were obtained from the Genomics of Drug Sensitivity in Cancer database (GDSC) (<xref rid="bib53" ref-type="bibr">Iorio et al., 2016</xref>). Mutations, CNA and RNA data, as well as cell lines descriptors, were downloaded from (<ext-link xlink:href="https://www.cancerrxgene.org/downloads" ext-link-type="uri">https://www.cancerrxgene.org/downloads</ext-link>). In this work, we have used 3- and 5-stage Gleason grades. Their correspondence is the following: GG Low is GG 1, GG Intermediate is GG 2 and 3, and GG High is GG 4 and 5.</p><p>All these data were used to personalise Boolean models using our PROFILE method (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>).</p></sec><sec id="s4-2"><title>Prior knowledge network construction</title><p>Several sources were used in building this prostate Boolean model and, in particular, the model published by <xref rid="bib39" ref-type="bibr">Fumi&#x000e3; and Martins, 2013</xref>. This model includes several signalling pathways such as the ones involving receptor tyrosine kinase (RTKs), phosphatidylinositol 3-kinase (PI3K)/AKT, WNT/&#x003b2;-Catenin, transforming growth factor-&#x003b2; (TGF-&#x003b2;)/Smads, cyclins, retinoblastoma protein (Rb), hypoxia-inducible transcription factor (HIF-1), p53 and ataxia-telangiectasia mutated (ATM)/ataxia-telangiectasia and Rad3-related (ATR) protein kinases. The model includes these pathways as well as the substantial cross-talks among them. For a complete description of the process of construction, see Appendix 1, Section 1.</p><p>The model also includes several pathways that have a relevant role in our datasets identified by ROMA (<xref rid="bib74" ref-type="bibr">Martignetti et al., 2016</xref>), a software that uses the first principal component of a PCA analysis to summarise the coexpression of a group of genes in the gene set, identifying significantly overdispersed pathways with a relevant role in a given set of samples. This software was applied to the TCGA transcriptomics data using the gene sets described in the Atlas of Cancer Signaling Networks, ACSN (<xref rid="bib65" ref-type="bibr">Kuperstein et al., 2015</xref>) (<ext-link xlink:href="http://www.acsn.curie.fr/" ext-link-type="uri">http://www.acsn.curie.fr/</ext-link>) and in the Hallmarks (<xref rid="bib70" ref-type="bibr">Liberzon et al., 2015</xref>) (Appendix 1, Section 1.1.3, <xref rid="app1fig1" ref-type="fig">Appendix 1&#x02014;figure 1</xref>) and highlighted the signalling pathways that show high variance across all samples, suggesting candidate pathways and genes. Additionally, OmniPath (<xref rid="bib111" ref-type="bibr">T&#x000fc;rei et al., 2021</xref>) was used to extend the model and complete it, connecting the nodes from Fumi&#x000e3; and Martins and the ones from ROMA analysis. OmniPath is a comprehensive collection of literature-curated human signalling pathways, which includes several databases such as Signor (<xref rid="bib83" ref-type="bibr">Perfetto et al., 2016</xref>) or Reactome (<xref rid="bib33" ref-type="bibr">Fabregat et al., 2016</xref>) and that can be queried using pypath, a Python module for molecular networks and pathways analyses.</p><p>Fusion genes are frequently found in human prostate cancer and have been identified as a specific subtype marker (<xref rid="bib15" ref-type="bibr">Cancer Genome Atlas Research Network, 2015</xref>). The most frequent is TMPRSS2:ERG, as it involves the transcription factor ERG, which leads to cell-cycle progression. ERG fuses with the AR-regulated TMPRSS2 gene promoter to form an oncogenic fusion gene that is especially common in hormone-refractory prostate cancer, conferring androgen responsiveness to ERG. A literature search reveals that ERG directly regulates EZH2, oncogene c-Myc and many other targets in prostate cancer (<xref rid="bib64" ref-type="bibr">Kunderfranco et al., 2010</xref>).</p><p>We modelled the gene fusion with activation of ERG by the decoupling of ERG in a special node <italic toggle="yes">AR_ERG</italic> that is only activated by the <italic toggle="yes">AR</italic> when the <italic toggle="yes">fused_event</italic> input node is active. In the healthy case, <italic toggle="yes">fused_event</italic> (that represents TMPRSS2:ERG fusion event) is fixed to 0 or inactive. The occurrence of the gene fusion is represented with the model perturbation where <italic toggle="yes">fused_event</italic> is fixed to 1. This <italic toggle="yes">AR_ERG</italic> node is further controlled by tumour suppressor NKX3-1 that accelerates <italic toggle="yes">DNA_repair</italic> response, and avoids the gene fusion TMPRSS2:ERG. Thus, loss of NKX3-1 favours recruitment to the ERG gene breakpoint of proteins that promote error-prone non-homologous end-joining (<xref rid="bib12" ref-type="bibr">Bowen et al., 2015</xref>).</p><p>The network was further documented using up-to-date literature and was constructed using GINsim (<xref rid="bib18" ref-type="bibr">Chaouiya et al., 2012</xref>), which allowed us to study its stable states and network properties.</p></sec><sec id="s4-3"><title>Boolean model construction</title><p>We converted the network to a Boolean model by defining a regulatory graph, where each node is associated with discrete levels of activity (0 or 1). Each edge represents a regulatory interaction between the source and target nodes and is labelled with a threshold and a sign (positive or negative). The model is completed by logical rules (or functions), which assign a target value to each node for each regulator level combination (<xref rid="bib2" ref-type="bibr">Abou-Jaoud&#x000e9; et al., 2016</xref>; <xref rid="bib18" ref-type="bibr">Chaouiya et al., 2012</xref>). The regulatory graph was constructed using GINsim software (<xref rid="bib18" ref-type="bibr">Chaouiya et al., 2012</xref>) and then exported in a format readable by MaBoSS software (see below) in order to perform stochastic simulations on the Boolean model.</p><p>The final model has a total of 133 nodes and 449 edges (<xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref>) and includes pathways such as androgen receptor and growth factor signalling, several signalling pathways (Wnt, NFkB, PI3K/AKT, MAPK, mTOR, SHH), cell cycle, epithelial-mesenchymal transition (EMT), Apoptosis, DNA damage, etc. This model has nine inputs (<italic toggle="yes">EGF, FGF, TGF beta, Nutrients, Hypoxia, Acidosis, Androgen, TNF alpha,</italic> and <italic toggle="yes">Carcinogen</italic> presence) and six outputs (<italic toggle="yes">Proliferation</italic>, <italic toggle="yes">Apoptosis, Invasion, Migration,</italic> (bone) <italic toggle="yes">Metastasis,</italic> and <italic toggle="yes">DNA repair</italic>). Note that a node in the network can represent complexes or families of proteins (e.g. AMPK represents the genes PRKAA1, PRKAA2, PRKAB1, PRKAB2, PRKAG1, PRKAG2, PRKAG3). The correspondence can be found in &#x0201c;Montagud2022_interactions_sources.xlsx&#x0201d; and &#x0201c;Montagud2022_nodes_in_pathways.xlsx&#x0201d; in <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref>.</p><p>This model was deposited in the GINsim Database with identifier 252 (<ext-link xlink:href="http://ginsim.org/model/signalling-prostate-cancer" ext-link-type="uri">http://ginsim.org/model/signalling-prostate-cancer</ext-link>) and in BioModels (<xref rid="bib72" ref-type="bibr">Malik-Sheriff et al., 2020</xref>) with identifier MODEL2106070001 (<ext-link xlink:href="https://www.ebi.ac.uk/biomodels/MODEL2106070001" ext-link-type="uri">https://www.ebi.ac.uk/biomodels/MODEL2106070001</ext-link>). <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref> is provided as a zipped folder with the model in several formats: MaBoSS, GINsim, SBML, as well as images of the networks and their annotations. An extensive description of the model construction can be found in the Appendix 1, Section 1.</p></sec><sec id="s4-4"><title>Stochastic Boolean model simulation</title><p>MaBoSS (<xref rid="bib104" ref-type="bibr">Stoll et al., 2017</xref>; <xref rid="bib103" ref-type="bibr">Stoll et al., 2012</xref>) is a C++ software for stochastically simulating continuous/discrete-time Markov processes defined on the state transition graph (STG) describing the dynamics of a Boolean model (for more details, see <xref rid="bib2" ref-type="bibr">Abou-Jaoud&#x000e9; et al., 2016</xref>; <xref rid="bib18" ref-type="bibr">Chaouiya et al., 2012</xref>). MaBoSS associates transition rates to each node&#x02019;s activation and inhibition, enabling it to account for different time scales of the processes described by the model. Probabilities to reach a phenotype (to have value ON) are thus computed by simulating random walks on the probabilistic STG. Since a state in the STG can combine the activation of several phenotypic variables, not all phenotype probabilities are mutually exclusive (like the ones in Appendix 1, Section 6.1, <xref rid="app1fig28" ref-type="fig">Appendix 1&#x02014;figure 28</xref>). Using MaBoSS, we can study an increase or decrease of a phenotype probability when the model variables are altered (nodes status, initial conditions and transition rates), which may correspond to the effect of particular genetic or environmental perturbation. In the present work, the use of MaBoSS was focused on the readouts of the model, but this can be done for any node of the model.</p><p>MaBoSS applies Monte-Carlo kinetic algorithm (i.e. Gillespie algorithm) to the STG to produce time trajectories (<xref rid="bib104" ref-type="bibr">Stoll et al., 2017</xref>; <xref rid="bib103" ref-type="bibr">Stoll et al., 2012</xref>), so time evolution of probabilities are estimated once a set of initial conditions are defined and a maximum time is set to ensure that the simulations reach asymptotic solutions. Results are analysed in two ways: (1) the trajectories for particular model states (states of nodes) can be interpreted as the evolution of a cell population as a function of time and (2) asymptotic solutions can be represented as pie charts to illustrate the proportions of cells in particular model states. Stochastic simulations with MaBoSS have already been successfully applied to study several Boolean models (<xref rid="bib13" ref-type="bibr">Calzone et al., 2010</xref>; <xref rid="bib23" ref-type="bibr">Cohen et al., 2015</xref>; <xref rid="bib87" ref-type="bibr">Remy et al., 2015</xref>). A description of the methods we have used for the simulation of the model can be found in the Appendix 1, Section 2.</p></sec><sec id="s4-5"><title>Data tailoring the Boolean model</title><p>Logical models were tailored to a dataset using PROFILE to obtain personalised models that capture the particularities of a set of patients (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>) and cell lines (<xref rid="bib10" ref-type="bibr">B&#x000e9;al et al., 2021</xref>). Proteomics, transcriptomics, mutations and CNA data can be used to modify different variables of the MaBoSS framework, such as node activity status, transition rates and initial conditions. The resulting ensemble of models is a set of personalised variants of the original model that can show great phenotypic differences. Different recipes (use of a given data type to modify a given MaBoSS variable) can be tested to find the combination that better correlates to a given clinical or otherwise descriptive data. In the present case, TCGA patient-specific models were built using mutations, CNA and/or RNA expression data. After studying the effect of these recipes in the clustering of patients according to their Gleason grading (Appendix 1, Section 4.1, <xref rid="app1fig10" ref-type="fig">Appendix 1&#x02014;figures 10</xref>&#x02013;<xref rid="app1fig11" ref-type="fig">14</xref>), we chose to use mutations and CNA as discrete data and RNA expression as continuous data.</p><p>Likewise, we tried different personalisation recipes to personalise the GDSC prostate cell lines models, but as they had no associated clinical grouping features, we were left with the comparison of the different values for the model&#x02019;s outputs among the recipes (Appendix 1, Section 5, <xref rid="app1fig23" ref-type="fig">Appendix 1&#x02014;figure 23</xref>). We used mutation data as discrete data and RNA expression as continuous data as it included the most quantity of data and reproduced the desired results (Appendix 1, Section 5, <xref rid="app1fig23" ref-type="fig">Appendix 1&#x02014;figure 23</xref>). We decided not to include CNA as discrete data as it forced LNCaP proliferation to be zero by forcing the E2F1 node to be 0 and the SMAD node to be 1 throughout the simulation (for more details, refer to Appendix 1, Section 5).</p><p>More on PROFILE&#x02019;s methodology can be found in its own work (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>) and at its dedicated GitHub repository (<ext-link xlink:href="https://github.com/sysbio-curie/PROFILE" ext-link-type="uri">https://github.com/sysbio-curie/PROFILE</ext-link>; <xref rid="bib11" ref-type="bibr">B&#x000e9;al, 2022</xref>). A description of the methods we have used for the personalisation of the models can be found in the Appendix 1, Section 3. The analysis of the TCGA personalisations and their patient-specific drug treatments can be found in Appendix 1, Section 4. The analysis of the prostate cell lines personalisations can be found in Appendix 1, Section 5, with a special focus on the LNCaP cell line model analysis in Section 6.</p></sec><sec id="s4-6"><title>High-throughput mutant analysis of Boolean models</title><p>MaBoSS allows the study of knock-out or loss-of-function (node forced to 0) and gain-of-function (node forced to 1) mutants as genetic perturbations and of initial conditions as environmental perturbations. Phenotypes&#x02019; stabilities against perturbations can be studied and allow to determine driver mutations that promote phenotypic transitions (<xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>).</p><p>Genetic interactions were thoroughly studied using our pipeline of computational methods for Boolean modelling of biological networks (available at <ext-link xlink:href="https://github.com/sysbio-curie/Logical_modelling_pipeline" ext-link-type="uri">https://github.com/sysbio-curie/Logical_modelling_pipeline</ext-link>; <xref rid="bib80" ref-type="bibr">Montagud, 2022b</xref>). The LNCaP-specific Boolean model was used to perform single and double knock-out (node forced to 0) and gain-of-function (node forced to 1) mutants for each one of the 133 nodes, resulting in a total of 32,258 models. These were simulated under the same initial conditions, their phenotypic results were collected, and a PCA was applied on the wild type-centred matrix (Appendix 1, Section 6.1, <xref rid="app1fig28" ref-type="fig">Appendix 1&#x02014;figures 28</xref> and <xref rid="app1fig29" ref-type="fig">29</xref>). In addition, we found that the LNCaP model is very robust against perturbations of its logical rules by systematically changing an AND for an OR gate or vice versa in all of its logical rules (Appendix 1, Section 6.2, <xref rid="app1fig30" ref-type="fig">Appendix 1&#x02014;figures 30</xref> and <xref rid="app1fig31" ref-type="fig">31</xref>).</p><p>The 488 TCGA patient-specific models were studied in a similar way, but only perturbing 16 nodes from <xref rid="table1" ref-type="table">Table 1</xref> shortlisted for their therapeutic target potential (AKT, AR, Caspase8, cFLAR, EGFR, ERK, GLUT1, HIF-1, HSPs, MEK1_2, MYC_MAX, p14ARF, PI3K, ROS, SPOP, and TERT). Then, the nodes that mostly contributed to a decrease of <italic toggle="yes">Proliferation</italic> (Appendix 1, Section 4.2, <xref rid="app1fig19" ref-type="fig">Appendix 1&#x02014;figure 19</xref>) or an increase in <italic toggle="yes">Apoptosis</italic> (Appendix 1, Section 4.2, <xref rid="app1fig20" ref-type="fig">Appendix 1&#x02014;figure 20</xref>) were gathered from the 488 models perturbed.</p><p>Additionally, the results of the LNCaP model&#x02019;s double mutants were used to quantify the level of genetic interactions (epistasis or otherwise) (<xref rid="bib31" ref-type="bibr">Drees et al., 2005</xref>) between two genetic perturbations (resulting from either the gain-of-function mutation of a gene or from its knock-out or loss-of-function mutation) with respect to wild type phenotypes&#x02019; probabilities (<xref rid="bib14" ref-type="bibr">Calzone et al., 2015</xref>). The method was applied to the LNCaP model studying <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis</italic> scores (Appendix 1, Section 7.3.2, <xref rid="app1fig34" ref-type="fig">Appendix 1&#x02014;figures 34</xref> and <xref rid="app1fig35" ref-type="fig">35</xref>).</p><p>This genetic interaction study uses the following equation for each gene pair, which is equation 2 in <xref rid="bib14" ref-type="bibr">Calzone et al., 2015</xref>:<disp-formula id="equ1"><label>(1)</label><mml:math id="m1"><mml:mrow><mml:msub><mml:mi>&#x003f5;</mml:mi><mml:mi>&#x003d5;</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi>A</mml:mi><mml:mo>,</mml:mo><mml:mi>B</mml:mi></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:msubsup><mml:mi>f</mml:mi><mml:mi>&#x003d5;</mml:mi><mml:mrow><mml:mi>A</mml:mi><mml:mi>B</mml:mi></mml:mrow></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:mi>&#x003c8;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msubsup><mml:mi>f</mml:mi><mml:mi>&#x003d5;</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:msubsup><mml:mi>f</mml:mi><mml:mi>&#x003d5;</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mrow><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>where <inline-formula><mml:math id="inf1"><mml:msubsup><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x003d5;</mml:mi></mml:mrow><mml:mrow><mml:mi>A</mml:mi></mml:mrow></mml:msubsup></mml:math></inline-formula> and <inline-formula><mml:math id="inf2"><mml:msubsup><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x003d5;</mml:mi></mml:mrow><mml:mrow><mml:mi>B</mml:mi></mml:mrow></mml:msubsup></mml:math></inline-formula> are phenotype <inline-formula><mml:math id="inf3"><mml:mi>&#x003d5;</mml:mi></mml:math></inline-formula> fitness values of single gene defects, <inline-formula><mml:math id="inf4"><mml:msubsup><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x003d5;</mml:mi></mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mi>B</mml:mi></mml:mrow></mml:msubsup></mml:math></inline-formula> is the phenotype <inline-formula><mml:math id="inf5"><mml:mi>&#x003d5;</mml:mi></mml:math></inline-formula> fitness of the double mutant, and <inline-formula><mml:math id="inf6"><mml:mi>&#x003c8;</mml:mi><mml:mfenced separators="|"><mml:mrow><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi></mml:mrow></mml:mfenced></mml:math></inline-formula> is one of the four functions:<disp-formula id="equ2"><label>(2)</label><mml:math id="m2"><mml:mrow><mml:mtable columnalign="left left" columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:msup><mml:mi>&#x003c8;</mml:mi><mml:mrow><mml:mi>A</mml:mi><mml:mi>D</mml:mi><mml:mi>D</mml:mi></mml:mrow></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>x</mml:mi><mml:mo>+</mml:mo><mml:mi>y</mml:mi><mml:mtext>&#x000a0;</mml:mtext><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">d</mml:mi><mml:mi mathvariant="normal">d</mml:mi><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">t</mml:mi><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">v</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msup><mml:mi>&#x003c8;</mml:mi><mml:mrow><mml:mi>L</mml:mi><mml:mi>O</mml:mi><mml:mi>G</mml:mi></mml:mrow></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>l</mml:mi><mml:mi>o</mml:mi><mml:msub><mml:mi>g</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msup><mml:mn>2</mml:mn><mml:mi>x</mml:mi></mml:msup><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msup><mml:mn>2</mml:mn><mml:mi>y</mml:mi></mml:msup><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mtext>&#x000a0;</mml:mtext><mml:mo stretchy="false">(</mml:mo><mml:mi>log</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msup><mml:mi>&#x003c8;</mml:mi><mml:mrow><mml:mi>M</mml:mi><mml:mi>L</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>x</mml:mi><mml:mo>&#x02217;</mml:mo><mml:mi>y</mml:mi><mml:mtext>&#x000a0;</mml:mtext><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="normal">m</mml:mi><mml:mi mathvariant="normal">u</mml:mi><mml:mi mathvariant="normal">l</mml:mi><mml:mi mathvariant="normal">t</mml:mi><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">p</mml:mi><mml:mi mathvariant="normal">l</mml:mi><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">c</mml:mi><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">t</mml:mi><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">v</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msup><mml:mi>&#x003c8;</mml:mi><mml:mrow><mml:mi>M</mml:mi><mml:mi>I</mml:mi><mml:mi>N</mml:mi></mml:mrow></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>m</mml:mi><mml:mi>i</mml:mi><mml:mi>n</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="normal">m</mml:mi><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">n</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula></p><p>To choose the best definition of <inline-formula><mml:math id="inf7"><mml:mi>&#x003c8;</mml:mi><mml:mfenced separators="|"><mml:mrow><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi></mml:mrow></mml:mfenced></mml:math></inline-formula> , the Pearson correlation coefficient is computed between the fitness values observed in all double mutants and estimated by the null model (more information on <xref rid="bib31" ref-type="bibr">Drees et al., 2005</xref>). Regarding the <inline-formula><mml:math id="inf8"><mml:msubsup><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x003d5;</mml:mi></mml:mrow><mml:mrow><mml:mi>X</mml:mi></mml:mrow></mml:msubsup></mml:math></inline-formula> fitness value, to a given phenotype <inline-formula><mml:math id="inf9"><mml:mi>&#x003d5;</mml:mi></mml:math></inline-formula>, <inline-formula><mml:math id="inf10"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msubsup><mml:mi>f</mml:mi><mml:mi>&#x003d5;</mml:mi><mml:mi>X</mml:mi></mml:msubsup><mml:mo>&#x0003c;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> represents deleterious, <inline-formula><mml:math id="inf11"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msubsup><mml:mi>f</mml:mi><mml:mi>&#x003d5;</mml:mi><mml:mi>X</mml:mi></mml:msubsup><mml:mo>&#x0003e;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> beneficial and <inline-formula><mml:math id="inf12"><mml:msubsup><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x003d5;</mml:mi></mml:mrow><mml:mrow><mml:mi>X</mml:mi></mml:mrow></mml:msubsup><mml:mo>&#x02248;</mml:mo><mml:mn>1</mml:mn></mml:math></inline-formula> neutral mutation.</p></sec><sec id="s4-7"><title>Drug simulations in Boolean models</title><p>Logical models can be used to simulate the effect of therapeutic interventions and predict the expected efficacy of candidate drugs on different genetic and environmental backgrounds by using our PROFILE_v2 methodology. MaBoSS can perform simulations changing the proportion of activated and inhibited status of a given node. This can be determined in the configuration file of each model (see, for instance, the &#x02018;istate&#x02019; section of the CFG files in the <xref rid="supp1 supp3 supp5" ref-type="supplementary-material">Supplementary files 1; 3 and 5</xref>). For instance, out of 5,000 trajectories of the Gillespie algorithm, MaBoSS can simulate 70% of them with an activated <italic toggle="yes">AKT</italic> and 30% with an inhibited <italic toggle="yes">AKT</italic> node. The phenotypes&#x02019; probabilities for the 5000 trajectories are averaged, and these are considered to be representative of a model with a drug that inhibits 30% of the activity of <italic toggle="yes">AKT</italic>. The same applies for a combined drug inhibition: a simulation of 50% <italic toggle="yes">AKT</italic> activity and 50% <italic toggle="yes">PI3K</italic> will have 50% of them with an activated <italic toggle="yes">AKT</italic> and 50% with an activated <italic toggle="yes">PI3K</italic>. Combining them, this will lead to 25% of the trajectories with both <italic toggle="yes">AKT</italic> and <italic toggle="yes">PI3K</italic> active, 25% with both nodes inactive, 25% with <italic toggle="yes">AKT</italic> active and 25% with <italic toggle="yes">PI3K</italic> active.</p><p>In the present work, the LNCaP model has been simulated with different levels of node activity, with 100% of node inhibition (proper knock-out), 80%, 60%, 40%, 20%, and 0% (no inhibition), under four different initial conditions, a nutrient-rich media that simulates RPMI Gibco media with DHT (androgen), with EGF, with both and with none. In terms of the model, the initial conditions are <italic toggle="yes">Nutrients</italic> is ON and <italic toggle="yes">Acidosis</italic>, <italic toggle="yes">Hypoxia</italic>, <italic toggle="yes">TGF beta</italic>, <italic toggle="yes">Carcinogen,</italic> and <italic toggle="yes">TNF alpha</italic> are set to OFF. <italic toggle="yes">EGF</italic> and <italic toggle="yes">Androgen</italic> values vary upon simulations. We simulated the inhibition of 17 nodes of interest. These were the 16 nodes from <xref rid="table1" ref-type="table">Table 1</xref> with the addition of the fused AR-ERG (Appendix 1, Section 7.3.1, <xref rid="app1fig34" ref-type="fig">Appendix 1&#x02014;figures 34</xref> and <xref rid="app1fig35" ref-type="fig">35</xref>) and their 136 pairwise combinations (Appendix 1, Section 7.3.2, <xref rid="app1fig36" ref-type="fig">Appendix 1&#x02014;figures 36</xref> and <xref rid="app1fig37" ref-type="fig">37</xref>). As we used six different levels of activity for each node, the resulting <xref rid="app1fig36" ref-type="fig">Appendix 1&#x02014;figures 36</xref> and <xref rid="app1fig37" ref-type="fig">37</xref> comprise a total of 4,998 simulations for each phenotype (136 &#x000d7; 6 x 6 + 17 x 6).</p><p>Drug synergies have been studied using Bliss Independence. The Combination Index was calculated with the following equation (<xref rid="bib37" ref-type="bibr">Foucquier and Guedj, 2015</xref>):<disp-formula id="equ3"><label>(3)</label><mml:math id="m3"><mml:mi>C</mml:mi><mml:mi>I</mml:mi><mml:mo>=</mml:mo><mml:mfenced separators="|"><mml:mrow><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>a</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mo>-</mml:mo><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>a</mml:mi></mml:mrow></mml:msub><mml:mi>*</mml:mi><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfenced><mml:mo>/</mml:mo><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math></disp-formula></p><p>where <inline-formula><mml:math id="inf13"><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>a</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> and <inline-formula><mml:math id="inf14"><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> is the efficiency of the single drug inhibitions and <inline-formula><mml:math id="inf15"><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> is the inhibition resulting from the double drug simulations. A Combination Index (<italic toggle="yes">CI</italic>) below 1 represents synergy among drugs (Appendix 1, Section 7.3.2, <xref rid="app1fig36" ref-type="fig">Appendix 1&#x02014;figures 36</xref> and <xref rid="app1fig37" ref-type="fig">37</xref>).</p><p>This methodology can be found in its own repository: <ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2" ext-link-type="uri">https://github.com/ArnauMontagud/PROFILE_v2.</ext-link></p></sec><sec id="s4-8"><title>Identification of drugs associated with proposed targets</title><p>To identify drugs that could act as potential inhibitors of the genes identified with our models (<xref rid="table1" ref-type="table">Table 1</xref>), we explored the drug-target associations in DrugBank (<xref rid="bib115" ref-type="bibr">Wishart et al., 2018</xref>). For those genes with multiple drug-target links, only those drugs that are selective and known to have relevance in various forms of cancer are considered here.</p><p>In addition to DrugBank searches, we also conducted exhaustive searches in ChEMBL (<xref rid="bib40" ref-type="bibr">Gaulton et al., 2017</xref>) (<ext-link xlink:href="http://doi.org/10.6019/CHEMBL.database.23" ext-link-type="uri">http://doi.org/10.6019/CHEMBL.database.23</ext-link>) to suggest potential candidates for genes whose information is not well documented in Drug Bank. From the large number of bioactivities extracted from ChEMBL, we filtered human data and considered only those compounds whose bioactivities fall within a specific threshold (IC50/Kd/ Ki &#x0003c;100 nM).</p><p>We performed a target set enrichment analysis using the <italic toggle="yes">fgsea</italic> method (<xref rid="bib60" ref-type="bibr">Korotkevich et al., 2016</xref>) from the <italic toggle="yes">piano</italic> R package (<xref rid="bib113" ref-type="bibr">V&#x000e4;remo et al., 2013</xref>). We targeted pathway information from the GDSC1 and GDSC2 studies (<xref rid="bib53" ref-type="bibr">Iorio et al., 2016</xref>) as target sets and performed the enrichment analysis on the normalised drug sensitivity profile of the LNCaP cell line. We normalised drug sensitivity across cell lines in the following way: cells were ranked from most sensitive to least sensitive (using ln(IC50) as the drug sensitivity metrics), and the rank was divided by the number of cell lines tested with the given drug. Thus, the most sensitive cell line has 0, while the most resistant cell line has 1 normalised sensitivity. This rank-based metric made it possible to analyse all drug sensitivities for a given cell line without drug-specific confounding factors, like mean IC50 of a given drug, etc. (Appendix 1, Sections 7.1 and 7.2).</p></sec><sec id="s4-9"><title>Cell culture method</title><p>For the in vitro drug perturbation validations, we used the androgen-sensitive prostate adenocarcinoma cell line LNCaP purchased from American Type Culture Collection (ATCC, Manassas, WV, USA). ATCC found no <italic toggle="yes">Mycoplasma</italic> contamination and the cell line was identified using STR profiling. Cells were maintained in RPMI-1640 culture media (Gibco, Thermo Fisher Scientific, Waltham, MA, USA) containing 4.5 g/L glucose, 10% foetal bovine serum (FBS, Gibco), 1 X GlutaMAX (Gibco), 1% PenStrep antibiotics (Penicillin G sodium salt, and Streptomycin sulfate salt, Sigma-Aldrich, St. Louis, MI, USA). Cells were maintained in a humidified incubator at 37 &#x000b0;C with 5% CO<sub>2</sub> (Sanyo, Osaka, Japan).</p></sec><sec id="s4-10"><title>Drugs used in the cell culture experiments</title><p>We tested two drugs targeted at Hsp90 and two targeted at PI3K complex. 17-DMAG is an Hsp90 inhibitor with an IC50 of 62 nM in a cell-free assay (<xref rid="bib82" ref-type="bibr">Pacey et al., 2011</xref>). NMS-E973 is an Hsp90 inhibitor with DC50 of &#x0003c;10 nM for Hsp90 binding (<xref rid="bib36" ref-type="bibr">Fogliatto et al., 2013</xref>). Pictilisib is an inhibitor of PI3K <inline-formula><mml:math id="inf16"><mml:mi>&#x003b1;</mml:mi><mml:mo>/</mml:mo><mml:mi>&#x003b4;</mml:mi></mml:math></inline-formula> with IC50 of 3.3 nM in cell-free assays (<xref rid="bib117" ref-type="bibr">Zhan et al., 2017</xref>). PI-103 is a multi-targeted PI3K inhibitor for p110 <inline-formula><mml:math id="inf17"><mml:mi>&#x003b1;</mml:mi><mml:mo>/</mml:mo><mml:mi>&#x003b2;</mml:mi><mml:mo>/</mml:mo><mml:mi>&#x003b4;</mml:mi><mml:mo>/</mml:mo><mml:mi>&#x003b3;</mml:mi></mml:math></inline-formula> with IC50 of 2&#x02013;3 nM in cell-free assays and less potent inhibitor to mTOR/DNA-PK with IC50 of 30 nM (<xref rid="bib86" ref-type="bibr">Raynaud et al., 2009</xref>). All drugs were obtained from commercial vendors and added to the growth media to have concentrations of 2, 8, 32, 128, and 512 nM for NMS-E973 and 1, 5, 25, 125, and 625 nM for the rest of the drugs in the endpoint cell viability and of 3.3, 10, 30 &#x000b5;M for all the drugs in the RT-CES cytotoxicity assay.</p></sec><sec id="s4-11"><title>Endpoint cell viability measurements</title><p>In vitro toxicity of the selected inhibitors was determined using the viability of LNCaP cells, determined by the fluorescent resazurin (Sigma-Aldrich, Germany) assay as described previously (<xref rid="bib106" ref-type="bibr">Szebeni et al., 2017</xref>). Briefly, the &#x0223c;10,000 LNCaP cells were seeded into 96-well plates (Corning Life Sciences, Tewksbury, MA, USA) in 100 &#x000b5;L RPMI media and incubated overnight. Test compounds were dissolved in dimethyl sulfoxide (DMSO, Sigma-Aldrich, Germany), and cells were treated with an increasing concentration of test compounds: 2, 8, 32, 128, and 512 nM for NMS-E973 and 1, 5, 25, 125, and 625 nM for the rest of the drugs. The highest applied DMSO content of the treated cells was 0.4%. Cell viability was determined after 48 hours of incubation. Resazurin reagent (Sigma&#x02013;Aldrich, Budapest, Hungary) was added at a final concentration of 25 &#x000b5;g/mL. After 2 hr at 37 &#x000b0;C 5%, CO<sub>2</sub> (Sanyo) fluorescence (530 nm excitation/580 nm emission) was recorded on a multimode microplate reader (Cytofluor4000, PerSeptive Biosystems, Framingham, MA, USA). Viability was calculated with relation to blank wells containing media without cells and to wells with untreated cells. Each treatment was repeated in two wells per plate during the experiments, except for the PI-103 treatment with 1 nM in which only one well was used.</p><p>In these assays, a deviation of 10&#x02013;15% for in vitro cellular assays is an acceptable variation as it is a fluorescent assay that detects the cellular metabolic activity of living cells. Thus, in our analyses, we consider changes above 1.00 to be the same value as the controls.</p></sec><sec id="s4-12"><title>Real-time cell electronic sensing (RT-CES) cytotoxicity assay</title><p>A real-time cytotoxicity assay was performed as previously described (<xref rid="bib81" ref-type="bibr">Ozsv&#x000e1;ri et al., 2010</xref>). Briefly, RT-CES 96-well E-plate (BioTech Hungary, Budapest, Hungary) was coated with gelatin solution (0.2% in PBS, phosphate buffer saline) for 20 min at 37 &#x000b0;C; then gelatin was washed twice with PBS solution. Growth media (50 &#x000b5;L) was then gently dispensed into each well of the 96-well E-plate for background readings by the RT-CES system prior to the addition of 50 &#x000b5;L of the cell suspension containing 2 &#x000d7; 10<sup>4</sup> LNCaP cells. Plates were kept at room temperature in a tissue culture hood for 30 min prior to insertion into the RT-CES device in the incubator to allow cells to settle. Cell growth was monitored overnight by measurements of electrical impedance every 15 min. The next day cells were co-treated with different drugs with concentrations of 3.3, 10 and 30 &#x000b5;M. Treated and control wells were dynamically monitored over 72 hr by measurements of electrical impedance every 5 min. Each treatment was repeated in two wells per plate during the experiments, except for the 3.3 &#x000b5;M ones in which only one well was used. Continuous recording of impedance in cells was used as a measurement of the cell growth rate and reflected by the Cell Index value (<xref rid="bib100" ref-type="bibr">Solly et al., 2004</xref>).</p><p>Note that around hour 15, our RT-CES reader had a technical problem caused by a short blackout in our laboratory and the reader detected a minor voltage fluctuation while the uninterruptible power supply (UPS) was switched on. This caused differences that are consistent across all samples and replicates: all wild type and drug reads decrease at that time point, except Pictilisib that slightly increases. For the sake of transparency and as the overall dynamic was not affected, we decided not to remove these readings.</p></sec></sec></body><back><sec sec-type="funding-information"><title>Funding Information</title><p>This paper was supported by the following grants:</p><list list-type="bullet"><list-item><p><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000780</institution-id><institution>European Commission</institution></institution-wrap>
</funding-source>
<award-id>H2020-PHC-668858</award-id> to Arnau Montagud, Jonas B&#x000e9;al, Luis Tobalina, Pauline Traynard, Vigneshwari Subramanian, Bence Szalai, R&#x000f3;bert Alf&#x000f6;ldi, L&#x000e1;szl&#x000f3; Pusk&#x000e1;s, Emmanuel Barillot, Julio Saez-Rodriguez, Laurence Calzone.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000780</institution-id><institution>European Commission</institution></institution-wrap>
</funding-source>
<award-id>H2020-ICT-825070</award-id> to Arnau Montagud, Alfonso Valencia.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000780</institution-id><institution>European Commission</institution></institution-wrap>
</funding-source>
<award-id>H2020-ICT-951773</award-id> to Arnau Montagud, Alfonso Valencia, Emmanuel Barillot, Julio Saez-Rodriguez, Laurence Calzone.</p></list-item></list></sec><ack id="ack"><title>Acknowledgements</title><p>The authors acknowledge the help provided by Jelena &#x0010c;uklina at ETH Zurich, Vincent No&#x000eb;l at Institut Curie, Annika Meert at Barcelona Supercomputing Center and Aur&#x000e9;lien Naldi at INRIA Saclay. The authors acknowledge the reviewers for their comments and suggestions that helped improve and clarify this article. The authors acknowledge the technical expertise and assistance provided by the Spanish Supercomputing Network (Red Espa&#x000f1;ola de Supercomputaci&#x000f3;n), as well as the computer resources used: the LaPalma Supercomputer, located at the Instituto de Astrof&#x000ed;sica de Canarias and MareNostrum4, located at the Barcelona Supercomputing Center. This work has been partially supported by the European Commission under the PrECISE project (H2020-PHC-668858), the INFORE project (H2020-ICT-825070) and the PerMedCoE (H2020-ICT-951773).</p></ack><sec sec-type="additional-information" id="s5"><title>Additional information</title><fn-group content-type="competing-interest"><title>Competing interests</title><fn fn-type="COI-statement" id="conf1"><p>No competing interests declared.</p></fn><fn fn-type="COI-statement" id="conf2"><p>is a full-time employee and shareholder of AstraZeneca.</p></fn><fn fn-type="COI-statement" id="conf3"><p>is a full-time employee of AstraZeneca.</p></fn><fn fn-type="COI-statement" id="conf4"><p>is CEO of Astridbio Technologies Ltd.</p></fn><fn fn-type="COI-statement" id="conf5"><p>is a scientific advisor of Astridbio Technologies Ltd.</p></fn><fn fn-type="COI-statement" id="conf6"><p>Reviewing editor, eLife.</p></fn><fn fn-type="COI-statement" id="conf7"><p>reports funding from GSK and Sanofi and fees from Travere Therapeutics and Astex.</p></fn></fn-group><fn-group content-type="author-contribution"><title>Author contributions</title><fn fn-type="con" id="con1"><p>Data curation, Formal analysis, Investigation, Methodology, Project administration, Resources, Software, Validation, Visualization, Writing &#x02013; original draft, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con2"><p>Formal analysis, Investigation, Methodology, Software, Visualization, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con3"><p>Data curation, Investigation, Resources, Writing &#x02013; original draft, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con4"><p>Data curation, Investigation, Methodology, Resources, Writing &#x02013; original draft, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con5"><p>Data curation, Formal analysis, Investigation, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con6"><p>Formal analysis, Methodology, Validation, Visualization, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con7"><p>Data curation, Formal analysis, Investigation, Resources, Validation, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con8"><p>Funding acquisition, Project administration, Supervision, Validation, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con9"><p>Funding acquisition, Supervision, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con10"><p>Conceptualization, Funding acquisition, Project administration, Supervision, Writing &#x02013; review and editing.</p></fn><fn fn-type="con" id="con11"><p>Conceptualization, Funding acquisition, Project administration, Supervision, Writing &#x02013; original draft, Writing &#x02013; review and editing, Co-senior authors.</p></fn><fn fn-type="con" id="con12"><p>Conceptualization, Funding acquisition, Project administration, Supervision, Visualization, Writing &#x02013; original draft, Writing &#x02013; review and editing, Co-senior authors.</p></fn></fn-group></sec><sec sec-type="supplementary-material" id="s6"><title>Additional files</title><supplementary-material id="supp1" position="float" content-type="local-data"><label>Supplementary file 1.</label><caption><title>A zipped folder with the generic prostate model in several formats: MaBoSS, GINsim, SBML, as well as images of the networks and their annotations.</title></caption><media xlink:href="elife-72626-supp1.zip" id="d64e3404" position="anchor"/></supplementary-material><supplementary-material id="supp2" position="float" content-type="local-data"><label>Supplementary file 2.</label><caption><title>A jupyter notebook to inspect Boolean models using MaBoSS.</title><p>This notebook can be used as source code with the model files from <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref> to generate <xref rid="fig3" ref-type="fig">Figure 3</xref>.</p></caption><media xlink:href="elife-72626-supp2.zip" id="d64e3417" position="anchor"/></supplementary-material><supplementary-material id="supp3" position="float" content-type="local-data"><label>Supplementary file 3.</label><caption><title>A zipped folder with the TCGA-specific personalised models and their <italic toggle="yes">Apoptosis</italic> and <italic toggle="yes">Proliferation</italic> phenotype scores.</title></caption><media xlink:href="elife-72626-supp3.zip" id="d64e3429" position="anchor"/></supplementary-material><supplementary-material id="supp4" position="float" content-type="local-data"><label>Supplementary file 4.</label><caption><title>A TSV file with all the phenotype scores, including <italic toggle="yes">Apoptosis</italic> and <italic toggle="yes">Proliferation,</italic> of the TCGA patient-specific mutations.</title><p>In the mutation list &#x0201c;_oe&#x0201d; stands for an overexpressed gene and &#x0201c;_ko&#x0201d; for a knocked out gene.</p></caption><media xlink:href="elife-72626-supp4.zip" id="d64e3442" position="anchor"/></supplementary-material><supplementary-material id="supp5" position="float" content-type="local-data"><label>Supplementary file 5.</label><caption><title>A zipped folder with the cell line-specific personalised models.</title></caption><media xlink:href="elife-72626-supp5.zip" id="d64e3447" position="anchor"/></supplementary-material><supplementary-material id="supp6" position="float" content-type="local-data"><label>Supplementary file 6.</label><caption><title>A TSV file with all the phenotype scores, including <italic toggle="yes">Apoptosis</italic> and <italic toggle="yes">Proliferation,</italic> of all 32,258 LNCaP cell line-specific mutations and the wild type LNCaP model.</title><p>In the mutation list &#x0201c;_oe&#x0201d; stands for an overexpressed gene and &#x0201c;_ko&#x0201d; for a knocked out gene.</p></caption><media xlink:href="elife-72626-supp6.txt" id="d64e3460" position="anchor"/></supplementary-material><supplementary-material id="transrepform" position="float" content-type="local-data"><label>Transparent reporting form</label><media xlink:href="elife-72626-transrepform1.docx" id="d64e3463" position="anchor"/></supplementary-material></sec><sec sec-type="data-availability" id="s7"><title>Data availability</title><p>Code (and processed data) to reproduce the analyses can be found in a dedicated GitHub (<ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2" ext-link-type="uri">https://github.com/ArnauMontagud/PROFILE_v2</ext-link> copy archived at <ext-link xlink:href="https://archive.softwareheritage.org/swh:1:dir:906d121eb7213c33d5eac35ea0f4b2f9519e49c5;origin=https://github.com/ArnauMontagud/PROFILE_v2;visit=swh:1:snp:e85457faff9deca815624f93f2b6e648f7776c16;anchor=swh:1:rev:cdea0bbfa0e7791c15c0dc452134f1196b4c1b09" ext-link-type="uri">swh:1:rev:cdea0bbfa0e7791c15c0dc452134f1196b4c1b09</ext-link>). Some of the code used in the work can be found in other GitHub repositories (<ext-link xlink:href="https://github.com/sysbio-curie/PROFILE" ext-link-type="uri">https://github.com/sysbio-curie/PROFILE</ext-link> copy archived at <ext-link xlink:href="https://archive.softwareheritage.org/swh:1:dir:336237c1f0cf8f39eecfadd20b6bcd4e5ccc36a8;origin=https://github.com/sysbio-curie/PROFILE;visit=swh:1:snp:b02f19ed076ecc9d2ef9d7c306ebac5f6eff52a0;anchor=swh:1:rev:2e0e74b21e7eac53dbedc46f350511b6558bf75b" ext-link-type="uri">swh:1:rev:2e0e74b21e7eac53dbedc46f350511b6558bf75b</ext-link>; <ext-link xlink:href="https://github.com/sysbio-curie/Logical_modelling_pipeline" ext-link-type="uri">https://github.com/sysbio-curie/Logical_modelling_pipeline</ext-link> copy archived at <ext-link xlink:href="https://archive.softwareheritage.org/swh:1:dir:af13c4fed5e31937b423e64a1045be30a6f7ee42;origin=https://github.com/sysbio-curie/Logical_modelling_pipeline;visit=swh:1:snp:41e2144ec65abac0d475911d6e54020b6f730e30;anchor=swh:1:rev:5524aae3eece3de1311a1724bd4c6452f0be0542" ext-link-type="uri">swh:1:rev:5524aae3eece3de1311a1724bd4c6452f0be0542</ext-link>). The model built can be accessed on the Supplementary File 1 and on BioModels and GINsim model repositories (<ext-link xlink:href="https://www.ebi.ac.uk/biomodels/MODEL2106070001" ext-link-type="uri">https://www.ebi.ac.uk/biomodels/MODEL2106070001</ext-link>; <ext-link xlink:href="http://ginsim.org/model/signalling-prostate-cancer" ext-link-type="uri">http://ginsim.org/model/signalling-prostate-cancer</ext-link>). The papers associated with Prostate Adenocarcinoma and Genomics of Drug Sensitivity in Cancer datasets can be found at <ext-link xlink:href="https://doi.org/10.1016/j.cell.2018.03.022" ext-link-type="uri">https://doi.org/10.1016/j.cell.2018.03.022</ext-link> and <ext-link xlink:href="https://doi.org/10.1016/j.cell.2016.06.017" ext-link-type="uri">https://doi.org/10.1016/j.cell.2016.06.017</ext-link> respectively.</p><p>The following previously published datasets were used:</p><p>
<element-citation publication-type="data" id="dataset1"><person-group person-group-type="author">
<name><surname>Hoadley</surname><given-names>KA</given-names></name>
<name><surname>Yau</surname><given-names>C</given-names></name>
<name><surname>Hinoue</surname><given-names>T</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><data-title>Prostate Adenocarcinoma (TCGA, PanCancer Atlas)</data-title><source>cBioPortal</source><pub-id pub-id-type="accession" xlink:href="https://www.cbioportal.org/study/summary?id=prad_tcga_pan_can_atlas_2018">prad_tcga_pan_can_atlas_2018</pub-id></element-citation>
</p><p>
<element-citation publication-type="data" id="dataset2"><person-group person-group-type="author">
<collab>Iorio F</collab>
</person-group><year iso-8601-date="2016">2016</year><data-title>GDSC 1 and 2</data-title><source>Genomics of Drug Sensitivity in Cancer</source><pub-id pub-id-type="accession" xlink:href="https://www.cancerrxgene.org/downloads/bulk_download">GDSC1/2</pub-id></element-citation>
</p></sec><ref-list><title>References</title><ref id="bib1"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Abou-Jaoud&#x000e9;</surname><given-names>W</given-names></name>
<name><surname>Chaves</surname><given-names>M</given-names></name>
<name><surname>Gouz&#x000e9;</surname><given-names>JL</given-names></name>
</person-group><year iso-8601-date="2011">2011</year><article-title>A theoretical exploration of birhythmicity in the p53-Mdm2 network</article-title><source>PLOS ONE</source><volume>6</volume><elocation-id>e17075</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0017075</pub-id><?supplied-pmid 21340030?><pub-id pub-id-type="pmid">21340030</pub-id></element-citation></ref><ref id="bib2"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Abou-Jaoud&#x000e9;</surname><given-names>W</given-names></name>
<name><surname>Traynard</surname><given-names>P</given-names></name>
<name><surname>Monteiro</surname><given-names>PT</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
<name><surname>Helikar</surname><given-names>T</given-names></name>
<name><surname>Thieffry</surname><given-names>D</given-names></name>
<name><surname>Chaouiya</surname><given-names>C</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Logical Modeling and Dynamical Analysis of Cellular Networks</article-title><source>Frontiers in Genetics</source><volume>7</volume><elocation-id>94</elocation-id><pub-id pub-id-type="doi">10.3389/fgene.2016.00094</pub-id><?supplied-pmid 27303434?><pub-id pub-id-type="pmid">27303434</pub-id></element-citation></ref><ref id="bib3"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Adamo</surname><given-names>P</given-names></name>
<name><surname>Ladomery</surname><given-names>MR</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>The oncogene ERG: a key factor in prostate cancer</article-title><source>Oncogene</source><volume>35</volume><fpage>403</fpage><lpage>414</lpage><pub-id pub-id-type="doi">10.1038/onc.2015.109</pub-id><?supplied-pmid 25915839?><pub-id pub-id-type="pmid">25915839</pub-id></element-citation></ref><ref id="bib4"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Al-Lazikani</surname><given-names>B</given-names></name>
<name><surname>Banerji</surname><given-names>U</given-names></name>
<name><surname>Workman</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>Combinatorial drug therapy for cancer in the post-genomic era</article-title><source>Nature Biotechnology</source><volume>30</volume><fpage>679</fpage><lpage>692</lpage><pub-id pub-id-type="doi">10.1038/nbt.2284</pub-id><?supplied-pmid 22781697?><pub-id pub-id-type="pmid">22781697</pub-id></element-citation></ref><ref id="bib5"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Altieri</surname><given-names>DC</given-names></name>
<name><surname>Languino</surname><given-names>LR</given-names></name>
<name><surname>Lian</surname><given-names>JB</given-names></name>
<name><surname>Stein</surname><given-names>JL</given-names></name>
<name><surname>Leav</surname><given-names>I</given-names></name>
<name><surname>van Wijnen</surname><given-names>AJ</given-names></name>
<name><surname>Jiang</surname><given-names>Z</given-names></name>
<name><surname>Stein</surname><given-names>GS</given-names></name>
</person-group><year iso-8601-date="2009">2009</year><article-title>Prostate cancer regulatory networks</article-title><source>Journal of Cellular Biochemistry</source><volume>107</volume><fpage>845</fpage><lpage>852</lpage><pub-id pub-id-type="doi">10.1002/jcb.22162</pub-id><?supplied-pmid 19492418?><pub-id pub-id-type="pmid">19492418</pub-id></element-citation></ref><ref id="bib6"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Babtie</surname><given-names>AC</given-names></name>
<name><surname>Stumpf</surname><given-names>MPH</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>How to deal with parameters for whole-cell modelling</article-title><source>Journal of the Royal Society, Interface</source><volume>14</volume><elocation-id>20170237</elocation-id><pub-id pub-id-type="doi">10.1098/rsif.2017.0237</pub-id><?supplied-pmid 28768879?><pub-id pub-id-type="pmid">28768879</pub-id></element-citation></ref><ref id="bib7"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Barrott</surname><given-names>JJ</given-names></name>
<name><surname>Haystead</surname><given-names>TAJ</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Hsp90, an unlikely ally in the war on cancer</article-title><source>The FEBS Journal</source><volume>280</volume><fpage>1381</fpage><lpage>1396</lpage><pub-id pub-id-type="doi">10.1111/febs.12147</pub-id><?supplied-pmid 23356585?><pub-id pub-id-type="pmid">23356585</pub-id></element-citation></ref><ref id="bib8"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bayat Mokhtari</surname><given-names>R</given-names></name>
<name><surname>Homayouni</surname><given-names>TS</given-names></name>
<name><surname>Baluch</surname><given-names>N</given-names></name>
<name><surname>Morgatskaya</surname><given-names>E</given-names></name>
<name><surname>Kumar</surname><given-names>S</given-names></name>
<name><surname>Das</surname><given-names>B</given-names></name>
<name><surname>Yeger</surname><given-names>H</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Combination therapy in combating cancer</article-title><source>Oncotarget</source><volume>8</volume><fpage>38022</fpage><lpage>38043</lpage><pub-id pub-id-type="doi">10.18632/oncotarget.16723</pub-id><?supplied-pmid 28410237?><pub-id pub-id-type="pmid">28410237</pub-id></element-citation></ref><ref id="bib9"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>B&#x000e9;al</surname><given-names>J</given-names></name>
<name><surname>Montagud</surname><given-names>A</given-names></name>
<name><surname>Traynard</surname><given-names>P</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Personalization of Logical Models With Multi-Omics Data Allows Clinical Stratification of Patients</article-title><source>Frontiers in Physiology</source><volume>9</volume><elocation-id>1965</elocation-id><pub-id pub-id-type="doi">10.3389/fphys.2018.01965</pub-id><?supplied-pmid 30733688?><pub-id pub-id-type="pmid">30733688</pub-id></element-citation></ref><ref id="bib10"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>B&#x000e9;al</surname><given-names>J</given-names></name>
<name><surname>Pantolini</surname><given-names>L</given-names></name>
<name><surname>No&#x000eb;l</surname><given-names>V</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2021">2021</year><article-title>Personalized logical models to investigate cancer response to BRAF treatments in melanomas and colorectal cancers</article-title><source>PLOS Computational Biology</source><volume>17</volume><elocation-id>e1007900</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1007900</pub-id><?supplied-pmid 33507915?><pub-id pub-id-type="pmid">33507915</pub-id></element-citation></ref><ref id="bib11"><element-citation publication-type="software"><person-group person-group-type="author">
<name><surname>B&#x000e9;al</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2022">2022</year><data-title>PROFILE</data-title><version designator="swh:1:rev:2e0e74b21e7eac53dbedc46f350511b6558bf75b">swh:1:rev:2e0e74b21e7eac53dbedc46f350511b6558bf75b</version><source>Software Heritage</source><ext-link xlink:href="https://archive.softwareheritage.org/swh:1:dir:336237c1f0cf8f39eecfadd20b6bcd4e5ccc36a8;origin=https://github.com/sysbio-curie/PROFILE;visit=swh:1:snp:b02f19ed076ecc9d2ef9d7c306ebac5f6eff52a0;anchor=swh:1:rev:2e0e74b21e7eac53dbedc46f350511b6558bf75b" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:dir:336237c1f0cf8f39eecfadd20b6bcd4e5ccc36a8;origin=https://github.com/sysbio-curie/PROFILE;visit=swh:1:snp:b02f19ed076ecc9d2ef9d7c306ebac5f6eff52a0;anchor=swh:1:rev:2e0e74b21e7eac53dbedc46f350511b6558bf75b</ext-link></element-citation></ref><ref id="bib12"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bowen</surname><given-names>C</given-names></name>
<name><surname>Zheng</surname><given-names>T</given-names></name>
<name><surname>Gelmann</surname><given-names>EP</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>NKX3.1 Suppresses TMPRSS2-ERG Gene Rearrangement and Mediates Repair of Androgen Receptor-Induced DNA Damage</article-title><source>Cancer Research</source><volume>75</volume><fpage>2686</fpage><lpage>2698</lpage><pub-id pub-id-type="doi">10.1158/0008-5472.CAN-14-3387</pub-id><?supplied-pmid 25977336?><pub-id pub-id-type="pmid">25977336</pub-id></element-citation></ref><ref id="bib13"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Calzone</surname><given-names>L</given-names></name>
<name><surname>Tournier</surname><given-names>L</given-names></name>
<name><surname>Fourquet</surname><given-names>S</given-names></name>
<name><surname>Thieffry</surname><given-names>D</given-names></name>
<name><surname>Zhivotovsky</surname><given-names>B</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2010">2010</year><article-title>Mathematical modelling of cell-fate decision in response to death receptor engagement</article-title><source>PLOS Computational Biology</source><volume>6</volume><elocation-id>e1000702</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1000702</pub-id><?supplied-pmid 20221256?><pub-id pub-id-type="pmid">20221256</pub-id></element-citation></ref><ref id="bib14"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Calzone</surname><given-names>L</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Predicting genetic interactions from Boolean models of biological networks</article-title><source>Integrative Biology</source><volume>7</volume><fpage>921</fpage><lpage>929</lpage><pub-id pub-id-type="doi">10.1039/c5ib00029g</pub-id><?supplied-pmid 25958956?><pub-id pub-id-type="pmid">25958956</pub-id></element-citation></ref><ref id="bib15"><element-citation publication-type="journal"><person-group person-group-type="author">
<collab>Cancer Genome Atlas Research Network</collab>
</person-group><year iso-8601-date="2015">2015</year><article-title>The Molecular Taxonomy of Primary Prostate Cancer</article-title><source>Cell</source><volume>163</volume><fpage>1011</fpage><lpage>1025</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2015.10.025</pub-id><?supplied-pmid 26544944?><pub-id pub-id-type="pmid">26544944</pub-id></element-citation></ref><ref id="bib16"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Carceles-Cordon</surname><given-names>M</given-names></name>
<name><surname>Kelly</surname><given-names>WK</given-names></name>
<name><surname>Gomella</surname><given-names>L</given-names></name>
<name><surname>Knudsen</surname><given-names>KE</given-names></name>
<name><surname>Rodriguez-Bravo</surname><given-names>V</given-names></name>
<name><surname>Domingo-Domenech</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>Cellular rewiring in lethal prostate cancer: the architect of drug resistance</article-title><source>Nature Reviews. Urology</source><volume>17</volume><fpage>292</fpage><lpage>307</lpage><pub-id pub-id-type="doi">10.1038/s41585-020-0298-8</pub-id><?supplied-pmid 32203305?><pub-id pub-id-type="pmid">32203305</pub-id></element-citation></ref><ref id="bib17"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Castoria</surname><given-names>G</given-names></name>
<name><surname>D&#x02019;Amato</surname><given-names>L</given-names></name>
<name><surname>Ciociola</surname><given-names>A</given-names></name>
<name><surname>Giovannelli</surname><given-names>P</given-names></name>
<name><surname>Giraldi</surname><given-names>T</given-names></name>
<name><surname>Sepe</surname><given-names>L</given-names></name>
<name><surname>Paolella</surname><given-names>G</given-names></name>
<name><surname>Barone</surname><given-names>MV</given-names></name>
<name><surname>Migliaccio</surname><given-names>A</given-names></name>
<name><surname>Auricchio</surname><given-names>F</given-names></name>
</person-group><year iso-8601-date="2011">2011</year><article-title>Androgen-induced cell migration: role of androgen receptor/filamin A association</article-title><source>PLOS ONE</source><volume>6</volume><elocation-id>e17218</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0017218</pub-id><?supplied-pmid 21359179?><pub-id pub-id-type="pmid">21359179</pub-id></element-citation></ref><ref id="bib18"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Chaouiya</surname><given-names>C</given-names></name>
<name><surname>Naldi</surname><given-names>A</given-names></name>
<name><surname>Thieffry</surname><given-names>D</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>Logical modelling of gene regulatory networks with GINsim</article-title><source>Methods in Molecular Biology (Clifton, N.J.)</source><volume>804</volume><fpage>463</fpage><lpage>479</lpage><pub-id pub-id-type="doi">10.1007/978-1-61779-361-5_23</pub-id><?supplied-pmid 22144167?><pub-id pub-id-type="pmid">22144167</pub-id></element-citation></ref><ref id="bib19"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Chen</surname><given-names>N</given-names></name>
<name><surname>Zhou</surname><given-names>Q</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>The evolving Gleason grading system</article-title><source>Chinese Journal of Cancer Research = Chung-Kuo Yen Cheng Yen Chiu</source><volume>28</volume><fpage>58</fpage><lpage>64</lpage><pub-id pub-id-type="doi">10.3978/j.issn.1000-9604.2016.02.04</pub-id><?supplied-pmid 27041927?><pub-id pub-id-type="pmid">27041927</pub-id></element-citation></ref><ref id="bib20"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Chen</surname><given-names>W</given-names></name>
<name><surname>Li</surname><given-names>G</given-names></name>
<name><surname>Peng</surname><given-names>J</given-names></name>
<name><surname>Dai</surname><given-names>W</given-names></name>
<name><surname>Su</surname><given-names>Q</given-names></name>
<name><surname>He</surname><given-names>Y</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>Transcriptomic analysis reveals that heat shock protein 90&#x003b1; is a potential diagnostic and prognostic biomarker for cancer</article-title><source>European Journal of Cancer Prevention</source><volume>29</volume><fpage>357</fpage><lpage>364</lpage><pub-id pub-id-type="doi">10.1097/CEJ.0000000000000549</pub-id><?supplied-pmid 31567483?><pub-id pub-id-type="pmid">31567483</pub-id></element-citation></ref><ref id="bib21"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cho</surname><given-names>SH</given-names></name>
<name><surname>Park</surname><given-names>SM</given-names></name>
<name><surname>Lee</surname><given-names>HS</given-names></name>
<name><surname>Lee</surname><given-names>HY</given-names></name>
<name><surname>Cho</surname><given-names>KH</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Attractor landscape analysis of colorectal tumorigenesis and its reversion</article-title><source>BMC Systems Biology</source><volume>10</volume><elocation-id>96</elocation-id><pub-id pub-id-type="doi">10.1186/s12918-016-0341-9</pub-id><?supplied-pmid 27765040?><pub-id pub-id-type="pmid">27765040</pub-id></element-citation></ref><ref id="bib22"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ciccarese</surname><given-names>C</given-names></name>
<name><surname>Massari</surname><given-names>F</given-names></name>
<name><surname>Iacovelli</surname><given-names>R</given-names></name>
<name><surname>Fiorentino</surname><given-names>M</given-names></name>
<name><surname>Montironi</surname><given-names>R</given-names></name>
<name><surname>Di Nunno</surname><given-names>V</given-names></name>
<name><surname>Giunchi</surname><given-names>F</given-names></name>
<name><surname>Brunelli</surname><given-names>M</given-names></name>
<name><surname>Tortora</surname><given-names>G</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Prostate cancer heterogeneity: Discovering novel molecular targets for therapy</article-title><source>Cancer Treatment Reviews</source><volume>54</volume><fpage>68</fpage><lpage>73</lpage><pub-id pub-id-type="doi">10.1016/j.ctrv.2017.02.001</pub-id><?supplied-pmid 28231559?><pub-id pub-id-type="pmid">28231559</pub-id></element-citation></ref><ref id="bib23"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cohen</surname><given-names>DPA</given-names></name>
<name><surname>Martignetti</surname><given-names>L</given-names></name>
<name><surname>Robine</surname><given-names>S</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Mathematical Modelling of Molecular Pathways Enabling Tumour Cell Invasion and Migration</article-title><source>PLOS Computational Biology</source><volume>11</volume><elocation-id>e1004571</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004571</pub-id><?supplied-pmid 26528548?><pub-id pub-id-type="pmid">26528548</pub-id></element-citation></ref><ref id="bib24"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Coleman</surname><given-names>DJ</given-names></name>
<name><surname>Gao</surname><given-names>L</given-names></name>
<name><surname>King</surname><given-names>CJ</given-names></name>
<name><surname>Schwartzman</surname><given-names>J</given-names></name>
<name><surname>Urrutia</surname><given-names>J</given-names></name>
<name><surname>Sehrawat</surname><given-names>A</given-names></name>
<name><surname>Tayou</surname><given-names>J</given-names></name>
<name><surname>Balter</surname><given-names>A</given-names></name>
<name><surname>Burchard</surname><given-names>J</given-names></name>
<name><surname>Chiotti</surname><given-names>KE</given-names></name>
<name><surname>Derrick</surname><given-names>DS</given-names></name>
<name><surname>Sun</surname><given-names>D</given-names></name>
<name><surname>Xia</surname><given-names>Z</given-names></name>
<name><surname>Heiser</surname><given-names>LM</given-names></name>
<name><surname>Alumkal</surname><given-names>JJ</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>BET bromodomain inhibition blocks the function of a critical AR-independent master regulator network in lethal prostate cancer</article-title><source>Oncogene</source><volume>38</volume><fpage>5658</fpage><lpage>5669</lpage><pub-id pub-id-type="doi">10.1038/s41388-019-0815-5</pub-id><?supplied-pmid 30996246?><pub-id pub-id-type="pmid">30996246</pub-id></element-citation></ref><ref id="bib25"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cunanan</surname><given-names>KM</given-names></name>
<name><surname>Iasonos</surname><given-names>A</given-names></name>
<name><surname>Shen</surname><given-names>R</given-names></name>
<name><surname>Begg</surname><given-names>CB</given-names></name>
<name><surname>G&#x000f6;nen</surname><given-names>M</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>An efficient basket trial design</article-title><source>Statistics in Medicine</source><volume>36</volume><fpage>1568</fpage><lpage>1579</lpage><pub-id pub-id-type="doi">10.1002/sim.7227</pub-id><?supplied-pmid 28098411?><pub-id pub-id-type="pmid">28098411</pub-id></element-citation></ref><ref id="bib26"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cunningham</surname><given-names>D</given-names></name>
<name><surname>You</surname><given-names>Z</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>In vitro and in vivo model systems used in prostate cancer research</article-title><source>Journal of Biological Methods</source><volume>2</volume><elocation-id>e17</elocation-id><pub-id pub-id-type="doi">10.14440/jbm.2015.63</pub-id><?supplied-pmid 26146646?><pub-id pub-id-type="pmid">26146646</pub-id></element-citation></ref><ref id="bib27"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Daroqui</surname><given-names>MC</given-names></name>
<name><surname>Vazquez</surname><given-names>P</given-names></name>
<name><surname>Bal de Kier Joff&#x000e9;</surname><given-names>E</given-names></name>
<name><surname>Bakin</surname><given-names>AV</given-names></name>
<name><surname>Puricelli</surname><given-names>LI</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>TGF-&#x003b2; autocrine pathway and MAPK signaling promote cell invasiveness and in vivo mammary adenocarcinoma tumor progression</article-title><source>Oncology Reports</source><volume>28</volume><fpage>567</fpage><lpage>575</lpage><pub-id pub-id-type="doi">10.3892/or.2012.1813</pub-id><?supplied-pmid 22614218?><pub-id pub-id-type="pmid">22614218</pub-id></element-citation></ref><ref id="bib28"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Datta</surname><given-names>D</given-names></name>
<name><surname>Aftabuddin</surname><given-names>M</given-names></name>
<name><surname>Gupta</surname><given-names>DK</given-names></name>
<name><surname>Raha</surname><given-names>S</given-names></name>
<name><surname>Sen</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Human Prostate Cancer Hallmarks Map</article-title><source>Scientific Reports</source><volume>6</volume><elocation-id>30691</elocation-id><pub-id pub-id-type="doi">10.1038/srep30691</pub-id><?supplied-pmid 27476486?><pub-id pub-id-type="pmid">27476486</pub-id></element-citation></ref><ref id="bib29"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>De Wever</surname><given-names>O</given-names></name>
<name><surname>Westbroek</surname><given-names>W</given-names></name>
<name><surname>Verloes</surname><given-names>A</given-names></name>
<name><surname>Bloemen</surname><given-names>N</given-names></name>
<name><surname>Bracke</surname><given-names>M</given-names></name>
<name><surname>Gespach</surname><given-names>C</given-names></name>
<name><surname>Bruyneel</surname><given-names>E</given-names></name>
<name><surname>Mareel</surname><given-names>M</given-names></name>
</person-group><year iso-8601-date="2004">2004</year><article-title>Critical role of N-cadherin in myofibroblast invasion and migration in vitro stimulated by colon-cancer-cell-derived TGF-beta or wounding</article-title><source>Journal of Cell Science</source><volume>117</volume><fpage>4691</fpage><lpage>4703</lpage><pub-id pub-id-type="doi">10.1242/jcs.01322</pub-id><?supplied-pmid 15331629?><pub-id pub-id-type="pmid">15331629</pub-id></element-citation></ref><ref id="bib30"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Dorier</surname><given-names>J</given-names></name>
<name><surname>Crespo</surname><given-names>I</given-names></name>
<name><surname>Niknejad</surname><given-names>A</given-names></name>
<name><surname>Liechti</surname><given-names>R</given-names></name>
<name><surname>Ebeling</surname><given-names>M</given-names></name>
<name><surname>Xenarios</surname><given-names>I</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Boolean regulatory network reconstruction using literature based knowledge with a genetic algorithm optimization method</article-title><source>BMC Bioinformatics</source><volume>17</volume><elocation-id>410</elocation-id><pub-id pub-id-type="doi">10.1186/s12859-016-1287-z</pub-id><?supplied-pmid 27716031?><pub-id pub-id-type="pmid">27716031</pub-id></element-citation></ref><ref id="bib31"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Drees</surname><given-names>BL</given-names></name>
<name><surname>Thorsson</surname><given-names>V</given-names></name>
<name><surname>Carter</surname><given-names>GW</given-names></name>
<name><surname>Rives</surname><given-names>AW</given-names></name>
<name><surname>Raymond</surname><given-names>MZ</given-names></name>
<name><surname>Avila-Campillo</surname><given-names>I</given-names></name>
<name><surname>Shannon</surname><given-names>P</given-names></name>
<name><surname>Galitski</surname><given-names>T</given-names></name>
</person-group><year iso-8601-date="2005">2005</year><article-title>Derivation of genetic interaction networks from quantitative phenotype data</article-title><source>Genome Biology</source><volume>6</volume><elocation-id>R38</elocation-id><pub-id pub-id-type="doi">10.1186/gb-2005-6-4-r38</pub-id><?supplied-pmid 15833125?><pub-id pub-id-type="pmid">15833125</pub-id></element-citation></ref><ref id="bib32"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Eduati</surname><given-names>F</given-names></name>
<name><surname>Jaaks</surname><given-names>P</given-names></name>
<name><surname>Wappler</surname><given-names>J</given-names></name>
<name><surname>Cramer</surname><given-names>T</given-names></name>
<name><surname>Merten</surname><given-names>CA</given-names></name>
<name><surname>Garnett</surname><given-names>MJ</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>Patient-specific logic models of signaling pathways from screenings on cancer biopsies to prioritize personalized combination therapies</article-title><source>Molecular Systems Biology</source><volume>16</volume><elocation-id>e8664</elocation-id><pub-id pub-id-type="doi">10.15252/msb.20188664</pub-id><?supplied-pmid 32073727?><pub-id pub-id-type="pmid">32073727</pub-id></element-citation></ref><ref id="bib33"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fabregat</surname><given-names>A</given-names></name>
<name><surname>Sidiropoulos</surname><given-names>K</given-names></name>
<name><surname>Garapati</surname><given-names>P</given-names></name>
<name><surname>Gillespie</surname><given-names>M</given-names></name>
<name><surname>Hausmann</surname><given-names>K</given-names></name>
<name><surname>Haw</surname><given-names>R</given-names></name>
<name><surname>Jassal</surname><given-names>B</given-names></name>
<name><surname>Jupe</surname><given-names>S</given-names></name>
<name><surname>Korninger</surname><given-names>F</given-names></name>
<name><surname>McKay</surname><given-names>S</given-names></name>
<name><surname>Matthews</surname><given-names>L</given-names></name>
<name><surname>May</surname><given-names>B</given-names></name>
<name><surname>Milacic</surname><given-names>M</given-names></name>
<name><surname>Rothfels</surname><given-names>K</given-names></name>
<name><surname>Shamovsky</surname><given-names>V</given-names></name>
<name><surname>Webber</surname><given-names>M</given-names></name>
<name><surname>Weiser</surname><given-names>J</given-names></name>
<name><surname>Williams</surname><given-names>M</given-names></name>
<name><surname>Wu</surname><given-names>G</given-names></name>
<name><surname>Stein</surname><given-names>L</given-names></name>
<name><surname>Hermjakob</surname><given-names>H</given-names></name>
<name><surname>D&#x02019;Eustachio</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>The Reactome pathway Knowledgebase</article-title><source>Nucleic Acids Research</source><volume>44</volume><fpage>D481</fpage><lpage>D487</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1351</pub-id><?supplied-pmid 26656494?><pub-id pub-id-type="pmid">26656494</pub-id></element-citation></ref><ref id="bib34"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Faur&#x000e9;</surname><given-names>A</given-names></name>
<name><surname>Naldi</surname><given-names>A</given-names></name>
<name><surname>Chaouiya</surname><given-names>C</given-names></name>
<name><surname>Thieffry</surname><given-names>D</given-names></name>
</person-group><year iso-8601-date="2006">2006</year><article-title>Dynamical analysis of a generic Boolean model for the control of the mammalian cell cycle</article-title><source>Bioinformatics (Oxford, England)</source><volume>22</volume><fpage>e124</fpage><lpage>e131</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btl210</pub-id><?supplied-pmid 16873462?><pub-id pub-id-type="pmid">16873462</pub-id></element-citation></ref><ref id="bib35"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Flobak</surname><given-names>&#x000c5;</given-names></name>
<name><surname>Baudot</surname><given-names>A</given-names></name>
<name><surname>Remy</surname><given-names>E</given-names></name>
<name><surname>Thommesen</surname><given-names>L</given-names></name>
<name><surname>Thieffry</surname><given-names>D</given-names></name>
<name><surname>Kuiper</surname><given-names>M</given-names></name>
<name><surname>L&#x000e6;greid</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Discovery of Drug Synergies in Gastric Cancer Cells Predicted by Logical Modeling</article-title><source>PLOS Computational Biology</source><volume>11</volume><elocation-id>e1004426</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004426</pub-id><?supplied-pmid 26317215?><pub-id pub-id-type="pmid">26317215</pub-id></element-citation></ref><ref id="bib36"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fogliatto</surname><given-names>G</given-names></name>
<name><surname>Gianellini</surname><given-names>L</given-names></name>
<name><surname>Brasca</surname><given-names>MG</given-names></name>
<name><surname>Casale</surname><given-names>E</given-names></name>
<name><surname>Ballinari</surname><given-names>D</given-names></name>
<name><surname>Ciomei</surname><given-names>M</given-names></name>
<name><surname>Degrassi</surname><given-names>A</given-names></name>
<name><surname>De Ponti</surname><given-names>A</given-names></name>
<name><surname>Germani</surname><given-names>M</given-names></name>
<name><surname>Guanci</surname><given-names>M</given-names></name>
<name><surname>Paolucci</surname><given-names>M</given-names></name>
<name><surname>Polucci</surname><given-names>P</given-names></name>
<name><surname>Russo</surname><given-names>M</given-names></name>
<name><surname>Sola</surname><given-names>F</given-names></name>
<name><surname>Valsasina</surname><given-names>B</given-names></name>
<name><surname>Visco</surname><given-names>C</given-names></name>
<name><surname>Zuccotto</surname><given-names>F</given-names></name>
<name><surname>Donati</surname><given-names>D</given-names></name>
<name><surname>Felder</surname><given-names>E</given-names></name>
<name><surname>Pesenti</surname><given-names>E</given-names></name>
<name><surname>Galvani</surname><given-names>A</given-names></name>
<name><surname>Mantegani</surname><given-names>S</given-names></name>
<name><surname>Isacchi</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>NMS-E973, a novel synthetic inhibitor of Hsp90 with activity against multiple models of drug resistance to targeted agents, including intracranial metastases</article-title><source>Clinical Cancer Research</source><volume>19</volume><fpage>3520</fpage><lpage>3532</lpage><pub-id pub-id-type="doi">10.1158/1078-0432.CCR-12-3512</pub-id><?supplied-pmid 23674492?><pub-id pub-id-type="pmid">23674492</pub-id></element-citation></ref><ref id="bib37"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Foucquier</surname><given-names>J</given-names></name>
<name><surname>Guedj</surname><given-names>M</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Analysis of drug combinations: current methodological landscape</article-title><source>Pharmacology Research &#x00026; Perspectives</source><volume>3</volume><elocation-id>e00149</elocation-id><pub-id pub-id-type="doi">10.1002/prp2.149</pub-id><?supplied-pmid 26171228?><pub-id pub-id-type="pmid">26171228</pub-id></element-citation></ref><ref id="bib38"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Francis</surname><given-names>JC</given-names></name>
<name><surname>Thomsen</surname><given-names>MK</given-names></name>
<name><surname>Taketo</surname><given-names>MM</given-names></name>
<name><surname>Swain</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>&#x003b2;-catenin is required for prostate development and cooperates with Pten loss to drive invasive carcinoma</article-title><source>PLOS Genetics</source><volume>9</volume><elocation-id>e1003180</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pgen.1003180</pub-id><?supplied-pmid 23300485?><pub-id pub-id-type="pmid">23300485</pub-id></element-citation></ref><ref id="bib39"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fumi&#x000e3;</surname><given-names>HF</given-names></name>
<name><surname>Martins</surname><given-names>ML</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Boolean network model for cancer pathways: predicting carcinogenesis and targeted therapy outcomes</article-title><source>PLOS ONE</source><volume>8</volume><elocation-id>e69008</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0069008</pub-id><?supplied-pmid 23922675?><pub-id pub-id-type="pmid">23922675</pub-id></element-citation></ref><ref id="bib40"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Gaulton</surname><given-names>A</given-names></name>
<name><surname>Hersey</surname><given-names>A</given-names></name>
<name><surname>Nowotka</surname><given-names>M</given-names></name>
<name><surname>Bento</surname><given-names>AP</given-names></name>
<name><surname>Chambers</surname><given-names>J</given-names></name>
<name><surname>Mendez</surname><given-names>D</given-names></name>
<name><surname>Mutowo</surname><given-names>P</given-names></name>
<name><surname>Atkinson</surname><given-names>F</given-names></name>
<name><surname>Bellis</surname><given-names>LJ</given-names></name>
<name><surname>Cibri&#x000e1;n-Uhalte</surname><given-names>E</given-names></name>
<name><surname>Davies</surname><given-names>M</given-names></name>
<name><surname>Dedman</surname><given-names>N</given-names></name>
<name><surname>Karlsson</surname><given-names>A</given-names></name>
<name><surname>Magari&#x000f1;os</surname><given-names>MP</given-names></name>
<name><surname>Overington</surname><given-names>JP</given-names></name>
<name><surname>Papadatos</surname><given-names>G</given-names></name>
<name><surname>Smit</surname><given-names>I</given-names></name>
<name><surname>Leach</surname><given-names>AR</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>The ChEMBL database in 2017</article-title><source>Nucleic Acids Research</source><volume>45</volume><fpage>D945</fpage><lpage>D954</lpage><pub-id pub-id-type="doi">10.1093/nar/gkw1074</pub-id><?supplied-pmid 27899562?><pub-id pub-id-type="pmid">27899562</pub-id></element-citation></ref><ref id="bib41"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Gillespie</surname><given-names>DT</given-names></name>
</person-group><year iso-8601-date="1976">1976</year><article-title>A general method for numerically simulating the stochastic time evolution of coupled chemical reactions</article-title><source>Journal of Computational Physics</source><volume>22</volume><fpage>403</fpage><lpage>434</lpage><pub-id pub-id-type="doi">10.1016/0021-9991(76)90041-3</pub-id></element-citation></ref><ref id="bib42"><element-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Gleason</surname><given-names>DF</given-names></name>
</person-group><year iso-8601-date="1977">1977</year><part-title>The Veteran&#x02019;s Administration Cooperative Urologic Research Group: Histologic Grading and Clinical Staging of Prostatic Carcinoma</part-title><person-group person-group-type="editor">
<name><surname>Tannenbaum</surname><given-names>M</given-names></name>
</person-group><source>Urologic Pathology: The Prostate</source><publisher-loc>Philadelphia</publisher-loc><publisher-name>Lea and Febiger</publisher-name><fpage>171</fpage><lpage>198</lpage></element-citation></ref><ref id="bib43"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Gleason</surname><given-names>DF</given-names></name>
</person-group><year iso-8601-date="1992">1992</year><article-title>Histologic grading of prostate cancer: a perspective</article-title><source>Human Pathology</source><volume>23</volume><fpage>273</fpage><lpage>279</lpage><pub-id pub-id-type="doi">10.1016/0046-8177(92)90108-f</pub-id><?supplied-pmid 1555838?><pub-id pub-id-type="pmid">1555838</pub-id></element-citation></ref><ref id="bib44"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Goldbeter</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2002">2002</year><article-title>Computational approaches to cellular rhythms</article-title><source>Nature</source><volume>420</volume><fpage>238</fpage><lpage>245</lpage><pub-id pub-id-type="doi">10.1038/nature01259</pub-id><?supplied-pmid 12432409?><pub-id pub-id-type="pmid">12432409</pub-id></element-citation></ref><ref id="bib45"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>G&#x000f3;mez Tejeda Za&#x000f1;udo</surname><given-names>J</given-names></name>
<name><surname>Scaltriti</surname><given-names>M</given-names></name>
<name><surname>Albert</surname><given-names>R</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>A network modeling approach to elucidate drug resistance mechanisms and predict combinatorial drug treatments in breast cancer</article-title><source>Cancer Convergence</source><volume>1</volume><elocation-id>5</elocation-id><pub-id pub-id-type="doi">10.1186/s41236-017-0007-6</pub-id><?supplied-pmid 29623959?><pub-id pub-id-type="pmid">29623959</pub-id></element-citation></ref><ref id="bib46"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Grieco</surname><given-names>L</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
<name><surname>Bernard-Pierrot</surname><given-names>I</given-names></name>
<name><surname>Radvanyi</surname><given-names>F</given-names></name>
<name><surname>Kahn-Perl&#x000e8;s</surname><given-names>B</given-names></name>
<name><surname>Thieffry</surname><given-names>D</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Integrative modelling of the influence of MAPK network on cancer cell fate decision</article-title><source>PLOS Computational Biology</source><volume>9</volume><elocation-id>e1003286</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1003286</pub-id><?supplied-pmid 24250280?><pub-id pub-id-type="pmid">24250280</pub-id></element-citation></ref><ref id="bib47"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Hayward</surname><given-names>SW</given-names></name>
<name><surname>Dahiya</surname><given-names>R</given-names></name>
<name><surname>Cunha</surname><given-names>GR</given-names></name>
<name><surname>Bartek</surname><given-names>J</given-names></name>
<name><surname>Deshpande</surname><given-names>N</given-names></name>
<name><surname>Narayan</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="1995">1995</year><article-title>Establishment and characterization of an immortalized but non-transformed human prostate epithelial cell line: BPH-1</article-title><source>In Vitro Cellular &#x00026; Developmental Biology. Animal</source><volume>31</volume><fpage>14</fpage><lpage>24</lpage><pub-id pub-id-type="doi">10.1007/BF02631333</pub-id><?supplied-pmid 7535634?><pub-id pub-id-type="pmid">7535634</pub-id></element-citation></ref><ref id="bib48"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Helikar</surname><given-names>T</given-names></name>
<name><surname>Konvalina</surname><given-names>J</given-names></name>
<name><surname>Heidel</surname><given-names>J</given-names></name>
<name><surname>Rogers</surname><given-names>JA</given-names></name>
</person-group><year iso-8601-date="2008">2008</year><article-title>Emergent decision-making in biological signal transduction networks</article-title><source>PNAS</source><volume>105</volume><fpage>1913</fpage><lpage>1918</lpage><pub-id pub-id-type="doi">10.1073/pnas.0705088105</pub-id><?supplied-pmid 18250321?><pub-id pub-id-type="pmid">18250321</pub-id></element-citation></ref><ref id="bib49"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Hessenkemper</surname><given-names>W</given-names></name>
<name><surname>Baniahmad</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Targeting heat shock proteins in prostate cancer</article-title><source>Current Medicinal Chemistry</source><volume>20</volume><fpage>2731</fpage><lpage>2740</lpage><pub-id pub-id-type="doi">10.2174/0929867311320220001</pub-id><?supplied-pmid 23521679?><pub-id pub-id-type="pmid">23521679</pub-id></element-citation></ref><ref id="bib50"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Hoadley</surname><given-names>KA</given-names></name>
<name><surname>Yau</surname><given-names>C</given-names></name>
<name><surname>Hinoue</surname><given-names>T</given-names></name>
<name><surname>Wolf</surname><given-names>DM</given-names></name>
<name><surname>Lazar</surname><given-names>AJ</given-names></name>
<name><surname>Drill</surname><given-names>E</given-names></name>
<name><surname>Shen</surname><given-names>R</given-names></name>
<name><surname>Taylor</surname><given-names>AM</given-names></name>
<name><surname>Cherniack</surname><given-names>AD</given-names></name>
<name><surname>Thorsson</surname><given-names>V</given-names></name>
<name><surname>Akbani</surname><given-names>R</given-names></name>
<name><surname>Bowlby</surname><given-names>R</given-names></name>
<name><surname>Wong</surname><given-names>CK</given-names></name>
<name><surname>Wiznerowicz</surname><given-names>M</given-names></name>
<name><surname>Sanchez-Vega</surname><given-names>F</given-names></name>
<name><surname>Robertson</surname><given-names>AG</given-names></name>
<name><surname>Schneider</surname><given-names>BG</given-names></name>
<name><surname>Lawrence</surname><given-names>MS</given-names></name>
<name><surname>Noushmehr</surname><given-names>H</given-names></name>
<name><surname>Malta</surname><given-names>TM</given-names></name>
<collab>Cancer Genome Atlas Network</collab>
<name><surname>Stuart</surname><given-names>JM</given-names></name>
<name><surname>Benz</surname><given-names>CC</given-names></name>
<name><surname>Laird</surname><given-names>PW</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>Cell-of-Origin Patterns Dominate the Molecular Classification of 10,000 Tumors from 33 Types of Cancer</article-title><source>Cell</source><volume>173</volume><fpage>291</fpage><lpage>304</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2018.03.022</pub-id><?supplied-pmid 29625048?><pub-id pub-id-type="pmid">29625048</pub-id></element-citation></ref><ref id="bib51"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Horoszewicz</surname><given-names>JS</given-names></name>
<name><surname>Leong</surname><given-names>SS</given-names></name>
<name><surname>Kawinski</surname><given-names>E</given-names></name>
<name><surname>Karr</surname><given-names>JP</given-names></name>
<name><surname>Rosenthal</surname><given-names>H</given-names></name>
<name><surname>Chu</surname><given-names>TM</given-names></name>
<name><surname>Mirand</surname><given-names>EA</given-names></name>
<name><surname>Murphy</surname><given-names>GP</given-names></name>
</person-group><year iso-8601-date="1983">1983</year><article-title>LNCaP model of human prostatic carcinoma</article-title><source>Cancer Research</source><volume>43</volume><fpage>1809</fpage><lpage>1818</lpage><?supplied-pmid 6831420?><pub-id pub-id-type="pmid">6831420</pub-id></element-citation></ref><ref id="bib52"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Hu</surname><given-names>Y</given-names></name>
<name><surname>Gu</surname><given-names>Y</given-names></name>
<name><surname>Wang</surname><given-names>H</given-names></name>
<name><surname>Huang</surname><given-names>Y</given-names></name>
<name><surname>Zou</surname><given-names>YM</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Integrated network model provides new insights into castration-resistant prostate cancer</article-title><source>Scientific Reports</source><volume>5</volume><elocation-id>17280</elocation-id><pub-id pub-id-type="doi">10.1038/srep17280</pub-id><?supplied-pmid 26603105?><pub-id pub-id-type="pmid">26603105</pub-id></element-citation></ref><ref id="bib53"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Iorio</surname><given-names>F</given-names></name>
<name><surname>Knijnenburg</surname><given-names>TA</given-names></name>
<name><surname>Vis</surname><given-names>DJ</given-names></name>
<name><surname>Bignell</surname><given-names>GR</given-names></name>
<name><surname>Menden</surname><given-names>MP</given-names></name>
<name><surname>Schubert</surname><given-names>M</given-names></name>
<name><surname>Aben</surname><given-names>N</given-names></name>
<name><surname>Gon&#x000e7;alves</surname><given-names>E</given-names></name>
<name><surname>Barthorpe</surname><given-names>S</given-names></name>
<name><surname>Lightfoot</surname><given-names>H</given-names></name>
<name><surname>Cokelaer</surname><given-names>T</given-names></name>
<name><surname>Greninger</surname><given-names>P</given-names></name>
<name><surname>van Dyk</surname><given-names>E</given-names></name>
<name><surname>Chang</surname><given-names>H</given-names></name>
<name><surname>de Silva</surname><given-names>H</given-names></name>
<name><surname>Heyn</surname><given-names>H</given-names></name>
<name><surname>Deng</surname><given-names>X</given-names></name>
<name><surname>Egan</surname><given-names>RK</given-names></name>
<name><surname>Liu</surname><given-names>Q</given-names></name>
<name><surname>Mironenko</surname><given-names>T</given-names></name>
<name><surname>Mitropoulos</surname><given-names>X</given-names></name>
<name><surname>Richardson</surname><given-names>L</given-names></name>
<name><surname>Wang</surname><given-names>J</given-names></name>
<name><surname>Zhang</surname><given-names>T</given-names></name>
<name><surname>Moran</surname><given-names>S</given-names></name>
<name><surname>Sayols</surname><given-names>S</given-names></name>
<name><surname>Soleimani</surname><given-names>M</given-names></name>
<name><surname>Tamborero</surname><given-names>D</given-names></name>
<name><surname>Lopez-Bigas</surname><given-names>N</given-names></name>
<name><surname>Ross-Macdonald</surname><given-names>P</given-names></name>
<name><surname>Esteller</surname><given-names>M</given-names></name>
<name><surname>Gray</surname><given-names>NS</given-names></name>
<name><surname>Haber</surname><given-names>DA</given-names></name>
<name><surname>Stratton</surname><given-names>MR</given-names></name>
<name><surname>Benes</surname><given-names>CH</given-names></name>
<name><surname>Wessels</surname><given-names>LFA</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
<name><surname>McDermott</surname><given-names>U</given-names></name>
<name><surname>Garnett</surname><given-names>MJ</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>A Landscape of Pharmacogenomic Interactions in Cancer</article-title><source>Cell</source><volume>166</volume><fpage>740</fpage><lpage>754</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2016.06.017</pub-id><?supplied-pmid 27397505?><pub-id pub-id-type="pmid">27397505</pub-id></element-citation></ref><ref id="bib54"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Iwai</surname><given-names>A</given-names></name>
<name><surname>Bourboulia</surname><given-names>D</given-names></name>
<name><surname>Mollapour</surname><given-names>M</given-names></name>
<name><surname>Jensen-Taubman</surname><given-names>S</given-names></name>
<name><surname>Lee</surname><given-names>S</given-names></name>
<name><surname>Donnelly</surname><given-names>AC</given-names></name>
<name><surname>Yoshida</surname><given-names>S</given-names></name>
<name><surname>Miyajima</surname><given-names>N</given-names></name>
<name><surname>Tsutsumi</surname><given-names>S</given-names></name>
<name><surname>Smith</surname><given-names>AK</given-names></name>
<name><surname>Sun</surname><given-names>D</given-names></name>
<name><surname>Wu</surname><given-names>X</given-names></name>
<name><surname>Blagg</surname><given-names>BS</given-names></name>
<name><surname>Trepel</surname><given-names>JB</given-names></name>
<name><surname>Stetler-Stevenson</surname><given-names>WG</given-names></name>
<name><surname>Neckers</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>Combined inhibition of Wee1 and Hsp90 activates intrinsic apoptosis in cancer cells</article-title><source>Cell Cycle (Georgetown, Tex.)</source><volume>11</volume><fpage>3649</fpage><lpage>3655</lpage><pub-id pub-id-type="doi">10.4161/cc.21926</pub-id><?supplied-pmid 22935698?><pub-id pub-id-type="pmid">22935698</pub-id></element-citation></ref><ref id="bib55"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Johnson</surname><given-names>BE</given-names></name>
<name><surname>Whang-Peng</surname><given-names>J</given-names></name>
<name><surname>Naylor</surname><given-names>SL</given-names></name>
<name><surname>Zbar</surname><given-names>B</given-names></name>
<name><surname>Brauch</surname><given-names>H</given-names></name>
<name><surname>Lee</surname><given-names>E</given-names></name>
<name><surname>Simmons</surname><given-names>A</given-names></name>
<name><surname>Russell</surname><given-names>E</given-names></name>
<name><surname>Nam</surname><given-names>MH</given-names></name>
<name><surname>Gazdar</surname><given-names>AF</given-names></name>
</person-group><year iso-8601-date="1989">1989</year><article-title>Retention of chromosome 3 in extrapulmonary small cell cancer shown by molecular and cytogenetic studies</article-title><source>Journal of the National Cancer Institute</source><volume>81</volume><fpage>1223</fpage><lpage>1228</lpage><pub-id pub-id-type="doi">10.1093/jnci/81.16.1223</pub-id><?supplied-pmid 2569043?><pub-id pub-id-type="pmid">2569043</pub-id></element-citation></ref><ref id="bib56"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kaighn</surname><given-names>ME</given-names></name>
<name><surname>Narayan</surname><given-names>KS</given-names></name>
<name><surname>Ohnuki</surname><given-names>Y</given-names></name>
<name><surname>Lechner</surname><given-names>JF</given-names></name>
<name><surname>Jones</surname><given-names>LW</given-names></name>
</person-group><year iso-8601-date="1979">1979</year><article-title>Establishment and characterization of a human prostatic carcinoma cell line (PC-3)</article-title><source>Investigative Urology</source><volume>17</volume><fpage>16</fpage><lpage>23</lpage><?supplied-pmid 447482?><pub-id pub-id-type="pmid">447482</pub-id></element-citation></ref><ref id="bib57"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kauffman</surname><given-names>SA</given-names></name>
</person-group><year iso-8601-date="1969">1969</year><article-title>Metabolic stability and epigenesis in randomly constructed genetic nets</article-title><source>Journal of Theoretical Biology</source><volume>22</volume><fpage>437</fpage><lpage>467</lpage><pub-id pub-id-type="doi">10.1016/0022-5193(69)90015-0</pub-id><?supplied-pmid 5803332?><pub-id pub-id-type="pmid">5803332</pub-id></element-citation></ref><ref id="bib58"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kholodenko</surname><given-names>BN</given-names></name>
<name><surname>Schuster</surname><given-names>S</given-names></name>
<name><surname>Rohwer</surname><given-names>JM</given-names></name>
<name><surname>Cascante</surname><given-names>M</given-names></name>
<name><surname>Westerhoff</surname><given-names>HV</given-names></name>
</person-group><year iso-8601-date="1995">1995</year><article-title>Composite control of cell function: metabolic pathways behaving as single control units</article-title><source>FEBS Letters</source><volume>368</volume><fpage>1</fpage><lpage>4</lpage><pub-id pub-id-type="doi">10.1016/0014-5793(95)00562-n</pub-id><?supplied-pmid 7615057?><pub-id pub-id-type="pmid">7615057</pub-id></element-citation></ref><ref id="bib59"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Korenchuk</surname><given-names>S</given-names></name>
<name><surname>Lehr</surname><given-names>JE</given-names></name>
<name><surname>MClean</surname><given-names>L</given-names></name>
<name><surname>Lee</surname><given-names>YG</given-names></name>
<name><surname>Whitney</surname><given-names>S</given-names></name>
<name><surname>Vessella</surname><given-names>R</given-names></name>
<name><surname>Lin</surname><given-names>DL</given-names></name>
<name><surname>Pienta</surname><given-names>KJ</given-names></name>
</person-group><year iso-8601-date="2001">2001</year><article-title>VCaP, a cell-based model system of human prostate cancer</article-title><source>Vivo Athens Greece</source><volume>15</volume><fpage>163</fpage><lpage>168</lpage></element-citation></ref><ref id="bib60"><element-citation publication-type="preprint"><person-group person-group-type="author">
<name><surname>Korotkevich</surname><given-names>G</given-names></name>
<name><surname>Sukhov</surname><given-names>V</given-names></name>
<name><surname>Sergushichev</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Fast Gene Set Enrichment Analysis</article-title><source>bioRxiv</source><pub-id pub-id-type="doi">10.1101/060012</pub-id></element-citation></ref><ref id="bib61"><element-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Korzybski</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="1995">1995</year><source>Science and Sanity: An Introduction to Non-Aristotelian Systems and General Semantics</source><publisher-loc>Brooklyn</publisher-loc><publisher-name>Inst. of General Semantics</publisher-name></element-citation></ref><ref id="bib62"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Krug</surname><given-names>K</given-names></name>
<name><surname>Mertins</surname><given-names>P</given-names></name>
<name><surname>Zhang</surname><given-names>B</given-names></name>
<name><surname>Hornbeck</surname><given-names>P</given-names></name>
<name><surname>Raju</surname><given-names>R</given-names></name>
<name><surname>Ahmad</surname><given-names>R</given-names></name>
<name><surname>Szucs</surname><given-names>M</given-names></name>
<name><surname>Mundt</surname><given-names>F</given-names></name>
<name><surname>Forestier</surname><given-names>D</given-names></name>
<name><surname>Jane-Valbuena</surname><given-names>J</given-names></name>
<name><surname>Keshishian</surname><given-names>H</given-names></name>
<name><surname>Gillette</surname><given-names>MA</given-names></name>
<name><surname>Tamayo</surname><given-names>P</given-names></name>
<name><surname>Mesirov</surname><given-names>JP</given-names></name>
<name><surname>Jaffe</surname><given-names>JD</given-names></name>
<name><surname>Carr</surname><given-names>SA</given-names></name>
<name><surname>Mani</surname><given-names>DR</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>A Curated Resource for Phosphosite-specific Signature Analysis</article-title><source>Molecular &#x00026; Cellular Proteomics</source><volume>18</volume><fpage>576</fpage><lpage>593</lpage><pub-id pub-id-type="doi">10.1074/mcp.TIR118.000943</pub-id><?supplied-pmid 30563849?><pub-id pub-id-type="pmid">30563849</pub-id></element-citation></ref><ref id="bib63"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Krzyszczyk</surname><given-names>P</given-names></name>
<name><surname>Acevedo</surname><given-names>A</given-names></name>
<name><surname>Davidoff</surname><given-names>EJ</given-names></name>
<name><surname>Timmins</surname><given-names>LM</given-names></name>
<name><surname>Marrero-Berrios</surname><given-names>I</given-names></name>
<name><surname>Patel</surname><given-names>M</given-names></name>
<name><surname>White</surname><given-names>C</given-names></name>
<name><surname>Lowe</surname><given-names>C</given-names></name>
<name><surname>Sherba</surname><given-names>JJ</given-names></name>
<name><surname>Hartmanshenn</surname><given-names>C</given-names></name>
<name><surname>O&#x02019;Neill</surname><given-names>KM</given-names></name>
<name><surname>Balter</surname><given-names>ML</given-names></name>
<name><surname>Fritz</surname><given-names>ZR</given-names></name>
<name><surname>Androulakis</surname><given-names>IP</given-names></name>
<name><surname>Schloss</surname><given-names>RS</given-names></name>
<name><surname>Yarmush</surname><given-names>ML</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>The growing role of precision and personalized medicine for cancer treatment</article-title><source>Technology</source><volume>6</volume><fpage>79</fpage><lpage>100</lpage><pub-id pub-id-type="doi">10.1142/S2339547818300020</pub-id><?supplied-pmid 30713991?><pub-id pub-id-type="pmid">30713991</pub-id></element-citation></ref><ref id="bib64"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kunderfranco</surname><given-names>P</given-names></name>
<name><surname>Mello-Grand</surname><given-names>M</given-names></name>
<name><surname>Cangemi</surname><given-names>R</given-names></name>
<name><surname>Pellini</surname><given-names>S</given-names></name>
<name><surname>Mensah</surname><given-names>A</given-names></name>
<name><surname>Albertini</surname><given-names>V</given-names></name>
<name><surname>Malek</surname><given-names>A</given-names></name>
<name><surname>Chiorino</surname><given-names>G</given-names></name>
<name><surname>Catapano</surname><given-names>CV</given-names></name>
<name><surname>Carbone</surname><given-names>GM</given-names></name>
</person-group><year iso-8601-date="2010">2010</year><article-title>ETS transcription factors control transcription of EZH2 and epigenetic silencing of the tumor suppressor gene Nkx3.1 in prostate cancer</article-title><source>PLOS ONE</source><volume>5</volume><elocation-id>e10547</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0010547</pub-id><?supplied-pmid 20479932?><pub-id pub-id-type="pmid">20479932</pub-id></element-citation></ref><ref id="bib65"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kuperstein</surname><given-names>I</given-names></name>
<name><surname>Bonnet</surname><given-names>E</given-names></name>
<name><surname>Nguyen</surname><given-names>HA</given-names></name>
<name><surname>Cohen</surname><given-names>D</given-names></name>
<name><surname>Viara</surname><given-names>E</given-names></name>
<name><surname>Grieco</surname><given-names>L</given-names></name>
<name><surname>Fourquet</surname><given-names>S</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
<name><surname>Russo</surname><given-names>C</given-names></name>
<name><surname>Kondratova</surname><given-names>M</given-names></name>
<name><surname>Dutreix</surname><given-names>M</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Atlas of Cancer Signalling Network: a systems biology resource for integrative analysis of cancer data with Google Maps</article-title><source>Oncogenesis</source><volume>4</volume><elocation-id>e160</elocation-id><pub-id pub-id-type="doi">10.1038/oncsis.2015.19</pub-id><?supplied-pmid 26192618?><pub-id pub-id-type="pmid">26192618</pub-id></element-citation></ref><ref id="bib66"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Lai</surname><given-names>SL</given-names></name>
<name><surname>Brauch</surname><given-names>H</given-names></name>
<name><surname>Knutsen</surname><given-names>T</given-names></name>
<name><surname>Johnson</surname><given-names>BE</given-names></name>
<name><surname>Nau</surname><given-names>MM</given-names></name>
<name><surname>Mitsudomi</surname><given-names>T</given-names></name>
<name><surname>Tsai</surname><given-names>CM</given-names></name>
<name><surname>Whang-Peng</surname><given-names>J</given-names></name>
<name><surname>Zbar</surname><given-names>B</given-names></name>
<name><surname>Kaye</surname><given-names>FJ</given-names></name>
</person-group><year iso-8601-date="1995">1995</year><article-title>Molecular genetic characterization of neuroendocrine lung cancer cell lines</article-title><source>Anticancer Research</source><volume>15</volume><fpage>225</fpage><lpage>232</lpage><?supplied-pmid 7762988?><pub-id pub-id-type="pmid">7762988</pub-id></element-citation></ref><ref id="bib67"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>L&#x000e9;</surname><given-names>S</given-names></name>
<name><surname>Josse</surname><given-names>J</given-names></name>
<name><surname>Husson</surname><given-names>F</given-names></name>
</person-group><year iso-8601-date="2008">2008</year><article-title>FactoMineR: An R Package for Multivariate Analysis</article-title><source>Journal of Statistical Software</source><volume>25</volume><fpage>1</fpage><lpage>18</lpage><pub-id pub-id-type="doi">10.18637/jss.v025.i01</pub-id></element-citation></ref><ref id="bib68"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Le</surname><given-names>B</given-names></name>
<name><surname>Powers</surname><given-names>GL</given-names></name>
<name><surname>Tam</surname><given-names>YT</given-names></name>
<name><surname>Schumacher</surname><given-names>N</given-names></name>
<name><surname>Malinowski</surname><given-names>RL</given-names></name>
<name><surname>Steinke</surname><given-names>L</given-names></name>
<name><surname>Kwon</surname><given-names>G</given-names></name>
<name><surname>Marker</surname><given-names>PC</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Multi-drug loaded micelles delivering chemotherapy and targeted therapies directed against HSP90 and the PI3K/AKT/mTOR pathway in prostate cancer</article-title><source>PLOS ONE</source><volume>12</volume><elocation-id>e0174658</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0174658</pub-id><?supplied-pmid 28350865?><pub-id pub-id-type="pmid">28350865</pub-id></element-citation></ref><ref id="bib69"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Le Nov&#x000e8;re</surname><given-names>N</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Quantitative and logic modelling of molecular and gene networks</article-title><source>Nature Reviews. Genetics</source><volume>16</volume><fpage>146</fpage><lpage>158</lpage><pub-id pub-id-type="doi">10.1038/nrg3885</pub-id><?supplied-pmid 25645874?><pub-id pub-id-type="pmid">25645874</pub-id></element-citation></ref><ref id="bib70"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Liberzon</surname><given-names>A</given-names></name>
<name><surname>Birger</surname><given-names>C</given-names></name>
<name><surname>Thorvaldsd&#x000f3;ttir</surname><given-names>H</given-names></name>
<name><surname>Ghandi</surname><given-names>M</given-names></name>
<name><surname>Mesirov</surname><given-names>JP</given-names></name>
<name><surname>Tamayo</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>The Molecular Signatures Database (MSigDB) hallmark gene set collection</article-title><source>Cell Systems</source><volume>1</volume><fpage>417</fpage><lpage>425</lpage><pub-id pub-id-type="doi">10.1016/j.cels.2015.12.004</pub-id><?supplied-pmid 26771021?><pub-id pub-id-type="pmid">26771021</pub-id></element-citation></ref><ref id="bib71"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Madani Tonekaboni</surname><given-names>SA</given-names></name>
<name><surname>Soltan Ghoraie</surname><given-names>L</given-names></name>
<name><surname>Manem</surname><given-names>VSK</given-names></name>
<name><surname>Haibe-Kains</surname><given-names>B</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>Predictive approaches for drug combination discovery in cancer</article-title><source>Briefings in Bioinformatics</source><volume>19</volume><fpage>263</fpage><lpage>276</lpage><pub-id pub-id-type="doi">10.1093/bib/bbw104</pub-id><?supplied-pmid 27881431?><pub-id pub-id-type="pmid">27881431</pub-id></element-citation></ref><ref id="bib72"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Malik-Sheriff</surname><given-names>RS</given-names></name>
<name><surname>Glont</surname><given-names>M</given-names></name>
<name><surname>Nguyen</surname><given-names>TVN</given-names></name>
<name><surname>Tiwari</surname><given-names>K</given-names></name>
<name><surname>Roberts</surname><given-names>MG</given-names></name>
<name><surname>Xavier</surname><given-names>A</given-names></name>
<name><surname>Vu</surname><given-names>MT</given-names></name>
<name><surname>Men</surname><given-names>J</given-names></name>
<name><surname>Maire</surname><given-names>M</given-names></name>
<name><surname>Kananathan</surname><given-names>S</given-names></name>
<name><surname>Fairbanks</surname><given-names>EL</given-names></name>
<name><surname>Meyer</surname><given-names>JP</given-names></name>
<name><surname>Arankalle</surname><given-names>C</given-names></name>
<name><surname>Varusai</surname><given-names>TM</given-names></name>
<name><surname>Knight-Schrijver</surname><given-names>V</given-names></name>
<name><surname>Li</surname><given-names>L</given-names></name>
<name><surname>Due&#x000f1;as-Roca</surname><given-names>C</given-names></name>
<name><surname>Dass</surname><given-names>G</given-names></name>
<name><surname>Keating</surname><given-names>SM</given-names></name>
<name><surname>Park</surname><given-names>YM</given-names></name>
<name><surname>Buso</surname><given-names>N</given-names></name>
<name><surname>Rodriguez</surname><given-names>N</given-names></name>
<name><surname>Hucka</surname><given-names>M</given-names></name>
<name><surname>Hermjakob</surname><given-names>H</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>BioModels-15 years of sharing computational models in life science</article-title><source>Nucleic Acids Research</source><volume>48</volume><fpage>D407</fpage><lpage>D415</lpage><pub-id pub-id-type="doi">10.1093/nar/gkz1055</pub-id><?supplied-pmid 31701150?><pub-id pub-id-type="pmid">31701150</pub-id></element-citation></ref><ref id="bib73"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Marshall</surname><given-names>CH</given-names></name>
<name><surname>Fu</surname><given-names>W</given-names></name>
<name><surname>Wang</surname><given-names>H</given-names></name>
<name><surname>Baras</surname><given-names>AS</given-names></name>
<name><surname>Lotan</surname><given-names>TL</given-names></name>
<name><surname>Antonarakis</surname><given-names>ES</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Prevalence of DNA repair gene mutations in localized prostate cancer according to clinical and pathologic features: association of Gleason score and tumor stage</article-title><source>Prostate Cancer and Prostatic Diseases</source><volume>22</volume><fpage>59</fpage><lpage>65</lpage><pub-id pub-id-type="doi">10.1038/s41391-018-0086-1</pub-id><?supplied-pmid 30171229?><pub-id pub-id-type="pmid">30171229</pub-id></element-citation></ref><ref id="bib74"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Martignetti</surname><given-names>L</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
<name><surname>Bonnet</surname><given-names>E</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>ROMA: Representation and Quantification of Module Activity from Target Expression Data</article-title><source>Frontiers in Genetics</source><volume>7</volume><elocation-id>18</elocation-id><pub-id pub-id-type="doi">10.3389/fgene.2016.00018</pub-id><?supplied-pmid 26925094?><pub-id pub-id-type="pmid">26925094</pub-id></element-citation></ref><ref id="bib75"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Menden</surname><given-names>MP</given-names></name>
<name><surname>Wang</surname><given-names>D</given-names></name>
<name><surname>Mason</surname><given-names>MJ</given-names></name>
<name><surname>Szalai</surname><given-names>B</given-names></name>
<name><surname>Bulusu</surname><given-names>KC</given-names></name>
<name><surname>Guan</surname><given-names>Y</given-names></name>
<name><surname>Yu</surname><given-names>T</given-names></name>
<name><surname>Kang</surname><given-names>J</given-names></name>
<name><surname>Jeon</surname><given-names>M</given-names></name>
<name><surname>Wolfinger</surname><given-names>R</given-names></name>
<name><surname>Nguyen</surname><given-names>T</given-names></name>
<name><surname>Zaslavskiy</surname><given-names>M</given-names></name>
<collab>AstraZeneca-Sanger Drug Combination DREAM Consortium</collab>
<name><surname>Jang</surname><given-names>IS</given-names></name>
<name><surname>Ghazoui</surname><given-names>Z</given-names></name>
<name><surname>Ahsen</surname><given-names>ME</given-names></name>
<name><surname>Vogel</surname><given-names>R</given-names></name>
<name><surname>Neto</surname><given-names>EC</given-names></name>
<name><surname>Norman</surname><given-names>T</given-names></name>
<name><surname>Tang</surname><given-names>EKY</given-names></name>
<name><surname>Garnett</surname><given-names>MJ</given-names></name>
<name><surname>Veroli</surname><given-names>GYD</given-names></name>
<name><surname>Fawell</surname><given-names>S</given-names></name>
<name><surname>Stolovitzky</surname><given-names>G</given-names></name>
<name><surname>Guinney</surname><given-names>J</given-names></name>
<name><surname>Dry</surname><given-names>JR</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Community assessment to advance computational prediction of cancer drug combinations in a pharmacogenomic screen</article-title><source>Nature Communications</source><volume>10</volume><elocation-id>2674</elocation-id><pub-id pub-id-type="doi">10.1038/s41467-019-09799-2</pub-id><?supplied-pmid 31209238?><pub-id pub-id-type="pmid">31209238</pub-id></element-citation></ref><ref id="bib76"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Molinari</surname><given-names>C</given-names></name>
<name><surname>Marisi</surname><given-names>G</given-names></name>
<name><surname>Passardi</surname><given-names>A</given-names></name>
<name><surname>Matteucci</surname><given-names>L</given-names></name>
<name><surname>De Maio</surname><given-names>G</given-names></name>
<name><surname>Ulivi</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>Heterogeneity in Colorectal Cancer: A Challenge for Personalized Medicine?</article-title><source>International Journal of Molecular Sciences</source><volume>19</volume><elocation-id>3733</elocation-id><pub-id pub-id-type="doi">10.3390/ijms19123733</pub-id><?supplied-pmid 30477151?><pub-id pub-id-type="pmid">30477151</pub-id></element-citation></ref><ref id="bib77"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Montagud</surname><given-names>A</given-names></name>
<name><surname>Traynard</surname><given-names>P</given-names></name>
<name><surname>Martignetti</surname><given-names>L</given-names></name>
<name><surname>Bonnet</surname><given-names>E</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Conceptual and computational framework for logical modelling of biological networks deregulated in diseases</article-title><source>Briefings in Bioinformatics</source><volume>20</volume><fpage>1238</fpage><lpage>1249</lpage><pub-id pub-id-type="doi">10.1093/bib/bbx163</pub-id><?supplied-pmid 29237040?><pub-id pub-id-type="pmid">29237040</pub-id></element-citation></ref><ref id="bib78"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Montagud</surname><given-names>A</given-names></name>
<name><surname>Ponce-de-Leon</surname><given-names>M</given-names></name>
<name><surname>Valencia</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2021">2021</year><article-title>Systems biology at the giga-scale: Large multiscale models of complex, heterogeneous multicellular systems</article-title><source>Current Opinion in Systems Biology</source><volume>28</volume><elocation-id>100385</elocation-id><pub-id pub-id-type="doi">10.1016/j.coisb.2021.100385</pub-id></element-citation></ref><ref id="bib79"><element-citation publication-type="software"><person-group person-group-type="author">
<name><surname>Montagud</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2022">2022a</year><data-title>PROFILE_v2</data-title><version designator="swh:1:rev:9290d67b20bde9a9d85c1017e5cd241c6dcdef23">swh:1:rev:9290d67b20bde9a9d85c1017e5cd241c6dcdef23</version><source>Software Heritage</source><ext-link xlink:href="https://archive.softwareheritage.org/swh:1:dir:539182867f2154b4aa3fe50c2f8d63c60f64063d;origin=https://github.com/ArnauMontagud/PROFILE_v2;visit=swh:1:snp:3fd7794e42443b85d2441df0a7643e0290873a7c;anchor=swh:1:rev:9290d67b20bde9a9d85c1017e5cd241c6dcdef23" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:dir:539182867f2154b4aa3fe50c2f8d63c60f64063d;origin=https://github.com/ArnauMontagud/PROFILE_v2;visit=swh:1:snp:3fd7794e42443b85d2441df0a7643e0290873a7c;anchor=swh:1:rev:9290d67b20bde9a9d85c1017e5cd241c6dcdef23</ext-link></element-citation></ref><ref id="bib80"><element-citation publication-type="software"><person-group person-group-type="author">
<name><surname>Montagud</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2022">2022b</year><data-title>Logical_modelling_pipeline</data-title><version designator="swh:1:rev:5524aae3eece3de1311a1724bd4c6452f0be0542">swh:1:rev:5524aae3eece3de1311a1724bd4c6452f0be0542</version><source>Software Heritage</source><ext-link xlink:href="https://archive.softwareheritage.org/swh:1:dir:af13c4fed5e31937b423e64a1045be30a6f7ee42;origin=https://github.com/sysbio-curie/Logical_modelling_pipeline;visit=swh:1:snp:41e2144ec65abac0d475911d6e54020b6f730e30;anchor=swh:1:rev:5524aae3eece3de1311a1724bd4c6452f0be0542" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:dir:af13c4fed5e31937b423e64a1045be30a6f7ee42;origin=https://github.com/sysbio-curie/Logical_modelling_pipeline;visit=swh:1:snp:41e2144ec65abac0d475911d6e54020b6f730e30;anchor=swh:1:rev:5524aae3eece3de1311a1724bd4c6452f0be0542</ext-link></element-citation></ref><ref id="bib81"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ozsv&#x000e1;ri</surname><given-names>B</given-names></name>
<name><surname>Pusk&#x000e1;s</surname><given-names>LG</given-names></name>
<name><surname>Nagy</surname><given-names>LI</given-names></name>
<name><surname>Kanizsai</surname><given-names>I</given-names></name>
<name><surname>Gyuris</surname><given-names>M</given-names></name>
<name><surname>Mad&#x000e1;csi</surname><given-names>R</given-names></name>
<name><surname>Feh&#x000e9;r</surname><given-names>LZ</given-names></name>
<name><surname>Ger&#x000f6;</surname><given-names>D</given-names></name>
<name><surname>Szab&#x000f3;</surname><given-names>C</given-names></name>
</person-group><year iso-8601-date="2010">2010</year><article-title>A cell-microelectronic sensing technique for the screening of cytoprotective compounds</article-title><source>International Journal of Molecular Medicine</source><volume>25</volume><fpage>525</fpage><lpage>530</lpage><pub-id pub-id-type="doi">10.3892/ijmm_00000373</pub-id><?supplied-pmid 20198300?><pub-id pub-id-type="pmid">20198300</pub-id></element-citation></ref><ref id="bib82"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Pacey</surname><given-names>S</given-names></name>
<name><surname>Wilson</surname><given-names>RH</given-names></name>
<name><surname>Walton</surname><given-names>M</given-names></name>
<name><surname>Eatock</surname><given-names>MM</given-names></name>
<name><surname>Hardcastle</surname><given-names>A</given-names></name>
<name><surname>Zetterlund</surname><given-names>A</given-names></name>
<name><surname>Arkenau</surname><given-names>H-T</given-names></name>
<name><surname>Moreno-Farre</surname><given-names>J</given-names></name>
<name><surname>Banerji</surname><given-names>U</given-names></name>
<name><surname>Roels</surname><given-names>B</given-names></name>
<name><surname>Peachey</surname><given-names>H</given-names></name>
<name><surname>Aherne</surname><given-names>W</given-names></name>
<name><surname>de Bono</surname><given-names>JS</given-names></name>
<name><surname>Raynaud</surname><given-names>F</given-names></name>
<name><surname>Workman</surname><given-names>P</given-names></name>
<name><surname>Judson</surname><given-names>I</given-names></name>
</person-group><year iso-8601-date="2011">2011</year><article-title>A phase I study of the heat shock protein 90 inhibitor alvespimycin (17-DMAG) given intravenously to patients with advanced solid tumors</article-title><source>Clinical Cancer Research</source><volume>17</volume><fpage>1561</fpage><lpage>1570</lpage><pub-id pub-id-type="doi">10.1158/1078-0432.CCR-10-1927</pub-id><?supplied-pmid 21278242?><pub-id pub-id-type="pmid">21278242</pub-id></element-citation></ref><ref id="bib83"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Perfetto</surname><given-names>L</given-names></name>
<name><surname>Briganti</surname><given-names>L</given-names></name>
<name><surname>Calderone</surname><given-names>A</given-names></name>
<name><surname>Cerquone Perpetuini</surname><given-names>A</given-names></name>
<name><surname>Iannuccelli</surname><given-names>M</given-names></name>
<name><surname>Langone</surname><given-names>F</given-names></name>
<name><surname>Licata</surname><given-names>L</given-names></name>
<name><surname>Marinkovic</surname><given-names>M</given-names></name>
<name><surname>Mattioni</surname><given-names>A</given-names></name>
<name><surname>Pavlidou</surname><given-names>T</given-names></name>
<name><surname>Peluso</surname><given-names>D</given-names></name>
<name><surname>Petrilli</surname><given-names>LL</given-names></name>
<name><surname>Pirr&#x000f2;</surname><given-names>S</given-names></name>
<name><surname>Posca</surname><given-names>D</given-names></name>
<name><surname>Santonico</surname><given-names>E</given-names></name>
<name><surname>Silvestri</surname><given-names>A</given-names></name>
<name><surname>Spada</surname><given-names>F</given-names></name>
<name><surname>Castagnoli</surname><given-names>L</given-names></name>
<name><surname>Cesareni</surname><given-names>G</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>SIGNOR: a database of causal relationships between biological entities</article-title><source>Nucleic Acids Research</source><volume>44</volume><fpage>D548</fpage><lpage>D554</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1048</pub-id><?supplied-pmid 26467481?><pub-id pub-id-type="pmid">26467481</pub-id></element-citation></ref><ref id="bib84"><element-citation publication-type="preprint"><person-group person-group-type="author">
<name><surname>Ponce-de-Leon</surname><given-names>M</given-names></name>
<name><surname>Montagud</surname><given-names>A</given-names></name>
<name><surname>Akasiadis</surname><given-names>C</given-names></name>
<name><surname>Schreiber</surname><given-names>J</given-names></name>
<name><surname>Ntiniakou</surname><given-names>T</given-names></name>
<name><surname>Valencia</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2021">2021</year><article-title>Optimizing Dosage-Specific Treatments in a Multi-Scale Model of a Tumor Growth</article-title><source>bioRxiv</source><pub-id pub-id-type="doi">10.1101/2021.12.17.473136</pub-id></element-citation></ref><ref id="bib85"><element-citation publication-type="preprint"><person-group person-group-type="author">
<name><surname>Ponce-de-Leon</surname><given-names>M</given-names></name>
<name><surname>Montagud</surname><given-names>A</given-names></name>
<name><surname>Noel</surname><given-names>V</given-names></name>
<name><surname>Pradas</surname><given-names>G</given-names></name>
<name><surname>Meert</surname><given-names>A</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
<name><surname>Valencia</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2022">2022</year><article-title>PhysiBoSS 2.0: A Sustainable Integration of Stochastic Boolean and Agent-Based Modelling Frameworks</article-title><source>bioRxiv</source><pub-id pub-id-type="doi">10.1101/2022.01.06.468363</pub-id></element-citation></ref><ref id="bib86"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Raynaud</surname><given-names>FI</given-names></name>
<name><surname>Eccles</surname><given-names>SA</given-names></name>
<name><surname>Patel</surname><given-names>S</given-names></name>
<name><surname>Alix</surname><given-names>S</given-names></name>
<name><surname>Box</surname><given-names>G</given-names></name>
<name><surname>Chuckowree</surname><given-names>I</given-names></name>
<name><surname>Folkes</surname><given-names>A</given-names></name>
<name><surname>Gowan</surname><given-names>S</given-names></name>
<name><surname>De Haven Brandon</surname><given-names>A</given-names></name>
<name><surname>Di Stefano</surname><given-names>F</given-names></name>
<name><surname>Hayes</surname><given-names>A</given-names></name>
<name><surname>Henley</surname><given-names>AT</given-names></name>
<name><surname>Lensun</surname><given-names>L</given-names></name>
<name><surname>Pergl-Wilson</surname><given-names>G</given-names></name>
<name><surname>Robson</surname><given-names>A</given-names></name>
<name><surname>Saghir</surname><given-names>N</given-names></name>
<name><surname>Zhyvoloup</surname><given-names>A</given-names></name>
<name><surname>McDonald</surname><given-names>E</given-names></name>
<name><surname>Sheldrake</surname><given-names>P</given-names></name>
<name><surname>Shuttleworth</surname><given-names>S</given-names></name>
<name><surname>Valenti</surname><given-names>M</given-names></name>
<name><surname>Wan</surname><given-names>NC</given-names></name>
<name><surname>Clarke</surname><given-names>PA</given-names></name>
<name><surname>Workman</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2009">2009</year><article-title>Biological properties of potent inhibitors of class I phosphatidylinositide 3-kinases: from PI-103 through PI-540, PI-620 to the oral agent GDC-0941</article-title><source>Molecular Cancer Therapeutics</source><volume>8</volume><fpage>1725</fpage><lpage>1738</lpage><pub-id pub-id-type="doi">10.1158/1535-7163.MCT-08-1200</pub-id><?supplied-pmid 19584227?><pub-id pub-id-type="pmid">19584227</pub-id></element-citation></ref><ref id="bib87"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Remy</surname><given-names>E</given-names></name>
<name><surname>Rebouissou</surname><given-names>S</given-names></name>
<name><surname>Chaouiya</surname><given-names>C</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
<name><surname>Radvanyi</surname><given-names>F</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>A Modeling Approach to Explain Mutually Exclusive and Co-Occurring Genetic Alterations in Bladder Tumorigenesis</article-title><source>Cancer Research</source><volume>75</volume><fpage>4042</fpage><lpage>4052</lpage><pub-id pub-id-type="doi">10.1158/0008-5472.CAN-15-0602</pub-id><?supplied-pmid 26238783?><pub-id pub-id-type="pmid">26238783</pub-id></element-citation></ref><ref id="bib88"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ren</surname><given-names>G</given-names></name>
<name><surname>Baritaki</surname><given-names>S</given-names></name>
<name><surname>Marathe</surname><given-names>H</given-names></name>
<name><surname>Feng</surname><given-names>J</given-names></name>
<name><surname>Park</surname><given-names>S</given-names></name>
<name><surname>Beach</surname><given-names>S</given-names></name>
<name><surname>Bazeley</surname><given-names>PS</given-names></name>
<name><surname>Beshir</surname><given-names>AB</given-names></name>
<name><surname>Fenteany</surname><given-names>G</given-names></name>
<name><surname>Mehra</surname><given-names>R</given-names></name>
<name><surname>Daignault</surname><given-names>S</given-names></name>
<name><surname>Al-Mulla</surname><given-names>F</given-names></name>
<name><surname>Keller</surname><given-names>E</given-names></name>
<name><surname>Bonavida</surname><given-names>B</given-names></name>
<name><surname>de la Serna</surname><given-names>I</given-names></name>
<name><surname>Yeung</surname><given-names>KC</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>Polycomb protein EZH2 regulates tumor invasion via the transcriptional repression of the metastasis suppressor RKIP in breast and prostate cancer</article-title><source>Cancer Research</source><volume>72</volume><fpage>3091</fpage><lpage>3104</lpage><pub-id pub-id-type="doi">10.1158/0008-5472.CAN-11-3546</pub-id><?supplied-pmid 22505648?><pub-id pub-id-type="pmid">22505648</pub-id></element-citation></ref><ref id="bib89"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Rivas-Barragan</surname><given-names>D</given-names></name>
<name><surname>Mubeen</surname><given-names>S</given-names></name>
<name><surname>Guim Bernat</surname><given-names>F</given-names></name>
<name><surname>Hofmann-Apitius</surname><given-names>M</given-names></name>
<name><surname>Domingo-Fern&#x000e1;ndez</surname><given-names>D</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>Drug2ways: Reasoning over causal paths in biological networks for drug discovery</article-title><source>PLOS Computational Biology</source><volume>16</volume><elocation-id>e1008464</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1008464</pub-id><?supplied-pmid 33264280?><pub-id pub-id-type="pmid">33264280</pub-id></element-citation></ref><ref id="bib90"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Robinson</surname><given-names>D</given-names></name>
<name><surname>Van Allen</surname><given-names>EM</given-names></name>
<name><surname>Wu</surname><given-names>Y-M</given-names></name>
<name><surname>Schultz</surname><given-names>N</given-names></name>
<name><surname>Lonigro</surname><given-names>RJ</given-names></name>
<name><surname>Mosquera</surname><given-names>J-M</given-names></name>
<name><surname>Montgomery</surname><given-names>B</given-names></name>
<name><surname>Taplin</surname><given-names>M-E</given-names></name>
<name><surname>Pritchard</surname><given-names>CC</given-names></name>
<name><surname>Attard</surname><given-names>G</given-names></name>
<name><surname>Beltran</surname><given-names>H</given-names></name>
<name><surname>Abida</surname><given-names>W</given-names></name>
<name><surname>Bradley</surname><given-names>RK</given-names></name>
<name><surname>Vinson</surname><given-names>J</given-names></name>
<name><surname>Cao</surname><given-names>X</given-names></name>
<name><surname>Vats</surname><given-names>P</given-names></name>
<name><surname>Kunju</surname><given-names>LP</given-names></name>
<name><surname>Hussain</surname><given-names>M</given-names></name>
<name><surname>Feng</surname><given-names>FY</given-names></name>
<name><surname>Tomlins</surname><given-names>SA</given-names></name>
<name><surname>Cooney</surname><given-names>KA</given-names></name>
<name><surname>Smith</surname><given-names>DC</given-names></name>
<name><surname>Brennan</surname><given-names>C</given-names></name>
<name><surname>Siddiqui</surname><given-names>J</given-names></name>
<name><surname>Mehra</surname><given-names>R</given-names></name>
<name><surname>Chen</surname><given-names>Y</given-names></name>
<name><surname>Rathkopf</surname><given-names>DE</given-names></name>
<name><surname>Morris</surname><given-names>MJ</given-names></name>
<name><surname>Solomon</surname><given-names>SB</given-names></name>
<name><surname>Durack</surname><given-names>JC</given-names></name>
<name><surname>Reuter</surname><given-names>VE</given-names></name>
<name><surname>Gopalan</surname><given-names>A</given-names></name>
<name><surname>Gao</surname><given-names>J</given-names></name>
<name><surname>Loda</surname><given-names>M</given-names></name>
<name><surname>Lis</surname><given-names>RT</given-names></name>
<name><surname>Bowden</surname><given-names>M</given-names></name>
<name><surname>Balk</surname><given-names>SP</given-names></name>
<name><surname>Gaviola</surname><given-names>G</given-names></name>
<name><surname>Sougnez</surname><given-names>C</given-names></name>
<name><surname>Gupta</surname><given-names>M</given-names></name>
<name><surname>Yu</surname><given-names>EY</given-names></name>
<name><surname>Mostaghel</surname><given-names>EA</given-names></name>
<name><surname>Cheng</surname><given-names>HH</given-names></name>
<name><surname>Mulcahy</surname><given-names>H</given-names></name>
<name><surname>True</surname><given-names>LD</given-names></name>
<name><surname>Plymate</surname><given-names>SR</given-names></name>
<name><surname>Dvinge</surname><given-names>H</given-names></name>
<name><surname>Ferraldeschi</surname><given-names>R</given-names></name>
<name><surname>Flohr</surname><given-names>P</given-names></name>
<name><surname>Miranda</surname><given-names>S</given-names></name>
<name><surname>Zafeiriou</surname><given-names>Z</given-names></name>
<name><surname>Tunariu</surname><given-names>N</given-names></name>
<name><surname>Mateo</surname><given-names>J</given-names></name>
<name><surname>Perez-Lopez</surname><given-names>R</given-names></name>
<name><surname>Demichelis</surname><given-names>F</given-names></name>
<name><surname>Robinson</surname><given-names>BD</given-names></name>
<name><surname>Schiffman</surname><given-names>M</given-names></name>
<name><surname>Nanus</surname><given-names>DM</given-names></name>
<name><surname>Tagawa</surname><given-names>ST</given-names></name>
<name><surname>Sigaras</surname><given-names>A</given-names></name>
<name><surname>Eng</surname><given-names>KW</given-names></name>
<name><surname>Elemento</surname><given-names>O</given-names></name>
<name><surname>Sboner</surname><given-names>A</given-names></name>
<name><surname>Heath</surname><given-names>EI</given-names></name>
<name><surname>Scher</surname><given-names>HI</given-names></name>
<name><surname>Pienta</surname><given-names>KJ</given-names></name>
<name><surname>Kantoff</surname><given-names>P</given-names></name>
<name><surname>de Bono</surname><given-names>JS</given-names></name>
<name><surname>Rubin</surname><given-names>MA</given-names></name>
<name><surname>Nelson</surname><given-names>PS</given-names></name>
<name><surname>Garraway</surname><given-names>LA</given-names></name>
<name><surname>Sawyers</surname><given-names>CL</given-names></name>
<name><surname>Chinnaiyan</surname><given-names>AM</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Integrative clinical genomics of advanced prostate cancer</article-title><source>Cell</source><volume>161</volume><fpage>1215</fpage><lpage>1228</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2015.05.001</pub-id><?supplied-pmid 26000489?><pub-id pub-id-type="pmid">26000489</pub-id></element-citation></ref><ref id="bib91"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Rosenblueth</surname><given-names>A</given-names></name>
<name><surname>Wiener</surname><given-names>N</given-names></name>
</person-group><year iso-8601-date="1945">1945</year><article-title>The Role of Models in Science</article-title><source>Philosophy of Science</source><volume>12</volume><fpage>316</fpage><lpage>321</lpage><pub-id pub-id-type="doi">10.1086/286874</pub-id></element-citation></ref><ref id="bib92"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Saadatpour</surname><given-names>A</given-names></name>
<name><surname>Albert</surname><given-names>R</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Boolean modeling of biological regulatory networks: a methodology tutorial</article-title><source>Methods (San Diego, Calif.)</source><volume>62</volume><fpage>3</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1016/j.ymeth.2012.10.012</pub-id><?supplied-pmid 23142247?><pub-id pub-id-type="pmid">23142247</pub-id></element-citation></ref><ref id="bib93"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
<name><surname>Alexopoulos</surname><given-names>LG</given-names></name>
<name><surname>Epperlein</surname><given-names>J</given-names></name>
<name><surname>Samaga</surname><given-names>R</given-names></name>
<name><surname>Lauffenburger</surname><given-names>DA</given-names></name>
<name><surname>Klamt</surname><given-names>S</given-names></name>
<name><surname>Sorger</surname><given-names>PK</given-names></name>
</person-group><year iso-8601-date="2009">2009</year><article-title>Discrete logic modelling as a means to link protein signalling networks with functional analysis of mammalian signal transduction</article-title><source>Molecular Systems Biology</source><volume>5</volume><elocation-id>331</elocation-id><pub-id pub-id-type="doi">10.1038/msb.2009.87</pub-id><?supplied-pmid 19953085?><pub-id pub-id-type="pmid">19953085</pub-id></element-citation></ref><ref id="bib94"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
<name><surname>Bl&#x000fc;thgen</surname><given-names>N</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>Personalized signaling models for personalized treatments</article-title><source>Molecular Systems Biology</source><volume>16</volume><elocation-id>e9042</elocation-id><pub-id pub-id-type="doi">10.15252/msb.20199042</pub-id><?supplied-pmid 32129942?><pub-id pub-id-type="pmid">32129942</pub-id></element-citation></ref><ref id="bib95"><element-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Saxena</surname><given-names>G</given-names></name>
<name><surname>Ponce-de-Leon</surname><given-names>M</given-names></name>
<name><surname>Montagud</surname><given-names>A</given-names></name>
<name><surname>Vicente Dorca</surname><given-names>D</given-names></name>
<name><surname>Valencia</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2021">2021</year><part-title>BioFVM-X: An MPI+OpenMP 3-D Simulator for Biological Systems In</part-title><person-group person-group-type="editor">
<name><surname>Cinquemani</surname><given-names>E</given-names></name>
<name><surname>Paulev&#x000e9;</surname><given-names>L</given-names></name>
</person-group><source>Computational Methods in Systems Biology, Lecture Notes in Computer Science</source><publisher-loc>Cham</publisher-loc><publisher-name>Springer International Publishing</publisher-name><fpage>266</fpage><lpage>279</lpage><pub-id pub-id-type="doi">10.1007/978-3-030-85633-5_18</pub-id></element-citation></ref><ref id="bib96"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Schopf</surname><given-names>FH</given-names></name>
<name><surname>Biebl</surname><given-names>MM</given-names></name>
<name><surname>Buchner</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>The HSP90 chaperone machinery</article-title><source>Nature Reviews. Molecular Cell Biology</source><volume>18</volume><fpage>345</fpage><lpage>360</lpage><pub-id pub-id-type="doi">10.1038/nrm.2017.20</pub-id><?supplied-pmid 28429788?><pub-id pub-id-type="pmid">28429788</pub-id></element-citation></ref><ref id="bib97"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Scott</surname><given-names>LJ</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>Enzalutamide: A Review in Castration-Resistant Prostate Cancer</article-title><source>Drugs</source><volume>78</volume><fpage>1913</fpage><lpage>1924</lpage><pub-id pub-id-type="doi">10.1007/s40265-018-1029-9</pub-id><?supplied-pmid 30535926?><pub-id pub-id-type="pmid">30535926</pub-id></element-citation></ref><ref id="bib98"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Shorning</surname><given-names>BY</given-names></name>
<name><surname>Dass</surname><given-names>MS</given-names></name>
<name><surname>Smalley</surname><given-names>MJ</given-names></name>
<name><surname>Pearson</surname><given-names>HB</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>The PI3K-AKT-mTOR Pathway and Prostate Cancer: At the Crossroads of AR, MAPK, and WNT Signaling</article-title><source>International Journal of Molecular Sciences</source><volume>21</volume><elocation-id>4507</elocation-id><pub-id pub-id-type="doi">10.3390/ijms21124507</pub-id><?supplied-pmid 32630372?><pub-id pub-id-type="pmid">32630372</pub-id></element-citation></ref><ref id="bib99"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sible</surname><given-names>JC</given-names></name>
<name><surname>Tyson</surname><given-names>JJ</given-names></name>
</person-group><year iso-8601-date="2007">2007</year><article-title>Mathematical modeling as a tool for investigating cell cycle control networks</article-title><source>Methods (San Diego, Calif.)</source><volume>41</volume><fpage>238</fpage><lpage>247</lpage><pub-id pub-id-type="doi">10.1016/j.ymeth.2006.08.003</pub-id><?supplied-pmid 17189866?><pub-id pub-id-type="pmid">17189866</pub-id></element-citation></ref><ref id="bib100"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Solly</surname><given-names>K</given-names></name>
<name><surname>Wang</surname><given-names>X</given-names></name>
<name><surname>Xu</surname><given-names>X</given-names></name>
<name><surname>Strulovici</surname><given-names>B</given-names></name>
<name><surname>Zheng</surname><given-names>W</given-names></name>
</person-group><year iso-8601-date="2004">2004</year><article-title>Application of real-time cell electronic sensing (RT-CES) technology to cell-based assays</article-title><source>Assay and Drug Development Technologies</source><volume>2</volume><fpage>363</fpage><lpage>372</lpage><pub-id pub-id-type="doi">10.1089/adt.2004.2.363</pub-id><?supplied-pmid 15357917?><pub-id pub-id-type="pmid">15357917</pub-id></element-citation></ref><ref id="bib101"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sramkoski</surname><given-names>RM</given-names></name>
<name><surname>Pretlow</surname><given-names>TG</given-names></name>
<name><surname>Giaconia</surname><given-names>JM</given-names></name>
<name><surname>Pretlow</surname><given-names>TP</given-names></name>
<name><surname>Schwartz</surname><given-names>S</given-names></name>
<name><surname>Sy</surname><given-names>MS</given-names></name>
<name><surname>Marengo</surname><given-names>SR</given-names></name>
<name><surname>Rhim</surname><given-names>JS</given-names></name>
<name><surname>Zhang</surname><given-names>D</given-names></name>
<name><surname>Jacobberger</surname><given-names>JW</given-names></name>
</person-group><year iso-8601-date="1999">1999</year><article-title>A new human prostate carcinoma cell line, 22Rv1</article-title><source>In Vitro Cellular &#x00026; Developmental Biology. Animal</source><volume>35</volume><fpage>403</fpage><lpage>409</lpage><pub-id pub-id-type="doi">10.1007/s11626-999-0115-4</pub-id><?supplied-pmid 10462204?><pub-id pub-id-type="pmid">10462204</pub-id></element-citation></ref><ref id="bib102"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>St John</surname><given-names>J</given-names></name>
<name><surname>Powell</surname><given-names>K</given-names></name>
<name><surname>Conley-Lacomb</surname><given-names>MK</given-names></name>
<name><surname>Chinni</surname><given-names>SR</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>TMPRSS2-ERG Fusion Gene Expression in Prostate Tumor Cells and Its Clinical and Biological Significance in Prostate Cancer Progression</article-title><source>Journal of Cancer Science &#x00026; Therapy</source><volume>4</volume><fpage>94</fpage><lpage>101</lpage><pub-id pub-id-type="doi">10.4172/1948-5956.1000119</pub-id><?supplied-pmid 23264855?><pub-id pub-id-type="pmid">23264855</pub-id></element-citation></ref><ref id="bib103"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Stoll</surname><given-names>G</given-names></name>
<name><surname>Viara</surname><given-names>E</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>Continuous time Boolean modeling for biological signaling: application of Gillespie algorithm</article-title><source>BMC Systems Biology</source><volume>6</volume><elocation-id>116</elocation-id><pub-id pub-id-type="doi">10.1186/1752-0509-6-116</pub-id><?supplied-pmid 22932419?><pub-id pub-id-type="pmid">22932419</pub-id></element-citation></ref><ref id="bib104"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Stoll</surname><given-names>G</given-names></name>
<name><surname>Caron</surname><given-names>B</given-names></name>
<name><surname>Viara</surname><given-names>E</given-names></name>
<name><surname>Dugourd</surname><given-names>A</given-names></name>
<name><surname>Zinovyev</surname><given-names>A</given-names></name>
<name><surname>Naldi</surname><given-names>A</given-names></name>
<name><surname>Kroemer</surname><given-names>G</given-names></name>
<name><surname>Barillot</surname><given-names>E</given-names></name>
<name><surname>Calzone</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>MaBoSS 2.0: an environment for stochastic Boolean modeling</article-title><source>Bioinformatics (Oxford, England)</source><volume>33</volume><fpage>2226</fpage><lpage>2228</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btx123</pub-id><?supplied-pmid 28881959?><pub-id pub-id-type="pmid">28881959</pub-id></element-citation></ref><ref id="bib105"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Stone</surname><given-names>KR</given-names></name>
<name><surname>Mickey</surname><given-names>DD</given-names></name>
<name><surname>Wunderli</surname><given-names>H</given-names></name>
<name><surname>Mickey</surname><given-names>GH</given-names></name>
<name><surname>Paulson</surname><given-names>DF</given-names></name>
</person-group><year iso-8601-date="1978">1978</year><article-title>Isolation of a human prostate carcinoma cell line (DU 145)</article-title><source>International Journal of Cancer</source><volume>21</volume><fpage>274</fpage><lpage>281</lpage><pub-id pub-id-type="doi">10.1002/ijc.2910210305</pub-id><?supplied-pmid 631930?><pub-id pub-id-type="pmid">631930</pub-id></element-citation></ref><ref id="bib106"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Szebeni</surname><given-names>GJ</given-names></name>
<name><surname>Bal&#x000e1;zs</surname><given-names>&#x000c1;</given-names></name>
<name><surname>Madar&#x000e1;sz</surname><given-names>I</given-names></name>
<name><surname>P&#x000f3;cz</surname><given-names>G</given-names></name>
<name><surname>Ayaydin</surname><given-names>F</given-names></name>
<name><surname>Kanizsai</surname><given-names>I</given-names></name>
<name><surname>Fajka-Boja</surname><given-names>R</given-names></name>
<name><surname>Alf&#x000f6;ldi</surname><given-names>R</given-names></name>
<name><surname>Hackler</surname><given-names>L</given-names></name>
<name><surname>Pusk&#x000e1;s</surname><given-names>LG</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Achiral Mannich-Base Curcumin Analogs Induce Unfolded Protein Response and Mitochondrial Membrane Depolarization in PANC-1 Cells</article-title><source>International Journal of Molecular Sciences</source><volume>18</volume><elocation-id>2105</elocation-id><pub-id pub-id-type="doi">10.3390/ijms18102105</pub-id><?supplied-pmid 28991167?><pub-id pub-id-type="pmid">28991167</pub-id></element-citation></ref><ref id="bib107"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Thomas</surname><given-names>R</given-names></name>
</person-group><year iso-8601-date="1973">1973</year><article-title>Boolean formalization of genetic control circuits</article-title><source>Journal of Theoretical Biology</source><volume>42</volume><fpage>563</fpage><lpage>585</lpage><pub-id pub-id-type="doi">10.1016/0022-5193(73)90247-6</pub-id><?supplied-pmid 4588055?><pub-id pub-id-type="pmid">4588055</pub-id></element-citation></ref><ref id="bib108"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Toth</surname><given-names>R</given-names></name>
<name><surname>Schiffmann</surname><given-names>H</given-names></name>
<name><surname>Hube-Magg</surname><given-names>C</given-names></name>
<name><surname>B&#x000fc;scheck</surname><given-names>F</given-names></name>
<name><surname>H&#x000f6;flmayer</surname><given-names>D</given-names></name>
<name><surname>Weidemann</surname><given-names>S</given-names></name>
<name><surname>Lebok</surname><given-names>P</given-names></name>
<name><surname>Fraune</surname><given-names>C</given-names></name>
<name><surname>Minner</surname><given-names>S</given-names></name>
<name><surname>Schlomm</surname><given-names>T</given-names></name>
<name><surname>Sauter</surname><given-names>G</given-names></name>
<name><surname>Plass</surname><given-names>C</given-names></name>
<name><surname>Assenov</surname><given-names>Y</given-names></name>
<name><surname>Simon</surname><given-names>R</given-names></name>
<name><surname>Meiners</surname><given-names>J</given-names></name>
<name><surname>Gerh&#x000e4;user</surname><given-names>C</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Random forest-based modelling to detect biomarkers for prostate cancer progression</article-title><source>Clinical Epigenetics</source><volume>11</volume><elocation-id>148</elocation-id><pub-id pub-id-type="doi">10.1186/s13148-019-0736-8</pub-id><?supplied-pmid 31640781?><pub-id pub-id-type="pmid">31640781</pub-id></element-citation></ref><ref id="bib109"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Traynard</surname><given-names>P</given-names></name>
<name><surname>Faur&#x000e9;</surname><given-names>A</given-names></name>
<name><surname>Fages</surname><given-names>F</given-names></name>
<name><surname>Thieffry</surname><given-names>D</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Logical model specification aided by model-checking techniques: application to the mammalian cell cycle regulation</article-title><source>Bioinformatics (Oxford, England)</source><volume>32</volume><fpage>i772</fpage><lpage>i780</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btw457</pub-id><?supplied-pmid 27587700?><pub-id pub-id-type="pmid">27587700</pub-id></element-citation></ref><ref id="bib110"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>T&#x000fc;rei</surname><given-names>D</given-names></name>
<name><surname>Korcsm&#x000e1;ros</surname><given-names>T</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>OmniPath: guidelines and gateway for literature-curated signaling pathway resources</article-title><source>Nature Methods</source><volume>13</volume><fpage>966</fpage><lpage>967</lpage><pub-id pub-id-type="doi">10.1038/nmeth.4077</pub-id><?supplied-pmid 27898060?><pub-id pub-id-type="pmid">27898060</pub-id></element-citation></ref><ref id="bib111"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>T&#x000fc;rei</surname><given-names>D</given-names></name>
<name><surname>Valdeolivas</surname><given-names>A</given-names></name>
<name><surname>Gul</surname><given-names>L</given-names></name>
<name><surname>Palacio-Escat</surname><given-names>N</given-names></name>
<name><surname>Klein</surname><given-names>M</given-names></name>
<name><surname>Ivanova</surname><given-names>O</given-names></name>
<name><surname>&#x000d6;lbei</surname><given-names>M</given-names></name>
<name><surname>G&#x000e1;bor</surname><given-names>A</given-names></name>
<name><surname>Theis</surname><given-names>F</given-names></name>
<name><surname>M&#x000f3;dos</surname><given-names>D</given-names></name>
<name><surname>Korcsm&#x000e1;ros</surname><given-names>T</given-names></name>
<name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2021">2021</year><article-title>Integrated intra- and intercellular signaling knowledge for multicellular omics analysis</article-title><source>Molecular Systems Biology</source><volume>17</volume><elocation-id>e9923</elocation-id><pub-id pub-id-type="doi">10.15252/msb.20209923</pub-id><?supplied-pmid 33749993?><pub-id pub-id-type="pmid">33749993</pub-id></element-citation></ref><ref id="bib112"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Tyson</surname><given-names>JJ</given-names></name>
<name><surname>Laomettachit</surname><given-names>T</given-names></name>
<name><surname>Kraikivski</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Modeling the dynamic behavior of biochemical regulatory networks</article-title><source>Journal of Theoretical Biology</source><volume>462</volume><fpage>514</fpage><lpage>527</lpage><pub-id pub-id-type="doi">10.1016/j.jtbi.2018.11.034</pub-id><?supplied-pmid 30502409?><pub-id pub-id-type="pmid">30502409</pub-id></element-citation></ref><ref id="bib113"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>V&#x000e4;remo</surname><given-names>L</given-names></name>
<name><surname>Nielsen</surname><given-names>J</given-names></name>
<name><surname>Nookaew</surname><given-names>I</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Enriching the gene set analysis of genome-wide data by incorporating directionality of gene expression and combining statistical hypotheses and methods</article-title><source>Nucleic Acids Research</source><volume>41</volume><fpage>4378</fpage><lpage>4391</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt111</pub-id><?supplied-pmid 23444143?><pub-id pub-id-type="pmid">23444143</pub-id></element-citation></ref><ref id="bib114"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Webber</surname><given-names>MM</given-names></name>
<name><surname>Bello</surname><given-names>D</given-names></name>
<name><surname>Kleinman</surname><given-names>HK</given-names></name>
<name><surname>Wartinger</surname><given-names>DD</given-names></name>
<name><surname>Williams</surname><given-names>DE</given-names></name>
<name><surname>Rhim</surname><given-names>JS</given-names></name>
</person-group><year iso-8601-date="1996">1996</year><article-title>Prostate specific antigen and androgen receptor induction and characterization of an immortalized adult human prostatic epithelial cell line</article-title><source>Carcinogenesis</source><volume>17</volume><fpage>1641</fpage><lpage>1646</lpage><pub-id pub-id-type="doi">10.1093/carcin/17.8.1641</pub-id><?supplied-pmid 8761420?><pub-id pub-id-type="pmid">8761420</pub-id></element-citation></ref><ref id="bib115"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Wishart</surname><given-names>DS</given-names></name>
<name><surname>Feunang</surname><given-names>YD</given-names></name>
<name><surname>Guo</surname><given-names>AC</given-names></name>
<name><surname>Lo</surname><given-names>EJ</given-names></name>
<name><surname>Marcu</surname><given-names>A</given-names></name>
<name><surname>Grant</surname><given-names>JR</given-names></name>
<name><surname>Sajed</surname><given-names>T</given-names></name>
<name><surname>Johnson</surname><given-names>D</given-names></name>
<name><surname>Li</surname><given-names>C</given-names></name>
<name><surname>Sayeeda</surname><given-names>Z</given-names></name>
<name><surname>Assempour</surname><given-names>N</given-names></name>
<name><surname>Iynkkaran</surname><given-names>I</given-names></name>
<name><surname>Liu</surname><given-names>Y</given-names></name>
<name><surname>Maciejewski</surname><given-names>A</given-names></name>
<name><surname>Gale</surname><given-names>N</given-names></name>
<name><surname>Wilson</surname><given-names>A</given-names></name>
<name><surname>Chin</surname><given-names>L</given-names></name>
<name><surname>Cummings</surname><given-names>R</given-names></name>
<name><surname>Le</surname><given-names>D</given-names></name>
<name><surname>Pon</surname><given-names>A</given-names></name>
<name><surname>Knox</surname><given-names>C</given-names></name>
<name><surname>Wilson</surname><given-names>M</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>DrugBank 5.0: a major update to the DrugBank database for 2018</article-title><source>Nucleic Acids Research</source><volume>46</volume><fpage>D1074</fpage><lpage>D1082</lpage><pub-id pub-id-type="doi">10.1093/nar/gkx1037</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="bib116"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Yang</surname><given-names>W</given-names></name>
<name><surname>Freeman</surname><given-names>MR</given-names></name>
<name><surname>Kyprianou</surname><given-names>N</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>Personalization of prostate cancer therapy through phosphoproteomics</article-title><source>Nature Reviews. Urology</source><volume>15</volume><fpage>483</fpage><lpage>497</lpage><pub-id pub-id-type="doi">10.1038/s41585-018-0014-0</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="bib117"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Zhan</surname><given-names>M</given-names></name>
<name><surname>Deng</surname><given-names>Y</given-names></name>
<name><surname>Zhao</surname><given-names>L</given-names></name>
<name><surname>Yan</surname><given-names>G</given-names></name>
<name><surname>Wang</surname><given-names>F</given-names></name>
<name><surname>Tian</surname><given-names>Y</given-names></name>
<name><surname>Zhang</surname><given-names>L</given-names></name>
<name><surname>Jiang</surname><given-names>H</given-names></name>
<name><surname>Chen</surname><given-names>Y</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Design, Synthesis, and Biological Evaluation of Dimorpholine Substituted Thienopyrimidines as Potential Class I PI3K/mTOR Dual Inhibitors</article-title><source>Journal of Medicinal Chemistry</source><volume>60</volume><fpage>4023</fpage><lpage>4035</lpage><pub-id pub-id-type="doi">10.1021/acs.jmedchem.7b00357</pub-id><?supplied-pmid 28409639?><pub-id pub-id-type="pmid">28409639</pub-id></element-citation></ref></ref-list><app-group><app id="appendix-1"><title>Appendix 1</title><sec sec-type="appendix" id="s8"><title>1. Prostate Boolean model construction</title><p>Building the model is done in three steps:</p><list list-type="order"><list-item><p>Identifying signalling pathways or particular genes and proteins that are especially relevant to describe the prostate cancer tumorigenesis and tumour growth. Most of them are components that are known to be frequently altered in cancers.</p></list-item><list-item><p>Building a regulatory network that includes simplified representations of pathways identified as relevant for prostate cancer, as well as all individually identified genes. Each pathway is characterised by the key players that regulate it. This network takes the form of a directed graph for which positive and negative influences between components are represented.</p></list-item><list-item><p>From this network, a logical model is derived describing the network dynamics in specific contexts (dependent on initial conditions or perturbations). To this end, logical rules are associated with each node of the network to indicate how it is activated or inhibited by different combinations of its regulators.</p></list-item></list><sec sec-type="appendix" id="s8-1"><title>1.1. Prior knowledge network construction</title><p>We started by using a published logical model of human signalling network (<xref rid="bib39" ref-type="bibr">Fumi&#x000e3; and Martins, 2013</xref>), which is based on integrated experimental evidence of signal transduction. This model integrates major signalling pathways that have a role in regulating cell death and proliferation in many tumours. They include those involving receptor tyrosine kinase (RTKs), phosphatidylinosital 3-kinase (PI3K)/AKT, WNT/&#x003b2;-Catenin, transforming growth factor-b (TGF-&#x003b2;)/Smads, cyclins, retinoblastoma protein (Rb), hypoxia-inducible transcription factor (HIF-1), p53 and ataxia-telangiectasia mutated (ATM)/ataxia-telangiectasia and Rad3-related (ATR) protein kinases. The pathways reveal substantial cross-talks.</p><p>This initial generic network was then extended to include prostate cancer-specific genes and proteins using several approaches presented below.</p><sec sec-type="appendix" id="s8-1-1"><title>1.1.1. Definition of inputs and outputs</title><p>Our Boolean model aims at predicting prostate phenotypic behaviours for healthy and cancer cells in different &#x02018;&#x02018;physiological&#x02019;&#x02019; conditions. To account for these conditions, we considered nine inputs that represent different physiological conditions of interest. These are <italic toggle="yes">EGF, FGF, TGF beta, Nutrients, Hypoxia, Acidosis androgen, TNF alpha, and Carcinogen</italic> presence. These input nodes have no regulation and their values are fixed for each simulation, representing the cell&#x02019;s microenvironmental characteristics.</p><p>For simplicity, we choose to clearly define phenotype variables as output nodes allowing the integration of multiple phenotypic signals and obtaining a 0/1 value for each phenotype. Our model has a total of 11 outputs. We define three main phenotypes representing the growing status of the cell: <italic toggle="yes">Proliferation</italic>, <italic toggle="yes">Apoptosis,</italic> and <italic toggle="yes">Quiescence. Apoptosis</italic> is activated by Caspase 8 or Caspase 9, while <italic toggle="yes">Proliferation</italic> is activated by cyclins from the cell cycle. We define <italic toggle="yes">Quiescence</italic> as the absence of <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis</italic> and these two, although not directly linked, are always mutually exclusive in simulations.</p><p>The proliferation output is sometimes described in already published models as specific stationary protein activation patterns, namely the following sequence of activation of cyclins: Cyclin D, then Cyclin E, then Cyclin A, then Cyclin B. This sequence can easily be detected in complex attractors in synchronous dynamics. However, since asynchronous dynamics was chosen for this work and it is more difficult to analyse complex attractors with it, we define <italic toggle="yes">Proliferation</italic> as activated by either of the four cyclins. Transient dynamics in MaBoSS simulations allow us to check the correct oscillation of cyclins.</p><p>Furthermore, we define several phenotypic outputs that are not mutually exclusive but detect the activation of some markers of cancer hallmarks: <italic toggle="yes">Invasion, Migration,</italic> (bone) <italic toggle="yes">Metastasis,</italic> and <italic toggle="yes">DNA repair</italic>.</p></sec><sec sec-type="appendix" id="s8-1-2"><title>1.1.2. Identification of new components based on literature search:</title><p>Several studies have focused on identifying main subtypes among the heterogeneous molecular abnormalities in prostate cancer. In particular, a TCGA study (<xref rid="bib15" ref-type="bibr">Cancer Genome Atlas Research Network, 2015</xref>) reported a comprehensive molecular analysis of 333 primary prostate carcinomas. Seven subtypes, containing 74% of these tumours, were defined by specific gene fusions (ERG, ETV1/4, and FLI1) or mutations (SPOP, FOXA1, and IDH1). Epigenetic profiles allowed us to identify a methylator phenotype in the IDH1 mutant subset. SPOP and FOXA1 mutant tumours show the highest levels of AR-induced transcripts. Lesions in the PI3K or MAPK signalling pathways are observed in 25% of the prostate cancers and DNA repair genes inactivation in 19%.</p><p>The following list of frequently mutated genes extracted from this study indicate components that could be included in the model, provided that enough information is available on their mechanistic roles:</p><list list-type="bullet"><list-item><p>gene fusions: ERG, ETV1, ETV4, FLI1</p></list-item><list-item><p>deletions: SPOP, FOXA1, IDH1, TP53, PTEN, PIK3CA, BRAF, CTNNB1, HRAS, MED12, ATM, CDKN1B, RB1, NKX3-1, AKT1, ZMYM3, KMT2C, KMT2D, ZNF595, CHD1, BRCA2, CDK12, SPINK1</p></list-item><list-item><p>amplifications: CCND1, MYC, FGFR1, WHSC1L1.</p></list-item></list><p>Comparing with a published cohort of 150 castration-resistant metastatic prostate cancer samples (<xref rid="bib90" ref-type="bibr">Robinson et al., 2015</xref>), the authors find a similar subtype distribution as in <xref rid="bib15" ref-type="bibr">Cancer Genome Atlas Research Network, 2015</xref>, with increased alteration rates in the metastatic samples and more frequent amplification or mutation of AR, as well as DNA repair and PI3K pathway alterations.</p><p>Other studies such as (<xref rid="bib5" ref-type="bibr">Altieri et al., 2009</xref>) focus on the role of specific pathways which play a critical role in prostate cancer maintenance, such as chaperone-mediated mitochondrial homeostasis (in particular with HSP90 found very abundant in prostate cancer), integrin-dependent cell signalling and RUNX2-regulated gene expression in the metastatic bone microenvironment.</p><p>Notably, a set of regulatory maps of signalling pathway maps and altered circuitries of various cell biological events associated with the pathogenesis of human prostate cancer have been published recently (<xref rid="bib28" ref-type="bibr">Datta et al., 2016</xref>). The authors manually constructed networks based on the literature. These networks constitute an important resource for retrieving information on prostate cancer specific components. Although not exhaustive, these maps are synthetic pictures of the existing knowledge on molecular events involved in prostate cancer hallmarks.</p><p>The covered hallmarks include: (1) classical cancer hallmarks: insensitivity to anti-growth signal, self-sufficiency in growth signal, tumour promoting inflammation, genome instability, mutation and perturbation, angiogenesis, metastasis, cell death resistance, metabolic reprogramming, avoidance of immune destruction, enabling replicative immortality, tumour microenvironment; and (2) prostate cancer specific hallmarks: androgen receptor signalling androgen independence, castration resistance.</p><p>This study points toward some candidate nodes to extend our network in order to take into account, at least in a simplified way, most pathways present in the maps. In particular, it shows that the initial network obtained through combinations of published models ignore any pathways related to inflammation, metabolism, immune evasion, or the tumour microenvironment. However, the resource contains few mechanistic details for the interactions between its components, which are a mix of genes, proteins, molecules, processes and phenotypes.</p><p>Finally, among all these genes associated with prostate cancer, a subset has been chosen for further research: AR, PTEN, SPOP, TP53, EZH2, FOXA1, BRCA1, BRCA2, PIK3CA, AKT1, NCOA2, NCOR1, NCOR2, EP300, MYC, RB1, CHD1, CDKN1B, MED12, ZNF595, HOXB13.</p></sec><sec sec-type="appendix" id="s8-1-3"><title>1.1.3. Identification of new components/pathways based on data analysis</title><p>ROMA (<xref rid="bib74" ref-type="bibr">Martignetti et al., 2016</xref>) is a software package written in Java for the quantification and representation of biological module activity using expression data. It uses the first principal component of a PCA analysis to summarise the coexpression of a group of genes in the gene set.</p><p>We apply ROMA analysis on the transcriptomics data of TCGA. We define gene sets as they are described in the atlas of cancer signalling networks, ACSN (<xref rid="bib65" ref-type="bibr">Kuperstein et al., 2015</xref>) (<ext-link xlink:href="http://www.acsn.curie.fr/" ext-link-type="uri">http://www.acsn.curie.fr/</ext-link>) and in the Hallmarks (<xref rid="bib70" ref-type="bibr">Liberzon et al., 2015</xref>). ACSN is centred on signalling pathways such as DNA repair, cell death, EMT, cell adhesion, cell cycle, etc. and the Hallmarks gene sets provide a list of genes that participate in biological processes integrating information from other pathway databases.</p><p>Using ROMA, we are able to identify some pathways significantly overdispersed over the samples that should have relevant roles in prostate cancer and need therefore to be correctly described in the model.</p><p>The results show that, for ACSN database, among the 140 pathways from the database, 65 modules reveal a high variance of protein expression across all samples (<xref rid="app1fig1" ref-type="fig">Appendix 1&#x02014;figure 1</xref>). The gene sets linked to the cell cycle seem to show a progressive activation from normal to high grade tumours, so does the DNA repair pathway with differences in the mechanisms that participate in DNA repair, whereas some gene sets such as the one related to immunosuppressive cytokine pathway show opposite behaviour. We performed the same analysis with the Hallmarks database and found 16 out of the 50 pathways that showed high variance. We can confirm the role of the cell cycle in tumour progression (E2F_targets and G2_M checkpoints).</p><p>Note that in both analyses, we see that group three is the most heterogeneous group, with a score that does not always follow the trend of increasing or decreasing pathway scores from group 1 to group 5.</p><p>ROMA provides some hints on where to extend the network to fully grab the alterations that are found in prostate cancer patients. For instance, the Hedgehog pathway was not described in the already published logical models that we used as a starting point of this model. Moreover, both the cell cycle and the DNA repair pathways were overly simplified, and were thus extended in this version.</p><p>Some pathways related to the immune response seem to be highly represented in ACSN and would need to be included in future extended versions of the prostate network, probably in the form of interacting networks of different cell types.</p></sec><sec sec-type="appendix" id="s8-1-4"><title>1.1.4. Model extension with Omnipath via pypath</title><p>OmniPath (<xref rid="bib110" ref-type="bibr">T&#x000fc;rei et al., 2016</xref>; <xref rid="bib111" ref-type="bibr">T&#x000fc;rei et al., 2021</xref>) is a comprehensive collection of high confidence, literature curated, human signalling pathways. It is accompanied and developed together with Pypath, a Python module for cellular signalling pathways analysis.</p><p>Pypath is a python module used to query the content of Omnipath in order to retrieve components and interactions in the human protein-protein signalling network associated with annotations, especially sources, literature references, direction, effect signs (stimulation/inhibition), and enzyme-substrate interactions.</p><p>The development of pypath allows us to build personalised queries. For instance, existing interaction paths between a protein of interest and a list of user-defined proteins can be found, with a given size for the paths. We use this in the extension process of our network to automatically find new interactions between a new gene and the genes already included in the network. We filter the interactions found to select the ones for which the direction and sign are known.</p><p>For example, when extending the network with the chaperone protein HSP90AA1, we generate the graph displayed in <xref rid="app1fig2" ref-type="fig">Appendix 1&#x02014;figure 2</xref>, which shows all signed directed interactions linking HSP90AA1 to the network. The associated references given as annotations are useful to check the mechanism behind each interaction and manually infer a logical rule.</p></sec><sec sec-type="appendix" id="s8-1-5"><title>1.1.5. Model extension with the literature</title><p>Protein-protein interactions (PPI) and signalling databases are useful to find quickly established interactions between genes and proteins. However, they are not exhaustive and in particular they often lack recent findings. It is therefore necessary to rely on manual literature search to find information on specific prostate cancer components.</p><p>The roles of the fusion gene TMPRSS2:ERG and the tumour suppressor NKX3-1 are examples where the information from databases retrieved from Omnipath or PPI databases is lacking, and for which we found additional information from the literature.</p><p>Fusion genes are frequently found in human prostate cancer and have been identified as a specific subtype marker (<xref rid="bib15" ref-type="bibr">Cancer Genome Atlas Research Network, 2015</xref>). The most frequent is TMPRSS2:ERG. It involves the transcription factor ERG, which leads to cell-cycle progression. ERG fuses with the AR-regulated TMPRSS2 gene promoter to form an oncogenic fusion gene that is especially common in hormone-refractory prostate cancer, conferring androgen responsiveness to ERG. This fusion is not found with Pypath, nor is any target of ERG (<xref rid="app1fig3" ref-type="fig">Appendix 1&#x02014;figure 3A</xref>). However, literature search reveals that ERG directly regulates EZH2, oncogene c-Myc and tumour suppressor NKX3-1 and many other targets in prostate cancer (<xref rid="bib64" ref-type="bibr">Kunderfranco et al., 2010</xref>).</p><p>We model the gene fusion with an activation of ERG by the decoupling of ERG in a special node AR_ERG that is only activated by the AR &#x00026; fused_event node. In the healthy case, fused_event (that represents TMPRSS2) is fixed to 0 or inactive. The occurrence of the gene fusion is represented with the model perturbation where fused_event is fixed to 1. Moreover, ERG expression has a major impact on cell invasion and epithelial-mesenchymal transition (EMT) through the upregulation of the FZD4 gene, a member of the frizzled family of receptors. In our model, we choose for simplicity to consider ERG as a marker of EMT, with a direct activation of the output node EMT by ERG (<xref rid="bib3" ref-type="bibr">Adamo and Ladomery, 2016</xref>).</p><p>NKX3-1 has been identified as a tumour suppressor for prostate cancer. Since it is frequently mutated, it should be included in the model. Some of its regulations can be found with Pypath (<xref rid="app1fig3" ref-type="fig">Appendix 1&#x02014;figure 3B</xref>), in particular its activation by AR and PKC. However, its role is not identified. The literature search highlighted its role in accelerating the DNA repair response and in particular in avoiding the gene fusion TMPRSS2:ERG. NKX3-1 binds to AR at the ERG gene breakpoint and inhibits both the juxtaposition of the TMPRSS2 and ERG gene loci and also their recombination, by influencing the recruitment of proteins that promote homology-directed DNA repair. Thus, loss of NKX3-1 favours recruitment to the ERG gene breakpoint of proteins that promote error-prone non-homologous end-joining (<xref rid="bib12" ref-type="bibr">Bowen et al., 2015</xref>).</p><p>We therefore add the absence of the node NKX3-1 as a new requirement for the activation of ERG by AR and TMPRSS2 in the model. The effect of the gene fusion can be seen in combination with the perturbation that maintains NKX3-1 to the null level.</p><p>In contrast with these examples where some knowledge can be retrieved from the literature, some new nodes cannot be included in the model in a satisfactory manner, because of missing information about their regulation or role. High-throughput studies have allowed us to identify genes with mutations or expression levels associated with prostate cancer progression or prognosis. Nevertheless, for many of them, the precise mechanisms behind this association remains to be elucidated.</p><p>For example, IDH1 (isocitrate dehydrogenase 1) exhibits a recurrent mutation in 1% of primary prostate cancers that defines a specific subtype (<xref rid="bib15" ref-type="bibr">Cancer Genome Atlas Research Network, 2015</xref>). This mutant status is associated with a DNA hypermethylation phenotype. Despite a lack of detailed mechanisms linking this gene to the regulation network, we can still reflect a candidate association in the model by including IDH1 as regulated by mTOR and MEK1_2, whose absence (level 0) induces the activation of the output node <italic toggle="yes">Hypermethylation</italic>. The regulation of both new nodes IDH1 and Hypermethylation should be refined when new knowledge is found.</p><p>In some cases, we cannot provide any link for a new node, either to an existing node or to a phenotypic output, even qualitatively. For example, ZNF595 has been linked to prostate cancer progression. However, this gene encodes a protein belonging to the Cys2His2 zinc finger protein family, whose members function as transcription factors that can regulate a broad variety of developmental and cellular processes. This knowledge is not detailed enough to add this node in the model yet. However, future mutation data from prostate cancer samples, associated with clinical data, will allow us to test several hypotheses.</p><p>This model includes several signalling pathways as well as the substantial cross-talks among them. These pathways range from receptors such as receptor tyrosine kinase (RTKs), androgen receptor (AR) and growth factors pathways (EGF, FGF, TGF-&#x003b2;); downstream gene regulation pathways such as phosphatidylinositol 3-kinase (PI3K)/AKT, Wnt/&#x003b2;-Catenin, NFkB, MAPK, mTOR, SHH, MYC, ETS1, p53, hypoxia-inducible transcription factor (HIF-1) and Smad pathways; cell cycle descriptions with cyclins, E2F1, retinoblastoma protein (Rb) and p21; epithelial-mesenchymal transition (EMT) and migration-related genes; DNA damage and apoptosis-related genes; as well as prostate cancer characteristic genes such as p53, ataxia-telangiectasia mutated (ATM)/ataxia-telangiectasia and Rad3-related (ATR) protein kinases, NKX3.1, TMPRSS2 and TMPRSS2:ERG fusion.</p><p>A complete list of the references for all the nodes and edges included in the model can be found in the XLS file of <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref>.</p></sec></sec><sec sec-type="appendix" id="s8-2"><title>1.2 Boolean model construction</title><sec sec-type="appendix" id="s8-2-1"><title>1.2.1. Primer on Boolean modelling</title><p>Boolean models are based on the logical formalism that relies on a regulatory graph and a list of logical rules associated with each of the nodes of the graph. We hereby present a small introduction of the principal terms of this modelling. For further information, we refer readers to other works (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>; <xref rid="bib92" ref-type="bibr">Saadatpour and Albert, 2013</xref>; <xref rid="bib2" ref-type="bibr">Abou-Jaoud&#x000e9; et al., 2016</xref>).</p><p>The aforementioned prior knowledge network is composed of nodes and edges, where nodes correspond to entities (e.g. genes, proteins, complexes, phenotypes or processes) and edges to influences, either positive or negative, which illustrate the possible interactions between two entities. Such regulatory networks are easily translatable to Boolean models. A node that has no regulator is denoted as <italic toggle="yes">input</italic> and a node that does not regulate another node is denoted as <italic toggle="yes">output. Input</italic> represent different physiological initial conditions and <italic toggle="yes">outputs</italic> represent biological read-outs.</p><p>Each node of the regulatory network has a corresponding Boolean variable associated that can take two values: 0 for inactive or OFF, and 1 for active or ON. These variables change their value according to a logical rule assigned to them. The state of a variable will thus depend on its logical rule, which is based on logical statements: a function of the node regulators linked with logical connectors AND, OR and NOT. More on this in Section 1.2.2 &#x0201c;Establishing the rules of the Boolean model&#x0201d;.</p><p>These operators can account for what is known about the biology behind these edges. If two input nodes are needed for the activation of the target node, they will be linked by an AND gate; to list different means of activation of a node, an OR gate will be used. For negative influences, a NOT gate will be utilised.</p><p>Finally, the state transition graph (STG) is another network that recapitulates all the states of the nodes and the possible transitions from one model state to another depending on the logical rules. The form of the graph will depend on the updating strategy chosen -either all nodes are updated at once or nodes are updated one at a time. In addition, the state transition graph informs on the existence of the two types of attractors of the model: stable steady states or limit cycles. More on this in Section 1.2.3 &#x0201c;State transition graph and the update mechanism&#x0201d;.</p></sec><sec sec-type="appendix" id="s8-2-2"><title>1.2.2. Establishing the rules of the Boolean model</title><p>When building our regulatory graph, there were many instances of concurrent activation and inhibition of a node. As a general rule, and unless evidence was found for the contrary, we decided to add the activators with OR gates and the inhibitors with AND NOT.</p><p>Usually the OR links activators from two different pieces of information extracted from different articles. For the inhibitors, the AND NOT allows to take into account their effect and overrule the activators.</p><p>This is an assumption that we make as a first try and when we have no further knowledge. If there is evidence that one of the activators is not affected when an inhibitor is present, then we adapt the logical formulas accordingly. For instance, if we know that two inhibitors only inhibit when both are present, we include that information and overwrite the previous formula.</p><p>Some of the possible combinations that we may find in Boolean models can be found in the following toy model (<xref rid="app1fig4" ref-type="fig">Appendix 1&#x02014;figure 4</xref>). Node D and E are self-regulated, meaning they are inputs: their initial value will rule their activation. Node A can be activated by B and any combination of C and/or E. Node B is activated if D is not present and when A or C are present. C is activated by A and only when D and E are both not present. This means that C can still be activated when A and D are present, or A and E, but not D and E.</p></sec><sec sec-type="appendix" id="s8-2-3"><title>1.2.3. State transition graph and the update mechanism</title><p>In a Boolean framework, the variables associated to each node can take two values, either 0 or 1. We define a model state as a vector of all node states. All the possible transitions from any model state to another are dependent on the set of logical rules that define the model.</p><p>These transitions can be viewed into a graph called a state transition graph, or STG, where nodes are model states and edges are the transitions from one model state to another.</p><p>The resulting dynamics of the Boolean model can be represented in terms of a state transition graph (STG), where the nodes denote the states of the system (i.e. vectors giving the levels of activity of all the variables) and the arcs represent state transitions (i.e. changes in variable values, according to the corresponding logical functions). This way, trajectories from an initial condition to all the final states can be determined. The STG can contain up to <italic toggle="yes">2</italic><sup><italic toggle="yes">n</italic></sup> model state nodes; thus, if <italic toggle="yes">n</italic> is too big, the construction and the visualisation of the graph becomes resource consuming.</p><p>The attractors of the model are the long-term asymptotic behaviours of the system. We have two types: stable states, when the system has reached a model state whose successor in the transition graph is itself; and cyclic attractors, when trajectories in the transition graph lead to a group of model states that are cycling. For more details, see <xref rid="bib18" ref-type="bibr">Chaouiya et al., 2012</xref>; <xref rid="bib2" ref-type="bibr">Abou-Jaoud&#x000e9; et al., 2016</xref>.</p><p>When concurrent variable changes are enabled at a given state, the resulting state transition depends on the chosen updating assumptions. Numerous studies use the fully synchronous strategy where all variables are updated through a unique transition. This assumption leads to relatively simple transition graphs and deterministic dynamics. The proportion of initial conditions leading to given attractors is measured as the attractor landscape (<xref rid="bib48" ref-type="bibr">Helikar et al., 2008</xref>; <xref rid="bib39" ref-type="bibr">Fumi&#x000e3; and Martins, 2013</xref>; <xref rid="bib21" ref-type="bibr">Cho et al., 2016</xref>). However, the synchronous updating assumption approximation often leads to spurious cyclic attractors. On the other hand, the fully asynchronous updating assumption considers separately all possible transitions and therefore allows the consideration of alternative dynamics in the absence of kinetic data. The resulting dynamics has a branching structure which makes it more difficult to evaluate. In this project, we consider asynchronous dynamics mixed with stochastic simulations.</p><p>The regulatory graph was constructed using GINsim software (<xref rid="bib18" ref-type="bibr">Chaouiya et al., 2012</xref>) and then exported in a format readable by MaBoSS software (see below) in order to perform stochastic simulations on the Boolean model.</p><p>The final model accounts for 133 nodes and 449 edges (<xref rid="app1fig1" ref-type="fig">Appendix 1&#x02014;figure 1</xref> and <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref>) and includes pathways such as androgen receptor and growth factor signalling, different signalling pathways (Wnt, NFkB, PI3K/AKT, MAPK, mTOR, SHH), cell cycle, epithelial-mesenchymal transition (EMT), Apoptosis, DNA damage, etc. This model has nine inputs (<italic toggle="yes">EGF, FGF, TGF beta, Nutrients, Hypoxia, Acidosis, Androgen, TNF alpha</italic> and <italic toggle="yes">Carcinogen</italic> presence) and six outputs (<italic toggle="yes">Proliferation</italic>, <italic toggle="yes">Apoptosis, Invasion, Migration,</italic> (bone) <italic toggle="yes">Metastasis</italic> and <italic toggle="yes">DNA repair</italic>).</p></sec></sec></sec><sec sec-type="appendix" id="s9"><title>2. Boolean model simulation</title><sec sec-type="appendix" id="s9-1"><title>2.1. Primer on MaBoSS methodology</title><p>In the present study, all simulations have been performed with MaBoSS that stands for Markovian Boolean Stochastic Simulator. We hereby present a small introduction of the MaBoSS simulations. For further information, we refer readers to other works (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>; <xref rid="bib103" ref-type="bibr">Stoll et al., 2012</xref>; <xref rid="bib104" ref-type="bibr">Stoll et al., 2017</xref>).</p><p>This framework is based on an asynchronous update scheme combined with a continuous time feature obtained with the Gillespie algorithm (<xref rid="bib41" ref-type="bibr">Gillespie, 1976</xref>), allowing simulations to be continuous in time. This algorithm is particularly useful when the state transition graph is too big, as it allows to stochastically sample trajectories from a given initial condition to all possible asymptotic solutions and associate a probability to each model state and final stable states.</p><p>Gillespie algorithm provides a stochastic way to choose a specific transition among several possible ones and to infer a corresponding time for this transition. Thus, MaBoSS computation results in one stochastic trajectory as a function of time when objective transition rates, seen as qualitative activation or inactivation rates, are specified for each node. These transition rates can be set either all to the same value by default or in various levels reflecting different orders of magnitude of biological processes&#x02019; time or due to difference among different patients&#x02019; omics datasets (See Section 3.1 &#x0201c;Primer on PROFILE methodology&#x0201d;). These transition rates are translated as transition probabilities in order to determine the actual transition. All in all, this modelling framework is at the intersection of logical modelling and continuous dynamic modelling.</p><p>Since MaBoSS computes stochastic trajectories, it is highly relevant to compute several trajectories to get an insight of their average behaviour. In present work, all simulations have consisted on the average of 5,000 computed trajectories.</p><p>To capture the gradual inhibition of drugs (Section 7.3), we have taken advantage of the simulation of a population of trajectories, so initial values of each node can be defined with a continuous value between 0 and 1 representing the probability for the node to be defined to one for each new trajectory. For instance, a node with a 0.7 initial condition will be set to 1 in 70% of simulated trajectories and to 0 in 30% of the trajectories.</p></sec><sec sec-type="appendix" id="s9-2"><title>2.2. Wild type simulation</title><p>Our prostate Boolean model recapitulates known phenotypes of prostate cells by stochastic simulations in each of the studied &#x0201c;physiological&#x0201d; conditions. The model can be considered as a model of healthy prostate cells when no mutants or fused genes are present, called wild type model in present work. These healthy cells mostly exhibit quiescence in absence of any input. Because the initial conditions of all components of the model are set to random values and input nodes are OFF, there is a possibility to activate transiently the pathways but not to maintain them, and all pathways are eventually turned off.</p><p>Our prostate Boolean model was simulated using MaBoSS and asynchronous updates and recapitulates known phenotypes of prostate cells under physiological conditions (Main text, <xref rid="app1fig2" ref-type="fig">Appendix 1&#x02014;figure 2</xref>). Model states distribution at the end of the simulation with growth factors, <italic toggle="yes">Nutrients</italic> and <italic toggle="yes">Androgen</italic> as inputs can be seen in <xref rid="app1fig2" ref-type="fig">Appendix 1&#x02014;figure 2B</xref>. Note that some outputs are not mutually exclusive, therefore the presence of cells with <italic toggle="yes">Invasion</italic> and <italic toggle="yes">Proliferation</italic>. In <xref rid="app1fig2" ref-type="fig">Appendix 1&#x02014;figure 2C</xref>, the same model with cell death factors ON.</p><p>In proliferating conditions, transient probabilities of the cyclins can be used to check that the order of activations of these nodes in the paths leading to the cyclic attractor is consistent with a proper cell cycle progression (<xref rid="app1fig5" ref-type="fig">Appendix 1&#x02014;figure 5</xref>).</p><p>These analyses can be performed using model files from <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref> and the jupyter notebook from <xref rid="supp2" ref-type="supplementary-material">Supplementary file 2</xref>.</p></sec><sec sec-type="appendix" id="s9-3"><title>2.3. Mutants simulation</title><p>A mutant in the logical framework is simulated by setting the node corresponding to the gene mutated to 0 in the case of loss of function and to 1 in the case of gain of function. The effect of a mutation is assessed, like the change of initial conditions, by comparing the mutant&#x02019;s probabilities of reaching a phenotype with respect to the wild type model. Therefore, mutations change the model phenotypes: <italic toggle="yes">Apoptosis, Proliferation, Invasion, Migration,</italic> (bone) <italic toggle="yes">Metastasis</italic> and <italic toggle="yes">DNA repair</italic>.</p><sec sec-type="appendix" id="s9-3-1"><title>2.3.1. Single mutations</title><p>The single mutations of some of the main nodes of the network show some changes in the probabilities of reaching the phenotypes when compared to wild type conditions.</p><p>The examples on <xref rid="app1fig6" ref-type="fig">Appendix 1&#x02014;figure 6</xref> show that a loss-of-function mutation of FOXA1 in proliferative conditions (nutrients and growth factors) results in the activation of migration and invasion but not metastasis. A loss-of-function mutation of TP53 in the same condition with the addition of carcinogen does not lead to loss of the apoptosis induced by DNA damage because of the activation of caspase three pathway.</p></sec><sec sec-type="appendix" id="s9-3-2"><title>2.3.2. Multiple mutations</title><p>Cancer progression is characterised by the accumulation of genetic alterations that affect multiple pathways in the signalling network. The logical model allows to easily simulate all possible combinations of mutations and study the potential redundancy or synergy of alteration effects and the importance of order. An example of double mutation is shown in <xref rid="app1fig7" ref-type="fig">Appendix 1&#x02014;figure 7</xref>, where the combination of the gene fusion TMPRSS2:ERG and the loss-of-function of NKX3-1 activates bone metastasis signals in proliferative conditions with androgen induction.</p><p>The model allows to study easily all possible associations of mutations to assess synergies or redundancies. It can also reproduce sets of mutations observed in tumours. Different sequences of possible acquired mutations can be simulated and compared to what is already known about patients harbouring these mutations.</p></sec></sec></sec><sec sec-type="appendix" id="s10"><title>3. Personalisation of Boolean models</title><sec sec-type="appendix" id="s10-1"><title>3.1. Primer on PROFILE methodology</title><p>We give here an intuitive idea of how the personalization is done with PROFILE for both discrete data (mutation and copy number alteration data) and continuous data (RNAseq and/or proteomics data when available). For more thorough details on the methodology, readers can refer to <xref rid="app1fig8" ref-type="fig">Appendix 1&#x02014;figure 8</xref> and the work described in <xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>.</p><p>For discrete data: if the mutation is an activating mutation, the corresponding node will be set to 1; if the mutation is an inhibiting mutation, the corresponding node will be set to 0.</p><p>For continuous data, the data is normalised at first. Then, depending on the expression of the gene compared to others, the corresponding transition rate will be set to a high value if it is higher and to a low value if it is lower. A high transition rate will be favoured when travelling through the state transition graph. Initial values for these genes are also set accordingly. This personalisation can be observed in the CFG file for the LNCaP cell line (<xref rid="app1table1" ref-type="table">Appendix 1&#x02014;table 1</xref>). The full file is available in the <xref rid="supp5" ref-type="supplementary-material">Supplementary file 5</xref>.</p><table-wrap position="anchor" id="app1table1"><label>Appendix 1&#x02014;table 1.</label><caption><title>Excerpt of the CFG file of the personalised LNCaP Boolean model.</title></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" valign="bottom" rowspan="1" colspan="1">Transition rates for LNCaP personalised model</th><th align="left" valign="bottom" rowspan="1" colspan="1">Initial conditions for LNCaP personalised model</th></tr></thead><tbody><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_Acidosis = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Acidosis].istate = 0.5[1], 0.5 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_Acidosis = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Androgen].istate = 0.5[1], 0.5 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_AKT = 1.15285;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Carcinogen].istate = 0.5[1], 0.5 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_AKT = 0.86742;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Hypoxia].istate = 0.5[1], 0.5 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_AMP_ATP = 0.06407;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Nutrients].istate = 0.5[1], 0.5 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_AMP_ATP = 15.60793;</td><td align="left" valign="middle" rowspan="1" colspan="1">[AKT].istate = 0.51544[1], 0.48456 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_AMPK = 0;</td><td align="left" valign="middle" rowspan="1" colspan="1">[AMP_ATP].istate = 0.20167[1], 0.79833 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_AMPK = 0.91263;</td><td align="left" valign="middle" rowspan="1" colspan="1">[ATR].istate = 0.32278[1], 0.677219 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_Androgen = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[AXIN1].istate = 0.38829[1], 0.61171 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_Androgen = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[BAD].istate = 0.65311[1], 0.34689 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_Angiogenesis = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Bak].istate = 0.32278[1], 0.677219 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_Angiogenesis = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Bcl_XL].istate = 0.36264[1], 0.637359 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_Apoptosis = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[BCL2].istate = 1e-05[1], 0.99999 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_Apoptosis = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[BIRC5].istate = 0.34426[1], 0.65574 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_AR = 100.0;</td><td align="left" valign="middle" rowspan="1" colspan="1">[BRCA1].istate = 0.42294[1], 0.57706 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_AR = 0;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Caspase8].istate = 0.21981[1], 0.780189 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_AR_ERG = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[Caspase9].istate = 0.32278[1], 0.677219 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_AR_ERG = 1;</td><td align="left" valign="middle" rowspan="1" colspan="1">[CDH2].istate = 0.0[1], 1.0 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$u_ATM = 0;</td><td align="left" valign="middle" rowspan="1" colspan="1">[cFLAR].istate = 0.5[1], 0.5 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">$d_ATM = 5.81395;</td><td align="left" valign="middle" rowspan="1" colspan="1">[CyclinB].istate = 0.23353[1], 0.76647 [0];</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">&#x02026;</td><td align="left" valign="middle" rowspan="1" colspan="1">...</td></tr></tbody></table></table-wrap></sec><sec sec-type="appendix" id="s10-2"><title>3.2. Differences of PROFILE with the state of the art</title><p>Personalised models should be able to capture heterogeneity among cancer cell lines, cells of a tumour and cells from different patients. Until now, personalisation of models has used in vitro perturbation experiments, as studying this kind of cell-level heterogeneity between patients&#x02019; responses to treatments is complicated in vivo. In vitro studies such as the ones from <xref rid="bib93" ref-type="bibr">Saez-Rodriguez et al., 2009</xref> and <xref rid="bib30" ref-type="bibr">Dorier et al., 2016</xref> showed how perturbation data could be used to capture differences in the models of different cell lines and patients.</p><p>Moreover, in vitro perturbation results are best when researchers can isolate the cells from their surrounding environment and study a small set of them, as happens with microfluidics techniques. <xref rid="bib32" ref-type="bibr">Eduati et al., 2020</xref> showed a procedure in which cells from two cell lines and four biopsies were tested against a panel of 8 drugs and their combinations. These drug responses were then used to personalise a generic model.</p><p>Our PROFILE methodology does not use in vitro perturbation experiments, but rather bulk omics data. We are capable of having results specific for each cell line and patient without the need of in vitro testing. The perturbation data does not lack any kind of information to have these personalised models, but we consider that being able to personalise models without needing further experimentation is an asset of our method. In any case, note that the present PROFILE_v2 methodology and perturbation tools as the ones above are compatible and complementary as they use different kinds of data as inputs.</p></sec></sec><sec sec-type="appendix" id="s11"><title>4. Personalised Boolean models of TCGA patients</title><p>Our prostate Boolean model was tailored to a set of 488 TCGA prostate cancer patients using our PROFILE personalisation method (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>). The distribution of the 488 patients&#x02019; Gleason score can be seen in <xref rid="app1fig9" ref-type="fig">Appendix 1&#x02014;figure 9</xref>. The prostate cancer patients recipe that has a better correlation with their Gleason score was using mutations and copy number alterations (CNA) as node activity status and RNA as initial conditions and transition rates (<xref rid="app1fig10" ref-type="fig">Appendix 1&#x02014;figure 10</xref>). All the 488 TCGA prostate cancer patients&#x02019; models can be found in MaBoSS format in <xref rid="supp3" ref-type="supplementary-material">Supplementary file 3</xref>.</p><sec sec-type="appendix" id="s11-1"><title>4.1. Phenotype distribution of TCGA patients</title><p>One of the quality checks performed in PROFILE is to build models using different recipes, i.e. using different data to modify different model variables, and to compare them to some clinical grouping or expression signature to rank them and select the most performing one. In our case, we used five different recipes (only mutations, mutations and CNA, mutations and RNA data, mutations, CNA and RNA data and only RNA data), we grouped the patients by their GG (either 3- or 5-stage) and studied the distributions of the different phenotypes scores: <italic toggle="yes">Apoptosis</italic> (<xref rid="app1fig10" ref-type="fig">Appendix 1&#x02014;figure 10</xref>), <italic toggle="yes">DNA repair</italic> (<xref rid="app1fig11" ref-type="fig">Appendix 1&#x02014;figure 11</xref>)<italic toggle="yes">, Invasion</italic> (<xref rid="app1fig12" ref-type="fig">Appendix 1&#x02014;figure 12</xref>), <italic toggle="yes">Migration</italic> (<xref rid="app1fig13" ref-type="fig">Appendix 1&#x02014;figure 13</xref>) and <italic toggle="yes">Proliferation</italic> (<xref rid="app1fig14" ref-type="fig">Appendix 1&#x02014;figure 14</xref>). Finally, we chose the recipe that uses mutations, CNA and RNA data as it included the most quantity of data and reproduced desired results (<xref rid="supp3" ref-type="supplementary-material">Supplementary file 3</xref>). Note that the correspondence between 3- and 5-stage GG is the following: GG Low is GG 1, GG Intermediate is GG 2 and 3 and GG High is GG 4 and 5. We used the Kruskal-Wallis rank sum test to identify if the phenotype distributions across 3- and 5-stage GG could originate from different distributions and, if significant, used the Dunn&#x02019;s nonparametric pairwise multiple comparisons test to identify which pairs of groups are statistically different.</p><p>Next, we took the personalised models that used mutations, CNA and RNA data and performed a PCA analysis on the 488 TCGA patients (<xref rid="supp3" ref-type="supplementary-material">Supplementary file 3</xref>) and their five phenotype scores that result from simulating them using MaBoSS. For these PCA, we grouped the patients by 3-stages GG (<xref rid="app1fig15" ref-type="fig">Appendix 1&#x02014;figure 15</xref>) and 5-stages GG (<xref rid="app1fig16" ref-type="fig">Appendix 1&#x02014;figure 16</xref>). In addition and for the sake of clarity, we reduced each of these groups to their barycenter (<xref rid="app1fig17" ref-type="fig">Appendix 1&#x02014;figure 17</xref> for 3-stages GG and <xref rid="app1fig18" ref-type="fig">Appendix 1&#x02014;figure 18</xref> for 5-stages GG), where we can see that higher GG move towards <italic toggle="yes">Proliferation</italic>, <italic toggle="yes">Invasion</italic> and <italic toggle="yes">Migration</italic> variables.</p></sec><sec sec-type="appendix" id="s11-2"><title>4.2. Analysis of drugs that inhibit the activity of genes of TCGA patients</title><p>Using our pipeline of tools (<xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>), we performed the analysis of all single perturbations that reduce <italic toggle="yes">Proliferation</italic> or increase <italic toggle="yes">Apoptosis</italic> together with the combined perturbations of a set of selected genes that are targets of already-developed drugs relevant in cancer progression (<xref rid="table1" ref-type="table">Table 1</xref>). Then, we aggregated the results of the 488 patients to identify which inhibitions affected <italic toggle="yes">Proliferation</italic> (<xref rid="app1fig19" ref-type="fig">Appendix 1&#x02014;figure 19</xref>) and <italic toggle="yes">Apoptosis</italic> (<xref rid="app1fig20" ref-type="fig">Appendix 1&#x02014;figure 20</xref>) the most in this cohort.</p><p>Interestingly, we found several genes that were found as suitable points of intervention in most of the patients (MYC_MAX complex and SPOP were identified in more than 80% of the cases) (<xref rid="app1fig19" ref-type="fig">Appendix 1&#x02014;figure 19</xref> and <xref rid="app1fig20" ref-type="fig">Appendix 1&#x02014;figure 20</xref>), but others were specific to only some of the patients (MXI1 was identified in only 4 patients, 1% of the total, GLI in only 7% and WNT in 8% of patients).</p><p>The inactivation of some of the targeted genes had greater effect in some patients than in others, suggesting the possibility for the design of personalised drug treatments (Main text). Nevertheless, knowing that some treatments that inhibit one gene are already able to reduce <italic toggle="yes">Proliferation</italic> phenotypes considerably, we explored the possibility of finding combinations of treatments that could lead to the same types of outcomes. One reason for searching for coupled drugs is that these combinations allow the use of lower doses of each of the two drugs and thus reduce their toxicity. It is important to note, though, that the analyses performed with the mathematical model do not aim at predicting drug dosages per se but to help in the identification of potential candidates.</p><p>The exhaustive search for combinations of drugs for each patient of the cohort requires an extensive amount of computation time (9 days and 7 hr on a personal computer or 3 hr on 20 nodes with 48 CPUs each, per model) as all variables of the model are automatically overexpressed and inhibited, one by one and in pairs, leading to a vast amount of simulations. For this reason, we have narrowed the list of potential candidates to reduce <italic toggle="yes">Proliferation</italic> or increase <italic toggle="yes">Apoptosis</italic> by performing the analysis of all single perturbation and selecting the combined perturbations of a set of selected genes that are targets of already-developed drugs relevant in cancer progression (Main text, <xref rid="table1" ref-type="table">Table 1</xref>).</p><p>We used the models to grade the effects that the combined treatments would have in each one of the 488 TCGA patient-specific models. The resulting list of combinations vary greatly from patient to patient, making it infeasible economically for labs and companies to pursue true patient-specific treatments. It also poses challenges in clinical trial designs aimed at validating the model based on the selection of treatments. Because of these constraints, it is more interesting commercially to target group-specific treatments, which can be more easily related to clinical stages of the disease. Mathematical modelling of patient profiles would then help to classify them in these groups, providing, in essence, a grade-specific treatment.</p><p>The TCGA mutants and their normalised phenotype scores in regards to the wild type model can be found in <xref rid="supp4" ref-type="supplementary-material">Supplementary file 4</xref>.</p></sec></sec><sec sec-type="appendix" id="s12"><title>5. Personalised Boolean models of prostate cell lines</title><p>We tailored our generic prostate model to eight prostate-specific cell lines: 22RV1 (<xref rid="bib101" ref-type="bibr">Sramkoski et al., 1999</xref>), BPH-1 (<xref rid="bib47" ref-type="bibr">Hayward et al., 1995</xref>), DU-145 (<xref rid="bib105" ref-type="bibr">Stone et al., 1978</xref>), LNCaP-Clone-FGC (<xref rid="bib51" ref-type="bibr">Horoszewicz et al., 1983</xref>), NCI-H660 (<xref rid="bib55" ref-type="bibr">Johnson et al., 1989</xref>; <xref rid="bib66" ref-type="bibr">Lai et al., 1995</xref>; <xref rid="bib17" ref-type="bibr">Castoria et al., 2011</xref>), PC-3 (<xref rid="bib56" ref-type="bibr">Kaighn et al., 1979</xref>), PWR-1E (<xref rid="bib114" ref-type="bibr">Webber et al., 1996</xref>), and VCaP (<xref rid="bib59" ref-type="bibr">Korenchuk et al., 2001</xref>). These cell lines had available datasets in the GDSC resource (<xref rid="bib53" ref-type="bibr">Iorio et al., 2016</xref>) and these were used to personalise models using our PROFILE framework (<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>) and using mutation data as discrete data and RNA as continuous data (<xref rid="app1fig21" ref-type="fig">Appendix 1&#x02014;figure 21</xref>).</p><p>We simulated the prostate cell line-specific models under random initial conditions and observed that they generated distinctive phenotype probabilities and captured some of the differences described in literature (<xref rid="app1fig21" ref-type="fig">Appendix 1&#x02014;figure 21</xref> and <xref rid="app1fig22" ref-type="fig">Appendix 1&#x02014;figure 22</xref>). For instance, it has been described that PC-3 cell line has high migratory potential compared to DU-145 cells, which have a moderate migratory potential, and to LNCaP cells, which have low migratory potential (<xref rid="bib26" ref-type="bibr">Cunningham and You, 2015</xref>). In our simulations, we capture that PC-3 has greater invasiveness, migration and proliferation than DU-145. However, the invasiveness and proliferation potential of LNCaP is much higher than PC3. Note that these results come from a collection of datasets from GDSC and a Boolean model that includes a subset of the interactions of 312 proteins. Distortions from real-life behaviour are expected and will be the focus of further research, such as the high LNCaP invasiveness or the lack of difference of the benign cell lines (BPH-1 and PWR-1E) with the rest of the cell lines.</p><p>As we did for the TCGA patients&#x02019; study, we tried different personalisation recipes to personalise these cell lines, but as they had no associated clinical features, we were left with the comparison of the different values for the model&#x02019;s outputs among the recipes. We chose the aforementioned recipe as it included two different data types (RNAseq and mutations) and reproduced desired results (<xref rid="app1fig21" ref-type="fig">Appendix 1&#x02014;figure 21</xref> and <xref rid="app1fig22" ref-type="fig">Appendix 1&#x02014;figure 22</xref>). Nevertheless, we could have considered using mutation and CNA as discrete data and RNA as continuous data, but the inclusion of CNA data forced LNCaP proliferation to be zero (<xref rid="app1fig23" ref-type="fig">Appendix 1&#x02014;figure 23</xref>). This is due to the fact that CNA data used as discrete data forces several nodes to be active or inactive throughout the simulation, as if they were mutants. Notably, CNA data forces E2F1 node to be 0, which forces Cyclin B to be 0 and it forces SMAD node to be 1, which forces MYC_MAX node to be 0 and p21 node to be 1, forcing Cyclin D to be 0. Without either Cyclin B or D, the model cannot activate the <italic toggle="yes">Proliferation</italic> node.</p><p>In addition, we wanted to study these different personalisation recipes to try to better match simulated phenotypes and cell line phenotypes described experimentally, but we had similar results (Appendix <xref rid="app1fig23" ref-type="fig">Appendix 1&#x02014;figure 23</xref>). Furthermore and due to the mismatches of cell line models with their described biology characteristics, we went back to the source data to study if these mismatches were something we could correct on the model or a problem of the dataset we used to personalise the model. We performed principal component analysis (PCA, using <italic toggle="yes">FactoMineR</italic> R package) (<xref rid="bib67" ref-type="bibr">L&#x000e9; et al., 2008</xref>; <xref rid="app1fig24" ref-type="fig">Appendix 1&#x02014;figure 24</xref>) on the dataset used to personalise the models: an RNAseq dataset of 111 genes. We found that the cell lines do not cluster by their characteristics: DU-145, an invasive cell line, is close to BPH-1 and PWR-1E, non-invasive cell lines.</p><p>Furthermore, we digged into the pathways that are characteristic of each of these cell lines by using single sample GSEA (using <italic toggle="yes">ssGSEA</italic> 2.0 R package) (<xref rid="bib62" ref-type="bibr">Krug et al., 2019</xref>; <xref rid="app1fig25" ref-type="fig">Appendix 1&#x02014;figure 25</xref>) on the same RNA dataset using the Hallmarks molecular signatures. We found that out of the 50 Hallmarks, 21 have an overlap of more than five genes with the model&#x02019;s genes. Thus, we set to cluster the cell lines by using the signatures of each one of them in these 21 pathways. The results are quite telling of the lack of clear clustering of these cell lines with their different characteristics (Appendix <xref rid="app1fig24" ref-type="fig">Appendix 1&#x02014;figures 24</xref> and <xref rid="app1fig25" ref-type="fig">25</xref>): invasive and non-invasive cell lines have similar signature values in EMT or G2M checkpoint pathways, BPH-1 clusters with NCI-H660 and PWR-1E with DU-145, etc.</p><p>All in all, it is unrealistic to expect that a model of different cellular behaviours will match all biological aspects and characteristics as models are, by definition, abstractions of reality (<xref rid="bib91" ref-type="bibr">Rosenblueth and Wiener, 1945</xref>; <xref rid="bib61" ref-type="bibr">Korzybski, 1995</xref>). For instance, if one were to match the cell lines&#x02019; doubling times, of which <italic toggle="yes">Proliferation</italic> phenotype should be a good proxy (<xref rid="bib102" ref-type="bibr">St John et al., 2012</xref>; <xref rid="bib26" ref-type="bibr">Cunningham and You, 2015</xref>), such a study would need a deeper understanding of the cell&#x02019;s biology, the modelling of many more processes, with many more parameters, and a more complete simulation framework both multi-scaled and finer-grained, which is beyond the scope of the present work.</p><p>All the cell line-specific personalised models are publicly available in <xref rid="supp5" ref-type="supplementary-material">Supplementary file 5</xref>.</p></sec><sec sec-type="appendix" id="s13"><title>6. Personalised LNCaP Boolean model</title><p>LNCaP model was selected to study its genetic interactions and its uses for drug discovery. The simulation of the LNCaP-specific model under random initial conditions leads to four most probable phenotypes: <italic toggle="yes">Invasion-Migration</italic>, <italic toggle="yes">Invasion-Migration-Proliferation</italic>, <italic toggle="yes">Invasion-Proliferation</italic> and <italic toggle="yes">Invasion</italic>. Using MaBoSS software, we were able to assign probabilities to each one of these phenotypes (<xref rid="app1fig26" ref-type="fig">Appendix 1&#x02014;figure 26</xref> and <xref rid="supp1 supp2" ref-type="supplementary-material">Supplementary files 1 and 2</xref>).</p><p>Additionally, we studied the LNCaP model under four different growth conditions that could be reproduced in experiments. These are a nutrient-rich media that mimics the RPMI supplemented with glucose and foetal bovine serum with additional androgen, EGF, both or none (<xref rid="app1fig27" ref-type="fig">Appendix 1&#x02014;figure 27</xref>).</p><sec sec-type="appendix" id="s13-1"><title>6.1. High-throughput mutant analysis of LNCaP model</title><p>A mutant in the logical framework is simulated by setting the node corresponding to the gene mutated to 0 in the case of loss of function and to 1 in the case of gain of function. The effect of a mutation is assessed, likewise to the change of initial conditions, by comparing the mutant&#x02019;s probability of reaching a phenotype with respect to the wild type model. Therefore, mutations change the model phenotypes&#x02019; probabilities and this can be compared to the wild type model.</p><p>The logical model allows us to easily simulate all possible combinations of mutations and study the potential redundancy or synergy of these perturbations. To perform this, tools like our high-throughput mutant analysis pipeline (<xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>) are ideally suited. This pipeline of tools was applied to the LNCaP-specific model in order to study all single and double mutants of the LNCaP model (32,258 mutants) and their probabilities of reaching all the phenotypes of the model.</p><p>The double mutants of the high-throughput mutant analysis were used to identify genetic interaction relationships, such as epistasis, among the single mutants. Phenotype probabilities&#x02019; variations of all 32,258 models were compared to the wild type model and were used to identify relevant combinations of perturbations that affect phenotypes of interest (<xref rid="app1fig28" ref-type="fig">Appendix 1&#x02014;figure 28</xref>) and single phenotypes (<xref rid="app1fig29" ref-type="fig">Appendix 1&#x02014;figure 29</xref>). In these figures, a PCA was applied to the matrix of the seven phenotype probabilities of the 32,258 mutants and was then normalised with the PCA values of the wild type. The result is a PCA centred around the wild type using the phenotypes as variables, where the distance between a given point and the wild type orange point at the centre is representative of the distance in the phenotype scores among them.</p><p>We were particularly interested in identifying knock-out (KO) and over-expression (OE) mutants that depleted <italic toggle="yes">Proliferation</italic> and/or increased <italic toggle="yes">Apoptosis</italic> with regard to the wild type LNCaP model. Using MaBoSS, we were able to quantify and rank the effect of all the 32,258 mutants on the probabilities of reaching <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Apoptosis</italic> (<xref rid="supp6" ref-type="supplementary-material">Supplementary file 6</xref>).</p><p>The double mutants that mostly depleted <italic toggle="yes">Proliferation</italic> were combinations of p21_oe, MXI1_oe, HIF1_oe, AR_ko and E2F1_ko. Likewise, the double mutants that mostly increased <italic toggle="yes">Apoptosis</italic> were combinations of GLI_oe, Caspase3_oe, Caspase8_oe, Caspase9_oe and PTCH1_oe. The single mutants that mostly depleted <italic toggle="yes">Proliferation</italic> were HIF_oe, MXI_oe, p21_oe, Caspase3_oe and Caspase8_oe. Likewise, those that mostly increased <italic toggle="yes">Apoptosis</italic> were GLI_oe, Caspase3_oe, Caspase8_oe, Caspase9_oe and SMO_oe</p><p>It was in our interest to identify drugs that could inhibit some of these genes, thus, we filtered these lists to find the best single KO mutations. We found that the single KO that mostly depleted <italic toggle="yes">Proliferation</italic> were AR_ko, VHL_ko, AKT_ko, E2F1_ko, PIP3_ko, EGFR_ko, PI3K_ko, CDH2_ko, TWIST1_ko, ERK_ko. Likewise, the single KO that mostly increased <italic toggle="yes">Apoptosis</italic> were AKT_ko, AR_ko, ERK_ko, cFLAR_ko, SPOP_ko, PIP3_ko, PI3K_ko, EGFR_ko, HSPs_ko and ATR_ko. Another knockout, p53_ko, was identified in our analysis, but was later discarded upon closer analysis. From topological analyses, p53 deletion should increase <italic toggle="yes">Proliferation</italic>, as p21, a cyclin inhibitor, is therefore not transcribed. Nevertheless, p53 has a dual effect on <italic toggle="yes">Apoptosis</italic> in the network: p53 activates CytC and Apaf1, which activate <italic toggle="yes">Apoptosis</italic>, but p53 also inhibits BIRC5, an activator of <italic toggle="yes">Apoptosis</italic>. The model should be closely inspected to correct this mismatch in future works. In any case, the effects of p53&#x02019;s mutations are not further analysed in present work, nor their results are further discussed.</p><p>We gathered the 20 top nodes from each of those lists and ended with 29 nodes that could be knocked out to deplete <italic toggle="yes">Proliferation</italic> and/or increase <italic toggle="yes">Apoptosis</italic> (AKT, AR, ATR, AXIN1, Bak, BIRC5, CDH2, cFLAR, CyclinB, CyclinD, E2F1, eEF2, eEF2K, EGFR, ERK, HSPs, MED12, mTORC1, mTORC2, MYC, MYC_MAX, PHDs, PI3K, PIP3, SPOP, TAK1, TWIST1, VHL). We used this ranking, the genes corresponding to these nodes and known drugs that target these genes to shortlist potential therapeutic target candidates tailored to LNCaP cell line (Main text, <xref rid="table1" ref-type="table">Table 1</xref>).</p></sec><sec sec-type="appendix" id="s13-2"><title>6.2. Robustness analysis of the logical model</title><p>We performed a perturbation on the logical rules stability of the LNCaP model, following our previous work (<xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>). In Section 6.1 we forced the value of a node to be 0 or 1 throughout the simulation. Now, we have changed one and two logical gates from each logical rule of the LNCaP model and studied the effects on the phenotype scores. In short, we have changed an AND in OR gate and vice versa in each logical rule (what we call level 1 analysis with 372 simulations in this model) or twice in the same rule (level 2 analysis with 1,263 simulations in this model).</p><p>Overall, we see that all of the most probable phenotypes (as the ones from <xref rid="app1fig28" ref-type="fig">Appendix 1&#x02014;figure 28</xref>) are very robust to this kind of perturbation. Even the less stable phenotype, <italic toggle="yes">Invasion-Migration-Proliferation</italic>, only has 2.69% of the single (level 1) perturbations that reduce this phenotype&#x02019;s probability to zero (<xref rid="app1fig30" ref-type="fig">Appendix 1&#x02014;figure 30A</xref>) and 3.33% of the double (level 2) perturbations (<xref rid="app1fig30" ref-type="fig">Appendix 1&#x02014;figure 30B</xref>). Most of these perturbations were focused on HIF1 and AR_ERG nodes for single perturbations (<xref rid="app1fig31" ref-type="fig">Appendix 1&#x02014;figure 31A</xref>) and HIF1 and p53 nodes for double perturbations (<xref rid="app1fig31" ref-type="fig">Appendix 1&#x02014;figure 31B</xref>).</p></sec></sec><sec sec-type="appendix" id="s14"><title>7. Drug studies in prostate cell lines</title><sec sec-type="appendix" id="s14-1"><title>7.1. Drugs associated to genes included in the model</title><p>We tested if the drugs that targeted the genes included in the model allowed us to identify cell line specificities. We analysed drug sensitivity data from GDSC1 and GDSC2 studies (<xref rid="bib53" ref-type="bibr">Iorio et al., 2016</xref>) and for each drug we calculated a normalised sensitivity of the eight prostate cell lines considered in present study (22RV1, BPH-1, DU-145, LNCaP-Clone-FGC, NCI-H660, PC-3, PWR-1E, and VCaP). We normalised drug sensitivity across cell lines in the following way: cells were ranked from most sensitive to least sensitive using ln(IC50) as the drug sensitivity metrics, and the rank was divided by the number of cell lines tested with the given drug. Thus, the most sensitive cell line scored 0, while the most resistant cell line scored one normalised sensitivity. This rank-based metric made it possible to analyse all drug sensitivities for a given cell line, without drug-specific confounding factors, such as the mean IC50 of a given drug or others.</p><p>We observed that cell lines described as resistant (DU-145 and PC-3) have a skewed distribution towards least sensitive values (<xref rid="app1fig32" ref-type="fig">Appendix 1&#x02014;figure 32D and E</xref>), while cell lines such as LNCaP have a skewed distribution towards more sensitive values (<xref rid="app1fig32" ref-type="fig">Appendix 1&#x02014;figure 32A</xref>). Meaning that the drugs that target the genes in the personalised model are not very effective against the resistant cell lines, but that LNCaP is significantly more sensitive to these. Additionally, we found that BPH-1 is generally sensitive to all drugs, let them be model-specific or not (<xref rid="app1fig32" ref-type="fig">Appendix 1&#x02014;figure 32C</xref>). For the other cell lines, there is no significant difference between model-specific drugs or not.</p><p>In addition, we performed a target set enrichment analysis using the <italic toggle="yes">fgsea</italic> method (<xref rid="bib60" ref-type="bibr">Korotkevich et al., 2016</xref>) from the <italic toggle="yes">piano</italic> R package (<xref rid="bib113" ref-type="bibr">V&#x000e4;remo et al., 2013</xref>). Again, we targeted pathway information from the GDSC1 and GDSC2 studies (<xref rid="bib53" ref-type="bibr">Iorio et al., 2016</xref>) as target sets, and performed the enrichment analysis on the aforementioned normalised drug sensitivity profile of the LNCaP cell line. This target enrichment analysis showed that LNCaP cell lines are especially sensitive to PI3K/AKT/MTOR, hormone related (AR targeting) and Chromatin targeting (bromodomain inhibitors, regulating Myc) drugs (<xref rid="app1table2" ref-type="table">Appendix 1&#x02014;table 2</xref>, adjusted p-values from target enrichment: 0.001, 0.001 and 0.037, respectively), which corresponds to the model predictions (Main text, <xref rid="table1" ref-type="table">Table 1</xref>).</p><table-wrap position="anchor" id="app1table2"><label>Appendix 1&#x02014;table 2.</label><caption><title>Target enrichment for LNCaP-specific drug sensitivities.</title><p>Drugs were sorted based on rank normalised drug sensitivity 0: most sensitive, 1 most resistant, based on GDSC AUC drug sensitivity metric for LNCaP. Target pathway enrichment analysis was performed based on the pathway membership of drug targets. Direction represents whether pathway-targeting drugs were enriched in sensitive or resistant drugs.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" valign="bottom" rowspan="1" colspan="1">Drug target pathway</th><th align="left" valign="bottom" rowspan="1" colspan="1">p-value</th><th align="left" valign="bottom" rowspan="1" colspan="1">adj. p-value</th><th align="left" valign="bottom" rowspan="1" colspan="1">Direction</th></tr></thead><tbody><tr><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>PI3K/MTOR signalling</bold>
</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.00011563</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">
<bold>0.0011106</bold>
</td><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>sensitive</bold>
</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>Hormone-related</bold>
</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.00014808</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">
<bold>0.0011106</bold>
</td><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>sensitive</bold>
</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>Chromatin other</bold>
</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.0065661</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">
<bold>0.03283</bold>
</td><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>sensitive</bold>
</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>Chromatin histone methylation</bold>
</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.01216</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">
<bold>0.045601</bold>
</td><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>sensitive</bold>
</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">p53 pathway</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.079554</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.23866</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">DNA replication</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.10466</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.26164</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">WNT signalling</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.13583</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.29107</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Unclassified</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.20391</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.38233</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Genome integrity</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.54186</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.90311</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Cytoskeleton</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.63153</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.93981</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Other, kinases</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.81647</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.93981</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">RTK signalling</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.85985</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.93981</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Other</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.87572</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.93981</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Protein stability and degradation</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.88166</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.93981</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">EGFR signalling</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.93981</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.93981</td><td align="left" valign="bottom" rowspan="1" colspan="1">sensitive</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Apoptosis regulation</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.96036</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.96036</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Chromatin histone acetylation</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.73164</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.83616</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">JNK and p38 signalling</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.63484</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.83616</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">IGF1R signalling</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.23538</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.37662</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Cell cycle</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.19382</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.37662</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Metabolism</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.053352</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.14227</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">Mitosis</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.027536</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.11014</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr><tr><td align="left" valign="bottom" rowspan="1" colspan="1">
<bold>ERK MAPK signalling</bold>
</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">0.00050075</td><td align="char" char="." valign="bottom" rowspan="1" colspan="1">
<bold>0.004006</bold>
</td><td align="left" valign="bottom" rowspan="1" colspan="1">resistant</td></tr></tbody></table></table-wrap></sec><sec sec-type="appendix" id="s14-2"><title>7.2. Drugs associated to the proposed targets of LNCaP</title><p>We wanted to test if the LNCaP cell line is more sensitive than the rest of the prostate cell lines to the LNCaP-specific drugs identified in <xref rid="table1" ref-type="table">Table 1</xref> from the main text. We compared GDSC&#x02019;s Z-score of these drugs in LNCaP with their Z-scores in all GDSC cell lines (<xref rid="app1fig5" ref-type="fig">Appendix 1&#x02014;figure 5</xref>). We observed that LNCaP is more sensitive to drugs targeting AKT or TERT than the rest of the studied prostate cell lines. In <xref rid="app1fig33" ref-type="fig">Appendix 1&#x02014;figure 33</xref>, we can observe that trend in comparison to the other prostate cell lines and to the rest of the GDSC cell lines. In addition, we see that AKT sensibility in LNCaP is one of the highest in the GDSC records.</p></sec><sec sec-type="appendix" id="s14-3"><title>7.3. Gradual inhibition of genes in LNCaP model</title><p>Logical models can be used to simulate the effect of therapeutic interventions by using our PROFILE_v2 methodology. For this, we can take advantage of MaBoSS as it can perform simulations using a population of trajectories by changing the proportion of activated and inhibited status of a given node. Using MaBoSS method (see Section 2.1), initial values of each node can be defined with a continuous value between 0 and 1 representing the probability for the node to be defined as 1 for each new trajectory. This can be determined in the configuration file of each model (see, for instance, &#x02018;istate&#x02019; section of the CFG files in the <xref rid="supp1 supp3 supp5" ref-type="supplementary-material">Supplementary files 13 and 5</xref>). For instance, out of 5,000 trajectories of the Gillespie algorithm, MaBoSS can simulate 70% of them with an activated AKT and 30% with an inhibited AKT node. The phenotypes&#x02019; probabilities for the 5,000 trajectories are averaged and these are considered representative of a model with a drug that inhibits 30% of the activity of AKT.</p><p>All these inhibitions were performed using our PROFILE_v2 framework (<ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2" ext-link-type="uri">https://github.com/ArnauMontagud/PROFILE_v2</ext-link>) that allow to study the effect of single and double mutations (knock-out and overexpression) in the phenotypes&#x02019; probabilities using MaBoSS as well as to study the Bliss Independence synergy score of these combinations.</p><sec sec-type="appendix" id="s14-3-1"><title>7.3.1. Single inhibitions</title><p>We studied the variations of all the phenotype scores upon the 17 nodes&#x02019; inhibitions under EGF growth condition (<xref rid="app1fig34" ref-type="fig">Appendix 1&#x02014;figure 34</xref>) and under AR, EGF, 00 and AR_EGF growth conditions (<xref rid="app1fig35" ref-type="fig">Appendix 1&#x02014;figure 35</xref>).</p></sec><sec sec-type="appendix" id="s14-3-2"><title>7.3.2. Double inhibitions</title><p>Thoroughly, we studied the effect of the inhibition of the 17 combined nodes under EGF growth condition in the <italic toggle="yes">Proliferation</italic> (<xref rid="app1fig36" ref-type="fig">Appendix 1&#x02014;figure 36</xref>) and <italic toggle="yes">Apoptosis</italic> phenotype score (<xref rid="app1fig37" ref-type="fig">Appendix 1&#x02014;figure 37</xref>).</p><p>This combined scores allowed us to study the Bliss Independence synergies scores and their variations in these combined nodes' inhibitions under EGF growth conditions. We studied <italic toggle="yes">Proliferation</italic> (<xref rid="app1fig38" ref-type="fig">Appendix 1&#x02014;figure 38</xref>) and <italic toggle="yes">Apoptosis</italic> phenotypes (<xref rid="app1fig39" ref-type="fig">Appendix 1&#x02014;figure 39</xref>).</p></sec></sec></sec><sec sec-type="appendix" id="s15"><title>8. Analyses of drug experiments</title><p>We present the dose-dependent changes in the LNCaP cell line growth upon drug addition of Hsp90 (<xref rid="app1fig40" ref-type="fig">Appendix 1&#x02014;figure 40</xref>) and PI3K/AKT inhibitors (<xref rid="app1fig41" ref-type="fig">Appendix 1&#x02014;figure 41</xref>) with insets to show the cytotoxicity assay results at 24, 48, and 72 hr after drug addition.</p><fig position="anchor" id="app1fig1"><label>Appendix 1&#x02014;figure 1.</label><caption><title>Mean activities by subgroups for gene modules defined from pathways described in ACSN.</title><p>(<bold>A</bold>) And in Hallmarks' gene sets (<bold>B</bold>) and that are significantly overdispersed over all samples. Blue indicates low pathway activity, red indicates high pathway activity.</p></caption><graphic xlink:href="elife-72626-app1-fig1" position="float"/></fig><fig position="anchor" id="app1fig2"><label>Appendix 1&#x02014;figure 2.</label><caption><title>Signed directed interactions between HSP90AA1 and nodes already taken into account in the model.</title></caption><graphic xlink:href="elife-72626-app1-fig2" position="float"/></fig><fig position="anchor" id="app1fig3"><label>Appendix 1&#x02014;figure 3.</label><caption><title>shortest paths found between ERG and TMPRSS2 or NKX3-1 by Pypath: no direct interaction is found.</title></caption><graphic xlink:href="elife-72626-app1-fig3" position="float"/></fig><fig position="anchor" id="app1fig4"><label>Appendix 1&#x02014;figure 4.</label><caption><title>Boolean toy model to showcase different examples of Boolean formulas.</title></caption><graphic xlink:href="elife-72626-app1-fig4" position="float"/></fig><fig position="anchor" id="app1fig5"><label>Appendix 1&#x02014;figure 5.</label><caption><title>Mean probabilities of the nodes characterising the cyclins and proliferation, with nutrients and growth factors as inputs.</title><p>We choose initial states for the nodes involved in the cell cycle that correspond to quiescence (cyclins OFF, cell cycle inhibitors Rb and p21 ON), in order to visualise the order of activation of the cyclins: first Cyclin D, then Cyclin B. The mean probabilities reach asymptotic levels because of the desynchronisation of stochastic trajectories in the population.</p></caption><graphic xlink:href="elife-72626-app1-fig5" position="float"/></fig><fig position="anchor" id="app1fig6"><label>Appendix 1&#x02014;figure 6.</label><caption><title>Mean probabilities in simulations of mutated models.</title><p>(<bold>A</bold>) Loss-of-function mutation of FOXA1. (<bold>B</bold>) Loss-of-function mutation of TP53.</p></caption><graphic xlink:href="elife-72626-app1-fig6" position="float"/></fig><fig position="anchor" id="app1fig7"><label>Appendix 1&#x02014;figure 7.</label><caption><title>Mean probabilities in simulations of the model with a multiple simulation: the gene fusion TMPRSS2:ERG and a loss-of-function of NKX3-1.</title></caption><graphic xlink:href="elife-72626-app1-fig7" position="float"/></fig><fig position="anchor" id="app1fig8"><label>Appendix 1&#x02014;figure 8.</label><caption><title>Data integration in Boolean models to have personalised Boolean models.</title></caption><graphic xlink:href="elife-72626-app1-fig8" position="float"/></fig><fig position="anchor" id="app1fig9"><label>Appendix 1&#x02014;figure 9.</label><caption><title>Distribution of 488 TCGA prostate cancer patients&#x02019; samples per Gleason grade.</title></caption><graphic xlink:href="elife-72626-app1-fig9" position="float"/></fig><fig position="anchor" id="app1fig10"><label>Appendix 1&#x02014;figure 10.</label><caption><title>Associations between simulations and Gleason grades (GG).</title><p>Distribution histograms of <italic toggle="yes">Apoptosis</italic> scores according to GG in three groups (<bold>A</bold>) and five groups (<bold>B</bold>). Columns correspond to different personalisation recipes (see <xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref> for more details). We found that across 3-stage GG Kruskal-Wallis rank sum test is significant for <italic toggle="yes">Apoptosis</italic> under the &#x02018;Mut, CNA, and RNA&#x02019; recipe (p-value = 2.83E-6) and significant across 5-stage GG (p-value = 1.88E-5). Additionally, we used Dunn&#x02019;s test to identify which pairs of groups are statistically different focusing on the 3-stage GG and found that grade High is statistically different from grades Low (Bonferroni&#x02019;s adjusted p-value = 3.3E-3) and Intermediate (Bonferroni&#x02019;s adjusted p-value = 9.47E-6).</p></caption><graphic xlink:href="elife-72626-app1-fig10" position="float"/></fig><fig position="anchor" id="app1fig11"><label>Appendix 1&#x02014;figure 11.</label><caption><title>Associations between simulations and Gleason grades (GG).</title><p>Distribution histograms of <italic toggle="yes">DNA_repair</italic> scores according to GG in three groups (<bold>A</bold>) and five groups (<bold>B</bold>). Columns correspond to different personalisation recipes (see <xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref> for more details). Kruskal-Wallis rank sum test across 3-stage GG is neither significant for <italic toggle="yes">DNA_Repair</italic> under the &#x02018;Mut, CNA and RNA&#x02019; recipe (p-value = 0.217) nor across 5-stage GG (p-value = 0.0995).</p></caption><graphic xlink:href="elife-72626-app1-fig11" position="float"/></fig><fig position="anchor" id="app1fig12"><label>Appendix 1&#x02014;figure 12.</label><caption><title>Associations between simulations and Gleason grades (GG).</title><p>Distribution histograms of <italic toggle="yes">Invasion</italic> scores according to GG in three groups (<bold>A</bold>) and five groups (<bold>B</bold>). Columns correspond to different personalisation recipes (see <xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref> for more details). Kruskal-Wallis rank sum test across 3-stage GG is significant for <italic toggle="yes">Invasion</italic> under the &#x02018;Mut, CNA, and RNA&#x02019; recipe (p-value = 0.0358), but not significant across 5-stage GG (p-value = 0.134). Using Dunn&#x02019;s test on the 3-stage GG, we found that grade High is statistically different from grade Intermediate (Bonferroni&#x02019;s adjusted p-value = 0.037).</p></caption><graphic xlink:href="elife-72626-app1-fig12" position="float"/></fig><fig position="anchor" id="app1fig13"><label>Appendix 1&#x02014;figure 13.</label><caption><title>Associations between simulations and Gleason grades (GG).</title><p>Distribution histograms of <italic toggle="yes">Migration</italic> scores according to GG in three groups (<bold>A</bold>) and five groups (<bold>B</bold>). Columns correspond to different personalisation recipes (see <xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref> for more details). Kruskal-Wallis rank sum test across 3-stage GG is neither significant for <italic toggle="yes">Migration</italic> under the &#x02018;Mut, CNA, and RNA&#x02019; recipe (p-value = 0.173) nor across 5-stage GG (p-value = 0.275).</p></caption><graphic xlink:href="elife-72626-app1-fig13" position="float"/></fig><fig position="anchor" id="app1fig14"><label>Appendix 1&#x02014;figure 14.</label><caption><title>Associations between simulations and Gleason Grades (GG).</title><p>Distribution histograms of <italic toggle="yes">Proliferation</italic> scores according to GG in three groups (<bold>A</bold>) and five groups (<bold>B</bold>). Columns correspond to different personalisation recipes (see <xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref> for more details). Kruskal-Wallis rank sum test across 3-stage GG is significant for <italic toggle="yes">Proliferation</italic> under the &#x02018;Mut, CNA, and RNA&#x02019; recipe (p-value = 0.00207) and across 5-stage GG (p-value = 0.013). Using Dunn&#x02019;s test on the 3-stage GG, we found that grade High is statistically different from grade Intermediate (Bonferroni&#x02019;s adjusted p-value = 0.0023).</p></caption><graphic xlink:href="elife-72626-app1-fig14" position="float"/></fig><fig position="anchor" id="app1fig15"><label>Appendix 1&#x02014;figure 15.</label><caption><title>Principal Component Analysis of all 488 TCGA patients in 3 Gleason Grades using the vectors of all five phenotypes from the model.</title></caption><graphic xlink:href="elife-72626-app1-fig15" position="float"/></fig><fig position="anchor" id="app1fig16"><label>Appendix 1&#x02014;figure 16.</label><caption><title>Principal Component Analysis of all 488 TCGA patients in 5 Gleason Grades using the vectors of all five phenotypes from the model.</title></caption><graphic xlink:href="elife-72626-app1-fig16" position="float"/></fig><fig position="anchor" id="app1fig17"><label>Appendix 1&#x02014;figure 17.</label><caption><title>Principal Component Analysis&#x02019; barycenters of all 488 TCGA patients grouped in 3 Gleason Grades using the vectors of all five phenotypes from the model.</title><p>This is the same figure as <xref rid="app1fig3" ref-type="fig">Appendix 1&#x02014;figure 3</xref> in the main text.</p></caption><graphic xlink:href="elife-72626-app1-fig17" position="float"/></fig><fig position="anchor" id="app1fig18"><label>Appendix 1&#x02014;figure 18.</label><caption><title>Principal Component Analysis&#x02019; barycenters of all 488 TCGA patients grouped in 5 Gleason Grades using the vectors of all five phenotypes from the model.</title></caption><graphic xlink:href="elife-72626-app1-fig18" position="float"/></fig><fig position="anchor" id="app1fig19"><label>Appendix 1&#x02014;figure 19.</label><caption><title>Nodes in the Boolean model that have a <italic toggle="yes">Proliferation</italic> value of at least 30% less the wild type value upon inactivation.</title><p>(<bold>A</bold>) Nodes from aggregating all patient-specific results; (<bold>B</bold>) Nodes from patients from Gleason Grade 1; (<bold>C</bold>) Nodes from patients from Gleason Grade 2; (<bold>D</bold>) Nodes from patients from Gleason Grade 3; (<bold>E</bold>) Nodes from patients from Gleason Grade 4; (<bold>F</bold>) Nodes from patients from Gleason Grade 5.</p></caption><graphic xlink:href="elife-72626-app1-fig19" position="float"/></fig><fig position="anchor" id="app1fig20"><label>Appendix 1&#x02014;figure 20.</label><caption><title>Nodes in the Boolean model that promote <italic toggle="yes">Apoptosis</italic> at least 30% more than the wild type value upon inactivation.</title><p>(<bold>A</bold>) Nodes from aggregating all patient-specific results; (<bold>B</bold>) Nodes from patients from Gleason Grade 1; (<bold>C</bold>) Nodes from patients from Gleason Grade 2; (<bold>D</bold>) Nodes from patients from Gleason Grade 3; (<bold>E</bold>) Nodes from patients from Gleason Grade 4; (<bold>F</bold>) Nodes from patients from Gleason Grade 5.</p></caption><graphic xlink:href="elife-72626-app1-fig20" position="float"/></fig><fig position="anchor" id="app1fig21"><label>Appendix 1&#x02014;figure 21.</label><caption><title>Phenotype simulation results across GDSC prostate cell line-specific Boolean models&#x02019; simulation with random initial conditions.</title><p>WT stands for wild type model, the original prostate model with no personalisation.</p></caption><graphic xlink:href="elife-72626-app1-fig21" position="float"/></fig><fig position="anchor" id="app1fig22"><label>Appendix 1&#x02014;figure 22.</label><caption><title>Phenotype simulation results across GDSC prostate cell line-specific Boolean models&#x02019; simulation with different initial conditions.</title><p>WT stands for wild type model, the original prostate model with no personalisation.</p></caption><graphic xlink:href="elife-72626-app1-fig22" position="float"/></fig><fig position="anchor" id="app1fig23"><label>Appendix 1&#x02014;figure 23.</label><caption><title>Phenotype simulation results across GDSC prostate cell line-specific Boolean models&#x02019; simulation with random initial conditions under different personalisation recipes.</title><p>Mutations and CNA are always considered as discrete data and RNA expression is always considered as continuous data.</p></caption><graphic xlink:href="elife-72626-app1-fig23" position="float"/></fig><fig position="anchor" id="app1fig24"><label>Appendix 1&#x02014;figure 24.</label><caption><title>Principal Component Analysis (PCA) of the RNA dataset used to tailor the prostate cell lines.</title></caption><graphic xlink:href="elife-72626-app1-fig24" position="float"/></fig><fig position="anchor" id="app1fig25"><label>Appendix 1&#x02014;figure 25.</label><caption><title>Results of the ssGSEA performed on the RNA dataset used to tailor the prostate cell lines.</title></caption><graphic xlink:href="elife-72626-app1-fig25" position="float"/></fig><fig position="anchor" id="app1fig26"><label>Appendix 1&#x02014;figure 26.</label><caption><title>Phenotype probabilities of LNCaP model under random initial conditions.</title></caption><graphic xlink:href="elife-72626-app1-fig26" position="float"/></fig><fig position="anchor" id="app1fig27"><label>Appendix 1&#x02014;figure 27.</label><caption><title>Wild type and LNCaP-specific model phenotype probability variations under four different growth conditions.</title><p>Androgen stands for androgen presence, EGF for EGF presence, and 00 for lack of androgen and EGF.</p></caption><graphic xlink:href="elife-72626-app1-fig27" position="float"/></fig><fig position="anchor" id="app1fig28"><label>Appendix 1&#x02014;figure 28.</label><caption><title>PCA of the 32,258 single and double LNCaP model mutants with combinations of the most probable phenotypes.</title></caption><graphic xlink:href="elife-72626-app1-fig28" position="float"/></fig><fig position="anchor" id="app1fig29"><label>Appendix 1&#x02014;figure 29.</label><caption><title>PCA of the 32,258 single and double LNCaP model mutants with the decomposition in single phenotypes.</title></caption><graphic xlink:href="elife-72626-app1-fig29" position="float"/></fig><fig position="anchor" id="app1fig30"><label>Appendix 1&#x02014;figure 30.</label><caption><title><italic toggle="yes">Invasion-Migration-Proliferation</italic> phenotype probability distribution across all mutants for logical gates.</title><p>Bin where wild type value is found has been marked with dark red colour. (<bold>A</bold>) Phenotype probability using level one single perturbations; (<bold>B</bold>) Phenotype probability using level two double perturbations.</p></caption><graphic xlink:href="elife-72626-app1-fig30" position="float"/></fig><fig position="anchor" id="app1fig31"><label>Appendix 1&#x02014;figure 31.</label><caption><title>Distribution of perturbations on nodes&#x02019; logical gates that reduce <italic toggle="yes">Invasion-Migration-Proliferation</italic> phenotype probability to zero.</title><p>(<bold>A</bold>) Counts of level one single perturbations; (<bold>B</bold>) Counts of level two double perturbations.</p></caption><graphic xlink:href="elife-72626-app1-fig31" position="float"/></fig><fig position="anchor" id="app1fig32"><label>Appendix 1&#x02014;figure 32.</label><caption><title>Drug sensitivity of the seven prostate cell lines.</title><p>Rank normalised drug sensitivity (0: most sensitive; 1: most resistant, based on GDSC AUC drug sensitivity metric) for each GDSC drug across prostate cancer cell lines. Drugs are grouped to be predicted effective drugs based on the LNCaP Boolean model (orange) and predicted ineffective drugs (blue). Mann-Whitney U p-values for differences between the rank normalised drug sensitivity between predicted effective and ineffective drugs: (<bold>A</bold>) LNCaP, <italic toggle="yes">P</italic> = 0.00041 (more sensitive to LNCaP model-predicted drugs); (<bold>B</bold>) 22RV1, <italic toggle="yes">P</italic> = 0.0033 (more sensitive to LNCaP model-predicted drugs); (<bold>C</bold>) BPH-1, <italic toggle="yes">P</italic> = 0.31; (<bold>D</bold>) DU-145, <italic toggle="yes">P</italic> = 0.0026 (more resistant to LNCaP model-predicted drugs); (<bold>E</bold>) PC-3, <italic toggle="yes">P</italic> = 0.15; (<bold>F</bold>) PWR-1E, <italic toggle="yes">P</italic> = 0.075; (<bold>G</bold>) VCaP <italic toggle="yes">P</italic> = 0.38.</p></caption><graphic xlink:href="elife-72626-app1-fig32" position="float"/></fig><fig position="anchor" id="app1fig33"><label>Appendix 1&#x02014;figure 33.</label><caption><title>Model-targeting drugs&#x02019; sensitivities across prostate cell lines.</title><p>GDSC Z-score was obtained for all the drugs targeting genes included in the model for all the prostate cell lines in GDSC. LNCaP is highlighted in red, the other seven prostate cell lines in blue and the rest of the GDSC cell lines are coloured in grey.</p></caption><graphic xlink:href="elife-72626-app1-fig33" position="float"/></fig><fig position="anchor" id="app1fig34"><label>Appendix 1&#x02014;figure 34.</label><caption><title>Phenotype score variations of the LNCaP model upon nodes&#x02019; inhibition under <italic toggle="yes">EGF</italic> growth condition.</title><p>Values of the scores are depicted with a colour gradient.</p></caption><graphic xlink:href="elife-72626-app1-fig34" position="float"/></fig><fig position="anchor" id="app1fig35"><label>Appendix 1&#x02014;figure 35.</label><caption><title>Phenotype score variations of the LNCaP model upon nodes inhibition under <italic toggle="yes">AR, EGF, 00</italic> and <italic toggle="yes">AR_EGF</italic> growth conditions.</title><p>Values of the scores are depicted with a colour gradient.</p></caption><graphic xlink:href="elife-72626-app1-fig35" position="float"/></fig><fig position="anchor" id="app1fig36"><label>Appendix 1&#x02014;figure 36.</label><caption><title><italic toggle="yes">Proliferation</italic> phenotype score variations of the LNCaP model upon combined nodes inhibition under <italic toggle="yes">EGF</italic> growth condition.</title><p><xref rid="app1fig4" ref-type="fig">Appendix 1&#x02014;figure 4A</xref> is a closer look at ERK and MYC_MAX combination and <xref rid="app1fig4" ref-type="fig">Appendix 1&#x02014;figure 4B</xref> at HSPs and PI3K combination.</p></caption><graphic xlink:href="elife-72626-app1-fig36" position="float"/></fig><fig position="anchor" id="app1fig37"><label>Appendix 1&#x02014;figure 37.</label><caption><title><italic toggle="yes">Apoptosis</italic> phenotype score variations of the LNCaP model upon combined nodes inhibition under <italic toggle="yes">EGF</italic> growth condition.</title></caption><graphic xlink:href="elife-72626-app1-fig37" position="float"/></fig><fig position="anchor" id="app1fig38"><label>Appendix 1&#x02014;figure 38.</label><caption><title>Bliss Independence synergies scores variations in <italic toggle="yes">Proliferation</italic> phenotype of the LNCaP model upon combined nodes inhibition under <italic toggle="yes">EGF</italic> growth conditions.</title><p>Bliss Independence synergy score &#x0003c;1 is characteristic of drug synergy. <xref rid="app1fig4" ref-type="fig">Appendix 1&#x02014;figure 4C</xref> is a closer look at ERK and MYC_MAX combination and <xref rid="app1fig4" ref-type="fig">Appendix 1&#x02014;figure 4D</xref> at HSPs and PI3K combination, grey colour means one of the drugs is absent and thus no synergy score is available.</p></caption><graphic xlink:href="elife-72626-app1-fig38" position="float"/></fig><fig position="anchor" id="app1fig39"><label>Appendix 1&#x02014;figure 39.</label><caption><title>Bliss Independence synergies scores variations in <italic toggle="yes">Apoptosis</italic> phenotypes of the LNCaP model upon combined nodes inhibition under <italic toggle="yes">EGF</italic> growth conditions.</title><p>Bliss Independence synergy score &#x0003c;1 is characteristic of drug synergy, grey colour means one of the drugs is absent and thus no synergy score is available.</p></caption><graphic xlink:href="elife-72626-app1-fig39" position="float"/></fig><fig position="anchor" id="app1fig40"><label>Appendix 1&#x02014;figure 40.</label><caption><title>Hsp90 inhibitors resulted in dose-dependent changes in the LNCaP cell line growth.</title><p>(<bold>A</bold>) Real-time cell electronic sensing (RT-CES) cytotoxicity assay of Hsp90 inhibitor, 17-DMAG, that uses the Cell Index as a measurement of the cell growth rate (see the Material and Methods section). The yellow dotted line represents 17-DMAG addition. The brown dotted lines are indicative of the cytotoxicity assay results at 24 hours (<bold>B</bold>), 48 hours (<bold>C</bold>) and 72 hours (<bold>D</bold>) after 17-DMAG addition. (<bold>E</bold>) RT-CES cytotoxicity assay of Hsp90 inhibitor, NMS-E973. The yellow dotted line represents NMS-E973 addition. The brown dotted lines are indicative of the cytotoxicity assay results at 24 hours (<bold>F</bold>), 48 hours (<bold>G</bold>) and 72 hours (<bold>H</bold>) after NMS-E973 addition.</p></caption><graphic xlink:href="elife-72626-app1-fig40" position="float"/></fig><fig position="anchor" id="app1fig41"><label>Appendix 1&#x02014;figure 41.</label><caption><title>PI3K/AKT pathway inhibition with different PI3K/AKT inhibitors shows dose-dependent response in LNCaP cell line growth.</title><p>(<bold>A</bold>) Real-time cell electronic sensing (RT-CES) cytotoxicity assay of PI3K/AKT pathway inhibitor, PI-103, that uses the Cell Index as a measurement of the cell growth rate (see the Material and Methods section). The yellow dotted line represents PI-103 addition. The brown dotted lines are indicative of the cytotoxicity assay results at 24 hours (<bold>B</bold>), 48 hours (<bold>C</bold>) and 72 hours (<bold>D</bold>) after PI-103 addition. (<bold>E</bold>) RT-CES cytotoxicity assay of PI3K/AKT pathway inhibitor, Pictilisib. The yellow dotted line represents Pictilisib addition. The brown dotted lines are indicative of the cytotoxicity assay results at 24 hours (<bold>F</bold>), 48 hours (<bold>G</bold>) and 72 hours (<bold>H</bold>) after Pictilisib addition.</p></caption><graphic xlink:href="elife-72626-app1-fig41" position="float"/></fig><table-wrap position="anchor" id="app1keyresource"><label>Appendix 1&#x02014;key resources table</label><table frame="hsides" rules="groups"><thead><tr><th align="left" valign="middle" rowspan="1" colspan="1">Reagent type (species) or resource</th><th align="left" valign="middle" rowspan="1" colspan="1">Designation</th><th align="left" valign="middle" rowspan="1" colspan="1">Source or reference</th><th align="left" valign="middle" rowspan="1" colspan="1">Identifiers</th><th align="left" valign="middle" rowspan="1" colspan="1">Additional information</th></tr></thead><tbody><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">AKT1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:391</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">AKT2</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:392</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">AKT3</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:393</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">AR</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:644</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">CASP8</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:1,509</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">CFLAR</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:1,876</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">EGFR</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:3,236</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">MAPK1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:6,871</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">MAPK3</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:6,877</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">SLC2A1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:11,005</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">HIF1A</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:4,910</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">HSP90AA1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:5,253</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">HSP90AB1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:5,258</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">HSP90B1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:12,028</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">HSPA1A</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:5,232</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">HSPA1B</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:5,233</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">HSPB1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:5,246</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">MAP2K1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:6,840</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">MAP2K2</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:6,842</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">MYC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:7,553</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">MAX</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:6,913</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">CDKN2A</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:1,787</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3CA</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,975</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3CB</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,976</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3CG</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,978</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3CD</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,977</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3R1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,979</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3R2</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,980</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3R3</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,981</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3R4</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,982</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3R5</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:30,035</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3R6</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:27,101</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3C2A</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,971</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3C2B</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,972</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3C2G</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,973</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">PIK3C3</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:8,974</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">NOX1</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:7,889</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">NOX3</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:7,890</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">NOX4</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:7,891</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">NOX2</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:2,578</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">SPOP</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:11,254</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">gene (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">TERT</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC</td><td align="left" valign="middle" rowspan="1" colspan="1">HGNC:11,730</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">cell line (Homo-sapiens)</td><td align="left" valign="middle" rowspan="1" colspan="1">LNCaP clone FGC, prostate carcinoma (normal, Adult)</td><td align="left" valign="middle" rowspan="1" colspan="1">ATCC</td><td align="left" valign="middle" rowspan="1" colspan="1">CRL-1740</td><td align="left" valign="middle" rowspan="1" colspan="1">RRID:<ext-link xlink:href="https://identifiers.org/RRID/RRID:CVCL_1379" ext-link-type="uri">CVCL_1379</ext-link></td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">RPMI 1640 Medium, GlutaMAX Supplement</td><td align="left" valign="middle" rowspan="1" colspan="1">Gibco</td><td align="char" char="ndash" valign="middle" rowspan="1" colspan="1">61870&#x02013;010</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">17-DMAG, an Hsp90 inhibitor</td><td align="left" valign="middle" rowspan="1" colspan="1">Sigma-Aldrich</td><td align="char" char="." valign="middle" rowspan="1" colspan="1">100,069</td><td align="left" valign="middle" rowspan="1" colspan="1">
<xref rid="bib82" ref-type="bibr">Pacey et al., 2011</xref>
</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">NMS-E973, an Hsp90 inhibitor</td><td align="left" valign="middle" rowspan="1" colspan="1">MedChemExpress</td><td align="left" valign="middle" rowspan="1" colspan="1">HY-17547</td><td align="left" valign="middle" rowspan="1" colspan="1">
<xref rid="bib36" ref-type="bibr">Fogliatto et al., 2013</xref>
</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">Pictilisib, an inhibitor of PI3K&#x003b1;/&#x003b4;</td><td align="left" valign="middle" rowspan="1" colspan="1">Thermo Scientific</td><td align="char" char="." valign="middle" rowspan="1" colspan="1">467861000</td><td align="left" valign="middle" rowspan="1" colspan="1">
<xref rid="bib117" ref-type="bibr">Zhan et al., 2017</xref>
</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">PI-103, a multi-targeted PI3K inhibitor for p110&#x003b1;/&#x003b2;/&#x003b4;/&#x003b3;</td><td align="left" valign="middle" rowspan="1" colspan="1">Sigma-Aldrich</td><td align="char" char="." valign="middle" rowspan="1" colspan="1">528,100</td><td align="left" valign="middle" rowspan="1" colspan="1">
<xref rid="bib86" ref-type="bibr">Raynaud et al., 2009</xref>
</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">Resazurin</td><td align="left" valign="middle" rowspan="1" colspan="1">Sigma-Aldrich</td><td align="left" valign="middle" rowspan="1" colspan="1">R7017</td><td align="left" valign="middle" rowspan="1" colspan="1">
<xref rid="bib106" ref-type="bibr">Szebeni et al., 2017</xref>
</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">Dimethyl sulfoxide (DMSO)</td><td align="left" valign="middle" rowspan="1" colspan="1">Sigma-Aldrich</td><td align="left" valign="middle" rowspan="1" colspan="1">D8418</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">Foetal bovine serum (FBS)</td><td align="left" valign="middle" rowspan="1" colspan="1">Gibco</td><td align="char" char="ndash" valign="middle" rowspan="1" colspan="1">16140&#x02013;071</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">chemical compound, drug</td><td align="left" valign="middle" rowspan="1" colspan="1">PenStrep antibiotics (Penicillin G sodium salt, and Streptomycin sulfate salt)</td><td align="left" valign="middle" rowspan="1" colspan="1">Sigma-Aldrich</td><td align="left" valign="middle" rowspan="1" colspan="1">P4333</td><td align="left" valign="middle" rowspan="1" colspan="1"/></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">software, algorithm</td><td align="left" valign="middle" rowspan="1" colspan="1">R language</td><td align="left" valign="middle" rowspan="1" colspan="1">
<ext-link xlink:href="https://www.R-project.org/" ext-link-type="uri">https://www.R-project.org/</ext-link>
</td><td align="left" valign="middle" rowspan="1" colspan="1"/><td align="left" valign="middle" rowspan="1" colspan="1">RRID:<ext-link xlink:href="https://identifiers.org/RRID/RRID:SCR_001905" ext-link-type="uri">SCR_001905</ext-link></td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">software, algorithm</td><td align="left" valign="middle" rowspan="1" colspan="1">Python language</td><td align="left" valign="middle" rowspan="1" colspan="1">
<ext-link xlink:href="https://www.python.org/" ext-link-type="uri">https://www.python.org/</ext-link>
</td><td align="left" valign="middle" rowspan="1" colspan="1"/><td align="left" valign="middle" rowspan="1" colspan="1">RRID:<ext-link xlink:href="https://identifiers.org/RRID/RRID:SCR_008394" ext-link-type="uri">SCR_008394</ext-link></td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">software, algorithm</td><td align="left" valign="middle" rowspan="1" colspan="1">MaBoS</td><td align="left" valign="middle" rowspan="1" colspan="1">
<ext-link xlink:href="https://github.com/maboss-bkmc/MaBoSS-env-2.0" ext-link-type="uri">https://github.com/maboss-bkmc/MaBoSS-env-2.0</ext-link>
</td><td align="left" valign="middle" rowspan="1" colspan="1"/><td align="left" valign="middle" rowspan="1" colspan="1"><xref rid="bib104" ref-type="bibr">Stoll et al., 2017</xref>; <xref rid="bib103" ref-type="bibr">Stoll et al., 2012</xref></td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">software, algorithm</td><td align="left" valign="middle" rowspan="1" colspan="1">High-throughput mutant analysis</td><td align="left" valign="middle" rowspan="1" colspan="1">
<ext-link xlink:href="https://github.com/sysbio-curie/Logical_modelling_pipeline" ext-link-type="uri">https://github.com/sysbio-curie/Logical_modelling_pipeline</ext-link>
</td><td align="left" valign="middle" rowspan="1" colspan="1"/><td align="left" valign="middle" rowspan="1" colspan="1">
<xref rid="bib77" ref-type="bibr">Montagud et al., 2019</xref>
</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">software, algorithm</td><td align="left" valign="middle" rowspan="1" colspan="1">PROFILE</td><td align="left" valign="middle" rowspan="1" colspan="1">
<ext-link xlink:href="https://github.com/sysbio-curie/PROFILE" ext-link-type="uri">https://github.com/sysbio-curie/PROFILE</ext-link>
</td><td align="left" valign="middle" rowspan="1" colspan="1"/><td align="left" valign="middle" rowspan="1" colspan="1">
<xref rid="bib9" ref-type="bibr">B&#x000e9;al et al., 2019</xref>
</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">software, algorithm</td><td align="left" valign="middle" rowspan="1" colspan="1">PROFILE_v2</td><td align="left" valign="middle" rowspan="1" colspan="1">
<ext-link xlink:href="https://github.com/ArnauMontagud/PROFILE_v2" ext-link-type="uri">https://github.com/ArnauMontagud/PROFILE_v2</ext-link>
</td><td align="left" valign="middle" rowspan="1" colspan="1"/><td align="left" valign="middle" rowspan="1" colspan="1">This work. Main text, Section "Personalisation of the prostate Boolean model" and Appendix 1, Sections 3,4,5 and 6.</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">software, algorithm</td><td align="left" valign="middle" rowspan="1" colspan="1">Prostate Boolean model</td><td align="left" valign="middle" rowspan="1" colspan="1"><ext-link xlink:href="https://www.ebi.ac.uk/biomodels/MODEL2106070001" ext-link-type="uri">https://www.ebi.ac.uk/biomodels/MODEL2106070001</ext-link>; <ext-link xlink:href="http://ginsim.org/model/signalling-prostate-cancer" ext-link-type="uri">http://ginsim.org/model/signalling-prostate-cancer</ext-link></td><td align="left" valign="middle" rowspan="1" colspan="1"/><td align="left" valign="middle" rowspan="1" colspan="1">This work. Main text, Section "Boolean model construction" and Appendix 1, Section 1.</td></tr></tbody></table></table-wrap></sec></app></app-group></back><sub-article article-type="editor-report" id="sa0"><front-stub><article-id pub-id-type="doi">10.7554/eLife.72626.sa0</article-id><title-group><article-title>Editor's evaluation</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Flegg</surname><given-names>Jennifer</given-names></name><role specific-use="editor">Reviewing Editor</role><aff>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/01ej9dk98</institution-id><institution>The University of Melbourne</institution></institution-wrap>
<country>Australia</country>
</aff></contrib></contrib-group><ext-link xlink:href="https://sciety.org/articles/activity/10.1101/2021.07.28.454126" ext-link-type="uri" id="sa0ro1"/></front-stub><body><p>This paper presents a mathematical model for prioritizing drugs for prostate cancer patients based on signal network database. The manuscript is of broad interest to the field of oncology and precision medicine. The methodology developed is sophisticated and relevant to real patient prostate cancer data. The predictions from the model are validated in an experimental setting and provide suggestions for the personalisation of prostate cancer treatment. The study can serve as a roadmap for future development of predictive, personalized models.</p></body></sub-article><sub-article article-type="decision-letter" id="sa1"><front-stub><article-id pub-id-type="doi">10.7554/eLife.72626.sa1</article-id><title-group><article-title>Decision letter</article-title></title-group><contrib-group><contrib contrib-type="editor"><name><surname>Flegg</surname><given-names>Jennifer</given-names></name><role>Reviewing Editor</role><aff>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/01ej9dk98</institution-id><institution>The University of Melbourne</institution></institution-wrap>
<country>Australia</country>
</aff></contrib></contrib-group><contrib-group><contrib contrib-type="reviewer"><name><surname>Albert</surname><given-names>R&#x000e9;ka</given-names></name><role>Reviewer</role><aff>
<institution-wrap><institution-id institution-id-type="ror">https://ror.org/04p491231</institution-id><institution>Pennsylvania State University</institution></institution-wrap>
<country>United States</country>
</aff></contrib></contrib-group></front-stub><body><boxed-text id="box1" position="float"><p>Our editorial process produces two outputs: (i) <ext-link xlink:href="https://sciety.org/articles/activity/10.1101/2021.07.28.454126" ext-link-type="uri">public reviews</ext-link> designed to be posted alongside <ext-link xlink:href="https://www.biorxiv.org/content/10.1101/2021.07.28.454126v1" ext-link-type="uri">the preprint</ext-link> for the benefit of readers; (ii) feedback on the manuscript for the authors, including requests for revisions, shown below. We also include an acceptance summary that explains what the editors found interesting or important about the work.</p></boxed-text><p>
<bold>Decision letter after peer review:</bold>
</p><p>Thank you for submitting your article "Patient&#x02013;specific Boolean models of signaling networks guide personalized treatments" for consideration by <italic toggle="yes">eLife</italic>. Your article has been reviewed by 3 peer reviewers, and the evaluation has been overseen by a Reviewing Editor and Aleksandra Walczak as the Senior Editor. The following individual involved in review of your submission has agreed to reveal their identity: R&#x000e9;ka Albert (Reviewer #2).</p><p>The reviewers have discussed their reviews with one another, and the Reviewing Editor has drafted this to help you prepare a revised submission.</p><p>Essential revisions:</p><p>1. The main limitation is that the bioinformatics conclusion was validated using only one cell line experimentally. The authors might consider how to consolidate the general utility of this model.</p><p>2. Another major limitation is that this model points to the existing drug targets as highlighted in the signal databases. Is it possible to identify new drug targets? When there are multiple reagents hitting the same drug target, can it advise which chemical to use? The authors should comment on this limitation.</p><p>3. The authors should comment on the contribution of tumor microenvironment, and whether this model could address this issue since it seems mainly built on signaling in tumor cells.</p><p>4. The author used Monte&#x02013;Carlo kinetic algorithm to generate the time trajectories for the state transition graph and set a maximum time to ensure an asymptotic solution. It would be better to provide more detail of situations if an asymptotic solution cannot be found or such solution can always be found and converge to the same result.</p><p>5. The author has not mentioned enough detail about the transition probability between states (e.g., does the transition probability based on prior knowledge, will the transition probability, will the transition probability change when constructing a personalized Boolean model, and how does it change if it changes). It will be good if the authors can provide a transition probability matrix as an example, and any influence from prior knowledge.</p><p>6. In the supplementary information, "Personalized Boolean model of prostate cell lines" section, the plots (S19/S20) used simulation result from random initial condition, does that mean the result is independent of the choice of initial condition? It would be helpful if the author could analyze the influence of the model output with different initial conditions (e.g., whether it will converge to the same results or different ones).</p><p>7. Why does the cell viability increase with drug (e.g. 2nM) compared to no drug (i.e. 0 nM) in Figure 7? Do the drugs promote proliferation in small doses?</p><p>8. It would be good to test the drug concentration cell viability experiment for larger doses of drug so we can actually see the maximal efficacy of these drugs. It seems for some of the drugs they don't even achieve their half&#x02013;effect in the doses tested. Is there a reason for this choice?</p><p>9. Indicating the general criteria for the logical rules and giving an example in the Appendix would help a lot.</p><p>10. Text queries/suggestions:</p><p>&#x02013; Figure 2, when the authors say phenotypes, is this also the 6 variable outputs that they mention in the text? If so, it might be good to say this in the caption so it's clear to readers.</p><p>&#x02013; It's challenging to really fully grasp the full model, I think the readers would benefit from more reference to appropriate sections of the supplementary material.</p><p>&#x02013; Can you elaborate on how the combined perturbations for already&#x02013;developed drugs was performed? How was the model changed for a given drug combination?</p><p>&#x02013; What is EGF, FGF, TGF etc? Are these acronyms? Since they are "physiological conditions of interest" it might be good for the readers to know what these represent.</p><p>&#x02013; When you say "the final model accounts for 133 nodes and 449 edges" can this be linked to its biological counterpart. i.e. Is this then 133 proteins and 449 protein&#x02013;protein pathways/interactions?</p><p>&#x02013; '&#x02026;such data can only be obtained with non&#x02013;standard procedures such as microfluidics from patients' material ': The authors should make it clear about what kind of information is missing from those data making those mode unavailable.</p><p>&#x02013; The author should clearly specify the number of pathways, genes, and cross&#x02013;talks involved in their models. It is unclear how many components were integrated into the network to obtain the final network containing 133 nodes and 449 edges. Please also specify how many drugs and drug combinations participated in personalised drug prediction. The authors should clarify the number of drug and drug combination instead of the word of " several".</p><p>11. Figures queries/suggestions:</p><p>&#x02013; Figure S1 the text cannot be read; it needs to be larger and the graphic needs to be higher quality.</p><p>&#x02013; Figure 2 should also be much bigger, it's too difficult to make out the blue rectangles and in turn most of the paths are difficult to discern.</p><p>&#x02013; Meaning for acronyms in Figure 4 should be given before they are used, i.e. PCA and GG.</p><p>&#x02013; Provided more details of what is mean by Cell index (a.u.) in Figure 8 and Figure 9 in the caption.</p><p>&#x02013; A figure summarising the combined treatment effects for the different patients with a figure to demonstrate how AKT was the top hit in Gleason Groups 1, 2 and 3 and so on, would be helpful.</p><p>&#x02013; What is the strange dynamic occurring at about 15 hours where the points shift down and then jump back up?</p><p>&#x02013; Figure3: Why there were a sharp increase of several elements before the decay for those output with 0 activities in the final states? Why the kinetics of decaying varied across different nodes? Any interpretation?</p><p>&#x02013; Figure 4: The correlation between the Gleason scores and Proliferation score is not clear by the graphics. Any other means to show this?</p><p>&#x02013; Figure 8/9 BCD/FGH seem redundant with Figure 8/9 A/E. You can combine the two types of figures. Also, there seems a discontinuous segment in Figure8/9A/E. Is it an editing error of images? You may consider integrate them as a whole panel.</p><p>
<italic toggle="yes">Reviewer #1 (Recommendations for the authors):</italic>
</p><p>Below are some queries/comments I have for the authors.</p><p>General queries:</p><p>&#x02013; Why does the cell viability increase with drug (e.g. 2nM) compared to no drug (i.e. 0 nM) in Figure 7? Do the drugs promote proliferation in small doses?</p><p>&#x02013; It would be good to test the drug concentration cell viability experiment for larger doses of drug so we can actually see the maximal efficacy of these drugs. It seems for some of the drugs they don't even achieve their half-effect in the doses tested. Is there a reason for this choice?</p><p>Text queries/suggestions:</p><p>&#x02013; Figure 2, when the authors say phenotypes, is this also the 6 variable outputs that they mention in the text? If so, it might be good to say this in the caption so it's clear to readers</p><p>&#x02013; It's challenging to really fully grasp the full model, I think the readers would benefit from more reference to appropriate sections of the supplementary material</p><p>&#x02013; Can you elaborate on how the combined perturbations for already-developed drugs was performed? How was the model changed for a given drug combination?</p><p>&#x02013; What is EGF, FGF, TGF etc? Are these acronyms? Since they are "physiological conditions of interest" it might be good for the readers to know what these represent</p><p>&#x02013; When you say "the final model accounts for 133 nodes and 449 edges" can this be linked to its biological counterpart. i.e. Is this then 133 proteins and 449 protein-protein pathways/interactions?</p><p>Figures queries/suggestions:</p><p>&#x02013; Figure S1 the text cannot be read; it needs to be larger and the graphic needs to be higher quality</p><p>&#x02013; Figure 2 should also be much bigger, it's too difficult to make out the blue rectangles and in turn most of the paths are difficult to discern.</p><p>&#x02013; Meaning for acronyms in Figure 4 should be given before they are used, i.e. PCA and GG.</p><p>&#x02013; Provided more details of what is mean by Cell index (a.u.) in Figure 8 and Figure 9 in the caption</p><p>&#x02013; A figure summarising the combined treatment effects for the different patients with a figure to demonstrate how AKT was the top hit in Gleason Groups 1, 2 and 3 and so on, would be helpful</p><p>&#x02013; What is the strange dynamic occurring at about 15 hours where the points shift down and then jump back up?</p><p>
<italic toggle="yes">Reviewer #2 (Recommendations for the authors):</italic>
</p><p>Indicating the general criteria for the logical rules and giving an example in the Appendix would help a lot.</p><p>
<italic toggle="yes">Reviewer #3 (Recommendations for the authors):</italic>
</p><p>1. The main limitation is that the bioinformatics conclusion was validated using only one cell line experimentally. The authors might consider how to consolidate the general utility of this model.</p><p>2. Another major limitation is that this model points to the existing drug targets as highlighted in the signal databases. Is it possible to identify new drug targets? When there are multiple reagents hitting the same drug target, can it advise which chemical to use? The authors should comment on this limitation.</p><p>3. The authors should comment on the contribution of tumor microenvironment, and whether this model could address this issue since it seems mainly built on signaling in tumor cells.</p><p>4. The author used Monte-Carlo kinetic algorithm to generate the time trajectories for the state transition graph and set a maximum time to ensure an asymptotic solution. It would be better to provide more detail of situations if an asymptotic solution can not be found or such solution can always be found and converge to the same result.</p><p>5. The author has not mentioned enough detail about the transition probability between states (e.g., does the transition probability based on prior knowledge, will the transition probability, will the transition probability change when constructing a personalized Boolean model, and how does it change if it changes). It will be good if the authors can provide a transition probability matrix as an example, and any influence from prior knowledge.</p><p>6. In the supplementary information, "Personalized Boolean model of prostate cell lines" section, the plots (S19/S20) used simulation result from random initial condition, does that mean the result is independent of the choice of initial condition? It would be helpful if the author could analyze the influence of the model output with different initial conditions (e.g., whether it will converge to the same results or different ones).</p></body></sub-article><sub-article article-type="reply" id="sa2"><front-stub><article-id pub-id-type="doi">10.7554/eLife.72626.sa2</article-id><title-group><article-title>Author response</article-title></title-group></front-stub><body><disp-quote content-type="editor-comment"><p>Essential revisions:</p><p>1. The main limitation is that the bioinformatics conclusion was validated using only one cell line experimentally. The authors might consider how to consolidate the general utility of this model.</p></disp-quote><p>We thank the editor and reviewers for their comments on how to improve the impact and clarity of our work. A convincing validation for patients would be to cross-validate the predictions of the model with already existing personalised treatments. This was done to some extent, with the example of the androgen receptor inhibitors that are more effective in high-grade tumour patients and that are indeed given in the clinics to prostate cancer patients with advanced tumour stages. We hope that a more systematic search will be done on clinical trials that are in progress.</p><p>The experimental validation on the LNCaP cell line proved that the candidates suggested by the personalised model were valid and can be seen as a proof of concept. Ideally, the drug targets would have been validated on more cell lines or more realistic models such as mice, but this validation would require another project, which is in our future plans.</p><disp-quote content-type="editor-comment"><p>2. Another major limitation is that this model points to the existing drug targets as highlighted in the signal databases. Is it possible to identify new drug targets? When there are multiple reagents hitting the same drug target, can it advise which chemical to use? The authors should comment on this limitation.</p></disp-quote><p>The results of our analyses are focused on existing drugs. Because of the size of the model, we chose to filter the "potential targeted nodes" with the list of existing drugs. In theory, we could provide a list of "potential targeted nodes", but we thought that focusing on the available drugs would increase the repurposing motivation of the work.</p><p>For the LNCaP example, we provide a complete list of potential targets without the filter in the supplemental information (Supplementary File 6 and Appendix 1, Section "High-Throughput mutant analysis of the LNCaP model"). This list is a ranked list of the effect of mutations on LNCaP behaviours. In this work, we have focused on mutants that knocked down the activity of the nodes and found drugs that cause this that are approved or in clinical trials (Table 1).</p><p>Even though our methodology can consider and simulate off-target effects of drugs, if these are known a priori and included in the list of potential targets, this work does not evaluate which one of the multiple reagents targeting a single node is better. The introduction of such an evaluation would be a good fit for follow-up work. We have added this to the Discussion.</p><disp-quote content-type="editor-comment"><p>3. The authors should comment on the contribution of tumor microenvironment, and whether this model could address this issue since it seems mainly built on signaling in tumor cells.</p></disp-quote><p>In the present work, we have not modelled the tumour microenvironment (TME) directly. We have only considered its indirect effects by modulating the activity of the model inputs that are representative of the status of the TME (e.g. the presence or absence of growth factors, oxygen, etc.). The focus of the model is intracellular, and it only addresses the effects that these environmental variables have on the signalling networks and how the cell reacts to these effects. We modified the first paragraph of the Results section to clarify this point: "These input nodes have no regulation. Their value is fixed according to the simulated experiment to represent the status of the microenvironmental characteristics (e.g. the presence or absence of growth factors, oxygen, etc.). A more complex multiscale approach would be required to consider the dynamical interaction with other cell types."</p><p>As mentioned in the modified text, we are expanding this work using a multiscale modelling framework (cf. with the tool PhysiBoSS (Letort et al. 2019. Bioinformatics)) to take into account these effects, but this is beyond the scope of the present work.</p><disp-quote content-type="editor-comment"><p>4. The author used Monte&#x02013;Carlo kinetic algorithm to generate the time trajectories for the state transition graph and set a maximum time to ensure an asymptotic solution. It would be better to provide more detail of situations if an asymptotic solution cannot be found or such solution can always be found and converge to the same result.</p></disp-quote><p>The formalism used is based on a Boolean framework with stochastic simulations, which considers that solutions are estimated with probabilities of populations of trajectories, not calculated analytically. Provided that the maximum time is long enough and the number of trajectories high enough, we can ensure that the probabilities of the solutions will end up with similar values. We have proof in the supplementary material of the first article of MaBoSS that states that for the same initial conditions, it will always converge to the same stationary solution (Stoll <italic toggle="yes">et al.</italic> 2012). This is even more verified because MaBoSS uses a different seed for the random generator for each run. We tested several rounds of simulations and confirmed that the obtained probabilities are very close.</p><p>Another issue would be to characterise the types of attractors that we obtain: stable states or limit cycles. If there were some complex attractors such as limit cycles, they would eventually end up in an asymptotic solution anyway. However, another way to measure the "chaos" of the system is to compute the entropy and the transition entropy. In MaBoSS, we added this functionality to assess how deterministic the system is. A typical signature of the existence of a limit cycle is that the entropy is non-zero, whereas the transition entropy is zero, which is the case here. The existence of the limit cycle is confirmed by the probabilities of all the fixed points that do not add to 1. We have added two cells in the notebook (Supplementary File 2) to show these results.</p><disp-quote content-type="editor-comment"><p>5. The author has not mentioned enough detail about the transition probability between states (e.g., does the transition probability based on prior knowledge, will the transition probability, will the transition probability change when constructing a personalized Boolean model, and how does it change if it changes). It will be good if the authors can provide a transition probability matrix as an example, and any influence from prior knowledge.</p></disp-quote><p>The transition probabilities are based on omics data in the case of the personalised models rather than prior knowledge. To be more precise, for the generic prostate model, at first, we set all the probabilities to be equal to explore all the possible behaviours of the model; but for the personalised models, the values for the transition rates will depend on the transcriptomics data. Once they are fixed for a personalised model, they do not change anymore. The way they are set is done through our PROFILE methodology. We have added a short and intuitive description of the method in the Appendix, section "Primer on PROFILE". An example of the obtained transition rates and initial conditions is provided for the LNCaP model. These transition probabilities can be inspected in the corresponding CFG files ($u_NODE and $d_NODE) for the rest of the cell line models and the TCGA patients.</p><disp-quote content-type="editor-comment"><p>6. In the supplementary information, "Personalized Boolean model of prostate cell lines" section, the plots (S19/S20) used simulation result from random initial condition, does that mean the result is independent of the choice of initial condition? It would be helpful if the author could analyze the influence of the model output with different initial conditions (e.g., whether it will converge to the same results or different ones).</p></disp-quote><p>By "random initial condition", we mean that input nodes have a 50% chance of having an active value in the initial conditions. We ran 5000 trajectories and, using the Monte-Carlo kinetic algorithm, ended up with these phenotype scores for each personalised cell line. The choice of initial conditions is critical for the resulting probabilities, as can be seen in Figure 3 of the main text and in the jupyter notebook of the wild type model analysis.</p><p>For Figures S21 and S23, we decided to choose random initial conditions as a levelling ground so that all prostate cell lines would have as many non-zero probabilities as possible in their outputs. To show the effect of the initial conditions on the simulation outputs, we used the same 3 initial conditions from Figure 3 (all initial inputs OFF, growth factors ON and death signals ON) on the 7 prostate cells lines and the non-personalised wild type model (Figure S22). Here we see that different initial conditions can have a drastic effect on the outcome of the simulations: Apoptosis and DNA_Repair are fired up upon death signals are ON, and Proliferation and Invasion are activated upon growth factors are ON.</p><disp-quote content-type="editor-comment"><p>7. Why does the cell viability increase with drug (e.g. 2nM) compared to no drug (i.e. 0 nM) in Figure 7? Do the drugs promote proliferation in small doses?</p></disp-quote><p>This is a problem caused by the resazurin cell viability assay. In these assays, a deviation of 10-15% for in vitro cellular assays is an acceptable variation as it is a fluorescent assay that detects the cellular metabolic activity of living cells. Thus, in our analyses, we consider changes above 1.00 to be the same value as the controls.</p><p>We have added a sentence on the analysis of Figure 7 "This dose-dependent activity is more notable in Hsp90 drugs (NMS-E973,17-DMAG) than in the PI3K/AKT pathway (Pictilisib) ones and very modest for PI-103.".</p><p>We have also added a sentence on these deviations in the Methods section.</p><disp-quote content-type="editor-comment"><p>8. It would be good to test the drug concentration cell viability experiment for larger doses of drug so we can actually see the maximal efficacy of these drugs. It seems for some of the drugs they don't even achieve their half&#x02013;effect in the doses tested. Is there a reason for this choice?</p></disp-quote><p>In our in vitro experiments, we first used the biologically relevant doses (in nanomolar concentrations) that were given in the literature for the respective agents. However, we found that we did not observe a proper dose-response for all substances in the endpoint measurements (resazurin assay, Figure 7). Thus, we repeated the experiments with real-time cytotoxicity assay (RT-CES) with higher drug concentrations (&#x003bc;M), which gave more accurate data with more than a hundred sampling points (Figure 8). The continuous recording of impedance in cells was used as a measurement of the cell growth rate and reflected the effects of different drugs.</p><disp-quote content-type="editor-comment"><p>9. Indicating the general criteria for the logical rules and giving an example in the Appendix would help a lot.</p></disp-quote><p>In general, and unless evidenced otherwise, we join the activators with OR gates and the inhibitors with AND NOT. This way of connecting the incoming arrows of a node is a strong assumption that we make as a first try and without any further knowledge, and which is often applied (Fumia and Martins, 2013), and other methods of Boolean rules' inferences (Lim et al. 2016, BMC Bioinformatics). Usually, the OR links information extracted from different articles. In addition, assigning the NOT gate to inhibitors is a way of increasing their weight so that they are, in fact, inhibiting. In any case, these assumptions are the first approach; if there is any information about any of the components, we modify the Boolean equation accordingly. For instance, if we know that two inhibitors only inhibit when both are present, we include that information and overwrite the previous formula.</p><p>We have included a new section in the Appendix called "Establishing the rules of the Boolean model", where we explain the choices that are made to write the logical rules. We exemplify it with a Boolean toy model.</p><disp-quote content-type="editor-comment"><p>10. Text queries/suggestions:</p><p>&#x02013; Figure 2, when the authors say phenotypes, is this also the 6 variable outputs that they mention in the text? If so, it might be good to say this in the caption so it's clear to readers.</p></disp-quote><p>Yes. The way we constructed the model is such that our outputs of the model are biological phenotypes or can be interpreted as phenotypic read-outs. We have changed the Figure 2 caption and included this description: "and dark blue rectangles [correspond] to outputs that represent biological phenotypes".</p><disp-quote content-type="editor-comment"><p>&#x02013; It's challenging to really fully grasp the full model, I think the readers would benefit from more reference to appropriate sections of the supplementary material.</p></disp-quote><p>We have added references to the supplementary material sections in the main manuscript.</p><disp-quote content-type="editor-comment"><p>&#x02013; Can you elaborate on how the combined perturbations for already&#x02013;developed drugs was performed? How was the model changed for a given drug combination?</p></disp-quote><p>Drugs act as inhibitors of nodes of the models. As we explain in the "Drug simulations in Boolean models" section of Material and Methods, to have nodes that are inhibited at a certain level: "out of 1000 trajectories of the Gillespie algorithm, MaBoSS can simulate 70% of them with an activated AKT (fixed value to 1) and 30% with an inhibited AKT node (fixed value to 0). The phenotype scores for the 1000 trajectories are averaged, and these are considered to be representative of a model with a drug that partially inhibits 30% of the activity of AKT. The same applies for a combined drug inhibition: a simulation of 50% AKT activity and 50% PI3K will have 50% of them with an activated AKT and 50% with an activated PI3K. Combining them, this will lead to 25% of the trajectories with both AKT and PI3K active, 25% with both nodes inactive, 25% with AKT active and 25% with PI3K active."</p><p>The combinations of these single inhibitions were considered as a double inhibition of the model nodes. As explained above, we filtered the inhibited nodes to existing drugs to limit the space of search.</p><disp-quote content-type="editor-comment"><p>&#x02013; What is EGF, FGF, TGF etc? Are these acronyms? Since they are "physiological conditions of interest" it might be good for the readers to know what these represent.</p></disp-quote><p>They are, in fact, acronyms for different growth factors: Epithelial Growth Factor (EGF), Fibroblast Growth Factor (FGF), Transforming Growth Factor &#x003b2; (TGFbeta), Tumour Necrosis Factor &#x003b1; (TNF &#x003b1;).</p><p>We have included these definitions in the manuscript in the Results section "Prostate Boolean model construction" and in the legend of Figure 2.</p><disp-quote content-type="editor-comment"><p>&#x02013; When you say "the final model accounts for 133 nodes and 449 edges" can this be linked to its biological counterpart. i.e. Is this then 133 proteins and 449 protein&#x02013;protein pathways/interactions?</p></disp-quote><p>A node in the model can represent one protein or several ones. "&#x003b2;_catenin" node in the model represents the <italic toggle="yes">CTNNB1</italic> gene, but the "AMPK" node represents genes <italic toggle="yes">PRKAA1</italic>, <italic toggle="yes">PRKAA2</italic>, <italic toggle="yes">PRKAB1</italic>, <italic toggle="yes">PRKAB2</italic>, <italic toggle="yes">PRKAG1</italic>, <italic toggle="yes">PRKAG2</italic>, <italic toggle="yes">PRKAG3</italic>. The correspondence list can be found in the file "Montagud2022_interactions_sources.xlsx" and "Montagud2022_nodes_in_pathways.xlsx" in Supplementary File 1.</p><p>We have added this info in the manuscript in the Materials and methods section, "Boolean model construction".</p><disp-quote content-type="editor-comment"><p>&#x02013; '&#x02026;such data can only be obtained with non&#x02013;standard procedures such as microfluidics from patients' material ': The authors should make it clear about what kind of information is missing from those data making those mode unavailable.</p></disp-quote><p>Our PROFILE_v2 methodology does not use in vitro perturbation experiments such as the ones from Saez-Rodriguez et al. (2009) and Dorier et al. (2016), but rather bulk omics data. The perturbation data does not lack any kind of information to have these personalised models, but we consider that being able to personalise models without needing further experimentation is an asset of our method. In any case, PROFILE_v2 methodology and perturbation tools are compatible and complementary as they use different kinds of data as inputs.</p><p>We have extended the explanation in the Appendix and rewritten these sentences in the last paragraph of the Introduction section with the aim of presenting our claim in a more clear manner: "When summarising the biological knowledge into a network and translating it into logical terms, the obtained model is generic and cannot explain the differences and heterogeneity between patients' responses to treatments. To personalise models and capture these heterogeneities, models can be trained with dedicated perturbation experiments (Dorier et al., 2016; Saez-Rodriguez et al., 2009), but such data can only be obtained doing further experimentation on patients' material using, for instance, non-standard clinical procedures such as microfluidics (Eduati et al., 2020). To address this limitation, we developed a methodology to use different omics data that are more commonly available to personalise generic models to individual cancer patients or cell lines and verified that the obtained models correlated with clinical results such as patient survival information (B&#x000e9;al et al., 2019)."</p><disp-quote content-type="editor-comment"><p>&#x02013; The author should clearly specify the number of pathways, genes, and cross&#x02013;talks involved in their models. It is unclear how many components were integrated into the network to obtain the final network containing 133 nodes and 449 edges. Please also specify how many drugs and drug combinations participated in personalised drug prediction. The authors should clarify the number of drug and drug combination instead of the word of " several".</p></disp-quote><p>Regarding the number of pathways and genes, we have detailed all this information in two different files in the zipped folder Supplementary File 1:</p><p>&#x000ad;&#x02013; Montagud2022_interactions_sources.xlsx details all the connections among nodes, the HUGO names corresponding to the target node, the interaction type, the source node of the interaction, the description of the interaction from the literature, the reference of the paper (in PMID or DOI), and the logical rule of the interaction.</p><p>&#x02013; Montagud2022_nodes_in_pathways.xlsx details how we have organised the nodes in pathways for Figure 1.</p><p>We have added in the "Drug simulations in Boolean models" section of Material and Methods a paragraph to detail the amount of simulations performed: "We simulated the inhibition of 17 nodes of interest. These were the 16 nodes from Table 1 with the addition of the fused AR-ERG (Figures S32 and S33) and their 136 pairwise combinations (Figures S34 and S35). As we used 6 different levels of activity for each node, the resulting Figures S34 and S35 comprise a total of 4998 simulations for each phenotype (136 x 6 x 6 + 17 x 6)."</p><disp-quote content-type="editor-comment"><p>11. Figures queries/suggestions:</p><p>&#x02013; Figure S1 the text cannot be read; it needs to be larger and the graphic needs to be higher quality.</p></disp-quote><p>We improved the readability of the figure by increasing the font for the names of the pathways.</p><disp-quote content-type="editor-comment"><p>&#x02013; Figure 2 should also be much bigger, it's too difficult to make out the blue rectangles and in turn most of the paths are difficult to discern.</p></disp-quote><p>We have improved the readability of the figure by increasing the font and its resolution. We can increase the size of the figure as much as the journal allows for it. In any case, the image is a vectorised SVG that, at least on a screen, can be zoomed-in to visualise in detail.</p><p>In addition, and apart from the SBML, MaBoSS and GINsim file formats, we have included the Cytoscape file of the network in the Supplementary File 1 so that readers can browse it.</p><disp-quote content-type="editor-comment"><p>&#x02013; Meaning for acronyms in Figure 4 should be given before they are used, i.e. PCA and GG.</p></disp-quote><p>Figure 4 caption has been changed to include the definition of these acronyms: "(A) Centroids of the Principal Component Analysis of the samples according to their Gleason grades (GG)".</p><disp-quote content-type="editor-comment"><p>&#x02013; Provided more details of what is mean by Cell index (a.u.) in Figure 8 and Figure 9 in the caption.</p></disp-quote><p>The Cell Index definition has been improved on the Material and Methods section, and we have added a reference to a technical paper on it: "Continuous recording of impedance in cells was used as a measurement of the cell growth rate and reflected by the Cell Index value [https://doi.org/10.1089/adt.2004.2.363]".</p><p>Figure 8 and 9 captions have been changed: "(A) Real-time cell electronic sensing (RT-CES) cytotoxicity assay of Hsp90 inhibitor, 17-DMAG, that uses the Cell Index as a measurement of the cell growth rate (see the Material and Methods section)".</p><disp-quote content-type="editor-comment"><p>&#x02013; A figure summarising the combined treatment effects for the different patients with a figure to demonstrate how AKT was the top hit in Gleason Groups 1, 2 and 3 and so on, would be helpful.</p></disp-quote><p>We have extended Figures S19 (previous S17) and S20 (previous S18) with panels for each GG to show the top hits disaggregated by the Gleason grade. This also allows readers to visually inspect the results from the section "Personalised drug predictions of TCGA Boolean models".</p><disp-quote content-type="editor-comment"><p>&#x02013; What is the strange dynamic occurring at about 15 hours where the points shift down and then jump back up?</p></disp-quote><p>This was a technical problem with the RT-CES 96-well E-plate reader. At that time, there was a short blackout in our laboratory, and the reader detected a minor voltage fluctuation while the uninterruptible power supply (UPS) was switched on. Thus, these differences are consistent across all samples and replicates: all wild type and drug reads decrease at that time point, except Pictilisib that slightly increases. We could have removed these data points as technical problems but considered that it was better to be transparent, and the overall dynamic was not affected.</p><p>We have included a note on the corresponding Methods section to state this explicitly: "Note that around hour 15 our RT-CES reader had a technical problem caused by a short blackout in our laboratory and the reader detected a minor voltage fluctuation while the uninterruptible power supply (UPS) was switched on. This caused differences that are consistent across all samples and replicates: all wild type and drug reads decrease at that time point, except Pictilisib that slightly increases. For the sake of transparency and as the overall dynamic was not affected, we decided to not remove these readings."</p><disp-quote content-type="editor-comment"><p>&#x02013; Figure3: Why there were a sharp increase of several elements before the decay for those output with 0 activities in the final states? Why the kinetics of decaying varied across different nodes? Any interpretation?</p></disp-quote><p>The stochastic Boolean simulations were done imposing some initial values for input nodes but leaving the activation of internal nodes as random (output nodes are always set to OFF). The different initial conditions cause that at early times the internal nodes that were ON activate some downstream nodes, causing some output nodes to have a non-zero probability. These transient activations are damped upon the first few updates of the model state.</p><p>In addition, we know that there is a cycle in this model that causes trajectories to have transient behaviours that are eventually attracted by a stable asymptotic solution.</p><disp-quote content-type="editor-comment"><p>&#x02013; Figure 4: The correlation between the Gleason scores and Proliferation score is not clear by the graphics. Any other means to show this?</p></disp-quote><p>We were hesitant to use any statistical conclusion on the distribution graphics because it is indeed difficult to differentiate among distributions. We observed some trends, and this is what we reported in the main text: "found that the density of high Proliferation score (close to 1, Figure 4B) tends to increase as the Gleason score increases (from low to intermediate to high)".</p><p>We have not found a good way to conclude with confidence that the Gleason grades correlate with the Proliferation or Apoptosis scores as we do not really quantify them. Note that our analyses using the phenotype scores are semi-quantitative: they make sense when compared to a reference value (as in the wild type model against the personalised cell line models) or when comparing groups (as in the TCGA patients and their GG).</p><p>Nevertheless, and as per the reviewer's request, we have studied these distributions statistically by using the Kruskal-Wallis rank-sum test to identify if the phenotype distributions across 3- and 5-stage GG could originate from different distributions. Then and only if the Kruskal-Wallis test was significant, we used Dunn's nonparametric pairwise multiple comparisons test to identify which pairs of groups were statistically different. We saw that <italic toggle="yes">Apoptosis</italic> distributions were significantly different (in 3- and 5-stage GG) as well as <italic toggle="yes">Proliferation</italic> and <italic toggle="yes">Invasion</italic> (only in 3-stage GG).</p><p>We have included these analyses in the main text and in Appendix 1, Section 4.1.</p><disp-quote content-type="editor-comment"><p>&#x02013; Figure 8/9 BCD/FGH seem redundant with Figure 8/9 A/E. You can combine the two types of figures. Also, there seems a discontinuous segment in Figure8/9A/E. Is it an editing error of images? You may consider integrate them as a whole panel.</p></disp-quote><p>Figure 8 BCD are zoom-ins of A, and Figure 9 FGH are zoom-ins of E, as we explain in the caption using the brown lines at the 24, 48 and 72 hours marks. The information is the same but shows the results differently to allow readers to compare better the different concentrations of drugs. Nevertheless, we have removed the snippets (BCD and FGH panels) and left the main figures (A and E). The former figures 8 and 9 are now in the Appendix 1, as Figures S38 and S39.</p><p>The discontinuous segment is at the 15 hours mark, and, as explained three questions above, we think it is a technical problem that, for the sake of transparency, we would rather not remove. We have added a note in this regard in the methods section.</p><disp-quote content-type="editor-comment"><p>Reviewer #1 (Recommendations for the authors):</p><p>Below are some queries/comments I have for the authors.</p><p>General queries:</p><p>&#x02013; Why does the cell viability increase with drug (e.g. 2nM) compared to no drug (i.e. 0 nM) in Figure 7? Do the drugs promote proliferation in small doses?</p><p>&#x02013; It would be good to test the drug concentration cell viability experiment for larger doses of drug so we can actually see the maximal efficacy of these drugs. It seems for some of the drugs they don&#x02019;t even achieve their half-effect in the doses tested. Is there a reason for this choice?</p><p>Text queries/suggestions:</p><p>&#x02013; Figure 2, when the authors say phenotypes, is this also the 6 variable outputs that they mention in the text? If so it might be good to say this in the caption so it&#x02019;s clear to readers</p><p>&#x02013; It&#x02019;s challenging to really fully grasp the full model, I think the readers would benefit from more reference to appropriate sections of the supplementary material</p><p>&#x02013; Can you elaborate on how the combined perturbations for already-developed drugs was performed? How was the model changed for a given drug combination?</p><p>&#x02013; What is EGF, FGF, TGF etc? Are these acronyms? Since they are &#x0201c;physiological conditions of interest&#x0201d; it might be good for the readers to know what these represent</p><p>&#x02013; When you say &#x0201c;the final model accounts for 133 nodes and 449 edges&#x0201d; can this be linked to it&#x02019;s biological counterpart. i.e. Is this then 133 proteins and 449 protein-protein pathways/interactions?</p><p>Figures queries/suggestions:</p><p>&#x02013; Figure S1 the text cannot be read; it needs to be larger and the graphic needs to be higher quality.</p><p>&#x02013; Figure 2 should also be much bigger, it&#x02019;s too difficult to make out the blue rectangles and in turn most of the paths are difficult to discern.</p><p>&#x02013; Meaning for acronyms in Figure 4 should be given before they are used, i.e. PCA and GG.</p><p>&#x02013; Provided more details of what is mean by Cell index (a.u.) in Figure 8 and Figure 9 in the caption.</p><p>&#x02013; A figure summarising the combined treatment effects for the different patients with a figure to demonstrate how AKT was the top hit in Gleason Groups 1, 2 and 3 and so on, would be helpful.</p><p>&#x02013; What is the strange dynamic occurring at about 15 hours where the points shift down and then jump back up?</p></disp-quote><p>These questions were addressed in the "Essential revision" section.</p><disp-quote content-type="editor-comment"><p>Reviewer #2 (Recommendations for the authors):</p><p>Indicating the general criteria for the logical rules and giving an example in the Appendix would help a lot.</p></disp-quote><p>This question was addressed in the Essential revision section.</p><disp-quote content-type="editor-comment"><p>Reviewer #3 (Recommendations for the authors):</p><p>1. The main limitation is that the bioinformatics conclusion was validated using only one cell line experimentally. The authors might consider how to consolidate the general utility of this model.</p><p>2. Another major limitation is that this model points to the existing drug targets as highlighted in the signal databases. Is it possible to identify new drug targets? When there are multiple reagents hitting the same drug target, can it advise which chemical to use? The authors should comment on this limitation.</p><p>3. The authors should comment on the contribution of tumor microenvironment, and whether this model could address this issue since it seems mainly built on signaling in tumor cells.</p><p>4. The author used Monte-Carlo kinetic algorithm to generate the time trajectories for the state transition graph and set a maximum time to ensure an asymptotic solution. It would be better to provide more detail of situations if an asymptotic solution can not be found or such solution can always be found and converge to the same result.</p><p>5. The author has not mentioned enough detail about the transition probability between states (e.g., does the transition probability based on prior knowledge, will the transition probability, will the transition probability change when constructing a personalized Boolean model, and how does it change if it changes). It will be good if the authors can provide a transition probability matrix as an example, and any influence from prior knowledge.</p><p>6. In the supplementary information, "Personalized Boolean model of prostate cell lines" section, the plots (S19/S20) used simulation result from random initial condition, does that mean the result is independent of the choice of initial condition? It would be helpful if the author could analyze the influence of the model output with different initial conditions (e.g., whether it will converge to the same results or different ones).</p></disp-quote><p>These questions were addressed in the "Essential revision" section.</p></body></sub-article></article>