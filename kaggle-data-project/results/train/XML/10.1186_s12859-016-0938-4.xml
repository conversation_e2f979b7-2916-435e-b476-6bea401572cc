<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 2?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">4751710</article-id><article-id pub-id-type="publisher-id">938</article-id><article-id pub-id-type="doi">10.1186/s12859-016-0938-4</article-id><article-categories><subj-group subj-group-type="heading"><subject>Software</subject></subj-group></article-categories><title-group><article-title>Seqinspector: position-based navigation through the ChIP-seq data landscape to identify gene expression regulators</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-2468-151X</contrib-id><name><surname>Piechota</surname><given-names>Marcin</given-names></name><address><phone>(+4812) 6623259</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Korostynski</surname><given-names>Michal</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Ficek</surname><given-names>Joanna</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Tomski</surname><given-names>Andrzej</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Przewlocki</surname><given-names>Ryszard</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1">Department of Molecular Neuropharmacology, Institute of Pharmacology Polish Academy of Sciences, Krakow, 31-344 Poland </aff></contrib-group><pub-date pub-type="epub"><day>12</day><month>2</month><year>2016</year></pub-date><pub-date pub-type="pmc-release"><day>12</day><month>2</month><year>2016</year></pub-date><pub-date pub-type="collection"><year>2016</year></pub-date><volume>17</volume><elocation-id>85</elocation-id><history><date date-type="received"><day>25</day><month>3</month><year>2015</year></date><date date-type="accepted"><day>5</day><month>2</month><year>2016</year></date></history><permissions><copyright-statement>&#x000a9; Piechota et al. 2016</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>The regulation of gene expression in eukaryotic cells is a complex process that involves epigenetic modifications and the interaction of DNA with multiple transcription factors. This process can be studied with unprecedented sensitivity using a combination of chromatin immunoprecipitation and next-generation DNA sequencing (ChIP-seq). Available ChIP-seq data can be further utilized to interpret new gene expression profiling experiments.</p></sec><sec><title>Results</title><p>Here, we describe seqinspector, a tool that accepts any set of genomic coordinates from ChIP-seq or RNA-seq studies to identify shared transcriptional regulators. The presented web resource includes a large collection of publicly available ChIP-seq and RNA-seq experiments (&#x0003e;1300 tracks) performed on transcription factors, histone modifications, RNA polymerases, enhancers and insulators in humans and mice. Over-representation is calculated based on the coverage computed directly from indexed files storing ChIP-seq data (bigwig). Therefore, seqinspector is not limited to pre-computed sets of gene promoters.</p></sec><sec><title>Conclusion</title><p>The tool can be used to identify common gene expression regulators for sets of co-expressed transcripts (including miRNAs, lncRNAs or any novel unannotated RNAs) or for sets of ChIP-seq peaks to identify putative protein-protein interactions or transcriptional co-factors. The tool is available at <ext-link ext-link-type="uri" xlink:href="http://seqinspector.cremag.org">http://seqinspector.cremag.org</ext-link>.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s12859-016-0938-4) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>ChIP-seq</kwd><kwd>RNA-seq</kwd><kwd>Microarray</kwd><kwd>Gene expression</kwd><kwd>Promoter analysis</kwd><kwd>Transcription factor</kwd></kwd-group><funding-group><award-group><funding-source><institution>Polish National Science Centre</institution></funding-source><award-id>2011/01/N/NZ2/04827</award-id><principal-award-recipient><name><surname>Piechota</surname><given-names>Marcin</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution>Polish National Science Centre</institution></funding-source><award-id>2011/03/D/NZ3/01686</award-id><principal-award-recipient><name><surname>Korostynski</surname><given-names>Michal</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution>PL-GRID</institution></funding-source><award-id>plgifpan</award-id><principal-award-recipient><name><surname>Piechota</surname><given-names>Marcin</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution>Institute of Pharmacology</institution></funding-source><award-id>Statutory funds</award-id><principal-award-recipient><name><surname>Piechota</surname><given-names>Marcin</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2016</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>The regulation of gene expression is one of the most complex processes in living organisms [<xref ref-type="bibr" rid="CR1">1</xref>]. This process is a result of interactions between core transcription factors, additional regulators and chromatin architecture [<xref ref-type="bibr" rid="CR2">2</xref>]. Coupling chromatin immunoprecipitation with massive parallel sequencing (ChIP-seq) has provided a breakthrough in the analysis of gene-expression machinery. ChIP-seq data released by the Encyclopedia of DNA Elements consortium (ENCODE) [<xref ref-type="bibr" rid="CR3">3</xref>] and other researchers have made it possible to analyze the functional regulatory genome on an unprecedented scale. The utilization of available ChIP-seq results can provide insights into multiple lines of investigation, including the search for molecular factors involved in the control of coordinated expression of a particular set of genes as well as for protein-protein interactions within peaks resulting from new ChIP-seq experiments. Such analyses require statistically oriented software to handle large genomic data in a computationally efficient manner.</p><p>Here, we present seqinspector, a bioinformatics service that utilizes up-to-date ChIP-seq data. Our tool allows the functional enrichment of user-defined lists of genes, transcripts and ChIP-seq peaks. The user is not limited to a predefined set of promoter regions and can use any set of genomic coordinates. Our tool provides solutions for multiple challenges that have arisen with new sequencing technologies. The first difficulty encountered in interpreting RNA-seq experimental results is that the output encodes multiple novel noncoding or un-annotated transcripts that are not represented in current databases. Another challenge that is often faced is the compatibility of the tool with dynamic and continuously updated genome annotations. Additionally, the tool should provide users the opportunity to search for protein binding sites in regions outside of gene promoters. For example, users might want to search for specific proteins that bind to the introns of differentially spliced genes or analyze peak regions obtained from new ChIP-seq experiments. Therefore, we eschewed the standard approach that uses gene annotations directly or converts genomic coordinates to gene annotations. Instead, our tool converts gene annotations into genomic ranges. For this purpose, we developed a new strategy for data storage, raw genomic bigwig tracks instead of an SQL system. These tracks are analyzed every time the user inputs a query. This method allowed us to develop a new coverage-based approach to calculate enrichment statistics.</p></sec><sec id="Sec2"><title>Implementation</title><sec id="Sec3"><title>Storage of large collections of raw genomic files</title><p>Next-generation sequencing generates vast amounts of data. The results are typically presented as signal information (a per-base estimate across the genome) and as discrete elements (regions computationally identified as enriched from the signal). The most useful file formats for storing ChIP-seq experimental results are bigwig and bigbed for signal information and discrete elements (peaks), respectively [<xref ref-type="bibr" rid="CR4">4</xref>]. Both formats are binary and indexed, and both can be used in the same way as tables in database systems. Therefore, we developed a database core using the bigwig file format instead of the SQL database system. We used a client based on the source code from the Integrative Genomics Viewer (IGV) genome browser to navigate bigwig files [<xref ref-type="bibr" rid="CR5">5</xref>]. This approach, if parallelized, allows one to browse through multiple coverage tracks in a relatively short amount of time. Moreover, this strategy allows us to utilize raw data downloaded from various resources without requiring data transformation or reformatting. At the time of writing this manuscript, the data collection contains 463 tracks from mice and 697 tracks from humans. These data have been downloaded from various sources including ENCODE [<xref ref-type="bibr" rid="CR3">3</xref>], FANTOM5 [<xref ref-type="bibr" rid="CR6">6</xref>] (345 tracks), Gene Expression Omnibus (GEO) [<xref ref-type="bibr" rid="CR7">7</xref>] (113 tracks) and the Short Read Archive (SRA) (11 tracks).</p></sec><sec id="Sec4"><title>A statistical test for the over-representation of ChIP-seq defined DNA features</title><p>The decision to choose a storage method between raw coverage (bigwig files) and peaks derived from ChIP-seq data (bigbed files) was critical to further implementing the system. Despite being computationally challenging, raw coverage files were favored because they can be directly used in visual explorations. Therefore, we used raw coverage files to store the data. This process required a different statistical approach than commonly implemented in over-representation analyses that assume a hypergeometric data distribution [<xref ref-type="bibr" rid="CR8">8</xref>&#x02013;<xref ref-type="bibr" rid="CR10">10</xref>]. We decided to use a parametric statistical test that allows a comparison between the coverage means (length-normalized and log-transformed) from genomic coordinates between different samples. We implemented a two-sample <italic>t</italic>-test to compare the ChIP-seq track coverages derived from two samples of genomic ranges that were provided by the user and the background set. By default, the background set consists of 1000 random gene promoters and can be replaced by the user-defined set. A statistic based on the parametric z-score is used for a single-gene exploration.</p></sec><sec id="Sec5"><title>Seqinspector user interface</title><p>Based on previous experience with developing user interfaces for bioinformatics resources [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR12">12</xref>], we decided to minimize the number of possible options. The user must select the genome assembly (between <italic>Mus musculus</italic> mm9 and mm10 and <italic>Homo sapiens</italic> hg19) and the query input (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1a</xref>). The server automatically recognizes the type and identification format. Some of the most popular identification types are acceptable (Ensembl transcript ID, RefSeq mRNA and gene symbol). The user can also input a genomic range in both bed and genomic coordinate formats. Moreover, it is possible to input multiple gene sets individually and use one of them as the background. The selected gene lists can be compared with the background set using the &#x0201c;Statistics&#x0201d; button. As a result, the user will obtain a list of ChIP-seq tracks sorted by statistical enrichment. The following columns are present in the output table: (1) Track name&#x02014;internal identifier of the track containing a short name of the transcription factor; (2) Query&#x02014;average coverage in the query set; (3) Background&#x02014;average coverage of the reference set; (4) Fold difference&#x02014;fold difference between the query and reference; (5) <italic>P</italic>-value&#x02014;the significance of the difference between the query and reference datasets (calculated by <italic>t</italic>-test); (6) Bonferroni&#x02014;Bonferroni-corrected <italic>P</italic>-value; (7) Stack plot&#x02014;stacked plots presenting the distribution of coverages in all query sets with respective <italic>p</italic>-values; (8) Histogram&#x02013; a visualization of the average coverage (2000&#x000a0;bp around the center of the genomic interval) for all query sets; (9) Genes&#x02014;genomic intervals in the query set, symbols for nearest genes and coverage for these intervals; and (10) Description&#x02014;description of the track.<fig id="Fig1"><label>Fig. 1</label><caption><p>
<bold>a</bold> Output of seqinspector showing the list of dexamethasone-regulated genes (input box on the upper-left), over-represented ChIP-seq tracks (<italic>bottom-left</italic>) and a stack plot with the distribution of coverages in the reference and query sets. <bold>b</bold> Output of a single-gene inspection showing the ChIP-seq tracks over-represented at the Fos gene promoter</p></caption><graphic xlink:href="12859_2016_938_Fig1_HTML" id="MO1"/></fig></p></sec></sec><sec id="Sec6"><title>Results</title><sec id="Sec7"><title>Common regulators of co-expressed genes</title><p>seqinspector can be used for different types of analyses. One is to study clusters of co-expressed genes to find their putative molecular regulators. To demonstrate this functionality, we utilized the results from gene expression profiling of mouse astroglial primary cultures treated with dexamethasone [<xref ref-type="bibr" rid="CR13">13</xref>]. Dexamethasone is an agonist of a nuclear transcription factor, the glucocorticoid receptor (GR). The list of 24 gene symbols for dexamethasone-regulated transcripts was submitted to seqinspector (<ext-link ext-link-type="uri" xlink:href="http://seqinspector.cremag.org">seqinspector.cremag.org</ext-link>) (see Additional file <xref rid="MOESM1" ref-type="media">1</xref>). The genome assembly was set as <italic>Mus musculus</italic> (mm9), and the default background was used. seqinterpreter correctly identified GR (<italic>P</italic>&#x02009;=&#x02009;0.0024; Bonferroni corrected) as a true-positive regulator of the genes from the list (see Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>) and provided a profile of the GR binding sites for each analyzed gene.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Top five enriched ChIP-seq tracks in the promoters of genes regulated by dexamethasone</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Track ID</th><th>Transcription factor</th><th>Query coverage</th><th>Background coverage</th><th>
<italic>P</italic>-value</th><th>Bonferroni-corrected <italic>P</italic>-value</th></tr></thead><tbody><tr><td>GR_04</td><td>Glucocorticoid receptor</td><td char="." align="char">1.3</td><td char="." align="char">0.59</td><td>6.7&#x02009;&#x000d7;&#x02009;10<sup>-6</sup>
</td><td char="." align="char">
<bold>0.0024</bold>
</td></tr><tr><td>GR_03</td><td>Glucocorticoid receptor</td><td char="." align="char">1.3</td><td char="." align="char">0.64</td><td>2.2&#x02009;&#x000d7;&#x02009;10<sup>-5</sup>
</td><td char="." align="char">
<bold>0.008</bold>
</td></tr><tr><td>MYOG_01</td><td>Myogenin</td><td char="." align="char">0.096</td><td char="." align="char">0.059</td><td>0.0017</td><td char="." align="char">0.59</td></tr><tr><td>TCF3_01</td><td>Transcription factor 3</td><td char="." align="char">0.058</td><td char="." align="char">0.038</td><td>0.0018</td><td char="." align="char">0.63</td></tr><tr><td>FOSL1_01</td><td>Fos-related antigen 1</td><td char="." align="char">0.021</td><td char="." align="char">0.016</td><td>0.0093</td><td char="." align="char">1.0</td></tr></tbody></table><table-wrap-foot><p>Only the over-representation of GR-tracks was found statistically significant&#x000a0;(Bonferroni corrected&#x000a0;<italic>P</italic>&#x000a0;&#x0003c;&#x02009;0.05, typed in bold)</p></table-wrap-foot></table-wrap></p></sec><sec id="Sec8"><title>Protein-protein interactions</title><p>seqinterpreter can be applied to study protein-protein interactions. seqinterpreter calculates the average coverage for all stored ChIP-seq tracks for the submitted genomic ranges. The obtained coverages are then compared with a reference dataset using a two-sample <italic>t</italic>-test followed by correction for multiple testing. To demonstrate this functionality, we utilized data from a ChIP-seq analysis of SP1 binding in GM12878 human lymphoblastoid cells (data available at GEO GSM803363). We submitted the top 358 peaks (&#x0003e;3000 signal value) from this dataset to seqinterpreter as genomic ranges in bed format (Additional file <xref rid="MOESM2" ref-type="media">2</xref>). We submitted the lowest 358 peaks as background. We used the <italic>Homo sapiens</italic> hg19 assembly. The transcription factors ATF3, SP2, NFYA, NFYB, E2F4, IRF1 and SRF indirectly bind to SP1 binding sites [<xref ref-type="bibr" rid="CR14">14</xref>]. seqinspector correctly identified the enrichment of the following factors as interacting proteins: (1) ATF3 (<italic>P</italic>&#x02009;=&#x02009;0.013), (2) SP2 (<italic>P</italic>&#x02009;=&#x02009;1.7&#x02009;&#x000d7;&#x02009;10<sup>-33</sup>), (3) NFYA (<italic>P</italic>&#x02009;=&#x02009;8.9&#x02009;&#x000d7;&#x02009;10<sup>-47</sup>), (4) NFYB (<italic>P</italic>&#x02009;=&#x02009;9.6&#x02009;&#x000d7;&#x02009;10<sup>-41</sup>), (5) E2F4 (<italic>P</italic>&#x02009;=&#x02009;1.8&#x02009;&#x000d7;&#x02009;10<sup>-16</sup>), (6) IRF1 (<italic>P</italic>&#x02009;=&#x02009;2.2&#x02009;&#x000d7;&#x02009;10<sup>-7</sup>) and (7) SRF (<italic>P</italic>&#x02009;=&#x02009;1.2&#x02009;&#x000d7;&#x02009;10<sup>-6</sup>). The tool identified also other transcription factors binding to the same sites, including IRF3 (<italic>P</italic>&#x02009;=&#x02009;3.0&#x02009;&#x000d7;&#x02009;10<sup>-43</sup>), C-FOS (<italic>P</italic>&#x02009;=&#x02009;4.0&#x02009;&#x000d7;&#x02009;10<sup>-42</sup>) and CHD2 (<italic>P</italic>&#x02009;=&#x02009;6.8&#x02009;&#x000d7;&#x02009;10<sup>-41</sup>). All of the presented <italic>P</italic>-values are Bonferroni corrected.</p></sec><sec id="Sec9"><title>Cell type enrichment</title><p>Another straightforward application of seqinspector is the study of transcript expression in various tissue and cell types. For this purpose, tracks generated by the FANTOM5 project using cap analysis of gene expression were added to the seqinspector database (264 tracks for human and 81 for mouse after manual curation). This type of analysis reveals active transcription start sites and gene variants expressed in particular cell populations. List of genes or transcripts derived from microarray or RNA-seq profiling experiments can be inspected for cell-type-specific gene expression. To provide an example of this utility, we used results of gene expression profiles in different cellular compartments of the nervous system [<xref ref-type="bibr" rid="CR15">15</xref>]. Submission of neuron- and astrocyte-specific lists of genes (Additional file <xref rid="MOESM3" ref-type="media">3</xref>) confirmed cell-type enrichment and indicated which transcriptional start sites are utilized in these two cell populations (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref>). For astrocyte-specific genes, only one significantly over-represented track was noted - the Hippocampal Astrocytes CAGE track generated by the FANTOM5 consortium (CNhs12129.11709-123B8, <italic>P</italic>&#x02009;=&#x02009;0.0034). For neuron-specific genes, 14 over-represented tracks were noted, including 13 FANTOM5 tracks generated from neural tissue or isolated neurons (e.g., Olfactory brain, CNhs10489.18-22I9, <italic>P</italic>&#x02009;=&#x02009;2.4 &#x000d7; 10<sup>-5</sup> and Raphe neurons, CNhs12631.11722-123D3, <italic>P</italic>&#x02009;=&#x02009;3.1 &#x000d7; 10<sup>-4</sup>) and one ENCODE track for the neuron-restrictive silencer factor (NRSF, GSM915175, <italic>P</italic>&#x02009;=&#x02009;0.026).<fig id="Fig2"><label>Fig. 2</label><caption><p>Identification of cell-type-specific transcript expression start site. The plots display the mean coverage for the selected gene sets in various cell types based on the CAGE data. The x-axis represents the genomic region around transcription start sites from 5&#x02019; to 3&#x02019;. The y-axis represents the coverage that has been normalized to the number of aligned tags per million. <bold>a</bold> Coverage histograms for neuron-specific (<italic>blue</italic>) and astrocyte-specific (<italic>red</italic>) gene lists from Zhang et al. [<xref ref-type="bibr" rid="CR15">15</xref>]. The upper panel displays the mean coverage of raphe neurons CAGE tags, whereas the bottom histogram refers to the hippocampal astrocyte CAGE tags. <bold>b</bold> Coverage histograms for genes up-regulated (<italic>blue</italic>) and down-regulated (<italic>red</italic>) in ruptured intracranial aneurysm from Pera et al. [<xref ref-type="bibr" rid="CR16">16</xref>]. The upper panel presents the mean coverage of neutrophil CAGE tags, whereas the bottom (average) histogram refers to CD8+ T lymphocytes CAGE tags</p></caption><graphic xlink:href="12859_2016_938_Fig2_HTML" id="MO2"/></fig></p><p>Another possible use of seqinspector is to estimate the distribution of transcriptional alterations among various cell populations, which may be estimated from results of gene expression profiling in a heterogeneous tissue. To demonstrate this functionality, we used a list of genes from expression profiling in whole-blood samples obtained from patients after ruptured intracranial aneurysms and a control group (Additional file <xref rid="MOESM4" ref-type="media">4</xref>) [<xref ref-type="bibr" rid="CR16">16</xref>]. The lists of up-regulated and down-regulated genes were compared against each other. seqinspector identified over-representation of CD8+ T-lymphocyte-specific transcripts among the down-regulated genes (CNhs12178.12191.129B4, <italic>P</italic>&#x02009;=&#x02009;0.005) and neutrophil-specific genes among the up-regulated genes (CNhs10862.11233.116C9, <italic>P</italic>&#x02009;=&#x02009;0.12) (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2b</xref>). All of the presented <italic>P</italic>-values are Bonferroni corrected.</p></sec><sec id="Sec10"><title>Comparison with other tools</title><p>To demonstrate the effectiveness of seqinspector, we compared this tool with available ChIP-seq data-based online tools (CSCAN [<xref ref-type="bibr" rid="CR10">10</xref>], ENCODE ChIP-Seq Significance Tool [<xref ref-type="bibr" rid="CR17">17</xref>] and Enrichr with ENCODE ChIP-seq and ChIP-x gene set libraries [<xref ref-type="bibr" rid="CR18">18</xref>]) as well as tools based on <italic>in silico</italic> predicted transcription factor binding sites (oPOSSUM 3.0 [<xref ref-type="bibr" rid="CR19">19</xref>] and Cremag [<xref ref-type="bibr" rid="CR12">12</xref>]). For this purpose, we used the following five example sets of genes regulated by various transcriptional mechanisms. One from each tool excluding Enrichr (no list of genes with specified transcriptional factor was provided with this tool): (a) an example gene set provided in this paper&#x02014;GR-dependent genes regulated in mouse; (b) a list of human genes regulated by dexamethasone from the ENCODE ChIP-Seq Significance Tool website; (c) BDP1 target gene set from the CSCAN website; (d) liver-specific gene set from the oPOSSUM 3.0 website and (e) a list of genes regulated by SRF from the Cremag website. We converted the original lists into Ensembl Transcript IDs and gene symbols using Biomart [<xref ref-type="bibr" rid="CR20">20</xref>] to meet the tool-specific input requirements. We used the default settings for all of the tools for the comparison. As a score, we used the rank of the expected transcription factor in the obtained results, where the points one to ten were awarded with a maximum given for the first position on the list (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>). seqinspector received the highest summary score (score&#x02009;=&#x02009;40) followed by Enrichr using ENCODE data (score&#x02009;=&#x02009;37) and oPOSSUM 3.0 (score&#x02009;=&#x02009;27). Thus, the seqinspector tool, which is based on parametric statistics for enrichment calculation, was comparable to other promoter analysis methods. All of the gene sets with the original IDs, RefSeq numbers, promoter sequences and results are provided in Additional file <xref rid="MOESM5" ref-type="media">5</xref>.<fig id="Fig3"><label>Fig. 3</label><caption><p>Comparison of seqinspector to other online tools. The heatmap presents the scores from seqinspector, Enrichr, oPOSSUM, Cremag, CSCAN and ENCODE ChIP-Seq Significance Tool (in columns) for five selected gene sets (in rows). The scores were calculated based on the rank in the results of the expected transcription factor (<italic>on the right</italic>) with ten points for first rank (<italic>dark green color</italic>), nine points for second and down to one point for the tenth rank (<italic>white color</italic>). The sum of the scores is presented at the bottom. The tools are ordered by the sum of their scores in decreasing order</p></caption><graphic xlink:href="12859_2016_938_Fig3_HTML" id="MO3"/></fig></p></sec></sec><sec id="Sec11"><title>Discussion</title><p>Comparative genomic studies suggest that 3 to 8&#x000a0;% of the sequence information in a genome is evolutionarily conserved among mammals [<xref ref-type="bibr" rid="CR21">21</xref>]. Earlier <italic>in silico</italic> approaches to predict transcription factor binding sites were limited to those specific regions [<xref ref-type="bibr" rid="CR22">22</xref>]. However, the ENCODE project found that the vast majority (~80&#x000a0;%) of the genome can be annotated with RNA- or chromatin-associated features (3). Therefore, the level of potentially functional genomic regions may be significantly higher than previously thought. seqinspector provides the possibility to search for functional elements with high confidence within unconserved genomic regions, offering a significant advantage over systems based on <italic>in silico</italic>-computed transcription factor binding sites. Another advantage of ChIP-seq&#x02013;based enrichment services is the ability to search for distant enhancer transcription factors through indirect binding to promoter regions. Enhancer regions can be distant (even up to 200&#x000a0;kb) from the proximal promoter of a target gene [<xref ref-type="bibr" rid="CR23">23</xref>]. To demonstrate this advantage, we assessed seqinspector using a set of genes regulated by the enhancer transcription factor GR.</p><p>A unique feature of seqinspector is that it accepts any set of genomic coordinates. Therefore, the tool can be used to identify common gene expression regulators for sets of co-expressed transcripts (including miRNAs, lncRNAs and any novel un-annotated RNAs) or for sets of ChIP-seq peaks to identify putative protein-protein interactions or transcriptional co-factors. Our software enables the study of transcript expression in various tissues and cell types using FANTOM5 CAGE data. Therefore, it is possible to identify the enrichment of cell-type-specific genes or transcripts. The seqinspector software package can also be used in more creative ways; for example, the user can compare the characteristics of 5&#x02019; untranslated region (UTR) histone modifications between two sets of transcripts. We are continuously adding new ChIP-seq experiments to the database. seqinspector is not easily outdated; any novel genomic features (e.g., genes, miRNAs or ChIP-seq peaks) can be used if their genomic positions are known. Even after major assembly changes in databases, the new genomic ranges can be translated to the old versions of the genome assembly using the liftover tool. This mechanism is currently built into seqinspector for the mouse mm10 assembly.</p></sec><sec id="Sec12"><title>Conclusions</title><p>The seqinspector tool was developed to facilitate the functional annotation and discovery of transcription factor binding sites on promoters of co-expressed transcripts, signals from ChIP-seq experiments and any other set of genomic coordinates sharing a common trait. The presented web resource includes a large collection of publicly available ChIP-seq experiments (&#x0003e;1100 tracks) performed on transcription factors, histone modifications, RNA polymerases and insulators in humans and mice.</p><sec id="Sec13"><title>Availability and requirements</title><p>The seqinspector tool is free, open to all users, and there is no login requirement. seqinspector is available at <ext-link ext-link-type="uri" xlink:href="http://seqinspector.cremag.org">http://seqinspector.cremag.org</ext-link>. When using seqinspector in future studies, please cite this paper.</p></sec></sec></body><back><app-group><app id="App1"><sec id="Sec14"><title>Additional files</title><p><media position="anchor" xlink:href="12859_2016_938_MOESM1_ESM.xls" id="MOESM1"><label>Additional file 1:</label><caption><p>
<bold>List of genes upregulated by dexamethason.</bold> (XLS 6 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_938_MOESM2_ESM.xls" id="MOESM2"><label>Additional file 2:</label><caption><p>
<bold>List of &#x000a0;SP1 binding sites in GM12878 human lymphoblastoid cells. </bold>(XLS 92 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_938_MOESM3_ESM.xls" id="MOESM3"><label>Additional file 3:</label><caption><p>
<bold>Lists of astroglia and neuron specific genes. </bold>(XLS 63 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_938_MOESM4_ESM.xls" id="MOESM4"><label>Additional file 4:</label><caption><p>
<bold>List of genes regulated in ruptured intracranial aneurysm. </bold>(XLS 25 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_938_MOESM5_ESM.xls" id="MOESM5"><label>Additional file 5:</label><caption><p>
<bold>Five example sets of genes used for comparison of seqinspector to other tools.</bold> (XLS 33 kb)</p></caption></media></p></sec></app></app-group><glossary><title>Abbreviations</title><def-list><def-item><term>ChIP-seq</term><def><p>Chromatin immunoprecipitation and next-generation DNA sequencing</p></def></def-item><def-item><term>ENCODE</term><def><p>Encyclopedia of DNA Elements Consortium</p></def></def-item><def-item><term>GR</term><def><p>Glucocorticoid receptor</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Competing interests</bold></p><p>The authors declare that they have no competing interests.</p></fn><fn><p><bold>Authors&#x02019; contributions</bold></p><p>MP designed the software. MP and AT implemented the software. MP, AT and JF prepared the ChIP-seq database. MP and MK performed the analysis. MP, MK and AF wrote the manuscript. The final version of the manuscript has been approved by all of the authors. RP supervised the study.</p></fn></fn-group><ack><title>Acknowledgements</title><p>This work was supported by the Polish National Science Centre grant [2011/01/N/NZ2/04827]; Michal Korostynski and Joanna Ficek were supported by the Polish National Science Centre grant [2011/03/D/NZ3/01686]. Funding for data storage: PL-GRID plgifpan grant. Funding for open access charge: Statutory Fund of Institute of Pharmacology Polish Academy of Sciences and the Polish National Science Centre grant [2011/03/D/NZ3/01686].</p></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maniatis</surname><given-names>T</given-names></name><name><surname>Reed</surname><given-names>R</given-names></name></person-group><article-title>An extensive network of coupling among gene expression machines</article-title><source>Nature</source><year>2002</year><volume>416</volume><issue>6880</issue><fpage>499</fpage><lpage>506</lpage><pub-id pub-id-type="doi">10.1038/416499a</pub-id><?supplied-pmid 11932736?><pub-id pub-id-type="pmid">11932736</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kouzarides</surname><given-names>T</given-names></name></person-group><article-title>Chromatin modifications and their function</article-title><source>Cell</source><year>2007</year><volume>128</volume><issue>4</issue><fpage>693</fpage><lpage>705</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2007.02.005</pub-id><?supplied-pmid 17320507?><pub-id pub-id-type="pmid">17320507</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>The ENCODE Project Consortium</collab></person-group><article-title>An integrated encyclopedia of DNA elements in the human genome</article-title><source>Nature</source><year>2012</year><volume>489</volume><issue>7414</issue><fpage>57</fpage><lpage>74</lpage><pub-id pub-id-type="doi">10.1038/nature11247</pub-id><pub-id pub-id-type="pmid">22955616</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kent</surname><given-names>WJ</given-names></name><name><surname>Zweig</surname><given-names>AS</given-names></name><name><surname>Barber</surname><given-names>G</given-names></name><name><surname>Hinrichs</surname><given-names>AS</given-names></name><name><surname>Karolchik</surname><given-names>D</given-names></name></person-group><article-title>BigWig and BigBed: enabling browsing of large distributed datasets</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><issue>17</issue><fpage>2204</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btq351</pub-id><?supplied-pmid 20639541?><pub-id pub-id-type="pmid">20639541</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robinson</surname><given-names>JT</given-names></name><name><surname>Thorvaldsd&#x000f3;ttir</surname><given-names>H</given-names></name><name><surname>Winckler</surname><given-names>W</given-names></name><name><surname>Guttman</surname><given-names>M</given-names></name><name><surname>Lander</surname><given-names>ES</given-names></name><name><surname>Getz</surname><given-names>G</given-names></name><etal/></person-group><article-title>Integrative genomics viewer</article-title><source>Nat Biotechnol</source><year>2011</year><volume>29</volume><issue>1</issue><fpage>24</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1038/nbt.1754</pub-id><?supplied-pmid 21221095?><pub-id pub-id-type="pmid">21221095</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>FANTOM Consortium and the RIKEN PMI and CLST (DGT)</collab><name><surname>Forrest</surname><given-names>ARR</given-names></name><name><surname>Kawaji</surname><given-names>H</given-names></name><name><surname>Rehli</surname><given-names>M</given-names></name><name><surname>Baillie</surname><given-names>JK</given-names></name><name><surname>de Hoon</surname><given-names>MJL</given-names></name><etal/></person-group><article-title>A promoter-level mammalian expression atlas</article-title><source>Nature</source><year>2014</year><volume>507</volume><issue>7493</issue><fpage>462</fpage><lpage>70</lpage><pub-id pub-id-type="doi">10.1038/nature13182</pub-id><pub-id pub-id-type="pmid">24670764</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barrett</surname><given-names>T</given-names></name><name><surname>Wilhite</surname><given-names>SE</given-names></name><name><surname>Ledoux</surname><given-names>P</given-names></name><name><surname>Evangelista</surname><given-names>C</given-names></name><name><surname>Kim</surname><given-names>IF</given-names></name><name><surname>Tomashevsky</surname><given-names>M</given-names></name><etal/></person-group><article-title>NCBI GEO: archive for functional genomics data sets--update</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>41</volume><issue>D1</issue><fpage>D991</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1093/nar/gks1193</pub-id><?supplied-pmid 23193258?><pub-id pub-id-type="pmid">23193258</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ho</surname><given-names>SSJ</given-names></name><name><surname>Fulton</surname><given-names>DL</given-names></name><name><surname>Arenillas</surname><given-names>DJ</given-names></name><name><surname>Kwon</surname><given-names>AT</given-names></name><name><surname>Wasserman</surname><given-names>WW</given-names></name></person-group><article-title>oPOSSUM: integrated tools for analysis of regulatory motif over-representation</article-title><source>Nucleic Acids Res</source><year>2007</year><volume>35</volume><issue>Web Server Issue</issue><fpage>W245</fpage><lpage>252</lpage><pub-id pub-id-type="pmid">17576675</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Frith</surname><given-names>MC</given-names></name><name><surname>Fu</surname><given-names>Y</given-names></name><name><surname>Yu</surname><given-names>L</given-names></name><name><surname>Chen</surname><given-names>J-F</given-names></name><name><surname>Hansen</surname><given-names>U</given-names></name><name><surname>Weng</surname><given-names>Z</given-names></name></person-group><article-title>Detection of functional DNA motifs via statistical over-representation</article-title><source>Nucleic Acids Res</source><year>2004</year><volume>32</volume><issue>4</issue><fpage>1372</fpage><lpage>81</lpage><pub-id pub-id-type="doi">10.1093/nar/gkh299</pub-id><?supplied-pmid 14988425?><pub-id pub-id-type="pmid">14988425</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zambelli</surname><given-names>F</given-names></name><name><surname>Prazzoli</surname><given-names>GM</given-names></name><name><surname>Pesole</surname><given-names>G</given-names></name><name><surname>Pavesi</surname><given-names>G</given-names></name></person-group><article-title>Cscan: finding common regulators of a set of genes by using a collection of genome-wide ChIP-seq datasets</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><issue>Web Server Issue</issue><fpage>W510</fpage><lpage>515</lpage><pub-id pub-id-type="doi">10.1093/nar/gks483</pub-id><?supplied-pmid 22669907?><pub-id pub-id-type="pmid">22669907</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Korostynski</surname><given-names>M</given-names></name><name><surname>Piechota</surname><given-names>M</given-names></name><name><surname>Dzbek</surname><given-names>J</given-names></name><name><surname>Mlynarski</surname><given-names>W</given-names></name><name><surname>Szklarczyk</surname><given-names>K</given-names></name><name><surname>Ziolkowska</surname><given-names>B</given-names></name><etal/></person-group><article-title>Novel drug-regulated transcriptional networks in brain reveal pharmacological properties of psychotropic drugs</article-title><source>BMC Genomics</source><year>2013</year><volume>14</volume><issue>1</issue><fpage>606</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-14-606</pub-id><?supplied-pmid 24010892?><pub-id pub-id-type="pmid">24010892</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Piechota</surname><given-names>M</given-names></name><name><surname>Korostynski</surname><given-names>M</given-names></name><name><surname>Przewlocki</surname><given-names>R</given-names></name></person-group><article-title>Identification of cis-regulatory elements in the mammalian genome: the cREMaG database</article-title><source>PLoS One</source><year>2010</year><volume>5</volume><issue>8</issue><fpage>e12465</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0012465</pub-id><?supplied-pmid 20824209?><pub-id pub-id-type="pmid">20824209</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Slezak</surname><given-names>M</given-names></name><name><surname>Korostynski</surname><given-names>M</given-names></name><name><surname>Gieryk</surname><given-names>A</given-names></name><name><surname>Golda</surname><given-names>S</given-names></name><name><surname>Dzbek</surname><given-names>J</given-names></name><name><surname>Piechota</surname><given-names>M</given-names></name><etal/></person-group><article-title>Astrocytes are a neural target of morphine action via glucocorticoid receptor-dependent signaling</article-title><source>Glia</source><year>2013</year><volume>61</volume><issue>4</issue><fpage>623</fpage><lpage>35</lpage><pub-id pub-id-type="doi">10.1002/glia.22460</pub-id><?supplied-pmid 23339081?><pub-id pub-id-type="pmid">23339081</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Neph</surname><given-names>S</given-names></name><name><surname>Vierstra</surname><given-names>J</given-names></name><name><surname>Stergachis</surname><given-names>AB</given-names></name><name><surname>Reynolds</surname><given-names>AP</given-names></name><name><surname>Haugen</surname><given-names>E</given-names></name><name><surname>Vernot</surname><given-names>B</given-names></name><etal/></person-group><article-title>An expansive human regulatory lexicon encoded in transcription factor footprints</article-title><source>Nature</source><year>2012</year><volume>489</volume><issue>7414</issue><fpage>83</fpage><lpage>90</lpage><pub-id pub-id-type="doi">10.1038/nature11212</pub-id><?supplied-pmid 22955618?><pub-id pub-id-type="pmid">22955618</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>Y</given-names></name><name><surname>Chen</surname><given-names>K</given-names></name><name><surname>Sloan</surname><given-names>SA</given-names></name><name><surname>Bennett</surname><given-names>ML</given-names></name><name><surname>Scholze</surname><given-names>AR</given-names></name><name><surname>O&#x02019;Keeffe</surname><given-names>S</given-names></name><etal/></person-group><article-title>An RNA-sequencing transcriptome and splicing database of glia, neurons, and vascular cells of the cerebral cortex</article-title><source>J Neurosci</source><year>2014</year><volume>34</volume><issue>36</issue><fpage>11929</fpage><lpage>47</lpage><pub-id pub-id-type="doi">10.1523/JNEUROSCI.1860-14.2014</pub-id><?supplied-pmid 25186741?><pub-id pub-id-type="pmid">25186741</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pera</surname><given-names>J</given-names></name><name><surname>Korostynski</surname><given-names>M</given-names></name><name><surname>Golda</surname><given-names>S</given-names></name><name><surname>Piechota</surname><given-names>M</given-names></name><name><surname>Dzbek</surname><given-names>J</given-names></name><name><surname>Krzyszkowski</surname><given-names>T</given-names></name><etal/></person-group><article-title>Gene expression profiling of blood in ruptured intracranial aneurysms: in search of biomarkers</article-title><source>J Cereb Blood Flow Metab</source><year>2013</year><volume>33</volume><issue>7</issue><fpage>1025</fpage><lpage>31</lpage><pub-id pub-id-type="doi">10.1038/jcbfm.2013.37</pub-id><?supplied-pmid 23512133?><pub-id pub-id-type="pmid">23512133</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Auerbach</surname><given-names>RK</given-names></name><name><surname>Chen</surname><given-names>B</given-names></name><name><surname>Butte</surname><given-names>AJ</given-names></name></person-group><article-title>Relating genes to function: identifying enriched transcription factors using the ENCODE ChIP-Seq significance tool</article-title><source>Bioinformatics</source><year>2013</year><volume>29</volume><issue>15</issue><fpage>1922</fpage><lpage>4</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btt316</pub-id><?supplied-pmid 23732275?><pub-id pub-id-type="pmid">23732275</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>EY</given-names></name><name><surname>Tan</surname><given-names>CM</given-names></name><name><surname>Kou</surname><given-names>Y</given-names></name><name><surname>Duan</surname><given-names>Q</given-names></name><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Meirelles</surname><given-names>G</given-names></name><etal/></person-group><article-title>Enrichr: interactive and collaborative HTML5 gene list enrichment analysis tool</article-title><source>BMC Bioinformatics</source><year>2013</year><volume>14</volume><issue>1</issue><fpage>128</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-14-128</pub-id><?supplied-pmid 23586463?><pub-id pub-id-type="pmid">23586463</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kwon</surname><given-names>AT</given-names></name><name><surname>Arenillas</surname><given-names>DJ</given-names></name><name><surname>Worsley Hunt</surname><given-names>R</given-names></name><name><surname>Wasserman</surname><given-names>WW</given-names></name></person-group><article-title>oPOSSUM-3: advanced analysis of regulatory motif over-representation across genes or ChIP-Seq datasets</article-title><source>G3 (Bethesda)</source><year>2012</year><volume>2</volume><issue>9</issue><fpage>987</fpage><lpage>1002</lpage><pub-id pub-id-type="doi">10.1534/g3.112.003202</pub-id><pub-id pub-id-type="pmid">22973536</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guberman</surname><given-names>JM</given-names></name><name><surname>Ai</surname><given-names>J</given-names></name><name><surname>Arnaiz</surname><given-names>O</given-names></name><name><surname>Baran</surname><given-names>J</given-names></name><name><surname>Blake</surname><given-names>A</given-names></name><name><surname>Baldock</surname><given-names>R</given-names></name><etal/></person-group><article-title>BioMart Central portal: an open database network for the biological community</article-title><source>Database (Oxford)</source><year>2011</year><volume>2011</volume><fpage>bar041</fpage><pub-id pub-id-type="doi">10.1093/database/bar041</pub-id><pub-id pub-id-type="pmid">21930507</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Siepel</surname><given-names>A</given-names></name><name><surname>Bejerano</surname><given-names>G</given-names></name><name><surname>Pedersen</surname><given-names>JS</given-names></name><name><surname>Hinrichs</surname><given-names>AS</given-names></name><name><surname>Hou</surname><given-names>M</given-names></name><name><surname>Rosenbloom</surname><given-names>K</given-names></name><etal/></person-group><article-title>Evolutionarily conserved elements in vertebrate, insect, worm, and yeast genomes</article-title><source>Genome Res</source><year>2005</year><volume>15</volume><issue>8</issue><fpage>1034</fpage><lpage>50</lpage><pub-id pub-id-type="doi">10.1101/gr.3715005</pub-id><?supplied-pmid 16024819?><pub-id pub-id-type="pmid">16024819</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wasserman</surname><given-names>WW</given-names></name><name><surname>Sandelin</surname><given-names>A</given-names></name></person-group><article-title>Applied bioinformatics for the identification of regulatory elements</article-title><source>Nat Rev Genet</source><year>2004</year><volume>5</volume><issue>4</issue><fpage>276</fpage><lpage>87</lpage><pub-id pub-id-type="doi">10.1038/nrg1315</pub-id><?supplied-pmid 15131651?><pub-id pub-id-type="pmid">15131651</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>G</given-names></name><name><surname>Ruan</surname><given-names>X</given-names></name><name><surname>Auerbach</surname><given-names>R</given-names></name><name><surname>Sandhu</surname><given-names>K</given-names></name><name><surname>Zheng</surname><given-names>M</given-names></name><name><surname>Wang</surname><given-names>P</given-names></name><etal/></person-group><article-title>Extensive promoter-centered chromatin interactions provide a topological basis for transcription regulation</article-title><source>Cell</source><year>2012</year><volume>148</volume><issue>1&#x02013;2</issue><fpage>84</fpage><lpage>98</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2011.12.014</pub-id><?supplied-pmid 22265404?><pub-id pub-id-type="pmid">22265404</pub-id></element-citation></ref></ref-list></back></article>