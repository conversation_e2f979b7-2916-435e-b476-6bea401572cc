<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.2 20190208//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-archivearticle1.dtd?><?SourceDTD.Version 1.2?><?ConverterInfo.XSLTName jats2jats3.xsl?><?ConverterInfo.Version 1?><?subarticle sa1?><?properties open_access?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">eLife</journal-id><journal-id journal-id-type="iso-abbrev">Elife</journal-id><journal-id journal-id-type="publisher-id">eLife</journal-id><journal-title-group><journal-title>eLife</journal-title></journal-title-group><issn pub-type="epub">2050-084X</issn><publisher><publisher-name>eLife Sciences Publications, Ltd</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7840180</article-id><article-id pub-id-type="pmid">33459255</article-id><article-id pub-id-type="publisher-id">63455</article-id><article-id pub-id-type="doi">10.7554/eLife.63455</article-id><article-categories><subj-group subj-group-type="display-channel"><subject>Research Article</subject></subj-group><subj-group subj-group-type="heading"><subject>Computational and Systems Biology</subject></subj-group><subj-group subj-group-type="heading"><subject>Neuroscience</subject></subj-group></article-categories><title-group><article-title>Bi-channel image registration and deep-learning segmentation (BIRDS) for efficient, versatile 3D mapping of mouse brain</article-title></title-group><contrib-group><contrib id="author-210455" contrib-type="author" equal-contrib="yes"><name><surname>Wang</surname><given-names>Xuechun</given-names></name><xref rid="aff1" ref-type="aff">1</xref><xref rid="equal-contrib1" ref-type="author-notes">&#x02020;</xref><xref rid="con1" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-209955" contrib-type="author" equal-contrib="yes"><name><surname>Zeng</surname><given-names>Weilin</given-names></name><xref rid="aff1" ref-type="aff">1</xref><xref rid="equal-contrib1" ref-type="author-notes">&#x02020;</xref><xref rid="con2" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-209899" contrib-type="author" equal-contrib="yes"><name><surname>Yang</surname><given-names>Xiaodan</given-names></name><xref rid="aff2" ref-type="aff">2</xref><xref rid="equal-contrib1" ref-type="author-notes">&#x02020;</xref><xref rid="con3" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-255268" contrib-type="author" corresp="yes" equal-contrib="yes"><name><surname>Zhang</surname><given-names>Yongsheng</given-names></name><email><EMAIL></email><xref rid="aff3" ref-type="aff">3</xref><xref rid="equal-contrib1" ref-type="author-notes">&#x02020;</xref><xref rid="fund7" ref-type="other"/><xref rid="fund8" ref-type="other"/><xref rid="con4" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-209900" contrib-type="author" equal-contrib="yes"><name><surname>Fang</surname><given-names>Chunyu</given-names></name><xref rid="aff1" ref-type="aff">1</xref><xref rid="equal-contrib1" ref-type="author-notes">&#x02020;</xref><xref rid="con5" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-124218" contrib-type="author"><name><surname>Zeng</surname><given-names>Shaoqun</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0002-1802-337X</contrib-id><xref rid="aff3" ref-type="aff">3</xref><xref rid="fund7" ref-type="other"/><xref rid="fund8" ref-type="other"/><xref rid="con6" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-209901" contrib-type="author" corresp="yes"><name><surname>Han</surname><given-names>Yunyun</given-names></name><email><EMAIL></email><xref rid="aff2" ref-type="aff">2</xref><xref rid="fund2" ref-type="other"/><xref rid="fund4" ref-type="other"/><xref rid="fund5" ref-type="other"/><xref rid="con7" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><contrib id="author-111080" contrib-type="author" corresp="yes"><name><surname>Fei</surname><given-names>Peng</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0003-3764-817X</contrib-id><email><EMAIL></email><xref rid="aff1" ref-type="aff">1</xref><xref rid="fund1" ref-type="other"/><xref rid="fund3" ref-type="other"/><xref rid="fund4" ref-type="other"/><xref rid="fund6" ref-type="other"/><xref rid="con8" ref-type="fn"/><xref rid="conf1" ref-type="fn"/></contrib><aff id="aff1">
<label>1</label>
<institution>School of Optical and Electronic Information- Wuhan National Laboratory for Optoelectronics, Huazhong University of Science and Technology</institution>
<addr-line>Wuhan</addr-line>
<country>China</country>
</aff><aff id="aff2">
<label>2</label>
<institution>School of Basic Medicine, Tongji Medical College, Huazhong University of Science and Technology</institution>
<addr-line>Wuhan</addr-line>
<country>China</country>
</aff><aff id="aff3">
<label>3</label>
<institution>Britton Chance Center for Biomedical Photonics, Wuhan National Laboratory for Optoelectronics, Huazhong University of Science and Technology</institution>
<addr-line>Wuhan</addr-line>
<country>China</country>
</aff></contrib-group><contrib-group><contrib contrib-type="editor"><name><surname>Colgin</surname><given-names>Laura L</given-names></name><role>Senior Editor</role><aff>
<institution>University of Texas at Austin</institution>
<country>United States</country>
</aff></contrib><contrib contrib-type="editor"><name><surname>Smith</surname><given-names>Jeffrey C</given-names></name><role>Reviewing Editor</role><aff>
<institution>National Institute of Neurological Disorders and Stroke</institution>
<country>United States</country>
</aff></contrib></contrib-group><author-notes><fn fn-type="con" id="equal-contrib1"><label>&#x02020;</label><p>These authors contributed equally to this work.</p></fn></author-notes><pub-date date-type="pub" publication-format="electronic"><day>18</day><month>1</month><year>2021</year></pub-date><pub-date pub-type="collection"><year>2021</year></pub-date><volume>10</volume><elocation-id>e63455</elocation-id><history>
<date date-type="received" iso-8601-date="2020-09-25"><day>25</day><month>9</month><year>2020</year></date>
<date date-type="accepted" iso-8601-date="2020-12-27"><day>27</day><month>12</month><year>2020</year></date>
</history><permissions><copyright-statement>&#x000a9; 2021, Wang et al</copyright-statement><copyright-year>2021</copyright-year><copyright-holder>Wang et al</copyright-holder><ali:free_to_read xmlns:ali="http://www.niso.org/schemas/ali/1.0/"/><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This article is distributed under the terms of the <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use and redistribution provided that the original author and source are credited.</license-p></license></permissions><self-uri xmlns:xlink="http://www.w3.org/1999/xlink" content-type="pdf" xlink:href="elife-63455.pdf"/><abstract><p>We have developed an open-source software called bi-channel image registration and deep-learning segmentation (BIRDS) for the mapping and analysis of 3D microscopy data and applied this to the mouse brain. The BIRDS pipeline includes image preprocessing, bi-channel registration, automatic annotation, creation of a 3D digital frame, high-resolution visualization, and expandable quantitative analysis. This new bi-channel registration algorithm is adaptive to various types of whole-brain data from different microscopy platforms and shows dramatically improved registration accuracy. Additionally, as this platform combines registration with neural networks, its improved function relative to the other platforms lies in the fact that the registration procedure can readily provide training data for network construction, while the trained neural network can efficiently segment-incomplete/defective brain data that is otherwise difficult to register. Our software is thus optimized to enable either minute-timescale registration-based segmentation of cross-modality, whole-brain datasets or real-time inference-based image segmentation of various brain regions of interest. Jobs can be easily submitted and implemented via a Fiji plugin that can be adapted to most computing environments.</p></abstract><abstract abstract-type="executive-summary"><title>eLife digest</title><p>Mapping all the cells and nerve connections in the mouse brain is a major goal of the neuroscience community, as this will provide new insights into how the brain works and what happens during disease. To achieve this, researchers must first capture three-dimensional images of the brain. These images are then processed using computational tools that can identify distinct anatomical features and cell types within the brain.</p><p>Various microscopy techniques are used to capture three-dimensional images of the brain. This has led to an increasing number of computational programs that can extract data from these images. However, these tools have been specifically designed for certain microscopy techniques. For example, some work on whole-brain datasets while others are built to analyze specific brain regions. Developing a more flexible, standardized method for annotating microscopy images of the brain would therefore enable researchers to analyze data more efficiently and compare results across experiments.</p><p>To this end, Wang, Zeng, Yang et al. have designed an open-source software program for extracting features from three-dimensional brain images which have been captured using different microscopes. Similar to other tools, the program uses an &#x02018;image registration&#x02019; method that is able to recognize and annotate features in the brain. These tools, however, are limited to whole-brain datasets in which the complete anatomy of each feature must be present in order to be recognized by the software.</p><p>To overcome this, Wang et al. combined the image registration method with a deep-learning algorithm which uses pixels in the image to identify features in isolated regions of the brain. Although these neural networks do not require whole-brain images, they do need large datasets to &#x02018;learn&#x02019; from. Therefore, the image registration method also benefits the neural network by providing a dataset of annotated features that the algorithm can train on.</p><p>Wang et al. showed that their software program, named BIRDS, could accurately recognize pixel-level brain features within imaging datasets of brain regions, as well as whole-brain images. The deep-learning algorithm could also adapt to analyze various types of imaging data from different microscopy platforms. This open-source software should make it easier for researchers to share, analyze and compare brain imaging datasets from different experiments.</p></abstract><kwd-group kwd-group-type="author-keywords"><kwd>mouse brain</kwd><kwd>deep-learning</kwd><kwd>image registration</kwd></kwd-group><kwd-group kwd-group-type="research-organism"><title>Research organism</title><kwd>Mouse</kwd></kwd-group><funding-group><award-group id="fund1"><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001809</institution-id><institution>National Natural Science Foundation of China</institution></institution-wrap>
</funding-source><award-id>21874052</award-id><principal-award-recipient>
<name><surname>Fei</surname><given-names>Peng</given-names></name>
</principal-award-recipient></award-group><award-group id="fund2"><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001809</institution-id><institution>National Natural Science Foundation of China</institution></institution-wrap>
</funding-source><award-id>31871089</award-id><principal-award-recipient>
<name><surname>Han</surname><given-names>Yunyun</given-names></name>
</principal-award-recipient></award-group><award-group id="fund3"><funding-source>
<institution-wrap><institution>Innovation Fund of WNLO</institution></institution-wrap>
</funding-source><principal-award-recipient>
<name><surname>Fei</surname><given-names>Peng</given-names></name>
</principal-award-recipient></award-group><award-group id="fund4"><funding-source>
<institution-wrap><institution>Junior Thousand Talents Program of China</institution></institution-wrap>
</funding-source><principal-award-recipient>
<name><surname>Han</surname><given-names>Yunyun</given-names></name>
<name><surname>Fei</surname><given-names>Peng</given-names></name>
</principal-award-recipient></award-group><award-group id="fund5"><funding-source>
<institution-wrap><institution>The FRFCU</institution></institution-wrap>
</funding-source><award-id>HUST:2172019kfyXKJC077</award-id><principal-award-recipient>
<name><surname>Han</surname><given-names>Yunyun</given-names></name>
</principal-award-recipient></award-group><award-group id="fund6"><funding-source>
<institution-wrap><institution>National Key R&#x00026;D program of China</institution></institution-wrap>
</funding-source><award-id>2017YFA0700501</award-id><principal-award-recipient>
<name><surname>Fei</surname><given-names>Peng</given-names></name>
</principal-award-recipient></award-group><award-group id="fund7"><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100012166</institution-id><institution>973 Program</institution></institution-wrap>
</funding-source><award-id>2015CB755603</award-id><principal-award-recipient>
<name><surname>Zeng</surname><given-names>Shaoqun</given-names></name>
<name><surname>Zhang</surname><given-names>Yongsheng</given-names></name>
</principal-award-recipient></award-group><award-group id="fund8"><funding-source>
<institution-wrap><institution>Director Fund of WNLO</institution></institution-wrap>
</funding-source><principal-award-recipient>
<name><surname>Zhang</surname><given-names>Yongsheng</given-names></name>
<name><surname>Zeng</surname><given-names>Shaoqun</given-names></name>
</principal-award-recipient></award-group><funding-statement>The funders had no role in study design, data collection and interpretation, or the decision to submit the work for publication.</funding-statement></funding-group><custom-meta-group><custom-meta specific-use="meta-only"><meta-name>Author impact statement</meta-name><meta-value>A dual-channel image registration pipeline combined with deep-learning inference achieves accurate-and-flexible registration/segmentation/mapping of mouse brain.</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec sec-type="intro" id="s1"><title>Introduction</title><p>The mapping of the brain and neural circuits is currently a major endeavor in neuroscience and has great potential for facilitating an understanding of fundamental and pathological brain processes (<xref rid="bib2" ref-type="bibr">Alivisatos et al., 2012</xref>; <xref rid="bib18" ref-type="bibr">Kandel et al., 2013</xref>; <xref rid="bib49" ref-type="bibr">Zuo et al., 2014</xref>). Large projects, including the Mouse Brain Architecture project (<xref rid="bib3" ref-type="bibr">Bohland et al., 2009</xref>), the <italic toggle="yes">Allen Mouse Brain Connectivity Atlas</italic> (<xref rid="bib33" ref-type="bibr">Oh et al., 2014</xref>), and the Mouse Connectome project, have mapped the mouse brain (<xref rid="bib48" ref-type="bibr">Zingg et al., 2014</xref>) in terms of cell types, long-range connectivity patterns, and microcircuit connectivity. In addition to these large-scale collaborative efforts, an increasing number of laboratories are also developing independent, automated, or semi-automated frameworks for processing brain data obtained for specific projects (<xref rid="bib11" ref-type="bibr">F&#x000fc;rth et al., 2018</xref>; <xref rid="bib30" ref-type="bibr">Ni et al., 2020</xref>; <xref rid="bib32" ref-type="bibr">Niedworok et al., 2016</xref>; <xref rid="bib35" ref-type="bibr">Renier et al., 2016</xref>; <xref rid="bib42" ref-type="bibr">Wang et al., 2020a</xref>; <xref rid="bib17" ref-type="bibr">Iqbal et al., 2019</xref>). With the improvement of experimental methods for dissection of brain connectivity and function, development of a standardized and automated computational pipeline to map, analyze, visualize, and share brain data has become a major challenge to all brain connectivity mapping efforts (<xref rid="bib2" ref-type="bibr">Alivisatos et al., 2012</xref>; <xref rid="bib11" ref-type="bibr">F&#x000fc;rth et al., 2018</xref>). Thus, the implementation of an efficient and reliable method is fundamentally required for defining the accurate anatomical boundaries of brain structures, by which the anatomical positions of cells or neuronal connections can be determined to enable interpretation and comparison across experiments (<xref rid="bib35" ref-type="bibr">Renier et al., 2016</xref>). The commonly used approach for automatic anatomical segmentation is to register an experimental image dataset within a standardized, fully segmented reference space, thus obtaining the anatomical segmentation for this set of experimental images (<xref rid="bib33" ref-type="bibr">Oh et al., 2014</xref>; <xref rid="bib30" ref-type="bibr">Ni et al., 2020</xref>; <xref rid="bib35" ref-type="bibr">Renier et al., 2016</xref>; <xref rid="bib19" ref-type="bibr">Kim et al., 2015</xref>; <xref rid="bib23" ref-type="bibr">Lein et al., 2007</xref>). There are currently several registration-based high-throughput image frameworks for analyzing large-scale brain datasets (<xref rid="bib11" ref-type="bibr">F&#x000fc;rth et al., 2018</xref>; <xref rid="bib30" ref-type="bibr">Ni et al., 2020</xref>; <xref rid="bib32" ref-type="bibr">Niedworok et al., 2016</xref>; <xref rid="bib35" ref-type="bibr">Renier et al., 2016</xref>). Most of these frameworks require the user to set a few parameters based on the image intensity or graphics outlines or to completely convert the dataset into a framework-readable format to ensure the quality of the resulting segmentation. However, with the rapid development of sample labeling technology (<xref rid="bib22" ref-type="bibr">Lee et al., 2016</xref>; <xref rid="bib36" ref-type="bibr">Richardson and Lichtman, 2015</xref>; <xref rid="bib38" ref-type="bibr">Schwarz et al., 2015</xref>) and high-resolution whole-brain microscopic imaging (<xref rid="bib9" ref-type="bibr">Economo et al., 2016</xref>; <xref rid="bib12" ref-type="bibr">Gong et al., 2013</xref>; <xref rid="bib31" ref-type="bibr">Nie et al., 2020</xref>; <xref rid="bib25" ref-type="bibr">Liu et al., 2017</xref>; <xref rid="bib24" ref-type="bibr">Li et al., 2010</xref>), the heterogeneous and non-uniform characteristics of brain structures make it difficult to use traditional registration methods for registering datasets from different imaging platforms to a standard brain space with high accuracy. In this case, laborious visual inspection, followed by manual correction, is often required, which significantly reduces the productivity of these techniques. Therefore, the research community urgently needs a robust, comprehensive registration method that can extract a significant number of unique features from image data and provide accurate registration between different types of individual datasets.</p><p>Moreover, though registration-based methods can achieve full anatomical annotation in reference to a standard atlas for whole-brain datasets, their region-based 3D registration to a whole-brain atlas lacks the flexibility to analyze incomplete brain datasets or those focused on a certain volume of interest (<xref rid="bib40" ref-type="bibr">Song and Song, 2018</xref>), which is often the case in neuroscience research. Though some frameworks can register certain types of brain slabs that contain complete coronal outlines slice by slice (<xref rid="bib11" ref-type="bibr">F&#x000fc;rth et al., 2018</xref>; <xref rid="bib40" ref-type="bibr">Song and Song, 2018</xref>; <xref rid="bib10" ref-type="bibr">Ferrante and Paragios, 2017</xref>), it remains very difficult to register a small brain block without obvious anatomical outlines. As neural networks have emerged as a technique of choice for image processing (<xref rid="bib26" ref-type="bibr">Long et al., 2015</xref>; <xref rid="bib4" ref-type="bibr">Chen et al., 2018a</xref>; <xref rid="bib15" ref-type="bibr">He et al., 2019</xref>; <xref rid="bib47" ref-type="bibr">Zhang et al., 2019</xref>), deep-learning-based brain mapping methods have also recently been reported to directly provide segmentation/annotation of primary regions for 3D brain datasets (<xref rid="bib17" ref-type="bibr">Iqbal et al., 2019</xref>; <xref rid="bib1" ref-type="bibr">Akkus et al., 2017</xref>; <xref rid="bib5" ref-type="bibr">Chen et al., 2018b</xref>; <xref rid="bib29" ref-type="bibr">Milletari et al., 2017</xref>; <xref rid="bib7" ref-type="bibr">de Brebisson and Montana, 2015</xref>). Such deep-learning-based segmentation networks are efficient in extracting pixel-level features and thus are not dependent on the presence of global features such as complete anatomical outlines, making them better suited for processing of incomplete brain data, as compared to registration-based methods. On the other hand, the establishment of these networks still relies on a sufficiently large training dataset, which is often laboriously registered, segmented, and annotated. Therefore, a combination of image registration and a neural network can possibly provide a synergistic improved analysis method and lead to more efficient and versatile brain mapping techniques.</p><p>Here, we provide an open-source software as a Fiji (<xref rid="bib37" ref-type="bibr">Schindelin et al., 2012</xref>)&#x000a0;plugin, termed bi-channel image registration and deep-learning segmentation (BIRDS), to support brain mapping efforts and to make it feasible to analyze, visualize, and share brain datasets. We developed BIRDS to allow investigators to quantify and spatially map 3D brain data in its own 3D digital space with reference to Allen CCFv3 (<xref rid="bib43" ref-type="bibr">Wang et al., 2020b</xref>). This facilitates analysis in its native status at cellular level. The pipeline features: (1) A bi-channel registration algorithm integrating a feature map with raw image data for co-registration with significantly improved accuracy and (2) a mutually beneficial strategy in which the registration procedure can readily provide training data for a neural network, while this network can efficiently segment incomplete brain data that is otherwise difficult to register with a standardized atlas. The whole computational framework is designed to be robust and flexible, allowing its application to a wide variety of imaging systems (e.g., epifluorescent microscopy or light-sheet microscopy) and labeling approaches (e.g., fluorescent proteins, immunohistochemistry, and in situ hybridization). The BIRDS pipeline offers a complete set of tools, including image preprocessing, feature-based registration and annotation, visualization of digital maps and quantitative analysis via a link with Imaris, and a neural network segmentation algorithm that allows efficient processing of incomplete brain data. We further demonstrate how BIRDS can be employed for fully automatic mapping of various brain structures and integration of multidimensional anatomical neuronal labeling datasets. The whole pipeline has been packaged into a Fiji plugin, with step-by-step tutorials that permit rapid implementation of this plugin in a standard laboratory computing environment.</p></sec><sec sec-type="results" id="s2"><title>Results</title><sec id="s2-1"><title>Bi-channel image registration with improved accuracy</title><p><xref rid="fig1" ref-type="fig">Figure 1</xref> shows our bi-channel registration procedure, which registers experimental whole-brain images using a standardized Allen Institute mouse brain average template, and then provides segmentations and annotations from CCFv3 for experimental data. The raw high-resolution 3D images (1 &#x000d7; 1 &#x000d7; 10 &#x003bc;m<sup>3</sup> per voxel), obtained by serial two-photon tomography (STPT, see Materials and methods), were first down-sampled into isotropic low-resolution data with a 20 &#x003bc;m voxel size identical to an averaged Allen template image (<xref rid="fig1" ref-type="fig">Figure 1a</xref>). The re-sampling ratios along the x (lateral-medial axis), y (dorsal-ventral axis), and z (anterior-posterior, AP axis) axes were thus 0.05, 0.05 and 0.5, respectively. It should be noted that, in addition to the individual differences, the preparation/mounting steps can also cause non-uniform deformation of samples, thereby posing extra challenges to the precise registration of experimental image to an averaged template (<xref rid="fig1" ref-type="fig">Figure 1b</xref>, original dataset). To mitigate this non-uniform deformation issue before registration, we applied a dynamic re-sampling ratio rather than using a fixed value of 0.5 to the z reslicing. We first subdivided the entire image stack into multiple sub-stacks (n = 6 in our demonstration, <xref rid="fig1" ref-type="fig">Figure 1a</xref>) according to seven selected landmark planes (<xref rid="fig1" ref-type="fig">Figure 1a</xref>, <xref rid="fig1s1" ref-type="fig">Figure 1&#x02014;figure supplement 1</xref>). Then we applied a dynamic <italic toggle="yes">z</italic> re-sampling ratio calculated corresponding to the positions of the landmark planes in the Allen template and sample data (varying from ~0.35 to 0.55) to each sub-stack, to finely compress (&#x0003c;0.5) or stretch (&#x0003e;0.5) the z depth of the sub-stacks, thereby better matching the depth of each sub-stack to the Allen template brain and rectifying the deformation along the AP axis (<xref rid="fig1" ref-type="fig">Figure 1a</xref>, Materials and methods). The rectified whole-brain stack assembled by these dynamically re-sampled sub-stacks showed higher original similarity to the Allen template brain as compared to a raw experimental image stack (<xref rid="fig1" ref-type="fig">Figure 1b</xref>). The implementation of such a preprocessing step was beneficial for the better alignment of non-uniformly morphed brain data to a standardized template (<xref rid="fig1s2" ref-type="fig">Figure 1&#x02014;figure supplement 2</xref>). After data preprocessing, we applied a feature-based iterative registration using the Allen reference images to the preprocessed experimental images. We note that previous registration methods were vulnerable to inadequate alignment accuracy (<xref rid="bib32" ref-type="bibr">Niedworok et al., 2016</xref>; <xref rid="bib35" ref-type="bibr">Renier et al., 2016</xref>; <xref rid="bib13" ref-type="bibr">Goubran et al., 2019</xref>), which was associated with inadequate registration information provided by merely using the raw background image data. To address this issue, in addition to the primary channel containing the background images of each sample and template brains, we further generated an assistant channel to augment the image registration and enhance the accuracy. First, we used a phase congruency (PC) algorithm (<xref rid="bib21" ref-type="bibr">Kovesi, 2019</xref>) to extract the high-contrast edge and texture information from both the experimental and template brain images based on their relatively fixed anatomy features (<xref rid="fig1" ref-type="fig">Figure 1c</xref>, Materials and methods). Then, we obtained the geometry features of both brains along their lateral&#x02013;medial, dorsal&#x02013;ventral, and anterior&#x02013;posterior axes with enhanced axial mutual information (MI) extracted using a grayscale reversal processing (<xref rid="bib28" ref-type="bibr">Maes et al., 1997</xref>; <xref rid="bib41" ref-type="bibr">Th&#x000e9;venaz and Unser, 2000</xref>) (<xref rid="fig1" ref-type="fig">Figure 1c</xref>, <xref rid="fig1s3" ref-type="fig">Figure 1&#x02014;figure supplement 3</xref>, Materials and methods). Finally, the primary channel containing raw brain images, in conjunction with the assistant channel containing the texture and geometry maps of brains, were included in the registration procedure to fulfill an information-augmented bi-channel registration requirement (<xref rid="fig1s4" ref-type="fig">Figure 1&#x02014;figure supplement 4</xref>), which was verified to show notably better registration accuracy as compared to conventional single-channel registration methods (aMAP [<xref rid="bib32" ref-type="bibr">Niedworok et al., 2016</xref>], ClearMap [<xref rid="bib35" ref-type="bibr">Renier et al., 2016</xref>], and MIRACL [<xref rid="bib13" ref-type="bibr">Goubran et al., 2019</xref>]). During registration, through an iterative optimization of the transformation from an averaged Allen brain template to the experimental data, the MI gradually reached its maximum when the inverse grayscale images, PC images, and the raw images were finally geometrically aligned (<xref rid="fig1" ref-type="fig">Figure 1d</xref>). The displacement was presented in a grid form to illustrate the non-linear deformation effects. The geometry wrapping parameters obtained from the registration process were then applied to the Allen annotation file to generate a transformed version specifically for experimental data (<xref rid="fig1s4" ref-type="fig">Figure 1&#x02014;figure supplement 4</xref>). Our dual-channel registration achieved fully automated registration/annotation at sufficiently high accuracy when processing STPT experimental data of an intact brain (<xref rid="bib14" ref-type="bibr">Han et al., 2018</xref>). As for low-quality or highly deformed brain data (e.g., clarified brain with obvious shrinkage), though the registration accuracy of our method was accordingly reduced, our method still quite obviously surpassed other methods (<xref rid="fig2" ref-type="fig">Figure 2</xref>). For such challenging data types, we also developed an interactive graphic user interface (GUI) to readily permit manual correction of the visible inaccuracies in the annotation file, through finely tuning the selected corresponding points (<xref rid="fig1" ref-type="fig">Figure 1e</xref>). Finally, an accurate 3D annotation could be generated and applied to experimental data, either fully automatically (STPT data) or after mild manual correction (light-sheet &#x0fb02;uorescence microscopy [LSFM] data of clarified brain), as shown in <xref rid="fig1" ref-type="fig">Figure 1f</xref>.</p><fig position="float" id="fig1"><label>Figure 1.</label><caption><title>Bi-channel brain registration procedure.</title><p>(<bold>a</bold>) Re-sampling of a raw 3D image into an isotropic low-resolution one, which has the same voxel size (20 &#x003bc;m) using an averaged Allen template image. The raw brain dataset was first subdivided into six sub-stacks along the AP axis according to landmarks identified in seven selected coronal planes (a1). Then an appropriate z re-sampling ratio, which was different for each slice, was applied to each sub-stack (a2, left) to finely adjust the depth of the stack in the down-sampled data (a2, right). This step roughly restored the deformation of non-uniformly morphed samples, thereby allowing the following registration with an Allen reference template. (<bold>b</bold>) Plot showing the variation of the down-sampling ratio applied to the six sub-stacks and comparison with the Allen template brain before and after the dynamic re-sampling showing the shape restoration effects of the this preprocessing step. (<bold>c</bold>) Additional feature channels containing a geometry and outline feature map extracted using grayscale reversal processing (left), as well as an edge and texture feature map extracted by a phase congruency algorithm (right). This feature channel was combined with a raw image channel for implementing our information-enriched bi-channel registration, which showed improved accuracy as compared to conventional single-channel registration solely based on raw images. (<bold>d</bold>) 3D view and anatomical sections (coronal and sagittal planes) of the registration results displayed in a grid deformed from an average Allen template. (<bold>e</bold>) Visual inspection and manual correction of automatically-registered results from an optically clarified brain, which showed obvious deformation. Using the GUI provided, this step could be readily operated by adjusting the interactive nodes in the annotation file (red points to light blue points). (<bold>f</bold>) A final atlas of an experimental brain image containing region segmentations and annotations.</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig1" position="float"/><p content-type="supplemental-figure"><fig position="anchor" id="fig1s1" specific-use="child-fig"><label>Figure 1&#x02014;figure supplement 1.</label><caption><title>Selection of featured coronal planes corresponded to the Allen Reference Atlas, for subdividing the entire brain volume into multiple sub-stacks along AP axis.</title><p>The GUI of BIRDS program first permits the selection of a series of coronal planes (monochrome images) from the deformed 3D brain data, which are corresponded to the Allen Reference Atlas (#23, 43, 56, 62, 78, 105, and 118 of total 132 slices). The selection of coronal planes corresponded to the Allen Reference Atlas is based on the identification of several anatomical features, as shown in the paired yellow-red rectangular boxes. Then the six sub-stacks segmented according to these seven planes are processed with different down-sampling ratios (from 0.36 to 0.59, <xref rid="fig1" ref-type="fig">Figure 1b</xref>), to obtain the corresponding rectified stacks with 1:1 scale ratio to the Allen template images (total 660 slice, 20 &#x003bc;m resolution). These rectified sub-stacks are finally combined to form an entire 3D brain volume with restored shape more similar to the Allen average template.</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig1-figsupp1" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig1s2" specific-use="child-fig"><label>Figure 1&#x02014;figure supplement 2.</label><caption><title>Improved registration accuracy enabled by the abovementioned shape rectification preprocessing.</title><p>(<bold>a&#x02013;d</bold>) Automatical registration results of the experimental brain data obtained after applying conventional uniform (top panel, a1&#x02013;d1) and our non-uniform down-sampling (bottom panel, a2&#x02013;d2), respectively. From (<bold>a</bold>) to (<bold>d</bold>), we comparatively show the moving template image (a1, a2), fixed experimental image (b1, b2), and corresponding segmentation results in horizontal (c1, c2) and coronal views (d1, d2), respectively. Scale bars, 1 mm. (<bold>e, f</bold>) Magnified views of two region-of-interests (boxes) shown in the registered-and-annotated horizontal planes (c1, c2). The yellow arrows indicate the improved accuracy by our BIRDS method (e2, f2), as compared to conventional uniform down-sampling registration (e1, f1). (<bold>g</bold>, <bold>h</bold>) Magnified views of two region-of-interests (boxes) in registered-and-annotated coronal planes (d1, d2). The yellow arrows indicate the improved accuracy by our method (g2, h2), as compared to conventional uniform down-sampling registration (g1, h1). Scale bar, 250 &#x003bc;m.</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig1-figsupp2" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig1s3" specific-use="child-fig"><label>Figure 1&#x02014;figure supplement 3.</label><caption><title>Extraction of axes features for registration.</title><p>(<bold>a</bold>) Determination of sagittal (orthogonal to lateral-medial axis, orange); horizontal (orthogonal to dorsal-ventral axis, blue); and coronal (orthogonal to anterior&#x02013;posterior axis, green) planes based on the identification of specific anotomical features, as shown in (<bold>a</bold>). (<bold>b</bold>) Grayscale reversal processing applied to the background-filtrated image dataset, according to the above-defined axis planes. The extraction of pure signals together with artificial reversal operation generate an extra feature map containing the geometry information of the axes, which is benefical to the improvement of registration accuracy.</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig1-figsupp3" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig1s4" specific-use="child-fig"><label>Figure 1&#x02014;figure supplement 4.</label><caption><title>Schematic of the multi-channel registration and annotation process.</title><p>For both experimental and template data, an additional feature channel (assistant channel) containing texture features extracted by PC and axial geometry features added by grayscale reversal, are combined with the raw image channel (primary channel) to implement information-augmented dual-channel registration based on rigid, affine, and B-spline transformation. This procedure simultaneously registers all the multi-spectral input data using a single cost function, as shown in Step 1. The transformation parameters obtained from the image registration procedure are then applied to the template mouse annotation atlas (Allen Institute, CCF v3) to transform the template annotation file into an individual one that specifically fits our experimental brain image, and thus describes the anatomical structures in the whole-brain space (Step 2).</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig1-figsupp4" position="float"/></fig></p></fig><fig position="float" id="fig2"><label>Figure 2.</label><caption><title>Comparison of BIRDS with conventional single-channel registration methods.</title><p>(<bold>a</bold>) Comparative registration accuracy (STPT data from an intact brain) using four different registration methods, aMAP, ClearMap, MIRACL, and BIRDS. (<bold>b</bold>) Comparative registration accuracy (LSFM data from clarified brain) using four methods. Magnified views of four regions of interest (a1&#x02013;a4, b1&#x02013;b4, blue boxes) selected from the horizontal (left, top) and coronal planes (left, bottom) are shown in the right four columns, with 3D detail for the registration/annotation accuracy for each method. All comparative annotation results were directly output from respective programs without manual correction. Scale bar, 1 mm (whole-brain view) and 250 &#x003bc;m (magnified view). (<bold>c</bold>) Ten groups of 3D fiducial points of interest (POIs) manually identified across the 3D space of whole brains. The blue and red points belong to the fixed experimental images and the registered Allen template images, respectively. The ten POIs were selected from the following landmarks: <italic toggle="yes">POIs: cc1</italic>: corpus callosum, midline; <italic toggle="yes">acoL</italic>, <italic toggle="yes">acoR</italic>: anterior commisure, olfactory limb; <italic toggle="yes">CPL, CPR</italic>: Caudoputamen, Striatum dorsal region; <italic toggle="yes">cc2</italic>: corpus callosum, midline; <italic toggle="yes">cc3</italic>: corpus callosum, midline; <italic toggle="yes">MM</italic>: medial mammillary nucleus, midline; <italic toggle="yes">DGsgL</italic>, <italic toggle="yes">DGsgR</italic>: dentate gyrus, granule cell layer. The registration error by each method could be thereby quantified through measuring the Euclidean distance between each pair of POIs in the experimental image and template image. (<bold>d</bold>) Box diagram comparing the POI distances of five brains registered by the four methods. Brains 1, 2: STPT images from two intact brains. Brain one is also shown in (<bold>a</bold>). Brains 3, 4, and 5: LSFM images from three clarified brains (u-DISCO) that showed significant deformations. Brain five is also shown in (<bold>b</bold>). The median error distance of 50 pairs of POIs in the five brains registered by BIRDS was ~104 &#x003bc;m, as compared to ~292 &#x003bc;m for aMAP, ~204 &#x003bc;m for ClearMap, and ~151 &#x003bc;m for MIRACL. (<bold>e, f</bold>) Comparative plot of Dice scores in nine registered regions of the five brains. The results were grouped by brain in (<bold>e</bold>) and region in (<bold>f</bold>). The calculation was implemented at the single nuclei level. When the results were analyzed by brain, BIRDS surpassed the other three methods most clearly using LSFM dataset #5, with a 0.881 median Dice score as compared to 0.574 from aMAP, 0.72 from ClearMap, and 0.645 from MIRACL. At the same time, all the methods performed well on STPT dataset #2, with a median Dice score of 0.874 from aMAP, 0.92 from ClearMap, 0.872 from MIRACL, and 0.933 from BIRDS. When the results were compared using nine functional regions, the median values acquired by BIRDS were also higher than the other three methods. Even the lowest median Dice score by our method was still 0.799 (indicated by black line), which was notably higher than 0.566 by aMAP, 0.596 by ClearMap, and 0.722 by MIRACL, respectively.</p><p>
<supplementary-material id="fig2sdata1" position="float" content-type="local-data"><label>Figure 2&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig2" ref-type="fig">Figure 2</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2-data1.zip" id="d64e751" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2" position="float"/><p content-type="supplemental-figure"><fig position="anchor" id="fig2s1" specific-use="child-fig"><label>Figure 2&#x02014;figure supplement 1.</label><caption><title>Selection of 10 fiducial points of interest (POIs) for measuring the error distance between registered brains.</title><p>(<bold>a</bold>) Ten selected feature points in Allen template image. (1) cc1: corpus callosum, midline. (2, 3) acoL, acoR: anterior commisure, olfactory limb. (4, 5) CPL, CPR: Caudoputamen, Striatum dorsal region. (6) cc2: corpus callosum, midline. (7) cc3: corpus callosum, midline. (8) MM: medial mammillary nucleus, midline. (9, 10) DGsgL, DGsgR: dentate gyrus, granule cell layer. (<bold>b</bold>) Box diagrams showing the error distances between the paired POIs in registered template and experimental brains (n = 5 brains, which are also used for <xref rid="fig2" ref-type="fig">Figure 2</xref>). Using BIRDS method, the median error distance of total 50 pairs of POIs in five brains is ~104 &#x003bc;m. (<bold>c</bold>) Line plot further showing the distance between each pair of POI.</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2-figsupp1" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig2s2" specific-use="child-fig"><label>Figure 2&#x02014;figure supplement 2.</label><caption><title>Comparison between BIRDS and conventional single-channel registration.</title><p>(<bold>a, b</bold>) STPT image of intact brain and LSFM image of clarified brain, which are registered by our BIRDS and conventional single-channel method. (<bold>c, d</bold>) Magnified views of six region-of-interests (yellow boxes) selected from the horizontal (top in <bold>a, b</bold>) and coronal planes (bottom in <bold>a, b</bold>) in the STPT and LSFM brains, respectively. The comparison between BIRDS (green annotation) and single-channel (red annotation) results indicates obviously less inaccuracy by our BIRDS method (yellow arrows). All segmentation results are directly outputted from the programs, without any manual correction. Scale bar, 1 mm for (<bold>a, b</bold>) and 250 &#x003bc;m for (<bold>c, d</bold>).</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2-figsupp2" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig2s3" specific-use="child-fig"><label>Figure 2&#x02014;figure supplement 3.</label><caption><title>Comparative accuracy analysis of BIRDS and single-channel registration.</title><p>(<bold>a</bold>) Box diagram showing the distances between the paired POIs (<xref rid="fig2s1" ref-type="fig">Figure 2&#x02014;figure supplement 1</xref>) in Allen template and experimental image registered by BIRDS and conventional single-channel registration. Black Brain 1 represents STPT image of an intact brain, and red Brain 5 represents LSFM image of a clarified brain, which have been also shown in <xref rid="fig2s2" ref-type="fig">Figure 2&#x02014;figure supplement 2</xref>. The median error distance of 20 pairs of POIs in the two brains registered by BIRDS is ~104 &#x003bc;m, which is compared to ~175 &#x003bc;m error distance by single-channel registration. (<bold>b</bold>) Line chart specifically showing the distance of each pair of points in two types of datasets by two registration methods. Besides smaller median error distance, here BIRDS registration with including feature information also yields smaller distance variance (solid lines), as compared to single-channel method (dash line). (<bold>c</bold>) Dice score comparison of two registration methods at nuclei level. Eighteen regions in two brains (nine regions for each) are selected for the analysis, with results grouped by the datasets. It is clearly shown that the median Dice scores by our method are higher than those by single-channel registration, especially for the highly deformed clarified brain (Brain 5, 0.65 vs 0.881). (<bold>d</bold>) Dice scores of two registration methods, with results grouped by nine selected sub-regions (five planes included for each region), which are ACA, ENT, MV, PAG, RT, SSp, SUB, VISp, and VMH. The black lines indicate the median Dice scores for each regions. Overall, Dice scores with averaged median value of &#x0003e;0.9 (calculated by two brains) or &#x0003e;0.87 (calculated by nine regions) were obtained by our BIRDS. These two values are obviously higher than 0.74 and 0.76 by single-channel registration.</p><p>
<supplementary-material id="fig2s3sdata1" position="float" content-type="local-data"><label>Figure 2&#x02014;figure supplement 3&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig2s3" ref-type="fig">Figure 2&#x02014;figure supplement 3</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2-figsupp3-data1.zip" id="d64e824" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2-figsupp3" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig2s4" specific-use="child-fig"><label>Figure 2&#x02014;figure supplement 4.</label><caption><title>Dice score comparison of nine regions in five brains registered by four registration tools: aMAP, ClearMap, MIRACL, and our BIRDS.</title><p>The results are grouped by brains in (<bold>a</bold>) and regions in (<bold>b</bold>). The calculation/comparison is implemented at region level with ~100 &#x003bc;m resolution. When the results are analyzed by brains, BIRDS surpass the other three methods most on LSFM dataset #5, with 0.944 median Dice score being compared to 0.801 by aMAP, 0.848 by ClearMap, and 0.835 by MIRACL. At the same time, all the methods perform well on STPT dataset #2 with median Dice of 0.919 by aMAP, 0.969 by ClearMap, 0.915 by MIRACL, and 0.977 by our BIRDS. When the results are compared by nine functional regions, the median values acquired by our BIRDS were also higher than the other three methods. Even the lowest median Dice score by our method is still 0.885 (indicated by black line), which is notably higher than 0.834 by aMAP, 0.82 by ClearMap, and 0.853 by MIRACL.</p><p>
<supplementary-material id="fig2s4sdata1" position="float" content-type="local-data"><label>Figure 2&#x02014;figure supplement 4&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig2s4" ref-type="fig">Figure 2&#x02014;figure supplement 4</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2-figsupp4-data1.zip" id="d64e848" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig2-figsupp4" position="float"/></fig></p></fig></sec><sec id="s2-2"><title>Comparison with conventional single-channel-based registration methods</title><p>Next, we merged our experimental brain image with a registered annotation file to generate a 3D annotated image and quantitatively compared its registration accuracy with aMAP, ClearMap, and MIRACL results. We made comparisons of both STPT data from intact brains that contained only minor deformations (<xref rid="fig2" ref-type="fig">Figure 2a</xref>) and LSFM data from clarified brains (u-DISCO) that showed obvious shrinkage (<xref rid="fig2" ref-type="fig">Figure 2b</xref>). It should be noted here that the annotated results of either previous single-channel methods or our bi-channel method were all using automatic registration without any manual correction applied, and the averaged manual annotations by our experienced researchers served as a ground truth for quantitative comparisons. It was visually obvious that, as compared to the other three methods (green: aMAP; red: ClearMap; and blue: MIRACL in <xref rid="fig2" ref-type="fig">Figure 2a,b</xref>), the Allen annotation files transformed and registered by our BIRDS method (yellow in <xref rid="fig2" ref-type="fig">Figure 2a,b</xref>) were far better aligned with both STPT (as shown in VISC, CENT, AL, and PAL regions, <xref rid="fig2" ref-type="fig">Figure 2a</xref>) and LSFM (as shown in HPF, CB, VIS, and COA regions, <xref rid="fig2" ref-type="fig">Figure 2b</xref>) images. Furthermore, we manually labeled 10 3D fiducial points of interest (POIs) across the registered Allen template images together with their corresponding experimental images (<xref rid="fig2" ref-type="fig">Figure 2c</xref>) and then measured the error distances between the paired anatomical landmarks in the two datasets, so that the registration accuracy by each registration method could be quantitatively evaluated (<xref rid="fig2s1" ref-type="fig">Figure 2&#x02014;figure supplement 1</xref>). As shown in <xref rid="fig2" ref-type="fig">Figure 2d</xref>, the error distance distributions of POIs in five brains (two STPT + three LSFM) registered by the abovementioned four methods were then quantified, showing the smallest median error distance (MED) was obtained using our method for all five brains (<xref rid="supp3" ref-type="supplementary-material">Supplementary file 3</xref>). In two different sets of STPT data, only our BIRDS method could provide an MED below 100 &#x003bc;m (~80 &#x003bc;m, n = 2), and this value slightly increased to ~120 &#x003bc;m for LSFM data (n = 3), but was still smaller than all the results obtained using the other three methods (aMAP, ~342 &#x003bc;m, n = 3; ClearMap, ~258 &#x003bc;m, n = 3; and MIRACL, ~175 &#x003bc;m, n = 3). Moreover, the Dice scores (<xref rid="bib8" ref-type="bibr">Dice, 1945</xref>), defined as a similarity scale function used to calculate the similarity of two samples, for each method were also calculated at the nucleus precision level based on nine functional regions in the five brains. The comparative results were then grouped by brain and region, as shown in <xref rid="fig2" ref-type="fig">Figure 2e,f</xref>, respectively. The highest Dice scores with an average median value of &#x0003e;0.89 (<xref rid="supp3" ref-type="supplementary-material">Supplementary file 3</xref>, calculated for five brains, 0.75, 0.81, and 0.81 for aMAP, ClearMap, and MIRACL) or &#x0003e;0.88 (<xref rid="supp3" ref-type="supplementary-material">Supplementary file 3</xref>, calculated using nine regions, 0.74, 0.77, and 0.84 for aMAP, ClearMap, and MIRACL, respectively) were obtained by BIRDS, further confirming the superior registration accuracy of our method. Through a comparative Wilcoxon test, our results were demonstrated to be superior to the other three methods (providing larger Dice scores) with a p value &#x0003c; 0.05 calculated either by brain or by region. More detailed comparisons of registration accuracies can be found in <xref rid="fig2s2" ref-type="fig">Figure 2&#x02014;figure supplements 2</xref>&#x02013;<xref rid="fig2s4" ref-type="fig">4</xref>.</p></sec><sec id="s2-3"><title>Whole-brain digital map identifying the distributions of labeled neurons and axon projections</title><p>A 3D digital map (CCFv3) based on the abovementioned bi-channel registration was generated to support automatic annotation, analysis, and visualization of neurons in a whole mouse brain (see Materials and methods). The framework thus enabled large-scale mapping of neuronal connectivity and activity to reveal the architecture and function of brain circuits. Here, we demonstrated how the BIRDS pipeline visualizes and quantifies single-neuron projection patterns obtained by STPT imaging. A mouse brain containing six GFP-labeled layer-2/3 neurons in the right visual cortex was imaged with STPT at 1 &#x000d7; 1 &#x000d7; 10 &#x003bc;m<sup>3</sup> resolution (<xref rid="bib14" ref-type="bibr">Han et al., 2018</xref>). After applying the BIRDS procedure to this STPT image stack, we generated a 3D map of this brain (<xref rid="fig3" ref-type="fig">Figure 3a</xref>). An interactive hierarchal tree of brain regions in the software interface allowed navigation through the corresponding selected-and-highlighted brain regions with its annotation information (<xref rid="fig3" ref-type="fig">Figure 3b</xref>, <xref rid="video1" ref-type="video">Video 1</xref>). Through linking with Imaris, we visualized and traced each fluorescently labeled neuronal cell (n = 5) using the filament module of Imaris across the 3D space of the entire brain (<xref rid="fig3" ref-type="fig">Figure 3c</xref>, Materials and methods, <xref rid="video2" ref-type="video">Video 2</xref>). The BIRD software can also apply reverse transformation to a raw image stack to generate a standard template-like rendered 3D map, including both traced axonal projections and selected whole-brain structures, which faithfully captures true 3D axonal arborization patterns and anatomical locations, as shown in <xref rid="fig3" ref-type="fig">Figure 3d</xref>. This software can also quantify the lengths and arborizations of traced axons according to the segmentation of the 3D digital map generated using the BIRDS pipeline (<xref rid="fig3" ref-type="fig">Figure 3e</xref>).</p><fig position="float" id="fig3"><label>Figure 3.</label><caption><title>3D digital atlas of a whole brain for visualization and quantitative analysis of inter-areal neuronal projections.</title><p>(<bold>a</bold>) Rendered 3D digital atlas of a whole brain (a2, pseudo color), which was generated from registered template and annotation files (a1, overlay of annotation mask and image data). (<bold>b</bold>) Interactive hierarchical tree shown as a sidebar menu in the BIRDS program, indexing the name of brain regions annotated in CCFv3. Clicking on any annotation name in the side bar of the hierarchal tree highlights the corresponding structure in the 3D brain map (b1, b2), and vice versa. For example, brain region LP was highlighted in the space after its name was chosen in the menu (b1). 3D rendering of an individual brain after applying a deformation field in reverse to a whole brain surface mask. The left side of the brain displays the 3D digital atlas (CCFv3, colored part in b2), while the right side of the brain is displayed in its original form (grayscale part in b2). (<bold>c</bold>) The distribution of axonal projections from five single neurons in 3D map space. The color-rendered space shown in horizontal, sagittal, and coronal views highlights multiple areas in the telencephalon, anterior cingulate cortex, striatum, and amygdala, which are all potential target areas of layer-2/3 neuron projections. (<bold>d</bold>) The traced axons of five selected neurons (n = 5) are shown. ENTm, entorhinal area, medial part, dorsal zone; RSPagl, retrosplenial area, lateral agranular part; VIS, visual areas; SSp-bfd, primary somatosensory area, barrel field; AUDd, dorsal auditory area; AUDpo, posterior auditory area; TEa, temporal association areas; CP, caudoputamen; IA, intercalated amygdalar nucleus; LA, lateral amygdalar nucleus; BLA, basolateral amygdalar nucleus; CEA, central amygdalar nucleus; ECT, ectorhinal area. (<bold>e</bold>) Quantification of the projection strength across the targeting areas of five GFP-labeled neurons. The color codes reflect the projection strengths of each neuron, as defined as axon length per target area, normalized to the axon length in VIS.</p><p>
<supplementary-material id="fig3sdata1" position="float" content-type="local-data"><label>Figure 3&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig3" ref-type="fig">Figure 3e</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig3-data1.zip" id="d64e962" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig3" position="float"/></fig><fig id="video1" position="anchor"><label>Video 1.</label><caption><title>Displays the 3D digital atlas.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-video1.mp4" mimetype="video" mime-subtype="mp4"/></fig><fig id="video2" position="anchor"><label>Video 2.</label><caption><title>Shows the arborization of 5 neurons in 3D map space.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-video2.mp4" mimetype="video" mime-subtype="mp4"/></fig><p>BIRDS can be linked to Imaris to perform automated cell counting with higher efficiency and accuracy (Materials and methods). Here, we demonstrate it with an example brain where neurons were retrogradely labeled by CAV-mCherry injected to the right striatum and imaged by STPT at 1&#x000d7;1&#x000d7;10 &#x003bc;m<sup>3</sup> resolution (<xref rid="bib14" ref-type="bibr">Han et al., 2018</xref>). The whole-brain image stacks were first processed by BIRDS to generate a 3D annotation map. Two of the example segregated brain areas (STR and BS) are outlined in the left panel of <xref rid="fig4" ref-type="fig">Figure 4a</xref>. The annotation map and the raw image stack were then transferred to Imaris, which processed the images within each segregated area independently. Imaris calculated the local image statistics for cell recognition only using the image stack within each segregated area; therefore, it fit the dynamic range of the local images to achieve better results, as shown in the middle column in the right panel of <xref rid="fig4" ref-type="fig">Figure 4a</xref>. In contrast, the conventional Imaris automated cell counting program processed the whole-brain image stack at once to calculate the global cell recognition parameters for every brain area, which easily resulted in false positive or false negative counts in brain areas where the labeling signal was too strong or too weak compared to the global signal, as demonstrated in the STR and SB in the right column of the right panels of <xref rid="fig4" ref-type="fig">Figure 4a</xref>, respectively. The BIRDS&#x02013;Imaris program could perform automated cell counting for each brain area and reconstructed them over the entire brain. The 3D model of the brain-wise distribution of labeled striatum-projecting neurons was visualized using the BIRDS&#x02013;Imaris program as a 3D rendered brain image and projection views from three axes in <xref rid="fig4" ref-type="fig">Figure 4b</xref>. The BIRDS program could calculate the volume of each segregated region according to the 3D segregation map and the density of labeled cells across the brain as shown in <xref rid="fig4" ref-type="fig">Figure 4c</xref>. Meanwhile, manual cell counting was also performed with every one out of four sections using an ImageJ plugin (<xref rid="fig4" ref-type="fig">Figure 4d</xref>). Compared to conventional Imaris results, our BIRDS&#x02013;Imaris results were more consistent with a manual one, especially for brain regions where the fluorescent signal was at the high or low end of the dynamic range (BS and STR, <xref rid="fig4" ref-type="fig">Figure 4e</xref>). Thanks to the 3D digital map generated by the BIRDS pipeline, BIRDS&#x02013;Imaris can process each segmented brain area separately, namely calculating the parameters for the cell recognition algorithm using local image statistics instead of processing the whole-brain image stack at once. Such a segmented cell counting strategy is much less demanding on computation resources, and moreover, it is optimized for each brain area to solve the problem that the same globe cell recognition parameter works poorly in certain brain regions with signal intensity at either of the two extreme ends of the dynamic range of the entire brain.</p><fig position="float" id="fig4"><label>Figure 4.</label><caption><title>Cell-type-specific counting and comparison between different cell counting methods.</title><p>(<bold>a</bold>) Cell counting of retrogradely labeled striatum-projecting cells. We selected two volumes (1 &#x000d7; 1 &#x000d7; 1 mm<sup>3</sup>) from SS and VIS areas, respectively, to show the difference in cell density and the quantitative results by BIRDS&#x02013;Imaris versus conventional Imaris. Here, a separate quantification parameters set for different brain areas in the BIRD&#x02013;Imaris procedure lead to obviously more accurate counting results. Scale bar, 2 mm. (<bold>b</bold>) 3D-rendered images of labeled cells in the whole brain space, shown in horizontal, sagittal, and coronal views. The color rendering of the cell bodies was in accordance with CCFv3, and the cells were mainly distributed in the Isocortex (darker hue). (<bold>c</bold>) The cell density calculated for 20 brain areas. The cell densities of MO and SS were highest (MO = 421.80 mm<sup>&#x02212;3</sup>; SS = 844.71 mm<sup>&#x02212;3</sup>) among all areas. GU, gustatory areas; TEa, temporal association areas; AI, agranular insular area; PL, prelimbic area; PERI, perirhinal area; RSP, retrosplenial area; ECT, ectorhinal area; ORB, orbital area; VISC, visceral area; VIS, visual areas; MO, somatomotor areas; SS, somatosensory areas; AUD, auditory areas; HPF, hippocampal formation; OLF, olfactory areas; CTXsp, cortical subplate; STR, striatum; PAL, pallidum; BS, brainstem; CB, cerebellum. (<bold>d</bold>) Comparison of the cell numbers from three different counting methods, BIRDS, Imaris (3D whole brain directly), and manual counting (2D slice by slice for a whole brain). (<bold>e</bold>) The cell counting accuracy using BIRDS&#x02013;Imaris (orange) and conventional Imaris methods (blue), relative to manual counting. Besides the highly divergent accuracy for the 20 regions, the counting results by conventional Imaris in STR and BS regions were especially inaccurate.</p><p>
<supplementary-material id="fig4sdata1" position="float" content-type="local-data"><label>Figure 4&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig4" ref-type="fig">Figure 4</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig4-data1.zip" id="d64e1035" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig4" position="float"/></fig></sec><sec id="s2-4"><title>Inference-based segmentation of incomplete brain datasets using a deep-learning procedure</title><p>In practice, acquired brain datasets are often incomplete, due to researcher&#x02019;s particular interest in specific brain regions, or limited imaging conditions. The registration of such incomplete brain datasets to an Allen template is often difficult due to the lack of sufficient morphology information for comparison of both datasets. To overcome this limitation, we further introduced a deep neural network (DNN)-based method for efficient segmentation/annotation of incomplete brain sections with minimal human supervision. Herein, we optimized a Deeplab V3+ network, which was based on an encoding-decoding structure, for our deep-learning implementation (<xref rid="fig5" ref-type="fig">Figure 5a</xref>). The input images passed through a series of feature processing stages in the network, with pixels being allocated, classified, and segmented into brain regions. It should be noted that the training of a neural network fundamentally requires a sufficiently large dataset containing various incomplete brain blocks which have been well segmented. Benefiting from our efficient BIRDS method, we could readily obtain a large number of such labeled datasets through cropping processed whole brains and without experiencing time-consuming manual annotation. Various types of incomplete brains, as shown in <xref rid="fig5" ref-type="fig">Figure 5b</xref>, were hereby generated and sent to our DNN for iterative training, after which the skilled network could directly infer the segmentations/annotations for new modes of incomplete brain images (Materials and methods). Next, we validated the network performance on three different modes of input brain images cropped from the registered whole-brain dataset (STPT). The DNN successfully inferred annotation results for a cropped hemisphere, irregular cut of hemisphere, and a randomly cropped volume, as shown in <xref rid="fig5" ref-type="fig">Figure 5c&#x02013;e</xref>, respectively. The inferred annotations (red lines) were found to be highly similar to the registered annotation results (green lines) in all three types of incomplete data. To further quantify the inference accuracy, the Dice scores of the network-segmented regions were also calculated by comparing the network outputs to ground truth, which was the registration results after visual inspection and correction (<xref rid="fig5s1" ref-type="fig">Figure 5&#x02014;figure supplement 1</xref>). The averaged median Dice scores for the individual sub-regions in the hemisphere, irregular cut of hemisphere, and random volumes were 0.86, 0.87, and 0.87, respectively, showing a sufficiently high inference accuracy in most of brain regions, such as the isocortex, HPF, OLF, or STR. It is worth noting that the performance of our network for segmentation using PAL, MBsta, P-sen regions remained sub-optimal (Dice score 0.78&#x02013;0.8), due to their lack of obvious borders, and large structural variations across planes (<xref rid="fig5s1" ref-type="fig">Figure 5&#x02014;figure supplement 1</xref>). Finally, we applied our network inferences to generate 3D atlases for these three incomplete brains, while segmenting the hemisphere into 18 regions such as the Isocortex, HPF, OLF, CTXsp, STR, PAL, CB, DORpm, DORsm, HY, MBsen, MBmot, MBsta, P-sen, P-mot, P-sat, MY-sen, and MY-mot, while we processed an irregular cut of half the telencephalon into 10 regions as Isocortex, HPF, OLF, CTXsp, STR, PAL, DORpm, DORsm, and HY, MY-mot, and the random volume into seven regions, defined as the Isocortex, HPF, STR, PAL, DORpm, DORsm, and HY (<xref rid="fig5" ref-type="fig">Figure 5f,g,h</xref>). Therefore, our DNN performed reasonably well even if the brain was highly incomplete. Furthermore, it could achieve second-level fine segmentation within a small brain region of interest. For example, we successfully segmented the hippocampus (CA1, CA2, CA3, and DG), as shown in <xref rid="fig5s2" ref-type="fig">Figure 5&#x02014;figure supplement 2</xref>. Such a unique capability of our DNN was possibly derived from the detection of pixel-level features rather than regions, and thereby substantially strengthened the robustness of our hybrid BIRDS method over conventional brain registration techniques when the data is highly incomplete/defective. More detailed performance comparisons between our DNN-based inference and other methods are shown in <xref rid="fig5s1" ref-type="fig">Figure 5&#x02014;figure supplements 1</xref>&#x02013;<xref rid="fig5s5" ref-type="fig">5</xref>.</p><fig position="float" id="fig5"><label>Figure 5.</label><caption><title>Inference-based network segmentation of incomplete brain data.</title><p>(<bold>a</bold>) The deep neural network architecture for directly inferring brain segmentation without required registration. The training datasets contained various types of incomplete brain images, which were cropped from annotated whole-brain datasets created using our bi-channel registration beforehand. (<bold>b</bold>) Four models of incomplete brain datasets for network training: whole brain (1), a large portion of telencephalon (2), a small portion of telencephalon (3), and a horizontal slab of whole brain (4). Scale bar, 1 mm. (<bold>c&#x02013;e</bold>) The inference-based segmentation results for three new modes of incomplete brain images, defined as the right hemisphere (<bold>c</bold>), an irregular cut of half the telencephalon (<bold>d</bold>), and a randomly cropped volume (<bold>e</bold>). The annotated sub-regions are shown in the x-y, x-z, and y-z planes, with the Isocortex, HPF, OLF, CTXsp, STR, PAL, CB, DORpm, DORsm, HY, MBsen, MBmot, MBsta, P-sen, P-mot, P-sat, MY-sen, and MY-mot for right hemisphere, Isocortex, HPF, OLF, CTXsp, STR, PAL, DORpm, DORsm, HY, and MY-mot for the irregular cut of half the telencephalon, and Isocortex, HPF, STR, PAL, DORpm, DORsm, and HY for the random volume. Scale bar, 1 mm. (<bold>f&#x02013;h</bold>) Corresponding 3D atlases generated for these three incomplete brains.</p><p>
<supplementary-material id="fig5sdata1" position="float" content-type="local-data"><label>Figure 5&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig5" ref-type="fig">Figure 5</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-data1.zip" id="d64e1101" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5" position="float"/><p content-type="supplemental-figure"><fig position="anchor" id="fig5s1" specific-use="child-fig"><label>Figure 5&#x02014;figure supplement 1.</label><caption><title>Dice scores of the DNN-segmented regions in three new types of incomplete brains.</title><p>These three incomplete brains, also shown in <xref rid="fig4" ref-type="fig">Figure 4c&#x02013;e</xref>, all look different with the training data modes. Thereby, we can test the real inference capability of the trained DNN for segmenting unfamiliar data. (<bold>a</bold>) Dice scores of 18 DNN-segmented regions in right hemisphere. The ground-truth references used for calculation are the results obtained by bi-channel registration plus manual correction. The averaged median value of Dice scores was 0.86, with maximum value of 0.965 at Isocortex and minimum value of 0.78 at P-sen. (<bold>b</bold>) Dice scores of 10 DNN-segmented regions in an irregular cut of half telencephalon. The averaged median value of Dice scores was 0.87, with maximum value of 0.97 at Isocortex and minimum value of 0.771 at PAL. (<bold>c</bold>) Dice scores of seven DNN-segmented regions in a small random cut of brain. The averaged median value of Dice scores was 0.86, with maximum value of 0.958 at Isocortex and minimum value of 0.745 at PAL.</p><p>
<supplementary-material id="fig5s1sdata1" position="float" content-type="local-data"><label>Figure 5&#x02014;figure supplement 1&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig5s1" ref-type="fig">Figure 5&#x02014;figure supplement 1</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-figsupp1-data1.zip" id="d64e1130" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-figsupp1" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig5s2" specific-use="child-fig"><label>Figure 5&#x02014;figure supplement 2.</label><caption><title>Performance of DNN inference for segmenting fine sub-regions specifically in the hippocampus.</title><p>Four coronal planes containing hippocampal sub-regions of CA1, CA2, CA3, and the DG are shown and compared to the bi-channel registration results. (<bold>a, b</bold>) Color-rendered segmentations of CA1, CA2, CA3, and DG by network inference and bi-channel registration (with manual correction), respectively. Scale bar, 1 mm. (<bold>c, d</bold>) Magnified views of the selected small regions (boxes) revealing the highly similar segmentation results by DNN inference and bi-channel registration. Scale bar, 250 &#x003bc;m. The averaged median value of Dice scores was 0.878, with maximum value of 0.96 at CA1 and minimum value of 0.70 at CA2.</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-figsupp2" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig5s3" specific-use="child-fig"><label>Figure 5&#x02014;figure supplement 3.</label><caption><title>Accuracy comparison between DNN-based and registration-based brain segmentation.</title><p>We test the accuracy of DNN-inference-based segmentation through comparison with bi-channel registration results (after manual correction). An entire STP brain and three types of cropped incomplete brain regions, which are the same modes of data selected for network training (<xref rid="fig2" ref-type="fig">Figure 2b</xref>), are included for performance validation, with results shown in (<bold>a</bold>&#x02013;<bold>d</bold>). In the comparative analysis of each group of data, we showed the same three sagittal planes (20 &#x003bc;m interval) segmented by DNN inference (upper row) and bi-channel registration (lower row). Eighteen segmented regions compared in the four modes of brain data (Isocortex, HPF, OLF, CTXsp, STR, PAL, CB, DORpm, DORsm, HY, MBsen, MBmot, Mbsta, P-sen, P-mot, P-sat, MY-sen, MY-mot) have widely verified the sufficient segmentation accuracy by our DNN inference. Scale bar, 1 mm.</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-figsupp3" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig5s4" specific-use="child-fig"><label>Figure 5&#x02014;figure supplement 4.</label><caption><title>Dice scores of the DNN-segmented regions in abovementioned four types of brains.</title><p>Data plots in <bold>a, b, c, d</bold> correspond to segmentation results shown in <bold>a, b, c, d</bold>, respectively. The average median value of Dice scores for most of the individual regions in all four brain modes are above 0.8, for example, Isocortex (~0.941), HPF (~0.884), OLF (~0.854), STR (~0.908), thereby showing a high inference accuracy for most of brain regions. At the same time, the performance of the network for segmenting PAL (~0.761) and MBsta (~0.799) regions remain limited, possibly owing to the large structure variation of these two regions as we move from the lateral to medial images across the sagittal plane.</p><p>
<supplementary-material id="fig5s4sdata1" position="float" content-type="local-data"><label>Figure 5&#x02014;figure supplement 4&#x02014;source data 1.</label><caption><title>Source data file for <xref rid="fig5s4" ref-type="fig">Figure 5&#x02014;figure supplement 4</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-figsupp4-data1.zip" id="d64e1181" position="anchor"/></supplementary-material>
</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-figsupp4" position="float"/></fig></p><p content-type="supplemental-figure"><fig position="anchor" id="fig5s5" specific-use="child-fig"><label>Figure 5&#x02014;figure supplement 5.</label><caption><title>Comparison of the performance of DNN inference by three deep learning-based brainregion segmentation techniques, QuickNAT, SeBRe and our BIRDS (DNN).</title><p>(<bold>a</bold>) BIRDS (Bi-channel registration) and DNNs-inference-based segmentation results of an entire STP brain. Scale bar, 1mm. In the comparative analysis of each method, we showed the same three sagittal planes (20-&#x003bc;m interval) segmented by DNN inference (BIRDS, QuickNAT, SeBRe) and bi-channel registration. Eighteen regions (Isocortex, HPF, OLF, CTXsp, STR, PAL, CB, DORpm, DORsm, HY, MBsen, MBmot, Mbsta, P-sen, P-mot, P-sat, MY-sen, MY-mot ) segmented by the three deep learning-based methods have widely verified the sufficient segmentation accuracy by DNN inference. In most of brain regions the performance of BIRDS (DNN) is the best among the three (*), except CTXsp, PAL, and MBsta (QuickNAT is better than BIRDS (DNN) and SeBRe).</p></caption><graphic xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-fig5-figsupp5" position="float"/></fig></p></fig></sec></sec><sec sec-type="discussion" id="s3"><title>Discussion</title><p>In summary, we demonstrate a bi-channel image registration method, in conjunction with a deep-learning framework, to readily provide accuracy-improved anatomical segmentation for whole mouse brain in reference to an Allen average template, and direct segmentation inference for incomplete brain datasets, which were otherwise not easily registered to standardized whole-brain space. The addition of a brain feature channel to the registration process greatly improved the accuracy of automatically registering individual whole-brain data with a standardized Allen average template. It should be noted that the registration was based on two-photon template images provided by Allen CCF, so it is currently limited to using on like-imaged brains, for example, brains imaged using wide-field, confocal, or light-sheet microscopes, etc. For processing various incomplete brain datasets, which were challenging for registration-based methods while remaining very common in neuroscience research, we applied our deep neural network to rapidly infer segmentations. The sufficiently accurate results shown using different types of incomplete data verify the advances of network segmentation. Though a full annotation using a neural network is currently too computationally demanding as compared to registration-based segmentation, it is undoubtedly a good complement to registration-based segmentation. Therefore, in our hybrid BIRDS pipeline, the DNN inference greatly reinforced the inefficient side of registration, while the registration also readily provided high-quality training data for our DNN. We believe such a synergistic effect in our method could provide a paradigm shift for enabling robust and efficient 3D image segmentation/annotation for biology research. With the unceasing development of deep learning, we envision that network-based segmentation will play an increasingly important role in new pipelines. A variety of applications, such as tracing of long-distance neuronal projections and parallel counting of cell populations in different brain regions, was also enabled as a result of our efficient brain mapping. The BIRDS pipeline is now fully open source and also has been packaged into a Fiji plugin to facilitate biological researchers. We sincerely expect that the BIRDS method can immediately allow new insights using current brain mapping techniques, and thus further push the resolution and scale limits in future explorations of brain space.</p></sec><sec sec-type="materials|methods" id="s4"><title>Materials and methods</title><sec id="s4-1"><title>Acquisition of STPT image dataset</title><p>Brains 1 and 2 were obtained with STPT, and each dataset encompassed ~180 Gigavoxels, for example, 11,980 &#x000d7; 7540 &#x000d7; 1075 in Dataset 1, with a voxel size of 1 &#x000d7; 1 &#x000d7; 10 &#x003bc;m<sup>3</sup>. The procedure of sample preparation and imaging acquisition were described in <xref rid="bib14" ref-type="bibr">Han et al., 2018</xref>. Briefly, the adult C57BL/6 mouse (RRID:<ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://identifiers.org/RRID/RRID:IMSR_JAX:000664" ext-link-type="uri">IMSR_JAX:000664</ext-link>) was anesthetized, and craniotomy was performed on top of the right visual cortex. Individuals neuronal axons were labeled with plasmid DNA (pCAG-eGFP [Addgene accession 11150]) by two-photon microscopy-guided single-cell electroporation, and the brain was fixed by cardioperfusion of 4% paraformaldehyde 8 days later. Striatum-projecting neurons were labeled by stereotactically injecting PRV-cre into the right striatum of tdTomato reporter mice (Ai14, JAX), and the brain was fixed cardioperfusion 30 days later. The brains were embedded in 5% oxidized agarose and imaged with a commercial STPT (TissueVision, USA) excited at 940 nm. Coronally, the brain was optically scanned every 10 &#x003bc;m at 1 &#x003bc;m/pixel without averaging and physically sectioned every 50 &#x003bc;m. The power of excitation laser was adjusted to compensate the depth of optical sections.</p></sec><sec id="s4-2"><title>Acquisition of LSFM images dataset</title><p>Brains 3, 4, and 5 were obtained with LSFM and each dataset encompassed ~700 Gigavoxels (~10,000 &#x000d7; 8000 &#x000d7; 5000), with an isotropic voxel size of 1 &#x003bc;m<sup>3</sup>. Brain tissues of eight-week-old Thy-GFP-M mice (RRID:<ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://identifiers.org/RRID/RRID:IMSR_JAX:007788" ext-link-type="uri">IMSR_JAX:007788</ext-link>) were first clarified with u-DISCO protocol (<xref rid="bib34" ref-type="bibr">Pan et al., 2016</xref>) before imaging. Brains 3 and 4 were acquired using a custom-built Bessel plane illumination microscope, a type of LSFM modality employing non-diffraction thin Bessel light-sheet. Brain 5 was whole-brain 3D image of a Thy-GFP-M mice acquired using a lab-built selective plane illumination microscope (<xref rid="bib31" ref-type="bibr">Nie et al., 2020</xref>), another LSFM modality combining Gaussian light-sheet with multi-view image acquisition/fusion.</p></sec><sec id="s4-3"><title>Implementation of Bi-channel registration</title><sec id="s4-3-1"><title>Preprocessing of raw data</title><p>First, we developed an interactive GUI in the Fiji plugin (RRID:<ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://scicrunch.org/resolver/RRID:SCR_002285" ext-link-type="uri">SCR_002285</ext-link>) to correspond the coronal planes in the Allen Reference Atlas (ARA) (132 planes, 100 &#x003bc;m interval) with those in the experimental 3D image stack (e.g., 1075 planes with 10 &#x003bc;m stepsize in Dataset 1). As shown in <xref rid="fig1s1" ref-type="fig">Figure 1&#x02014;figure supplement 1</xref>, seven coronal planes, from the anterior bulbus olfactorius to the posterior cerebellum, were identified across the entire brain, with their number of layers being recorded as <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf1" overflow="scroll"><mml:mrow><mml:msub><mml:mi>a</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> in template atlas and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf2" overflow="scroll"><mml:mrow><mml:msub><mml:mi>b</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> in the raw image stack. Therefore, <italic toggle="yes">k</italic> sub-stacks (<inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf3" overflow="scroll"><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mo>[</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mn>6</mml:mn><mml:mo>]</mml:mo><mml:mo>,</mml:mo><mml:mi>k</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi>N</mml:mi></mml:mrow></mml:math></inline-formula>) was defined by these seven planes (<xref rid="fig1" ref-type="fig">Figure 1b</xref>). According to the ratio of step size between the ARA and its template image stack (100 &#x003bc;m&#x02013;20 &#x003bc;m), we also obtained the number of layers of the selected planes in the template image as <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf4" overflow="scroll"><mml:mrow><mml:msub><mml:mi>c</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>5</mml:mn><mml:msub><mml:mi>a</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:math></inline-formula>. The reslicing ratio of the <italic toggle="yes">k</italic>th sub-stack sandwiched by every two planes (a<italic toggle="yes"><sub>k</sub></italic> to a<italic toggle="yes"><sub>k+1</sub></italic>) was then calculated by: <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf5" overflow="scroll"><mml:mrow><mml:mi>l</mml:mi><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:msub><mml:mi>b</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mi>b</mml:mi><mml:mi>k</mml:mi></mml:mrow><mml:mrow><mml:msub><mml:mi>c</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mi>c</mml:mi><mml:mi>k</mml:mi></mml:mrow></mml:mfrac></mml:mrow></mml:math></inline-formula>. Each <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf6" overflow="scroll"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> was applied to the <italic toggle="yes">k</italic>th sub-stack to obtain the resliced version of the sub-stack. Finally, the six resliced sub-stacks together formed a complete image stack of whole brain (20 &#x003bc;m stepsize), which had a rectified shape more similar to the Allen average template image, as compared to the raw experimental data. According to the isotropic voxel size of 20 &#x003bc;m in the template, the lateral size of voxel in the resliced image stack was also adjusted from originally 1 &#x003bc;m to 20 &#x003bc;m with a uniform lateral down-sampling ratio of 20 applied to all the coronal planes. The computational cost of abovementioned data re-sampling operation was low, taking merely ~5 min for processing 180 GB raw STPT data on a Xeon workstation (E5-2630 V3 CPU).</p></sec><sec id="s4-3-2"><title>Features extraction and processing</title><p>We extracted feature information based on the purified signals of the image data with backgrounds filtrated. We realized this through calculating the threshold values of the signals and backgrounds using by Huang&#x02019;s fuzzy thresholding method (<xref rid="bib16" ref-type="bibr">Huang and Wang, 1995</xref>) and removing the backgrounds according to the calculated thresholds. Then the feature information was detected using a PC algorithm, which was robust to the intensity change of signals, and could efficiently extract corners, lines, textures information from the image. Furthermore, when the images had relatively low contrast at the border, which was very common in our study, the edge information could be much better retained using PC detection. Finally, the pixel intensity in the generated PC feature map can be calculated by following formula:<disp-formula id="equ1"><label>(1)</label><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="m1" overflow="scroll"><mml:mrow><mml:mtext>E</mml:mtext><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mstyle displaystyle="true"><mml:msub><mml:mo>&#x02211;</mml:mo><mml:mi>n</mml:mi></mml:msub><mml:mrow><mml:msub><mml:mi>A</mml:mi><mml:mi>n</mml:mi></mml:msub><mml:mo>[</mml:mo><mml:mi>cos</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>n</mml:mi></mml:msub><mml:mtext>&#x02009;</mml:mtext><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mover accent="true"><mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mtext>&#x02009;</mml:mtext></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover><mml:mtext>&#x02009;</mml:mtext><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>)</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mo>|</mml:mo><mml:mi>sin</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>n</mml:mi></mml:msub><mml:mtext>&#x02009;</mml:mtext><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mover accent="true"><mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mtext>&#x02009;</mml:mtext></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover><mml:mtext>&#x02009;</mml:mtext><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>)</mml:mo><mml:mo>|</mml:mo><mml:mo>]</mml:mo></mml:mrow></mml:mstyle></mml:mrow></mml:math></disp-formula><disp-formula id="equ2"><label>(2)</label><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="m2" overflow="scroll"><mml:mrow><mml:mi>P</mml:mi><mml:mi>C</mml:mi><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>W</mml:mi><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mrow><mml:mo>&#x0230a;</mml:mo><mml:mrow><mml:mo>|</mml:mo><mml:mi>E</mml:mi><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>|</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mi>T</mml:mi></mml:mrow><mml:mo>&#x0230b;</mml:mo></mml:mrow></mml:mrow><mml:mrow><mml:mstyle displaystyle="true"><mml:msub><mml:mo>&#x02211;</mml:mo><mml:mi>n</mml:mi></mml:msub><mml:mrow><mml:msub><mml:mi>A</mml:mi><mml:mi>n</mml:mi></mml:msub><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo><mml:mo>+</mml:mo><mml:mi>&#x003b5;</mml:mi></mml:mrow></mml:mstyle></mml:mrow></mml:mfrac></mml:mrow></mml:math></disp-formula>where <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf7" overflow="scroll"><mml:mi>x</mml:mi></mml:math></inline-formula> is the angle vector of a pixel after Fourier transform of the image, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf8" overflow="scroll"><mml:mrow><mml:msub><mml:mi>A</mml:mi><mml:mi>n</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the local amplitude of the nth cosine component, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf9" overflow="scroll"><mml:mrow><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>n</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the local phase, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf10" overflow="scroll"><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mtext>&#x02009;</mml:mtext></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover><mml:mtext>&#x02009;</mml:mtext></mml:mrow></mml:math></inline-formula> is the weighted average of phase, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf11" overflow="scroll"><mml:mrow><mml:mtext>E</mml:mtext><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:math></inline-formula> is the local energy, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf12" overflow="scroll"><mml:mrow><mml:mi>W</mml:mi><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:math></inline-formula> is the filter band, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf13" overflow="scroll"><mml:mi>T</mml:mi></mml:math></inline-formula> is the noise threshold, and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf14" overflow="scroll"><mml:mi>&#x003b5;</mml:mi></mml:math></inline-formula> is a small positive number (=0.01 in our practice) to prevent the denominator from leading to too large value of <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf15" overflow="scroll"><mml:mrow><mml:mi>P</mml:mi><mml:mi>C</mml:mi><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:math></inline-formula>.</p></sec><sec id="s4-3-3"><title>Bi-channel registration procedure</title><p>Image registration is fundamentally an iterative process optimized by a pre-designed cost function, which reasonably assesses the similarity between experimental and template datasets. Our bi-channel registration procedure is implemented based on the Elastix open-source program (Version 4.9.0) (<xref rid="bib39" ref-type="bibr">Shamonin et al., 2013</xref>; <xref rid="bib20" ref-type="bibr">Klein et al., 2010</xref>). Unlike conventional single-channel image registration, our method simultaneously registers all groups of input data using a single cost function defined as:<disp-formula id="equ3"><label>(3)</label><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="m3" overflow="scroll"><mml:mrow><mml:mi>c</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>;</mml:mo><mml:msub><mml:mi>I</mml:mi><mml:mi>F</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>I</mml:mi><mml:mi>M</mml:mi></mml:msub><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo movablelimits="false">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>N</mml:mi></mml:msubsup><mml:mrow><mml:msub><mml:mi>&#x003c9;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mstyle></mml:mrow></mml:mfrac><mml:mstyle displaystyle="true"><mml:munderover><mml:mo movablelimits="false">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>N</mml:mi></mml:munderover><mml:mrow><mml:msub><mml:mi>&#x003c9;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mi>c</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>;</mml:mo><mml:msubsup><mml:mi>I</mml:mi><mml:mi>F</mml:mi><mml:mi>i</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>I</mml:mi><mml:mi>M</mml:mi><mml:mi>i</mml:mi></mml:msubsup><mml:mo>)</mml:mo></mml:mrow></mml:mstyle></mml:mrow></mml:math></disp-formula><disp-formula id="equ4"><label>(4)</label><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="m4" overflow="scroll"><mml:mrow><mml:mi>c</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>;</mml:mo><mml:msubsup><mml:mi>I</mml:mi><mml:mi>F</mml:mi><mml:mi>i</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>I</mml:mi><mml:mi>M</mml:mi><mml:mi>i</mml:mi></mml:msubsup><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mstyle displaystyle="true"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>L</mml:mi><mml:mi>M</mml:mi></mml:msub></mml:mrow></mml:munder><mml:mrow><mml:mstyle displaystyle="true"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>f</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>L</mml:mi><mml:mi>F</mml:mi></mml:msub></mml:mrow></mml:munder><mml:mrow><mml:mi>p</mml:mi><mml:mo>(</mml:mo><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mo>;</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>)</mml:mo><mml:msub><mml:mrow><mml:mi>log</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msub><mml:mo>(</mml:mo><mml:msub><mml:mi>p</mml:mi><mml:mi>F</mml:mi></mml:msub><mml:mo>(</mml:mo><mml:mi>f</mml:mi><mml:mo>)</mml:mo><mml:msub><mml:mi>p</mml:mi><mml:mi>M</mml:mi></mml:msub><mml:mo>(</mml:mo><mml:mi>m</mml:mi><mml:mo>;</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>)</mml:mo><mml:mo>)</mml:mo></mml:mrow></mml:mstyle></mml:mrow></mml:mstyle></mml:mrow><mml:mrow><mml:mstyle displaystyle="true"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>L</mml:mi><mml:mi>M</mml:mi></mml:msub></mml:mrow></mml:munder><mml:mrow><mml:mstyle displaystyle="true"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>f</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>L</mml:mi><mml:mi>F</mml:mi></mml:msub></mml:mrow></mml:munder><mml:mrow><mml:mi>p</mml:mi><mml:mo>(</mml:mo><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mo>;</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>)</mml:mo><mml:msub><mml:mrow><mml:mi>log</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msub><mml:mi>p</mml:mi><mml:mo>(</mml:mo><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mo>;</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mstyle></mml:mrow></mml:mstyle></mml:mrow></mml:mfrac></mml:mrow></mml:math></disp-formula>where <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf16" overflow="scroll"><mml:mi>N</mml:mi></mml:math></inline-formula> represents the number of data groups and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf17" overflow="scroll"><mml:mrow><mml:msub><mml:mi>&#x003c9;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the weighting parameter for each data group. Since we used primary channel containing raw image stack in conjunction with assistant channel containing geometry and texture feature maps for registration simultaneously, here <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf18" overflow="scroll"><mml:mrow><mml:mi>N</mml:mi><mml:mtext>=</mml:mtext><mml:mn>3</mml:mn></mml:mrow></mml:math></inline-formula>. <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf19" overflow="scroll"><mml:mrow><mml:mi>c</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>;</mml:mo><mml:msubsup><mml:mi>I</mml:mi><mml:mi>F</mml:mi><mml:mi>i</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>I</mml:mi><mml:mi>M</mml:mi><mml:mi>i</mml:mi></mml:msubsup><mml:mo>)</mml:mo></mml:mrow></mml:math></inline-formula> is the cost function of each channel, where <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf20" overflow="scroll"><mml:mrow><mml:msubsup><mml:mi>I</mml:mi><mml:mi>F</mml:mi><mml:mi>i</mml:mi></mml:msubsup></mml:mrow></mml:math></inline-formula> represents the fixed image (experimental data) and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf21" overflow="scroll"><mml:mrow><mml:msubsup><mml:mi>I</mml:mi><mml:mi>M</mml:mi><mml:mi>i</mml:mi></mml:msubsup></mml:mrow></mml:math></inline-formula> represents the moving image (template). <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf22" overflow="scroll"><mml:mrow><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> denotes the deformation function of the registration model, with parameter &#x003bc; being optimized during the iterative registration process. In <xref rid="equ4" ref-type="disp-formula">Equation 4</xref>, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf23" overflow="scroll"><mml:mrow><mml:msub><mml:mi>L</mml:mi><mml:mi>F</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf24" overflow="scroll"><mml:mrow><mml:msub><mml:mi>L</mml:mi><mml:mi>M</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> are two sets of regularly spaced intensity bin centers, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf25" overflow="scroll"><mml:mi>p</mml:mi></mml:math></inline-formula> is the discrete joint probability, and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf26" overflow="scroll"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mi>F</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf27" overflow="scroll"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mi>M</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> are the marginal discrete probabilities of the fixed and moving image. Here we used rigid+affine+B-spline three-level model for the registration, with rigid and affine transformations mainly for aligning the overall orientation differences between the datasets, and B-spline model mainly for aligning the local geometry differences. B-spline places a regular grid of control points onto the images. These control points are movable during the registration and cause the surrounding image data to be transformed, thereby permitting the local, non-linear alignment of the image data. A stepsize of 30 pixels was set for the movement of control points in 3D space, and a five-level coarse-to-fine pyramidal registration was applied for achieving faster convergence. During the iterative optimization of <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf28" overflow="scroll"><mml:mrow><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, we used gradient descent method to efficiently approach the optimal registration of the template images to the fixed experimental images. The solution of &#x003bc; can be expressed as<disp-formula id="equ5"><label>(5)</label><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="m5" overflow="scroll"><mml:mrow><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mi>l</mml:mi><mml:mo>*</mml:mo><mml:mfrac><mml:mrow><mml:mo>&#x02202;</mml:mo><mml:mi>c</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:msub><mml:mo>;</mml:mo><mml:msub><mml:mi>I</mml:mi><mml:mi>F</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>I</mml:mi><mml:mi>M</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02202;</mml:mo><mml:mi>&#x003bc;</mml:mi></mml:mrow></mml:mfrac></mml:mrow></mml:math></disp-formula>where <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf29" overflow="scroll"><mml:mi>l</mml:mi></mml:math></inline-formula> is the learning rate, which also means the stepsize of the gradient descent optimization. The transformation parameters obtained from the multi-channel registration were finally applied to the annotation files to generate the atlas /annotation for the whole-brain data.</p></sec></sec><sec id="s4-4"><title>Visualization and quantification of brain-map results</title><sec id="s4-4-1"><title>Generation of 3D digital map for whole brain</title><p>We obtained a low-resolution annotation (20 &#x003bc;m isotropic resolution) for entire mouse brain after registration. In order to generate a 3D digital framework based on the high-resolution raw image, <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf30" overflow="scroll"><mml:mrow><mml:mi>b</mml:mi><mml:mo>,</mml:mo><mml:mi>l</mml:mi></mml:mrow></mml:math></inline-formula> recorded in the down-sampling step were used for resolution restoration. Annotation information is to distinguish different brain regions by pixel intensity. In order to generate a digital frame for quantitative analysis, we introduced Marching cubes algorithm (<xref rid="bib27" ref-type="bibr">Lorensen and Cline, 1987</xref>) to generate 3D surface graphics, which is also, to generate the 3D digital maps. Then, through the programmable API link with Imaris (RRID:<ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://identifiers.org/RRID/RRID:SCR_007370" ext-link-type="uri">SCR_007370</ext-link>), we could readily visualize the 3D digital map and perform various quantification in Imaris. After registration and interpolation applied to the experimental data, a 3D digital map was visualized in Imaris (9.0.0) invoked by our program. Then neuron tracing and cell counting tasks could be performed in Imaris at native resolution (e.g., 1 &#x000d7; 1 &#x000d7; 10 &#x003bc;m<sup>3</sup> for STPT data). During the neural tracing process, the brain regions where the selected neurons passed through could be three-dimensionally displayed under arbitrary view. Furthermore, the cell counting could be performed in parallel by simultaneously setting a number of kernel diameters and intensity thresholds for different segmented brain regions.</p></sec><sec id="s4-4-2"><title>Distance calculation</title><p>The Euclidean distance between one pair of landmarks shown in different image datasets (<xref rid="fig2" ref-type="fig">Figure 2c</xref>) indicates the registration error and can be calculated as:<disp-formula id="equ6"><label>(6)</label><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="m6" overflow="scroll"><mml:mrow><mml:mi>&#x003c1;</mml:mi><mml:mo>=</mml:mo><mml:msqrt><mml:mrow><mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>z</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>z</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:msqrt></mml:mrow></mml:math></disp-formula></p></sec><sec id="s4-4-3"><title>Calculation of dice scores</title><p>Dice score is the indicator for quantifying the accuracy of segmentation and can be calculated as:<disp-formula id="equ7"><label>(7)</label><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="m7" overflow="scroll"><mml:mrow><mml:mi>D</mml:mi><mml:mi>i</mml:mi><mml:mi>c</mml:mi><mml:mi>e</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mn>2</mml:mn><mml:mo>(</mml:mo><mml:mi>A</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi>B</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mo>+</mml:mo><mml:mi>B</mml:mi></mml:mrow></mml:mfrac></mml:mrow></mml:math></disp-formula>where <italic toggle="yes">A</italic> is the ground truth of segmentation, while <italic toggle="yes">B</italic> is the result by brain-map. <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf31" overflow="scroll"><mml:mrow><mml:mi>A</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi>B</mml:mi></mml:mrow></mml:math></inline-formula> represents the number of pixels where <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf32" overflow="scroll"><mml:mi>A</mml:mi></mml:math></inline-formula> and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf33" overflow="scroll"><mml:mi>B</mml:mi></mml:math></inline-formula> overlap, and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf34" overflow="scroll"><mml:mrow><mml:mi>A</mml:mi><mml:mo>+</mml:mo><mml:mi>B</mml:mi></mml:mrow></mml:math></inline-formula> refers to the total number of pixels in <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf35" overflow="scroll"><mml:mi>A</mml:mi></mml:math></inline-formula> and <inline-formula><mml:math xmlns:mml="http://www.w3.org/1998/Math/MathML" id="inf36" overflow="scroll"><mml:mi>B</mml:mi></mml:math></inline-formula>.</p><p>Here, with referring to BrainsMapi (<xref rid="bib30" ref-type="bibr">Ni et al., 2020</xref>) methods, we compared the registration/segmentation results by four registration tools at both coarse region level and fine nuclei level. As we assessed the accuracy of these methods at brain-region level, 10 brain regions, Outline, CB, CP, HB (hindbrain), HIP, HY (hypothalamus), Isocortex, MB (midbrain), OLF, and TH (thalamus), were first selected from the entire brain for comparison. Then we further picked out 50 planes in each selected brain region (totally 500 planes for 10 regions) and manually segmented them to generate the reference results (ground truth). For nuclei-level comparison, we selected nine small sub-regions, ACA, ENT, MV, PAG, RT, SSp, SUB, VISp, and VMH as targets, and performed similar operation on them with selecting five representative coronal sections for each region. To allow the manual segmentation as objective as possible, two skillful persons independently repeated the abovementioned process for five times, and a STAPLE algorithm (<xref rid="bib46" ref-type="bibr">Warfield et al., 2004</xref>) was used to fuse the 10 manual segmentation results to obtain the final averaged output as the ground-truth segmentation, for each region.</p></sec></sec><sec id="s4-5"><title>Deep neural network segmentation</title><sec id="s4-5-1"><title>Generation of ground-truth training data</title><p>We chose 18 primary regions (levels 4 and 5): Isocortex, HPF, OLF, CTXsp, STR, PAL, CB, DORpm, DORsm, HY, MBsen, MBmot, Mbsta, P-sen, P-mot, P-sat, MY-sen, and MY-mot, in whole brain for the DNN training and performance validation. The ground-truth annotation masks for these regions were readily obtained from our bi-channel registration procedure of BIRDS. For high-generosity DNN segmentation of whole brain and incomplete brain, we specifically prepared two groups of data training and validation as following: (1) Nine whole mouse brains containing 5000 annotated sagittal slices (660 &#x000d7; 400 for each slice) were first used as the training dataset. Then, the sagittal sections of whole brains were cropped, to generate different types of incomplete brains, as shown in <xref rid="fig5" ref-type="fig">Figure 5b</xref>. The training dataset were thus comprised both complete and incomplete sagittal sections (5000 and 4500 slices, respectively). The DNN trained by such datasets was able to infer segmentations for given complete or incomplete sagittal planes. <xref rid="fig5s3" ref-type="fig">Figure 5&#x02014;figure supplement 3</xref> compared the DNN segmentation results of both whole brain (a) and incomplete brains (b, c, d) with the corresponding ground truths. (2) To demonstrate the performance of the DNN in segmenting sub-regions at finer scale, we chose the ground-truth images from coronal sections of specific hippocampus region (1100 slices from eight mouse brains, 570 &#x000d7; 400 for each slice) for DNN training. The corresponding ground-truth masks for the four major sub-regions of hippocampus, CA1, CA2, CA3, and the DG, were then generated by registration. We validated the DNN&#x02019; performance on segmenting these small sub-regions through comparison with the ground-truths at four different coronal planes.</p><p>During network training, we defined the segmentation classes with the same number of regions, which is 19 in <xref rid="fig5" ref-type="fig">Figure 5</xref>, <xref rid="fig5s3" ref-type="fig">Figure 5&#x02014;figure supplement 3</xref> for large incomplete (background as one class) and 5 in <xref rid="fig5s2" ref-type="fig">Figure 5&#x02014;figure supplement 2</xref> (background as one class). Finally, each region was assigned a channel for generating the segmented output. The details of training and test datasets are shown in <xref rid="supp2" ref-type="supplementary-material">Supplementary file 2</xref>.</p></sec><sec id="s4-5-2"><title>Modified deeplab V3+ network</title><p>Our DNN is based on the modification of Deeplab V3+, which contains a classical encoder&#x02013;decoder structure (<xref rid="bib6" ref-type="bibr">Chen et al., 2020</xref>). The main framework is based on Xception, which is optimized for depthwise separable convolutions and thereby reduces the computational complexity while maintains high performance. The network gradually reduces the spatial dimension of the feature map at the encoder stage and allows complicated information to be easily outputted at deep level. At the final stage of the encoder structure, we introduce a dilated convolution Atrous Spatial Pyramid Pooling (ASPP), which increased the receptive field of convolution by changing the stepsize of atrous. The number of cores selected in ASPP is 6, 12, and 18. To solve the issue of aliased edges in inference results of conventional Deeplab V3+, we introduced more original image information into the decoder by serially convolving the 2&#x000d7; and 4&#x000d7; down-sampling results generated at the encoder stage and concatenating them to the decoder. The network was trained for ~30,000 iterations with a learning rate of 0.001, learning momentum of 0.9 and output stride of 8. The training was implemented using two NVIDIA GeForce GTX 1080Ti graphics cards and took approximately 40 hr.</p></sec><sec id="s4-5-3"><title>Neuron tracing</title><p>The neurons in whole brain data were segmented semi-automatically using the Imaris software. With registering our brain to ABA, we obtained the anatomical annotation for all the segmented areas. The Autopath Mode of the Filament module was applied to trace long-distance neurons. We first assigned one point on a long-distance neuron to initiate the tracing. Then, Imaris automatically calculated the pathway in accordance with image data, reconstructed the 3D morphology, and linked it with the previous part. This procedure would repeat several times until the whole neuron, which could also be recognized by human&#x02019;s eye, was segmented.</p></sec><sec id="s4-5-4"><title>Cell counting</title><p>The Spots module and Surface module of Imaris software were used to count retrogradely labeled striatum-projecting cells in SS and VIS areas. We first separated the brain regions into multiple channels in Surface module. Then automatic creation in Spots module was applied to count cells number for each channel. To achieve accurate counting, the essential parts were the appropriate estimate of cell bodies&#x02019; diameter and filtration of the chosen cells by tuning the quality parameters. The accuracy of this automatic counting procedure was compared with manual counting, which herein severed as ground truth. After obtaining the total number of cells in each brain region, according to the sub-region ranges divided by the Surface module, Imaris could also calculate the volume of each brain region. Then, with knowing the number of cell nuclei and the volume of each segmented brain region, the density of the cell nuclei inside each brain region could be calculated.</p></sec></sec><sec id="s4-6"><title>Computing resources</title><p>We run BIRDS on a Windows 10 workstation equipped with dual Xeon E5-2630 V3 CPUs, 1 TB RAM, and two GeForce GTX 1080 Ti graphic cards. In <xref rid="supp1" ref-type="supplementary-material">Supplementary file 1</xref>, we showed the memory and time consumptions of our BIRDS plugin for processing 180 GB and 320 GB brain datasets. The image preprocessing time at stage 1 is approximately proportional to the size of data. In contrast, since the datasets for registration are down-sampled to the same size to match the Allen CCF template, the time and memory consumption at stages 2 and 3 are nearly the same for two datasets. The time and memory consumption for generating the 3D digital framework at stage 4 is proportional to the data size. It should be noted that memory with capacity at least 1.5 times of the data size is required at this step. Therefore, when applying BIRDS to larger datasets, such as rat brain, we will recommend a powerful workstation with at least one XEON CPU, 1 TB memory, and one state-of-the-art graphic card (Geforce RTX 3090), to guarantee a smooth running of whole pipeline.</p></sec><sec id="s4-7"><title>Code availability</title><p>We have made our pipeline open access for the community. We have provided source code of the BIRDS for Windows 10 at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://github.com/bleach1by1/birds_reg" ext-link-type="uri">https://github.com/bleach1by1/birds_reg</ext-link> (<xref rid="bib44" ref-type="bibr">Wang, 2021a</xref>; copy archived at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:dir:1eb68910dba957ab6819ec8d5f364fb0ccd7d249;origin=https://github.com/bleach1by1/birds_reg;visit=swh:1:snp:fc666e617787c219905a3cdd65e7c1e94cc05e54;anchor=swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836/" ext-link-type="uri">swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836</ext-link>). FIJI plugin and ancillary installation packages are provided at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://github.com/bleach1by1/BIRDS_plugin" ext-link-type="uri">https://github.com/bleach1by1/BIRDS_plugin</ext-link> (<xref rid="bib45" ref-type="bibr">Wang, 2021b</xref>; copy archived at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:dir:a7a4ec239977432884d064a17d721578b22e5b54;origin=https://github.com/bleach1by1/BIRDS_plugin;visit=swh:1:snp:54f28d1de7607346ea62ea5de2132e79396d0e9e;anchor=swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475/" ext-link-type="uri">swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475</ext-link>). BIRDS contains five core modules, image preprocessing, bi-channel registration, manual correction, link with Imaris software, and deep-learning segmentation, all of which can be executed on a GUI. We also provided a step-by-step tutorial and test data to facilitate the program implementation by other researchers.</p></sec></sec></body><back><sec sec-type="funding-information"><title>Funding Information</title><p>This paper was supported by the following grants:</p><list list-type="bullet"><list-item><p><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001809</institution-id><institution>National Natural Science Foundation of China</institution></institution-wrap>
</funding-source>
<award-id>21874052</award-id> to Peng Fei.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001809</institution-id><institution>National Natural Science Foundation of China</institution></institution-wrap>
</funding-source>
<award-id>31871089</award-id> to Yunyun Han.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution>Innovation Fund of WNLO</institution></institution-wrap>
</funding-source> to Peng Fei.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution>Junior Thousand Talents Program of China</institution></institution-wrap>
</funding-source> to Yunyun Han, Peng Fei.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution>The FRFCU</institution></institution-wrap>
</funding-source>
<award-id>HUST:2172019kfyXKJC077</award-id> to Yunyun Han.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution>National Key R&#x00026;D program of China</institution></institution-wrap>
</funding-source>
<award-id>2017YFA0700501</award-id> to Peng Fei.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100012166</institution-id><institution>973 Program</institution></institution-wrap>
</funding-source>
<award-id>2015CB755603</award-id> to Shaoqun Zeng, Yongsheng Zhang.</p></list-item><list-item><p><funding-source>
<institution-wrap><institution>Director Fund of WNLO</institution></institution-wrap>
</funding-source> to Yongsheng Zhang, Shaoqun Zeng.</p></list-item></list></sec><ack id="ack"><title>Acknowledgements</title><p>We thank Haohong Li, Luoying Zhang, Man Jiang, Bo Xiong for discussions and comments on the work&#x000a0;and Hao Zhang for the help on the code implementation. This work was supported by the National Key R and D program of China (2017YFA0700501 PF), the National Natural Science Foundation of China (21874052 for PF,&#x000a0;31871089 for YH), the Innovation Fund of WNLO (PF) and the Junior Thousand Talents Program of China (PF and YH), the FRFCU (HUST:2172019kfyXKJC077 YH).</p></ack><sec sec-type="additional-information" id="s5"><title>Additional information</title><fn-group content-type="competing-interest"><title>Competing interests</title><fn fn-type="COI-statement" id="conf1"><p>No competing interests declared.</p></fn></fn-group><fn-group content-type="author-contribution"><title>Author contributions</title><fn fn-type="con" id="con1"><p>Data curation, Software, Validation, Visualization, Methodology, Writing - original draft.</p></fn><fn fn-type="con" id="con2"><p>Methodology, Software, Validation.</p></fn><fn fn-type="con" id="con3"><p>Resources, Visualization.</p></fn><fn fn-type="con" id="con4"><p>Methodology, Software.</p></fn><fn fn-type="con" id="con5"><p>Resources.</p></fn><fn fn-type="con" id="con6"><p>Methodology.</p></fn><fn fn-type="con" id="con7"><p>Resources, Visualization, Writing - review and editing.</p></fn><fn fn-type="con" id="con8"><p>Software, Funding acquisition, Visualization, Writing - review and editing.</p></fn></fn-group></sec><sec sec-type="supplementary-material" id="s6"><title>Additional files</title><supplementary-material id="supp1" position="float" content-type="local-data"><label>Supplementary file 1.</label><caption><title>Data size, memory cost and time consumption at different BIRDS stages for processing 180 GB STPT and 320 GB LSFM datasets.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-supp1.xlsx" id="d64e2581" position="anchor"/></supplementary-material><supplementary-material id="supp2" position="float" content-type="local-data"><label>Supplementary file 2.</label><caption><title>Details of training dataset and test dataset for coarse and fine DNN segmentations.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-supp2.xlsx" id="d64e2586" position="anchor"/></supplementary-material><supplementary-material id="supp3" position="float" content-type="local-data"><label>Supplementary file 3.</label><caption><title>The median values corresponding to <xref rid="fig2" ref-type="fig">Figure 2d&#x02013;f</xref>.</title></caption><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-supp3.xlsx" id="d64e2595" position="anchor"/></supplementary-material><supplementary-material id="transrepform" position="float" content-type="local-data"><label>Transparent reporting form</label><media xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="elife-63455-transrepform.docx" id="d64e2598" position="anchor"/></supplementary-material></sec><sec sec-type="data-availability" id="s7"><title>Data availability</title><p>The Allen CCF is open access and available with related tools at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://atlas.brain-map.org/" ext-link-type="uri">https://atlas.brain-map.org/</ext-link>. The datasets (Brain1~5) have been deposited in Dryad at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://datadryad.org/stash/share/4fesXcJif0L2DnSj7YmjREe37yPm1bEnUiK49ELtALg" ext-link-type="uri">https://datadryad.org/stash/share/4fesXcJif0L2DnSj7YmjREe37yPm1bEnUiK49ELtALg</ext-link> and <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://datadryad.org/stash/share/PWwOzHmOqVBa_CBplDW133X5AEGwFsuoZZ4BNW_nAsQ" ext-link-type="uri">https://datadryad.org/stash/share/PWwOzHmOqVBa_CBplDW133X5AEGwFsuoZZ4BNW_nAsQ</ext-link>. The code and plugin can be found at the following link: <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://github.com/bleach1by1/BIRDS_plugin" ext-link-type="uri">https://github.com/bleach1by1/BIRDS_plugin</ext-link> (copy archived at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475/" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475/</ext-link>), <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://github.com/bleach1by1/birds_reg" ext-link-type="uri">https://github.com/bleach1by1/birds_reg</ext-link> (copy archived at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836/" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836/</ext-link>), <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://github.com/bleach1by1/birds_dl.git" ext-link-type="uri">https://github.com/bleach1by1/birds_dl.git</ext-link> (copy archived at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:rev:92d3a68c7805cbef58c834e39c807e8cbaa902e6/" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:rev:92d3a68c7805cbef58c834e39c807e8cbaa902e6/</ext-link>), <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://github.com/bleach1by1/BIRDS_demo" ext-link-type="uri">https://github.com/bleach1by1/BIRDS_demo</ext-link> (copy archived at <ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:rev:61ad20ab070b7af9881d69df643fa4b878993f90/" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:rev:61ad20ab070b7af9881d69df643fa4b878993f90/</ext-link>). All data generated or analysed during this study are included in the manuscript. Source data files have been provided for Figures 1, 2, 3, 4, 5 and Figure 2&#x02014;figure supplements 3,4; Figure 5&#x02014;figure supplements 2,3.</p><p>The following previously published datasets were used:</p><p>
<element-citation publication-type="data" id="dataset4"><person-group person-group-type="author">
<name><surname>Wang</surname><given-names>X</given-names></name>
</person-group><year iso-8601-date="2021">2021</year><data-title>brain3_4_5</data-title><source>Dryad Digital Repository</source><pub-id pub-id-type="doi">10.5061/dryad.qnk98sffp</pub-id></element-citation>
</p><p>
<element-citation publication-type="data" id="dataset5"><person-group person-group-type="author">
<name><surname>Wang</surname><given-names>X</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><data-title>Brain1_2 Dataset</data-title><source>Dryad Digital Repository</source><pub-id pub-id-type="doi">10.5061/dryad.37pvmcvj9</pub-id></element-citation>
</p></sec><ref-list><title>References</title><ref id="bib1"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Akkus</surname><given-names>Z</given-names></name>
<name><surname>Galimzianova</surname><given-names>A</given-names></name>
<name><surname>Hoogi</surname><given-names>A</given-names></name>
<name><surname>Rubin</surname><given-names>DL</given-names></name>
<name><surname>Erickson</surname><given-names>BJ</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Deep learning for brain MRI segmentation: state of the art and future directions</article-title><source>Journal of Digital Imaging</source><volume>30</volume><fpage>449</fpage><lpage>459</lpage><pub-id pub-id-type="doi">10.1007/s10278-017-9983-4</pub-id><?supplied-pmid 28577131?><pub-id pub-id-type="pmid">28577131</pub-id></element-citation></ref><ref id="bib2"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Alivisatos</surname><given-names>AP</given-names></name>
<name><surname>Chun</surname><given-names>M</given-names></name>
<name><surname>Church</surname><given-names>GM</given-names></name>
<name><surname>Greenspan</surname><given-names>RJ</given-names></name>
<name><surname>Roukes</surname><given-names>ML</given-names></name>
<name><surname>Yuste</surname><given-names>R</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>The brain activity map project and the challenge of functional connectomics</article-title><source>Neuron</source><volume>74</volume><fpage>970</fpage><lpage>974</lpage><pub-id pub-id-type="doi">10.1016/j.neuron.2012.06.006</pub-id><?supplied-pmid 22726828?><pub-id pub-id-type="pmid">22726828</pub-id></element-citation></ref><ref id="bib3"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bohland</surname><given-names>JW</given-names></name>
<name><surname>Wu</surname><given-names>C</given-names></name>
<name><surname>Barbas</surname><given-names>H</given-names></name>
<name><surname>Bokil</surname><given-names>H</given-names></name>
<name><surname>Bota</surname><given-names>M</given-names></name>
<name><surname>Breiter</surname><given-names>HC</given-names></name>
<name><surname>Cline</surname><given-names>HT</given-names></name>
<name><surname>Doyle</surname><given-names>JC</given-names></name>
<name><surname>Freed</surname><given-names>PJ</given-names></name>
<name><surname>Greenspan</surname><given-names>RJ</given-names></name>
<name><surname>Haber</surname><given-names>SN</given-names></name>
<name><surname>Hawrylycz</surname><given-names>M</given-names></name>
<name><surname>Herrera</surname><given-names>DG</given-names></name>
<name><surname>Hilgetag</surname><given-names>CC</given-names></name>
<name><surname>Huang</surname><given-names>ZJ</given-names></name>
<name><surname>Jones</surname><given-names>A</given-names></name>
<name><surname>Jones</surname><given-names>EG</given-names></name>
<name><surname>Karten</surname><given-names>HJ</given-names></name>
<name><surname>Kleinfeld</surname><given-names>D</given-names></name>
<name><surname>K&#x000f6;tter</surname><given-names>R</given-names></name>
<name><surname>Lester</surname><given-names>HA</given-names></name>
<name><surname>Lin</surname><given-names>JM</given-names></name>
<name><surname>Mensh</surname><given-names>BD</given-names></name>
<name><surname>Mikula</surname><given-names>S</given-names></name>
<name><surname>Panksepp</surname><given-names>J</given-names></name>
<name><surname>Price</surname><given-names>JL</given-names></name>
<name><surname>Safdieh</surname><given-names>J</given-names></name>
<name><surname>Saper</surname><given-names>CB</given-names></name>
<name><surname>Schiff</surname><given-names>ND</given-names></name>
<name><surname>Schmahmann</surname><given-names>JD</given-names></name>
<name><surname>Stillman</surname><given-names>BW</given-names></name>
<name><surname>Svoboda</surname><given-names>K</given-names></name>
<name><surname>Swanson</surname><given-names>LW</given-names></name>
<name><surname>Toga</surname><given-names>AW</given-names></name>
<name><surname>Van Essen</surname><given-names>DC</given-names></name>
<name><surname>Watson</surname><given-names>JD</given-names></name>
<name><surname>Mitra</surname><given-names>PP</given-names></name>
</person-group><year iso-8601-date="2009">2009</year><article-title>A proposal for a coordinated effort for the determination of brainwide neuroanatomical connectivity in model organisms at a mesoscopic scale</article-title><source>PLOS Computational Biology</source><volume>5</volume><elocation-id>e1000334</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1000334</pub-id><?supplied-pmid 19325892?><pub-id pub-id-type="pmid">19325892</pub-id></element-citation></ref><ref id="bib4"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Chen</surname><given-names>LC</given-names></name>
<name><surname>Papandreou</surname><given-names>G</given-names></name>
<name><surname>Kokkinos</surname><given-names>I</given-names></name>
<name><surname>Murphy</surname><given-names>K</given-names></name>
<name><surname>Yuille</surname><given-names>AL</given-names></name>
</person-group><year iso-8601-date="2018">2018a</year><article-title>DeepLab: semantic image segmentation with deep convolutional nets, Atrous convolution, and fully connected CRFs</article-title><source>IEEE Transactions on Pattern Analysis and Machine Intelligence</source><volume>40</volume><fpage>834</fpage><lpage>848</lpage><pub-id pub-id-type="doi">10.1109/TPAMI.2017.2699184</pub-id><?supplied-pmid 28463186?><pub-id pub-id-type="pmid">28463186</pub-id></element-citation></ref><ref id="bib5"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Chen</surname><given-names>H</given-names></name>
<name><surname>Dou</surname><given-names>Q</given-names></name>
<name><surname>Yu</surname><given-names>L</given-names></name>
<name><surname>Qin</surname><given-names>J</given-names></name>
<name><surname>Heng</surname><given-names>PA</given-names></name>
</person-group><year iso-8601-date="2018">2018b</year><article-title>VoxResNet: deep voxelwise residual networks for brain segmentation from 3D MR images</article-title><source>NeuroImage</source><volume>170</volume><fpage>446</fpage><lpage>455</lpage><pub-id pub-id-type="doi">10.1016/j.neuroimage.2017.04.041</pub-id><?supplied-pmid 28445774?><pub-id pub-id-type="pmid">28445774</pub-id></element-citation></ref><ref id="bib6"><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Chen</surname><given-names>L-C</given-names></name>
<name><surname>Zhu</surname><given-names>Y</given-names></name>
<name><surname>Papandreou</surname><given-names>G</given-names></name>
<name><surname>Schroff</surname><given-names>F</given-names></name>
<name><surname>Adam</surname><given-names>H</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>Encoder-decoder with atrous separable convolution for semantic image segmentation</article-title><conf-name>Proceedings of the European Conference on Computer Vision (ECCV)</conf-name><fpage>801</fpage><lpage>818</lpage></element-citation></ref><ref id="bib7"><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>de Brebisson</surname><given-names>A</given-names></name>
<name><surname>Montana</surname><given-names>G</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Deep neural networks for anatomical brain segmentation</article-title><conf-name>IEEE Conference on Computer Vision and Pattern Recognition Workshops (CVPRW)</conf-name><fpage>20</fpage><lpage>28</lpage><pub-id pub-id-type="doi">10.1109/CVPRW.2015.7301312</pub-id></element-citation></ref><ref id="bib8"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Dice</surname><given-names>LR</given-names></name>
</person-group><year iso-8601-date="1945">1945</year><article-title>Measures of the amount of ecologic association between species</article-title><source>Ecology</source><volume>26</volume><fpage>297</fpage><lpage>302</lpage><pub-id pub-id-type="doi">10.2307/1932409</pub-id></element-citation></ref><ref id="bib9"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Economo</surname><given-names>MN</given-names></name>
<name><surname>Clack</surname><given-names>NG</given-names></name>
<name><surname>Lavis</surname><given-names>LD</given-names></name>
<name><surname>Gerfen</surname><given-names>CR</given-names></name>
<name><surname>Svoboda</surname><given-names>K</given-names></name>
<name><surname>Myers</surname><given-names>EW</given-names></name>
<name><surname>Chandrashekar</surname><given-names>J</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>A platform for brain-wide imaging and reconstruction of individual neurons</article-title><source>eLife</source><volume>5</volume><elocation-id>e10566</elocation-id><pub-id pub-id-type="doi">10.7554/eLife.10566</pub-id><pub-id pub-id-type="pmid">26796534</pub-id></element-citation></ref><ref id="bib10"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ferrante</surname><given-names>E</given-names></name>
<name><surname>Paragios</surname><given-names>N</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Slice-to-volume medical image registration: a survey</article-title><source>Medical Image Analysis</source><volume>39</volume><fpage>101</fpage><lpage>123</lpage><pub-id pub-id-type="doi">10.1016/j.media.2017.04.010</pub-id><?supplied-pmid 28482198?><pub-id pub-id-type="pmid">28482198</pub-id></element-citation></ref><ref id="bib11"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>F&#x000fc;rth</surname><given-names>D</given-names></name>
<name><surname>Vaissi&#x000e8;re</surname><given-names>T</given-names></name>
<name><surname>Tzortzi</surname><given-names>O</given-names></name>
<name><surname>Xuan</surname><given-names>Y</given-names></name>
<name><surname>M&#x000e4;rtin</surname><given-names>A</given-names></name>
<name><surname>Lazaridis</surname><given-names>I</given-names></name>
<name><surname>Spigolon</surname><given-names>G</given-names></name>
<name><surname>Fisone</surname><given-names>G</given-names></name>
<name><surname>Tomer</surname><given-names>R</given-names></name>
<name><surname>Deisseroth</surname><given-names>K</given-names></name>
<name><surname>Carl&#x000e9;n</surname><given-names>M</given-names></name>
<name><surname>Miller</surname><given-names>CA</given-names></name>
<name><surname>Rumbaugh</surname><given-names>G</given-names></name>
<name><surname>Meletis</surname><given-names>K</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>An interactive framework for whole-brain maps at cellular resolution</article-title><source>Nature Neuroscience</source><volume>21</volume><fpage>139</fpage><lpage>149</lpage><pub-id pub-id-type="doi">10.1038/s41593-017-0027-7</pub-id><pub-id pub-id-type="pmid">29203898</pub-id></element-citation></ref><ref id="bib12"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Gong</surname><given-names>H</given-names></name>
<name><surname>Zeng</surname><given-names>S</given-names></name>
<name><surname>Yan</surname><given-names>C</given-names></name>
<name><surname>Lv</surname><given-names>X</given-names></name>
<name><surname>Yang</surname><given-names>Z</given-names></name>
<name><surname>Xu</surname><given-names>T</given-names></name>
<name><surname>Feng</surname><given-names>Z</given-names></name>
<name><surname>Ding</surname><given-names>W</given-names></name>
<name><surname>Qi</surname><given-names>X</given-names></name>
<name><surname>Li</surname><given-names>A</given-names></name>
<name><surname>Wu</surname><given-names>J</given-names></name>
<name><surname>Luo</surname><given-names>Q</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Continuously tracing brain-wide long-distance axonal projections in mice at a one-micron voxel resolution</article-title><source>NeuroImage</source><volume>74</volume><fpage>87</fpage><lpage>98</lpage><pub-id pub-id-type="doi">10.1016/j.neuroimage.2013.02.005</pub-id><?supplied-pmid 23416252?><pub-id pub-id-type="pmid">23416252</pub-id></element-citation></ref><ref id="bib13"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Goubran</surname><given-names>M</given-names></name>
<name><surname>Leuze</surname><given-names>C</given-names></name>
<name><surname>Hsueh</surname><given-names>B</given-names></name>
<name><surname>Aswendt</surname><given-names>M</given-names></name>
<name><surname>Ye</surname><given-names>L</given-names></name>
<name><surname>Tian</surname><given-names>Q</given-names></name>
<name><surname>Cheng</surname><given-names>MY</given-names></name>
<name><surname>Crow</surname><given-names>A</given-names></name>
<name><surname>Steinberg</surname><given-names>GK</given-names></name>
<name><surname>McNab</surname><given-names>JA</given-names></name>
<name><surname>Deisseroth</surname><given-names>K</given-names></name>
<name><surname>Zeineh</surname><given-names>M</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Multimodal image registration and connectivity analysis for integration of connectomic data from microscopy to MRI</article-title><source>Nature Communications</source><volume>10</volume><elocation-id>5504</elocation-id><pub-id pub-id-type="doi">10.1038/s41467-019-13374-0</pub-id><?supplied-pmid 31796741?><pub-id pub-id-type="pmid">31796741</pub-id></element-citation></ref><ref id="bib14"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Han</surname><given-names>Y</given-names></name>
<name><surname>Kebschull</surname><given-names>JM</given-names></name>
<name><surname>Campbell</surname><given-names>RAA</given-names></name>
<name><surname>Cowan</surname><given-names>D</given-names></name>
<name><surname>Imhof</surname><given-names>F</given-names></name>
<name><surname>Zador</surname><given-names>AM</given-names></name>
<name><surname>Mrsic-Flogel</surname><given-names>TD</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>The logic of single-cell projections from visual cortex</article-title><source>Nature</source><volume>556</volume><fpage>51</fpage><lpage>56</lpage><pub-id pub-id-type="doi">10.1038/nature26159</pub-id><?supplied-pmid 29590093?><pub-id pub-id-type="pmid">29590093</pub-id></element-citation></ref><ref id="bib15"><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>He</surname><given-names>K</given-names></name>
<name><surname>Gkioxari</surname><given-names>G</given-names></name>
<name><surname>Doll&#x000e1;r</surname><given-names>P</given-names></name>
<name><surname>Girshick</surname><given-names>R</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Mask R-CNN</article-title><conf-name>EEE International Conference on Computer Vision (ICCV)</conf-name><fpage>2961</fpage><lpage>2969</lpage><pub-id pub-id-type="doi">10.1109/ICCV.2017.322</pub-id></element-citation></ref><ref id="bib16"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Huang</surname><given-names>L-K</given-names></name>
<name><surname>Wang</surname><given-names>M-JJ</given-names></name>
</person-group><year iso-8601-date="1995">1995</year><article-title>Image thresholding by minimizing the measures of fuzziness</article-title><source>Pattern Recognition</source><volume>28</volume><fpage>41</fpage><lpage>51</lpage><pub-id pub-id-type="doi">10.1016/0031-3203(94)E0043-K</pub-id></element-citation></ref><ref id="bib17"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Iqbal</surname><given-names>A</given-names></name>
<name><surname>Khan</surname><given-names>R</given-names></name>
<name><surname>Karayannis</surname><given-names>T</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Developing a brain atlas through deep learning</article-title><source>Nature Machine Intelligence</source><volume>1</volume><fpage>277</fpage><lpage>287</lpage><pub-id pub-id-type="doi">10.1038/s42256-019-0058-8</pub-id></element-citation></ref><ref id="bib18"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kandel</surname><given-names>ER</given-names></name>
<name><surname>Markram</surname><given-names>H</given-names></name>
<name><surname>Matthews</surname><given-names>PM</given-names></name>
<name><surname>Yuste</surname><given-names>R</given-names></name>
<name><surname>Koch</surname><given-names>C</given-names></name>
</person-group><year iso-8601-date="2013">2013</year><article-title>Neuroscience thinks big (and collaboratively)</article-title><source>Nature Reviews Neuroscience</source><volume>14</volume><fpage>659</fpage><lpage>664</lpage><pub-id pub-id-type="doi">10.1038/nrn3578</pub-id><?supplied-pmid 23958663?><pub-id pub-id-type="pmid">23958663</pub-id></element-citation></ref><ref id="bib19"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kim</surname><given-names>Y</given-names></name>
<name><surname>Venkataraju</surname><given-names>KU</given-names></name>
<name><surname>Pradhan</surname><given-names>K</given-names></name>
<name><surname>Mende</surname><given-names>C</given-names></name>
<name><surname>Taranda</surname><given-names>J</given-names></name>
<name><surname>Turaga</surname><given-names>SC</given-names></name>
<name><surname>Arganda-Carreras</surname><given-names>I</given-names></name>
<name><surname>Ng</surname><given-names>L</given-names></name>
<name><surname>Hawrylycz</surname><given-names>MJ</given-names></name>
<name><surname>Rockland</surname><given-names>KS</given-names></name>
<name><surname>Seung</surname><given-names>HS</given-names></name>
<name><surname>Osten</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Mapping social behavior-induced brain activation at cellular resolution in the mouse</article-title><source>Cell Reports</source><volume>10</volume><fpage>292</fpage><lpage>305</lpage><pub-id pub-id-type="doi">10.1016/j.celrep.2014.12.014</pub-id><?supplied-pmid 25558063?><pub-id pub-id-type="pmid">25558063</pub-id></element-citation></ref><ref id="bib20"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Klein</surname><given-names>S</given-names></name>
<name><surname>Staring</surname><given-names>M</given-names></name>
<name><surname>Murphy</surname><given-names>K</given-names></name>
<name><surname>Viergever</surname><given-names>MA</given-names></name>
<name><surname>Pluim</surname><given-names>JP</given-names></name>
</person-group><year iso-8601-date="2010">2010</year><article-title>Elastix: a toolbox for intensity-based medical image registration</article-title><source>IEEE Transactions on Medical Imaging</source><volume>29</volume><fpage>196</fpage><lpage>205</lpage><pub-id pub-id-type="doi">10.1109/TMI.2009.2035616</pub-id><?supplied-pmid 19923044?><pub-id pub-id-type="pmid">19923044</pub-id></element-citation></ref><ref id="bib21"><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Kovesi</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>Psivt 2019</article-title><conf-name>Pacific-Rim Symposium on Image and Video Technology</conf-name></element-citation></ref><ref id="bib22"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Lee</surname><given-names>E</given-names></name>
<name><surname>Choi</surname><given-names>J</given-names></name>
<name><surname>Jo</surname><given-names>Y</given-names></name>
<name><surname>Kim</surname><given-names>JY</given-names></name>
<name><surname>Jang</surname><given-names>YJ</given-names></name>
<name><surname>Lee</surname><given-names>HM</given-names></name>
<name><surname>Kim</surname><given-names>SY</given-names></name>
<name><surname>Lee</surname><given-names>HJ</given-names></name>
<name><surname>Cho</surname><given-names>K</given-names></name>
<name><surname>Jung</surname><given-names>N</given-names></name>
<name><surname>Hur</surname><given-names>EM</given-names></name>
<name><surname>Jeong</surname><given-names>SJ</given-names></name>
<name><surname>Moon</surname><given-names>C</given-names></name>
<name><surname>Choe</surname><given-names>Y</given-names></name>
<name><surname>Rhyu</surname><given-names>IJ</given-names></name>
<name><surname>Kim</surname><given-names>H</given-names></name>
<name><surname>Sun</surname><given-names>W</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>ACT-PRESTO: rapid and consistent tissue clearing and labeling method for 3-dimensional (3D) imaging</article-title><source>Scientific Reports</source><volume>6</volume><elocation-id>18631</elocation-id><pub-id pub-id-type="doi">10.1038/srep18631</pub-id><?supplied-pmid 26750588?><pub-id pub-id-type="pmid">26750588</pub-id></element-citation></ref><ref id="bib23"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Lein</surname><given-names>ES</given-names></name>
<name><surname>Hawrylycz</surname><given-names>MJ</given-names></name>
<name><surname>Ao</surname><given-names>N</given-names></name>
<name><surname>Ayres</surname><given-names>M</given-names></name>
<name><surname>Bensinger</surname><given-names>A</given-names></name>
<name><surname>Bernard</surname><given-names>A</given-names></name>
<name><surname>Boe</surname><given-names>AF</given-names></name>
<name><surname>Boguski</surname><given-names>MS</given-names></name>
<name><surname>Brockway</surname><given-names>KS</given-names></name>
<name><surname>Byrnes</surname><given-names>EJ</given-names></name>
<name><surname>Chen</surname><given-names>L</given-names></name>
<name><surname>Chen</surname><given-names>L</given-names></name>
<name><surname>Chen</surname><given-names>TM</given-names></name>
<name><surname>Chin</surname><given-names>MC</given-names></name>
<name><surname>Chong</surname><given-names>J</given-names></name>
<name><surname>Crook</surname><given-names>BE</given-names></name>
<name><surname>Czaplinska</surname><given-names>A</given-names></name>
<name><surname>Dang</surname><given-names>CN</given-names></name>
<name><surname>Datta</surname><given-names>S</given-names></name>
<name><surname>Dee</surname><given-names>NR</given-names></name>
<name><surname>Desaki</surname><given-names>AL</given-names></name>
<name><surname>Desta</surname><given-names>T</given-names></name>
<name><surname>Diep</surname><given-names>E</given-names></name>
<name><surname>Dolbeare</surname><given-names>TA</given-names></name>
<name><surname>Donelan</surname><given-names>MJ</given-names></name>
<name><surname>Dong</surname><given-names>HW</given-names></name>
<name><surname>Dougherty</surname><given-names>JG</given-names></name>
<name><surname>Duncan</surname><given-names>BJ</given-names></name>
<name><surname>Ebbert</surname><given-names>AJ</given-names></name>
<name><surname>Eichele</surname><given-names>G</given-names></name>
<name><surname>Estin</surname><given-names>LK</given-names></name>
<name><surname>Faber</surname><given-names>C</given-names></name>
<name><surname>Facer</surname><given-names>BA</given-names></name>
<name><surname>Fields</surname><given-names>R</given-names></name>
<name><surname>Fischer</surname><given-names>SR</given-names></name>
<name><surname>Fliss</surname><given-names>TP</given-names></name>
<name><surname>Frensley</surname><given-names>C</given-names></name>
<name><surname>Gates</surname><given-names>SN</given-names></name>
<name><surname>Glattfelder</surname><given-names>KJ</given-names></name>
<name><surname>Halverson</surname><given-names>KR</given-names></name>
<name><surname>Hart</surname><given-names>MR</given-names></name>
<name><surname>Hohmann</surname><given-names>JG</given-names></name>
<name><surname>Howell</surname><given-names>MP</given-names></name>
<name><surname>Jeung</surname><given-names>DP</given-names></name>
<name><surname>Johnson</surname><given-names>RA</given-names></name>
<name><surname>Karr</surname><given-names>PT</given-names></name>
<name><surname>Kawal</surname><given-names>R</given-names></name>
<name><surname>Kidney</surname><given-names>JM</given-names></name>
<name><surname>Knapik</surname><given-names>RH</given-names></name>
<name><surname>Kuan</surname><given-names>CL</given-names></name>
<name><surname>Lake</surname><given-names>JH</given-names></name>
<name><surname>Laramee</surname><given-names>AR</given-names></name>
<name><surname>Larsen</surname><given-names>KD</given-names></name>
<name><surname>Lau</surname><given-names>C</given-names></name>
<name><surname>Lemon</surname><given-names>TA</given-names></name>
<name><surname>Liang</surname><given-names>AJ</given-names></name>
<name><surname>Liu</surname><given-names>Y</given-names></name>
<name><surname>Luong</surname><given-names>LT</given-names></name>
<name><surname>Michaels</surname><given-names>J</given-names></name>
<name><surname>Morgan</surname><given-names>JJ</given-names></name>
<name><surname>Morgan</surname><given-names>RJ</given-names></name>
<name><surname>Mortrud</surname><given-names>MT</given-names></name>
<name><surname>Mosqueda</surname><given-names>NF</given-names></name>
<name><surname>Ng</surname><given-names>LL</given-names></name>
<name><surname>Ng</surname><given-names>R</given-names></name>
<name><surname>Orta</surname><given-names>GJ</given-names></name>
<name><surname>Overly</surname><given-names>CC</given-names></name>
<name><surname>Pak</surname><given-names>TH</given-names></name>
<name><surname>Parry</surname><given-names>SE</given-names></name>
<name><surname>Pathak</surname><given-names>SD</given-names></name>
<name><surname>Pearson</surname><given-names>OC</given-names></name>
<name><surname>Puchalski</surname><given-names>RB</given-names></name>
<name><surname>Riley</surname><given-names>ZL</given-names></name>
<name><surname>Rockett</surname><given-names>HR</given-names></name>
<name><surname>Rowland</surname><given-names>SA</given-names></name>
<name><surname>Royall</surname><given-names>JJ</given-names></name>
<name><surname>Ruiz</surname><given-names>MJ</given-names></name>
<name><surname>Sarno</surname><given-names>NR</given-names></name>
<name><surname>Schaffnit</surname><given-names>K</given-names></name>
<name><surname>Shapovalova</surname><given-names>NV</given-names></name>
<name><surname>Sivisay</surname><given-names>T</given-names></name>
<name><surname>Slaughterbeck</surname><given-names>CR</given-names></name>
<name><surname>Smith</surname><given-names>SC</given-names></name>
<name><surname>Smith</surname><given-names>KA</given-names></name>
<name><surname>Smith</surname><given-names>BI</given-names></name>
<name><surname>Sodt</surname><given-names>AJ</given-names></name>
<name><surname>Stewart</surname><given-names>NN</given-names></name>
<name><surname>Stumpf</surname><given-names>KR</given-names></name>
<name><surname>Sunkin</surname><given-names>SM</given-names></name>
<name><surname>Sutram</surname><given-names>M</given-names></name>
<name><surname>Tam</surname><given-names>A</given-names></name>
<name><surname>Teemer</surname><given-names>CD</given-names></name>
<name><surname>Thaller</surname><given-names>C</given-names></name>
<name><surname>Thompson</surname><given-names>CL</given-names></name>
<name><surname>Varnam</surname><given-names>LR</given-names></name>
<name><surname>Visel</surname><given-names>A</given-names></name>
<name><surname>Whitlock</surname><given-names>RM</given-names></name>
<name><surname>Wohnoutka</surname><given-names>PE</given-names></name>
<name><surname>Wolkey</surname><given-names>CK</given-names></name>
<name><surname>Wong</surname><given-names>VY</given-names></name>
<name><surname>Wood</surname><given-names>M</given-names></name>
<name><surname>Yaylaoglu</surname><given-names>MB</given-names></name>
<name><surname>Young</surname><given-names>RC</given-names></name>
<name><surname>Youngstrom</surname><given-names>BL</given-names></name>
<name><surname>Yuan</surname><given-names>XF</given-names></name>
<name><surname>Zhang</surname><given-names>B</given-names></name>
<name><surname>Zwingman</surname><given-names>TA</given-names></name>
<name><surname>Jones</surname><given-names>AR</given-names></name>
</person-group><year iso-8601-date="2007">2007</year><article-title>Genome-wide atlas of gene expression in the adult mouse brain</article-title><source>Nature</source><volume>445</volume><fpage>168</fpage><lpage>176</lpage><pub-id pub-id-type="doi">10.1038/nature05453</pub-id><?supplied-pmid 17151600?><pub-id pub-id-type="pmid">17151600</pub-id></element-citation></ref><ref id="bib24"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Li</surname><given-names>A</given-names></name>
<name><surname>Gong</surname><given-names>H</given-names></name>
<name><surname>Zhang</surname><given-names>B</given-names></name>
<name><surname>Wang</surname><given-names>Q</given-names></name>
<name><surname>Yan</surname><given-names>C</given-names></name>
<name><surname>Wu</surname><given-names>J</given-names></name>
<name><surname>Liu</surname><given-names>Q</given-names></name>
<name><surname>Zeng</surname><given-names>S</given-names></name>
<name><surname>Luo</surname><given-names>Q</given-names></name>
</person-group><year iso-8601-date="2010">2010</year><article-title>Micro-optical sectioning tomography to obtain a high-resolution atlas of the mouse brain</article-title><source>Science</source><volume>330</volume><fpage>1404</fpage><lpage>1408</lpage><pub-id pub-id-type="doi">10.1126/science.1191776</pub-id><?supplied-pmid 21051596?><pub-id pub-id-type="pmid">21051596</pub-id></element-citation></ref><ref id="bib25"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Liu</surname><given-names>S</given-names></name>
<name><surname>Nie</surname><given-names>J</given-names></name>
<name><surname>Li</surname><given-names>Y</given-names></name>
<name><surname>Yu</surname><given-names>T</given-names></name>
<name><surname>Zhu</surname><given-names>D</given-names></name>
<name><surname>Fei</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Three-dimensional, isotropic imaging of mouse brain using multi-view deconvolution light sheet microscopy</article-title><source>Journal of Innovative Optical Health Sciences</source><volume>10</volume><elocation-id>1743006</elocation-id><pub-id pub-id-type="doi">10.1142/S1793545817430064</pub-id></element-citation></ref><ref id="bib26"><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Long</surname><given-names>J</given-names></name>
<name><surname>Shelhamer</surname><given-names>E</given-names></name>
<name><surname>Darrell</surname><given-names>T</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Fully convolutional networks for semantic segmentation</article-title><conf-name>IEEE Conference on Computer Vision and Pattern Recognition (CVPR)</conf-name><fpage>3431</fpage><lpage>3440</lpage><pub-id pub-id-type="doi">10.1109/CVPR.2015.7298965</pub-id></element-citation></ref><ref id="bib27"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Lorensen</surname><given-names>WE</given-names></name>
<name><surname>Cline</surname><given-names>HE</given-names></name>
</person-group><year iso-8601-date="1987">1987</year><article-title>Marching cubes: a high resolution 3D surface construction algorithm</article-title><source>ACM SIGGRAPH Computer Graphics</source><volume>21</volume><fpage>163</fpage><lpage>169</lpage><pub-id pub-id-type="doi">10.1145/37402.37422</pub-id></element-citation></ref><ref id="bib28"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Maes</surname><given-names>F</given-names></name>
<name><surname>Collignon</surname><given-names>A</given-names></name>
<name><surname>Vandermeulen</surname><given-names>D</given-names></name>
<name><surname>Marchal</surname><given-names>G</given-names></name>
<name><surname>Suetens</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="1997">1997</year><article-title>Multimodality image registration by maximization of mutual information</article-title><source>IEEE Transactions on Medical Imaging</source><volume>16</volume><fpage>187</fpage><lpage>198</lpage><pub-id pub-id-type="doi">10.1109/42.563664</pub-id><?supplied-pmid 9101328?><pub-id pub-id-type="pmid">9101328</pub-id></element-citation></ref><ref id="bib29"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Milletari</surname><given-names>F</given-names></name>
<name><surname>Ahmadi</surname><given-names>S-A</given-names></name>
<name><surname>Kroll</surname><given-names>C</given-names></name>
<name><surname>Plate</surname><given-names>A</given-names></name>
<name><surname>Rozanski</surname><given-names>V</given-names></name>
<name><surname>Maiostre</surname><given-names>J</given-names></name>
<name><surname>Levin</surname><given-names>J</given-names></name>
<name><surname>Dietrich</surname><given-names>O</given-names></name>
<name><surname>Ertl-Wagner</surname><given-names>B</given-names></name>
<name><surname>B&#x000f6;tzel</surname><given-names>K</given-names></name>
<name><surname>Navab</surname><given-names>N</given-names></name>
</person-group><year iso-8601-date="2017">2017</year><article-title>Hough-CNN: deep learning for segmentation of deep brain regions in MRI and ultrasound</article-title><source>Computer Vision and Image Understanding</source><volume>164</volume><fpage>92</fpage><lpage>102</lpage><pub-id pub-id-type="doi">10.1016/j.cviu.2017.04.002</pub-id></element-citation></ref><ref id="bib30"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ni</surname><given-names>H</given-names></name>
<name><surname>Tan</surname><given-names>C</given-names></name>
<name><surname>Feng</surname><given-names>Z</given-names></name>
<name><surname>Chen</surname><given-names>S</given-names></name>
<name><surname>Zhang</surname><given-names>Z</given-names></name>
<name><surname>Li</surname><given-names>W</given-names></name>
<name><surname>Guan</surname><given-names>Y</given-names></name>
<name><surname>Gong</surname><given-names>H</given-names></name>
<name><surname>Luo</surname><given-names>Q</given-names></name>
<name><surname>Li</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>A robust image registration interface for large volume brain atlas</article-title><source>Scientific Reports</source><volume>10</volume><elocation-id>59042-y</elocation-id><pub-id pub-id-type="doi">10.1038/s41598-020-59042-y</pub-id></element-citation></ref><ref id="bib31"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Nie</surname><given-names>J</given-names></name>
<name><surname>Liu</surname><given-names>S</given-names></name>
<name><surname>Yu</surname><given-names>T</given-names></name>
<name><surname>Li</surname><given-names>Y</given-names></name>
<name><surname>Ping</surname><given-names>J</given-names></name>
<name><surname>Wan</surname><given-names>P</given-names></name>
<name><surname>Zhao</surname><given-names>F</given-names></name>
<name><surname>Huang</surname><given-names>Y</given-names></name>
<name><surname>Mei</surname><given-names>W</given-names></name>
<name><surname>Zeng</surname><given-names>S</given-names></name>
<name><surname>Zhu</surname><given-names>D</given-names></name>
<name><surname>Fei</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2020">2020</year><article-title>Fast, 3D isotropic imaging of whole mouse brain using Multiangle-Resolved subvoxel SPIM</article-title><source>Advanced Science</source><volume>7</volume><elocation-id>1901891</elocation-id><pub-id pub-id-type="doi">10.1002/advs.201901891</pub-id><?supplied-pmid 32042557?><pub-id pub-id-type="pmid">32042557</pub-id></element-citation></ref><ref id="bib32"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Niedworok</surname><given-names>CJ</given-names></name>
<name><surname>Brown</surname><given-names>APY</given-names></name>
<name><surname>Jorge Cardoso</surname><given-names>M</given-names></name>
<name><surname>Osten</surname><given-names>P</given-names></name>
<name><surname>Ourselin</surname><given-names>S</given-names></name>
<name><surname>Modat</surname><given-names>M</given-names></name>
<name><surname>Margrie</surname><given-names>TW</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>aMAP is a validated pipeline for registration and segmentation of high-resolution mouse brain data</article-title><source>Nature Communications</source><volume>7</volume><elocation-id>ncomms11879</elocation-id><pub-id pub-id-type="doi">10.1038/ncomms11879</pub-id></element-citation></ref><ref id="bib33"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Oh</surname><given-names>SW</given-names></name>
<name><surname>Harris</surname><given-names>JA</given-names></name>
<name><surname>Ng</surname><given-names>L</given-names></name>
<name><surname>Winslow</surname><given-names>B</given-names></name>
<name><surname>Cain</surname><given-names>N</given-names></name>
<name><surname>Mihalas</surname><given-names>S</given-names></name>
<name><surname>Wang</surname><given-names>Q</given-names></name>
<name><surname>Lau</surname><given-names>C</given-names></name>
<name><surname>Kuan</surname><given-names>L</given-names></name>
<name><surname>Henry</surname><given-names>AM</given-names></name>
<name><surname>Mortrud</surname><given-names>MT</given-names></name>
<name><surname>Ouellette</surname><given-names>B</given-names></name>
<name><surname>Nguyen</surname><given-names>TN</given-names></name>
<name><surname>Sorensen</surname><given-names>SA</given-names></name>
<name><surname>Slaughterbeck</surname><given-names>CR</given-names></name>
<name><surname>Wakeman</surname><given-names>W</given-names></name>
<name><surname>Li</surname><given-names>Y</given-names></name>
<name><surname>Feng</surname><given-names>D</given-names></name>
<name><surname>Ho</surname><given-names>A</given-names></name>
<name><surname>Nicholas</surname><given-names>E</given-names></name>
<name><surname>Hirokawa</surname><given-names>KE</given-names></name>
<name><surname>Bohn</surname><given-names>P</given-names></name>
<name><surname>Joines</surname><given-names>KM</given-names></name>
<name><surname>Peng</surname><given-names>H</given-names></name>
<name><surname>Hawrylycz</surname><given-names>MJ</given-names></name>
<name><surname>Phillips</surname><given-names>JW</given-names></name>
<name><surname>Hohmann</surname><given-names>JG</given-names></name>
<name><surname>Wohnoutka</surname><given-names>P</given-names></name>
<name><surname>Gerfen</surname><given-names>CR</given-names></name>
<name><surname>Koch</surname><given-names>C</given-names></name>
<name><surname>Bernard</surname><given-names>A</given-names></name>
<name><surname>Dang</surname><given-names>C</given-names></name>
<name><surname>Jones</surname><given-names>AR</given-names></name>
<name><surname>Zeng</surname><given-names>H</given-names></name>
</person-group><year iso-8601-date="2014">2014</year><article-title>A mesoscale connectome of the mouse brain</article-title><source>Nature</source><volume>508</volume><fpage>207</fpage><lpage>214</lpage><pub-id pub-id-type="doi">10.1038/nature13186</pub-id><?supplied-pmid 24695228?><pub-id pub-id-type="pmid">24695228</pub-id></element-citation></ref><ref id="bib34"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Pan</surname><given-names>C</given-names></name>
<name><surname>Cai</surname><given-names>R</given-names></name>
<name><surname>Quacquarelli</surname><given-names>FP</given-names></name>
<name><surname>Ghasemigharagoz</surname><given-names>A</given-names></name>
<name><surname>Lourbopoulos</surname><given-names>A</given-names></name>
<name><surname>Matryba</surname><given-names>P</given-names></name>
<name><surname>Plesnila</surname><given-names>N</given-names></name>
<name><surname>Dichgans</surname><given-names>M</given-names></name>
<name><surname>Hellal</surname><given-names>F</given-names></name>
<name><surname>Ert&#x000fc;rk</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Shrinkage-mediated imaging of entire organs and organisms using uDISCO</article-title><source>Nature Methods</source><volume>13</volume><fpage>859</fpage><lpage>867</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3964</pub-id><?supplied-pmid 27548807?><pub-id pub-id-type="pmid">27548807</pub-id></element-citation></ref><ref id="bib35"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Renier</surname><given-names>N</given-names></name>
<name><surname>Adams</surname><given-names>EL</given-names></name>
<name><surname>Kirst</surname><given-names>C</given-names></name>
<name><surname>Wu</surname><given-names>Z</given-names></name>
<name><surname>Azevedo</surname><given-names>R</given-names></name>
<name><surname>Kohl</surname><given-names>J</given-names></name>
<name><surname>Autry</surname><given-names>AE</given-names></name>
<name><surname>Kadiri</surname><given-names>L</given-names></name>
<name><surname>Umadevi Venkataraju</surname><given-names>K</given-names></name>
<name><surname>Zhou</surname><given-names>Y</given-names></name>
<name><surname>Wang</surname><given-names>VX</given-names></name>
<name><surname>Tang</surname><given-names>CY</given-names></name>
<name><surname>Olsen</surname><given-names>O</given-names></name>
<name><surname>Dulac</surname><given-names>C</given-names></name>
<name><surname>Osten</surname><given-names>P</given-names></name>
<name><surname>Tessier-Lavigne</surname><given-names>M</given-names></name>
</person-group><year iso-8601-date="2016">2016</year><article-title>Mapping of brain activity by automated volume analysis of immediate early genes</article-title><source>Cell</source><volume>165</volume><fpage>1789</fpage><lpage>1802</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2016.05.007</pub-id><?supplied-pmid 27238021?><pub-id pub-id-type="pmid">27238021</pub-id></element-citation></ref><ref id="bib36"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Richardson</surname><given-names>DS</given-names></name>
<name><surname>Lichtman</surname><given-names>JW</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Clarifying tissue clearing</article-title><source>Cell</source><volume>162</volume><fpage>246</fpage><lpage>257</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2015.06.067</pub-id><?supplied-pmid 26186186?><pub-id pub-id-type="pmid">26186186</pub-id></element-citation></ref><ref id="bib37"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Schindelin</surname><given-names>J</given-names></name>
<name><surname>Arganda-Carreras</surname><given-names>I</given-names></name>
<name><surname>Frise</surname><given-names>E</given-names></name>
<name><surname>Kaynig</surname><given-names>V</given-names></name>
<name><surname>Longair</surname><given-names>M</given-names></name>
<name><surname>Pietzsch</surname><given-names>T</given-names></name>
<name><surname>Preibisch</surname><given-names>S</given-names></name>
<name><surname>Rueden</surname><given-names>C</given-names></name>
<name><surname>Saalfeld</surname><given-names>S</given-names></name>
<name><surname>Schmid</surname><given-names>B</given-names></name>
<name><surname>Tinevez</surname><given-names>JY</given-names></name>
<name><surname>White</surname><given-names>DJ</given-names></name>
<name><surname>Hartenstein</surname><given-names>V</given-names></name>
<name><surname>Eliceiri</surname><given-names>K</given-names></name>
<name><surname>Tomancak</surname><given-names>P</given-names></name>
<name><surname>Cardona</surname><given-names>A</given-names></name>
</person-group><year iso-8601-date="2012">2012</year><article-title>Fiji: an open-source platform for biological-image analysis</article-title><source>Nature Methods</source><volume>9</volume><fpage>676</fpage><lpage>682</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2019</pub-id><?supplied-pmid 22743772?><pub-id pub-id-type="pmid">22743772</pub-id></element-citation></ref><ref id="bib38"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Schwarz</surname><given-names>MK</given-names></name>
<name><surname>Scherbarth</surname><given-names>A</given-names></name>
<name><surname>Sprengel</surname><given-names>R</given-names></name>
<name><surname>Engelhardt</surname><given-names>J</given-names></name>
<name><surname>Theer</surname><given-names>P</given-names></name>
<name><surname>Giese</surname><given-names>G</given-names></name>
</person-group><year iso-8601-date="2015">2015</year><article-title>Fluorescent-protein stabilization and high-resolution imaging of cleared, intact mouse brains</article-title><source>PLOS ONE</source><volume>10</volume><elocation-id>e0124650</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0124650</pub-id><?supplied-pmid 25993380?><pub-id pub-id-type="pmid">25993380</pub-id></element-citation></ref><ref id="bib39"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Shamonin</surname><given-names>DP</given-names></name>
<name><surname>Bron</surname><given-names>EE</given-names></name>
<name><surname>Lelieveldt</surname><given-names>BP</given-names></name>
<name><surname>Smits</surname><given-names>M</given-names></name>
<name><surname>Klein</surname><given-names>S</given-names></name>
<name><surname>Staring</surname><given-names>M</given-names></name>
<collab>Alzheimer's Disease Neuroimaging Initiative</collab>
</person-group><year iso-8601-date="2013">2013</year><article-title>Fast parallel image registration on CPU and GPU for diagnostic classification of Alzheimer's disease</article-title><source>Frontiers in Neuroinformatics</source><volume>7</volume><elocation-id>50</elocation-id><pub-id pub-id-type="doi">10.3389/fninf.2013.00050</pub-id><?supplied-pmid 24474917?><pub-id pub-id-type="pmid">24474917</pub-id></element-citation></ref><ref id="bib40"><element-citation publication-type="preprint"><person-group person-group-type="author">
<name><surname>Song</surname><given-names>JH</given-names></name>
<name><surname>Song</surname><given-names>Y-H</given-names></name>
</person-group><year iso-8601-date="2018">2018</year><article-title>Automated 3-D mapping of single neurons in the standard brain atlas using single brain slices</article-title><source>bioRxiv</source><pub-id pub-id-type="doi">10.1101/373134</pub-id></element-citation></ref><ref id="bib41"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Th&#x000e9;venaz</surname><given-names>P</given-names></name>
<name><surname>Unser</surname><given-names>M</given-names></name>
</person-group><year iso-8601-date="2000">2000</year><article-title>Optimization of mutual information for multiresolution image registration</article-title><source>IEEE Transactions on Image Processing : A Publication of the IEEE Signal Processing Society</source><volume>9</volume><fpage>2083</fpage><lpage>2099</lpage><pub-id pub-id-type="doi">10.1109/83.887976</pub-id><?supplied-pmid 18262946?><pub-id pub-id-type="pmid">18262946</pub-id></element-citation></ref><ref id="bib42"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Wang</surname><given-names>X</given-names></name>
<name><surname>Zhang</surname><given-names>Y</given-names></name>
<name><surname>Wang</surname><given-names>X</given-names></name>
<name><surname>Dai</surname><given-names>J</given-names></name>
<name><surname>Hua</surname><given-names>R</given-names></name>
<name><surname>Zeng</surname><given-names>S</given-names></name>
<name><surname>Li</surname><given-names>H</given-names></name>
</person-group><year iso-8601-date="2020">2020a</year><article-title>Anxiety-related cell-type-specific neural circuits in the anterior-dorsal bed nucleus of the stria terminalis</article-title><source>Science Bulletin</source><volume>65</volume><fpage>1203</fpage><lpage>1216</lpage><pub-id pub-id-type="doi">10.1016/j.scib.2020.03.028</pub-id></element-citation></ref><ref id="bib43"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Wang</surname><given-names>Q</given-names></name>
<name><surname>Ding</surname><given-names>S-L</given-names></name>
<name><surname>Li</surname><given-names>Y</given-names></name>
<name><surname>Royall</surname><given-names>J</given-names></name>
<name><surname>Feng</surname><given-names>D</given-names></name>
<name><surname>Lesnar</surname><given-names>P</given-names></name>
<name><surname>Graddis</surname><given-names>N</given-names></name>
<name><surname>Naeemi</surname><given-names>M</given-names></name>
<name><surname>Facer</surname><given-names>B</given-names></name>
<name><surname>Ho</surname><given-names>A</given-names></name>
<name><surname>Dolbeare</surname><given-names>T</given-names></name>
<name><surname>Blanchard</surname><given-names>B</given-names></name>
<name><surname>Dee</surname><given-names>N</given-names></name>
<name><surname>Wakeman</surname><given-names>W</given-names></name>
<name><surname>Hirokawa</surname><given-names>KE</given-names></name>
<name><surname>Szafer</surname><given-names>A</given-names></name>
<name><surname>Sunkin</surname><given-names>SM</given-names></name>
<name><surname>Oh</surname><given-names>SW</given-names></name>
<name><surname>Bernard</surname><given-names>A</given-names></name>
<name><surname>Phillips</surname><given-names>JW</given-names></name>
<name><surname>Hawrylycz</surname><given-names>M</given-names></name>
<name><surname>Koch</surname><given-names>C</given-names></name>
<name><surname>Zeng</surname><given-names>H</given-names></name>
<name><surname>Harris</surname><given-names>JA</given-names></name>
<name><surname>Ng</surname><given-names>L</given-names></name>
</person-group><year iso-8601-date="2020">2020b</year><article-title>The allen mouse brain common coordinate framework: a 3D reference atlas</article-title><source>Cell</source><volume>181</volume><fpage>936</fpage><lpage>953</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2020.04.007</pub-id><pub-id pub-id-type="pmid">32386544</pub-id></element-citation></ref><ref id="bib44"><element-citation publication-type="software"><person-group person-group-type="author">
<name><surname>Wang</surname><given-names>X</given-names></name>
</person-group><year iso-8601-date="2021">2021a</year><data-title>birds_reg</data-title><source>Software Heritage</source><version designator="swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836">swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836</version><ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:dir:1eb68910dba957ab6819ec8d5f364fb0ccd7d249;origin=https://github.com/bleach1by1/birds_reg;visit=swh:1:snp:fc666e617787c219905a3cdd65e7c1e94cc05e54;anchor=swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836/" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:dir:1eb68910dba957ab6819ec8d5f364fb0ccd7d249;origin=https://github.com/bleach1by1/birds_reg;visit=swh:1:snp:fc666e617787c219905a3cdd65e7c1e94cc05e54;anchor=swh:1:rev:22cf3d792c3887708a65ddae43d6dde7ed8b7836/</ext-link></element-citation></ref><ref id="bib45"><element-citation publication-type="software"><person-group person-group-type="author">
<name><surname>Wang</surname><given-names>X</given-names></name>
</person-group><year iso-8601-date="2021">2021b</year><data-title>BIRDS_plugin110</data-title><source>Software Heritage</source><version designator="swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475">swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475</version><ext-link xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="https://archive.softwareheritage.org/swh:1:dir:a7a4ec239977432884d064a17d721578b22e5b54;origin=https://github.com/bleach1by1/BIRDS_plugin;visit=swh:1:snp:54f28d1de7607346ea62ea5de2132e79396d0e9e;anchor=swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475/" ext-link-type="uri">https://archive.softwareheritage.org/swh:1:dir:a7a4ec239977432884d064a17d721578b22e5b54;origin=https://github.com/bleach1by1/BIRDS_plugin;visit=swh:1:snp:54f28d1de7607346ea62ea5de2132e79396d0e9e;anchor=swh:1:rev:41e90d4518321d6ca8e806ccadb2809bfa6bd475/</ext-link></element-citation></ref><ref id="bib46"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Warfield</surname><given-names>SK</given-names></name>
<name><surname>Zou</surname><given-names>KH</given-names></name>
<name><surname>Wells</surname><given-names>WM</given-names></name>
</person-group><year iso-8601-date="2004">2004</year><article-title>Simultaneous truth and performance level estimation (STAPLE): an algorithm for the validation of image segmentation</article-title><source>IEEE Transactions on Medical Imaging</source><volume>23</volume><fpage>903</fpage><lpage>921</lpage><pub-id pub-id-type="doi">10.1109/TMI.2004.828354</pub-id><?supplied-pmid 15250643?><pub-id pub-id-type="pmid">15250643</pub-id></element-citation></ref><ref id="bib47"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Zhang</surname><given-names>H</given-names></name>
<name><surname>Fang</surname><given-names>C</given-names></name>
<name><surname>Xie</surname><given-names>X</given-names></name>
<name><surname>Yang</surname><given-names>Y</given-names></name>
<name><surname>Mei</surname><given-names>W</given-names></name>
<name><surname>Jin</surname><given-names>D</given-names></name>
<name><surname>Fei</surname><given-names>P</given-names></name>
</person-group><year iso-8601-date="2019">2019</year><article-title>High-throughput, high-resolution deep learning microscopy based on registration-free generative adversarial network</article-title><source>Biomedical Optics Express</source><volume>10</volume><fpage>1044</fpage><lpage>1063</lpage><pub-id pub-id-type="doi">10.1364/BOE.10.001044</pub-id><?supplied-pmid 30891329?><pub-id pub-id-type="pmid">30891329</pub-id></element-citation></ref><ref id="bib48"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Zingg</surname><given-names>B</given-names></name>
<name><surname>Hintiryan</surname><given-names>H</given-names></name>
<name><surname>Gou</surname><given-names>L</given-names></name>
<name><surname>Song</surname><given-names>MY</given-names></name>
<name><surname>Bay</surname><given-names>M</given-names></name>
<name><surname>Bienkowski</surname><given-names>MS</given-names></name>
<name><surname>Foster</surname><given-names>NN</given-names></name>
<name><surname>Yamashita</surname><given-names>S</given-names></name>
<name><surname>Bowman</surname><given-names>I</given-names></name>
<name><surname>Toga</surname><given-names>AW</given-names></name>
<name><surname>Dong</surname><given-names>HW</given-names></name>
</person-group><year iso-8601-date="2014">2014</year><article-title>Neural networks of the mouse neocortex</article-title><source>Cell</source><volume>156</volume><fpage>1096</fpage><lpage>1111</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2014.02.023</pub-id><?supplied-pmid 24581503?><pub-id pub-id-type="pmid">24581503</pub-id></element-citation></ref><ref id="bib49"><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Zuo</surname><given-names>X-N</given-names></name>
<name><surname>Anderson</surname><given-names>JS</given-names></name>
<name><surname>Bellec</surname><given-names>P</given-names></name>
<name><surname>Birn</surname><given-names>RM</given-names></name>
<name><surname>Biswal</surname><given-names>BB</given-names></name>
<name><surname>Blautzik</surname><given-names>J</given-names></name>
<name><surname>Breitner</surname><given-names>JCS</given-names></name>
<name><surname>Buckner</surname><given-names>RL</given-names></name>
<name><surname>Calhoun</surname><given-names>VD</given-names></name>
<name><surname>Castellanos</surname><given-names>FX</given-names></name>
<name><surname>Chen</surname><given-names>A</given-names></name>
<name><surname>Chen</surname><given-names>B</given-names></name>
<name><surname>Chen</surname><given-names>J</given-names></name>
<name><surname>Chen</surname><given-names>X</given-names></name>
<name><surname>Colcombe</surname><given-names>SJ</given-names></name>
<name><surname>Courtney</surname><given-names>W</given-names></name>
<name><surname>Craddock</surname><given-names>RC</given-names></name>
<name><surname>Di Martino</surname><given-names>A</given-names></name>
<name><surname>Dong</surname><given-names>H-M</given-names></name>
<name><surname>Fu</surname><given-names>X</given-names></name>
<name><surname>Gong</surname><given-names>Q</given-names></name>
<name><surname>Gorgolewski</surname><given-names>KJ</given-names></name>
<name><surname>Han</surname><given-names>Y</given-names></name>
<name><surname>He</surname><given-names>Y</given-names></name>
<name><surname>He</surname><given-names>Y</given-names></name>
<name><surname>Ho</surname><given-names>E</given-names></name>
<name><surname>Holmes</surname><given-names>A</given-names></name>
<name><surname>Hou</surname><given-names>X-H</given-names></name>
<name><surname>Huckins</surname><given-names>J</given-names></name>
<name><surname>Jiang</surname><given-names>T</given-names></name>
<name><surname>Jiang</surname><given-names>Y</given-names></name>
<name><surname>Kelley</surname><given-names>W</given-names></name>
<name><surname>Kelly</surname><given-names>C</given-names></name>
<name><surname>King</surname><given-names>M</given-names></name>
<name><surname>LaConte</surname><given-names>SM</given-names></name>
<name><surname>Lainhart</surname><given-names>JE</given-names></name>
<name><surname>Lei</surname><given-names>X</given-names></name>
<name><surname>Li</surname><given-names>H-J</given-names></name>
<name><surname>Li</surname><given-names>K</given-names></name>
<name><surname>Li</surname><given-names>K</given-names></name>
<name><surname>Lin</surname><given-names>Q</given-names></name>
<name><surname>Liu</surname><given-names>D</given-names></name>
<name><surname>Liu</surname><given-names>J</given-names></name>
<name><surname>Liu</surname><given-names>X</given-names></name>
<name><surname>Liu</surname><given-names>Y</given-names></name>
<name><surname>Lu</surname><given-names>G</given-names></name>
<name><surname>Lu</surname><given-names>J</given-names></name>
<name><surname>Luna</surname><given-names>B</given-names></name>
<name><surname>Luo</surname><given-names>J</given-names></name>
<name><surname>Lurie</surname><given-names>D</given-names></name>
<name><surname>Mao</surname><given-names>Y</given-names></name>
<name><surname>Margulies</surname><given-names>DS</given-names></name>
<name><surname>Mayer</surname><given-names>AR</given-names></name>
<name><surname>Meindl</surname><given-names>T</given-names></name>
<name><surname>Meyerand</surname><given-names>ME</given-names></name>
<name><surname>Nan</surname><given-names>W</given-names></name>
<name><surname>Nielsen</surname><given-names>JA</given-names></name>
<name><surname>O&#x02019;Connor</surname><given-names>D</given-names></name>
<name><surname>Paulsen</surname><given-names>D</given-names></name>
<name><surname>Prabhakaran</surname><given-names>V</given-names></name>
<name><surname>Qi</surname><given-names>Z</given-names></name>
<name><surname>Qiu</surname><given-names>J</given-names></name>
<name><surname>Shao</surname><given-names>C</given-names></name>
<name><surname>Shehzad</surname><given-names>Z</given-names></name>
<name><surname>Tang</surname><given-names>W</given-names></name>
<name><surname>Villringer</surname><given-names>A</given-names></name>
<name><surname>Wang</surname><given-names>H</given-names></name>
<name><surname>Wang</surname><given-names>K</given-names></name>
<name><surname>Wei</surname><given-names>D</given-names></name>
<name><surname>Wei</surname><given-names>G-X</given-names></name>
<name><surname>Weng</surname><given-names>X-C</given-names></name>
<name><surname>Wu</surname><given-names>X</given-names></name>
<name><surname>Xu</surname><given-names>T</given-names></name>
<name><surname>Yang</surname><given-names>N</given-names></name>
<name><surname>Yang</surname><given-names>Z</given-names></name>
<name><surname>Zang</surname><given-names>Y-F</given-names></name>
<name><surname>Zhang</surname><given-names>L</given-names></name>
<name><surname>Zhang</surname><given-names>Q</given-names></name>
<name><surname>Zhang</surname><given-names>Z</given-names></name>
<name><surname>Zhang</surname><given-names>Z</given-names></name>
<name><surname>Zhao</surname><given-names>K</given-names></name>
<name><surname>Zhen</surname><given-names>Z</given-names></name>
<name><surname>Zhou</surname><given-names>Y</given-names></name>
<name><surname>Zhu</surname><given-names>X-T</given-names></name>
<name><surname>Milham</surname><given-names>MP</given-names></name>
</person-group><year iso-8601-date="2014">2014</year><article-title>An open science resource for establishing reliability and reproducibility in functional connectomics</article-title><source>Scientific Data</source><volume>1</volume><fpage>1</fpage><lpage>13</lpage><pub-id pub-id-type="doi">10.1038/sdata.2014.49</pub-id></element-citation></ref></ref-list></back><sub-article article-type="decision-letter" id="sa1"><front-stub><article-id pub-id-type="doi">10.7554/eLife.63455.sa1</article-id><title-group><article-title>Decision letter</article-title></title-group><contrib-group><contrib contrib-type="editor"><name><surname>Smith</surname><given-names>Jeffrey C</given-names></name><role>Reviewing Editor</role><aff>
<institution>National Institute of Neurological Disorders and Stroke</institution>
<country>United States</country>
</aff></contrib></contrib-group><contrib-group><contrib contrib-type="reviewer"><name><surname>Gerfen</surname><given-names>Charles R</given-names></name><role>Reviewer</role><aff>
<institution>National Institute of Mental Health</institution>
<country>United States</country>
</aff></contrib><contrib contrib-type="reviewer"><name><surname>Jahanipour</surname><given-names>Jahandar</given-names></name><role>Reviewer</role><aff>
<institution>National Institutes of Health</institution>
<country>United States</country>
</aff></contrib></contrib-group></front-stub><body><boxed-text position="float"><p>In the interests of transparency, eLife publishes the most substantive revision requests and the accompanying author responses.</p></boxed-text><p>
<bold>Acceptance summary:</bold>
</p><p>A major advance in analyses of brain and neural circuit morphology is the capability to register labeled neuronal structures to a reference digital 3D brain structural atlas such as the Allen Institute for Brain Sciences mouse brain Common Coordinate Framework. In this technical advance, the authors have developed open source, automated methods for 3D morphological mapping in the mouse brain employing dual-channel microscopic neuroimage data and deep neural network learning. The authors demonstrate improved accuracy of image registration to the Allen Institute atlas and morphological reconstruction of mouse brain image sets, suggesting that the authors' approach is an important addition to the tool box for 3D brain morphological reconstruction and representation.</p><p>
<bold>Decision letter after peer review:</bold>
</p><p>Thank you for submitting your article "Bi-channel Image Registration and Deep-learning Segmentation (BIRDS) for efficient, versatile 3D mapping of mouse brain" for consideration by <italic toggle="yes">eLife</italic>. Your article has been reviewed by two peer reviewers, and the evaluation has been overseen by a Reviewing Editor and Laura Colgin as the Senior Editor. The following individuals involved in review of your submission have agreed to reveal their identity: Charles R Gerfen (Reviewer #1); Jahandar Jahanipour (Reviewer #2).</p><p>The reviewers have discussed the reviews with one another and the Reviewing Editor has drafted this decision to help you prepare a revised submission.</p><p>We would like to draw your attention to changes in our revision policy that we have made in response to COVID-19 (https://elifesciences.org/articles/57162). Specifically, we are asking editors to accept without delay manuscripts, like yours, that they judge can stand as <italic toggle="yes">eLife</italic> papers without additional data, even if they feel that they would make the manuscript stronger. Thus the revisions requested below only address clarity and presentation.</p><p>Summary:</p><p>Wang et al. present a technique for registering and analysis of whole mouse brain image sets to the Allen CCF. A major advance in the analysis of neuroanatomical circuits is the ability to register labeled neuronal structures in the whole mouse brain to a common mouse brain atlas. Numerous studies have used various techniques for such registration with varying success and accuracy. The authors' BIRDS process for registering whole mouse brain image sets to the Allen CCF uses a number of innovative approaches that provide some advances in the accuracy of registration, at least for the examples described. There are a number of major revisions listed below that need to be addressed.</p><p>Essential revisions:</p><p>1) Despite the steps being described in detail, parts of the image processing and registration process are difficult to understand, which requires careful revision of the text to clarify the steps in the procedure depicted in Figure 1:</p><p>&#x02013; The down-sampling is straightforward. However, the "dynamic down-sampling ratios applied to the.&#x02026; " is not at all clear. What exactly is being done and what is the purpose? By "dynamic down-sampling ratio" is it meant that instead of the ratio of the original (1:20 downsampling ratio to convert the original to the dimensions of the Allen CCF ) that the ratio might be different at different rostro-caudal levels of the brain? And is this to account for differences in how the brains are processed. For instance, experimental brains might have different dimensions in the x and y planes than the Allen CCF or might be deformed in different ways. And that this process makes those corrections at the appropriate rostro-caudal levels.</p><p>&#x02013; The next step(s) are very difficult to follow: the "feature based iterative registration of the Allen reference image with pre-processed experimental images". This step involves the application of: 1) phase congruency algorithm to extract high contrast information from both experimental and template brain (CCF?) images and 2) enhanced axial mutual information extracted by grayscale reversal processing. And then somehow a "bi-channel" registration was performed. What is unclear is what channels are used for the "bi-channel" registration. Is a separate channel formed by the combination of the processes of 1) and 2)? Is the original image also combined with these channels? Are these processes applied to the Allen 2-photon images? And then exactly what is registered and how is that done?</p><p>&#x02013; It might be better if the process steps were described separately and succinctly from comments about how this is better than other methods. The description in the Materials and methods section is somewhat better, but still confusing. The figure legend for Figure 1&#x02014;figure supplement 4 comes close to being understood.</p><p>&#x02013; The "bi-channel" process is the most confusing, and since it is part of the acronym should be better explained. In general, it seems straightforward, using not only the original image data but also that data that has been processed to enhance edges and other features. What is confusing is whether you are also using image data from channels that have labeled neurons and axons &#x02013; which is stored in other channels?</p><p>2) Authors propose an algorithm for registration of high-resolution large-scale microscopic images of whole mouse brain datasets. This type of computational processing requires adequate memory and processing power which might not be available in a standard laboratory environment. Authors should elaborate on the computational techniques to address the large dataset handling of the proposed algorithm in regards of memory and the speed. Furthermore, is the proposed algorithm expandable to larger datasets such as super-resolution images or high-resolution whole rat brain datasets specifically in the context of memory and speed?</p><p>3) Authors discuss downsampling the dataset before registration. How does this downsampling affect the results specifically the smoothness of the borders between the regions?</p><p>4) The tracing of each fluorescently labelled neuronal cell is not clear! The method for tracing is neither explained nor referenced in the Results section.</p><p>5) Is the Deep Neural Network mainly to be used for incomplete brain datasets? Would be nice if it helped with providing better registration for registration of local structure differences, such as different layers in cortical structures or areas with multiple nuclei in addition to when there is damage to the tissue. Also, the 2-photon image data used for registration does not always provide sufficient morphologic features to identify certain brain structures, such as certain subnuclei in the brainstem and thalamus and to some extent different cortical layers. Basing the training data set on the structures detected using bi-channel procedures on 2-photon data may not best identify certain brain structures.</p><p>6) The DNN structure needs further clarification in terms of input and output. The input size is not specified; is it 2D section or 3D? How many output classes are defined? Does each region get a specific node in the output? It is not clear how the network output is selected. In "Generation of ground-truth training data" section it is mentioned that 18 primary regions were chosen for training set. In inference, the four major sub-regions of hippocampus, CA1, CA2, CA3, and the DG are not in the original 18 regions defined in the training set. The structure of the training set both in the training and test mode should be clearly explained.</p><p>7) It is recommended to report the median values of the comparison of different methods in a separate table rather than the legend of the figure for easier readability.</p><p>8) Variables in Equations 1 and 2 are not fully defined.</p><p>9) Equation 3 only explains the relationship of the final cost function to the Individual cost functions. The individual cost functions are not defined.</p><p>10) The "Marching cubes" algorithm introduced in "Generation of 3D digital map for whole brain" section is not defined. Is it a proposed algorithm by authors or an algorithm used from somewhere else? If latter, the authors should cite the paper.</p></body></sub-article><sub-article article-type="reply" id="sa2"><front-stub><article-id pub-id-type="doi">10.7554/eLife.63455.sa2</article-id><title-group><article-title>Author response</article-title></title-group></front-stub><body><disp-quote content-type="editor-comment"><p>Essential revisions:</p><p>1) Despite the steps being described in detail, parts of the image processing and registration process are difficult to understand, which requires careful revision of the text to clarify the steps in the procedure depicted in Figure 1:</p><p>&#x02013; The down-sampling is straightforward. However, the "dynamic down-sampling ratios applied to the.&#x02026; " is not at all clear. What exactly is being done and what is the purpose? By "dynamic down-sampling ratio" is it meant that instead of the ratio of the original (1:20 downsampling ratio to convert the original to the dimensions of the Allen CCF ) that the ratio might be different at different rostro-caudal levels of the brain? And is this to account for differences in how the brains are processed. For instance, experimental brains might have different dimensions in the x and y planes than the Allen CCF or might be deformed in different ways. And that this process makes those corrections at the appropriate rostro-caudal levels.</p></disp-quote><p>We apologize for the ambiguity brought to the reviewer. In addition to the individual difference, the sample preparation step often causes additional nonuniform deformation of samples. It means that the scaling factors of different parts of the brain are not the same, as compared to Allen CCF template. Such nonuniform deformation is found to be especially obvious along the AP axis and makes the following precise registration a lot more difficult. So at image preprocessing step, we divided the whole brain into a few z sub-stacks (6 in our demonstration) according to several selected landmark planes (7 in our demonstration) and applied appropriate re-sampling ratios, which are different, to them to finely re-adjust the depth of the sub-regions. Through compressing or stretching the depth of the sub-stacks by dynamic re-sampling ratio calculated by corresponding the positions of the landmark planes in Allen CCF3 template and sample data (Materials and methods section), we rectify the nonuniform deformation to restore a uniform rostro-caudal level of brain. This step was verified to be beneficial to the following image registration. Figure 1&#x02014;figure supplement 2 of the revised manuscript shows the improvement of registration accuracy by using our dynamic downsampling preprocessing. We further illustrated this dynamic down-sampling operation in the revised Figure 1 and improved the descriptions in Results and Figure 1&#x02014;figure supplement 1.</p><disp-quote content-type="editor-comment"><p>&#x02013; The next step(s) are very difficult to follow: the "feature based iterative registration of the Allen reference image with pre-processed experimental images". This step involves the application of: 1) phase congruency algorithm to extract high contrast information from both experimental and template brain (CCF?) images and 2) enhanced axial mutual information extracted by grayscale reversal processing. And then somehow a "bi-channel" registration was performed. What is unclear is what channels are used for the "bi-channel" registration. Is a separate channel formed by the combination of the processes of 1) and 2)? Is the original image also combined with these channels? Are these processes applied to the Allen 2-photon images? And then exactly what is registered and how is that done?</p><p>&#x02013; It might be better if the process steps were described separately and succinctly from comments about how this is better than other methods. The description in the Materials and methods section is somewhat better, but still confusing. The figure legend for Figure 1&#x02014;figure supplement 4 comes close to being understood.</p><p>&#x02013; The "bi-channel" process is the most confusing, and since it is part of the acronym should be better explained. In general, it seems straightforward, using not only the original image data but also that data that has been processed to enhance edges and other features. What is confusing is whether you are also using image data from channels that have labeled neurons and axons &#x02013; which is stored in other channels?</p></disp-quote><p>Bi-channel is defined to be distinguished with conventional registration that merely uses the brain background images for registration. The 1<sup>st</sup> primary channel consisted of brain background images of both sample and Allen CCF brains is always included in the registration. The 2<sup>nd</sup> assistant channel consisted of the generated texture and geometry maps of both brains participates the registration procedure with appropriate weighting being applied. Therefore, both primary and assistant channels contain the graphic information from the background images of both sample and Allen CCF brains. Meanwhile, no neurons/axons labelled images are required at registration step. In the revised manuscript, we have further clarified the definitions and implementations of the bi-channel registration in Results and Materials and methods sections. Also, the registration procedure has been better illustrated in the revised Figure 1&#x02014;figure supplement 4.</p><disp-quote content-type="editor-comment"><p>2) Authors propose an algorithm for registration of high-resolution large-scale microscopic images of whole mouse brain datasets. This type of computational processing requires adequate memory and processing power which might not be available in a standard laboratory environment. Authors should elaborate on the computational techniques to address the large dataset handling of the proposed algorithm in regards of memory and the speed. Furthermore, is the proposed algorithm expandable to larger datasets such as super-resolution images or high-resolution whole rat brain datasets specifically in the context of memory and speed?</p></disp-quote><p>We appreciate the reviewer&#x02019;s professional advices, which are indeed helpful to the improvement of our manuscript. We have discussed the computational cost and the possible expansion to larger dataset in the Materials and methods section. A new Supplementary file 1 is also provided to summarize the data size, memory cost and time consumption at different BIRDS stages for processing 180 GB STPT and 320 GB LSFM datasets.</p><disp-quote content-type="editor-comment"><p>3) Authors discuss downsampling the dataset before registration. How does this downsampling affect the results specifically the smoothness of the borders between the regions?</p></disp-quote><p>In consideration of the big saving of computational costs (speed and memory), down-sampling the raw image dataset before registration is a standard operation, which shows minor effect on the smoothness of the borders between regions. This step has been widely used in many established brain registration works such as: ClearMap (Renier et al., 2016), aMAP (Niedworok et al., 2016) and MIRACL (Goubran et al., 2019) with downsampling the raw image data to 25-&#x003bc;m, 12.5-&#x003bc;m and 25-&#x003bc;m voxel size, respectively. In our method, we chose downsampling to 20-&#x003bc;m voxel size, which was within the normal range.</p><disp-quote content-type="editor-comment"><p>4) The tracing of each fluorescently labelled neuronal cell is not clear! The method for tracing is neither explained nor referenced in the Results section.</p></disp-quote><p>The tracing of fluorescently labelled neurons shown in Figure 3 was performed using the filament module of Imaris, which was based on the automatic neuron segmentation aided by human inspection / correction. We have added the descriptions on neuron tracing and counting in the Results and Materials and methods sections.</p><disp-quote content-type="editor-comment"><p>5) Is the Deep Neural Network mainly to be used for incomplete brain datasets? Would be nice if it helped with providing better registration for registration of local structure differences, such as different layers in cortical structures or areas with multiple nuclei in addition to when there is damage to the tissue. Also, the 2-photon image data used for registration does not always provide sufficient morphologic features to identify certain brain structures, such as certain subnuclei in the brainstem and thalamus and to some extent different cortical layers. Basing the training data set on the structures detected using bi-channel procedures on 2-photon data may not best identify certain brain structures.</p></disp-quote><p>We appreciate reviewer&#x02019;s professional question. In our BIRDS pipeline, since the registration step has handled the whole brain datasets, DNN is designed to be used for incomplete brain datasets, which are otherwise difficult for registration. As long as finer labelling data being provided, the DNN itself certainly has the ability to segment finer brain structures, like CA1, CA2, CA3 and DG structures shown in Figure 5&#x02014;figure supplement 4. Since only 2-photon and light-sheet datasets are available to us, it is a pity that we are currently unable to train the network with data containing sufficient morphologic features at certain brain structures, such as certain subnuclei in the brainstem and thalamus. However, it should be noted that BIRDS is a fully open-source approach, thus higher-quality data containing specific regions labelled from various laboratories could be processed using this approach. We also look forward to seeing increasingly stronger segmentation / capabilities shown by BIRDS in future.</p><disp-quote content-type="editor-comment"><p>6) The DNN structure needs further clarification in terms of input and output. The input size is not specified; is it 2D section or 3D? How many output classes are defined? Does each region get a specific node in the output? It is not clear how the network output is selected. In "Generation of ground-truth training data" section it is mentioned that 18 primary regions were chosen for training set. In inference, the four major sub-regions of hippocampus, CA1, CA2, CA3, and the DG are not in the original 18 regions defined in the training set. The structure of the training set both in the training and test mode should be clearly explained.</p></disp-quote><p>We apologize for the confusion to reviewer. The inputs for DNN are 2D image slices. We trained the network twice on dataset of 18 primary brain regions and dataset of 4 small sub-regions in hippocampus separately, to demonstrate network&#x02019;s inference capability on both coarse segmentation of large incomplete brain (Figure 5) and fine segmentation of certain region of interest (Figure 5&#x02014;figure supplement 4). During network training, we defined the segmentation classes the same with the number of regions, which is 19 in Figure 5 (background as one class) and 5 in Figure 5&#x02014;figure supplement 4 (background as one class). Finally, each region was assigned a channel rather than a node for generating the segmented output. In the revised manuscript, we have included a Supplementary file 2 to provide the detailed information of training and test datasets. More DNN information can be found in the revised Materials and methods section and the source code we have provided.</p><disp-quote content-type="editor-comment"><p>7) It is recommended to report the median values of the comparison of different methods in a separate table rather than the legend of the figure for easier readability.</p></disp-quote><p>In response to reviewer&#x02019;s suggestion, we have added a separate Supplementary file 3 to report the median values.</p><disp-quote content-type="editor-comment"><p>8) Variables in Equations 1 and 2 are not fully defined.</p></disp-quote><p>We have fully defined the variables in Equations 1 and 2 (Materials and methods section).</p><disp-quote content-type="editor-comment"><p>9) Equation 3 only explains the relationship of the final cost function to the Individual cost functions. The individual cost functions are not defined.</p></disp-quote><p>We have defined the individual cost functions as new Equation 4 in the Materials and methods section.</p><disp-quote content-type="editor-comment"><p>10) The "Marching cubes" algorithm introduced in "Generation of 3D digital map for whole brain" section is not defined. Is it a proposed algorithm by authors or an algorithm used from somewhere else? If latter, the authors should cite the paper.</p></disp-quote><p>We have cited the paper that reports Marching cubes algorithm in the revised manuscript (Lorensen and Cline, 1987).</p></body></sub-article></article>