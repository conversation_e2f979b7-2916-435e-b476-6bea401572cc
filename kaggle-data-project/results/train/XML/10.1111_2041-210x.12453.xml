<?xml version="1.0" encoding="UTF-8"?><component xmlns="http://www.wiley.com/namespaces/wiley" xmlns:wiley="http://www.wiley.com/namespaces/wiley/wiley" type="serialArticle" version="2.0" xml:id="mee312453" xml:lang="en"><header><publicationMeta level="product"><doi origin="wiley" registered="yes">10.1111/(ISSN)2041-210X</doi><issn type="print">2041-210X</issn><issn type="electronic">2041-210X</issn><idGroup><id type="product" value="MEE3"/></idGroup><titleGroup><title sort="METHODS IN ECOLOGY AND EVOLUTION" type="main">Methods in Ecology and Evolution</title><title type="short">Methods Ecol Evol</title></titleGroup></publicationMeta><publicationMeta level="part" position="30"><doi origin="wiley">10.1111/mee3.2016.7.issue-3</doi><copyright ownership="thirdParty"><i>Methods in Ecology and Evolution</i> © 2016 British Ecological Society</copyright><numberingGroup><numbering type="journalVolume" number="7">7</numbering><numbering type="journalIssue">3</numbering></numberingGroup><coverDate startDate="2016-03">March 2016</coverDate></publicationMeta><publicationMeta level="unit" position="130" status="forIssue" type="article"><doi>10.1111/2041-210X.12453</doi><idGroup><id type="unit" value="MEE312453"/></idGroup><countGroup><count number="11" type="pageTotal"/></countGroup><titleGroup><title type="articleCategory">Research Article</title><title type="tocHeading1">Conservation</title></titleGroup><copyright ownership="thirdParty">© 2015 The Authors. Methods in Ecology and Evolution © 2015 British Ecological Society</copyright><eventGroup><event date="2015-05-02" type="manuscriptReceived"/><event date="2015-07-23" type="manuscriptAccepted"/><event agent="SPS" date="2015-08-07" type="xmlCreated"/><event type="firstOnline" date="2015-09-06"/><event type="publishedOnlineAccepted" date="2015-07-29"/><event type="publishedOnlineEarlyUnpaginated" date="2015-09-06"/><event type="publishedOnlineFinalForm" date="2016-03-14"/><event type="xmlConverted" agent="Converter:WML3G_To_WML3G version:5.7.7 mode:FullText" date="2020-03-09"/></eventGroup><numberingGroup><numbering type="pageFirst">369</numbering><numbering type="pageLast">379</numbering></numberingGroup><correspondenceTo>Correspondence author. E‐mail: <email><EMAIL></email></correspondenceTo><linkGroup><link type="toTypesetVersion" href="file:2041-210X.12453.pdf"/><link type="toAuthorManuscriptVersion" href="file:2041-210X.12453.am.pdf"/></linkGroup></publicationMeta><contentMeta><titleGroup><title type="main">Extracting spatio‐temporal patterns in animal trajectories: an ecological application of sequence analysis methods</title><title type="shortAuthors"><i>J. De Groeve</i> et al.</title></titleGroup><creators><creator affiliationRef="#mee312453-aff-0001 #mee312453-aff-0002" corresponding="yes" creatorRole="author" xml:id="mee312453-cr-0001"><idGroup><id type="orcid" value="http://orcid.org/0000-0002-1274-3237"/></idGroup><personName><givenNames>Johannes</givenNames> <familyName>De Groeve</familyName></personName></creator><creator affiliationRef="#mee312453-aff-0001" creatorRole="author" xml:id="mee312453-cr-0002"><personName><givenNames>Nico</givenNames> <familyName>Van de Weghe</familyName></personName></creator><creator affiliationRef="#mee312453-aff-0002 #mee312453-aff-0003" creatorRole="author" xml:id="mee312453-cr-0003"><personName><givenNames>Nathan</givenNames> <familyName>Ranc</familyName></personName></creator><creator affiliationRef="#mee312453-aff-0001" creatorRole="author" xml:id="mee312453-cr-0004"><personName><givenNames>Tijs</givenNames> <familyName>Neutens</familyName></personName></creator><creator affiliationRef="#mee312453-aff-0004" creatorRole="author" xml:id="mee312453-cr-0005"><personName><givenNames>Lino</givenNames> <familyName>Ometto</familyName></personName></creator><creator affiliationRef="#mee312453-aff-0004" creatorRole="author" xml:id="mee312453-cr-0006"><personName><givenNames>Omar</givenNames> <familyName>Rota‐Stabelli</familyName></personName></creator><creator affiliationRef="#mee312453-aff-0002" creatorRole="author" xml:id="mee312453-cr-0007"><personName><givenNames>Francesca</givenNames> <familyName>Cagnacci</familyName></personName></creator><creator creatorRole="editor" xml:id="mee312453-cr-0008"><personName><givenNames>Stéphane</givenNames> <familyName>Dray</familyName></personName></creator></creators><affiliationGroup><affiliation countryCode="BE" type="organization" xml:id="mee312453-aff-0001"><orgDiv>Department of Geography</orgDiv> <orgName>Ghent University</orgName> <address><street>Krijgslaan 281</street> <postCode>9000</postCode> <city>Gent</city> <country>Belgium</country></address></affiliation><affiliation countryCode="IT" type="organization" xml:id="mee312453-aff-0002"><orgDiv>Department of Biodiversity and Molecular Ecology</orgDiv> <orgDiv>Research and Innovation Centre</orgDiv> <orgName>Fondazione Edmund Mach</orgName> <address><street>Via E. Mach 1</street> <postCode>38010</postCode> <city>San Michele all’ Adige</city> <countryPart>Trentino</countryPart> <country>Italy</country></address></affiliation><affiliation countryCode="US" type="organization" xml:id="mee312453-aff-0003"><orgDiv>Department of Organismic and Evolutionary Biology</orgDiv> <orgName>Harvard University</orgName> <address><street>26 Oxford Street</street> <city>Cambridge</city> <countryPart>MA</countryPart> <postCode>02138</postCode> <country>USA</country></address></affiliation><affiliation countryCode="IT" type="organization" xml:id="mee312453-aff-0004"><orgDiv>Department of Sustainable Agro‐Ecosystems and Bioresources</orgDiv> <orgDiv>Research and Innovation Centre</orgDiv> <orgName>Fondazione Edmund Mach</orgName> <address><street>Via E. Mach 1</street> <postCode>38010</postCode> <city>San Michele all’ Adige</city> <countryPart>Trentino</countryPart> <country>Italy</country></address></affiliation></affiliationGroup><keywordGroup type="author"><keyword xml:id="mee312453-kwd-0001">autocorrelation</keyword><keyword xml:id="mee312453-kwd-0002">distance</keyword><keyword xml:id="mee312453-kwd-0003">exploratory analysis</keyword><keyword xml:id="mee312453-kwd-0004">habitat use</keyword><keyword xml:id="mee312453-kwd-0005">Hamming</keyword><keyword xml:id="mee312453-kwd-0006">roe deer</keyword><keyword xml:id="mee312453-kwd-0007">spatio‐temporal sequences</keyword><keyword xml:id="mee312453-kwd-0008">trees</keyword></keywordGroup><fundingInfo><fundingAgency>MOVE</fundingAgency><fundingNumber>IC0903</fundingNumber></fundingInfo><fundingInfo><fundingAgency fundRefName="Universiteit Gent" funderDoi="10.13039/501100004385">Special Research Fund (BOF) of Ghent University</fundingAgency></fundingInfo><supportingInformation><supportingInfoItem><mediaResource alt="supporting" mimeType="application/msword" href="suppl/mee312453-sup-0001-AppendixS1.docx"/><caption><p><b>Appendix S1.</b> Simulated arenas.</p><p><b>Figure S1.1.</b> Nine square gridded arenas, each composed of 10 000 squared cells of 100 × 100 m, with varying proportions of open (light colors) and closed (dark colors) habitats (10–90%) at high (blue) and low elevation (red/orange).</p></caption></supportingInfoItem><supportingInfoItem><mediaResource alt="supporting" mimeType="application/msword" href="suppl/mee312453-sup-0002-AppendixS2.docx"/><caption><b>Appendix S2.</b> The movement model.</caption></supportingInfoItem><supportingInfoItem><mediaResource alt="supporting" mimeType="application/msword" href="suppl/mee312453-sup-0003-AppendixS3.docx"/><caption><p><b>Appendix S3.</b> Substitution matrix to account for spatial correlation.</p><p><b>Figure S3.1.</b> Simulated dispersed (a), random (b) and clustered (c) spatial distribution of habitat patches based on the shape of the study area.</p><p><b>Figure S3.2.</b> Spatial correlation (<i>Z</i>‐scores) in relation to habitat availability (<i>x</i>‐axis, % open habitat) for HCxHC (red), HOxHO (yellow), LCxLC (blue), LOxLO (light blue).</p><p><b>Table S3.1</b> Join‐Count Statistics for simulated scenarios (a, b, c).</p><p><b>Table S3.2</b> Substitution matrices for simulated scenarios (a, b, c), expressed as dissimilarity measured using Join‐Counts, i.e. the proportion of the total number of adjacent polygons where the specified habitat neighbour relationship is not observed.</p><p><b>Table S3.3</b> Join‐Count Statistics for 9 arenas with variable proportions of open and closed habitats (expressed as the percentage of open habitat). See table S3.1 for explanation of codes.</p><p><b>Table S3.4</b> Substitution matrices for the nine arenas.</p></caption></supportingInfoItem><supportingInfoItem><mediaResource alt="supporting" mimeType="application/msword" href="suppl/mee312453-sup-0004-AppendixS4.docx"/><caption><p><b>Appendix S4.</b> Trees.</p><p><b>Figure S4.1–4.9.</b> Simulated and roe deer bimonthly sequences of habitat use classes (right portion of the panel; daily scale reported bottom‐right), and resulting dissimilarity tree, based on the Hamming distance algorithm (left portion of the panel).</p></caption></supportingInfoItem><supportingInfoItem><mediaResource alt="supporting" mimeType="application/msword" href="suppl/mee312453-sup-0005-AppendixS5.docx"/><caption><p><b>Appendix S5.</b> Clusters of real bimonthly sequences.</p><p><b>Figure S5.1</b> Clusters of real bimonthly sequences with similar patterns of sequential habitat use, extracted from the corresponding dissimilarity trees.</p></caption></supportingInfoItem><supportingInfoItem><mediaResource alt="supporting" mimeType="text/r" href="suppl/mee312453-sup-0006-AppendixS6.r"/><caption><b>Appendix S6.</b> R‐script of the complete analysis.</caption></supportingInfoItem></supportingInformation><abstractGroup><abstract type="main" xml:id="mee312453-abs-0001"><title type="main">Summary</title><p><list formatted="paragraph" style="1" xml:id="mee312453-list-0001"><listItem><p>Digital tracking technologies have considerably increased the amount and quality of animal trajectories, enabling the study of habitat use and habitat selection at a fine spatial and temporal scale. However, current approaches do not yet explicitly account for a key aspect of habitat use, namely the sequential variation in the use of different habitat features.</p></listItem><listItem><p>To overcome this limitation, we propose a tree‐based approach that makes use of sequence analysis methods, derived from molecular biology, to explore and identify ecologically relevant sequential patterns in habitat use by animals. We applied this approach to ecological data consisting of simulated and real trajectories from a roe deer population (<i>Capreolus capreolus</i>), expressed as ordered sequences of habitat use.</p></listItem><listItem><p>We show that our approach effectively captured spatio‐temporal patterns of sequential habitat use by roe deer. In our case study, individual sequences were clustered according to the sequential use of the elevation gradient (first order) and of open/closed habitats (second order). We provided evidence for several behavioural processes, such as migration and daily alternating habitat use. Some unexpected patterns, such as homogeneous sequences of use of open habitat, could also be identified.</p></listItem><listItem><p>Our findings advocate the importance of dealing with the sequential nature of movement data. Approaches based on sequence analysis methods are particularly useful and effective since they allow exploring temporal patterns of habitat use in a synthetic and visually captive manner. The proposed approach represents a useful and effective way to classify individual movement behaviour across populations and species. Ultimately, this method can be applied to explore the temporal scale of ecological processes based on movement.</p></listItem></list></p></abstract></abstractGroup></contentMeta></header><body sectionsNumbered="no" xml:id="mee312453-body-0001"><section xml:id="mee312453-sec-0001"><title type="main">Introduction</title><p>Recent advances in digital tracking technology and increased availability of high‐resolution environmental data by remote sensing have facilitated the collection of spatio‐temporal series of animal‐borne data (Cagnacci <i>et al</i>. <link href="#mee312453-bib-0007"/>). Application of satellite navigation technology (e.g. Global Positioning System, GPS) to individual animals allows recording temporal sequences of animal locations at an unprecedented spatio‐temporal resolution. Moreover, by projecting these locations onto spatial layers, including satellite images, it is possible to obtain robust and standardized information about the habitat of these animals (Urbano <i>et al</i>. <link href="#mee312453-bib-0029"/>).</p><p>At present, an array of both exploratory and inferential methods is available to the analyst to investigate the relation between animal movement and the use of habitat. Exploratory methods apply multivariate analysis techniques (e.g. general niche‐environment system factor analysis, GNESFA, the K‐select analysis, (canonical) outlying mean index analysis; see R‐package AdehabitatHS of Calenge (<link href="#mee312453-bib-0009"/>) for an overview) to identify relevant variables describing the habitat (or the realized niche) of a population. Similarly, decision tree learning methods, such as random forest and CART modelling, are data mining techniques that present decision rules for classifying a set of data based on associated explanatory variables (see R‐package rpart of Therneau, Atkinson &amp; Ripley (<link href="#mee312453-bib-0026"/>)). Conversely, inferential methods mainly consist of a variety of regression models testing the disproportion between used and available habitat units (i.e. habitat selection; Johnson <link href="#mee312453-bib-0019"/>), such as resource selection functions, RSF (Boyce <i>et al</i>. <link href="#mee312453-bib-0005"/>) and step selection functions, SSF (Fortin <i>et al</i>. <link href="#mee312453-bib-0014"/>). In essence, exploratory methods offer a description of animals’ habitat, whereas inferential methods allow to test specific hypotheses (Calenge &amp; Basille <link href="#mee312453-bib-0010"/>). In this sense, the first can be used to select explanatory variables that are relevant for the application of the latter (e.g. Calenge <link href="#mee312453-bib-0009"/>; Wittemyer <i>et al</i>. <link href="#mee312453-bib-0031"/>; Dray, Royer‐Carenzi &amp; Calenge <link href="#mee312453-bib-0013"/>).</p><p>Despite the proliferation of exploratory methods, current approaches rarely evaluate the sequential use of habitats by animals, that is the sequence of locations (trajectory) vs. the underlying ordered pattern of habitat use. In movement ecology, temporal patterns have been addressed, for example, by exploration of temporal autocorrelation of movement parameters (Wittemyer <i>et al</i>. <link href="#mee312453-bib-0031"/>; Dray, Royer‐Carenzi &amp; Calenge <link href="#mee312453-bib-0013"/>). We wish to draw attention on the meaningfulness of temporal patterns when describing habitat choices. For example, the same proportion of habitat use in a certain time interval may correspond to very different sequential patterns. An animal may continuously use a single habitat type, then switch to another, or, in contrast, alternate the use of both. Such spatio‐temporal patterns may correspond to alternative space‐use tactics, and find a deep ecological significance. Currently, insights in the spatial patterns of use of multiple habitat‐related variables are easy to obtain, for example using suitability maps (e.g. Calenge <link href="#mee312453-bib-0009"/>); however, very few methods provide insights into spatio‐temporal patterns combined. An interesting publication in that direction comes from Benhamou &amp; Riotte‐Lambert (<link href="#mee312453-bib-0003"/>) presenting a framework using movement‐based kernel density estimation (utilization distribution) and computation of residence time combined to explore the areas of intensive use. Here, we are interested into methods to visually explore the sequential and thus temporal structure of habitat use.</p><p>In other research areas, such as geo‐visual analytics, important progress has been made in terms of visually exploring sequential data at variable spatio‐temporal scales (Andrienko, Andrienko &amp; Heurich <link href="#mee312453-bib-0002"/>). Buchin, Dodge &amp; Speckmann (<link href="#mee312453-bib-0006"/>), for example, developed a geometric algorithm for trajectory clustering that takes into account environmental context parameters such as temperature and habitat type. In sociology, on the other hand, the link between sequential order of human behaviour and space use has been investigated using sequence analysis methods (SAMs) (Abbott <link href="#mee312453-bib-0001"/>). This technique is principally used in the field of bioinformatics to evaluate the degree of similarity among DNA or protein sequences, but has also been applied successfully in transportation science (Wilson <link href="#mee312453-bib-0030"/>), tourism research (Shoval &amp; Isaacson <link href="#mee312453-bib-0025"/>) and indoor navigation (Delafontaine <i>et al</i>. <link href="#mee312453-bib-0012"/>).</p><p>Sequence analysis methods to our knowledge has never been applied to explore spatio‐temporal patterns in sequential habitat use by animals. The essence of this approach is the possibility to ‘extract’ ordered sequences of habitat classes occupied along trajectories by means of clusters, which can be conveniently visualized in trees and validated by measures of statistical reliability. Moreover, SAM allows to deal with two common issues of GPS‐based location data sets: missing data points (i.e. acquisition failures by GPS sensors; Frair <i>et al</i>. <link href="#mee312453-bib-0015"/>) and spatial correlation (Dray, Royer‐Carenzi &amp; Calenge <link href="#mee312453-bib-0013"/>).</p><p>In this study, we aimed to evaluate the applicability of SAM to movement ecology data for exploratory purposes by analysing both simulated trajectories and time‐stamped locations of individually tracked roe deer (<i>Capreolus capreolus</i>) from a partially migratory alpine population. The analysis consisted of several steps (Fig. <link href="#mee312453-fig-0001"/>). We first produced a classification tree based on bimonthly sequences of habitat use by individual roe deer. We used this first exploratory classification to hypothesize potential patterns of sequential habitat use. Then, we produced simulated trajectories with those patterns, at different proportion of habitat availability, and classified them in trees. We then re‐added the real trajectories to simulation trees, while accounting for their relative proportion of habitat availability. Finally, we evaluated the biological relevance of such classification on the basis of ecological predictions. This way, we explored spatio‐temporal patterns of real trajectories and evaluated them in a simulated experimental setting.</p><figure xml:id="mee312453-fig-0001"><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee312453:mee312453-fig-0001"/><mediaResource alt="image" href="graphic/mee312453-fig-0001-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Flowchart of the complete process to explore spatio‐temporal habitat use patterns of real trajectories by sequence similarity analysis. In essence, based on simple classification trees of real trajectories (a), we simulated trajectories with a priori defined patterns of sequential habitat use (b), which in turn were used to produce classification trees accounting for spatial correlation, at different proportion of habitat availability (b/c). We then introduced the real trajectories (c), accounting for the relative proportion of habitat availability, and assessed their classification into tree clusters resulting from the simulation exercise (c/d).</caption></figure></section><section xml:id="mee312453-sec-0002"><title type="main">Materials and methods</title><section xml:id="mee312453-sec-0003"><title type="main">Study area and real trajectories</title><p>The studied animal population consisted of 26 European roe deer equipped with a GPS collar (GPS‐Plus D, Vectronic Aerospace GmbH), of which 16 were females and 10 males. Six of them were collared as fawns (i.e. less than 1 year old; 2 females and 4 males), one as yearling (i.e. one female between 1 and 2 year old), while all others were collared as adults. Figure <link href="#mee312453-fig-0002"/> pictures the cumulative sum of the 90% fixed‐kernel home ranges (KDE) with reference smoothing parameter (href; Worton <link href="#mee312453-bib-0032"/>) of individual roe deer using Home Range Extension (Rodgers &amp; Carr <link href="#mee312453-bib-0023"/>). This area extends across the Monte Bondone–Monte Stivo range, west of Trento and Adige valley and east of Valle dei Laghi, in north‐east Italy (46°4′N, 11°7′E). Elevation ranges between 200 and 2300 m above sea level (m a.s.l.). Along this altitudinal gradient, climate is extremely varied, ranging from semi‐Mediterranean and temperate (&lt;1000 m a.s.l. defined as ‘low elevation’) to semi‐alpine and alpine (&gt;1000 m a.s.l. defined as ‘high elevation’) conditions. The study area is mainly covered by broad‐leaved, coniferous and mixed forest (defined as ‘closed habitat’, representing 50% and 75% of high and low elevations, respectively), alternated by pastures (defined as ‘open habitat’, representing 50% and 25% of high and low elevations, respectively). Relevantly, the high‐elevation range is mainly constituted by protected land, whereas the low elevation is not and is characterized by more anthropic land use.</p><figure xml:id="mee312453-fig-0002"><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee312453:mee312453-fig-0002"/><mediaResource alt="image" href="graphic/mee312453-fig-0002-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>The study area, indicated by the darker irregular shaped polygons, was computed as the cumulative sum of home ranges of 26 individually marked roe deer (kernel density estimator, 90% polygons). For codes of habitat classes, see Table <link href="#mee312453-tbl-0001"/>. N: urban areas.</caption></figure><p>The sampling period spanned from 2005 to 2008, during which GPS collars yielded a total of 54 845 time‐stamped locations. The interval and duration of sampling were different among individuals, depending on date of capture and battery exhaustion. Locations were acquired at a pre‐determined temporal interval Δ<i>t</i> of 4 h at fixed time stamps (0, 4, 8, 12, 16 and 20 h) except for cold winter months (January and February), when Δ<i>t</i> was 6 h (0, 6, 12 and 18 h) in order to save battery. We linked GPS locations to two geographical parameters retrieved from remote sensing sources: habitat openness [EEA‐Corine Landcover (CLC) 2006 IV, European Environment Agency, EEA (<link href="#mee312453-bib-0504"/>), resolution = 100 m] and elevation [NASA‐ASTER GDEM, Ministry of Economy, Trade, and Industry of Japan, METI, National Aeronautics and Space Administration of the United States, NASA (<link href="#mee312453-bib-0505"/>) = 30 m]. We combined and reclassified environmental parameters into four classes: high closed (HC), high open (HO), low closed (LC), low open (LO) (Table <link href="#mee312453-tbl-0001"/> and Fig. <link href="#mee312453-fig-0002"/>). In this application, we used four classes to reduce the complexity of data analysis for demonstration. On the one side, the classes correspond to well‐defined vegetation successional types in the alpine habitat of this population (Cagnacci <i>et al</i>. <link href="#mee312453-bib-0008"/>); on the other side, they are meaningful for roe deer habitat use traits, since they are known to prefer ecotonal habitats and forest edges (Tufto, Andersen &amp; Linnell <link href="#mee312453-bib-0027"/>).</p><tabular copyright="John Wiley &amp; Sons, Ltd" xml:id="mee312453-tbl-0001"><title type="main">Reclassification of the environmental parameters elevation and habitat openness resulting in combined classes (coded as HC, HO, LC and LO)</title><table colsep="0" frame="topbot" pgwide="1" rowsep="0"><tgroup cols="3"><colspec colname="col1" colnum="1" colsep="0"/><colspec colname="col2" colnum="2" colsep="0"/><colspec colname="col3" colnum="3" colsep="0"/><thead valign="top"><row rowsep="1"><entry align="left" colname="col1">Elevation</entry><entry align="left" colname="col2">Habitat openness </entry><entry align="left" colname="col3">Habitat use classes</entry></row></thead><tbody><row><entry align="left" colname="col1">Low (&lt;1000 m)</entry><entry align="left" colname="col2">Closed<link href="#mee312453-note-0001"/></entry><entry align="left" colname="col3">LC</entry></row><row><entry align="left" colname="col1">Low</entry><entry align="left" colname="col2">Open<link href="#mee312453-note-0002"/></entry><entry align="left" colname="col3">LO</entry></row><row><entry align="left" colname="col1">High (&gt;1000 m)</entry><entry align="left" colname="col2">Closed </entry><entry align="left" colname="col3">HC</entry></row><row><entry align="left" colname="col1">High</entry><entry align="left" colname="col2">Open</entry><entry align="left" colname="col3">HO</entry></row></tbody></tgroup></table><noteGroup xml:id="mee312453-ntgp-0001"><note xml:id="mee312453-note-0001"><p>Forest: Corine Landcover classes 311, 312, 313, 323, 324.</p></note><note xml:id="mee312453-note-0002"><p>No Forest: All other Corine Landcover classes, except inland water.</p></note></noteGroup></tabular><p>Then, we recoded sequences of locations into sequences of the environmental classes above, adding asterisks to account for missing locations due to acquisition failure. More precisely, the input sequences for SAM describe the habitat use by individual animals at regular time stamps (0, 4, 8, 12, 16 and 20 h) over a period of 2 months. While SAM is able to deal with missing data, if they are too frequent, they can over‐fragment the sequence and thus bias the similarity measurement. To avoid a bias in the downstream analyses, we therefore excluded bimonthly sequences with more than 40% of missing data. After removal, the input file consisted of a total of 111 sequences (min 21, max 24 per bimonthly period), which were reclassified as belonging to summer (May–October) or winter (November–April) season, based on snowfalls and typical alpine climate (Ramanzin, Sturaro &amp; Zanon <link href="#mee312453-bib-0022"/>; Cagnacci <i>et al</i>. <link href="#mee312453-bib-0008"/>). Only seven sequences could be retained for January–February; therefore, we excluded the whole period from further analysis. Finally, sequences were associated with sex, age (fawn, yearling or adult) and migration occurrence (migrant, non‐migrant, no data), of each individual. We obtained a first visualization of the habitat use patterns represented in our sample by creating a simple classification tree of the 111 sequences (Fig. <link href="#mee312453-fig-0001"/>a). This classification tree was based on Hamming distance (Gabadinho <i>et al</i>. <link href="#mee312453-bib-0016"/>; see below for more details), without accounting for spatial correlation, nor habitat availability. Thus, this first classification tree does not allow to objectively classify the sequential use of habitat types, but can be used to build hypotheses for expected models of habitat use.</p></section><section xml:id="mee312453-sec-0004"><title type="main">Simulation arenas and simulated trajectories</title><p>We simulated nine squared gridded arenas, each composed of 10 000 squared cells of 100 × 100 m mirroring the availability of environmental classes present in the real settings (Fig. <link href="#mee312453-fig-0001"/>b, Appendix S1, Supporting Information). Since elevation classes are highly clustered in our study area (see Fig. <link href="#mee312453-fig-0002"/>), we split each arena into two equal parts, corresponding to high and low elevations. We obtained the final four categories by randomly assigning all cells to either open or closed habitats, with varying proportions from 10 to 90% in each arena. Thus, simulated landscapes covered all possible habitat prevalence, which may occur within individual home ranges in this specific study area.</p><p>Within each arena, we generated simulated sequential habitat use using a simple spatially explicit stochastic movement model (Fig. <link href="#mee312453-fig-0001"/>b, grey box ‘MM’; Appendix S2). For the simulated trajectories, we chose 4 different patterns of habitat selection, based on preliminary observations on the classification tree of real trajectories (Fig. <link href="#mee312453-fig-0001"/>a), and previous knowledge on roe deer ecology. We thus distinguished homogeneous use of closed or open habitats, and random and alternating use (i.e. day–night patterns) of open and closed habitats. The random pattern represented the ‘control’ in our simulated experimental settings. The homogeneous closed and the alternation between closed and open were the expected patterns according to roe deer ecology and specifically the known preference for forest and ecotonal habitats (Tufto, Andersen &amp; Linnell <link href="#mee312453-bib-0027"/>). Homogeneous open represented the alternative hypothesis. We simulated 100 trajectory replicates for each of the nine arenas and four behaviours of habitat selection (total of 3600 sequences, Fig. <link href="#mee312453-fig-0001"/>b, grey box ‘simulated sequences’). Because sequential use of elevation was strongly dependent on the release location of the simulated agents, each of the 100th set of simulations had the same seed random locations across arenas and behaviours (i.e. trajectories had the same seed in groups of 36). We then trimmed the simulated trajectories to match the length of the real bimonthly roe deer trajectories, that is 366 steps. Finally, simulated sequences of habitat use were extracted from these calibrated trajectories.</p></section><section xml:id="mee312453-sec-0005"><title type="main">General procedure of sequence similarity analysis for calculation of dissimilarity trees</title><p>Sequence analysis methods are based on sequence similarity measures that are used to identify groups of sequences showing similar behaviour. The input of such analysis always relies on a <i>dissimilarity matrix</i>, which provides the dissimilarity, or ‘distance’ among all possible pairs of sequences. Among the available algorithms, we chose Hamming distance (HD) to ascertain the dissimilarity matrix, as it is considered the most suitable for sequences with a temporal dimension. HD relies solely on two operations: identity and substitution, and in fact, it computes the minimum number of substitutions to equate a number of sequences of equal length (Gabadinho <i>et al</i>. <link href="#mee312453-bib-0016"/>). In a more optimized HD, also weights can be assigned to substitutions; that is, HD computation can be based on a <i>substitution weight matrix</i>. Figure <link href="#mee312453-fig-0003"/> gives a conceptual example of two alternative HD of the character strings ‘Kapreolo’ and ‘Capriolo’, respectively, the word for roe deer in Esperanto and Italian. Both distinguish six identities and two substitutions, but differ from each other in weights assigned to substitutions. The substitution between the letters K and C gets a lower weight (i.e. probability) in ‘a’ (substitution score = 0·4, Fig. <link href="#mee312453-fig-0003"/>a) than in ‘b’ (substitution score = 1, Fig. <link href="#mee312453-fig-0003"/>b), since the former HD takes into account the phonetic similarity. Consequently, the total dissimilarity in ‘a’ will be lower than in ‘b’.</p><figure xml:id="mee312453-fig-0003"><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee312453:mee312453-fig-0003"/><mediaResource alt="image" href="graphic/mee312453-fig-0003-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Two alternative computations of the Hamming distance, based on substitution weights given to each operation performed to equate the sequences (reported under each character). Identities are in italic and substitutions bolt.</caption></figure><p>In our case, the dissimilarity matrix computed by the HD algorithm was based on substitution weights for all combinations of habitat classes, and constrained to their availability and distribution, which together determine the patterns of spatial correlation. Therefore, <i>we derived the substitution weights from spatial correlation</i> of habitat classes in the simulated arenas (Fig. <link href="#mee312453-fig-0001"/>b, grey box ‘substitution weight matrix’, see Appendix S3 for computation and simulations of the substitution weights).</p><p>The successive step in data analysis is using the HD dissimilarity matrix to calculate a dissimilarity tree. Here, we used Ward's method (Fig. <link href="#mee312453-fig-0001"/>b/c, HCW), the most common hierarchical agglomerative cluster procedure (Gabadinho <i>et al</i>. <link href="#mee312453-bib-0016"/>). The resulting trees are a representation of the dissimilarity among the habitat use sequences of animals. The distance, or ‘branch length’, between leaves (which stay for individuals) and nodes (which stay for groups of individuals) can be used as a proxy for the dissimilarity between the portions of the tree: the longer the distance, the higher the dissimilarity.</p><p>The following step is the identification of ‘clusters’, or portions of the tree indicating association between sequences. This is the most important step to use trees for exploratory purposes and the subsequent interpretation of results. We determined the number of clusters present in a tree by a cut‐off distance, based on a repeatable bootstrapping procedure (Fig. <link href="#mee312453-fig-0001"/>b/c, <i>B</i><sub>J</sub>). For this application, we performed 1000 iterations and calculated the Jaccard bootstrapping index (i.e. bootmean; Hennig <link href="#mee312453-bib-0017"/>) for a cut‐off distance that separated from two to twenty clusters. This index compares the similarities of the original clusters to the most similar clusters in bootstrapped data sets. The cut‐off distance was determined as the maximum number of clusters where the median bootmean is higher than 0·9, a value corresponding to highly stable clusters. This threshold is conservative in a sense that normally values above 0·75 correspond to robust classification.</p></section><section xml:id="mee312453-sec-0006"><title type="main">Determining classification trees of simulated trajectories</title><p>Using this procedure, we computed a dissimilarity tree and identified clusters for each simulated landscape by using the 400 simulated sequences (100 replicates × 4 patterns of sequential habitat use, Fig. <link href="#mee312453-fig-0001"/>c). To account for spatial correlation of the four habitat classes, we recomputed the substitution matrix for each landscape. In fact, although all grid cells were assigned to habitat classes (open/closed) at random, this still corresponded to different spatial correlation structures across arenas, an effect of the different proportion of classes. Then, we investigated the sequence composition of individual clusters distinguished by the classification trees to assess the liability of the method to <i>correctly group sequences of different sequential behaviours</i>. This also allowed us to identify the name of each cluster.</p></section><section xml:id="mee312453-sec-0007"><title type="main">Determining spatio‐temporal patterns of real trajectories</title><p>In the next step, we introduced the real trajectories into one of the classification trees (Fig. <link href="#mee312453-fig-0001"/>c), according to the relative proportion of habitat availability as follows. For each real bimonthly sequence, we measured the availability of open/closed habitat within the corresponding bimonthly home range. We then associated them to the simulated trajectories referring to the arena with the same habitat proportion. For example, if an individual bimonthly home range showed an open habitat availability between 45 and 55%, the corresponding sequences were associated with the simulated sequences originated from the 50% arena. Then, each tree was recalculated for the combined set of sequences (simulated and corresponding real sequences). Using this approach, simulation sequences could be used as a guide for classification of real sequences to their most similar sequential behavioural group (Fig. <link href="#mee312453-fig-0001"/>c,d).</p></section><section xml:id="mee312453-sec-0008"><title type="main">Expected spatio‐temporal patterns of real trajectories</title><p>Based on previous knowledge on roe deer ecology, and the individual descriptive variables, we formulated predictions of tree clustering. This was the core of our study, to assess the meaningfulness of SAM for exploration of spatio‐temporal sequences of ecological data. In roe deer populations of northern and alpine environments, some individuals reach higher elevations in summer, when habitat suitability increases, and return at lower elevation in rigid winter conditions. Other individuals, instead, occupy the same low‐elevation range all year round (Ramanzin, Sturaro &amp; Zanon <link href="#mee312453-bib-0022"/>; Cagnacci <i>et al</i>. <link href="#mee312453-bib-0008"/>). This phenomenon is known as partial migration. On these premises, we expected individuals to classify into two main clusters according to the use of elevation: animals with a constant use of the same elevation range (winter and summer sequences) and animals with a seasonal shift in elevation range associated with migration (winter sequences separated from summer sequences) (P1a). Roe deer reproductive season is concentrated in summer, when both males and females exploit the best environmental conditions to meet the high energetic demand of mating and giving birth (Hewison, Vincent &amp; Reby <link href="#mee312453-bib-0018"/>). If migration is linked to habitat quality, we expect both sexes to show similar patterns of migration and thus of elevation range use (P1b, but see Ramanzin, Sturaro &amp; Zanon <link href="#mee312453-bib-0022"/>). Likewise, since fawns are not yet engaged in reproduction, we may expect a lower rate of migrating individuals (P1c). Alternatively, they may follow the mother in the migrating movements. Predictions on sequential use of open and closed habitats are less straightforward. Roe deer are known to prefer habitats providing cover and protection, especially intermediate stages of forest succession and ecotonal habitats (e.g. Tufto, Andersen &amp; Linnell <link href="#mee312453-bib-0027"/>). Therefore, we predict that animals would show a sequential use of habitats different from random (P2a). In particular, we predict a separation between animals using only closed habitat and those showing a combined use of closed and open habitats (P2b). When animals use both habitats, we expect open habitats to be used mainly during night, due to the anti‐disturbance and anti‐predatory behaviour of roe deer, translating in an alternating sequential use of open and closed (P2c) (Saïd &amp; Servanty <link href="#mee312453-bib-0024"/>). For similar reasons, a constant use of open habitat is instead less likely (P2d).</p></section></section><section xml:id="mee312453-sec-0009"><title type="main">Classification trees of simulated trajectories</title><p>The application of HD algorithm to simulated bimonthly sequences generated 9 trees, each corresponding to landscapes with different habitat availabilities (i.e. T<sub>10</sub>–T<sub>90</sub>; see Fig. <link href="#mee312453-fig-0001"/>c and Appendix S4 for all trees). In all trees, the topological relationships between tree branches indicated two main orders of classification: first‐order clusters, splitting the sequences into two groups according to the preferential use of different elevations (C<sub>1</sub> and C<sub>2</sub>; e.g. Fig. <link href="#mee312453-fig-0001"/>c), and several second‐order clusters separating animals with different sequential use of open and closed habitats (C, R, A, O, U; see text below, Fig. <link href="#mee312453-fig-0001"/>c and Appendix S4: second‐order clusters are distinguished by coloured branches).</p><p>At first order, all trees showed a significant separation (Jaccard bootstrapping index <i>B</i><sub>J </sub>&gt; 0·95) between high‐ and low‐ elevation trajectories, thus defining a ‘high‐elevation’ cluster (C<sub>1</sub>) and a ‘low‐elevation’ cluster (C<sub>2</sub>). At second order, our defined bootstrap threshold (i.e. <i>B</i><sub>J,median </sub>&gt; 0·9) identified 6–8 main clusters (T<sub>10</sub>–T<sub>30</sub> and T<sub>70</sub>–T<sub>90</sub> = 6 clusters, with 40–80 sequences in each cluster; T<sub>40</sub>–T<sub>60 </sub>= 8 clusters, with about 40 sequences in each) and 3–7 small clusters (with less than 10 sequences each). Separation of the main second‐order clusters was significant (most clusters <i>B</i><sub>J </sub>&gt; 0·95), whereas for smaller clusters, it was not (most clusters <i>B</i><sub>J </sub>&lt; 0·75). For both high and low elevations, the main second‐order clusters corresponded to different patterns of sequential open/closed habitat use. The classification of sequences in such clusters was highly dependent on habitat availability in arenas. For trees deriving from arenas with similar open/closed habitat proportion (40–60%: T<sub>40</sub>, T<sub>50,</sub> T<sub>60</sub>, Fig. <link href="#mee312453-fig-0001"/>c, Figs S4.4–S4.6 in Appendix S4), we distinguished all four simulated sequential behaviours: homogeneous closed (C; brown and dark blue branches), random (R; red and cyan branches), alternating (A; orange and blue branches) and homogeneous open (O; yellow and light blue branches). In this case, 99% of sequences in each cluster was of the same sequential behaviour. For example, all sequences in the blue cluster of Fig. <link href="#mee312453-fig-0001"/>c are homogeneous closed. Conversely, for landscapes where habitats were disproportionally available (open&lt;30% or open&gt;70%: T<sub>10,</sub> T<sub>20</sub>, T<sub>30</sub> and T<sub>70,</sub> T<sub>80</sub>, T<sub>90</sub>, Fig. S4.1–S4.3, S4.7–S4.9 in Appendix S4), random sequences clustered with homogeneous sequences of the dominant habitat (C with R: brown and dark blue branches; O with R: light blue and yellow branches). The random sequential use of habitat according to availability (i.e. the control case) was therefore effectively represented by a separated random cluster only when sequences were not trivial (e.g. T<sub>10</sub> and T<sub>90</sub> obviously led to homogeneous sequences ‘at random’).</p><p>Finally, small clusters corresponded to trajectories indicating a mixed use of high and low elevations, for a specific pattern of sequential use of open and closed habitats (e.g. mixed‐alternating). Alternatively, small clusters were undefined (U) due to a too small number of sequences (&lt;5 sequences) and can be considered as outliers. Both mixed and undefined clusters were coloured grey in trees (Fig. <link href="#mee312453-fig-0001"/>c, Appendix S4).</p></section><section xml:id="mee312453-sec-0010"><title type="main">Spatio‐temporal patterns of real trajectories and discussion of the study case</title><section xml:id="mee312453-sec-0011"><title type="main">Sequential habitat use patterns</title><p>We used the trees based on mixed simulated and real trajectories to assess the classification of real trajectories according to sequential habitat use. To visualize how real trajectories were classified, we extracted (i.e. pruning) the real sequences from nine different trees (Figs <link href="#mee312453-fig-0001"/>d and <link href="#mee312453-fig-0004"/>, see also Appendix S5 for a different visualization).</p><figure xml:id="mee312453-fig-0004"><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee312453:mee312453-fig-0004"/><mediaResource alt="image" href="graphic/mee312453-fig-0004-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Roe deer bimonthly sequences of habitat use classes (right portion of the nine panels; daily scale reported bottom right), and resulting dissimilarity trees, based on the Hamming distance algorithm (left portion of the panels). The real sequences were extracted (pruned) from their corresponding simulated tree from which sequences were derived from a trajectory running over nine different arenas with varying habitat proportions (10–90%). Tree ‘leaves’ represent a real sequence, while ‘nodes’ indicate their clustering. Branch lengths (distance between leaves, and first common node: bottom left for the scale) indicate the dissimilarity between individual sequences. The split into two main clusters define first‐order clusters separation, based on use of elevation classes (high, C<sub>1</sub>; low, C<sub>2</sub>). Second‐order clusters are based on the use of open/closed habitats and distinguish homogeneous closed (C, brown and dark blue branches), random (R, red and cyan branches), alternating (A, orange and blue branches) and homogeneous open (O, yellow and light blue branches). Grey branches (U) are sequences with undefined classification or clusters with mixed sequences of high and low. The id gives the animal code and season (summer, S; winter, W). Variables season, age, sex and migration are represented as colour‐coded bars between trees and sequences (see legend for meaning of the colours).</caption></figure><p>The first‐order clusters C<sub>1</sub> and C<sub>2</sub> distinguish very well high‐ from low‐elevation sequences (Fig. <link href="#mee312453-fig-0004"/>, Appendix S5 right vs. left panels).</p><p>More interesting is the classification of sequences in different sequential patterns of open/closed habitat use (second order). Within our roe deer population, we found evidence of the four different patterns of open/closed sequential use that have been addressed in our simulations. Specifically, 98 sequences out of 111 were classified according to those patterns (91·5%). Moreover, some interesting differences emerged in sequential use of open/closed habitats for different elevations (Fig. <link href="#mee312453-fig-0004"/>, Appendix S5). At high elevation, half of the sequences have an alternating (C<sub>1</sub>A: 32%, 18 sequences) or homogeneous closed pattern (C<sub>1</sub>C: 25%, 14 sequences), whereas only 11% of the sequences are random (C<sub>1</sub>R: 6 sequences). At low elevation, 63% of the sequences are homogeneous closed (C<sub>2</sub>C: 41%, 22 sequences) or random (C<sub>1</sub>R: 22%, 12 sequences), and conversely alternating sequences are uncommon (C<sub>1</sub>A: 13%, 7 sequences). Surprisingly, both at high and low elevations, there are also homogeneous open sequences (C<sub>1</sub>O: 18%, 10 sequences; C<sub>2</sub>O: 14%, 8 sequences). At the study area scale, different sequential patterns of open/closed habitat use according to elevation are possibly linked to those habitats availability in the study area (Fig. <link href="#mee312453-fig-0002"/>: open more available at high elevation). Indeed, individual habitat use sequences are obviously related to the availability of habitat classes within the home range (Fig. <link href="#mee312453-fig-0004"/>), in particular sequences classified as homogeneous sequences (C and O) derived from home ranges with a large proportion of one specific habitat type (Fig. <link href="#mee312453-fig-0004"/>.1–<link href="#mee312453-fig-0004"/>.3, &lt;30% open; Fig. <link href="#mee312453-fig-0004"/>.7–<link href="#mee312453-fig-0004"/>.9, &gt;70% open). Instead, heterogeneous sequences showed an alternating or random pattern (A and R) and derived from home ranges where both habitat types were available (Fig. <link href="#mee312453-fig-0004"/>.4–<link href="#mee312453-fig-0004"/>.6, &gt;40% open and &lt;60% open). Interestingly, though, both homogeneous and heterogeneous trajectories lay next to each other in the study area (Fig. <link href="#mee312453-fig-0005"/>). Also, several sequences with the same home range availability were assigned to different sequential patterns. For example, 776_W was classified in C<sub>2</sub>A and 787_S in C<sub>2</sub>C, when they both have 30% open in home ranges (Fig. <link href="#mee312453-fig-0004"/>.3). Or, 771_S was classified in C<sub>1</sub>C and 789_S in C<sub>1</sub>R, when they both have 40% open in home ranges (Fig. <link href="#mee312453-fig-0004"/>.4).</p><figure xml:id="mee312453-fig-0005"><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee312453:mee312453-fig-0005"/><mediaResource alt="image" href="graphic/mee312453-fig-0005-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Clusters of real bimonthly trajectories with similar patterns of sequential habitat use in their spatial context. Legend in bottom‐right panel.</caption></figure><p>Finally, sequences switching between high and low were classified in the mixed‐alternating cluster (only one sequence) or in the undefined clusters (i.e. as an outlier) (Fig. <link href="#mee312453-fig-0004"/>, C<sub>1</sub>U: 14%, 8 sequences; C<sub>2</sub>U: 9%, 5 sequences). Notably, C<sub>1</sub>U mainly consists of sequences changing from high to low elevation (e.g. Fig. <link href="#mee312453-fig-0004"/>.4, 797_W; Fig. <link href="#mee312453-fig-0004"/>.6, 784_S, 795_W), whereas C<sub>2</sub>U shows the opposite pattern (e.g. Fig. <link href="#mee312453-fig-0004"/>.5, 773_S), as a result of the migration between winter and summer ranges (Fig. <link href="#mee312453-fig-0005"/>). Alternatively, undefined sequences may correspond to sequential behaviours we did not simulate, such as the shift from homogeneous closed to homogeneous open sequential habitat use within the bimonthly period (e.g. Fig. <link href="#mee312453-fig-0004"/>.5, 767_W).</p></section><section xml:id="mee312453-sec-0012"><title type="main">Sequential habitat use vs. descriptive variables</title><p>Descriptive variables of individual sequences, in particular migration occurrence, sex and age, enabled to further interpret the classification of trajectories in first‐ and second‐order clusters. In winter, individual trajectories were classified both at high and at low elevation, regardless of the space‐use strategy (i.e. both migrants and non‐migrants; see Figs <link href="#mee312453-fig-0004"/> and <link href="#mee312453-fig-0006"/>a). Conversely, in summer migrants’ trajectories were always classified at high elevation and non‐migrants’ mainly at low elevation. Sequences that were classified as alternating mainly belonged to migrants, whereas homogeneous open sequences equally corresponded to migrant and non‐migrant individuals (Figs <link href="#mee312453-fig-0004"/> and <link href="#mee312453-fig-0006"/>c). Finally, more than 60% of the non‐migrant sequences were either classified as homogeneous closed or random.</p><figure xml:id="mee312453-fig-0006"><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee312453:mee312453-fig-0006"/><mediaResource alt="image" href="graphic/mee312453-fig-0006-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Individual descriptive characteristics are summarized by the clusters to which real individual sequences were assigned. Migration (%; panel a; migrant‐m<sc>m</sc>, non‐migrant‐<fc>mN</fc>) and age (%; panel b; adult‐<fc>aA</fc>, yearling‐<fc>aY</fc>, fawn‐<fc>aF</fc>) in relation to first‐order clusters (high, C<sub>1</sub>; low, C<sub>2</sub>), both in summer and in winter. Second‐order clusters (%; C, R, A, O; see legend, Fig. <link href="#mee312453-fig-0004"/> for meaning of codes) in relation to migration (panel c) and age (panel d).</caption></figure><p>Interestingly, trajectories of fawns were classified at low elevation, both in summer and in winter, except one sequence (Figs <link href="#mee312453-fig-0004"/> and <link href="#mee312453-fig-0006"/>b). In terms of second‐order clusters, trajectories of fawns were classified in the great majority as homogeneous closed or random (Figs <link href="#mee312453-fig-0004"/> and <link href="#mee312453-fig-0006"/>d). Adults’ sequences were more equally distributed between different patterns of habitat use.</p></section><section xml:id="mee312453-sec-0013"><title type="main">Assessment of predictions and case study discussion</title><p>The first clear spatio‐temporal pattern that emerged in the study of roe deer population through exploratory analysis with SAM is a differential use of altitude. This can be linked to <i>migration</i> for two pieces of evidence: first, some individuals with sequences classified at high elevation in some trees also showed sequences classified at low elevation in other trees (Fig. <link href="#mee312453-fig-0004"/>, e.g. individual 799). Secondly, some sequences from the same individuals included both high and low altitudes in the migration periods, when animals move between seasonal ranges (Fig. <link href="#mee312453-fig-0004"/>, e.g. individual 797, Fig. <link href="#mee312453-fig-0005"/> – undefined). Moreover, the classification of sequences in high‐ and low‐elevation clusters can be attributed to <i>partial migration</i> for a further evidence; that is, sequences of some other individuals were always included in the low‐elevation cluster (Fig. <link href="#mee312453-fig-0004"/>, e.g. individual 783), or always across both (Fig. <link href="#mee312453-fig-0004"/>, e.g. individual 784); that is, they were resident. As such, prediction P1a is supported.</p><p>The property of SAM as a valid exploratory tool to identify spatio‐temporal patterns of individual movements was also highlighted when looking at descriptive variables. Sequential use of high and low elevations was same among sexes (Fig. <link href="#mee312453-fig-0004"/>: sequences of both sexes were included in both clusters; P1b), but not across age classes (Figs <link href="#mee312453-fig-0004"/> and <link href="#mee312453-fig-0006"/>; P1c). This is a novel result for roe deer that opens up new directions of research. For example, age class could be included as an explanatory variable to assess partial migratory behaviour of roe deer.</p><p>The most innovative outcome of our analysis was the spatio‐temporal analysis of open/closed habitat use by individual roe deer. The most obvious, and yet relevant, result was that roe deer did not always use open/closed habitat at random (P2a). As predicted, sequences of individuals were clearly distinguished between homogeneous and heterogeneous use or shifted between the two categories. We intend to underline that by SAM exploratory analysis, we always looked at sequential <i>use</i> of habitat; that is, habitat selection was not considered here. The term ‘random’ use, therefore, does not refer to habitat use according to total habitat availability, as in classic habitat selection analysis (Manly <i>et al</i>. <link href="#mee312453-bib-0020"/>), but to sequential use of available habitats as it would happen by chance. Indeed, we found that sequential use of habitats was heavily influenced by availability in the home range. For example, home ranges including a high percentage of closed habitat likely resulted in a ‘random’ homogeneous use of closed (Fig. <link href="#mee312453-fig-0004"/>.1–<link href="#mee312453-fig-0004"/>.3, Fig. <link href="#mee312453-fig-0005"/> – homogeneous closed). Conversely, home ranges including both open and closed habitats may lead to ‘random’ use of both (Figs <link href="#mee312453-fig-0004"/>.4–<link href="#mee312453-fig-0004"/>.6 and <link href="#mee312453-fig-0005"/> – random). Here, we have two observations. On the one side, the fact that trajectories with a different spatio‐temporal pattern laid next one to each other in the study area (Fig. <link href="#mee312453-fig-0005"/>) indicates that sequential habitat use reflects the selection operated by animals to define their home ranges within the study area (i.e. second‐order habitat selection, <i>sensu</i> Johnson <link href="#mee312453-bib-0019"/>). On the other side, though, we had clear evidence of <i>alternative</i> tactics of temporal use of habitats available within the home range (several examples of sequences with same total habitat availability, but different sequential patterns, see above and Fig. <link href="#mee312453-fig-0004"/>). When both open and closed were available in home ranges, one pattern stuck out as particularly well represented, that is the alternating use of both habitats on a daily basis (P2c; Figs <link href="#mee312453-fig-0004"/> and <link href="#mee312453-fig-0005"/>). To our knowledge, this is among the first systematic exploratory analyses of a behavioural pattern previously empirically described, and linked to individual personalities (Bonnot <i>et al</i>. <link href="#mee312453-bib-0501"/>). According to our results, the propensity of roe deer to expose to open habitats can result in a systematic spatio‐temporal pattern of habitat use, which is evident only in some individuals (Fig. <link href="#mee312453-fig-0004"/>). Even more, our explorations indicated that migrant individuals used this pattern much more than resident, at equal habitat availability in the home range (Figs <link href="#mee312453-fig-0004"/>.4–<link href="#mee312453-fig-0004"/>.6 and <link href="#mee312453-fig-0005"/> – alternating). Migration attitude in partial migratory populations has been previously attributed to a ‘boldness’ syndrome (Chapman <i>et al</i>. <link href="#mee312453-bib-0011"/>). Investigating the effect of personalities at both seasonal and daily habitat use scales is a very exciting research direction that our exploratory results suggest.</p><p>A further result that supports the individual differences in sequential habitat use is the presence of homogenous open sequences, an unexpected and yet observed pattern both at high and at low elevation (P2d; Fig. <link href="#mee312453-fig-0004"/>).</p></section></section><section xml:id="mee312453-sec-0014"><title type="main">Discussion: Application of SAM to animal habitat use</title><p>In this paper, we showed that SAM is a useful and powerful tool to explore and compare sequences of habitat use by animals, and extract common spatio‐temporal patterns. Importantly, we took into consideration an aspect of animal ecology largely overlooked in the literature. Indeed, one of the most interesting and captivating outcomes of SAM is that different topological levels of trees are associated with hierarchical similarities between the individuals’ sequential use of environmental features. Repetitive patterns of sequential habitat use are informative with respect to the interaction between animals and their environment. In fact, well‐known phenomena, such as migration, or feeding‐resting cycles emerge as repetitive patterns of habitat use at different temporal scales, as shown by our study case.</p><p>To understand the potential usefulness of SAM for movement ecology, we shall first clarify what SAM <i>is not</i>. SAM <i>is not</i> a spatial explicit method and <i>does not</i> provide predictive models of habitat use, nor of animal distribution. Instead, SAM embeds the temporal component of habitat use, in the form of real ordered sequences of used habitat classes. SAM can provide information at the <i>population and individual level</i>, by clustering individuals in trees through robust algorithms that search for dissimilarities in spatio‐temporal patterns of habitat use. Based on all these considerations, we see SAM as an approach to <i>explore</i> temporal patterns in habitat use across an animal population. As such, it differentiates from and it complements current exploratory methods in habitat analysis. The R‐package AdehabitatHS provides a suite of niche‐based methods that are primarily used to express the realized niche (Calenge &amp; Basille <link href="#mee312453-bib-0010"/>). However, niche‐based methods are also meaningful to identify the most relevant explanatory variables, and their relationships, for predictive habitat selection models, such as RSF (Boyce <i>et al</i>. <link href="#mee312453-bib-0005"/>). RSF model the probability of disproportional habitat use in a hypothetical‐deductive framework, by means of selection of competing models. A preliminary investigation of most relevant variables thus allows to better express hypotheses, and focuses the analysis. SAM may complement the aforementioned approaches by also exploring the temporal component of habitat use patterns. A big advantage of niche‐based methods or recursive partitioning trees is their ability to provide a graphical representation of the importance and relation between variables. Similarly, SAM summarizes common patterns of sequential habitat use across the population, by clustering. Importantly, we suggest that the way to handle the length of the sequences (i.e. time resolution and total duration) should depend on the research questions. In the study case, we decided to split the individuals’ sequences in bimonthly periods, since we were interested in intra‐annual patterns. This design implies that each individual is present more than once in the trees, and caution must be taken when interpreting the results (i.e. pseudo‐replication Tukey <link href="#mee312453-bib-0028"/>).</p><p>In our exercise, we considered a simple combination of habitat classes, although the R‐package TraMineR allows to define more complex combinations of environmental parameters. All the same, sequences based on many habitat classes, would lead to a very articulated dissimilarity tree, thus likely difficult to interpret. We therefore suggest a rationale in the combined and complementary use of the aforementioned methods, when exploring spatio‐temporal patterns in animal movement data. First, niche‐based or recursive partitioning methods or other simpler multivariate approaches (e.g. PCA) can be applied to identify the most important and least related environmental parameters. Then, ordered sequences of locations can be matched to those variables by spatial join with relevant geographical layers, and sequences of habitat use thus obtained. Sequence analysis can then be performed, and provide a representation of existing spatio‐temporal patterns, or be used to formulate new hypotheses evaluated through a classic model selection approach.</p><p>Technically, we suggest to simulate expected sequential behaviours (e.g. based on preliminary classifications or previous knowledge, Fig. <link href="#mee312453-fig-0001"/>a), and use them as a guide to extract the sequential habitat use pattern of real sequences, while accounting for spatial correlation (through the substitution matrix) and habitat availability. As our study case shows, real‐world sequences can exhibit more complex behaviours than those represented by simulations. We suggest that if the great majority of real trajectories are classified in cluster types derived from simulations, then the real sequential habitat use is well represented. Otherwise, one may want to change the simulation rules, which can be easily modified in the movement model.</p><p>Sequence analysis methods is a well‐suited method for data acquired by animal‐borne tracking technologies, since the method can account for two main limitations related to animal movement data: missing locations (Frair <i>et al</i>. <link href="#mee312453-bib-0015"/>) and spatial correlation (Dray, Royer‐Carenzi &amp; Calenge <link href="#mee312453-bib-0013"/>). We explicitly remarked for the first time that the weight matrix used by SAM can be used to deal with spatial correlation. Specifically, we offer a quantifiable and repeatable assessment of the spatial correlation between different habitat classes (i.e. substitution matrix). Notwithstanding the novelty, future studies may assess the sensitivity of SAM output to changes in the substitution matrix.</p><p>While the framework presented in this paper is relevant and innovative in ecological studies, in practice there are also some limitations to SAM. First, temporal correlation is not directly accounted, whereas there is a clear temporal association between consecutive observations. To account for temporal correlation, a variant of the Hamming distance known as the fuzzy Hamming distance (Bookstein, Klein &amp; Raita <link href="#mee312453-bib-0004"/>) could be used, but is only developed for binary data. Secondly, several researchers in evolutionary biology (e.g. Morrison <link href="#mee312453-bib-0021"/>) and social sciences (e.g. Wu <link href="#mee312453-bib-0033"/>) have argued that many steps in SAM are based on subjective decisions (e.g. definition of classes, parameter settings, interpretation of results).</p><p>These limitations notwithstanding, we believe that SAM offers great advantages and new insights into movement ecology studies. For instance, the method can be promptly extended to multiple or other species than roe deer as well as to other spatial (e.g. home range) and temporal resolutions (e.g. hours) and reveal yet underappreciated or overlooked patterns.</p></section><section type="acknowledgments" xml:id="mee312453-sec-0015"><title type="main">Acknowledgements</title><p>We are grateful to MOVE (COST Action IC0903) to provide funding for two Short‐Term Scientific Missions (STSMs) to conduct this research. Also, we thank Fondazione Edmund Mach and Special Research Fund (BOF) of Ghent University for their financial support to JDG. Finally, we wish to thank the collaborative EURODEER project to provide geospatial and environmental data. We are particularly grateful to Ferdinando Urbano for his support with data management, to Matteo Marcantonio for precious suggestions in R programming and to Matthias Studer for advice on TraMineR.</p></section><section type="acknowledgments" xml:id="mee312453-sec-0016"><title type="main">Data accessibility</title><p>We provide the R script (Appendix S6) to repeat the sequence analysis as here presented. The full data set is made available in the Dryad digital repository (De Groeve <i>et al</i>. <link href="#mee312453-bib-0503"/>).</p></section><bibliography cited="yes" style="nameDate" xml:id="mee312453-bibl-0001"><title type="main">References</title><bib xml:id="mee312453-bib-0001"><citation type="journal" xml:id="mee312453-cit-0001"><author><familyName>Abbott</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="1995">1995</pubYear>) <articleTitle>Sequence analysis: new methods for old ideas</articleTitle>. <journalTitle>Annual Review of Sociology</journalTitle>, <vol>21</vol>, <pageFirst>93</pageFirst>–<pageLast>113</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0002"><citation type="journal" xml:id="mee312453-cit-0002"><author><familyName>Andrienko</familyName>, <givenNames>G.</givenNames></author>, <author><familyName>Andrienko</familyName>, <givenNames>N.</givenNames></author> &amp; <author><familyName>Heurich</familyName>, <givenNames>M.</givenNames></author> (<pubYear year="2011">2011</pubYear>) <articleTitle>An event‐based conceptual model for context‐aware movement analysis</articleTitle>. <journalTitle>International Journal of Geographical Information Science</journalTitle>, <vol>25</vol>, <pageFirst>1347</pageFirst>–<pageLast>1370</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0003"><citation type="journal" xml:id="mee312453-cit-0003"><author><familyName>Benhamou</familyName>, <givenNames>S.</givenNames></author> &amp; <author><familyName>Riotte‐Lambert</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2012">2012</pubYear>) <articleTitle>Beyond the utilization distribution: identifying home range areas that are intensively exploited or repeatedly visited</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>227</vol>, <pageFirst>112</pageFirst>–<pageLast>116</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0501"><citation type="journal" xml:id="mee312453-cit-0501"><author><familyName>Bonnot</familyName>, <givenNames>N.</givenNames></author>, <author><familyName>Verheyden</familyName>, <givenNames>H.</givenNames></author>, <author><familyName>Blanchard</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Cote</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Debeffe</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Cargnelutti</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Klein</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>Hewison</familyName>, <givenNames>A.J.M.</givenNames></author> &amp; <author><familyName>Morellet</familyName>, <givenNames>N.</givenNames></author> (<pubYear year="2015">2015</pubYear>) <articleTitle>Interindividual variability in habitat use: evidence for a risk management syndrome in roe deer?</articleTitle> <journalTitle>Behavioral Ecology</journalTitle>, <vol>26</vol>, <pageFirst>105</pageFirst>–<pageLast>114</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0004"><citation type="journal" xml:id="mee312453-cit-0004"><author><familyName>Bookstein</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Klein</familyName>, <givenNames>S.T.</givenNames></author> &amp; <author><familyName>Raita</familyName>, <givenNames>T.</givenNames></author> (<pubYear year="2001">2001</pubYear>) <articleTitle>Fuzzy Hamming distance: a new dissimilarity measure</articleTitle>. <journalTitle>Combinatorial Pattern Matching</journalTitle>, <vol>2089</vol>, <pageFirst>86</pageFirst>–<pageLast>97</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0005"><citation type="journal" xml:id="mee312453-cit-0005"><author><familyName>Boyce</familyName>, <givenNames>M.S.</givenNames></author>, <author><familyName>Vernier</familyName>, <givenNames>P.R.</givenNames></author>, <author><familyName>Nielsen</familyName>, <givenNames>S.E.</givenNames></author> &amp; <author><familyName>Schmiegelow</familyName>, <givenNames>F.K.A.</givenNames></author> (<pubYear year="2002">2002</pubYear>) <articleTitle>Evaluating resource selection functions</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>157</vol>, <pageFirst>281</pageFirst>–<pageLast>300</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0006"><citation type="book" xml:id="mee312453-cit-0006"><author><familyName>Buchin</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Dodge</familyName>, <givenNames>S.</givenNames></author> &amp; <author><familyName>Speckmann</familyName>, <givenNames>B</givenNames></author>. (<pubYear year="2012">2012</pubYear>) <chapterTitle>Context‐aware similarity of trajectories</chapterTitle>. <bookTitle>Proceedings of the 6th International Conference on Geographic Information Science (GIScience)</bookTitle>. <publisherName>Springer</publisherName>, <publisherLoc>Berlin Heidelberg</publisherLoc>.</citation></bib><bib xml:id="mee312453-bib-0007"><citation type="journal" xml:id="mee312453-cit-0007"><author><familyName>Cagnacci</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>Boitani</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Powell</familyName>, <givenNames>R.A.</givenNames></author> &amp; <author><familyName>Boyce</familyName>, <givenNames>M.S.</givenNames></author> (<pubYear year="2010">2010</pubYear>) <articleTitle>Animal ecology meets GPS‐based radiotelemetry: a perfect storm of opportunities and challenges</articleTitle>. <journalTitle>Philosophical Transactions of the Royal Society B, Biological Sciences</journalTitle>, <vol>365</vol>, <pageFirst>2157</pageFirst>–<pageLast>2162</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0008"><citation type="journal" xml:id="mee312453-cit-0008"><author><familyName>Cagnacci</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>Focardi</familyName>, <givenNames>S.</givenNames></author>, <author><familyName>Heurich</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Stache</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Hewison</familyName>, <givenNames>A.J.M.</givenNames></author>, <author><familyName>Morellet</familyName>, <givenNames>N.</givenNames></author> <i>et al</i>. (<pubYear year="2011">2011</pubYear>) <articleTitle>Partial migration in roe deer: migratory and resident tactics are end points of a behavioural gradient determined by ecological factors</articleTitle>. <journalTitle>Oikos</journalTitle>, <vol>120</vol>, <pageFirst>1790</pageFirst>–<pageLast>1802</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0009"><citation type="journal" xml:id="mee312453-cit-0009"><author><familyName>Calenge</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2006">2006</pubYear>) <articleTitle>The package adehabitat for the R software: a tool for the analysis of space and habitat use by animals</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>197</vol>, <pageFirst>516</pageFirst>–<pageLast>519</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0010"><citation type="journal" xml:id="mee312453-cit-0010"><author><familyName>Calenge</familyName>, <givenNames>C.</givenNames></author> &amp; <author><familyName>Basille</familyName>, <givenNames>M.</givenNames></author> (<pubYear year="2008">2008</pubYear>) <articleTitle>A general framework for the statistical exploration of the ecological niche</articleTitle>. <journalTitle>Journal of Theoretical Biology</journalTitle>, <vol>252</vol>, <pageFirst>674</pageFirst>–<pageLast>685</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0011"><citation type="journal" xml:id="mee312453-cit-0011"><author><familyName>Chapman</familyName>, <givenNames>B.B.</givenNames></author>, <author><familyName>Hulthén</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Blomqvist</familyName>, <givenNames>D.R.</givenNames></author>, <author><familyName>Hansson</familyName>, <givenNames>L.‐A.</givenNames></author>, <author><familyName>Nilsson</familyName>, <givenNames>J.‐Å.</givenNames></author>, <author><familyName>Brodersen</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Nilsson</familyName>, <givenNames>P.A.</givenNames></author>, <author><familyName>Skov</familyName>, <givenNames>C.</givenNames></author> &amp; <author><familyName>Brönmark</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2011">2011</pubYear>) <articleTitle>To boldly go: individual differences in boldness influence migratory tendency</articleTitle>. <journalTitle>Ecology Letters</journalTitle>, <vol>14</vol>, <pageFirst>871</pageFirst>–<pageLast>876</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0503"><citation type="journal" xml:id="mee312453-cit-0503"><author><familyName>De Groeve</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Van de Weghe</familyName>, <givenNames>N.</givenNames></author>, <author><familyName>Ranc</familyName>, <givenNames>N.</givenNames></author>, <author><familyName>Ometto</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Rota‐Stabelli</familyName>, <givenNames>O.</givenNames></author> &amp; <author><familyName>Cagnacci</familyName>, <givenNames>F.</givenNames></author> (<pubYear year="2015">2015</pubYear>) <articleTitle>Data from: Extracting spatio‐temporal patterns in animal trajectories: an ecological application of sequence analysis methods (SAM)</articleTitle>. <journalTitle>Dryad Digital Repository</journalTitle>. doi:<accessionId ref="info:doi/10.5061/dryad.h4f7p">10.5061/dryad.h4f7p</accessionId>.</citation></bib><bib xml:id="mee312453-bib-0012"><citation type="journal" xml:id="mee312453-cit-0012"><author><familyName>Delafontaine</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Versichele</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Neutens</familyName>, <givenNames>T.</givenNames></author> &amp; <author><familyNamePrefix>Van de</familyNamePrefix> <familyName>Weghe</familyName>, <givenNames>N.</givenNames></author> (<pubYear year="2012">2012</pubYear>) <articleTitle>Analysing spatio‐temporal sequences in Bluetooth tracking data</articleTitle>. <journalTitle>Applied Geography</journalTitle>, <vol>34</vol>, <pageFirst>659</pageFirst>–<pageLast>668</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0013"><citation type="journal" xml:id="mee312453-cit-0013"><author><familyName>Dray</familyName>, <givenNames>S.</givenNames></author>, <author><familyName>Royer‐Carenzi</familyName>, <givenNames>M.</givenNames></author> &amp; <author><familyName>Calenge</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2010">2010</pubYear>) <articleTitle>The exploratory analysis of autocorrelation in animal‐movement studies</articleTitle>. <journalTitle>Ecological Research</journalTitle>, <vol>4</vol>, <pageFirst>34</pageFirst>–<pageLast>41</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0504"><citation type="other" xml:id="mee312453-cit-0504"><groupName>European Environment Agency, EEA</groupName> (<pubYear year="2010">2010</pubYear>) <otherTitle>CORINE Land Cover 2006</otherTitle>. URL <url href="http://www.eea.europa.eu/dataand-maps/data/corine-land-cover-2000-2006">http://www.eea.europa.eu/dataand-maps/data/corine-land-cover-2000-2006</url> (accessed 20 August 2015).</citation></bib><bib xml:id="mee312453-bib-0014"><citation type="journal" xml:id="mee312453-cit-0014"><author><familyName>Fortin</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>Hawthorne</familyName>, <givenNames>L.B.</givenNames></author>, <author><familyName>Boyce</familyName>, <givenNames>M.S.</givenNames></author>, <author><familyName>Smith</familyName>, <givenNames>D.W.</givenNames></author>, <author><familyName>Duchesne</familyName>, <givenNames>T.</givenNames></author> &amp; <author><familyName>Mao</familyName>, <givenNames>J.S.</givenNames></author> (<pubYear year="2005">2005</pubYear>) <articleTitle>Wolves influence elk movements: behaviour shapes a trophic cascade in Yellowstone national park</articleTitle>. <journalTitle>Ecology</journalTitle>, <vol>86</vol>, <pageFirst>1320</pageFirst>–<pageLast>1330</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0015"><citation type="journal" xml:id="mee312453-cit-0015"><author><familyName>Frair</familyName>, <givenNames>J.L.</givenNames></author>, <author><familyName>Fieberg</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Hebblewhite</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Cagnacci</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>DeCesare</familyName>, <givenNames>N.J.</givenNames></author> &amp; <author><familyName>Pedrotti</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2010">2010</pubYear>) <articleTitle>Resolving issues of imprecise and habitat‐biased locations in ecological analyses using GPS telemetry data</articleTitle>. <journalTitle>Philosophical Transactions of the Royal Society B, Biological Sciences</journalTitle>, <vol>365</vol>, <pageFirst>2187</pageFirst>–<pageLast>2200</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0016"><citation type="journal" xml:id="mee312453-cit-0016"><author><familyName>Gabadinho</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Ritschard</familyName>, <givenNames>G.</givenNames></author>, <author><familyName>Müller</familyName>, <givenNames>N.S.</givenNames></author> &amp; <author><familyName>Studer</familyName>, <givenNames>M.</givenNames></author> (<pubYear year="2011">2011</pubYear>) <articleTitle>Analyzing and visualizing state sequences in R with TraMineR</articleTitle>. <journalTitle>Journal of Statistical Software</journalTitle>, <vol>40</vol>, <pageFirst>1</pageFirst>–<pageLast>37</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0017"><citation type="journal" xml:id="mee312453-cit-0017"><author><familyName>Hennig</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2007">2007</pubYear>) <articleTitle>Cluster‐wise assessment of cluster stability</articleTitle>. <journalTitle>Computational Statistics and Data Analysis</journalTitle>, <vol>52</vol>, <pageFirst>258</pageFirst>–<pageLast>271</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0018"><citation type="book" xml:id="mee312453-cit-0018"><author><familyName>Hewison</familyName>, <givenNames>A.J.M.</givenNames></author>, <author><familyName>Vincent</familyName>, <givenNames>J.P.</givenNames></author> &amp; <author><familyName>Reby</familyName>, <givenNames>D.</givenNames></author> (<pubYear year="1998">1998</pubYear>) <chapterTitle>Social organisation of European roe deer</chapterTitle>. <bookTitle>The European roe Deer: The Biology of Success</bookTitle> (eds <editor><givenNames>R.</givenNames> <familyName>Andersen</familyName></editor>, <editor><givenNames>P.</givenNames> <familyName>Duncan</familyName></editor> &amp; <editor><givenNames>J.D.C.</givenNames> <familyName>Linnell</familyName></editor>), pp. <pageFirst>189</pageFirst>–<pageLast>219</pageLast>. <publisherName>Scandinavian University Press</publisherName>, <publisherLoc>Oslo</publisherLoc>.</citation></bib><bib xml:id="mee312453-bib-0019"><citation type="journal" xml:id="mee312453-cit-0019"><author><familyName>Johnson</familyName>, <givenNames>D.H.</givenNames></author> (<pubYear year="1980">1980</pubYear>) <articleTitle>The comparison of usage and availability measurements for evaluating resource preference</articleTitle>. <journalTitle>Ecology</journalTitle>, <vol>61</vol>, <pageFirst>65</pageFirst>–<pageLast>71</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0020"><citation type="book" xml:id="mee312453-cit-0020"><author><familyName>Manly</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>McDonald</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Thomas</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>MacDonald</familyName>, <givenNames>T.</givenNames></author> &amp; <author><familyName>Erickson</familyName>, <givenNames>W.</givenNames></author> (<pubYear year="2002">2002</pubYear>) <bookTitle>Resource Selection by Animals. Statistical Design and Analysis for Field Studies</bookTitle>. <publisherName>Kluwer Academic Publisher</publisherName>, <publisherLoc>London</publisherLoc>.</citation></bib><bib xml:id="mee312453-bib-0505"><citation type="other" xml:id="mee312453-cit-0505"><groupName>Ministery of Economy, Trade, and Industry of Japan, METI, National Aeronautics and Space Administration of the United States, NASA</groupName> (<pubYear year="2012">2012</pubYear>) <otherTitle>Advanced Spaceborne Thermal Emission and Reflection Radiometer (ASTER) Global Digital Elevation Model Version 1 (GDEM V1)</otherTitle>. URL <url href="http://asterweb.jpl.nasa.gov/gdem.asp">http://asterweb.jpl.nasa.gov/gdem.asp</url> (accessed 20 August 2015).</citation></bib><bib xml:id="mee312453-bib-0021"><citation type="journal" xml:id="mee312453-cit-0021"><author><familyName>Morrison</familyName>, <givenNames>D.A.</givenNames></author> (<pubYear year="2010">2010</pubYear>) <articleTitle>Sequence alignment: methods, models, concepts, and strategies</articleTitle>. <journalTitle>Systematic Biology</journalTitle>, <vol>59</vol>, <pageFirst>363</pageFirst>–<pageLast>365</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0022"><citation type="journal" xml:id="mee312453-cit-0022"><author><familyName>Ramanzin</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Sturaro</familyName>, <givenNames>E.</givenNames></author> &amp; <author><familyName>Zanon</familyName>, <givenNames>D.</givenNames></author> (<pubYear year="2007">2007</pubYear>) <articleTitle>Seasonal migration and home range of roe deer (<i>Capreolus capreolus</i>) in the Italian eastern Alps</articleTitle>. <journalTitle>Canadian Journal of Zoology</journalTitle>, <vol>85</vol>, <pageFirst>280</pageFirst>–<pageLast>289</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0023"><citation type="book" xml:id="mee312453-cit-0023"><author><familyName>Rodgers</familyName>, <givenNames>A.R.</givenNames></author> &amp; <author><familyName>Carr</familyName>, <givenNames>A.P</givenNames></author>. (<pubYear year="1998">1998</pubYear>) <bookTitle>HRE: The Home Range Extension for ArcView. Ontario Ministry of Natural Resources</bookTitle>, <publisherName>Centre for Northern Forest Ecosystem Research</publisherName>, <publisherLoc>Thunder Bay, Ontario, Canada</publisherLoc>. URL <url href="http://flash.lakeheadu.ca/~arodgers/hre/">http://flash.lakeheadu.ca/~arodgers/hre/</url> (accessed 4 March 2014).</citation></bib><bib xml:id="mee312453-bib-0024"><citation type="journal" xml:id="mee312453-cit-0024"><author><familyName>Saïd</familyName>, <givenNames>S.</givenNames></author> &amp; <author><familyName>Servanty</familyName>, <givenNames>S.</givenNames></author> (<pubYear year="2005">2005</pubYear>) <articleTitle>The influence of landscape structure on female roe deer home‐range size</articleTitle>. <journalTitle>Landscape Ecology</journalTitle>, <vol>20</vol>, <pageFirst>1003</pageFirst>–<pageLast>1012</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0025"><citation type="journal" xml:id="mee312453-cit-0025"><author><familyName>Shoval</familyName>, <givenNames>N.</givenNames></author> &amp; <author><familyName>Isaacson</familyName>, <givenNames>M.</givenNames></author> (<pubYear year="2007">2007</pubYear>) <articleTitle>Sequence alignment as a method for human activity analysis in space and time</articleTitle>. <journalTitle>Annals of the Association of American Geographers</journalTitle>, <vol>97</vol>, <pageFirst>282</pageFirst>–<pageLast>297</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0026"><citation type="other" xml:id="mee312453-cit-0026"><author><familyName>Therneau</familyName>, <givenNames>T.</givenNames></author>, <author><familyName>Atkinson</familyName>, <givenNames>B.</givenNames></author> &amp; <author><familyName>Ripley</familyName>, <givenNames>B</givenNames></author>. (<pubYear year="2014">2014</pubYear>) <otherTitle>Recursive Partitioning and Regression Trees</otherTitle>. URL <url href="http://cran.r-project.org/web/packages/rpart/rpart.pdf">http://cran.r-project.org/web/packages/rpart/rpart.pdf</url> (accessed 10 October 2014).</citation></bib><bib xml:id="mee312453-bib-0027"><citation type="journal" xml:id="mee312453-cit-0027"><author><familyName>Tufto</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Andersen</familyName>, <givenNames>R.</givenNames></author> &amp; <author><familyName>Linnell</familyName>, <givenNames>J.D.C.</givenNames></author> (<pubYear year="1996">1996</pubYear>) <articleTitle>Habitat use and ecological correlates of home range size in a small cervid: the roe deer</articleTitle>. <journalTitle>Journal of Animal Ecology</journalTitle>, <vol>65</vol>, <pageFirst>715</pageFirst>–<pageLast>724</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0028"><citation type="book" xml:id="mee312453-cit-0028"><author><familyName>Tukey</familyName>, <givenNames>J.W.</givenNames></author> (<pubYear year="1977">1977</pubYear>) <bookTitle>Exploratory Data Analysis</bookTitle>. <publisherName>Addison‐Wesley</publisherName>, <publisherLoc>Reading, Massachusetts</publisherLoc>.</citation></bib><bib xml:id="mee312453-bib-0029"><citation type="journal" xml:id="mee312453-cit-0029"><author><familyName>Urbano</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>Cagnacci</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>Calenge</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Dettki</familyName>, <givenNames>H.</givenNames></author>, <author><familyName>Cameron</familyName>, <givenNames>A.</givenNames></author> &amp; <author><familyName>Neteler</familyName>, <givenNames>M.</givenNames></author> (<pubYear year="2010">2010</pubYear>) <articleTitle>Wildlife tracking data management: a new vision</articleTitle>. <journalTitle>Philosophical transactions of the Royal Society of London Series B, Biological Sciences</journalTitle>, <vol>365</vol>, <pageFirst>2177</pageFirst>–<pageLast>2185</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0030"><citation type="journal" xml:id="mee312453-cit-0030"><author><familyName>Wilson</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2008">2008</pubYear>) <articleTitle>Activity patterns in space and time: calculation representative Hägerstrand ClustalG software</articleTitle>. <journalTitle>Transportation</journalTitle>, <vol>35</vol>, <pageFirst>485</pageFirst>–<pageLast>499</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0031"><citation type="journal" xml:id="mee312453-cit-0031"><author><familyName>Wittemyer</familyName>, <givenNames>G.</givenNames></author>, <author><familyName>Polansky</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Douglas‐Hamilton</familyName>, <givenNames>I.</givenNames></author> &amp; <author><familyName>Getz</familyName>, <givenNames>W.M.</givenNames></author> (<pubYear year="2008">2008</pubYear>) <articleTitle>Disentangling the effects of forage, social rank, and risk on movement autocorrelation of elephants using Fourier and wavelet analyses</articleTitle>. <journalTitle>Proceedings of the National Academy of Sciences of the United States of America</journalTitle>, <vol>105</vol>, <pageFirst>19108</pageFirst>–<pageLast>19113</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0032"><citation type="journal" xml:id="mee312453-cit-0032"><author><familyName>Worton</familyName>, <givenNames>B.J.</givenNames></author> (<pubYear year="1995">1995</pubYear>) <articleTitle>Using Monte‐Carlo simulation to evaluate kernel‐based home‐range estimators</articleTitle>. <journalTitle>Journal of Wildlife Management</journalTitle>, <vol>59</vol>, <pageFirst>794</pageFirst>–<pageLast>800</pageLast>.</citation></bib><bib xml:id="mee312453-bib-0033"><citation type="journal" xml:id="mee312453-cit-0033"><author><familyName>Wu</familyName>, <givenNames>L.L.</givenNames></author> (<pubYear year="2000">2000</pubYear>) <articleTitle>Some comments on sequence analysis and optimal matching methods in sociology: review and prospect</articleTitle>. <journalTitle>Sociological Methods and Research</journalTitle>, <vol>29</vol>, <pageFirst>41</pageFirst>–<pageLast>64</pageLast>.</citation></bib></bibliography></body></component>