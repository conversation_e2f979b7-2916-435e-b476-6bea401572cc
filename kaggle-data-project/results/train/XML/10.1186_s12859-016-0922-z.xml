<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 2?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">4739097</article-id><article-id pub-id-type="publisher-id">922</article-id><article-id pub-id-type="doi">10.1186/s12859-016-0922-z</article-id><article-categories><subj-group subj-group-type="heading"><subject>Methodology Article</subject></subj-group></article-categories><title-group><article-title>Measure transcript integrity using RNA-seq data</article-title></title-group><contrib-group><contrib contrib-type="author" equal-contrib="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-2072-4826</contrib-id><name><surname>Wang</surname><given-names>Liguo</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Nie</surname><given-names>Jinfu</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Sicotte</surname><given-names>Hugues</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Li</surname><given-names>Ying</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Eckel-Passow</surname><given-names>Jeanette E.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Dasari</surname><given-names>Surendra</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Vedell</surname><given-names>Peter T.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Barman</surname><given-names>Poulami</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Wang</surname><given-names>Liewei</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3"/></contrib><contrib contrib-type="author"><name><surname>Weinshiboum</surname><given-names>Richard</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3"/></contrib><contrib contrib-type="author"><name><surname>Jen</surname><given-names>Jin</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4"/></contrib><contrib contrib-type="author"><name><surname>Huang</surname><given-names>Haojie</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff5"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Kohli</surname><given-names>Manish</given-names></name><address><phone>****** 284 6934</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Kocher</surname><given-names>Jean-Pierre A.</given-names></name><address><phone>****** 538 8315</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><label/>Division of Biomedical Statistics and Informatics, Mayo Clinic, Rochester, MN 55905 USA </aff><aff id="Aff2"><label/>Department of Oncology, Mayo Clinic, Rochester, MN 55905 USA </aff><aff id="Aff3"><label/>Department of Molecular Pharmacology and Experimental Therapeutics, Mayo Clinic, Rochester, MN 55905 USA </aff><aff id="Aff4"><label/>Department of laboratory medicine and pathology, Mayo Clinic, Rochester, MN 55905 USA </aff><aff id="Aff5"><label/>Department of Biochemistry and Molecular Biology, Mayo Clinic, Rochester, MN 55905 USA </aff></contrib-group><pub-date pub-type="epub"><day>3</day><month>2</month><year>2016</year></pub-date><pub-date pub-type="pmc-release"><day>3</day><month>2</month><year>2016</year></pub-date><pub-date pub-type="collection"><year>2016</year></pub-date><volume>17</volume><elocation-id>58</elocation-id><history><date date-type="received"><day>2</day><month>10</month><year>2015</year></date><date date-type="accepted"><day>29</day><month>1</month><year>2016</year></date></history><permissions><copyright-statement>&#x000a9; Wang et al. 2016</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>Stored biological samples with pathology information and medical records are invaluable resources for translational medical research. However, RNAs extracted from the archived clinical tissues are often substantially degraded. RNA degradation distorts the RNA-seq read coverage in a gene-specific manner, and has profound influences on whole-genome gene expression profiling.</p></sec><sec><title>Result</title><p>We developed the transcript integrity number (TIN) to measure RNA degradation. When applied to 3 independent RNA-seq datasets, we demonstrated TIN is a reliable and sensitive measure of the RNA degradation at both transcript and sample level. Through comparing 10 prostate cancer clinical samples with lower RNA integrity to 10 samples with higher RNA quality, we demonstrated that calibrating gene expression counts with TIN scores could effectively neutralize RNA degradation effects by reducing false positives and recovering biologically meaningful pathways. When further evaluating the performance of TIN correction using spike-in transcripts in RNA-seq data generated from the Sequencing Quality Control consortium, we found TIN adjustment had better control of false positives and false negatives (sensitivity&#x02009;=&#x02009;0.89, specificity&#x02009;=&#x02009;0.91, accuracy&#x02009;=&#x02009;0.90), as compared to gene expression analysis results without TIN correction (sensitivity = 0.98, specificity&#x02009;=&#x02009;0.50, accuracy&#x02009;=&#x02009;0.86).</p></sec><sec><title>Conclusion</title><p>TIN is a reliable measurement of RNA integrity and a valuable approach used to neutralize in vitro RNA degradation effect and improve differential gene expression analysis.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s12859-016-0922-z) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Transcript integrity number</kwd><kwd>TIN</kwd><kwd>RNA-seq quality control</kwd><kwd>Gene expression</kwd></kwd-group><funding-group><award-group><funding-source><institution>Mayo Clinic Center for Individualized Medicine</institution></funding-source></award-group><award-group><funding-source><institution>A.T. Suharya and Ghan D.H</institution></funding-source></award-group><award-group><funding-source><institution>Joseph and Gail Gassner</institution></funding-source></award-group><award-group><funding-source><institution>Mayo Clinic Schulze Cancer for Novel Therapeutics in Cancer Research</institution></funding-source></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution>Pharmacogenomics Research Network </institution></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2016</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>In vitro RNA degradation occurs in most of the isolated RNA samples and the degree of degradation depends on the specimen collection and storage conditions such as formalin-fixed, paraffin-embedded (FFPE) and fresh frozen [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR3">3</xref>]. This is especially a major issue for clinical tissues collected in surgery suites because optimal storage of collected specimens is often not the primary focus in that setting. There have been multiple studies showing that in vitro degradation of RNA impairs accurate measurement of in vivo gene expression [<xref ref-type="bibr" rid="CR4">4</xref>, <xref ref-type="bibr" rid="CR5">5</xref>]. RNA degradation has not been a major problem up to recently since it has a minor influence on gene expression measured with hybridization-based microarray platforms, in which the expression of each gene is measured by only a few short, discrete probes. For example, a previous study found that only 0.67&#x000a0;% (275 out of 41,000) of the probes were significantly affected by in vitro RNA degradation [<xref ref-type="bibr" rid="CR6">6</xref>]. However, in recent years, more studies including The Cancer Genome Atlas consortium (TCGA) are switching to use sequencing-based RNA-seq to profile gene expression. RNA-seq works under the assumption that every nucleotide of the transcript has the equal chance to be sequenced and the amount of reads produced from a transcript is proportional to the abundance and length of the transcript. However, if RNA molecules were partially or completely degraded the corresponding read yield would be also distorted accordingly. Hence, in vitro RNA degradation introduces a major source of variation when measuring gene expression via RNA-seq. In support of this hypothesis, a recent study found that up to 56&#x000a0;% of the genes were differentially expressed due to in vitro RNA degradation [<xref ref-type="bibr" rid="CR5">5</xref>].</p><p>RNA Integrity Number (RIN) is the most widely used approach to assess in vitro RNA degradation [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR7">7</xref>]. However, the RIN metric has several weaknesses that limit its applications in both pre-sequencing RNA sample screening and post-sequencing RNA-seq data analysis. First, the RIN score relies heavily on the amount of 18S and 28S ribosome RNAs; the four main features used by the RIN algorithm includes the &#x0201c;total RNA ratio&#x0201d;, &#x0201c;28S-region height&#x0201d;, &#x0201c;28S area ratio&#x0201d; and the &#x0201c;18S:28S ratio&#x0201d;. While this metric accurately captures the integrity of ribosomal RNAs, it fails to measure the mRNA integrity directly, which is the main input for RNA sequencing. Second, RNA decay rate is transcript specific and it is modulated by several endogenous and exogenous factors as well as other factors including &#x0201c;AU-rich&#x0201d; sequence, transcript length, GC content, secondary structure, RNA-protein complex [<xref ref-type="bibr" rid="CR4">4</xref>, <xref ref-type="bibr" rid="CR5">5</xref>]. It was found that RNA decay rate varies between functional groups [<xref ref-type="bibr" rid="CR6">6</xref>, <xref ref-type="bibr" rid="CR8">8</xref>] and between transcripts by up to ten-fold [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>]. Third, RIN is an overall assessment of RNA quality and cannot be used as a co-factor to adjust for differential RNA degradation between transcripts in downstream gene expression analysis. Finally, it has been reported that RIN was not a sensitive measure of RNA quality for substantially degraded samples (<ext-link ext-link-type="uri" xlink:href="https://www.illumina.com/content/dam/illumina-marketing/documents/products/technotes/evaluating-rnaquality-from-ffpe-samples-technical-note-470-2014-001.pdf">https://www.illumina.com/content/dam/illumina-marketing/documents/products/technotes/evaluating-rnaquality-from-ffpe-samples-technical-note-470-2014-001.pdf</ext-link>). Illumina&#x000ae; proposed DV<sub>200</sub> metric (the percentage of RNA fragments&#x02009;&#x0003e;&#x02009;200 nucleotides) to assess RNA quality. However, similar to RIN, DV<sub>200</sub> is also an overall measurement and fails to determine RNA degradation at transcript level.</p><p>The reduction of sequencing cost has opened doors for large-scale, RNA-seq-based, gene expression profiling studies (like TCGA) that use clinical specimens with rich outcomes data. At the same time, the RNA quality of these clinical samples could vary significantly and poses a great challenge to gene expression analysis. Here we developed a novel algorithm&#x02013;transcript integrity number (TIN)&#x02013;to evaluate RNA integrity from RNA-seq data. We applied our TIN algorithm to RNA-seq data generated from 12 human glioblastoma (GBM) cell line samples, 20 human peripheral blood mononuclear cell samples (PBMC), and 120 metastatic castration resistant prostate cancer (mCRPC) samples. Our results showed that TIN metric accurately measured the mRNA integrity at transcript level, as demonstrated by high concordance with RNA fragment size that estimated from RNA-seq read pairs. We also demonstrated that the median TIN score (medTIN) across all transcripts can be an accurate and reliable measurement of RNA integrity at transcriptome (or &#x0201c;sample&#x0201d;) level. More importantly, the TIN that is computed for each transcript can be used to adjust gene expression and improve differential expression analysis by reducing the false positives ascribed to in vitro RNA degradation.</p></sec><sec id="Sec2" sec-type="discussion"><title>Results and discussion</title><sec id="Sec3"><title>Measuring sample level RNA integrity</title><p>We used the median TIN score (medTIN) of all the transcripts to measure the overall RNA integrity of a sample. We evaluated the concordance between medTIN and the widely used RIN metric using three independent human datasets: GBM cell lines, PBMCs, and mCRPC. Each of these datasets has samples covering a broad range of RIN values. GBM samples have RIN values ranging from 2 to 10 (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S1), PBMC samples have RIN values ranging from 2.8 to 9.4 (Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S2) and mCPRC samples have RIN values ranging from 2.2 to 9.2 (Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Table S3). The Pearson correlation coefficients between medTINs and the corresponding RIN scores for the GBM, mCRPC and PBMC samples were 0.93 (<italic>P</italic>&#x02009;=&#x02009;9.1&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;6</sup>; Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1a</xref>), 0.77 (<italic>P</italic>&#x02009;&#x0003c;&#x02009;2.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;16</sup>; Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S1) and 0.83 (<italic>P</italic>&#x02009;=&#x02009;7.3&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;6</sup>; Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1b</xref>), respectively. The high concordance highlighted that medTIN was a reliable index of the overall RNA quality of a sample. Compared to GBM samples. The correlation between RIN and medTIN in mCRPC samples was lower, which was probably because the RIN scores were clustered into two extremes: with 28 (23.3&#x000a0;%) samples had RIN&#x02009;&#x0003c;&#x02009;3 and 61 (50.8&#x000a0;%) samples had RIN&#x02009;&#x0003e;&#x02009;8 (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1b</xref>, Additional file <xref rid="MOESM5" ref-type="media">5</xref>: Figure S2, Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Table S3).<fig id="Fig1"><label>Fig. 1</label><caption><p>Evaluating median TIN score (medTIN) metric using RIN and gene body read coverage. <bold>a</bold> Scatterplot showing correlation between the medTIN and the corresponding RIN score for 12 GBM samples. Black dashed line is the linear regression line fitted to data. <bold>b</bold> Scatterplot showing correlation between the medTIN and the corresponding RIN score for 120 mCRPC samples. Black dashed line is the linear regression line fitted to data. <bold>c</bold> Gene body coverage profiles for 12 GBM samples. Samples were ranked from top to bottom on the y-axis in the decreasing order of medTIN. Numbers in parentheses are the corresponding RIN scores. <bold>d</bold> Gene body coverage profiles for 120 mCRPC samples. Samples were ranked from top to bottom on the y-axis in the decreasing order of medTIN. <italic>r</italic> stands for Pearson&#x02019;s correlation coefficient; &#x003c1; stands for Spearman&#x02019;s correlation coefficient</p></caption><graphic xlink:href="12859_2016_922_Fig1_HTML" id="MO1"/></fig></p><p>The 3&#x02032; bias observed in RNA-seq data could arise from RNA degradation by 5&#x02032; exonuclease [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR12">12</xref>], and the commonly used polyA enrichment approach would lead to a even stronger 3&#x02032; bias particularly in degraded RNA samples because oligo (dT) selection will only isolate the most 3&#x02032; portion of the transcript [<xref ref-type="bibr" rid="CR13">13</xref>]. Consistently with this hypothesis, we found that samples with lower medTIN score usually had more skewed gene body coverage (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1c</xref>-<xref rid="Fig1" ref-type="fig">d</xref>). The PBMC dataset was excluded from further analysis because its single-end sequencing design prevents the estimation of RNA fragment size.</p><p>The average RNA fragment size of a sequencing library, which can be directly estimated from mapped read pairs, is a surrogate measurement of RNA integrity because RNA fragments become smaller after in vitro degradation process. We therefore computed the average RNA fragment size of all read pairs to measure the integrity of a RNA sample, and compared it with medTIN and RIN metrics, respectively. For the 12 GBM samples, both RIN (<italic>r</italic>&#x02009;=&#x02009;0.90, <italic>P</italic>&#x02009;=&#x02009;1.0&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;4</sup>; Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref>) and medTIN (<italic>r</italic>&#x02009;=&#x02009;0.96, <italic>P</italic>&#x02009;=&#x02009;1.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;6</sup>; Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2b</xref>) were strongly correlated with the average RNA fragment sizes with medTIN metric performed slightly better. For mCRPC RNA samples, the medTIN (<italic>r</italic>&#x02009;=&#x02009;0.55, <italic>P</italic>&#x02009;=&#x02009;7.7&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;11</sup>) also performed significantly better than RIN (<italic>r</italic>&#x02009;=&#x02009;0.40, <italic>P</italic>&#x02009;=&#x02009;5.5&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;6</sup>) (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2c</xref>, <xref rid="Fig2" ref-type="fig">d</xref>). We further evaluated the performance of medTIN metric on severely degraded samples using a subset of 28 mCRPC samples that have RIN values&#x02009;&#x0003c;&#x02009;3 (Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Table S3). We observed no positive correlation between RIN and the corresponding average RNA fragment sizes (<italic>r</italic>&#x02009;=&#x02009;0.089, <italic>P</italic>&#x02009;=&#x02009;0.65; Additional file <xref rid="MOESM6" ref-type="media">6</xref>: Figure S3a). In contrast, we observed a strong positive correlation between medTINs and the RNA fragment sizes for these samples (<italic>r</italic>&#x02009;=&#x02009;0.62, <italic>P</italic>&#x02009;=&#x02009;4.5&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;4</sup>; Additional file <xref rid="MOESM6" ref-type="media">6</xref> Figure S3b). These results highlighted medTIN was more sensitive than RIN to measure the integrity of RNA samples that were severely degraded.<fig id="Fig2"><label>Fig. 2</label><caption><p>Evaluating median TIN score (medTIN) and RIN metric using sample level average RNA fragment size. The average RNA fragment size of a sample was estimated from all read pairs that uniquely mapped to the reference genome (see <xref rid="Sec12" ref-type="sec">Methods</xref>). <bold>a</bold> Correlation between RIN score and the average RNA fragment size for 12 GMB samples. <bold>b</bold> Correlation between medTIN and average RNA fragment size for 12 GMB samples. <bold>c</bold> Correlation between RIN score and average RNA fragment size for 120 mCRPC samples. <bold>d</bold> Correlation between medTIN and average RNA fragment size for 120 mCRPC samples. (<bold>c</bold>-<bold>d</bold>) Samples with RIN&#x02009;&#x0003c;&#x02009;3 and RIN &#x02265;3 were indicated as red and blue circles, respectively. (<bold>a</bold>-<bold>d</bold>) Linear regression lines fitted to data are indicated as black dashed lines</p></caption><graphic xlink:href="12859_2016_922_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec4"><title>Measuring transcript level RNA integrity</title><p>Compared to RIN and other global measurements [<xref ref-type="bibr" rid="CR14">14</xref>&#x02013;<xref ref-type="bibr" rid="CR16">16</xref>], one of the major improvements of TIN is to measure RNA integrity of individual transcripts/genes. We evaluated the performance of TIN by correlating it with the transcript level average RNA fragment size. As shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>, TIN score and RNA fragment size had a strong positive correlation (Pearson&#x02019;s <italic>r</italic>&#x02009;=&#x02009;0.88, <italic>P</italic>&#x02009;&#x0003c;&#x02009;2.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;16</sup>; Spearman&#x02019;s <italic>&#x003c1;</italic>&#x02009;=&#x02009;0.71, <italic>P</italic>&#x02009;&#x0003c;&#x02009;2.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;16</sup>) suggesting that TIN was a good metric to measure transcript integrity. Interestingly, we found the average RNA fragment size became asymptotically stable as TIN score went beyond certain threshold (i.e. saturation point). For instance, in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>, the saturation point was around TIN&#x02009;=&#x02009;70, and the correlation between TIN and RNA fragment size was much higher for transcripts with TIN&#x02009;&#x0003c;&#x02009;70 (<italic>r</italic>&#x02009;=&#x02009;0.94, <italic>P</italic>&#x02009;&#x0003c;&#x02009;2.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;16</sup>) than that of transcripts with TIN&#x02009;&#x0003e;&#x02009;70 (<italic>r</italic>&#x02009;=&#x02009;0.22, <italic>P</italic>&#x02009;=&#x02009;0.003). We observed the similar trend in all GBM samples with different RIN values (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>, Additional file <xref rid="MOESM7" ref-type="media">7</xref>: Figure S4). This is because the RNA degradation is not the sole determinant for RNA fragment size as most sequencing library preparation protocols also incorporate a RNA (or cDNA) &#x0201c;fragmentation step&#x0201d;. Therefore, the sizes of RNA fragments of a particular transcript are determined by two factors at the same time: the fragmentation intensity during library preparation and the RNA degradation. Presumably, transcripts with larger TIN values had better RNA integrity and therefore &#x0201c;fragmentation step&#x0201d; played a dominant role in determining the fragment size whereas RNA degradation played a major role in affecting the fragment size of transcripts with lower TIN values.<fig id="Fig3"><label>Fig. 3</label><caption><p>Evaluating TIN metric using transcript level RNA fragment size. The average RNA fragment size (y-axis) of a particular transcript was estimated from all the read pairs that uniquely mapped to the transcript (see <xref rid="Sec11" ref-type="sec">Methods</xref>). <bold>a</bold> Correlation between TIN score and transcript level RNA fragment size. A single GBM sample (SRR873822; RIN&#x02009;=&#x02009;10) was used to produce the figure. Each dot represents 50 transcripts. Red curve indicates the locally weighted polynomial regression curve. <bold>b</bold> Locally weighted polynomial regression curves for all GBM RNA-seq samples</p></caption><graphic xlink:href="12859_2016_922_Fig3_HTML" id="MO3"/></fig></p><p>As the overall RNA quality decreased, concordance between TIN and fragment size was also decreased (Additional file <xref rid="MOESM8" ref-type="media">8</xref>: Figure S5). For example, the Pearson&#x02019;s <italic>r</italic> were 0.88, 0.89, and 0.88 for three samples with RIN score of 10 whereas the Pearson&#x02019;s <italic>r</italic> were 0.66, 0.61 and 0.63 for three samples with RIN score of 6 (Additional file <xref rid="MOESM7" ref-type="media">7</xref>: Figure S4 and Additional file <xref rid="MOESM8" ref-type="media">8</xref>: Figure S5). This is because the non-linear relationship between TIN and the RNA fragment size (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>), and the correlation was mainly determined by those transcripts whose TINs were smaller than saturation point.</p></sec><sec id="Sec5"><title>Effects of transcript features on TIN score</title><p>We demonstrated that medTIN and TIN were useful metrics for assessing the RNA integrity at sample and individual transcript level, respectively. Next, we asked what characteristics of transcripts could affect the RNA degradation and thereby affect TIN score. To accomplish this, we compared the mRNA size, CDS (Coding DNA Sequence) size, 5&#x02032;UTR (5-prime Untranslated Region) size, 3&#x02032;UTR size and GC content of the transcripts to their corresponding TIN scores. We found no or very weak correlation between transcript size and TIN score in samples with high RNA integrity. However, we observed a strong negative correlation between the transcript size and TIN score for samples with lower RNA quality (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4a</xref>; Additional file <xref rid="MOESM9" ref-type="media">9</xref>: Figure S6). For example, the Pearson&#x02019;s <italic>r</italic> was 0.035, 0.059 and 0.063 for three GBM samples with RIN of 10 whereas the Pearson&#x02019;s <italic>r</italic> was -0.50, -0.51 and -0.56 for three GBM samples with RIN of 4. The Pearson&#x02019;s <italic>r</italic> was -0.72 for a sample with RIN value of 2. We observed similar trends for CDS size (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4b</xref>; Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S7), 3&#x02032;UTR size (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4c</xref>; Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Figure S8) and 5&#x02032;UTR size (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4d</xref>; Additional file <xref rid="MOESM12" ref-type="media">12</xref>: Figure S9). However, these features had weaker association with TIN when compared with that of the transcript size. The observation that larger transcripts had lower TIN scores in degraded samples suggested these transcripts were more susceptible to the in vitro degradation process. In contrast to transcript size that had negative correlation with TIN score, the GC content had positive albeit weak correlation with TIN score, suggesting GC-rich transcripts were resistant to RNA degradation (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4e</xref>; Additional file <xref rid="MOESM13" ref-type="media">13</xref>: Figure S10). This could be explained by the fact that GC base pairings are more stable than AU base pairings and transcripts with high GC content tend to have better thermodynamic stability. A similar observation was also made by another study [<xref ref-type="bibr" rid="CR17">17</xref>].<fig id="Fig4"><label>Fig. 4</label><caption><p>Correlation between TIN score and transcript features including (<bold>a</bold>) transcript size, (<bold>b</bold>) CDS size, (<bold>c</bold>) 3&#x02032;UTR size, (<bold>d</bold>) 5&#x02032;UTR size and (<bold>e</bold>) GC content. The GBM dataset was used to make these comparisons. CDS stands for coding DNA sequence. UTR stands for un-translated region</p></caption><graphic xlink:href="12859_2016_922_Fig4_HTML" id="MO4"/></fig></p></sec><sec id="Sec6"><title>Using TIN to adjust for RNA degradation in gene differential expression analysis</title><p>We first investigated if TIN metric was useful to improve gene differential expression analysis and reduce false positives. We selected 10 mCRPC samples with lower RIN (RIN<sub>mean</sub>&#x02009;=&#x02009;2.4, RIN<sub>sd</sub>&#x02009;=&#x02009;0.08) values and another 10 samples with higher RIN (RIN<sub>mean</sub>&#x02009;=&#x02009;7.1, RIN<sub>sd</sub>&#x02009;=&#x02009;1.6) values (Additional file <xref rid="MOESM14" ref-type="media">14</xref>: Table S4). All of these samples were biopsied from bone metastases and processed using the same protocol. As an independent dataset, we also selected 3 GBM samples with RIN value of 10 and 3 samples with RIN value of 4. We found that the normalized gene expression count (FPKM) did not correlate with the corresponding TIN scores in mCRPC samples with relatively higher RNA quality (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5a</xref>; Additional file <xref rid="MOESM15" ref-type="media">15</xref>: Figure S11A-J). However, FPKM values positively correlated with TIN scores in mCRPC samples with lower RNA quality (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5a</xref>; Additional file <xref rid="MOESM15" ref-type="media">15</xref>: Figure S11K-T). We could reproduce this result using the GBM data (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5b</xref>; Additional file <xref rid="MOESM16" ref-type="media">16</xref>: Figure S12). It is notable that the expression fold change between the high RIN and the low RIN samples was also significantly correlated with the TIN fold change; the Pearson&#x02019;s <italic>r</italic> were 0.45 (<italic>P</italic>&#x02009;&#x0003c;&#x02009;2.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;16</sup>) and 0.64 (<italic>P</italic>&#x02009;&#x0003c;&#x02009;2.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;16</sup>) for mCRPC and GBM data, respectively (Additional file <xref rid="MOESM17" ref-type="media">17</xref>: Figure S13).<fig id="Fig5"><label>Fig. 5</label><caption><p>Comparing Pearson correlation coefficient between gene expression (FPKM) and TIN score. <bold>a</bold> 20 mCRPC samples with 10 high RIN/TIN samples (RIN<sub>mean</sub>&#x02009;=&#x02009;7.1, RIN<sub>sd</sub>&#x02009;=&#x02009;1.6; red bars) and 10 low RIN/TIN samples (RIN<sub>mean</sub>&#x02009;=&#x02009;2.4, RIN<sub>sd</sub>&#x02009;=&#x02009;0.08; blue bars). <bold>b</bold> 6 GBM samples with 3 high RIN/TIN samples (red bars) and 3 low RIN/TIN samples (blue bars). FPKM stands for Fragments Per Kilobase of transcript per Million mapped reads</p></caption><graphic xlink:href="12859_2016_922_Fig5_HTML" id="MO5"/></fig></p><p>This dependency of gene expression values on TIN scores in low quality RNA samples, if not corrected, can increase the false positive (i.e. Type I error) rates during gene expression analysis. We corrected this bias by normalizing a gene&#x02019;s raw read count with its corresponding TIN score using a nonparametric locally weighted polynomial regression model (see <xref rid="Sec11" ref-type="sec">Methods</xref>). As expected, the <italic>loess</italic> correction procedure had little effect on good quality sample (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6a</xref>, <xref rid="Fig6" ref-type="fig">c</xref>) but effectively neutralized the dependency between read count and TIN score for low quality samples (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6b</xref>, <xref rid="Fig6" ref-type="fig">d</xref>).<fig id="Fig6"><label>Fig. 6</label><caption><p>Evaluate the effect of TIN correction on gene expression. <bold>a</bold> Smoothed scatterplot showing TIN scores and raw read counts for a sample (GSM1722952) with good RNA quality with RIN&#x02009;=&#x02009;6.7 and medTIN&#x02009;=&#x02009;71.5 (before correction), (<bold>b</bold>) Smoothed scatterplot showing TIN scores and raw read counts for a sample (GSM1722948) with poor RNA quality with RIN&#x02009;=&#x02009;2.6 and medTIN&#x02009;=&#x02009;48.9 (before correction). <bold>c</bold> Smoothed scatterplot showing TIN scores and corrected read counts (using loess regression) for the sample with good RNA quality (after correction). <bold>d</bold>. Scatterplot showing TIN scores and corrected read counts (using loess regression) for the sample with poor RNA quality (after correction). Loess and linear regression trends were indicated as yellow (solid) and red (dashed) curves, respectively</p></caption><graphic xlink:href="12859_2016_922_Fig6_HTML" id="MO6"/></fig></p><p>We then explore if we could improve gene expression analysis using TIN corrected gene expression read counts. When comparing 10 high RIN mCRPC samples to 10 low RIN mCRPC samples, we detected 665 differentially expressed genes (DEGs) using the unadjusted gene read count (Additional file <xref rid="MOESM18" ref-type="media">18</xref>: Table S5). However, we detected much less DEGs (289) when using TIN-corrected read counts (Additional file <xref rid="MOESM19" ref-type="media">19</xref>: Table S6), 172 (60&#x000a0;%) of which were also seen in the unadjusted DEG list (Additional file <xref rid="MOESM20" ref-type="media">20</xref>: Figure S14). We performed functional annotation analyses for the 665 DEGs using DAVID [<xref ref-type="bibr" rid="CR18">18</xref>]. Interestingly, &#x0201c;ribosomal protein&#x0201d; was the most enriched term (adjusted <italic>P</italic>&#x02009;=&#x02009;3.1&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;16</sup>) (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). We observed the same set of enriched terms when using DEGs detected by comparing GBM samples with RIN&#x02009;=&#x02009;10 to RIN&#x02009;=&#x02009;4 (adjusted <italic>P</italic>&#x02009;=&#x02009;1.7&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;41</sup>) (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>; Additional file <xref rid="MOESM21" ref-type="media">21</xref>: Table S7). Ribosomal RNAs were expected to be differentially expressed between high RIN samples and low RIN samples, because they were differentially degraded as reflected by the RIN scores. Therefore, most DEGs related to &#x0201c;ribosomal protein&#x0201d; were arguably the false positives due to differential RNA degradation. As a comparison, we also performed functional annotation analysis for the 289 DEGs detected from TIN-adjusted read count. The &#x0201c;<italic>ribosome</italic>&#x0201d; term was completely removed from the enrichment results and replaced with several pathways that were strongly relevant to cancer development and progression such as &#x0201c;<italic>icosanoid metabolic process</italic>&#x0201d;[<xref ref-type="bibr" rid="CR19">19</xref>], &#x0201c;<italic>fatty acid metabolic process</italic>&#x0201d; [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR21">21</xref>], and &#x0201c;<italic>prostaglandin metabolic process</italic>&#x0201d;[<xref ref-type="bibr" rid="CR22">22</xref>] (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). It is noteworthy that these cancer specific pathways were mainly contributed from the 172 common DEGs, while the &#x0201c;ribosome&#x0201d; terms were exclusively contributed from the 493 &#x0201c;unadjusted specific&#x0201d; DEGs. The &#x0201c;TIN-adjusted specific&#x0201d; 117 DEGs were enriched in other pathways that are also highly relevant to cancer, such as &#x0201c;<italic>Purine nucleotide binding proteins</italic>&#x0201d; [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR24">24</xref>] and &#x0201c;<italic>LIM domain containing proteins</italic>&#x0201d;[<xref ref-type="bibr" rid="CR25">25</xref>] (Additional file <xref rid="MOESM20" ref-type="media">20</xref>: Figure S14).<table-wrap id="Tab1"><label>Table 1</label><caption><p>Functional annotation analysis using DAVID (<ext-link ext-link-type="uri" xlink:href="http://david.abcc.ncifcrf.gov/">http://david.abcc.ncifcrf.gov/</ext-link>) for 4 lists of differentially expressed genes (DEGs)</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Term</th><th>P value</th><th>Benjamini</th></tr></thead><tbody><tr><td rowspan="10">Enriched pathways for the 665 differentially expressed genes in mCRPC samples (without TIN correction).</td><td>ribosomal protein</td><td char="." align="char">7.50E-19</td><td>3.10E-16</td></tr><tr><td>ribosome</td><td char="." align="char">1.10E-17</td><td>4.10E-15</td></tr><tr><td>structural constituent of ribosome</td><td char="." align="char">2.00E-17</td><td>1.10E-14</td></tr><tr><td>ribosomal subunit</td><td char="." align="char">3.70E-16</td><td>6.30E-14</td></tr><tr><td>cytosolic ribosome</td><td char="." align="char">1.00E-14</td><td>1.30E-12</td></tr><tr><td>translational elongation</td><td char="." align="char">9.70E-13</td><td>1.80E-09</td></tr><tr><td>large ribosomal subunit</td><td char="." align="char">5.40E-12</td><td>5.10E-10</td></tr><tr><td>ribonucleoprotein complex</td><td char="." align="char">1.20E-11</td><td>9.40E-10</td></tr><tr><td>translation</td><td char="." align="char">7.70E-10</td><td>7.10E-07</td></tr><tr><td>cytosolic large ribosomal subunit</td><td char="." align="char">7.10E-09</td><td>3.80E-07</td></tr><tr><td rowspan="10">Enriched pathways for the top 500 differentially expressed genes in human brain Glioblastoma cell line data (without TIN correction).</td><td>ribonucleoprotein</td><td char="." align="char">9.00E-44</td><td>1.70E-41</td></tr><tr><td>structural constituent of ribosome</td><td char="." align="char">3.70E-37</td><td>1.90E-34</td></tr><tr><td>ribosome</td><td char="." align="char">1.80E-34</td><td>7.40E-32</td></tr><tr><td>ribonucleoprotein complex</td><td char="." align="char">1.20E-32</td><td>2.60E-30</td></tr><tr><td>ribosomal subunit</td><td char="." align="char">8.80E-30</td><td>1.20E-27</td></tr><tr><td>translational elongation</td><td char="." align="char">2.80E-28</td><td>4.40E-25</td></tr><tr><td>translation</td><td char="." align="char">3.70E-26</td><td>2.90E-23</td></tr><tr><td>cytosolic ribosome</td><td char="." align="char">1.10E-21</td><td>9.60E-20</td></tr><tr><td>structural molecule activity</td><td char="." align="char">2.70E-20</td><td>6.80E-18</td></tr><tr><td>large ribosomal subunit</td><td char="." align="char">4.60E-20</td><td>2.80E-18</td></tr><tr><td rowspan="7">Enriched pathways for the 289 differentially expressed genes in mCRPC samples (after TIN correction).</td><td>icosanoid metabolic process</td><td char="." align="char">3.10E-05</td><td>3.60E-02</td></tr><tr><td>unsaturated fatty acid metabolic process</td><td char="." align="char">4.90E-05</td><td>2.90E-02</td></tr><tr><td>fatty acid metabolic process</td><td char="." align="char">5.60E-05</td><td>2.20E-02</td></tr><tr><td>prostaglandin metabolic process</td><td char="." align="char">9.20E-05</td><td>2.70E-02</td></tr><tr><td>prostanoid metabolic process</td><td char="." align="char">9.20E-05</td><td>2.70E-02</td></tr><tr><td>Arachidonic acid metabolism</td><td char="." align="char">7.80E-04</td><td>7.50E-02</td></tr><tr><td>PPAR signaling pathway</td><td char="." align="char">2.00E-03</td><td>9.60E-02</td></tr><tr><td rowspan="7">Enriched pathways for the 117 differentially expressed genes in mCRPC samples (using 3&#x02032; tag counting method).</td><td>protein homooligomerization</td><td char="." align="char">2.20E-03</td><td>6.00E-01</td></tr><tr><td>protein complex assembly</td><td char="." align="char">8.50E-03</td><td>8.30E-01</td></tr><tr><td>protein complex biogenesis</td><td char="." align="char">8.50E-03</td><td>8.30E-01</td></tr><tr><td>macromolecular complex assembly</td><td char="." align="char">1.40E-02</td><td>9.10E-01</td></tr><tr><td>protein oligomerization</td><td char="." align="char">1.80E-02</td><td>9.20E-01</td></tr><tr><td>macromolecular complex subunit organization</td><td char="." align="char">2.10E-02</td><td>9.20E-01</td></tr><tr><td>cellular macromolecular complex subunit organization</td><td char="." align="char">3.40E-01</td><td>1.00E&#x02009;+&#x02009;00</td></tr></tbody></table></table-wrap></p><p>We have shown that TIN correction could significantly reduce false positive DEGS. We next evaluated the performance of TIN correction on false negatives using ERCC spike-in controls from SEQC data as &#x0201c;ground truth&#x0201d;. We removed spike-in transcripts that did not have at least 5 reads in all of the samples. There were 45 transcripts with a set of predetermined fold changes (ranging from 0.67 to 4) between group A and group B. Additional 14 transcripts had identical molar concentration between the two groups. We considered the 45 transcripts as &#x0201c;true positives (TP)&#x0201d; and the 14 transcripts as &#x0201c;true negatives (TN)&#x0201d;. When TIN correction was not applied prior to gene differential expression analysis, 44 out of 45 TPs and 7 out of 14 TNs were called DEGs, resulting in a sensitivity of 0.98 and specificity of 0.5. When TIN correction was applied before gene differential expression, 40 out 45 TPs and 1 out of 14 TNs called as differentially expressed, resulting in a sensitivity of 0.89 and specificity of 0.93 (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). In essence, when using the limited number of spike-in transcripts, TIN correction prior to differential expression analysis decreased its sensitivity from 0.98 to 0.89 but dramatically increased its specificity from 0.5 to 0.93. When measuring the performance by accuracy, TIN correction improved the accuracy from 0.86 to 0.90. In addition, TIN correction moved the estimated fold changes closer to the predetermined fold changes, suggesting that the TIN correction could improve gene quantification (Additional file <xref rid="MOESM22" ref-type="media">22</xref>: Figure S15).<table-wrap id="Tab2"><label>Table 2</label><caption><p>Evaluate TIN correction using SEQC RNA-seq data with spike-in controls</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>TIN correction</th><th>Without TIN correction</th></tr></thead><tbody><tr><td>TP</td><td>40</td><td>44</td></tr><tr><td>FN</td><td>5</td><td>1</td></tr><tr><td>Sensitivity</td><td>0.89</td><td>0.98</td></tr><tr><td>TN</td><td>13</td><td>7</td></tr><tr><td>FP</td><td>1</td><td>7</td></tr><tr><td>Specificity</td><td>0.93</td><td>0.5</td></tr><tr><td>Accuracy</td><td>0.90</td><td>0.86</td></tr></tbody></table></table-wrap></p><p>The qualities of commercially available reference RNA samples used in SEQC project were presumably high. Therefore, the improvement of TIN correction was unlikely to be explained by the mitigation of RNA quality differences. However, in addition to RNA degradation, RNA-seq has many other inherent biases (such as GC content, PolyA selection, mappability, etc) that could also produce non-uniform coverage, which could partially explain the improvement after TIN correction.</p></sec><sec id="Sec7"><title>Comparing TIN correction to 3&#x02032; tag counting method</title><p>When dealing with RNA-seq data generated from low quality RNA, Sigurgeirsson et al. proposed to use 3&#x02032; tag counting (3TC) method to reduce false positives in differential expression analysis [<xref ref-type="bibr" rid="CR5">5</xref>]. To mitigate the read coverage bias effects on gene expression quantification, 3TC only considered 3&#x02032; part of the transcripts by extending <italic>N</italic> (0&#x02009;&#x02264;&#x02009;<italic>N</italic>&#x02009;&#x02264;&#x02009;transcript length) nucleotides from the 3&#x02032; end, and all bases and exons beyond <italic>N</italic> length were left out. While 3TC could reduce false positives to some extent, it also reduced statistical power and increased false negatives since only a small fraction of all mapped reads were considered. For example, for 10 mCRPC samples with high RIN values, 61.8&#x02009;&#x000b1;&#x02009;7.5&#x000a0;% of total reads was uniquely mapped to exon regions and can be used for gene expression analysis. However, if 3TC method only considered the 3&#x02032; 1 Kb region of transcript, only 26.9&#x02009;&#x000b1;&#x02009;4.2&#x000a0;% of total reads were left to use. And when considered the 3&#x02032; 250 nucleotides (see below), only 6.6&#x02009;&#x000b1;&#x02009;1.7&#x000a0;% of reads were left to use, which was equivalent to leave out 90&#x000a0;% usable reads (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7a</xref>). Because of 3&#x02032; bias, the fraction of retained reads for samples with low RIN values was significantly higher than those of high quality samples, but only 20.1&#x02009;&#x000b1;&#x02009;9.1&#x000a0;% reads were retained if 3&#x02032; 250 nucleotides were used (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7b</xref>).<fig id="Fig7"><label>Fig. 7</label><caption><p>Compare TIN correction to 3&#x02032; tag counting method (3TC). <bold>a</bold>-<bold>b</bold> Percentage of retained reads if 3&#x02032; 1 Kb, 0.5 Kb and 0.25 Kb were considered. <bold>c</bold> Reads coverage profiles for high RIN (blue) and low RIN mCRPC samples (red). All transcripts were aligned to the 3&#x02032; end (i.e transcription end site). <bold>d</bold> Venn diagram showing overlapping between DEGs detected by TIN correction and 3TC</p></caption><graphic xlink:href="12859_2016_922_Fig7_HTML" id="MO7"/></fig></p><p>For 3TC method, deciding the size of <italic>N</italic> is not straightforward: to retain statistical power, <italic>N</italic> should be as large as possible; however, coverage bias cannot be effectively removed if <italic>N</italic> is too large. To determine the proper <italic>N</italic> size, we generated read coverage profiles for 20 mCRPC samples with all expressed transcripts aligned to the 3&#x02032; end (i.e. transcription end site) (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7c</xref>). Based on Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7c</xref>, we set <italic>N</italic> to 250 and then performed gene expression analysis using the same procedure (see <xref rid="Sec11" ref-type="sec">Methods</xref>). As we expected, 3TC method detected 117 DEGs (Additional file <xref rid="MOESM23" ref-type="media">23</xref>: Table S8), a much smaller number as compared to 289 DEGs that detected with TIN correction and 665 DEGs detected without TIN correction. Although there were 29 common genes detected by both 3TC and TIN correction methods (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7d</xref>). No prostate or prostate cancer relevant pathways were enriched for the 117 DEG list (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>).</p></sec><sec id="Sec8"><title>Comparing TIN to mRIN</title><p>When writing this manuscript, we noticed another method named mRIN was also developed to directly assess mRNA integrity from RNA-seq data [<xref ref-type="bibr" rid="CR26">26</xref>]. Although conceptually similar, mRIN used a modified Kolmogorov-Smirnov (KS) statistic to quantify the 3&#x02032; bias of reads coverage while TIN used the Shannon&#x02019;s entropy. To compare the performance of medTIN and mRIN, we ran mRIN algorithm for the same 12 GBM samples. At sample level, we found medTIN score was highly correlated with mRIN score (<italic>r</italic>&#x02009;=&#x02009;0.98, <italic>P</italic>&#x02009;=&#x02009;1.7&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;8</sup>) (Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8a</xref>), suggesting the two methods agreed remarkably well despite the underlying computation approaches are different. When comparing mRIN and medTIN to Agilent&#x02019;s RIN, we found the correlation between mRIN and RIN (<italic>r</italic>&#x02009;=&#x02009;0.96, <italic>P</italic>&#x02009;=&#x02009;5.5&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;7</sup>) was slightly better than that of medTIN (<italic>r</italic>&#x02009;=&#x02009;0.93, <italic>P</italic>&#x02009;=&#x02009;9.1&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;6</sup>) (Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8b</xref>-<xref rid="Fig8" ref-type="fig">c</xref>). However, when using average RNA fragment size as a benchmark, medTIN (<italic>r</italic>&#x02009;=&#x02009;0.96, <italic>P</italic>&#x02009;=&#x02009;1.2&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;6</sup>) performed slightly better than mRIN (<italic>r</italic>&#x02009;=&#x02009;0.92, <italic>P</italic>&#x02009;=&#x02009;2.1&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;5</sup>) (Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8d</xref>-<xref rid="Fig8" ref-type="fig">e</xref>). mRIN algorithm also reported GIS (gene integrity score) for each gene. However, we were unable to compare gene level TIN score with GIS, because GIS score was calculated from all samples (in our case, the 12 GBM samples), while TIN was calculated for each gene in each sample. Although GIS is a gene-specific measurement, it is practically less useful than TIN to evaluate gene level integrity since the same gene was often degraded differently in different samples.<fig id="Fig8"><label>Fig. 8</label><caption><p>Compare median TIN score (medTIN) with mRIN using 12 GBM RNA-seq data. <bold>a</bold> Concordance between medTIN and mRIN when measuring sample level RNA integrity. <bold>b</bold>-<bold>c</bold> Compare medTIN and mRIN to Agilent&#x02019;s RIN. <bold>d</bold>-<bold>e</bold> Compare medTIN and mRIN to average RNA fragment size calculated from read pairs. <italic>r</italic> stands for Pearson&#x02019;s correlation coefficient</p></caption><graphic xlink:href="12859_2016_922_Fig8_HTML" id="MO8"/></fig></p></sec></sec><sec id="Sec9"><title>Discussion</title><p>Although TIN and Agilent&#x02019;s RIN are highly concordant, there are three major differences between them. First, RIN is a valuable approach for pre-sequencing sample screening, while TIN scores can only be calculated after RNA-seq data is produced. Second, when using RNA fragment size as a surrogate for RNA integrity to compare RIN and medTIN, we found that Agilent&#x02019;s RIN only worked well for samples with relative higher RNA integrity, as evidenced by spread of the distribution of blue circles in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2c</xref>. In contrast, medTIN was more sensitive to samples with low integrity, as demonstrated by more spread of distribution of red circles in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2d</xref>. Third, TIN provides RNA quality measurements at transcript level, which not only enables transcript level quality control, but also helps improve gene expression analysis. This is particularly useful given that different genes usually degraded differently.</p><p>Since RNA fragment size can be directly estimated from paired-end RNA-seq data, one might question the need for TIN. There are several drawbacks for measuring the RNA integrity using RNA fragment size alone. First, it can only be estimated from paired-end RNA-seq data. Second, RNA fragment size is influenced by other confounding factors such as the fragmentation and size selection steps during library preparation.</p><p>We chose 10 mCRPC samples with lower RIN/medTIN scores (low RIN group) and another 10 samples with higher RIN/medTIN scores (high RIN group) with the primary purpose of comparing &#x0201c;RNA degradation effect&#x0201d; on gene expression analysis. Unlike GBM and PBMC datasets that generated from cell lines, the mCRPC dataset was generated from real clinical tissues, and represented the genuine RNA degradation complexity and inter-tumor heterogeneity. However, this was a less than ideal dataset because: 1) these 20 clinical samples were not exact biological replicates and the pathology characteristics of these samples were slightly different (Additional file <xref rid="MOESM14" ref-type="media">14</xref>: Table S4). For example, Gleason scores were slightly lower in &#x0201c;low RIN group&#x0201d; (mean&#x02009;=&#x02009;6.9, median&#x02009;=&#x02009;7) than that of &#x0201c;high RIN group&#x0201d; (mean&#x02009;=&#x02009;7.3, median&#x02009;=&#x02009;8), even though the difference was not statistically significant (<italic>P</italic>&#x02009;=&#x02009;0.28, two-sided Wilcoxon rank sum test). This pathological differences between low and high RIN group also explained the detection of prostate cancer related DEGs. 2) Unlike SEQC which had spike-in transcripts with predetermined known expression values, there was no &#x0201c;true DEGs&#x0201d; available to accurately test the performance of TIN correction. However, we demonstrated through pathways analysis that TIN correction could remove ribosome genes and identify DEGs that related to prostate cancer.</p><p>It is known that oligo(dT) is not a ideal choice for isolating mRNA from degraded samples. Other protocols such as exome capture has been demonstrated with greatly improved performance [<xref ref-type="bibr" rid="CR27">27</xref>]. However, using oligo(dT) to isolate polyadenylated mRNA is the most widely used RNA-seq protocol especially at the early stage when more advanced protocols are not available. For example, BrainSpan (Atlas of the Developing Human Brain, <ext-link ext-link-type="uri" xlink:href="http://www.brainspan.org/">http://www.brainspan.org/</ext-link>) used oligo(dT) to deplete rRNA during RNA-seq library preparation for RNA samples collected from post-mortem tissues. Being designed to correct non-uniform coverage derived from RNA degradation as well as other biases, our TIN algorithm would be a useful approach to reanalyze or meta-analyze these RNA-seq data available from public repositories. On the other hand, even for samples with reasonable RNA integrity (eg. RIN&#x02009;=&#x02009;8), 3&#x02032; bias still persist (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1c</xref>). And we have demonstrated using the SEQC dataset that TIN could improve gene expression analysis even when the RNA quality is high.</p></sec><sec id="Sec10"><title>Conclusions</title><p>In this study, we developed TIN as a novel metric to measure RNA integrity, and demonstrated with multiple datasets that the TIN metric is not only a reliable measurement of RNA integrity in both transcriptome and transcript level, but also a valuable metric to neutralize in vitro RNA degradation effect and improve differential gene expression analysis.</p></sec><sec id="Sec11"><title>Methods</title><sec id="Sec12"><title>RNA-seq datasets</title><p>This study used a total of four datasets including three published RNA-seq datasets. All three published datasets were obtained from the NCBI Sequence Read Archive (SRA; <ext-link ext-link-type="uri" xlink:href="http://www.ncbi.nlm.nih.gov/Traces/sra/">http://www.ncbi.nlm.nih.gov/Traces/sra/</ext-link>) or Gene Expression Omnibus (GEO, <ext-link ext-link-type="uri" xlink:href="http://www.ncbi.nlm.nih.gov/geo/">http://www.ncbi.nlm.nih.gov/geo/</ext-link>). Sequencing reads from all samples were independently aligned to the human reference genome (hg19/GRCh37) using Tophat (v2.0.6) software configured with default options.<list list-type="order"><list-item><p>Human U-251 MG brain glioblastoma cell lines (GBM) [<xref ref-type="bibr" rid="CR5">5</xref>]. This dataset has 12 pair-end RNA-seq data files available under SRA accession SRP023548. Samples in this dataset have a wide range of RIN values: three samples with RIN value of 10 (SRR873838, SRR873834 and SRR873822), two samples with RIN value of 8 (SRR879615 and SRR879800), three samples with RIN value of 6 (SRR880232, SRR881272 and SRR880070), three samples with RIN value of 4 (SRR881852, SRR881451, and SRR881672) and one sample with RIN value of 2 (SRR881985). Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S1 presents the details of this dataset.</p></list-item><list-item><p>Human peripheral blood mononuclear cells (PBMC) [<xref ref-type="bibr" rid="CR4">4</xref>]. This dataset has 20 single-end RNA-seq data files available under SRA accession SRP041955. This dataset was developed to estimate the in vitro degradation at 12&#x000a0;h, 24&#x000a0;h, 48&#x000a0;h and 84&#x000a0;h. Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S2 presents the details of the samples along with their associated RIN values (varied from 2.8 to 9.4).</p></list-item><list-item><p>Sequencing quality control consortium data set (SEQC) [<xref ref-type="bibr" rid="CR28">28</xref>]. The Sequencing Quality Control Consortium analyzed samples containing reference RNA. This dataset was downloaded from NCBI Gene Expression Omnibus (GEO) with accession number GSE49712. This SEQC subset has a total of 10 samples. Group A contains 5 replicates (SRR950078, SRR950080, SRR950082, SRR950084 and SRR950086) of the Stratagene Universal Human Reference RNA (UHRR) and Group B has 5 replicates (SRR950079, SRR950081, SRR950083, SRR950085 and SRR950087) of the Ambion Human Brain Reference RNA (HBRR). ERCC (External RNA Controls Consortium) control mix was spiked in both groups at 2&#x000a0;% by volume. This control mixture contains 92 synthetic polyadenylated oligonucleotides of 250-2000 nucleotides in length, which were meant to resemble human transcripts.</p></list-item><list-item><p>Human prostate cancer tissue samples (mCRPC). This study was approved by the Mayo Clinic Institutional Review Board and conducted in accordance with the Declaration of Helsinki. We obtained a total of 120 samples from 46 castration-resistant prostate cancer patients. Out of the collected 120 samples, 62 were blood samples, 18 were metastatic rib lesion biopsies and 40 were metastatic bone tissue biopsies. Tissues were snap frozen with liquid nitrogen and RNA was harvested using Rneasy Plus Mini Kit (Qiagen). RNA libraries were prepared according to the manufacturer&#x02019;s instructions for the TruSeq RNA Sample Prep Kit v2 (Illumina, San Diego, CA). Briefly, poly-A mRNA was purified from total RNA using oligo dT magnetic beads. The purified mRNA was fragmented at 95&#x000a0;&#x000b0;C for 8&#x000a0;min and eluted from the beads. Double stranded cDNA was made using SuperScript III reverse transcriptase, random primers (Invitrogen, Carlsbad, CA) and DNA polymerase I and RNase H. The cDNA ends were repaired and an &#x0201c;A&#x0201d; base added to the 3&#x02032; ends. TruSeq paired end index DNA adaptors (Illumina, San Diego CA) with a single &#x0201c;T&#x0201d; base overhang at the 3&#x02032; end were ligated and the resulting constructs were purified using AMPure SPRI beads from Agencourt. The adapter-modified DNA fragments were enriched by 12&#x000a0;cycles of PCR using Illumina TruSeq PCR primers. The concentration and size distribution of the libraries was determined on an Agilent Bioanalyzer DNA 1000 chip and Qubit fluorometry (Invitrogen, Carlsbad, CA). Pair-end RNA sequencing was performed using Illumina HiSeq 2500. Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Table S3 presents the details of this dataset.</p></list-item></list></p></sec><sec id="Sec13"><title>Determine the RNA integrity number (RIN)</title><p>All mCRPC RNA samples were analysed by Agilent Bioanalyzer 2100 before sequencing. Based on the recorded electropherograms, RIN values were calculated according to the algorithm[<xref ref-type="bibr" rid="CR7">7</xref>] considering four features: &#x0201c;total RNA ratio&#x0201d; (i.e. the fraction of the area in the region of 18S and 28S compared to the total area under the curve), 28S-region height, 28S area ratio and the 18S:28S ratio. RIN values of GBM, PBMC and SEQC RNA samples were obtained from the original publications.</p></sec><sec id="Sec14"><title>Algorithm for computing the transcript integrity number (TIN)</title><p>We assumed that a systematic in vitro degradation of a transcript would result in areas with shallow read depths. Hence we designed the TIN metric to capture the uniformity of coverage for a given transcript. Given a transcript of <italic>n</italic> nucleotides long and its read coverage at each nucleotide is (<italic>C</italic><sub><italic>i</italic></sub>; <italic>i</italic>&#x02009;=&#x02009;1,2,&#x02026;,<italic>n</italic>). the relative coverage (<italic>P</italic><sub><italic>i</italic></sub>) of each nucleotide is calculated as:<disp-formula id="Equa"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {P}_i=\frac{Coverage\  at\ i-th\  position}{Total\  Coverage}=\frac{C_i}{{\displaystyle \sum }{C}_i} $$\end{document}</tex-math><mml:math id="M2"><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi mathvariant="italic">Coverage</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="italic">at</mml:mi><mml:mspace width="0.25em"/><mml:mi>i</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="italic">position</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">Total</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="italic">Coverage</mml:mi></mml:mrow></mml:mfrac><mml:mo>=</mml:mo><mml:mfrac><mml:msub><mml:mi>C</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mrow><mml:mstyle displaystyle="true"><mml:mo stretchy="true">&#x02211;</mml:mo></mml:mstyle><mml:msub><mml:mi>C</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2016_922_Article_Equa.gif" position="anchor"/></alternatives></disp-formula>with <italic>P</italic><sub><italic>1</italic></sub>&#x02009;+&#x02009;<italic>P</italic><sub><italic>2</italic></sub>&#x02009;+&#x02009;<italic>P</italic><sub><italic>3</italic></sub>&#x02009;+&#x02009;&#x02026;&#x02009;+&#x02009;<italic>P</italic><sub><italic>n</italic></sub>&#x02009;=&#x02009;1. The coverage evenness of a transcript can be measured by Shannon&#x02019;s entropy:<disp-formula id="Equb"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ H=-{\displaystyle {\sum}_{i=1}^n{P}_i\times log{P}_i} $$\end{document}</tex-math><mml:math id="M4"><mml:mi>H</mml:mi><mml:mo>=</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>n</mml:mi></mml:msubsup><mml:mrow><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x000d7;</mml:mo><mml:mi mathvariant="italic">log</mml:mi><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mstyle></mml:math><graphic xlink:href="12859_2016_922_Article_Equb.gif" position="anchor"/></alternatives></disp-formula></p><p>If a particular nucleotide position has no read coverage (i.e. <italic>P</italic><sub><italic>i</italic></sub>&#x02009;=&#x02009;0), the entropy <italic>H</italic>&#x02009;=&#x02009;<italic>P</italic><sub><italic>i</italic></sub>&#x02009;&#x000d7;&#x02009;log <italic>P</italic><sub><italic>i</italic></sub>&#x02009;=&#x02009;0. <italic>H</italic> is maximized if the coverage is perfectly uniform (i.e. <italic>P</italic><sub><italic>1</italic></sub>&#x02009;=&#x02009;<italic>P</italic><sub><italic>2</italic></sub>&#x02009;=&#x02009;<italic>P</italic><sub><italic>3</italic></sub>&#x02009;=&#x02009;&#x02026;&#x02009;=&#x02009;<italic>P</italic><sub><italic>n</italic></sub>&#x02009;=&#x02009;1/<italic>n</italic>) across the entire length of the transcript. For computational efficiency, we did not use the entire transcripts to calculate the <italic>H</italic>. Instead, we selected <italic>k</italic> equally spaced positions across the transcript from 5&#x02032; end (transcription start site) to 3&#x02032; end (transcription end site). <italic>k</italic> is an adjustable parameter in our TIN program. To distinguish different transcripts transcribed from the same gene locus, all the exon-exon joint positions (<italic>j</italic>) were also taken into calculation:<disp-formula id="Equc"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \widehat{H}=-{\displaystyle {\sum}_{i=1}^{\widehat{n}}{P}_i\times log{P}_i = -{\displaystyle {\sum}_{i=1}^{k+j}{P}_i\times log{P}_i}} $$\end{document}</tex-math><mml:math id="M6"><mml:mover accent="true"><mml:mi>H</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>=</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mover accent="true"><mml:mi>n</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover></mml:mrow></mml:msubsup><mml:mrow><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x000d7;</mml:mo><mml:mi mathvariant="italic">log</mml:mi><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mspace width="0.25em"/><mml:mo>=</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msubsup><mml:mrow><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x000d7;</mml:mo><mml:mi mathvariant="italic">log</mml:mi><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mstyle></mml:mrow></mml:mstyle></mml:math><graphic xlink:href="12859_2016_922_Article_Equc.gif" position="anchor"/></alternatives></disp-formula></p><p>Although Shannon&#x02019;s <italic>H</italic> is a useful index to measure the uniformity, its logarithmic scale is difficult to interpret and compare [<xref ref-type="bibr" rid="CR29">29</xref>]. We addressed this issue by converting the <italic>H</italic> index into real &#x0201c;uniformity&#x0201d; (<italic>U</italic>) as suggested by Jost et al. [<xref ref-type="bibr" rid="CR29">29</xref>]:<disp-formula id="Equd"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ U={e}^{\widehat{H}}=e\left(-{\displaystyle {\sum}_{i=1}^{k+j}{P}_i\times log{P}_{i\ }}\right) $$\end{document}</tex-math><mml:math id="M8"><mml:mi>U</mml:mi><mml:mo>=</mml:mo><mml:msup><mml:mi>e</mml:mi><mml:mover accent="true"><mml:mi>H</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover></mml:msup><mml:mo>=</mml:mo><mml:mi>e</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msubsup><mml:mrow><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x000d7;</mml:mo><mml:mi mathvariant="italic">log</mml:mi><mml:msub><mml:mi>P</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mspace width="0.25em"/></mml:mrow></mml:msub></mml:mrow></mml:mstyle></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12859_2016_922_Article_Equd.gif" position="anchor"/></alternatives></disp-formula>where <italic>U</italic> (0&#x02009;&#x02264;&#x02009;<italic>U</italic>&#x02009;&#x02264;&#x02009;(<italic>k</italic>&#x02009;+&#x02009;<italic>j</italic>)) is technically and biologically meaningful since it is equivalent to the number of nucleotides with uniform read coverage. Accordingly, the <italic>TIN</italic> score is the percentage of transcript that has uniform read coverage:<disp-formula id="Eque"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ TIN=100\times \frac{U}{\left(k+j\right)}=100\times \frac{e^{\left(-{\displaystyle {\sum}_{i=1}^{k+j}}{P}_i\times log{P}_{i\ }\right)}}{\left(k+j\right)} $$\end{document}</tex-math><mml:math id="M10"><mml:mi mathvariant="italic">TIN</mml:mi><mml:mo>=</mml:mo><mml:mn>100</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mfrac><mml:mi>U</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:mfenced></mml:mfrac><mml:mo>=</mml:mo><mml:mn>100</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mfrac><mml:msup><mml:mi>e</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:msub><mml:mi>P</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x000d7;</mml:mo><mml:mi mathvariant="italic">log</mml:mi><mml:msub><mml:mi>P</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mspace width="0.25em"/></mml:mrow></mml:msub></mml:mrow></mml:mfenced></mml:msup><mml:mfenced close=")" open="("><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:mfenced></mml:mfrac></mml:math><graphic xlink:href="12859_2016_922_Article_Eque.gif" position="anchor"/></alternatives></disp-formula></p></sec><sec id="Sec15"><title>Calculating library RNA fragment size</title><p>RNA fragment size is the natural measure of the in vitro RNA degradation. Since read pairs were sequenced from both ends of RNA (actually cDNA) fragments, the size of each RNA fragment in the sequencing library can be directly estimated from the distance between read pairs after mapping them to the reference genome. We used uniquely mapped high quality (mapq&#x02009;&#x02265;&#x02009;30) read pairs to estimate the RNA fragment size. When a read pair was mapped to the same exon, the fragment size is defined as the genomic distance covered by the two reads (i.e. distance between the &#x0201c;start&#x0201d; of the first read and &#x0201c;end&#x0201d; of the second read). When a read pair was mapped to different exons of the same gene, introns lying between the two reads were subtracted from the genomic distance covered by the read pair. We considered the longest RNA isoform when multiple splicing isoforms (exon skipping, intron retention, alternative donor/acceptor sites, etc.) exist. We removed transcripts with &#x0003c;30 mapped read-pairs to improve the reliability of library fragment size estimation. The &#x0201c;sample level&#x0201d; RNA fragment size was estimated by taking the average of fragment sizes calculated from all read pairs that uniquely mapped to the reference genome. Similarly, the &#x0201c;transcript level&#x0201d; RNA fragment size was estimated from all read pairs that specifically mapped to a transcript.</p></sec><sec id="Sec16"><title>Normalizing gene level read counts using TIN metric</title><p>For samples with poor RNA quality, both raw read counts and normalized read counts (FPKMs) were positively correlated with TIN scores (see Results). This type of in vitro degradation bias would tamper with gene expression analysis and produce significant numbers of false positives. To correct this bias, we recalibrated the gene level read count using the corresponding TIN score within each sample. In brief, gene level raw read counts <italic>y</italic><sub><italic>i</italic></sub> (<italic>i</italic>&#x02009;=&#x02009;<italic>1,2,3,&#x02026;,n. n</italic> is the total number of genes under investigation) were regressed to TIN score <italic>t</italic><sub><italic>i</italic></sub> using a locally weighted polynomial regression method. For this, we utilized the logarithmic scale of the gene-level counts because it is more robust to outliers that can bias the fit. The R function <italic>loess</italic> was used for the following function.<disp-formula id="Equf"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {y}_i^{\prime }={y}_i-{\widehat{y}}_i+ median\;\left({y}_1,{y}_2,\dots, {y}_n\right) $$\end{document}</tex-math><mml:math id="M12"><mml:msubsup><mml:mi>y</mml:mi><mml:mi>i</mml:mi><mml:mo>&#x02032;</mml:mo></mml:msubsup><mml:mo>=</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mover accent="true"><mml:mi>y</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:mi mathvariant="italic">median</mml:mi><mml:mspace width="0.12em"/><mml:mfenced close=")" open="(" separators=",,,"><mml:msub><mml:mi>y</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:msub><mml:mi>y</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>&#x02026;</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>n</mml:mi></mml:msub></mml:mfenced></mml:math><graphic xlink:href="12859_2016_922_Article_Equf.gif" position="anchor"/></alternatives></disp-formula></p><p>Where <italic>y</italic><sub arrange="stack"><italic>i</italic></sub><sup arrange="stack">&#x02032;</sup> denote the normalized read count of gene <italic>i</italic> and <italic>&#x00177;</italic><sub><italic>i</italic></sub> denote the fitted value.</p></sec><sec id="Sec17"><title>Differential expression analysis</title><p>We applied the same procedure for mCRPC dataset (compared 10 samples of lower RIN/TIN values with 10 samples of higher RIN/TIN values), GBM dataset (compared three samples with RIN&#x02009;=&#x02009;10 to three samples with RIN&#x02009;=&#x02009;4) and SEQC dataset (compared group A to group B). This method utilized edgeR (version 3.6.8) to perform differential expression analysis [<xref ref-type="bibr" rid="CR30">30</xref>]. The software was configured to use the TMM (trimmed mean of M values) method for normalizing the library depth differences between samples [<xref ref-type="bibr" rid="CR31">31</xref>]. Differential expression p-values were FDR corrected using the Benjamini-Hochberg method. Genes with an FDR of&#x02009;&#x02264;&#x02009;0.01were considered as differentially expressed between groups.</p></sec></sec><sec id="Sec18"><title>Availability of supporting data</title><p>Twenty RNA-seq data generated from metastatic prostate cancer tissues were submitted to Gene Expression Omnibus (<ext-link ext-link-type="uri" xlink:href="http://www.ncbi.nlm.nih.gov/geo/">http://www.ncbi.nlm.nih.gov/geo/</ext-link>) with accession number: GSE70285 (reviewers&#x02019; link: <ext-link ext-link-type="uri" xlink:href="http://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?token=knchmaksrfqfnov&#x00026;acc=GSE70285">http://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?token=knchmaksrfqfnov&#x00026;acc=GSE70285</ext-link>). Python Code to calculate TIN score (tin.py) is freely available from RSeQC package (<ext-link ext-link-type="uri" xlink:href="http://www.http//rseqc.sourceforge.net)">www.http://rseqc.sourceforge.net) </ext-link>[<xref ref-type="bibr" rid="CR32">32</xref>].</p></sec></body><back><app-group><app id="App1"><sec id="Sec19"><title>Additional files</title><p><media position="anchor" xlink:href="12859_2016_922_MOESM1_ESM.xls" id="MOESM1"><label>Additional file 1: Table S1.</label><caption><p>Twelve RNA-seq datasets generated from human brain Glioblastoma (GBM) cell line<sup>2</sup>. Accession number, RNA Integrity Numbers (RIN), the median Transcript Integrity Numbers (medTIN), total read pairs, read pairs with mapping quality&#x02009;&#x0003e;&#x02009;30, and number of genes with at least 10 reads are listed. (XLS 7 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM2_ESM.xls" id="MOESM2"><label>Additional file 2: Table S2.</label><caption><p>Twenty RNA-seq datasets generated from human peripheral blood mononuclear cell (PBMC)<sup>1</sup>. Accession number, RNA Integrity Numbers (RIN), and the median Transcript Integrity Numbers (medTIN), total reads, total reads with mapping quality &#x0003e;30, and number of gene with at least 10 reads are listed. The PBMC samples were stored at room temperature for 0&#x000a0;h, 12&#x000a0;h, 24&#x000a0;h, 48&#x000a0;h and 84&#x000a0;h. Each time point contains 4 individuals (replicates). (XLS 9 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM3_ESM.xls" id="MOESM3"><label>Additional file 3: Table S3.</label><caption><p>120 RNA-seq datasets generated from clinical tissues of human metastatic castration resistant prostate cancers (mCRPC). Sample ID, RNA Integrity Numbers (RIN), the median Transcript Integrity Numbers (medTIN), total read pairs, read pairs with mapping quality&#x02009;&#x0003e;&#x02009;30, and number of genes with at least 10 reads are listed. &#x0201c;B&#x0201d;&#x02009;=&#x02009;Blood, &#x0201c;T&#x0201d;&#x02009;=&#x02009;Metastatic soft tumor tissue, &#x0201c;N&#x0201d;&#x02009;=&#x02009;Metastatic bone site, &#x0201c;V1&#x0201d;&#x02009;=&#x02009;Visit 1, &#x0201c;V2&#x0201d;&#x02009;=&#x02009;Visit 2. (XLS 22 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM4_ESM.pdf" id="MOESM4"><label>Additional file 4: Figure S1.</label><caption><p>Concordance between RIN and median TIN score for 20 peripheral blood mononuclear cell (PBMC) samples [4]. The PBMC samples were stored at room temperature for 0&#x000a0;h (blue), 12&#x000a0;h (green), 24&#x000a0;h (orange), 48&#x000a0;h (purple) and 84&#x000a0;h (red). Each time point contains 4 individuals (replicates). <italic>r</italic>, Pearson correlation coefficient. (PDF 143 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM5_ESM.pdf" id="MOESM5"><label>Additional file 5: Figure S2.</label><caption><p>RIN (RNA integrity number) score distribution for 120 metastatic castration resistant prostate cancer (mCRPC) samples. (PDF 9 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM6_ESM.pdf" id="MOESM6"><label>Additional file 6: Figure S3.</label><caption><p>Evaluating RIN and median TIN score using sample level RNA fragment size as benchmark. Only 28 mCRPC samples with RIN&#x02009;&#x0003c;&#x02009;3 were used. (a) Scatterplot showing relationship between RIN and average RNA fragment size. (b) Scatterplot showing relationship between median TIN score and RNA fragment size. Linear regression lines fitted to data are indicated as black dashed lines. (PDF 186 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM7_ESM.pdf" id="MOESM7"><label>Additional file 7: Figure S4.</label><caption><p>Evaluating TIN (x-axis) metric using transcript level RNA fragment size (y-axis) for 12 Glioblastoma (GBM) samples [5]. (a)-(c) Three samples with RIN value of 10 (red); (d)-(e), two samples with RIN value of 8 (purple); (f)-(h) three samples with RIN value of 6 (orange); (i)-(k) three samples with RIN value of 4 (blue); (l) one sample with RIN value of 2 (cyan). Each dot represents 50 transcripts. Black curves indicate locally weighted polynomial regression curves. <italic>r</italic>, Pearson correlation coefficient. (PDF 1490 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM8_ESM.pdf" id="MOESM8"><label>Additional file 8: Figure S5.</label><caption><p>Barplot showing Pearson correlation coefficients between TIN and RNA fragment size. 12 Glioblastoma (GBM) samples were stratified by RIN score; RIN&#x02009;=&#x02009;10 (red), RIN&#x02009;=&#x02009;8 (purple), RIN&#x02009;=&#x02009;6 (orange), RIN&#x02009;=&#x02009;4 (blue) and RIN&#x02009;=&#x02009;2 (cyan). (PDF 120 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM9_ESM.pdf" id="MOESM9"><label>Additional file 9: Figure S6.</label><caption><p>Smoothed scatter plots showing correlation between TIN score and transcript size. (a)-(c) three samples with RIN value of 10; (d)-(e), two samples with RIN value of 8; (f)-(h) three samples with RIN value of 6; (i)-(k) three samples with RIN value of 4; (l) one sample with RIN value of 2. Blue, orange and red represents low, median and high density of data points, respectively. Transcripts with no read coverage or smaller then 100 nucleotide were removed. <italic>r</italic>, Pearson correlation coefficient. Linear regression lines fitted to data are indicated as black dashed lines. (PDF 6875 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM10_ESM.pdf" id="MOESM10"><label>Additional file 10: Figure S7.</label><caption><p>Relationship between CDS (coding DNA sequence) size and TIN score for 12 Glioblastoma (GBM) samples. (a)-(c) three samples with RIN value of 10; (d)-(e), two samples with RIN value of 8; (f)-(h) three samples with RIN value of 6; (i)-(k) three samples with RIN value of 4; (l) one sample with RIN value of 2. <italic>r</italic>, Pearson correlation coefficient. Linear regression lines fitted to data are indicated as black dashed lines. (PDF 7104 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM11_ESM.pdf" id="MOESM11"><label>Additional file 11: Figure S8.</label><caption><p>Relationship between 3&#x02032;UTR (untranslated regoin) size and TIN score for 12 Glioblastoma (GBM) samples. (a)-(c) three samples with RIN value of 10; (d)-(e), two samples with RIN value of 8; (f)-(h) three samples with RIN value of 6; (i)-(k) three samples with RIN value of 4; (l) one sample with RIN value of 2. <italic>r</italic>, Pearson correlation coefficient. <italic>r</italic>, Pearson correlation coefficient. Linear regression lines fitted to data are indicated as black dashed lines. (PDF 7201 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM12_ESM.pdf" id="MOESM12"><label>Additional file 12: Figure S9.</label><caption><p>Relationship between 5&#x02032;UTR (untranslated regoin) size and TIN score for 12 Glioblastoma (GBM) samples. (a)-(c) three samples with RIN value of 10; (d)-(e), two samples with RIN value of 8; (f)-(h) three samples with RIN value of 6; (i)-(k) three samples with RIN value of 4; (l) one sample with RIN value of 2. <italic>r</italic>, Pearson correlation coefficient. <italic>r</italic>, Pearson correlation coefficient. Linear regression lines fitted to data are indicated as black dashed lines. (PDF 7140 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM13_ESM.pdf" id="MOESM13"><label>Additional file 13: Figure S10.</label><caption><p>Relationship between GC content (GC-ratio) and TIN score for 12 Glioblastome cell line samples. (a)-(c) three samples with RIN value of 10; (d)-(e), two samples with RIN value of 8; (f)-(h) three samples with RIN value of 6; (i)-(k) three samples with RIN value of 4; (l) one sample with RIN value of 2. <italic>r</italic>, Pearson correlation coefficient. <italic>r</italic>, Pearson correlation coefficient. Linear regression lines fitted to data are indicated as black dashed lines. (PDF 6617 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM14_ESM.xls" id="MOESM14"><label>Additional file 14: Table S4.</label><caption><p>List of 10 low RIN/medTIN mCRPC and 10 higher RIN/medTIN mCRPC samples used for differential expression analysis. &#x0201c;N&#x0201d;&#x02009;=&#x02009;Metastatic bone site, &#x0201c;V1&#x0201d;&#x02009;=&#x02009;Visit 1. Whole datasets are available with accession # GSM1722952. (XLS 97 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM15_ESM.pdf" id="MOESM15"><label>Additional file 15: Figure S11.</label><caption><p>Dependency between FPKM (y-axis) and TIN scores (x-axis) for 20 mCRPC samples. (a)-(j) 10 high RIN/medTIN mCRPC samples. (k)-(t) 10 low RIN/medTIN mCRPC samples. FPKM, Fragment Per Kilobase exon per Million mapped reads. <italic>r</italic>, Pearson correlation coefficient. (PDF 12400 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM16_ESM.pdf" id="MOESM16"><label>Additional file 16: Figure S12.</label><caption><p>Dependency between FPKM (y-axis) and TIN scores (x-axis) for all 6 Glioblastoma (GBM) samples. (a)-(c) 3 GBM samples with RIN value of 10. (d)-(f) 3 GBM samples with RIN value of 4. FPKM, Fragment Per Kilobase exon per Million mapped reads. <italic>r</italic>, Pearson correlation coefficient. Linear regression lines fitted to data are indicated as red dashed lines. (PDF 3998 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM17_ESM.pdf" id="MOESM17"><label>Additional file 17: Figure S13.</label><caption><p>Relationship between expression fold change measured by log2 (FPKM) and TIN fold change. (a) mCRPC dataset. (b) GBM dataset. Linear regression lines fitted to data are indicated as red dashed lines. <italic>r</italic>, Pearson correlation coefficient. (PDF 1315 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM18_ESM.xls" id="MOESM18"><label>Additional file 18: Table S5.</label><caption><p>edgeR detected 665 differentially expressed genes (FDR cutoff&#x02009;=&#x02009;0.01) in mCRPC samples (without TIN correction). FC&#x02009;=&#x02009;Fold Change; CPM&#x02009;=&#x02009;Count Per Million; FDR&#x02009;=&#x02009;False Discovery Rate. (XLS 97 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM19_ESM.xls" id="MOESM19"><label>Additional file 19: Table S6.</label><caption><p>edgeR detected 289 differentially expressed genes (FDR cutoff&#x02009;=&#x02009;0.01) in mCRPC samples (after TIN correction). FC&#x02009;=&#x02009;Fold Change; CPM&#x02009;=&#x02009;Count Per Million; FDR&#x02009;=&#x02009;False Discovery Rate. (XLS 45 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM20_ESM.pdf" id="MOESM20"><label>Additional file 20: Figure S14.</label><caption><p>Venn diagram showing the overlapping between 665 DEGs (before TIN correction) and 289 DEGs (after TIN correction). DEG, differentially expressed gene. (PDF 101 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM21_ESM.xls" id="MOESM21"><label>Additional file 21: Table S7.</label><caption><p>edgeR detected top 1000 differentially expressed genes (FDR cutoff&#x02009;=&#x02009;0.01) in GBM samples without TIN correction. FC&#x02009;=&#x02009;Fold Change; CPM&#x02009;=&#x02009;Count Per Million; FDR&#x02009;=&#x02009;False Discovery Rate. (XLS 143 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM22_ESM.pdf" id="MOESM22"><label>Additional file 22: Figure S15.</label><caption><p>Comparing fold change estimated from RNA-seq data to predetermined fold change (red dashed line). A total of 15 genes with predetermined fold change of 4 were considered. (PDF 95 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_922_MOESM23_ESM.xls" id="MOESM23"><label>Additional file 23: Table S8.</label><caption><p>edgeR detected top 117 differentially expressed genes (FDR cutoff&#x02009;=&#x02009;0.01) in GBM samples using 3&#x02032; count method (3TC). FC&#x02009;=&#x02009;Fold Change; CPM&#x02009;=&#x02009;Count Per Million; FDR&#x02009;=&#x02009;False Discovery Rate. (XLS 22 kb)</p></caption></media></p></sec></app></app-group><glossary><title>Abbreviations</title><def-list><def-item><term>CDS</term><def><p>coding DNA sequence</p></def></def-item><def-item><term>DEG</term><def><p>differentially expression gene</p></def></def-item><def-item><term>ERCC</term><def><p>external RNA controls consortium</p></def></def-item><def-item><term>FFPE</term><def><p>formalin-fixed, paraffin-embedded</p></def></def-item><def-item><term>FPKM</term><def><p>fragments per kilobase of transcript per million mapped reads</p></def></def-item><def-item><term>GBM</term><def><p>glioblastoma</p></def></def-item><def-item><term>GEO</term><def><p>gene expression omnibus</p></def></def-item><def-item><term>HBRR</term><def><p>ambion human brain reference rna</p></def></def-item><def-item><term>mCRPC</term><def><p>metastatic castration resistant prostate cancer</p></def></def-item><def-item><term>PBMC</term><def><p>peripheral blood mononuclear cell</p></def></def-item><def-item><term>RIN</term><def><p>RNA integrity number</p></def></def-item><def-item><term>SEQC</term><def><p>sequencing quality control consortium</p></def></def-item><def-item><term>SRA</term><def><p>sequencer read archive</p></def></def-item><def-item><term>TES</term><def><p>transcription end site</p></def></def-item><def-item><term>TIN</term><def><p>transcript integrity number</p></def></def-item><def-item><term>TSS</term><def><p>transcript start site</p></def></def-item><def-item><term>UHRR</term><def><p>stratagene universal human reference rna</p></def></def-item><def-item><term>UTR</term><def><p>un-translated region</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Competing interests</bold></p><p>The authors declare no competing financial interests.</p></fn><fn><p><bold>Authors&#x02019; contributions</bold></p><p>LW1, JPK and HM conceived of the project. LW1, JPK, SD and JN wrote the manuscript. JN, HS, YL, JEE, PTV and PB collected and analysed the RNA-seq data. LW2, JJ, RW, HH, HM supervised and generated the mCRPC RNA-seq data. LW1: Liguo Wang, LW: Liewei Wang. All authors read and approved the final manuscript.</p></fn></fn-group><ack><title>Acknowledgements</title><p>This work is support by the Mayo Clinic Center for Individualized Medicine; A.T. Suharya and Ghan D.H.; Joseph and Gail Gassner; and Mayo Clinic Schulze Cancer for Novel Therapeutics in Cancer Research [grant number MC1351 to M.K]; National Institutes of Health [grant numbers CA134514, CA130908 to H.H.]. Other contributing groups include the Mayo Clinic Cancer Center and the Pharmacogenomics Research Network (PGRN).</p></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>von Ahlfen</surname><given-names>S</given-names></name><name><surname>Missel</surname><given-names>A</given-names></name><name><surname>Bendrat</surname><given-names>K</given-names></name><name><surname>Schlumpberger</surname><given-names>M</given-names></name></person-group><article-title>Determinants of RNA quality from FFPE samples</article-title><source>PLoS One</source><year>2007</year><volume>2</volume><fpage>e1261</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0001261</pub-id><pub-id pub-id-type="pmid">18060057</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Masuda</surname><given-names>N</given-names></name><name><surname>Ohnishi</surname><given-names>T</given-names></name><name><surname>Kawamoto</surname><given-names>S</given-names></name><name><surname>Monden</surname><given-names>M</given-names></name><name><surname>Okubo</surname><given-names>K</given-names></name></person-group><article-title>Analysis of chemical modification of RNA from formalin-fixed samples and optimization of molecular biology applications for such samples</article-title><source>Nucleic Acids Res</source><year>1999</year><volume>27</volume><fpage>4436</fpage><lpage>4443</lpage><pub-id pub-id-type="doi">10.1093/nar/27.22.4436</pub-id><?supplied-pmid 10536153?><pub-id pub-id-type="pmid">10536153</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Botling</surname><given-names>J</given-names></name><name><surname>Edlund</surname><given-names>K</given-names></name><name><surname>Segersten</surname><given-names>U</given-names></name><name><surname>Tahmasebpoor</surname><given-names>S</given-names></name><name><surname>Engstr&#x000f6;m</surname><given-names>M</given-names></name><name><surname>Sundstr&#x000f6;m</surname><given-names>M</given-names></name><etal/></person-group><article-title>Impact of thawing on RNA integrity and gene expression analysis in fresh frozen tissue</article-title><source>Diagn Mol Pathol</source><year>2009</year><volume>18</volume><fpage>44</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1097/PDM.0b013e3181857e92</pub-id><?supplied-pmid 19214109?><pub-id pub-id-type="pmid">19214109</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gallego Romero</surname><given-names>I</given-names></name><name><surname>Pai</surname><given-names>AA</given-names></name><name><surname>Tung</surname><given-names>J</given-names></name><name><surname>Gilad</surname><given-names>Y</given-names></name></person-group><article-title>RNA-seq: impact of RNA degradation on transcript quantification</article-title><source>BMC Biol</source><year>2014</year><volume>12</volume><fpage>42</fpage><pub-id pub-id-type="doi">10.1186/1741-7007-12-42</pub-id><?supplied-pmid 24885439?><pub-id pub-id-type="pmid">24885439</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sigurgeirsson</surname><given-names>B</given-names></name><name><surname>Emanuelsson</surname><given-names>O</given-names></name><name><surname>Lundeberg</surname><given-names>J</given-names></name></person-group><article-title>Sequencing degraded RNA addressed by 3&#x02032; tag counting</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><fpage>e91851</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0091851</pub-id><?supplied-pmid 24632678?><pub-id pub-id-type="pmid">24632678</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Opitz</surname><given-names>L</given-names></name><name><surname>Salinas-Riester</surname><given-names>G</given-names></name><name><surname>Grade</surname><given-names>M</given-names></name><name><surname>Jung</surname><given-names>K</given-names></name><name><surname>Jo</surname><given-names>P</given-names></name><name><surname>Emons</surname><given-names>G</given-names></name><etal/></person-group><article-title>Impact of RNA degradation on gene expression profiling</article-title><source>BMC Med Genomics</source><year>2010</year><volume>3</volume><fpage>36</fpage><pub-id pub-id-type="doi">10.1186/1755-8794-3-36</pub-id><?supplied-pmid 20696062?><pub-id pub-id-type="pmid">20696062</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schroeder</surname><given-names>A</given-names></name><name><surname>Mueller</surname><given-names>O</given-names></name><name><surname>Stocker</surname><given-names>S</given-names></name><name><surname>Salowsky</surname><given-names>R</given-names></name><name><surname>Leiber</surname><given-names>M</given-names></name><name><surname>Gassmann</surname><given-names>M</given-names></name><etal/></person-group><article-title>The RIN: an RNA integrity number for assigning integrity values to RNA measurements</article-title><source>BMC Mol Biol</source><year>2006</year><volume>7</volume><fpage>3</fpage><pub-id pub-id-type="doi">10.1186/1471-2199-7-3</pub-id><?supplied-pmid 16448564?><pub-id pub-id-type="pmid">16448564</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yang</surname><given-names>E</given-names></name><name><surname>van Nimwegen</surname><given-names>E</given-names></name><name><surname>Zavolan</surname><given-names>M</given-names></name><name><surname>Rajewsky</surname><given-names>N</given-names></name><name><surname>Schroeder</surname><given-names>M</given-names></name><name><surname>Magnasco</surname><given-names>M</given-names></name><etal/></person-group><article-title>Decay rates of human mRNAs: correlation with functional characteristics and sequence attributes</article-title><source>Genome Res</source><year>2003</year><volume>13</volume><fpage>1863</fpage><lpage>1872</lpage><pub-id pub-id-type="doi">10.1101/gr.997703</pub-id><?supplied-pmid 12902380?><pub-id pub-id-type="pmid">12902380</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Beelman</surname><given-names>CA</given-names></name><name><surname>Parker</surname><given-names>R</given-names></name></person-group><article-title>Degradation of mRNA in eukaryotes</article-title><source>Cell</source><year>1995</year><volume>81</volume><fpage>179</fpage><lpage>183</lpage><pub-id pub-id-type="doi">10.1016/0092-8674(95)90326-7</pub-id><?supplied-pmid 7736570?><pub-id pub-id-type="pmid">7736570</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>van Hoof</surname><given-names>A</given-names></name><name><surname>Parker</surname><given-names>R</given-names></name></person-group><article-title>The exosome: a proteasome for RNA?</article-title><source>Cell</source><year>1999</year><volume>99</volume><fpage>347</fpage><lpage>350</lpage><pub-id pub-id-type="doi">10.1016/S0092-8674(00)81520-2</pub-id><?supplied-pmid 10571176?><pub-id pub-id-type="pmid">10571176</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Houseley</surname><given-names>J</given-names></name><name><surname>Tollervey</surname><given-names>D</given-names></name></person-group><article-title>The many pathways of RNA degradation</article-title><source>Cell</source><year>2009</year><volume>136</volume><fpage>763</fpage><lpage>776</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2009.01.019</pub-id><?supplied-pmid 19239894?><pub-id pub-id-type="pmid">19239894</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Garneau</surname><given-names>NL</given-names></name><name><surname>Wilusz</surname><given-names>J</given-names></name><name><surname>Wilusz</surname><given-names>CJ</given-names></name></person-group><article-title>The highways and byways of mRNA decay</article-title><source>Nat Rev Mol Cell Biol</source><year>2007</year><volume>8</volume><fpage>113</fpage><lpage>126</lpage><pub-id pub-id-type="doi">10.1038/nrm2104</pub-id><?supplied-pmid 17245413?><pub-id pub-id-type="pmid">17245413</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Adiconis</surname><given-names>X</given-names></name><name><surname>Borges-Rivera</surname><given-names>D</given-names></name><name><surname>Satija</surname><given-names>R</given-names></name><name><surname>DeLuca</surname><given-names>DS</given-names></name><name><surname>Busby</surname><given-names>MA</given-names></name><name><surname>Berlin</surname><given-names>AM</given-names></name><etal/></person-group><article-title>Comparative analysis of RNA sequencing methods for degraded or low-input samples</article-title><source>Nat Methods</source><year>2013</year><volume>10</volume><fpage>623</fpage><lpage>629</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2483</pub-id><?supplied-pmid 23685885?><pub-id pub-id-type="pmid">23685885</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brisco</surname><given-names>MJ</given-names></name><name><surname>Morley</surname><given-names>AA</given-names></name></person-group><article-title>Quantification of RNA integrity and its use for measurement of transcript number</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><fpage>e144</fpage><pub-id pub-id-type="doi">10.1093/nar/gks588</pub-id><?supplied-pmid 22735698?><pub-id pub-id-type="pmid">22735698</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bauer</surname><given-names>M</given-names></name><name><surname>Polzin</surname><given-names>S</given-names></name><name><surname>Patzelt</surname><given-names>D</given-names></name></person-group><article-title>Quantification of RNA degradation by semi-quantitative duplex and competitive RT-PCR: a possible indicator of the age of bloodstains?</article-title><source>Forensic Sci Int</source><year>2003</year><volume>138</volume><fpage>94</fpage><lpage>103</lpage><pub-id pub-id-type="doi">10.1016/j.forsciint.2003.09.008</pub-id><?supplied-pmid 14642725?><pub-id pub-id-type="pmid">14642725</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gong</surname><given-names>X</given-names></name><name><surname>Tao</surname><given-names>R</given-names></name><name><surname>Li</surname><given-names>Z</given-names></name></person-group><article-title>Quantification of RNA damage by reverse transcription polymerase chain reactions</article-title><source>Anal Biochem</source><year>2006</year><volume>357</volume><fpage>58</fpage><lpage>67</lpage><pub-id pub-id-type="doi">10.1016/j.ab.2006.06.025</pub-id><?supplied-pmid 16860776?><pub-id pub-id-type="pmid">16860776</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Duan</surname><given-names>J</given-names></name><name><surname>Shi</surname><given-names>J</given-names></name><name><surname>Ge</surname><given-names>X</given-names></name><name><surname>D&#x000f6;lken</surname><given-names>L</given-names></name><name><surname>Moy</surname><given-names>W</given-names></name><name><surname>He</surname><given-names>D</given-names></name><etal/></person-group><article-title>Genome-wide survey of interindividual differences of RNA stability in human lymphoblastoid cell lines</article-title><source>Sci Rep</source><year>2013</year><volume>3</volume><fpage>1318</fpage><?supplied-pmid 23422947?><pub-id pub-id-type="pmid">23422947</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>DW</given-names></name><name><surname>Sherman</surname><given-names>BT</given-names></name><name><surname>Lempicki</surname><given-names>RA</given-names></name></person-group><article-title>Systematic and integrative analysis of large gene lists using DAVID bioinformatics resources</article-title><source>Nat Protoc</source><year>2009</year><volume>4</volume><fpage>44</fpage><lpage>57</lpage><pub-id pub-id-type="doi">10.1038/nprot.2008.211</pub-id><pub-id pub-id-type="pmid">19131956</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nie</surname><given-names>D</given-names></name><name><surname>Che</surname><given-names>M</given-names></name><name><surname>Grignon</surname><given-names>D</given-names></name><name><surname>Tang</surname><given-names>K</given-names></name><name><surname>Honn</surname><given-names>KV</given-names></name></person-group><article-title>Role of eicosanoids in prostate cancer progression</article-title><source>Cancer Metastasis Rev</source><year>2001</year><volume>20</volume><fpage>195</fpage><lpage>206</lpage><pub-id pub-id-type="doi">10.1023/A:1015579209850</pub-id><?supplied-pmid 12085962?><pub-id pub-id-type="pmid">12085962</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>Y</given-names></name></person-group><article-title>Fatty acid oxidation is a dominant bioenergetic pathway in prostate cancer</article-title><source>Prostate Cancer Prostatic Dis</source><year>2006</year><volume>9</volume><fpage>230</fpage><lpage>234</lpage><pub-id pub-id-type="doi">10.1038/sj.pcan.4500879</pub-id><?supplied-pmid 16683009?><pub-id pub-id-type="pmid">16683009</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Baron</surname><given-names>A</given-names></name><name><surname>Migita</surname><given-names>T</given-names></name><name><surname>Tang</surname><given-names>D</given-names></name><name><surname>Loda</surname><given-names>M</given-names></name></person-group><article-title>Fatty acid synthase: a metabolic oncogene in prostate cancer?</article-title><source>J Cell Biochem</source><year>2004</year><volume>91</volume><fpage>47</fpage><lpage>53</lpage><pub-id pub-id-type="doi">10.1002/jcb.10708</pub-id><?supplied-pmid 14689581?><pub-id pub-id-type="pmid">14689581</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Moreno</surname><given-names>J</given-names></name><name><surname>Krishnan</surname><given-names>AV</given-names></name><name><surname>Swami</surname><given-names>S</given-names></name><name><surname>Nonn</surname><given-names>L</given-names></name><name><surname>Peehl</surname><given-names>DM</given-names></name><name><surname>Feldman</surname><given-names>D</given-names></name></person-group><article-title>Regulation of prostaglandin metabolism by calcitriol attenuates growth stimulation in prostate cancer cells</article-title><source>Cancer Res</source><year>2005</year><volume>65</volume><fpage>7917</fpage><lpage>7925</lpage><pub-id pub-id-type="doi">10.1158/0008-5472.CAN-05-0884</pub-id><?supplied-pmid 16140963?><pub-id pub-id-type="pmid">16140963</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wierenga</surname><given-names>RK</given-names></name><name><surname>Hol</surname><given-names>WG</given-names></name></person-group><article-title>Predicted nucleotide-binding properties of p21 protein and its cancer-associated variant</article-title><source>Nature</source><year>1983</year><volume>302</volume><fpage>842</fpage><lpage>844</lpage><pub-id pub-id-type="doi">10.1038/302842a0</pub-id><?supplied-pmid 6843652?><pub-id pub-id-type="pmid">6843652</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fukumoto</surname><given-names>M</given-names></name><name><surname>Amanuma</surname><given-names>T</given-names></name><name><surname>Kuwahara</surname><given-names>Y</given-names></name><name><surname>Shimura</surname><given-names>T</given-names></name><name><surname>Suzuki</surname><given-names>M</given-names></name><name><surname>Mori</surname><given-names>S</given-names></name><etal/></person-group><article-title>Guanine nucleotide-binding protein 1 is one of the key molecules contributing to cancer cell radioresistance</article-title><source>Cancer Sci</source><year>2014</year><volume>105</volume><fpage>1351</fpage><lpage>1359</lpage><pub-id pub-id-type="doi">10.1111/cas.12489</pub-id><?supplied-pmid 25098609?><pub-id pub-id-type="pmid">25098609</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Matthews</surname><given-names>JM</given-names></name><name><surname>Lester</surname><given-names>K</given-names></name><name><surname>Joseph</surname><given-names>S</given-names></name><name><surname>Curtis</surname><given-names>DJ</given-names></name></person-group><article-title>LIM-domain-only proteins in cancer</article-title><source>Nat Rev Cancer</source><year>2013</year><volume>13</volume><fpage>111</fpage><lpage>122</lpage><pub-id pub-id-type="doi">10.1038/nrc3418</pub-id><?supplied-pmid 23303138?><pub-id pub-id-type="pmid">23303138</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Feng</surname><given-names>H</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>Zhang</surname><given-names>C</given-names></name></person-group><article-title>mRIN for direct assessment of genome-wide and gene-specific mRNA integrity from large-scale RNA-sequencing data</article-title><source>Nat Commun</source><year>2015</year><volume>6</volume><fpage>7816</fpage><pub-id pub-id-type="doi">10.1038/ncomms8816</pub-id><?supplied-pmid 26234653?><pub-id pub-id-type="pmid">26234653</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cieslik</surname><given-names>M</given-names></name><name><surname>Chugh</surname><given-names>R</given-names></name><name><surname>Wu</surname><given-names>Y-M</given-names></name><name><surname>Wu</surname><given-names>M</given-names></name><name><surname>Brennan</surname><given-names>C</given-names></name><name><surname>Lonigro</surname><given-names>R</given-names></name><etal/></person-group><article-title>The use of exome capture RNA-seq for highly degraded RNA with application to clinical cancer sequencing</article-title><source>Genome Res</source><year>2015</year><volume>25</volume><fpage>1372</fpage><lpage>1381</lpage><pub-id pub-id-type="doi">10.1101/gr.189621.115</pub-id><?supplied-pmid 26253700?><pub-id pub-id-type="pmid">26253700</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>SEQC/MAQC-III Consortium</collab></person-group><article-title>A comprehensive assessment of RNA-seq accuracy, reproducibility and information content by the Sequencing Quality Control Consortium</article-title><source>Nat Biotechnol</source><year>2014</year><volume>32</volume><fpage>903</fpage><lpage>914</lpage><pub-id pub-id-type="doi">10.1038/nbt.2957</pub-id><pub-id pub-id-type="pmid">25150838</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jost</surname><given-names>L</given-names></name></person-group><article-title>Entropy and diversity</article-title><source>Oikos</source><year>2006</year><volume>113</volume><issue>2</issue><fpage>363</fpage><lpage>375</lpage><pub-id pub-id-type="doi">10.1111/j.2006.0030-1299.14714.x</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robinson</surname><given-names>MD</given-names></name><name><surname>McCarthy</surname><given-names>DJ</given-names></name><name><surname>Smyth</surname><given-names>GK</given-names></name></person-group><article-title>edgeR: a Bioconductor package for differential expression analysis of digital gene expression data</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><fpage>139</fpage><lpage>140</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btp616</pub-id><?supplied-pmid 19910308?><pub-id pub-id-type="pmid">19910308</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robinson</surname><given-names>MD</given-names></name><name><surname>Oshlack</surname><given-names>A</given-names></name></person-group><article-title>A scaling normalization method for differential expression analysis of RNA-seq data</article-title><source>Genome Biol</source><year>2010</year><volume>11</volume><fpage>R25</fpage><pub-id pub-id-type="doi">10.1186/gb-2010-11-3-r25</pub-id><?supplied-pmid 20196867?><pub-id pub-id-type="pmid">20196867</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>L</given-names></name><name><surname>Wang</surname><given-names>S</given-names></name><name><surname>Li</surname><given-names>W</given-names></name></person-group><article-title>RSeQC: quality control of RNA-seq experiments</article-title><source>Bioinformatics</source><year>2012</year><volume>28</volume><fpage>2184</fpage><lpage>2185</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bts356</pub-id><?supplied-pmid 22743226?><pub-id pub-id-type="pmid">22743226</pub-id></element-citation></ref></ref-list></back></article>