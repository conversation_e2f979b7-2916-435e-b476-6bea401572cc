<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="other"><?properties open_access?><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with OASIS Tables with MathML3 v1.1 20151215//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-archive-oasis-article1-mathml3.dtd?><?SourceDTD.Version 1.1?><?ConverterInfo.XSLTName jpoasis-nisons2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Med Phys</journal-id><journal-id journal-id-type="iso-abbrev">Med Phys</journal-id><journal-id journal-id-type="doi">10.1002/(ISSN)2473-4209</journal-id><journal-id journal-id-type="publisher-id">MP</journal-id><journal-title-group><journal-title>Medical Physics</journal-title></journal-title-group><issn pub-type="ppub">0094-2405</issn><issn pub-type="epub">2473-4209</issn><publisher><publisher-name>John Wiley and Sons Inc.</publisher-name><publisher-loc>Hoboken</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7722027</article-id><article-id pub-id-type="pmid">32749075</article-id><article-id pub-id-type="doi">10.1002/mp.14424</article-id><article-id pub-id-type="publisher-id">MP14424</article-id><article-categories><subj-group subj-group-type="overline"><subject>Medical Physics Dataset Article</subject></subj-group><subj-group subj-group-type="heading"><subject>Medical Physics Dataset Articles</subject></subj-group></article-categories><title-group><article-title>PleThora: Pleural effusion and thoracic cavity segmentations in diseased lungs for benchmarking chest CT processing pipelines</article-title><alt-title alt-title-type="right-running-head">PleThora</alt-title><alt-title alt-title-type="left-running-head">Kiser <italic>et al</italic>.</alt-title></title-group><contrib-group><contrib id="mp14424-cr-0001" contrib-type="author" corresp="yes"><name><surname>Kiser</surname><given-names>Kendall J.</given-names></name><xref ref-type="aff" rid="mp14424-aff-0001">
<sup>1</sup>
</xref><xref ref-type="aff" rid="mp14424-aff-0002">
<sup>2</sup>
</xref><xref ref-type="aff" rid="mp14424-aff-0003">
<sup>3</sup>
</xref><address><email><EMAIL></email></address></contrib><contrib id="mp14424-cr-0002" contrib-type="author"><name><surname>Ahmed</surname><given-names>Sara</given-names></name><xref ref-type="aff" rid="mp14424-aff-0003">
<sup>3</sup>
</xref></contrib><contrib id="mp14424-cr-0003" contrib-type="author"><name><surname>Stieb</surname><given-names>Sonja</given-names></name><xref ref-type="aff" rid="mp14424-aff-0003">
<sup>3</sup>
</xref></contrib><contrib id="mp14424-cr-0004" contrib-type="author"><name><surname>Mohamed</surname><given-names>Abdallah S. R.</given-names></name><xref ref-type="aff" rid="mp14424-aff-0003">
<sup>3</sup>
</xref><xref ref-type="aff" rid="mp14424-aff-0004">
<sup>4</sup>
</xref></contrib><contrib id="mp14424-cr-0005" contrib-type="author"><name><surname>Elhalawani</surname><given-names>Hesham</given-names></name><xref ref-type="aff" rid="mp14424-aff-0005">
<sup>5</sup>
</xref></contrib><contrib id="mp14424-cr-0006" contrib-type="author"><name><surname>Park</surname><given-names>Peter Y. S.</given-names></name><xref ref-type="aff" rid="mp14424-aff-0006">
<sup>6</sup>
</xref></contrib><contrib id="mp14424-cr-0007" contrib-type="author"><name><surname>Doyle</surname><given-names>Nathan S.</given-names></name><xref ref-type="aff" rid="mp14424-aff-0006">
<sup>6</sup>
</xref></contrib><contrib id="mp14424-cr-0008" contrib-type="author"><name><surname>Wang</surname><given-names>Brandon J.</given-names></name><xref ref-type="aff" rid="mp14424-aff-0006">
<sup>6</sup>
</xref></contrib><contrib id="mp14424-cr-0009" contrib-type="author"><name><surname>Barman</surname><given-names>Arko</given-names></name><xref ref-type="aff" rid="mp14424-aff-0002">
<sup>2</sup>
</xref></contrib><contrib id="mp14424-cr-0010" contrib-type="author"><name><surname>Li</surname><given-names>Zhao</given-names></name><xref ref-type="aff" rid="mp14424-aff-0002">
<sup>2</sup>
</xref></contrib><contrib id="mp14424-cr-0011" contrib-type="author"><name><surname>Zheng</surname><given-names>W. Jim</given-names></name><xref ref-type="aff" rid="mp14424-aff-0002">
<sup>2</sup>
</xref></contrib><contrib id="mp14424-cr-0012" contrib-type="author" corresp="yes"><name><surname>Fuller</surname><given-names>Clifton D.</given-names></name><xref ref-type="aff" rid="mp14424-aff-0003">
<sup>3</sup>
</xref><xref ref-type="aff" rid="mp14424-aff-0004">
<sup>4</sup>
</xref><address><email><EMAIL></email></address></contrib><contrib id="mp14424-cr-0013" contrib-type="author" corresp="yes"><name><surname>Giancardo</surname><given-names>Luca</given-names></name><xref ref-type="aff" rid="mp14424-aff-0002">
<sup>2</sup>
</xref><xref ref-type="aff" rid="mp14424-aff-0005">
<sup>5</sup>
</xref><address><email><EMAIL></email></address></contrib></contrib-group><aff id="mp14424-aff-0001">
<label><sup>1</sup></label>
<institution>John P. and Kathrine G. McGovern Medical School</institution>
<city>Houston</city>
<named-content content-type="country-part">TX</named-content>
<country country="US">USA</country>
</aff><aff id="mp14424-aff-0002">
<label><sup>2</sup></label>
<named-content content-type="organisation-division">Center for Precision Health</named-content>
<institution>UTHealth School of Biomedical Informatics</institution>
<city>Houston</city>
<named-content content-type="country-part">TX</named-content>
<country country="US">USA</country>
</aff><aff id="mp14424-aff-0003">
<label><sup>3</sup></label>
<named-content content-type="organisation-division">Department of Radiation Oncology</named-content>
<institution>University of Texas MD Anderson Cancer Center</institution>
<city>Houston</city>
<named-content content-type="country-part">TX</named-content>
<country country="US">USA</country>
</aff><aff id="mp14424-aff-0004">
<label><sup>4</sup></label>
<institution>MD Anderson Cancer Center&#x02010;UTHealth Graduate School of Biomedical Sciences</institution>
<city>Houston</city>
<named-content content-type="country-part">TX</named-content>
<country country="US">USA</country>
</aff><aff id="mp14424-aff-0005">
<label><sup>5</sup></label>
<named-content content-type="organisation-division">Department of Radiation Oncology</named-content>
<institution>Cleveland Clinic Taussig Cancer Center</institution>
<city>Cleveland</city>
<named-content content-type="country-part">OH</named-content>
<country country="US">USA</country>
</aff><aff id="mp14424-aff-0006">
<label><sup>6</sup></label>
<named-content content-type="organisation-division">Department of Diagnostic and Interventional Imaging</named-content>
<institution>John P. and Kathrine G. McGovern Medical School</institution>
<city>Houston</city>
<named-content content-type="country-part">TX</named-content>
<country country="US">USA</country>
</aff><author-notes><corresp id="correspondenceTo"><label>*</label>
Authors to whom correspondence should be addressed. Electronic mails: <email><EMAIL></email>, <email><EMAIL></email>, <email><EMAIL></email>; Telephones: ************, ************, ************.<break/></corresp></author-notes><pub-date pub-type="epub"><day>28</day><month>8</month><year>2020</year></pub-date><pub-date pub-type="ppub"><month>11</month><year>2020</year></pub-date><volume>47</volume><issue>11</issue><issue-id pub-id-type="doi">10.1002/mp.v47.11</issue-id><fpage>5941</fpage><lpage>5952</lpage><history><date date-type="received"><day>07</day><month>4</month><year>2020</year></date><date date-type="rev-recd"><day>22</day><month>7</month><year>2020</year></date><date date-type="accepted"><day>27</day><month>7</month><year>2020</year></date></history><permissions><!--<copyright-statement content-type="issue-copyright">Copyright &#x000a9; 2020 American Association of Physicists in Medicine<copyright-statement>--><copyright-statement content-type="article-copyright">&#x000a9; 2020 The Authors. Medical Physics published by Wiley Periodicals LLC on behalf of American Association of Physicists in Medicine</copyright-statement><license license-type="creativeCommonsBy"><license-p>This is an open access article under the terms of the <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link> License, which permits use, distribution and reproduction in any medium, provided the original work is properly cited.</license-p></license></permissions><self-uri content-type="pdf" xlink:href="file:MP-47-5941.pdf"/><abstract id="mp14424-abs-0001"><p>This manuscript describes a dataset of thoracic cavity segmentations and discrete pleural effusion segmentations we have annotated on 402 computed tomography (CT) scans acquired from patients with non&#x02010;small cell lung cancer. The segmentation of these anatomic regions precedes fundamental tasks in image analysis pipelines such as lung structure segmentation, lesion detection, and radiomics feature extraction. Bilateral thoracic cavity volumes and pleural effusion volumes were manually segmented on CT scans acquired from The Cancer Imaging Archive &#x0201c;NSCLC Radiomics&#x0201d; data collection. Four hundred and two thoracic segmentations were first generated automatically by a U&#x02010;Net based algorithm trained on chest CTs without cancer, manually corrected by a medical student to include the complete thoracic cavity (normal, pathologic, and atelectatic lung parenchyma, lung hilum, pleural effusion, fibrosis, nodules, tumor, and other anatomic anomalies), and revised by a radiation oncologist or a radiologist. Seventy&#x02010;eight pleural effusions were manually segmented by a medical student and revised by a radiologist or radiation oncologist. Interobserver agreement between the radiation oncologist and radiologist corrections was acceptable. All expert&#x02010;vetted segmentations are publicly available in NIfTI format through The Cancer Imaging Archive at <ext-link ext-link-type="uri" xlink:href="https://doi.org/10.7937/tcia.2020.6c7y-gq39">https://doi.org/10.7937/tcia.2020.6c7y&#x02010;gq39</ext-link>. Tabular data detailing clinical and technical metadata linked to segmentation cases are also available. Thoracic cavity segmentations will be valuable for developing image analysis pipelines on pathologic lungs &#x02014; where current automated algorithms struggle most. In conjunction with gross tumor volume segmentations already available from &#x0201c;NSCLC Radiomics,&#x0201d; pleural effusion segmentations may be valuable for investigating radiomics profile differences between effusion and primary tumor or training algorithms to discriminate between them.</p></abstract><kwd-group><kwd id="mp14424-kwd-0001">computer&#x02010;aided decision support systems</kwd><kwd id="mp14424-kwd-0002">image segmentation techniques</kwd><kwd id="mp14424-kwd-0003">image processing</kwd><kwd id="mp14424-kwd-0004">informatics in imaging</kwd><kwd id="mp14424-kwd-0005">quantitative imaging</kwd></kwd-group><funding-group><award-group id="funding-0001"><funding-source><institution-wrap><institution>Swiss Cancer League </institution><institution-id institution-id-type="open-funder-registry">10.13039/501100004361</institution-id></institution-wrap></funding-source><award-id>BIL KLS&#x02010;4300&#x02010;08&#x02010;2017</award-id></award-group><award-group id="funding-0002"><funding-source><institution-wrap><institution>HHS | National Institutes of Health (NIH) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000002</institution-id></institution-wrap></funding-source><award-id>1R01DE025248</award-id></award-group><award-group id="funding-0003"><funding-source><institution-wrap><institution>HHS | National Institutes of Health (NIH) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000002</institution-id></institution-wrap></funding-source><award-id>R56DE025248</award-id></award-group><award-group id="funding-0004"><funding-source><institution-wrap><institution>HHS | National Institutes of Health (NIH) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000002</institution-id></institution-wrap></funding-source><award-id>R01DE028290</award-id></award-group><award-group id="funding-0005"><funding-source>HHS | NIH | National Cancer Institute (NCI)</funding-source><award-id>1R01CA218148</award-id></award-group><award-group id="funding-0006"><funding-source>HHS | NIH | National Cancer Institute (NCI)</funding-source><award-id>P30CA016672</award-id></award-group><award-group id="funding-0007"><funding-source>HHS | NIH | National Cancer Institute (NCI)</funding-source><award-id>P50CA097007</award-id></award-group><award-group id="funding-0008"><funding-source><institution-wrap><institution>National Science Foundation (NSF) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000001</institution-id></institution-wrap></funding-source><award-id>NSF1557679</award-id></award-group><award-group id="funding-0009"><funding-source><institution-wrap><institution>National Science Foundation (NSF) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000001</institution-id></institution-wrap></funding-source><award-id>NSF1933369</award-id></award-group><award-group id="funding-0010"><funding-source><institution-wrap><institution>HHS | NIH | National Institute of Biomedical Imaging and Bioengineering (NIBIB) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000070</institution-id></institution-wrap></funding-source><award-id>R25EB025787&#x02010;01</award-id></award-group><award-group id="funding-0011"><funding-source><institution-wrap><institution>HHS | National Institutes of Health (NIH) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000002</institution-id></institution-wrap></funding-source><award-id>1R01CA214825</award-id></award-group><award-group id="funding-0012"><funding-source><institution-wrap><institution>UTHealth Center for Clinical and Translational Sciences </institution><institution-id institution-id-type="open-funder-registry">10.13039/100012684</institution-id></institution-wrap></funding-source></award-group><award-group id="funding-0013"><funding-source><institution-wrap><institution>HHS | National Institutes of Health (NIH) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000002</institution-id></institution-wrap></funding-source><award-id>UL1TR003167</award-id></award-group><award-group id="funding-0014"><funding-source><institution-wrap><institution>Cancer Prevention and Research Institute of Texas (CPRIT) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100004917</institution-id></institution-wrap></funding-source><award-id>RP170668</award-id></award-group><award-group id="funding-0015"><funding-source><institution-wrap><institution>HHS | National Institutes of Health (NIH) </institution><institution-id institution-id-type="open-funder-registry">10.13039/100000002</institution-id></institution-wrap></funding-source><award-id>R01AG066749</award-id></award-group></funding-group><counts><fig-count count="6"/><table-count count="2"/><page-count count="13"/><word-count count="8040"/></counts><custom-meta-group><custom-meta><meta-name>source-schema-version-number</meta-name><meta-value>2.0</meta-value></custom-meta><custom-meta><meta-name>cover-date</meta-name><meta-value>November 2020</meta-value></custom-meta><custom-meta><meta-name>details-of-publishers-convertor</meta-name><meta-value>Converter:WILEY_ML3GV2_TO_JATSPMC version:5.9.6 mode:remove_FC converted:22.12.2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body id="mp14424-body-0001"><sec id="mp14424-sec-0005"><label>1</label><title>INTRODUCTION</title><p>Automated or semi&#x02010;automated algorithms aimed at analyzing chest computed tomography (CT) scans typically require the creation of a three&#x02010;dimensional (3D) map of the volume&#x02010;of&#x02010;interest (VOI) as the initial step.<xref rid="mp14424-bib-0001" ref-type="ref">
<sup>1</sup>
</xref>, <xref rid="mp14424-bib-0002" ref-type="ref">
<sup>2</sup>
</xref> For example, a common first step for lung tumor detection on CT scans is lung segmentation.<xref rid="mp14424-bib-0003" ref-type="ref">
<sup>3</sup>
</xref> Effective strategies for identifying healthy lungs have existed at least since Hu et al. introduced a method for segmenting healthy lung parenchyma based on gray&#x02010;level thresholding.<xref rid="mp14424-bib-0004" ref-type="ref">
<sup>4</sup>
</xref> Nevertheless, identifying the initial VOI in pathologic lungs remains an obstacle.<xref rid="mp14424-bib-0005" ref-type="ref">
<sup>5</sup>
</xref>, <xref rid="mp14424-bib-0006" ref-type="ref">
<sup>6</sup>
</xref> Many pathologic states &#x02014; such as pleural effusion, severe fibrosis, or tumor &#x02014; can alter the space lung would normally occupy, and in this circumstance the VOI is not only the lung but the thoracic cavity. To build image processing pipelines intended to analyze chest CTs with substantively altered thoracic anatomy, identifying this VOI is critical.</p><p>Preprocessing strategies to identify thoracic VOIs in the presence of pathology have been described. In 2014, Mansoor et al. presented a seminal approach that identified CTs with large volumetric differences between autosegmented lung and the thoracic cage and refined these lung segmentations using texture&#x02010;based features.<xref rid="mp14424-bib-0007" ref-type="ref">
<sup>7</sup>
</xref> Subsequent studies have approached VOI identification in myriad ways, such as threshold&#x02010;based methodologies,<xref rid="mp14424-bib-0008" ref-type="ref">
<sup>8</sup>
</xref> deep learning architectures,<xref rid="mp14424-bib-0009" ref-type="ref">
<sup>9</sup>
</xref>, <xref rid="mp14424-bib-0010" ref-type="ref">
<sup>10</sup>
</xref>, <xref rid="mp14424-bib-0011" ref-type="ref">
<sup>11</sup>
</xref>, <xref rid="mp14424-bib-0012" ref-type="ref">
<sup>12</sup>
</xref>, <xref rid="mp14424-bib-0013" ref-type="ref">
<sup>13</sup>
</xref> anatomic or shape&#x02010;prior models,<xref rid="mp14424-bib-0005" ref-type="ref">
<sup>5</sup>
</xref>, <xref rid="mp14424-bib-0014" ref-type="ref">
<sup>14</sup>
</xref>, <xref rid="mp14424-bib-0015" ref-type="ref">
<sup>15</sup>
</xref>, <xref rid="mp14424-bib-0016" ref-type="ref">
<sup>16</sup>
</xref> and region&#x02010;growing methods.<xref rid="mp14424-bib-0017" ref-type="ref">
<sup>17</sup>
</xref>, <xref rid="mp14424-bib-0018" ref-type="ref">
<sup>18</sup>
</xref>
</p><p>As methodologies to identify thoracic VOIs in pathologic lungs march forward, data to train and vet them must keep pace. Hofmanninger et al. cited a paucity of diverse data &#x02014; not inferior methodologies &#x02014; as the principal obstacle in pathologic lung segmentation.<xref rid="mp14424-bib-0019" ref-type="ref">
<sup>19</sup>
</xref> Echoing this point, Shaukat et al. observed that automated lung nodule detection system optimization is usually limited to just one dataset.<xref rid="mp14424-bib-0020" ref-type="ref">
<sup>20</sup>
</xref> Many datasets consist exclusively of diseases typified by mild to moderate anatomic change &#x02014; for example, chronic obstructive pulmonary disorder or interstitial lung disease.<xref rid="mp14424-bib-0010" ref-type="ref">
<sup>10</sup>
</xref>, <xref rid="mp14424-bib-0011" ref-type="ref">
<sup>11</sup>
</xref> However, disease processes commonly beset by lung consolidations, effusions, masses, and other opacities that dramatically alter the thoracic VOI are radiographically distinct.</p><p>We present PleThora, a dataset of <bold><italic>ple</italic></bold>ural effusion and left and right <bold><italic>thora</italic></bold>cic cavity segmentations delineated on 402 CT scans from The Cancer Imaging Archive<xref rid="mp14424-bib-0021" ref-type="ref">
<sup>21</sup>
</xref> (TCIA) NSCLC&#x02010;Radiomics collection.<xref rid="mp14424-bib-0022" ref-type="ref">
<sup>22</sup>
</xref>, <xref rid="mp14424-bib-0023" ref-type="ref">
<sup>23</sup>
</xref> Many of these cases have dramatic anatomic changes secondary to cancer. Thoracic segmentations include lung parenchyma, primary tumor, atelectasis, adhesions, effusion, and other anatomic variations when present. On scans where effusion is present, separate segmentations labeling pleural effusion alone are also provided. These may serve particular use for correlating effusion radiomics features with clinical data (available in the NSCLC&#x02010;Radiomics spreadsheet, &#x0201c;Radiomics Lung1.clinical&#x02010;version3&#x02010;Oct 2019.csv&#x0201d;) or studying how these features differ between effusion and primary tumor. Thoracic segmentations were generated automatically by a U&#x02010;Net based deep learning algorithm trained on lungs without cancer, manually corrected by a medical student, and revised by a radiation oncologist or a radiologist. Pleural effusion segmentations were manually delineated by a medical student and revised by a radiologist. Expert gross tumor volume (GTV) segmentations already provided by the NSCLC&#x02010;Radiomics collection informed our segmentations and made possible delineation of pleural effusion volumes that excluded GTV.</p></sec><sec id="mp14424-sec-0006"><label>2</label><title>ACQUISITION AND VALIDATION METHODS</title><sec id="mp14424-sec-0007"><label>2.A</label><title>Segmentation acquisition</title><p>Four hundred twenty&#x02010;two Digital Imaging and Communications in Medicine (DICOM) chest CT datasets and 318 corresponding &#x0201c;RTSTRUCT&#x0201d; DICOM segmentations (featuring GTVs) were downloaded from the TCIA NSCLC&#x02010;Radiomics collection in January 2019. Four hundred and two CT scans were successfully converted from DICOM to Neuroimaging Informatics Technology Initiative (NIfTI) format using a free executable called &#x0201c;dcm2niix&#x0201d;.<xref rid="mp14424-bib-0024" ref-type="ref">
<sup>24</sup>
</xref>, <xref rid="mp14424-bib-0025" ref-type="ref">
<sup>25</sup>
</xref> These 402 scans comprise the dataset upon which our thoracic cavity and pleural effusion segmentations were delineated.</p><sec id="mp14424-sec-0008"><label>2.A.1</label><title>Thoracic cavity segmentation acquisition</title><p>After converting each CT dataset to NIfTI format, lungs were automatically segmented by a publicly available 3D U&#x02010;Net lung segmentation algorithm<xref rid="mp14424-bib-0026" ref-type="ref">
<sup>26</sup>
</xref> that had been trained on a private dataset of approximately 200 chest CTs acquired from patients without cancer. Lung segmented reasonably well in subjects with minimal anatomic variation due to disease, but poorly in subjects with significant disease. A fourth&#x02010;year medical student manually expanded each segmentation to include the thoracic cavity volume normally occupied by healthy lung. Among other anatomic anomalies, the expansion included normal, pathologic, and atelectatic lung parenchyma, pleural effusion, fibrosis, nodules, tumor, and compensatory anatomic variants such as enlarged collateral vessels. It also included major hilar vessels and bronchi that are not always segmented as part of the lung but are a frequent site of pathologic change. Importantly, effort was made to include all primary tumors in the segmentation even if this invaded the mediastinum or extended out of the thoracic cage. &#x0201c;RTSTRUCT&#x0201d; GTV segmentations from the NSCLC&#x02010;Radiomics collection assisted in this determination. Nodal disease in the mediastinum was not targeted. Each hemithorax, right and left, was segmented as a separate structure but saved under the same segmentation file.</p><p>The medical student&#x02019;s segmentations passed to at least one physician reviewer &#x02014; either a radiologist or a radiation oncologist &#x02014; to be vetted. As necessary, reviewers expanded or contracted the segmentations to include the target thoracic volumes. The most recent &#x0201c;RTSTRUCT&#x0201d; GTV segmentations (which were updated by the NSCLC&#x02010;Radiomics dataset authors in October 2019) were available to reviewers for reference as necessary. The thoracic cavity segmentations we make public are vetted segmentations. The thoracic cavity segmentation workflow is illustrated in Fig.&#x000a0;<xref rid="mp14424-fig-0001" ref-type="fig">1</xref> with examples of various lesions that were included in the delineations.</p><fig fig-type="Fig." xml:lang="en" id="mp14424-fig-0001" orientation="portrait" position="float"><label>Fig. 1</label><caption><p>Thoracic cavity volumes were segmented automatically then iteratively corrected by a medical student and at least one radiologist or radiation oncologist to include the entire hemithoraces. [Color figure can be viewed at <ext-link ext-link-type="uri" xlink:href="http://wileyonlinelibrary.com">wileyonlinelibrary.com</ext-link>]</p></caption><graphic id="nlm-graphic-1" xlink:href="MP-47-5941-g001"/></fig></sec><sec id="mp14424-sec-0009"><label>2.A.2</label><title>Pleural effusion segmentation acquisition</title><p>A fourth&#x02010;year medical student identified a subset of 78 CT scans with trace to massive pleural effusions. The medical student segmented effusions de novo rather than from an autosegmented prior (unlike the thoracic cavity segmentations). Spaces where primary tumor overlapped with effusion were excluded from the segmentation, and NSCLC&#x02010;Radiomics GTV segmentations determined the effusion segmentation extent (exemplified in Fig.&#x000a0;<xref rid="mp14424-fig-0002" ref-type="fig">2</xref>). In most subjects, an effusion in a single hemithorax was segmented. However, effusions were bilateral in 19 subjects, and in this circumstance both sides were segmented and saved under a single structure label.</p><fig fig-type="Fig." xml:lang="en" id="mp14424-fig-0002" orientation="portrait" position="float"><label>Fig. 2</label><caption><p>Pleural effusion segmentations, excluding gross tumor volume, were delineated by a medical student and subsequently corrected by at least two radiologists. [Color figure can be viewed at <ext-link ext-link-type="uri" xlink:href="http://wileyonlinelibrary.com">wileyonlinelibrary.com</ext-link>]</p></caption><graphic id="nlm-graphic-3" xlink:href="MP-47-5941-g002"/></fig><p>The medical student's pleural effusion segmentations were vetted by at least two physicians and corrected as necessary. Like the thoracic cavity segmentations, the pleural effusion segmentations we make public are vetted by physicians. The pleural effusion segmentation workflow is illustrated in Fig.&#x000a0;<xref rid="mp14424-fig-0002" ref-type="fig">2</xref>.</p></sec></sec><sec id="mp14424-sec-0010"><label>2.B</label><title>Computational tools</title><p>Manual segmentations were delineated using ITK&#x02010;SNAP v.3.6.<xref rid="mp14424-bib-0027" ref-type="ref">
<sup>27</sup>
</xref> ITK&#x02010;SNAP provides tools for manual or semi&#x02010;automated segmentation delineation including segmentation interpolation, 3D segmentation visualization and manipulation, and image contrast adjustment permitting mediastinal and lung window visualization. However, ITK&#x02010;SNAP does not support structure sets in DICOM&#x02010;RT format and could not open &#x0201c;RTSTRUCT&#x0201d; files to view GTV segmentations. Therefore, we used a free DICOM&#x02010;RT viewer called Dicompyler<xref rid="mp14424-bib-0028" ref-type="ref">
<sup>28</sup>
</xref> to reference GTV structures while segmenting.</p><p>Several additional computational tools transformed CT scans from DICOM to NIfTI format, autosegmented initial thoracic volumes, analyzed segmentations, and organized and visualized metadata. As previously noted, an executable named &#x0201c;dcm2niix&#x0201d;<xref rid="mp14424-bib-0024" ref-type="ref">
<sup>24</sup>
</xref> converted CTs from DICOM to NIfTI format and a 3D U&#x02010;Net<xref rid="mp14424-bib-0026" ref-type="ref">
<sup>26</sup>
</xref> automatically generated bilateral lung segmentations. All data were prepared using custom Python scripts leveraging common scientific libraries: NumPy v.1.16.2,<xref rid="mp14424-bib-0029" ref-type="ref">
<sup>29</sup>
</xref> Nilearn v.0.5.0,<xref rid="mp14424-bib-0030" ref-type="ref">
<sup>30</sup>
</xref> Pandas v.0.23.4,<xref rid="mp14424-bib-0031" ref-type="ref">
<sup>31</sup>
</xref> Surface_distance v.0.1,<xref rid="mp14424-bib-0032" ref-type="ref">
<sup>32</sup>
</xref> Scikit&#x02010;learn v.0.20.3,<xref rid="mp14424-bib-0033" ref-type="ref">
<sup>33</sup>
</xref> Matplotlib v.3.0.1,<xref rid="mp14424-bib-0034" ref-type="ref">
<sup>34</sup>
</xref> and Seaborn v.0.9.0.<xref rid="mp14424-bib-0035" ref-type="ref">
<sup>35</sup>
</xref>
</p></sec><sec id="mp14424-sec-0011"><label>2.C</label><title>Segmentation quality assessment</title><p>To assess the quality of segmentations with respect to interobserver variability, volumetric (i.e., traditional) Dice similarity coefficients<xref rid="mp14424-bib-0036" ref-type="ref">
<sup>36</sup>
</xref> (DSC), Cohen&#x02019;s kappa coefficients,<xref rid="mp14424-bib-0037" ref-type="ref">
<sup>37</sup>
</xref> surface Dice similarity coefficients<xref rid="mp14424-bib-0032" ref-type="ref">
<sup>32</sup>
</xref> (sDSC), 95th percentile Hausdorff distances<xref rid="mp14424-bib-0038" ref-type="ref">
<sup>38</sup>
</xref> (95HD), and symmetric average surface distances (ASD) were computed between expert reviewer segmentations. These metrics are discussed in the following subsections, and lung segmentation interobserver variability reference values are given where possible. Although methodologies to automate CT pleural effusion segmentation<xref rid="mp14424-bib-0039" ref-type="ref">
<sup>39</sup>
</xref>, <xref rid="mp14424-bib-0040" ref-type="ref">
<sup>40</sup>
</xref>, <xref rid="mp14424-bib-0041" ref-type="ref">
<sup>41</sup>
</xref>, <xref rid="mp14424-bib-0042" ref-type="ref">
<sup>42</sup>
</xref> or decrease qualitative size estimation variability<xref rid="mp14424-bib-0043" ref-type="ref">
<sup>43</sup>
</xref> have been described, we found only one limited report of pleural effusion segmentation interobserver variability<xref rid="mp14424-bib-0039" ref-type="ref">
<sup>39</sup>
</xref> against which we can compare our results (despite extensive PubMed searches).</p><sec id="mp14424-sec-0012"><label>2.C.1</label><title>Volumetric Dice similarity coefficient</title><p>The DSC measures interobserver agreement and ranges from 0 to 1, where 0 indicates no agreement and 1 indicates perfect agreement. In the context of image segmentation, the DSC relates the overlap between two segmentations to their total volumes; mathematically, this is twice the shared volume divided by the sum of their total volumes. No consensus dictates what constitutes a &#x0201c;good&#x0201d; DSC because the DSC is sensitive to the volume of the target structure. The American Association of Physicists in Medicine (AAPM) Task Group 132 notes that the contouring uncertainty of a structure can be expected to be a DSC of 0.8&#x02013;0.9, while cautioning that &#x0201c;very large or very small structures may have different expected DSC values for contour uncertainty.&#x0201d;<xref rid="mp14424-bib-0044" ref-type="ref">
<sup>44</sup>
</xref> For lung segmentation, DSC interobserver variability between three medical physicist organizers of the 2017 AAPM Thoracic Auto&#x02010;Segmentation Challenge was reported as 0.956&#x000a0;&#x000b1;&#x000a0;0.019 and 0.955&#x000a0;&#x000b1;&#x000a0;0.019 for the left and right lungs, respectively.<xref rid="mp14424-bib-0045" ref-type="ref">
<sup>45</sup>
</xref> For pleural effusion segmentation, Yao et al.<xref rid="mp14424-bib-0039" ref-type="ref">
<sup>39</sup>
</xref> reported mean DSC observer variability between a research fellow, the same fellow months later, and an image processing technologist to be about 0.73. Comparisons against this reference value should be made cautiously because it was calculated for only 12 CT scans with unspecified pleural effusion volumes.</p></sec><sec id="mp14424-sec-0013"><label>2.C.2</label><title>Cohen&#x02019;s Kappa coefficient</title><p>The Cohen&#x02019;s kappa (&#x003ba;) coefficient measures interobserver reliability for qualitative observations with mutually exclusive classifications. We computed &#x003ba; between expert reviewer segmentations by treating each voxel as a qualitative datum. We transformed the segmentations to numerical arrays where each array value assumed one of two binary values depending on whether the reviewer included the voxel as part of the target structure. &#x003ba; is similar to the DSC in its computation of interobserver agreement, but also assesses a likelihood of chance agreement and penalizes accordingly. Like the DSC, &#x003ba; generally ranges between 0 and 1, although a result&#x000a0;&#x0003c;&#x000a0;0 is possible. Results &#x0003e;0.6 are generally considered good and greater than 0.8 are considered very good,<xref rid="mp14424-bib-0046" ref-type="ref">
<sup>46</sup>
</xref> although like the DSC, &#x003ba; inflates and deflates artificially depending on the target&#x02019;s volume.</p></sec><sec id="mp14424-sec-0014"><label>2.C.3</label><title>Surface Dice similarity coefficient</title><p>Recognizing the limitations of the traditional DSC with respect to volume, Nikolov et al.<xref rid="mp14424-bib-0032" ref-type="ref">
<sup>32</sup>
</xref> introduced a novel way to compute Dice's coefficient called the surface DSC. As its name suggests, the sDSC's inputs are segmentation surfaces rather than volumes. A primary advantage of the sDSC over the traditional DSC is increased robustness to segmentation size variation. The sDSC is not agnostic to size, but it inflates less dramatically with size than the volumetric DSC. The sDSC computation accepts a tolerance parameter whereby differences between two surfaces can be tolerated as the same surface. We set this parameter equal to zero in order to capture all differences between physician segmentations. Because the sDSC is novel, reference values for this metric are not yet widely reported. However, Vaassen et al.<xref rid="mp14424-bib-0047" ref-type="ref">
<sup>47</sup>
</xref> calculated the sDSC between automatically generated and radiotherapist&#x02010;corrected lung segmentations and reported the majority of values to be greater than 0.85. Reasoning that inter&#x02010;physician agreement ought to be at least as good as autosegmentation&#x02010;radiotherapist agreement, we suggest 0.85 as an acceptable mean reference value for thoracic cavity segmentations.</p></sec><sec id="mp14424-sec-0015"><label>2.C.4</label><title>Hausdorff distance</title><p>In contrast to the DSC, &#x003ba;, and sDSC, which measure fractional overlap between segmentations, HDs are geometric distances between segmentation surfaces. Larger HDs signify worse interobserver agreement. Computing a HD requires determining the minimum distances from every point on the surface of segmentation A to every point on the surface of segmentation B, and the same from B to A, and arranging them in ascending order. We report the 95HD, which is the value at the 95th percentile of the ordered minimum distances. 95HD interobserver variability reported by the AAPM Thoracic Auto&#x02010;Segmentation Challenge was 5.17&#x000a0;<mml:math id="nlm-math-1"><mml:mo>&#x000b1;</mml:mo></mml:math>&#x000a0;2.73 and 6.71&#x000a0;<mml:math id="nlm-math-2"><mml:mo>&#x000b1;</mml:mo></mml:math>&#x000a0;3.91 for left and right lung segmentations, respectively.<xref rid="mp14424-bib-0045" ref-type="ref">
<sup>45</sup>
</xref>
</p></sec><sec id="mp14424-sec-0016"><label>2.C.5</label><title>Average surface distance</title><p>The directed ASD from segmentation A to segmentation B is the average of the minimum distances from every point in the surface of A to every point in the surface of B. The directed ASD from B to A is calculated similarly. We report a symmetric ASD value that averages the two directed ASDs. Symmetric ASD interobserver variability reported by the AAPM Thoracic Auto&#x02010;Segmentation Challenge was 1.51&#x000a0;<mml:math id="nlm-math-3"><mml:mo>&#x000b1;</mml:mo></mml:math>&#x000a0;0.67 and 1.87&#x000a0;<mml:math id="nlm-math-4"><mml:mo>&#x000b1;</mml:mo></mml:math>&#x000a0;0.87 for left and right lung segmentations, respectively.<xref rid="mp14424-bib-0045" ref-type="ref">
<sup>45</sup>
</xref>
</p></sec><sec id="mp14424-sec-0017"><label>2.C.6</label><title>Expert reviewers</title><p>Expert reviewers consisted of four radiologists and three radiation oncologists with varying years of experience (Table<xref rid="mp14424-tbl-0001" ref-type="table">&#x000a0;I</xref>). All thoracic cavity segmentations were reviewed by at least one expert, and randomly selected, unique subsets were reviewed by two. All pleural effusion segmentations were reviewed by at least two experts and a subset was reviewed by three. Relationships between reviewers are illustrated in Fig.&#x000a0;<xref rid="mp14424-fig-0003" ref-type="fig">3</xref>. Median and minimum values for each spatial similarity metric for each reviewer pair are given in Table&#x000a0;<xref rid="mp14424-tbl-0002" ref-type="table">II</xref>. Pairwise Mann&#x02010;Whitney <italic>U</italic> tests<xref rid="mp14424-bib-0048" ref-type="ref">
<sup>48</sup>
</xref> conducted between reviewer pair distributions suggest that they are significantly different from one another for all metrics (<italic>P</italic>&#x000a0;&#x0003c;&#x000a0;0.001). The following paragraphs discuss how interobserver variability in our study compares with variability described in other studies.</p><table-wrap id="mp14424-tbl-0001" xml:lang="en" content-type="Table" orientation="portrait" position="float"><label>Table I</label><caption><p>Seven radiologists (Rad) and radiation oncologists (RO) collaborated to review and correct 402 thoracic cavity segmentations and 78 pleural effusion segmentations delineated by a fourth&#x02010;year medical student.</p></caption><table frame="hsides" rules="groups"><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><thead valign="top"><tr style="border-bottom:solid 1px #000000"><th align="left" valign="top" rowspan="1" colspan="1">Expert reviewer</th><th align="center" valign="top" rowspan="1" colspan="1">Years of experience</th></tr></thead><tbody><tr><td align="left" rowspan="1" colspan="1">Rad1</td><td align="left" rowspan="1" colspan="1">4</td></tr><tr><td align="left" rowspan="1" colspan="1">Rad2</td><td align="left" rowspan="1" colspan="1">2</td></tr><tr><td align="left" rowspan="1" colspan="1">Rad3</td><td align="left" rowspan="1" colspan="1">1</td></tr><tr><td align="left" rowspan="1" colspan="1">Rad4</td><td align="left" rowspan="1" colspan="1">3</td></tr><tr><td align="left" rowspan="1" colspan="1">RO1</td><td align="left" rowspan="1" colspan="1">4</td></tr><tr><td align="left" rowspan="1" colspan="1">RO2</td><td align="left" rowspan="1" colspan="1">11</td></tr><tr><td align="left" rowspan="1" colspan="1">RO3</td><td align="left" rowspan="1" colspan="1">5</td></tr></tbody></table><permissions><copyright-holder>John Wiley &#x00026; Sons, Ltd</copyright-holder></permissions></table-wrap><fig fig-type="Fig." xml:lang="en" id="mp14424-fig-0003" orientation="portrait" position="float"><label>Fig. 3</label><caption><p>A schematic of interobserver comparisons, with the number of segmentation cases shared between observer pairs given as n. All 78 pleural effusion segmentations were reviewed and as necessary corrected by two radiologists: Rad3 and Rad4. A subset of 15 pleural effusion segmentations were also reviewed and corrected by RO1. In contrast, not all 402 thoracic cavity segmentations were reviewed by two physicians. Rather, four subsets of 21 or 22 thoracic cavity segmentations were randomly selected for dual review. All members of a given subset were exclusive to that subset. [Color figure can be viewed at <ext-link ext-link-type="uri" xlink:href="http://wileyonlinelibrary.com">wileyonlinelibrary.com</ext-link>]</p></caption><graphic id="nlm-graphic-5" xlink:href="MP-47-5941-g003"/></fig><table-wrap id="mp14424-tbl-0002" xml:lang="en" content-type="Table" orientation="portrait" position="float"><label>Table II</label><caption><p>Median and minimum values for Dice similarity coefficient (DSC), surface DSC, &#x003ba;, 95HD, and symmetric ASD spatial similarity metrics calculated between paired physician segmentations. The distributions for each observer pair are significantly different from one another for all metrics (paired Mann&#x02010;Whitney <italic>U</italic> test<xref rid="mp14424-bib-0048" ref-type="ref">
<sup>48</sup>
</xref>
<italic>P</italic>&#x000a0;&#x0003c;&#x000a0;0.001). However, interobserver variability between pairs of physician&#x02010;vetted segmentations is generally acceptable. Median DSC, 95HD and symmetric ASD values for thoracic cavity segmentations are comparable to mean interobserver variability values reported by the 2017 AAPM Thoracic Auto&#x02010;Segmentation Challenge for lung segmentations.<xref rid="mp14424-bib-0045" ref-type="ref">
<sup>45</sup>
</xref> In general, pleural effusion segmentation interobserver agreement is also acceptable but more variable, reflecting both variation in pleural effusion size and inclusion or exclusion of trace pleural fluid.</p></caption><table frame="hsides" rules="groups"><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><col style="border-right:solid 1px #000000" span="1"/><thead valign="bottom"><tr style="border-bottom:solid 1px #000000"><th align="left" rowspan="2" colspan="2" valign="bottom">Metric</th><th align="center" colspan="3" style="border-bottom:solid 1px #000000" valign="bottom" rowspan="1">Pleural effusions</th><th align="center" colspan="4" style="border-bottom:solid 1px #000000" valign="bottom" rowspan="1">Thoracic cavities</th></tr><tr style="border-bottom:solid 1px #000000"><th align="center" valign="bottom" rowspan="1" colspan="1">RO1&#x02010;Rad3</th><th align="center" valign="bottom" rowspan="1" colspan="1">RO1&#x02010;Rad4</th><th align="center" valign="bottom" rowspan="1" colspan="1">Rad3&#x02010;Rad4</th><th align="center" valign="bottom" rowspan="1" colspan="1">RO1&#x02010;RO3</th><th align="center" valign="bottom" rowspan="1" colspan="1">Rad1&#x02010;RO3</th><th align="center" valign="bottom" rowspan="1" colspan="1">Rad1&#x02010;Rad2</th><th align="center" valign="bottom" rowspan="1" colspan="1">RO2&#x02010;Rad2</th></tr></thead><tbody><tr><td align="left" colspan="9" rowspan="1">Conformality metrics (unitless)</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">DSC</td><td align="left" rowspan="1" colspan="1">Med</td><td align="char" char="." rowspan="1" colspan="1">0.81</td><td align="char" char="." rowspan="1" colspan="1">0.85</td><td align="char" char="." rowspan="1" colspan="1">0.93</td><td align="char" char="." rowspan="1" colspan="1">0.99</td><td align="char" char="." rowspan="1" colspan="1">1.00</td><td align="char" char="." rowspan="1" colspan="1">1.00</td><td align="char" char="." rowspan="2" colspan="1">1.00</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">Min</td><td align="left" rowspan="1" colspan="1">0.10<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.26</td><td align="char" char="." rowspan="1" colspan="1">0.20</td><td align="char" char="." rowspan="1" colspan="1">0.96</td><td align="char" char="." rowspan="1" colspan="1">0.91<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.99</td><td align="char" char="." rowspan="1" colspan="1">0.97</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">sDSC</td><td align="left" rowspan="1" colspan="1">Med</td><td align="char" char="." rowspan="1" colspan="1">0.62</td><td align="char" char="." rowspan="1" colspan="1">0.77</td><td align="char" char="." rowspan="1" colspan="1">0.87</td><td align="char" char="." rowspan="1" colspan="1">0.94</td><td align="char" char="." rowspan="1" colspan="1">0.98</td><td align="char" char="." rowspan="1" colspan="1">0.98</td><td align="char" char="." rowspan="2" colspan="1">1.00</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">Min</td><td align="left" rowspan="1" colspan="1">0.20<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.32</td><td align="char" char="." rowspan="1" colspan="1">0.21</td><td align="char" char="." rowspan="1" colspan="1">0.73<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.82</td><td align="char" char="." rowspan="1" colspan="1">0.94</td><td align="char" char="." rowspan="1" colspan="1">0.88</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">Kappa</td><td align="left" rowspan="1" colspan="1">Med</td><td align="char" char="." rowspan="1" colspan="1">0.81</td><td align="char" char="." rowspan="1" colspan="1">0.85</td><td align="char" char="." rowspan="1" colspan="1">0.93</td><td align="char" char="." rowspan="1" colspan="1">0.99</td><td align="char" char="." rowspan="1" colspan="1">0.99</td><td align="char" char="." rowspan="1" colspan="1">0.99</td><td align="char" char="." rowspan="2" colspan="1">0.99</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">Min</td><td align="left" rowspan="1" colspan="1">0.10<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.26</td><td align="char" char="." rowspan="1" colspan="1">0.20</td><td align="char" char="." rowspan="1" colspan="1">0.96</td><td align="char" char="." rowspan="1" colspan="1">0.90<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.99</td><td align="char" char="." rowspan="1" colspan="1">0.97</td></tr><tr><td align="left" colspan="9" rowspan="1">Surface distance metrics (mm)</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">95HD</td><td align="left" rowspan="1" colspan="1">Med</td><td align="char" char="." rowspan="1" colspan="1">24.00</td><td align="char" char="." rowspan="1" colspan="1">21.65</td><td align="char" char="." rowspan="1" colspan="1">5.31</td><td align="char" char="." rowspan="1" colspan="1">1.95</td><td align="char" char="." rowspan="1" colspan="1">0.00</td><td align="char" char="." rowspan="1" colspan="1">0.00</td><td align="char" char="." rowspan="2" colspan="1">0.00</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">Max</td><td align="left" rowspan="1" colspan="1">127.83</td><td align="char" char="." rowspan="1" colspan="1">127.80</td><td align="char" char="." rowspan="1" colspan="1">161.48<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">11.35</td><td align="char" char="." rowspan="1" colspan="1">55.11<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.98</td><td align="char" char="." rowspan="1" colspan="1">24.82</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">ASD</td><td align="left" rowspan="1" colspan="1">Med</td><td align="char" char="." rowspan="1" colspan="1">1.82</td><td align="char" char="." rowspan="1" colspan="1">2.45</td><td align="char" char="." rowspan="1" colspan="1">0.79</td><td align="char" char="." rowspan="1" colspan="1">0.25</td><td align="char" char="." rowspan="1" colspan="1">0.05</td><td align="char" char="." rowspan="1" colspan="1">0.03</td><td align="char" char="." rowspan="2" colspan="1">0.00</td></tr><tr><td align="left" style="padding-left:10%" rowspan="1" colspan="1">Max</td><td align="left" rowspan="1" colspan="1">23.53</td><td align="char" char="." rowspan="1" colspan="1">22.39</td><td align="char" char="." rowspan="1" colspan="1">33.49<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">1.01</td><td align="char" char="." rowspan="1" colspan="1">5.78<xref ref-type="fn" rid="mp14424-note-0001">
<sup>a</sup>
</xref>
</td><td align="char" char="." rowspan="1" colspan="1">0.12</td><td align="char" char="." rowspan="1" colspan="1">1.68</td></tr></tbody></table><table-wrap-foot id="mp14424-ntgp-0001"><fn id="mp14424-note-0001"><label><sup>a</sup></label><p>Select cases with extreme spatial similarity metric values are explored visually in Fig.&#x000a0;<xref rid="mp14424-fig-0006" ref-type="fig">6</xref>.</p></fn></table-wrap-foot><permissions><copyright-holder>John Wiley &#x00026; Sons, Ltd</copyright-holder></permissions></table-wrap><p>In general, thoracic cavity segmentation pairs enjoyed a good level of agreement. Gauged by DSC, 95HD, and symmetric ASD spatial similarity metrics, thoracic cavity segmentation interobserver variability was similar to reported interobserver variability for lung segmentation<xref rid="mp14424-bib-0045" ref-type="ref">
<sup>45</sup>
</xref> and similar to values achieved by state&#x02010;of&#x02010;the&#x02010;art deep learning architectures trained for lung autosegmentation.<xref rid="mp14424-bib-0019" ref-type="ref">
<sup>19</sup>
</xref> Spatial similarity metrics improved with each iteration of corrections. For example, DSC values calculated between initial automated segmentations and substantially corrected medical student segmentations were relatively low (DSC<sub>min</sub>&#x000a0;=&#x000a0;0.353, DSC<sub>med</sub>&#x000a0;=&#x000a0;0.958, IQR&#x000a0;=&#x000a0;0.041). In contrast, the DSC was never &#x0003c;0.9 between any pair of physician&#x02010;corrected contours, and the median DSC for each interobserver distribution approximated 0.99 (Fig.&#x000a0;<xref rid="mp14424-fig-0004" ref-type="fig">4</xref>).</p><fig fig-type="Fig." xml:lang="en" id="mp14424-fig-0004" orientation="portrait" position="float"><label>Fig. 4</label><caption><p>Dice similarity coefficient distributions reveal consistently strong agreement (&#x0003e;0.98) between paired, independently vetted radiologist and radiation oncologist thoracic cavity segmentations. Colored curves are kernel density estimates of DSC distributions. Note that Figs.&#x000a0;<xref rid="mp14424-fig-0004" ref-type="fig">4</xref> and <xref rid="mp14424-fig-0005" ref-type="fig">5</xref> do not share the same x axis limits; the difference in DSC distributions in Figs.&#x000a0;<xref rid="mp14424-fig-0004" ref-type="fig">4</xref> and <xref rid="mp14424-fig-0005" ref-type="fig">5</xref> is at least partially an artifact of the difference in average volume between thoraces and effusions. [Color figure can be viewed at <ext-link ext-link-type="uri" xlink:href="http://wileyonlinelibrary.com">wileyonlinelibrary.com</ext-link>]</p></caption><graphic id="nlm-graphic-7" xlink:href="MP-47-5941-g004"/></fig><p>Pleural effusion segmentation interobserver agreement was also consistent, although the distributions of conformality metrics and surface distance metrics generally suggest lower agreement than for thoracic cavity segmentations. Medians DSCs (Fig.&#x000a0;<xref rid="mp14424-fig-0005" ref-type="fig">5</xref>) compare favorably with Yao et al.&#x02019;s<xref rid="mp14424-bib-0039" ref-type="ref">
<sup>39</sup>
</xref> mean DSC interobserver variability estimate of 0.73, but we again note that the sample in this study was small and the pleural effusion volumes unspecified. Knowing the pleural effusion volume distribution matters because wide variation in conformality spatial similarity metrics can be partly explained by the spread of pleural effusion volumes. Surface distance metrics can also be influenced by pleural effusion spatial spread (i.e., large distances separating effusion pockets). In a preprint analytic study that uses PleThora,<xref rid="mp14424-bib-0049" ref-type="ref">
<sup>49</sup>
</xref> we determined the median right and left pleural effusion volumes to be respectively 58.57&#x000a0;cm<sup>3</sup> (IQR 30.31&#x02013;113.7&#x000a0;cm<sup>3</sup>) and 50.85&#x000a0;cm<sup>3</sup> (IQR 25.01&#x02013;142.5&#x000a0;cm<sup>3</sup>). These are an order of magnitude smaller than the mean pleural effusion volumes described in some pleural effusion autosegmentation methodologic studies,<xref rid="mp14424-bib-0040" ref-type="ref">
<sup>40</sup>
</xref> which highlights the unique character of this dataset.</p><fig fig-type="Fig." xml:lang="en" id="mp14424-fig-0005" orientation="portrait" position="float"><label>Fig. 5</label><caption><p>Dice similarity coefficient (DSC) distributions indicate good agreement (&#x0003e;0.8) between most paired, independently vetted radiologist and radiation oncologist pleural effusion segmentations. Interpretation of this result should respect that DSC values calculated on trace pleural effusions are sensitive to variation between segmentations on the order of only a few voxels. As in Fig.&#x000a0;<xref rid="mp14424-fig-0004" ref-type="fig">4</xref>, colored curves are kernel density estimations of DSC distributions. [Color figure can be viewed at <ext-link ext-link-type="uri" xlink:href="http://wileyonlinelibrary.com">wileyonlinelibrary.com</ext-link>]</p></caption><graphic id="nlm-graphic-9" xlink:href="MP-47-5941-g005"/></fig><p>Notwithstanding that interobserver agreement was generally very good, a few segmentation cases demonstrated markedly poorer agreement than others (Fig.&#x000a0;<xref rid="mp14424-fig-0006" ref-type="fig">6</xref>). RO1 and Rad3&#x02019;s pleural effusion segmentations for case LUNG1&#x02010;005 yielded the worst DSC and &#x003ba; values of any interobserver comparison. Visual inspection reveals a large posterior density that RO1 classified as pleural effusion but Rad3 excluded as an atelectatic lung segment. In this case, Rad4&#x02019;s segmentation arbitrates that the density is not effusion. The segmentation we made publicly available through TCIA in our first version of the dataset was only Rad4&#x02019;s segmentation, but all reviewer segmentations were made available in the second version. Rad3 and Rad4&#x02019;s pleural effusion segmentations for case LUNG1&#x02010;253 suffered the highest 95HD value of any interobserver comparison. Here, 2D and 3D visual inspection reveals substantial disagreement respecting the superior&#x02010;inferior and lateral extents of a thin layer of pleural fluid. We did not prospectively define guidelines for whether trace pleural fluid should or should not be segmented, which in hindsight we acknowledge is a limitation of our pleural effusion segmentation methodology. By contrast, the worst discrepancies between thoracic cavity segmentations are easily resolved by our segmentation guidelines. Thoracic cavity segmentations for LUNG1&#x02010;026 suffered the worst sDSC interobserver variability, which can be attributed to the secondary physician reviewer&#x02019;s erroneous neglect of the full extent of tumor, left hilum, and right pleural effusion. Similarly, the secondary reviewer erroneously excluded a left collapsed lung for case LUNG1&#x02010;354, which was the thoracic cavity segmentation case with the worst DSC, &#x003ba;, 95HD, and symmetric ASD. The thoracic cavity segmentations we made publicly available for these cases in the first version of the dataset were the primary reviewer&#x02019;s segmentations, but all reviewers&#x02019; segmentations were made available in the second version (after correcting the aforementioned errors).</p><fig fig-type="Fig." xml:lang="en" id="mp14424-fig-0006" orientation="portrait" position="float"><label>Fig. 6</label><caption><p>A visual exploration of physician&#x02010;corrected segmentation pairs with the least interobserver agreement. Case LUNG1&#x02010;005 accounts for the worst Dice similarity coefficient (DSC), surface DSC, and &#x003ba; values between any pair of pleural effusion segmentations. RO1 mistook atelectatic lung for effusion, but Rad3 and Rad4 did not. Case LUNG1&#x02010;253 accounts for the worst 95HD and symmetric ASD values between any pair of pleural effusion segmentations. Rad3 and Rad4 varied in how much trace pleural fluid they chose to segment. This exposes a weakness in our pleural effusion segmentation methodology because we did not decide at projection initiation whether or to what extent trace pleural fluid should be part of the segmentation. Case LUNG1&#x02010;026 accounts for the worst sDSC value between any pair of thoracic cavity segmentations. RO3 failed to segment the full extent of peri&#x02010;mediastinal primary gross tumor volume, right effusion, and left hilum (orange arrows). Case LUNG1&#x02010;354 accounts for the worst DSC, &#x003ba;, 95HD, and symmetric ASD values between any pair of thoracic cavity segmentations. RO3 erroneously excluded a collapsed left lung. [Color figure can be viewed at <ext-link ext-link-type="uri" xlink:href="http://wileyonlinelibrary.com">wileyonlinelibrary.com</ext-link>]</p></caption><graphic id="nlm-graphic-11" xlink:href="MP-47-5941-g006"/></fig></sec></sec><sec id="mp14424-sec-0018"><label>2.D</label><title>NSCLC&#x02010;radiomics collection update</title><p>The NSCLC&#x02010;Radiomics collection was updated on 10/23/2019, featuring new &#x0201c;RTSTRUCT&#x0201d; segmentations for all 422 cases, including revised GTVs in some cases. Our thoracic cavity segmentations were reviewed by radiologists or radiation oncologists who had access to &#x0201c;RTSTRUCT&#x0201d; files from the latest collection update. However, these segmentations were first delineated by a medical student at a time when only earlier versions of the &#x0201c;RTSTRUCT&#x0201d; files were available. In contrast, pleural effusion segmentations were all delineated with input from the latest &#x0201c;RTSTRUCT&#x0201d; files.</p></sec></sec><sec id="mp14424-sec-0019"><label>3</label><title>DATA FORMAT AND USAGE NOTES</title><sec id="mp14424-sec-0020"><label>3.A</label><title>Data and metadata repository</title><p>In keeping with findable, accessible, interoperable, re&#x02010;usable (FAIR) data usage principles,<xref rid="mp14424-bib-0050" ref-type="ref">
<sup>50</sup>
</xref> all PleThora thoracic cavity and pleural effusion segmentations have been made available on TCIA at <ext-link ext-link-type="uri" xlink:href="https://doi.org/10.7937/tcia.2020.6c7y-gq39">https://doi.org/10.7937/tcia.2020.6c7y&#x02010;gq39</ext-link>.<xref rid="mp14424-bib-0051" ref-type="ref">
<sup>51</sup>
</xref> Thoracic cavity segmentations are in a compressed NIfTI format, are named for their respective case and reviewer (e.g., &#x0201c;LUNG1&#x02010;001_thor_cav_primary_reviewer.nii.gz&#x0201d;), and are indexed in folders labeled after their respective NSCLC&#x02010;Radiomics collection cases (e.g., &#x0201c;LUNG1&#x02010;001&#x0201d;). Pleural effusion segmentations are likewise saved in a compressed NIfTI format and named for their respective case and reviewer (e.g., &#x0201c;LUNG1&#x02010;001_effusion_first_reviewer.nii.gz&#x0201d;). Many thoracic cavity and all pleural effusion segmentations were reviewed by two or more experts. In this dataset&#x02019;s original TCIA publication, only primary reviewer segmentations were made available. However, all reviewers&#x02019; segmentations &#x02014; primary, secondary, and tertiary &#x02014; were made available in a recent dataset update (version 2). A spreadsheet entitled &#x0201c;Thorax and Pleural Effusion Segmentation Metadata&#x0201d; contains clinical and technical metadata pertaining to each segmentation or CT scan. It is hosted in the same repository as the segmentations. We strongly recommend users merge it with the NSCLC&#x02010;Radiomics spreadsheet &#x0201c;Radiomics Lung1.clinical&#x02010;version3&#x02010;Oct 2019.csv,&#x0201d; which provides ten columns of clinical data tied to each case. We provide Appendix 1 as a data dictionary to clarify the meaning of our spreadsheet&#x02019;s column names.</p></sec><sec id="mp14424-sec-0021"><label>3.B</label><title>Baseline for deep learning model performance</title><p>To provide a performance baseline for researchers interested in using our thoracic cavity segmentations for deep learning model development, we trained and tested two U&#x02010;Net models, one based on 2D convolutional layers and one based on 3D convolutional layers. U&#x02010;Nets are common Convolutional Deep Neural Network architectures and form the basis of many deep learning algorithms for medical image segmentation.<xref rid="mp14424-bib-0052" ref-type="ref">
<sup>52</sup>
</xref>, <xref rid="mp14424-bib-0053" ref-type="ref">
<sup>53</sup>
</xref>, <xref rid="mp14424-bib-0054" ref-type="ref">
<sup>54</sup>
</xref>, <xref rid="mp14424-bib-0055" ref-type="ref">
<sup>55</sup>
</xref>, <xref rid="mp14424-bib-0056" ref-type="ref">
<sup>56</sup>
</xref>
</p><p>CT scans were preprocessed as follows: voxel intensities were clipped to a range of [&#x02212;250, 0] Hounsfield units by reassigning voxels less than &#x02212;250 to &#x02212;250 and greater than 0 to 0, voxels were isotropically resampled to 1.7&#x000a0;mm in each dimension, and scans were cropped from the image center to 256 by 256 by 128 voxels. Segmentations were likewise resampled and cropped.</p><p>The models were trained using 316 of the 402 primary reviewer segmentations and tested with 86 secondary reviewer segmentations. The latter served as a test dataset because secondary and primary reviews were conducted independently. Nevertheless, because secondary and primary reviewers corrected the same template segmentation, we felt that corrected segmentations were likely to inherit similarities from the template that would bias the test dataset toward the training dataset. Therefore, we chose to exclude the 86 primary reviewer segmentations that corresponded to cases with a secondary reviewer from the training data. The model was initially trained end&#x02010;to&#x02010;end by fivefold cross validation. This permitted fine&#x02010;tuning of the hyperparameters (e.g., epochs, learning rate, batch size) without overfitting the external training set. The whole dataset split and secondary reviewer segmentations were recently made available through TCIA in an update (version 2) of the original dataset.</p><p>To build the 3D U&#x02010;Net model we used the architecture described by &#x000c7;i&#x000e7;ek et al.<xref rid="mp14424-bib-0053" ref-type="ref">
<sup>53</sup>
</xref> For each epoch the train/validation split was 80% train (252 scans) and 20% validation (64 scans). The batch size was 1 scan, the DSC was used to evaluate loss, and the learning rate was set initially to 0.001 and adapted by the Adam optimization algorithm.<xref rid="mp14424-bib-0057" ref-type="ref">
<sup>57</sup>
</xref> The DSC plateaued at 100 epochs. The mean DSC performance on the test dataset was 0.95 (standard deviation&#x000a0;&#x000b1;&#x000a0;0.05) and 0.95 (&#x000b1;0.04) for the left and right lungs, respectively. For reference, the DSC between the primary and secondary reviewer segmentations was 0.98 (&#x000b1; 0.11) and 0.98 (&#x000b1; 0.11) for the left and right lungs, respectively.</p><p>To build the 2D U&#x02010;Net model we used the same architecture as the 3D U&#x02010;Net but changed all 3D convolution and deconvolution operations to 2D operations. In this case, the algorithm was trained on 2D axial slices (i.e., 256 by 256 voxels) rather than whole volumes. Like the 3D model, the 2D U&#x02010;Net train/validation split was 80% train (32&#x000a0;256 slices) and 20% validation (8192 slices). The DSC plateaued at 60 epochs. The batch size was 64 slices, the DSC was used to evaluate loss, and the learning rate was set initially to 0.001 and adapted by the Adam optimization algorithm.<xref rid="mp14424-bib-0057" ref-type="ref">
<sup>57</sup>
</xref> The mean DSC performance on the test dataset was 0.94 (&#x000b1; 0.10) and 0.94 (&#x000b1; 0.10) for the left and right lungs, respectively.</p></sec></sec><sec sec-type="discussion" id="mp14424-sec-0022"><label>4</label><title>DISCUSSION</title><p>To our knowledge, PleThora is the first public dataset of VOIs curated to capture all thoracic cavity pathologic change in patients with lung cancer, and the first public dataset of pleural effusion segmentations. The thoracic cavity segmentations are likely to be valuable to scientists and engineers who develop chest CT image processing pipelines that require robust methods to identify thoracic VOIs. We anticipate these segmentations to be a particularly useful addition to the corpus of training data for image processing pipelines, including the ones leveraging deep learning algorithms. Indeed, this project began as an effort to provide VOIs to study image feature symmetry between left and right thorax anatomy as a clinical outcomes predictor, building on previous work from our group that localized stroke cores by comparing and contrasting brain hemisphere information extracted by &#x0201c;symmetry&#x02010;sensitive convolutional neural networks.&#x0201d;<xref rid="mp14424-bib-0058" ref-type="ref">
<sup>58</sup>
</xref>
</p><p>Our pleural effusion segmentations are likely to be useful for investigating two questions surrounding a CT or PET/CT finding of pleural effusion: (a) the prognostic significance of pleural effusion in various cancer types,<xref rid="mp14424-bib-0059" ref-type="ref">
<sup>59</sup>
</xref> and (b) the capacity of CT to discriminate between benign and malignant effusions.<xref rid="mp14424-bib-0060" ref-type="ref">
<sup>60</sup>
</xref>, <xref rid="mp14424-bib-0061" ref-type="ref">
<sup>61</sup>
</xref>, <xref rid="mp14424-bib-0062" ref-type="ref">
<sup>62</sup>
</xref>, <xref rid="mp14424-bib-0063" ref-type="ref">
<sup>63</sup>
</xref> Regarding the first question, Ryu et al.<xref rid="mp14424-bib-0059" ref-type="ref">
<sup>59</sup>
</xref> showed that in small cell lung cancer with stage I&#x02013;III disease, the presence of even minimal pleural effusion confers an increased risk of death. Investigating the prognostic relationship of pleural effusion presence in other cancer types would presumably be facilitated by deep learning pleural effusion segmentation algorithms, such as might be trained with datasets like ours. Regarding the second question, some CT findings (nodular, mediastinal, parietal, and circumferential pleural thickening) are classically considered to be reasonably specific but poorly sensitive discriminators of malignant effusion.<xref rid="mp14424-bib-0064" ref-type="ref">
<sup>64</sup>
</xref> Perhaps investigations into the value of pleural effusion quantitative imaging biomarkers for accomplishing this task could be fruitful. For example, Yang et al.<xref rid="mp14424-bib-0065" ref-type="ref">
<sup>65</sup>
</xref> reported that radiomics features extracted from lungs and pleura contained information capable of discriminating between patients with and without NSCLC dry pleural dissemination (AUC: 0.93; 95% CI: 0.891&#x02013;0.968), which is a contraindication to primary tumor surgical resection that cannot always be detected by gross visualization. Our pleural effusion segmentations could enable other quantitative imaging biomarker studies. Importantly, our pleural effusion segmentations exclude primary tumor, as outlined in &#x0201c;RTSTRUCT&#x0201d; files, but users should not mistake this to mean that the effusions are necessarily benign. Microscopic tumor and macroscopic tumor below the threshold of radiologic detection are likely to exist in the effusions as delineated. It is also important to note that we failed to establish a guiding threshold for inclusion or exclusion of trace pleural fluid, an inconsistency that is reflected in several spatial similarity metrics we used to gauge interobserver variability. This stated, the effusion segmentations are still likely to be useful and may serve such research initiatives as correlating effusion parameters with clinical data available at the NSCLC&#x02010;Radiomics &#x0201c;Radiomics Lung1.clinical&#x02010;version3&#x02010;Oct 2019.csv&#x0201d; spreadsheet or investigating differences in radiomics features between effusion and primary tumor.</p><p>We acknowledge the limiting inconsistencies of human&#x02010;delineated segmentations, even those from trained radiologists or radiation oncologists. We also acknowledge intrinsic limitations in the metrics themselves. The DSC and &#x003ba; are both artificially increased in large volumes, and the power of &#x003ba; to penalize chance agreement is artificially decreased by the high number of true negatives in our segmentations (i.e., the high number of voxels that neither reviewer segmented as part of the target). We attempted to buffer this limitation by calculating and comparing sDSC values, which are less sensitive to variation in size. The 95 HD and symmetric ASD are not inflated by volume but are only snapshots of segmentations at their average (ASD) and near their worst (95HD) differences. Notwithstanding these limitations, we consider that the measures of interobserver variability obtained between radiologist and radiation oncologist reviewers justify acceptability of these segmentations for public use.</p></sec><sec sec-type="conclusions" id="mp14424-sec-0023"><label>5</label><title>CONCLUSIONS</title><p>We describe PleThora, a dataset of 402 expert&#x02010;vetted thoracic cavity segmentations, 78 expert&#x02010;vetted pleural effusion segmentations, and corresponding clinical and technical metadata made available to the public through TCIA at <ext-link ext-link-type="uri" xlink:href="https://doi.org/10.7937/tcia.2020.6c7y-gq39">https://doi.org/10.7937/tcia.2020.6c7y&#x02010;gq39</ext-link>.<xref rid="mp14424-bib-0051" ref-type="ref">
<sup>51</sup>
</xref> These segmentations have value for preprocessing steps in image analysis pipelines built for fundamental quantitative imaging tasks, including but not limited to pathologic lung segmentation, lesion detection, and radiomics feature extraction.</p></sec><sec sec-type="COI-statement" id="mp14424-sec-0025"><title>CONFLICT OF INTEREST</title><p>The authors declare no conflict of interest relevant to this publication or the data therein described.</p></sec><sec sec-type="supplementary-material"><title>Supporting information</title><supplementary-material content-type="local-data" id="mp14424-sup-0001"><caption><p>
<bold>Appendix S1</bold>. Data dictionary to the &#x0201c;Thorax and Pleural Effusion Segmentation Metadata&#x0201d; spreadsheet.</p></caption><media xlink:href="MP-47-5941-s001.pdf"><caption><p>Click here for additional data file.</p></caption></media></supplementary-material></sec></body><back><ack id="mp14424-sec-0024"><title>ACKNOWLEDGMENTS</title><p>
<bold>SMS</bold> is funded by a grant from the Swiss Cancer League (BIL KLS&#x02010;4300&#x02010;08&#x02010;2017). <bold>CDF</bold> has received funding and salary support unrelated to this project from: the National Institutes of Health (NIH) National Institute for Dental and Craniofacial Research Establishing Outcome Measures Award (1R01DE025248/R56DE025248) and an Academic Industrial Partnership Grant (R01DE028290); National Cancer Institute (NCI) Early Phase Clinical Trials in Imaging and Image&#x02010;Guided Interventions Program (1R01CA218148); an NIH/NCI Cancer Center Support Grant (CCSG) Pilot Research Program Award from the UT MD Anderson CCSG Radiation Oncology and Cancer Imaging Program (P30CA016672) and an NIH/NCI Head and Neck Specialized Programs of Research Excellence (SPORE) Developmental Research Program Award (P50CA097007); National Science Foundation (NSF), Division of Mathematical Sciences, Joint NIH/NSF Initiative on Quantitative Approaches to Biomedical Big Data (QuBBD) Grant (NSF 1557679); NSF Division of Civil, Mechanical, and Manufacturing Innovation (CMMI) standard grant (NSF 1933369) a National Institute of Biomedical Imaging and Bioengineering (NIBIB) Research Education Programs for Residents and Clinical Fellows Grant&#x000a0;(R25EB025787&#x02010;01);&#x000a0;the NIH Big Data to Knowledge (BD2K) Program of the NCI Early Stage Development of Technologies in Biomedical Computing, Informatics, and Big Data Science Award (1R01CA214825). <bold>CDF</bold> has also received direct industry grant support, honoraria, and travel funding from Elekta AB. <bold>WJZ</bold> and <bold>LG</bold> are supported in part by the NIH (UL1TR003167) and the Cancer Prevention and Research Institute of Texas (RP 170668). <bold>WJZ</bold> is also supported by an NIH grant (R01AG066749), and <bold>LG</bold> is also supported by a Learning Healthcare Award funded by the UTHealth Center for Clinical and Translational Science (CCTS).</p></ack><ref-list content-type="cited-references" id="mp14424-bibl-0001"><title>REFERENCES</title><ref id="mp14424-bib-0001"><label>1</label><mixed-citation publication-type="journal" id="mp14424-cit-0001">
<string-name>
<surname>Kumar</surname>
<given-names>V</given-names>
</string-name>, <string-name>
<surname>Gu</surname>
<given-names>Y</given-names>
</string-name>, <string-name>
<surname>Basu</surname>
<given-names>S</given-names>
</string-name>, et al. <article-title>Radiomics: the process and the challenges</article-title>. <source xml:lang="en">Magn Reson Imaging</source>. <year>2012</year>;<volume>30</volume>:<fpage>1234</fpage>&#x02013;<lpage>1248</lpage>.<pub-id pub-id-type="pmid">22898692</pub-id></mixed-citation></ref><ref id="mp14424-bib-0002"><label>2</label><mixed-citation publication-type="journal" id="mp14424-cit-0002">
<string-name>
<surname>Zhang</surname>
<given-names>G</given-names>
</string-name>, <string-name>
<surname>Jiang</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Yang</surname>
<given-names>Z</given-names>
</string-name>, et al. <article-title>Automatic nodule detection for lung cancer in CT images: a review</article-title>. <source xml:lang="en">Comput Biol Med</source>. <year>2018</year>;<volume>103</volume>:<fpage>287</fpage>&#x02013;<lpage>300</lpage>.<pub-id pub-id-type="pmid">30415174</pub-id></mixed-citation></ref><ref id="mp14424-bib-0003"><label>3</label><mixed-citation publication-type="journal" id="mp14424-cit-0003">
<string-name>
<surname>Ardila</surname>
<given-names>D</given-names>
</string-name>, <string-name>
<surname>Kiraly</surname>
<given-names>AP</given-names>
</string-name>, <string-name>
<surname>Bharadwaj</surname>
<given-names>S</given-names>
</string-name>, et al. <article-title>End&#x02010;to&#x02010;end lung cancer screening with three&#x02010;dimensional deep learning on low&#x02010;dose chest computed tomography</article-title>. <source xml:lang="en">Nat Med</source>. <year>2019</year>;<volume>25</volume>:<fpage>954</fpage>&#x02013;<lpage>961</lpage>.<pub-id pub-id-type="pmid">31110349</pub-id></mixed-citation></ref><ref id="mp14424-bib-0004"><label>4</label><mixed-citation publication-type="journal" id="mp14424-cit-0004">
<string-name>
<surname>Hu</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Hoffman</surname>
<given-names>EA</given-names>
</string-name>, <string-name>
<surname>Reinhardt</surname>
<given-names>JM</given-names>
</string-name>. <article-title>Automatic lung segmentation for accurate quantitation of volumetric X&#x02010;ray CT images</article-title>. <source xml:lang="en">IEEE Trans Med Imaging</source>. <year>2001</year>;<volume>20</volume>:<fpage>490</fpage>&#x02013;<lpage>498</lpage>.<pub-id pub-id-type="pmid">11437109</pub-id></mixed-citation></ref><ref id="mp14424-bib-0005"><label>5</label><mixed-citation publication-type="journal" id="mp14424-cit-0005">
<string-name>
<surname>Tong</surname>
<given-names>Y</given-names>
</string-name>, <string-name>
<surname>Udupa</surname>
<given-names>JK</given-names>
</string-name>, <string-name>
<surname>Odhner</surname>
<given-names>D</given-names>
</string-name>, <string-name>
<surname>Wu</surname>
<given-names>C</given-names>
</string-name>, <string-name>
<surname>Schuster</surname>
<given-names>SJ</given-names>
</string-name>, <string-name>
<surname>Torigian</surname>
<given-names>DA</given-names>
</string-name>. <article-title>Disease quantification on PET/CT images without explicit object delineation</article-title>. <source xml:lang="en">Med Image Anal</source>. <year>2019</year>;<volume>51</volume>:<fpage>169</fpage>&#x02013;<lpage>183</lpage>.<pub-id pub-id-type="pmid">30453165</pub-id></mixed-citation></ref><ref id="mp14424-bib-0006"><label>6</label><mixed-citation publication-type="journal" id="mp14424-cit-0006">
<string-name>
<surname>Mansoor</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Bagci</surname>
<given-names>U</given-names>
</string-name>, <string-name>
<surname>Foster</surname>
<given-names>B</given-names>
</string-name>, et al. <article-title>Segmentation and image analysis of abnormal lungs at CT: current approaches, challenges, and future trends</article-title>. <source xml:lang="en">Radiographics</source>. <year>2015</year>;<volume>35</volume>:<fpage>1056</fpage>&#x02013;<lpage>1076</lpage>.<pub-id pub-id-type="pmid">26172351</pub-id></mixed-citation></ref><ref id="mp14424-bib-0007"><label>7</label><mixed-citation publication-type="journal" id="mp14424-cit-0007">
<string-name>
<surname>Mansoor</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Bagci</surname>
<given-names>U</given-names>
</string-name>, <string-name>
<surname>Xu</surname>
<given-names>Z</given-names>
</string-name>, et al. <article-title>A generic approach to pathological lung segmentation</article-title>. <source xml:lang="en">IEEE Trans Med Imaging</source>. <year>2014</year>;<volume>33</volume>:<fpage>2293</fpage>&#x02013;<lpage>2310</lpage>.<pub-id pub-id-type="pmid">25020069</pub-id></mixed-citation></ref><ref id="mp14424-bib-0008"><label>8</label><mixed-citation publication-type="journal" id="mp14424-cit-0008">
<string-name>
<surname>Gordaliza</surname>
<given-names>PM</given-names>
</string-name>, <string-name>
<surname>Munoz&#x02010;Barrutia</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Abella</surname>
<given-names>M</given-names>
</string-name>, <string-name>
<surname>Desco</surname>
<given-names>M</given-names>
</string-name>, <string-name>
<surname>Sharpe</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Vaquero</surname>
<given-names>JJ</given-names>
</string-name>. <article-title>Unsupervised CT lung image segmentation of a mycobacterium tuberculosis infection model</article-title>. <source xml:lang="en">Sci Rep</source>. <year>2018</year>;<volume>8</volume>:<fpage>9802</fpage>.<pub-id pub-id-type="pmid">29955159</pub-id></mixed-citation></ref><ref id="mp14424-bib-0009"><label>9</label><mixed-citation publication-type="book" id="mp14424-cit-0009">
<string-name>
<surname>Wang</surname>
<given-names>X</given-names>
</string-name>, <string-name>
<surname>Teng</surname>
<given-names>P</given-names>
</string-name>, <string-name>
<surname>Lo</surname>
<given-names>P</given-names>
</string-name>, et al. <chapter-title>High throughput lung and lobar segmentation by 2D and 3D CNN on chest CT with diffuse lung disease</chapter-title> In: <person-group person-group-type="editor"><name name-style="western"><surname>Stoyanov</surname><given-names>D</given-names></name></person-group>, <person-group person-group-type="editor"><name name-style="western"><surname>Taylor</surname><given-names>Z</given-names></name></person-group>, <person-group person-group-type="editor"><name name-style="western"><surname>Kainz</surname><given-names>B</given-names></name></person-group>, eds. <source xml:lang="en">Image Analysis for Moving Organ, Breast, and Thoracic Images</source>. <publisher-loc>Cham</publisher-loc>: <publisher-name>Springer</publisher-name>; <year>2018</year>
<pub-id pub-id-type="doi">10.1007/978-3-030-00946-5_21</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0010"><label>10</label><mixed-citation publication-type="book" id="mp14424-cit-0010">
<string-name>
<surname>Harrison</surname>
<given-names>AP</given-names>
</string-name>, <string-name>
<surname>Xu</surname>
<given-names>Z</given-names>
</string-name>, <string-name>
<surname>George</surname>
<given-names>K</given-names>
</string-name>, <string-name>
<surname>Lu</surname>
<given-names>L</given-names>
</string-name>, <string-name>
<surname>Summers</surname>
<given-names>RM</given-names>
</string-name>, <string-name>
<surname>Mollura</surname>
<given-names>DJ</given-names>
</string-name>. <chapter-title>Progressive and multi&#x02010;path holistically nested neural networks for pathological lung segmentation from CT images</chapter-title> In: <person-group person-group-type="editor"><name name-style="western"><surname>Descoteaux</surname><given-names>M</given-names></name></person-group>, <person-group person-group-type="editor"><name name-style="western"><surname>Maier&#x02010;Hein</surname><given-names>L</given-names></name></person-group>, <person-group person-group-type="editor"><name name-style="western"><surname>Franz</surname><given-names>A</given-names></name></person-group>, <person-group person-group-type="editor"><name name-style="western"><surname>Jannin</surname><given-names>P</given-names></name></person-group>, <person-group person-group-type="editor"><name name-style="western"><surname>Collins</surname><given-names>DL</given-names></name></person-group>, <person-group person-group-type="editor"><name name-style="western"><surname>Duchesne</surname><given-names>S</given-names></name></person-group>, eds. <source xml:lang="en">Medical Image Computing and Computer Assisted Intervention &#x02212; MICCAI 2017</source>. <publisher-loc>Cham</publisher-loc>: <publisher-name>Springer</publisher-name>; <year>2017</year>
<pub-id pub-id-type="doi">10.1007/978-3-319-66179-7_71</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0011"><label>11</label><mixed-citation publication-type="journal" id="mp14424-cit-0011">
<string-name>
<surname>Deen</surname>
<given-names>KJ</given-names>
</string-name>, <string-name>
<surname>Ganesan</surname>
<given-names>R</given-names>
</string-name>, <string-name>
<surname>Merline</surname>
<given-names>A</given-names>
</string-name>. <article-title>Fuzzy&#x02010;C&#x02010;means clustering based segmentation and CNN&#x02010;classification for accurate segmentation of lung nodules</article-title>. <source xml:lang="en">Asian Pac J Cancer Prev</source>. <year>2017</year>;<volume>18</volume>:<fpage>1869</fpage>&#x02013;<lpage>1874</lpage>.<pub-id pub-id-type="pmid">28749127</pub-id></mixed-citation></ref><ref id="mp14424-bib-0012"><label>12</label><mixed-citation publication-type="journal" id="mp14424-cit-0012">
<string-name>
<surname>Gudmundsson</surname>
<given-names>E</given-names>
</string-name>, <string-name>
<surname>Straus</surname>
<given-names>CM</given-names>
</string-name>, <string-name>
<surname>Armato</surname>
<given-names>SG</given-names>
<suffix>3rd</suffix>
</string-name>. <article-title>Deep convolutional neural networks for the automated segmentation of malignant pleural mesothelioma on computed tomography scans</article-title>. <source xml:lang="en">J Med Imaging (Bellingham)</source>. <year>2018</year>;<volume>5</volume>:<elocation-id>034503</elocation-id>.<pub-id pub-id-type="pmid">30840717</pub-id></mixed-citation></ref><ref id="mp14424-bib-0013"><label>13</label><mixed-citation publication-type="journal" id="mp14424-cit-0013">
<string-name>
<surname>Park</surname>
<given-names>B</given-names>
</string-name>, <string-name>
<surname>Park</surname>
<given-names>H</given-names>
</string-name>, <string-name>
<surname>Lee</surname>
<given-names>SM</given-names>
</string-name>, <string-name>
<surname>Seo</surname>
<given-names>JB</given-names>
</string-name>, <string-name>
<surname>Kim</surname>
<given-names>N</given-names>
</string-name>. <article-title>Lung segmentation on HRCT and volumetric CT for diffuse interstitial lung disease using deep convolutional neural networks</article-title>. <source xml:lang="en">J Digit Imaging</source>. <year>2019</year>;<volume>32</volume>:<fpage>1019</fpage>&#x02013;<lpage>1026</lpage>.<pub-id pub-id-type="pmid">31396776</pub-id></mixed-citation></ref><ref id="mp14424-bib-0014"><label>14</label><mixed-citation publication-type="journal" id="mp14424-cit-0014">
<string-name>
<surname>Chen</surname>
<given-names>G</given-names>
</string-name>, <string-name>
<surname>Xiang</surname>
<given-names>D</given-names>
</string-name>, <string-name>
<surname>Zhang</surname>
<given-names>B</given-names>
</string-name>, et al. <article-title>Automatic pathological lung segmentation in low&#x02010;dose CT image using Eigenspace sparse shape composition</article-title>. <source xml:lang="en">IEEE Trans Med Imaging</source>. <year>2019</year>;<volume>38</volume>:<fpage>1736</fpage>&#x02013;<lpage>1749</lpage>.<pub-id pub-id-type="pmid">30605097</pub-id></mixed-citation></ref><ref id="mp14424-bib-0015"><label>15</label><mixed-citation publication-type="journal" id="mp14424-cit-0015">
<string-name>
<surname>Soliman</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Khalifa</surname>
<given-names>F</given-names>
</string-name>, <string-name>
<surname>Elnakib</surname>
<given-names>A</given-names>
</string-name>, et al. <article-title>Accurate lungs segmentation on CT chest images by adaptive appearance&#x02010;guided shape modeling</article-title>. <source xml:lang="en">IEEE Trans Med Imaging</source>. <year>2017</year>;<volume>36</volume>:<fpage>263</fpage>&#x02013;<lpage>276</lpage>.<pub-id pub-id-type="pmid">27705854</pub-id></mixed-citation></ref><ref id="mp14424-bib-0016"><label>16</label><mixed-citation publication-type="journal" id="mp14424-cit-0016">
<string-name>
<surname>Doel</surname>
<given-names>T</given-names>
</string-name>, <string-name>
<surname>Gavaghan</surname>
<given-names>DJ</given-names>
</string-name>, <string-name>
<surname>Grau</surname>
<given-names>V</given-names>
</string-name>. <article-title>Review of automatic pulmonary lobe segmentation methods from CT</article-title>. <source xml:lang="en">Comput Med Imaging Graph</source>. <year>2015</year>;<volume>40</volume>:<fpage>13</fpage>&#x02013;<lpage>29</lpage>.<pub-id pub-id-type="pmid">25467805</pub-id></mixed-citation></ref><ref id="mp14424-bib-0017"><label>17</label><mixed-citation publication-type="journal" id="mp14424-cit-0017">
<string-name>
<surname>Ren</surname>
<given-names>H</given-names>
</string-name>, <string-name>
<surname>Zhou</surname>
<given-names>L</given-names>
</string-name>, <string-name>
<surname>Liu</surname>
<given-names>G</given-names>
</string-name>, et al. <article-title>An unsupervised semi&#x02010;automated pulmonary nodule segmentation method based on enhanced region growing</article-title>. <source xml:lang="en">Quant Imaging Med Surg</source>. <year>2020</year>;<volume>10</volume>:<fpage>233</fpage>&#x02013;<lpage>242</lpage>.<pub-id pub-id-type="pmid">31956545</pub-id></mixed-citation></ref><ref id="mp14424-bib-0018"><label>18</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0018">
<string-name>
<surname>Dong</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Lu</surname>
<given-names>K</given-names>
</string-name>, <string-name>
<surname>Dai</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Xue</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Zhai</surname>
<given-names>R</given-names>
</string-name>. <article-title>Auto&#x02010;Segmentation of Pathological Lung Parenchyma Based on Region Growing Method</article-title>. Paper presented at: Internet Multimedia Computing and Service; Aug. 23&#x02013;25, 2017; Qingdao, China. <pub-id pub-id-type="doi">10.1007/978-981-10-8530-7_23</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0019"><label>19</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0019">
<string-name>
<surname>Hofmanninger</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Prayer</surname>
<given-names>F</given-names>
</string-name>, <string-name>
<surname>Pan</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Rohrich</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Prosch</surname>
<given-names>H</given-names>
</string-name>, <string-name>
<surname>Langs</surname>
<given-names>G</given-names>
</string-name>. <article-title>Automatic lung segmentation in routine imaging is a data diversity problem, not a methodology problem</article-title>. arXiv:2001.11767. Published 31 Jan <year>2020</year>. Accessed 4 Apr 2020.</mixed-citation></ref><ref id="mp14424-bib-0020"><label>20</label><mixed-citation publication-type="journal" id="mp14424-cit-0020">
<string-name>
<surname>Shaukat</surname>
<given-names>F</given-names>
</string-name>, <string-name>
<surname>Raja</surname>
<given-names>G</given-names>
</string-name>, <string-name>
<surname>Frangi</surname>
<given-names>AF</given-names>
</string-name>. <article-title>Computer&#x02010;aided detection of lung nodules: a review</article-title>. <source xml:lang="en">J Med Imaging</source>. <year>2019</year>;<volume>6</volume>:<fpage>1</fpage>.</mixed-citation></ref><ref id="mp14424-bib-0021"><label>21</label><mixed-citation publication-type="journal" id="mp14424-cit-0021">
<string-name>
<surname>Clark</surname>
<given-names>K</given-names>
</string-name>, <string-name>
<surname>Vendt</surname>
<given-names>B</given-names>
</string-name>, <string-name>
<surname>Smith</surname>
<given-names>K</given-names>
</string-name>, et al. <article-title>The cancer imaging archive (TCIA): maintaining and operating a public information repository</article-title>. <source xml:lang="en">J Digit Imaging</source>. <year>2013</year>;<volume>26</volume>:<fpage>1045</fpage>&#x02013;<lpage>1057</lpage>.<pub-id pub-id-type="pmid">23884657</pub-id></mixed-citation></ref><ref id="mp14424-bib-0022"><label>22</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0022">
<string-name>
<surname>Aerts</surname>
<given-names>HJWL</given-names>
</string-name>, <string-name>
<surname>Wee</surname>
<given-names>L</given-names>
</string-name>, <string-name>
<surname>Rios Velazquez</surname>
<given-names>E</given-names>
</string-name>, et al. <article-title>Data from NSCLC&#x02010;Radiomics [Dataset]</article-title>. In: <italic>The Cancer Imaging Archive</italic>; <year>2019</year>
<pub-id pub-id-type="doi">10.7937/K9/TCIA.2015.PF0M9REI</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0023"><label>23</label><mixed-citation publication-type="journal" id="mp14424-cit-0023">
<string-name>
<surname>Aerts</surname>
<given-names>HJ</given-names>
</string-name>, <string-name>
<surname>Velazquez</surname>
<given-names>ER</given-names>
</string-name>, <string-name>
<surname>Leijenaar</surname>
<given-names>RT</given-names>
</string-name>, et al. <article-title>Decoding tumour phenotype by noninvasive imaging using a quantitative radiomics approach</article-title>. <source xml:lang="en">Nat Commun</source>. <year>2014</year>;<volume>5</volume>:<fpage>4006</fpage>.<pub-id pub-id-type="pmid">24892406</pub-id></mixed-citation></ref><ref id="mp14424-bib-0024"><label>24</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0024">
<string-name>
<surname>Li</surname>
<given-names>X</given-names>
</string-name>, <string-name>
<surname>Morgan</surname>
<given-names>PS</given-names>
</string-name>, <string-name>
<surname>Ashburner</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Smith</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Rorden</surname>
<given-names>C</given-names>
</string-name>. <article-title>dcm2niix.exe [computer program]</article-title>. Version v1.0.201811142020. Accessed January 23, 2019. Available from: <ext-link ext-link-type="uri" xlink:href="https://www.nitrc.org/plugins/mwiki/index.php/dcm2nii:MainPage">https://www.nitrc.org/plugins/mwiki/index.php/dcm2nii:MainPage</ext-link>
</mixed-citation></ref><ref id="mp14424-bib-0025"><label>25</label><mixed-citation publication-type="journal" id="mp14424-cit-0025">
<string-name>
<surname>Li</surname>
<given-names>X</given-names>
</string-name>, <string-name>
<surname>Morgan</surname>
<given-names>PS</given-names>
</string-name>, <string-name>
<surname>Ashburner</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Smith</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Rorden</surname>
<given-names>C</given-names>
</string-name>. <article-title>The first step for neuroimaging data analysis: DICOM to NIfTI conversion</article-title>. <source xml:lang="en">J Neurosci Methods</source>. <year>2016</year>;<volume>264</volume>:<fpage>47</fpage>&#x02013;<lpage>56</lpage>.<pub-id pub-id-type="pmid">26945974</pub-id></mixed-citation></ref><ref id="mp14424-bib-0026"><label>26</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0026">
<string-name>
<surname>Pesiuk</surname>
<given-names>V</given-names>
</string-name>. <article-title>Lung Segmentation (3D) [computer program]</article-title>. GitHub 2017. Accessed December 15, 2018. Available from <ext-link ext-link-type="uri" xlink:href="https://github.com/imlab-uiip/lung-segmentation-3d">https://github.com/imlab&#x02010;uiip/lung&#x02010;segmentation&#x02010;3d</ext-link>
</mixed-citation></ref><ref id="mp14424-bib-0027"><label>27</label><mixed-citation publication-type="journal" id="mp14424-cit-0027">
<string-name>
<surname>Yushkevich</surname>
<given-names>PA</given-names>
</string-name>, <string-name>
<surname>Piven</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Hazlett</surname>
<given-names>HC</given-names>
</string-name>, et al. <article-title>User&#x02010;guided 3D active contour segmentation of anatomical structures: significantly improved efficiency and reliability</article-title>. <source xml:lang="en">Neuroimage</source>. <year>2006</year>;<volume>31</volume>:<fpage>1116</fpage>&#x02013;<lpage>1128</lpage>.<pub-id pub-id-type="pmid">16545965</pub-id></mixed-citation></ref><ref id="mp14424-bib-0028"><label>28</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0028">
<string-name>
<surname>Panchal</surname>
<given-names>A</given-names>
</string-name>. <article-title>Dicompyler [computer program]</article-title>. Version vol 0.4.22014. Accessed February 4, 2020. Available from <ext-link ext-link-type="uri" xlink:href="https://www.dicompyler.com/">https://www.dicompyler.com/</ext-link>
</mixed-citation></ref><ref id="mp14424-bib-0029"><label>29</label><mixed-citation publication-type="journal" id="mp14424-cit-0029">
<string-name>
<surname>Oliphant</surname>
<given-names>TE</given-names>
</string-name>. <article-title>Python for scientific computing</article-title>. <source xml:lang="en">Comput Sci Eng</source>. <year>2007</year>;<volume>9</volume>:<fpage>10</fpage>&#x02013;<lpage>20</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0030"><label>30</label><mixed-citation publication-type="journal" id="mp14424-cit-0030">
<string-name>
<surname>Abraham</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Pedregosa</surname>
<given-names>F</given-names>
</string-name>, <string-name>
<surname>Eickenberg</surname>
<given-names>M</given-names>
</string-name>, et al. <article-title>Machine learning for neuroimaging with scikit&#x02010;learn</article-title>. <source xml:lang="en">Front Neuroinform</source>. <year>2014</year>;<volume>8</volume>:<fpage>14</fpage>.<pub-id pub-id-type="pmid">24600388</pub-id></mixed-citation></ref><ref id="mp14424-bib-0031"><label>31</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0031">
<string-name>
<surname>McKinney</surname>
<given-names>W</given-names>
</string-name>. <article-title>Data structures for statistical computing in Python</article-title>. <italic>Proceedings of the 9th Python in Science Conference (SCIPY 2010)</italic>
<year>2010</year>;445:<fpage>51</fpage>&#x02013;<lpage>56</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0032"><label>32</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0032">
<string-name>
<surname>Nikolov</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Blackwell</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Mendes</surname>
<given-names>R</given-names>
</string-name>, et al. <article-title>Deep learning to achieve clinically applicable segmentation of head and neck anatomy for radiotherapy</article-title>. arXiv:1809.04430. Published 12 Sep 2018. Accessed 07 Mar 2019.</mixed-citation></ref><ref id="mp14424-bib-0033"><label>33</label><mixed-citation publication-type="journal" id="mp14424-cit-0033">
<string-name>
<surname>Pedregosa</surname>
<given-names>F</given-names>
</string-name>, <string-name>
<surname>Varoquaux</surname>
<given-names>G</given-names>
</string-name>, <string-name>
<surname>Gramfort</surname>
<given-names>A</given-names>
</string-name>, et al. <article-title>Scikit&#x02010;learn: machine learning in Python</article-title>. <source xml:lang="en">J Mach Learn Res</source>. <year>2011</year>;<volume>12</volume>:<fpage>2825</fpage>&#x02013;<lpage>2830</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0034"><label>34</label><mixed-citation publication-type="journal" id="mp14424-cit-0034">
<string-name>
<surname>Hunter</surname>
<given-names>JD</given-names>
</string-name>. <article-title>Matplotlib: a 2d graphics environment</article-title>. <source xml:lang="en">Comput Sci Eng</source>. <year>2007</year>;<volume>9</volume>:<fpage>90</fpage>&#x02013;<lpage>95</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0035"><label>35</label><mixed-citation publication-type="book" id="mp14424-cit-0035">
<string-name>
<surname>Waskom</surname>
<given-names>M</given-names>
</string-name>, <string-name>
<surname>Botvinnik</surname>
<given-names>O</given-names>
</string-name>, <string-name>
<surname>O'Kane</surname>
<given-names>D</given-names>
</string-name>, et al. <source xml:lang="en">MWaskom/Seaborn</source> [computer program]. Version 0.9.0: Zenodo; 2018. Accessed 03 Apr 2020. Available from <pub-id pub-id-type="doi">10.5281/zenodo.1313201</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0036"><label>36</label><mixed-citation publication-type="journal" id="mp14424-cit-0036">
<string-name>
<surname>Dice</surname>
<given-names>LR</given-names>
</string-name>. <article-title>Measures of the amount of ecologic association between species</article-title>. <source xml:lang="en">Ecology</source>. <year>1945</year>;<volume>26</volume>:<fpage>297</fpage>.</mixed-citation></ref><ref id="mp14424-bib-0037"><label>37</label><mixed-citation publication-type="journal" id="mp14424-cit-0037">
<string-name>
<surname>Cohen</surname>
<given-names>J</given-names>
</string-name>. <article-title>A coefficient of agreement for nominal scales</article-title>. <source xml:lang="en">Educat Psychol Meas</source>. <year>1960</year>;<volume>20</volume>:<fpage>37</fpage>&#x02013;<lpage>46</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0038"><label>38</label><mixed-citation publication-type="journal" id="mp14424-cit-0038">
<string-name>
<surname>Huttenlocher</surname>
<given-names>DP</given-names>
</string-name>, <string-name>
<surname>Klanderman</surname>
<given-names>GA</given-names>
</string-name>, <string-name>
<surname>Rucklidge</surname>
<given-names>WJ</given-names>
</string-name>. <article-title>Comparing images using the Hausdorff distance</article-title>. <source xml:lang="en">IEEE Trans Pattern Anal Mach Intell</source>. <year>1993</year>;<volume>15</volume>:<fpage>850</fpage>&#x02013;<lpage>863</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0039"><label>39</label><mixed-citation publication-type="journal" id="mp14424-cit-0039">
<string-name>
<surname>Yao</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Bliton</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Summers</surname>
<given-names>RM</given-names>
</string-name>. <article-title>Automatic segmentation and measurement of pleural effusions on CT</article-title>. <source xml:lang="en">IEEE Trans Bio&#x02010;med Eng</source>. <year>2013</year>;<volume>60</volume>:<fpage>1834</fpage>&#x02013;<lpage>1840</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0040"><label>40</label><mixed-citation publication-type="journal" id="mp14424-cit-0040">
<string-name>
<surname>von Falck</surname>
<given-names>C</given-names>
</string-name>, <string-name>
<surname>Meier</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>J&#x000f6;rdens</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>King</surname>
<given-names>B</given-names>
</string-name>, <string-name>
<surname>Galanski</surname>
<given-names>M</given-names>
</string-name>, <string-name>
<surname>Shin</surname>
<given-names>HO</given-names>
</string-name>. <article-title>Semiautomated segmentation of pleural effusions in MDCT datasets</article-title>. <source xml:lang="en">Acad Radiol</source>. <year>2010</year>;<volume>17</volume>:<fpage>841</fpage>&#x02013;<lpage>848</lpage>.<pub-id pub-id-type="pmid">20399688</pub-id></mixed-citation></ref><ref id="mp14424-bib-0041"><label>41</label><mixed-citation publication-type="journal" id="mp14424-cit-0041">
<string-name>
<surname>Anderson</surname>
<given-names>SA</given-names>
</string-name>, <string-name>
<surname>Danelson</surname>
<given-names>KA</given-names>
</string-name>, <string-name>
<surname>Mammarappallil</surname>
<given-names>JG</given-names>
</string-name>, <string-name>
<surname>Chiles</surname>
<given-names>C</given-names>
</string-name>, <string-name>
<surname>Stitzel</surname>
<given-names>JD</given-names>
</string-name>. <article-title>Semiautomatic method of quantifying pleural effusions using computed tomography scans &#x02010; biomed 2013</article-title>. <source xml:lang="en">Biomed Sci Instrum</source>. <year>2013</year>;<volume>49</volume>:<fpage>13</fpage>&#x02013;<lpage>19</lpage>.<pub-id pub-id-type="pmid">23686175</pub-id></mixed-citation></ref><ref id="mp14424-bib-0042"><label>42</label><mixed-citation publication-type="journal" id="mp14424-cit-0042">
<string-name>
<surname>Song</surname>
<given-names>L</given-names>
</string-name>, <string-name>
<surname>Gao</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Wang</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Hu</surname>
<given-names>H</given-names>
</string-name>, <string-name>
<surname>Guo</surname>
<given-names>Y</given-names>
</string-name>. <article-title>A quantitative evaluation of pleural effusion on computed tomography scans using B&#x02010;spline and local clustering level set</article-title>. <source xml:lang="en">J Xray Sci Technol</source>. <year>2017</year>;<volume>25</volume>:<fpage>887</fpage>&#x02013;<lpage>905</lpage>.<pub-id pub-id-type="pmid">28550270</pub-id></mixed-citation></ref><ref id="mp14424-bib-0043"><label>43</label><mixed-citation publication-type="journal" id="mp14424-cit-0043">
<string-name>
<surname>Moy</surname>
<given-names>MP</given-names>
</string-name>, <string-name>
<surname>Levsky</surname>
<given-names>JM</given-names>
</string-name>, <string-name>
<surname>Berko</surname>
<given-names>NS</given-names>
</string-name>, <string-name>
<surname>Godelman</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Jain</surname>
<given-names>VR</given-names>
</string-name>, <string-name>
<surname>Haramati</surname>
<given-names>LB</given-names>
</string-name>. <article-title>A new, simple method for estimating pleural effusion size on CT scans</article-title>. <source xml:lang="en">Chest</source>. <year>2013</year>;<volume>143</volume>:<fpage>1054</fpage>&#x02013;<lpage>1059</lpage>.<pub-id pub-id-type="pmid">23632863</pub-id></mixed-citation></ref><ref id="mp14424-bib-0044"><label>44</label><mixed-citation publication-type="journal" id="mp14424-cit-0044">
<string-name>
<surname>Brock</surname>
<given-names>KK</given-names>
</string-name>, <string-name>
<surname>Mutic</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>McNutt</surname>
<given-names>TR</given-names>
</string-name>, <string-name>
<surname>Li</surname>
<given-names>H</given-names>
</string-name>, <string-name>
<surname>Kessler</surname>
<given-names>ML</given-names>
</string-name>. <article-title>Use of image registration and fusion algorithms and techniques in radiotherapy: report of the AAPM Radiation Therapy Committee Task Group No. 132</article-title>. <source xml:lang="en">Med Phys</source>. <year>2017</year>;<volume>44</volume>:<fpage>e43</fpage>&#x02013;<lpage>e76</lpage>.<pub-id pub-id-type="pmid">28376237</pub-id></mixed-citation></ref><ref id="mp14424-bib-0045"><label>45</label><mixed-citation publication-type="journal" id="mp14424-cit-0045">
<string-name>
<surname>Yang</surname>
<given-names>J</given-names>
</string-name>, <string-name>
<surname>Veeraraghavan</surname>
<given-names>H</given-names>
</string-name>, <string-name>
<surname>Armato</surname>
<given-names>SG</given-names>
<suffix>3rd</suffix>
</string-name>, et al. <article-title>Autosegmentation for thoracic radiation treatment planning: a grand challenge at AAPM 2017</article-title>. <source xml:lang="en">Med Phys</source>. <year>2018</year>;<volume>45</volume>:<fpage>4568</fpage>&#x02013;<lpage>4581</lpage>.<pub-id pub-id-type="pmid">30144101</pub-id></mixed-citation></ref><ref id="mp14424-bib-0046"><label>46</label><mixed-citation publication-type="journal" id="mp14424-cit-0046">
<string-name>
<surname>Landis</surname>
<given-names>JR</given-names>
</string-name>, <string-name>
<surname>Koch</surname>
<given-names>GG</given-names>
</string-name>. <article-title>The measurement of observer agreement for categorical data</article-title>. <source xml:lang="en">Biometrics</source>. <year>1977</year>;<volume>33</volume>:<fpage>159</fpage>.<pub-id pub-id-type="pmid">843571</pub-id></mixed-citation></ref><ref id="mp14424-bib-0047"><label>47</label><mixed-citation publication-type="journal" id="mp14424-cit-0047">
<string-name>
<surname>Vaassen</surname>
<given-names>F</given-names>
</string-name>, <string-name>
<surname>Hazelaar</surname>
<given-names>C</given-names>
</string-name>, <string-name>
<surname>Vaniqui</surname>
<given-names>A</given-names>
</string-name>, et al. <article-title>Evaluation of measures for assessing time&#x02010;saving of automatic organ&#x02010;at&#x02010;risk segmentation in radiotherapy</article-title>. <source xml:lang="en">Phy Imaging Radiat Oncol</source>. <year>2020</year>;<volume>13</volume>:<fpage>1</fpage>&#x02013;<lpage>6</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0048"><label>48</label><mixed-citation publication-type="journal" id="mp14424-cit-0048">
<string-name>
<surname>Mann</surname>
<given-names>HB</given-names>
</string-name>, <string-name>
<surname>Whitney</surname>
<given-names>DR</given-names>
</string-name>. <article-title>On a test of whether one of two random variables is stochastically larger than the other</article-title>. <source xml:lang="en">Ann Math Stat</source>. <year>1947</year>;<volume>18</volume>:<fpage>50</fpage>&#x02013;<lpage>60</lpage>.</mixed-citation></ref><ref id="mp14424-bib-0049"><label>49</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0049">
<string-name>
<surname>Kiser</surname>
<given-names>K</given-names>
</string-name>, <string-name>
<surname>Barman</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Stieb</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Fuller</surname>
<given-names>CD</given-names>
</string-name>, <string-name>
<surname>Giancardo</surname>
<given-names>L</given-names>
</string-name>. <article-title>Novel autosegmentation spatial similarity metrics capture the time required to correct segmentations better than traditional metrics in a thoracic cavity segmentation workflow</article-title>. medRxiv: <pub-id pub-id-type="doi">10.1101/2020.05.14.20102103</pub-id>. Published 18 May 2020. Accessed 18 May 2020.</mixed-citation></ref><ref id="mp14424-bib-0050"><label>50</label><mixed-citation publication-type="journal" id="mp14424-cit-0050">
<string-name>
<surname>Wilkinson</surname>
<given-names>MD</given-names>
</string-name>, <string-name>
<surname>Dumontier</surname>
<given-names>M</given-names>
</string-name>, <string-name>
<surname>Aalbersberg</surname>
<given-names>IJ</given-names>
</string-name>, et al. <article-title>The FAIR Guiding Principles for scientific data management and stewardship</article-title>. <source xml:lang="en">Sci Data</source>. <year>2016</year>;<volume>3</volume>:<fpage>160018</fpage>.<pub-id pub-id-type="pmid">26978244</pub-id></mixed-citation></ref><ref id="mp14424-bib-0051"><label>51</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0051">
<string-name>
<surname>Kiser</surname>
<given-names>KJ</given-names>
</string-name>, <string-name>
<surname>Ahmed</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Stieb</surname>
<given-names>SM</given-names>
</string-name>, et al. <article-title>Data from the thoracic volume and pleural effusion segmentations in diseased lungs for benchmarking chest CT processing pipelines [Dataset]</article-title>. In: <italic>The Cancer Imaging Archive</italic>; <year>2020</year>
<pub-id pub-id-type="doi">10.7937/tcia.2020.6c7y-gq39</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0052"><label>52</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0052">
<string-name>
<surname>Ronneberger</surname>
<given-names>O</given-names>
</string-name>, <string-name>
<surname>Fischer</surname>
<given-names>P</given-names>
</string-name>, <string-name>
<surname>Brox</surname>
<given-names>T</given-names>
</string-name>. <article-title>U&#x02010;Net: Convolutional Networks for Biomedical Image Segmentation</article-title>. Paper presented at: Medical Image Computing and Computer&#x02010;Assisted Intervention &#x02013; MICCAI 2015; 5&#x02013;9 October, 2015; Munich, Germany. <pub-id pub-id-type="doi">10.1007/978-3-319-24574-4_28</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0053"><label>53</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0053">
<string-name>
<surname>&#x000c7;i&#x000e7;ek</surname>
<given-names>&#x000d6;</given-names>
</string-name>, <string-name>
<surname>Abdulkadir</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Lienkamp</surname>
<given-names>SS</given-names>
</string-name>, <string-name>
<surname>Brox</surname>
<given-names>T</given-names>
</string-name>, <string-name>
<surname>Ronneberger</surname>
<given-names>O</given-names>
</string-name>. <article-title>3D U&#x02010;Net: Learning Dense Volumetric Segmentation from Sparse Annotation</article-title>. Paper presented at: Medical Image Computing and Computer&#x02010;Assisted Intervention &#x02013; MICCAI 2016; 17&#x02013;21 October, 2016; Athens, Greece. <pub-id pub-id-type="doi">10.1007/978-3-319-46723-8_49</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0054"><label>54</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0054">
<string-name>
<surname>Milletari</surname>
<given-names>F</given-names>
</string-name>, <string-name>
<surname>Navab</surname>
<given-names>N</given-names>
</string-name>, <string-name>
<surname>Ahmadi</surname>
<given-names>S</given-names>
</string-name>. <article-title>V&#x02010;Net: Fully Convolutional Neural Networks for Volumetric Medical Image Segmentation</article-title>. Paper presented at: 2016 Fourth International Conference on 3D Vision (3DV); 25&#x02013;28 Oct. 2016, 2016. <pub-id pub-id-type="doi">10.1109/3DV.2016.79</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0055"><label>55</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0055">
<string-name>
<surname>Zhou</surname>
<given-names>Z</given-names>
</string-name>, <string-name>
<surname>Rahman Siddiquee</surname>
<given-names>MM</given-names>
</string-name>, <string-name>
<surname>Tajbakhsh</surname>
<given-names>N</given-names>
</string-name>, <string-name>
<surname>Liang</surname>
<given-names>J</given-names>
</string-name>. <article-title>UNet++: A Nested U&#x02010;Net Architecture for Medical Image Segmentation</article-title>. Paper presented at: Deep Learning in Medical Image Analysis and Multimodal Learning for Clinical Decision Support; 2018//, 2018; Cham. <pub-id pub-id-type="doi">10.1007/978-3-030-00889-5_1</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0056"><label>56</label><mixed-citation publication-type="journal" id="mp14424-cit-0056">
<string-name>
<surname>Brosch</surname>
<given-names>T</given-names>
</string-name>, <string-name>
<surname>Tang</surname>
<given-names>LYW</given-names>
</string-name>, <string-name>
<surname>Yoo</surname>
<given-names>Y</given-names>
</string-name>, <string-name>
<surname>Li</surname>
<given-names>DKB</given-names>
</string-name>, <string-name>
<surname>Traboulsee</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Tam</surname>
<given-names>R</given-names>
</string-name>. <article-title>Deep 3D convolutional encoder networks with shortcuts for multiscale feature integration applied to multiple sclerosis lesion segmentation</article-title>. <source xml:lang="en">IEEE Trans Med Imaging</source>. <year>2016</year>;<volume>35</volume>:<fpage>1229</fpage>&#x02013;<lpage>1239</lpage>.<pub-id pub-id-type="pmid">26886978</pub-id></mixed-citation></ref><ref id="mp14424-bib-0057"><label>57</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0057">
<string-name>
<surname>Kingma</surname>
<given-names>DP</given-names>
</string-name>, <string-name>
<surname>Ba</surname>
<given-names>JL</given-names>
</string-name>. <article-title>Adam: A Method for Stochastic Optimization</article-title>. Paper presented at: 3rd Internation Conference on Learning Represenctiona, ICLR 2015; 7&#x02013;9 May, 2015; San Diego, CA. arXiv:1412.6980v9.</mixed-citation></ref><ref id="mp14424-bib-0058"><label>58</label><mixed-citation publication-type="miscellaneous" id="mp14424-cit-0058">
<string-name>
<surname>Barman</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Inam</surname>
<given-names>ME</given-names>
</string-name>, <string-name>
<surname>Lee</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Savitz</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Sheth</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Giancardo</surname>
<given-names>L</given-names>
</string-name>. <article-title>Determining ischemic stroke from CT&#x02010;angiography imaging using symmetry&#x02010;sensitive convolutional networks</article-title>. 2019 IEEE 16th International Symposium on Biomedical Imaging; <year>2019</year>
<pub-id pub-id-type="doi">10.1109/ISBI.2019.8759475</pub-id>
</mixed-citation></ref><ref id="mp14424-bib-0059"><label>59</label><mixed-citation publication-type="journal" id="mp14424-cit-0059">
<string-name>
<surname>Ryu</surname>
<given-names>JS</given-names>
</string-name>, <string-name>
<surname>Lim</surname>
<given-names>JH</given-names>
</string-name>, <string-name>
<surname>Lee</surname>
<given-names>JM</given-names>
</string-name>, et al. <article-title>Minimal pleural effusion in small cell lung cancer: proportion, mechanisms, and prognostic effect</article-title>. <source xml:lang="en">Radiology</source>. <year>2016</year>;<volume>278</volume>:<fpage>593</fpage>&#x02013;<lpage>600</lpage>.<pub-id pub-id-type="pmid">26323029</pub-id></mixed-citation></ref><ref id="mp14424-bib-0060"><label>60</label><mixed-citation publication-type="journal" id="mp14424-cit-0060">
<string-name>
<surname>Zhang</surname>
<given-names>X</given-names>
</string-name>, <string-name>
<surname>Duan</surname>
<given-names>H</given-names>
</string-name>, <string-name>
<surname>Yu</surname>
<given-names>Y</given-names>
</string-name>, et al. <article-title>Differential diagnosis between benign and malignant pleural effusion with dual&#x02010;energy spectral CT</article-title>. <source xml:lang="en">PLoS One</source>. <year>2018</year>;<volume>13</volume>:<elocation-id>e0193714</elocation-id>.<pub-id pub-id-type="pmid">29641601</pub-id></mixed-citation></ref><ref id="mp14424-bib-0061"><label>61</label><mixed-citation publication-type="journal" id="mp14424-cit-0061">
<string-name>
<surname>Otoshi</surname>
<given-names>T</given-names>
</string-name>, <string-name>
<surname>Kataoka</surname>
<given-names>Y</given-names>
</string-name>, <string-name>
<surname>Ikegaki</surname>
<given-names>S</given-names>
</string-name>, et al. <article-title>Pleural effusion biomarkers and computed tomography findings in diagnosing malignant pleural mesothelioma: a retrospective study in a single center</article-title>. <source xml:lang="en">PLoS One</source>. <year>2017</year>;<volume>12</volume>:<elocation-id>e0185850</elocation-id>.<pub-id pub-id-type="pmid">28968445</pub-id></mixed-citation></ref><ref id="mp14424-bib-0062"><label>62</label><mixed-citation publication-type="journal" id="mp14424-cit-0062">
<string-name>
<surname>Herrera Lara</surname>
<given-names>S</given-names>
</string-name>, <string-name>
<surname>Fernandez&#x02010;Fabrellas</surname>
<given-names>E</given-names>
</string-name>, <string-name>
<surname>Juan Samper</surname>
<given-names>G</given-names>
</string-name>, et al. <article-title>Predicting malignant and paramalignant pleural effusions by combining clinical, radiological and pleural fluid analytical parameters</article-title>. <source xml:lang="en">Lung</source>. <year>2017</year>;<volume>195</volume>:<fpage>653</fpage>&#x02013;<lpage>660</lpage>.<pub-id pub-id-type="pmid">28656381</pub-id></mixed-citation></ref><ref id="mp14424-bib-0063"><label>63</label><mixed-citation publication-type="journal" id="mp14424-cit-0063">
<string-name>
<surname>Nakajima</surname>
<given-names>R</given-names>
</string-name>, <string-name>
<surname>Abe</surname>
<given-names>K</given-names>
</string-name>, <string-name>
<surname>Sakai</surname>
<given-names>S</given-names>
</string-name>. <article-title>Diagnostic ability of FDG&#x02010;PET/CT in the detection of malignant pleural effusion</article-title>. <source xml:lang="en">Medicine (Baltimore)</source>. <year>2015</year>;<volume>94</volume>:<elocation-id>e1010</elocation-id>.<pub-id pub-id-type="pmid">26200610</pub-id></mixed-citation></ref><ref id="mp14424-bib-0064"><label>64</label><mixed-citation publication-type="journal" id="mp14424-cit-0064">
<string-name>
<surname>Hallifax</surname>
<given-names>RJ</given-names>
</string-name>, <string-name>
<surname>Talwar</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Wrightson</surname>
<given-names>JM</given-names>
</string-name>, <string-name>
<surname>Edey</surname>
<given-names>A</given-names>
</string-name>, <string-name>
<surname>Gleeson</surname>
<given-names>FV</given-names>
</string-name>. <article-title>State&#x02010;of&#x02010;the&#x02010;art: radiological investigation of pleural disease</article-title>. <source xml:lang="en">Respir Med</source>. <year>2017</year>;<volume>124</volume>:<fpage>88</fpage>&#x02013;<lpage>99</lpage>.<pub-id pub-id-type="pmid">28233652</pub-id></mixed-citation></ref><ref id="mp14424-bib-0065"><label>65</label><mixed-citation publication-type="journal" id="mp14424-cit-0065">
<string-name>
<surname>Yang</surname>
<given-names>M</given-names>
</string-name>, <string-name>
<surname>Ren</surname>
<given-names>Y</given-names>
</string-name>, <string-name>
<surname>She</surname>
<given-names>Y</given-names>
</string-name>, et al. <article-title>Imaging phenotype using radiomics to predict dry pleural dissemination in non&#x02010;small cell lung cancer</article-title>. <source xml:lang="en">Ann Transl Med</source>. <year>2019</year>;<volume>7</volume>:<fpage>259</fpage>.<pub-id pub-id-type="pmid">31355226</pub-id></mixed-citation></ref></ref-list></back></article>