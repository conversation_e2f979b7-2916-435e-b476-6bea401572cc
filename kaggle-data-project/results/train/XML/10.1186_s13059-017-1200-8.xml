<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Genome Biol</journal-id><journal-id journal-id-type="iso-abbrev">Genome Biol</journal-id><journal-title-group><journal-title>Genome Biology</journal-title></journal-title-group><issn pub-type="ppub">1474-7596</issn><issn pub-type="epub">1474-760X</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5407026</article-id><article-id pub-id-type="publisher-id">1200</article-id><article-id pub-id-type="doi">10.1186/s13059-017-1200-8</article-id><article-categories><subj-group subj-group-type="heading"><subject>Method</subject></subj-group></article-categories><title-group><article-title>SCALE: modeling allele-specific gene expression by single-cell RNA sequencing</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Jiang</surname><given-names>Yuchao</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Zhang</surname><given-names>Nancy R.</given-names></name><address><phone>(+1) ************</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Li</surname><given-names>Mingyao</given-names></name><address><phone>(+1) ************</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 8972</institution-id><institution-id institution-id-type="GRID">grid.25879.31</institution-id><institution>Genomics and Computational Biology Graduate Program, Perelman School of Medicine, </institution><institution>University of Pennsylvania, </institution></institution-wrap>Philadelphia, PA 19104 USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 8972</institution-id><institution-id institution-id-type="GRID">grid.25879.31</institution-id><institution>Department of Statistics, The Wharton School, </institution><institution>University of Pennsylvania, </institution></institution-wrap>Philadelphia, PA 19104 USA </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 8972</institution-id><institution-id institution-id-type="GRID">grid.25879.31</institution-id><institution>Department of Biostatistics and Epidemiology, Perelman School of Medicine, </institution><institution>University of Pennsylvania, </institution></institution-wrap>Philadelphia, PA 19104 USA </aff></contrib-group><pub-date pub-type="epub"><day>26</day><month>4</month><year>2017</year></pub-date><pub-date pub-type="pmc-release"><day>26</day><month>4</month><year>2017</year></pub-date><pub-date pub-type="collection"><year>2017</year></pub-date><volume>18</volume><elocation-id>74</elocation-id><history><date date-type="received"><day>17</day><month>2</month><year>2017</year></date><date date-type="accepted"><day>24</day><month>3</month><year>2017</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2017</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><p>Allele-specific expression is traditionally studied by bulk RNA sequencing, which measures average expression across cells. Single-cell RNA sequencing allows the comparison of expression distribution between the two alleles of a diploid organism and the characterization of allele-specific bursting. Here, we propose SCALE to analyze genome-wide allele-specific bursting, with adjustment of technical variability. SCALE detects genes exhibiting allelic differences in bursting parameters and genes whose alleles burst non-independently. We apply SCALE to mouse blastocyst and human fibroblast cells and find that <italic>cis</italic> control in gene expression overwhelmingly manifests as differences in burst frequency.</p><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s13059-017-1200-8) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Single-cell RNA sequencing</kwd><kwd>Expression stochasticity</kwd><kwd>Allele-specific expression</kwd><kwd>Transcriptional bursting</kwd><kwd><italic>cis</italic> and <italic>trans</italic> transcriptional control</kwd><kwd>Technical variability</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>R01GM108600</award-id><award-id>R01HL113147</award-id><principal-award-recipient><name><surname>Li</surname><given-names>Mingyao</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2017</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>In diploid organisms, two copies of each autosomal gene are available for transcription, and differences in gene expression level between the two alleles are widespread in tissues [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR7">7</xref>]. Allele-specific expression (ASE), in its extreme, is found in genomic imprinting, where the allele from one parent is uniformly silenced across cells, and in random X-chromosome inactivation, where one of the two X-chromosomes in females is randomly silenced. During the past decade, using single-nucleotide polymorphism (SNP)-sensitive microarrays and bulk RNA sequencing (RNA-seq), more subtle expression differences between the two alleles were found, mostly in the form of allelic imbalance of varying magnitudes in mean expression across cells [<xref ref-type="bibr" rid="CR8">8</xref>&#x02013;<xref ref-type="bibr" rid="CR11">11</xref>]. In some cases such expression differences between alleles can lead to phenotypic consequences and result in disease [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR12">12</xref>&#x02013;<xref ref-type="bibr" rid="CR14">14</xref>]. These studies, though revelatory, were at the bulk tissue level, where one could only observe average expression across a possibly heterogeneous mixture of cells.</p><p>Recent developments in single-cell RNA sequencing (scRNA-seq) have made possible the better characterization of the nature of allelic differences in gene expression across individual cells [<xref ref-type="bibr" rid="CR6">6</xref>, <xref ref-type="bibr" rid="CR15">15</xref>, <xref ref-type="bibr" rid="CR16">16</xref>]. For example, recent scRNA-seq studies estimated that 12&#x02013;24% of the expressed genes are monoallelically expressed during mouse preimplantation development [<xref ref-type="bibr" rid="CR2">2</xref>] and that 76.4% of the heterozygous loci across all cells express only one allele [<xref ref-type="bibr" rid="CR17">17</xref>]. These ongoing efforts have improved our understanding of gene regulation and enriched our vocabulary in describing gene expression at the allelic level with single-cell resolution.</p><p>Despite this rapid progress, much of the potential offered by scRNA-seq data remains untapped. ASE, in the setting of bulk RNA-seq data, is usually quantified by comparing the mean expression level of the two alleles. However, due to the inherent stochasticity of gene expression across cells, the characterization of ASE using scRNA-seq data should look beyond mean expression. A fundamental property of gene expression is transcriptional bursting, in which transcription from DNA to RNA occurs in bursts, depending on whether the gene&#x02019;s promoter is activated (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1a</xref>) [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR19">19</xref>]. Transcriptional bursting is a widespread phenomenon that has been observed across many species, including bacteria [<xref ref-type="bibr" rid="CR20">20</xref>], yeast [<xref ref-type="bibr" rid="CR21">21</xref>], <italic>Drosophila</italic> embryos [<xref ref-type="bibr" rid="CR22">22</xref>], and mammalian cells [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR24">24</xref>], and is one of the primary sources of expression variability in single cells. Figure&#x000a0;<xref rid="Fig1" ref-type="fig">1b</xref> illustrates the expression across time of the two alleles of a gene. Under the assumption of ergodicity, each cell in a scRNA-seq sample pool is at a different time in this process, implying that, for each allele, some cells might be in the transcriptional &#x0201c;ON&#x0201d; state, whereas other cells are in the &#x0201c;OFF&#x0201d; state. While in the ON state, the magnitude and length of the burst can also vary across cells, further complicating analysis. For each expressed heterozygous site, a scRNA-seq experiment gives us the bivariate distribution of the expression of its two alleles across cells, allowing us to compare the alleles not only in their mean, but also in their distribution. In this study, we use scRNA-seq data to characterize transcriptional bursting in an allele-specific manner and detect genes with allelic differences in the parameters of this process.<fig id="Fig1"><label>Fig. 1</label><caption><p>Allele-specific transcriptional bursting and gene categorization by single-cell ASE. <bold>a</bold> Transcription from DNA to RNA occurs in bursts, where genes switch between the &#x0201c;ON&#x0201d; and the &#x0201c;OFF&#x0201d; states. <italic>k</italic>
<sub><italic>on</italic></sub>, <italic>k</italic>
<sub><italic>off</italic></sub>, <italic>s</italic>, and <italic>d</italic> are activation, deactivation, transcription, and mRNA decay rate in the kinetic model, respectively. <bold>b</bold> Transcriptional bursting of the two alleles of a gene give rise to cells expressing neither, one, or both alleles of a gene, sampled as vertical snapshots along the time axis. Partially adapted from Reinius and Sandberg [<xref ref-type="bibr" rid="CR6">6</xref>]. <bold>c</bold> Empirical Bayes framework that categorizes each gene as silent, monoallelic and biallelic (biallelic bursty, one-allele constitutive, and both-alleles constitutive) based on ASE data with single-cell resolution</p></caption><graphic xlink:href="13059_2017_1200_Fig1_HTML" id="MO1"/></fig>
</p><p>Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>] first studied bursting kinetics of stochastic gene expression from scRNA-seq data, using a Beta-Poisson model, and estimated the kinetic parameters via a Gibbs sampler. In this early attempt, they assumed shared bursting kinetics between the two alleles and modeled total expression of a gene instead of ASE. Current scRNA-seq protocols often introduce substantial technical noise (e.g., gene dropouts, amplification and sequencing bias; Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S1) [<xref ref-type="bibr" rid="CR26">26</xref>&#x02013;<xref ref-type="bibr" rid="CR30">30</xref>] and this is largely ignored in Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>] and another recent scRNA-seq study by Borel et al. [<xref ref-type="bibr" rid="CR17">17</xref>], where, in particular, gene dropout may have led to overestimation of the pervasiveness of monoallelic expression (ME). Realizing this, Kim et al. [<xref ref-type="bibr" rid="CR31">31</xref>] incorporated measurements of technical noise from external spike-in molecules in the identification of stochastic ASE (defined as excessive variability in allelic ratios among cells) and concluded that more than 80% of stochastic ASE in mouse embryonic stem cells is due to scRNA-seq technical noise. Kim et al.&#x02019;s analysis was restricted to the identification of random ME and did not consider more general patterns of ASE, such as allele-specific transcriptional bursting.</p><p>scRNA-seq also enables us to quantify the degree of dependence between the expression of the two alleles. A previous RNA fluorescence in situ hybridization (FISH) experiment fluorescently labeled 20 genes in an allele-specific manner and showed that there was no significant deviation from independent bursting between the two alleles [<xref ref-type="bibr" rid="CR32">32</xref>]. A recent scRNA-seq study of mouse cells through embryonic development [<xref ref-type="bibr" rid="CR2">2</xref>] produced similar conclusions on the genome-wide level: they modeled transcript loss by splitting each cell&#x02019;s lysate into two fractions of equal volume and controlling for false discoveries by diluting bulk RNA down to the single-cell level. Their results suggest that, on the genome-wide scale, assuming both alleles share the same bursting kinetics, the two alleles of most genes burst independently. Deviation from the theoretical curve in Deng et al. [<xref ref-type="bibr" rid="CR2">2</xref>] for independent bursting with shared allele-specific kinetics, however, can be due to not only dependent bursting, but also different bursting kinetics.</p><p>In this study, we develop SCALE (Single-Cell ALlelic Expression), a systematic statistical framework to study ASE in single cells by examining allele-specific transcriptional bursting kinetics. Our main goal is to detect and characterize differences between the two alleles in their expression distribution across cells. As a by-product, we also quantify the degree of dependence between the expression of the two alleles. SCALE is comprised of three steps. First, an empirical Bayes method determines, for each <italic>gene</italic>, whether it is silent, monoallelically expressed, or biallelically expressed based on its allele-specific counts across cells (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1c</xref>). Next, for genes determined to be biallelic bursty (i.e., both alleles have zero expression level in some but not all cells), a Poisson-Beta hierarchical model is used to estimate allele-specific transcriptional kinetics while accounting for technical noise and cell size differences. Finally, resampling-based testing procedures are developed to detect allelic differences in transcriptional burst size or burst frequency and identify genes whose alleles exhibit non-independent transcription.</p><p>In silico simulations are conducted to investigate estimation accuracy and testing power. The stringency of model assumptions, and the robustness of the proposed procedures to the violation of these assumptions, will be discussed as they are introduced. Using SCALE, we re-analyze the scRNA-seq data for 122 mouse blastocyst cells [<xref ref-type="bibr" rid="CR2">2</xref>] and 104 human fibroblast cells [<xref ref-type="bibr" rid="CR17">17</xref>]. The mouse blastocyst study initially found abundant random ME generated by independent and stochastic allelic transcription [<xref ref-type="bibr" rid="CR2">2</xref>]; the human fibroblast study reported that 76.4% of the heterozygous loci displayed patterns of ME [<xref ref-type="bibr" rid="CR17">17</xref>]. Through proper modeling of technical noise, our re-analysis of these two datasets brings forth new insights: While for 90% of the bursty genes there are no significant deviations from the assumption of independent allelic bursting and shared bursting kinetics, the remaining bursty genes show different burst frequencies by a <italic>cis</italic>-effect and/or non-independent bursting with an enrichment in coordinated bursting. Collectively, we present a genome-wide approach to systematically analyze expression variation in an allele-specific manner with single-cell resolution. SCALE is an open-source R package available at <ext-link ext-link-type="uri" xlink:href="https://github.com/yuchaojiang/SCALE">https://github.com/yuchaojiang/SCALE</ext-link>.</p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>Methods overview</title><p>Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> shows an overview of the analysis pipeline of SCALE. We start with allele-specific read counts of endogenous RNAs across all profiled single cells. An empirical Bayes method is adopted to classify expression of genes into monoallelic, biallelic, and silent states based on ASE data across cells. SCALE then estimates allele-specific transcriptional bursting parameters via a hierarchical Poisson-Beta model, while adjusting for technical variabilities and cell size differences. Statistical testing procedures are then performed to identify genes whose two alleles have different bursting parameters or burst non-independently. We describe each of these steps in turn.<fig id="Fig2"><label>Fig. 2</label><caption><p>Overview of analysis pipeline of SCALE. SCALE takes as input allele-specific read counts at heterozygous loci and carries out three major steps: (i) gene classification using an empirical Bayes method, (ii) estimation of allele-specific transcriptional kinetics using a Poisson-Beta hierarchical model with adjustment of technical variability and cell size, (iii) testing of the two alleles of a gene to determine if they have different bursting kinetics and/or non-independent firing using a hypothesis testing framework</p></caption><graphic xlink:href="13059_2017_1200_Fig2_HTML" id="MO2"/></fig>
</p><sec id="Sec4"><title>Gene classification by ASE data across cells</title><p>SCALE first determines for each gene whether its expression is silent, paternal/maternal monoallelic, or biallelic. Figure&#x000a0;<xref rid="Fig1" ref-type="fig">1c</xref> outlines this categorization scheme. Briefly, for each gene, each cell is assigned to one of four categories corresponding to scenarios where both alleles are off (<inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \varnothing $$\end{document}</tex-math><mml:math id="M2"><mml:mo>&#x02205;</mml:mo></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq1.gif"/></alternatives></inline-formula>), only the A allele is expressed (<inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ A $$\end{document}</tex-math><mml:math id="M4"><mml:mi>A</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq2.gif"/></alternatives></inline-formula>), only the B allele is expressed (<inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ B $$\end{document}</tex-math><mml:math id="M6"><mml:mi>B</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq3.gif"/></alternatives></inline-formula>), and both alleles are expressed (<inline-formula id="IEq4"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ AB $$\end{document}</tex-math><mml:math id="M8"><mml:mi mathvariant="italic">AB</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq4.gif"/></alternatives></inline-formula>). An expectation-maximization (EM) algorithm is implemented for parameter estimation. This classification accounts for both sequencing depth variation and sequencing errors. The assignment of the <italic>gene</italic> is then determined based on the posterior assignments of all cells. For example, if all cells are assigned to <inline-formula id="IEq5"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\varnothing \right\} $$\end{document}</tex-math><mml:math id="M10"><mml:mfenced close="}" open="{"><mml:mo>&#x02205;</mml:mo></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq5.gif"/></alternatives></inline-formula>, the gene is silent; if all cells are assigned to either <inline-formula id="IEq6"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\varnothing \right\} $$\end{document}</tex-math><mml:math id="M12"><mml:mfenced close="}" open="{"><mml:mo>&#x02205;</mml:mo></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq6.gif"/></alternatives></inline-formula> or <inline-formula id="IEq7"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{ A\right\} $$\end{document}</tex-math><mml:math id="M14"><mml:mfenced close="}" open="{"><mml:mi>A</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq7.gif"/></alternatives></inline-formula>, the gene has ME of the A allele; if all cells are assigned to either <inline-formula id="IEq8"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\varnothing \right\} $$\end{document}</tex-math><mml:math id="M16"><mml:mfenced close="}" open="{"><mml:mo>&#x02205;</mml:mo></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq8.gif"/></alternatives></inline-formula> or <inline-formula id="IEq9"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{ B\right\} $$\end{document}</tex-math><mml:math id="M18"><mml:mfenced close="}" open="{"><mml:mi>B</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq9.gif"/></alternatives></inline-formula>, the gene has ME of the B allele; if both the A and B allele are expressed in the cell pool, then the gene is biallelically expressed. Refer to &#x0201c;<xref rid="Sec14" ref-type="sec">Methods</xref>&#x0201d; for detailed statistical methods and the EM algorithm.</p><p>Through simulation studies (see the &#x0201c;<xref rid="Sec11" ref-type="sec">Assessment of estimation accuracy and testing power</xref>&#x0201d; section), we show that bursting parameters can only be stably estimated for <italic>bursty</italic> genes, that is, genes that are silent in a non-zero proportion of cells. Therefore, for biallelic bursty genes, allele-specific transcriptional kinetics are modeled through a Poisson-Beta distribution with adjustment for technical noise, see next section. For silent, monoallelically expressed, or constitutively expressed genes, there is no way nor need to estimate bursting kinetics for both alleles.</p></sec><sec id="Sec5"><title>Allele-specific transcriptional bursting</title><p>When studying ASE in single cells, it is critical to consider transcriptional bursting due to its pervasiveness in various organisms [<xref ref-type="bibr" rid="CR20">20</xref>&#x02013;<xref ref-type="bibr" rid="CR24">24</xref>]. We adopt a Poisson-Beta hierarchical model to quantify allele-specific transcriptional kinetics while accounting for dropout events and amplification and sequencing bias. Here, we start by reviewing the relevant literature with regard to transcriptional bursting at the single-cell level.</p><p>A two-state model for gene transcription is shown in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1a</xref>, where genes switch between the ON and OFF states with activation and deactivation rates <inline-formula id="IEq10"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M20"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq10.gif"/></alternatives></inline-formula> and <inline-formula id="IEq11"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M22"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq11.gif"/></alternatives></inline-formula>. When the gene is in the ON state, DNA is transcribed into RNA at rate <inline-formula id="IEq12"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ s $$\end{document}</tex-math><mml:math id="M24"><mml:mi>s</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq12.gif"/></alternatives></inline-formula> while RNA decays at rate <inline-formula id="IEq13"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ d $$\end{document}</tex-math><mml:math id="M26"><mml:mi>d</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq13.gif"/></alternatives></inline-formula>. A Poisson-Beta stochastic model was first proposed by Kepler and Elston [<xref ref-type="bibr" rid="CR33">33</xref>]:<disp-formula id="Equa"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{l} Y\sim \mathrm{Poisson}(sp),\\ {} p\sim \mathrm{Beta}\left({k}_{on},{k}_{off}\right),\end{array} $$\end{document}</tex-math><mml:math id="M28"><mml:mtable columnalign="left"><mml:mtr><mml:mtd><mml:mi>Y</mml:mi><mml:mo>~</mml:mo><mml:mi mathvariant="normal">Poisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mi>p</mml:mi><mml:mo>~</mml:mo><mml:mi mathvariant="normal">Beta</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:mfenced><mml:mtext>,</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equa.gif" position="anchor"/></alternatives></disp-formula>
</p><p>where <inline-formula id="IEq14"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ Y $$\end{document}</tex-math><mml:math id="M30"><mml:mi>Y</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq14.gif"/></alternatives></inline-formula> is the number of mRNA molecules and <inline-formula id="IEq15"><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ p $$\end{document}</tex-math><mml:math id="M32"><mml:mi>p</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq15.gif"/></alternatives></inline-formula> is the fraction of time that the gene spends in the active state, the latter having mean <inline-formula id="IEq16"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on}/\left({k}_{on}+{k}_{off}\right) $$\end{document}</tex-math><mml:math id="M34"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub><mml:mo stretchy="true">/</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq16.gif"/></alternatives></inline-formula>. Under this model, <inline-formula id="IEq17"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 1/{k}_{on} $$\end{document}</tex-math><mml:math id="M36"><mml:mn>1</mml:mn><mml:mo stretchy="true">/</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq17.gif"/></alternatives></inline-formula> and <inline-formula id="IEq18"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 1/{k}_{off} $$\end{document}</tex-math><mml:math id="M38"><mml:mn>1</mml:mn><mml:mo stretchy="true">/</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq18.gif"/></alternatives></inline-formula> are the average waiting times in the inactive and active states, respectively. <italic>Burst size</italic>, defined as the average number of synthesized mRNA per burst episode, is given by <inline-formula id="IEq19"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ s/{k}_{off} $$\end{document}</tex-math><mml:math id="M40"><mml:mi>s</mml:mi><mml:mo stretchy="true">/</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq19.gif"/></alternatives></inline-formula>, and <italic>burst frequency</italic> is given by <inline-formula id="IEq20"><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M42"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq20.gif"/></alternatives></inline-formula>. Kepler and Elston [<xref ref-type="bibr" rid="CR33">33</xref>] gave detailed analytical solutions via differential equations. Raj et al. [<xref ref-type="bibr" rid="CR23">23</xref>] offered empirical support for this model via single-molecule FISH on reporter genes. Since the kinetic parameters are measured in units of time and only the stationary distribution is assumed to be observed (e.g., when cells are killed for sequencing and fixed for FISH), the rate of decay <inline-formula id="IEq21"><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ d $$\end{document}</tex-math><mml:math id="M44"><mml:mi>d</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq21.gif"/></alternatives></inline-formula> is set to one [<xref ref-type="bibr" rid="CR15">15</xref>]. This is equivalent to having three kinetic parameters <inline-formula id="IEq22"><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{ s,{k}_{on},{k}_{off}\right\} $$\end{document}</tex-math><mml:math id="M46"><mml:mrow><mml:mo>{</mml:mo><mml:mi>s</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>k</mml:mi></mml:mrow><mml:mrow><mml:mi>o</mml:mi><mml:mi>n</mml:mi></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>k</mml:mi></mml:mrow><mml:mrow><mml:mi>o</mml:mi><mml:mi>f</mml:mi><mml:mi>f</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq22.gif"/></alternatives></inline-formula>, each normalized by the decay rate <inline-formula id="IEq23"><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ d $$\end{document}</tex-math><mml:math id="M48"><mml:mi>d</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq23.gif"/></alternatives></inline-formula>. Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>] applied this Poisson-Beta model to total gene-level transcript counts from scRNA-seq data of mouse embryonic stem cells. While they found that the inferred kinetic parameters are correlated with RNA polymerase II occupancy and histone modification [<xref ref-type="bibr" rid="CR25">25</xref>], they didn&#x02019;t address the issue of technical noise, especially the dropout events, introduced by scRNA-seq. Failure to account for gene dropouts may lead to biased estimation of bursting kinetics.</p><p>Furthermore, since the transitions between active and inactive states occur separately for the two alleles, when ASE data are available, it seems more appropriate to model transcriptional bursting in an allele-specific manner. The fact that transcriptional bursting occurs independently for the two alleles has been supported by empirical evidence: case studies based on imaging methods have suggested that the two alleles of genes are transcribed in an independent fashion [<xref ref-type="bibr" rid="CR34">34</xref>, <xref ref-type="bibr" rid="CR35">35</xref>]; using scRNA-seq data, Deng et al. [<xref ref-type="bibr" rid="CR2">2</xref>] showed that the two alleles of most genes tend to fire independently with the assumption that both alleles share the same set of kinetic parameters. These findings, although limited in scale or relying on strong assumptions, emphasize the need to study transcriptional bursting in an allele-specific manner.</p></sec><sec id="Sec6"><title>Technical noise in scRNA-seq and other complicating factors</title><p>Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S1 outlines the major steps of the scRNA-seq protocols and the sources of bias that are introduced during library preparation and sequencing. After the cells are captured and lysed, exogenous spike-ins are added as internal controls, which have fixed and known concentrations and can thus be used to convert the number of sequenced transcripts into actual abundances. During the reverse transcription, pre-amplification, and library preparation steps, lowly expressed transcripts might be lost, in which case they will not be detected during sequencing. This leads to so-called &#x0201c;dropout&#x0201d; events. Since spike-ins undergo the same experimental procedure as endogenous RNAs in a cell, amplification and sequencing bias can be captured and estimated through the spike-in molecules. Here we adopt the statistical model in TASC (Toolkit for Analysis of Single Cell data, unpublished), which explicitly models the technical noise through spike-ins. TASC&#x02019;s model is based on the key observation that the probability of a gene being a dropout depends on its true expression in the cell, with lowly expressed genes more likely to drop out. Specifically, let <inline-formula id="IEq24"><alternatives><tex-math id="M49">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg} $$\end{document}</tex-math><mml:math id="M50"><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq24.gif"/></alternatives></inline-formula> and <inline-formula id="IEq25"><alternatives><tex-math id="M51">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_{cg} $$\end{document}</tex-math><mml:math id="M52"><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq25.gif"/></alternatives></inline-formula> be, respectively, the observed and true expression levels of gene <inline-formula id="IEq26"><alternatives><tex-math id="M53">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ g $$\end{document}</tex-math><mml:math id="M54"><mml:mi>g</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq26.gif"/></alternatives></inline-formula> in cell <inline-formula id="IEq27"><alternatives><tex-math id="M55">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ c $$\end{document}</tex-math><mml:math id="M56"><mml:mi>c</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq27.gif"/></alternatives></inline-formula>. The hierarchical mixture model used to model dropout, amplification, and sequencing bias is:<disp-formula id="Equb"><alternatives><tex-math id="M57">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{l}{Q}_{c g} \sim {Z}_{c g}\mathrm{Poisson}\left({\alpha}_c{\left({Y}_{c g}\right)}^{\beta_c}\right),\\ {}{Z}_{c g} \sim \mathrm{Bernoulli}\left({\pi}_{c g}\right),\\ {}{\pi}_{c g}=\mathrm{expit}\left({\kappa}_c+{\tau}_c \log \left({Y}_{c g}\right)\right),\end{array} $$\end{document}</tex-math><mml:math id="M58"><mml:mtable columnalign="left"><mml:mtr><mml:mtd><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:msub><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mi mathvariant="normal">Poisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003b1;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:msup><mml:mfenced close=")" open="("><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:mfenced><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:msup></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Bernoulli</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>&#x003c0;</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>&#x003c0;</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003ba;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced><mml:mtext>,</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equb.gif" position="anchor"/></alternatives></disp-formula>
</p><p>where <inline-formula id="IEq28"><alternatives><tex-math id="M59">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Z}_{cg} $$\end{document}</tex-math><mml:math id="M60"><mml:msub><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq28.gif"/></alternatives></inline-formula> is a Bernoulli random variable indicating that gene <inline-formula id="IEq29"><alternatives><tex-math id="M61">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ g $$\end{document}</tex-math><mml:math id="M62"><mml:mi>g</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq29.gif"/></alternatives></inline-formula> is detected in cell <inline-formula id="IEq30"><alternatives><tex-math id="M63">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ c $$\end{document}</tex-math><mml:math id="M64"><mml:mi>c</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq30.gif"/></alternatives></inline-formula>, that is, a dropout event has not occurred. The success probability <inline-formula id="IEq31"><alternatives><tex-math id="M65">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\pi}_{cg}= P\left({Z}_{cg}=1\right) $$\end{document}</tex-math><mml:math id="M66"><mml:msub><mml:mi>&#x003c0;</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mi>P</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq31.gif"/></alternatives></inline-formula> depends on <inline-formula id="IEq32"><alternatives><tex-math id="M67">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \log \left({Y}_{cg}\right) $$\end{document}</tex-math><mml:math id="M68"><mml:mi>log</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>Y</mml:mi></mml:mrow><mml:mrow><mml:mi>c</mml:mi><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq32.gif"/></alternatives></inline-formula>, the logarithm of the true underlying expression. Cell-specific parameter <inline-formula id="IEq33"><alternatives><tex-math id="M69">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\alpha}_c $$\end{document}</tex-math><mml:math id="M70"><mml:msub><mml:mi>&#x003b1;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq33.gif"/></alternatives></inline-formula> models the capture and sequencing efficiency; <inline-formula id="IEq34"><alternatives><tex-math id="M71">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\beta}_c $$\end{document}</tex-math><mml:math id="M72"><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq34.gif"/></alternatives></inline-formula> models the amplification bias; <inline-formula id="IEq35"><alternatives><tex-math id="M73">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\kappa}_c $$\end{document}</tex-math><mml:math id="M74"><mml:msub><mml:mi>&#x003ba;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq35.gif"/></alternatives></inline-formula> and <inline-formula id="IEq36"><alternatives><tex-math id="M75">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\tau}_c $$\end{document}</tex-math><mml:math id="M76"><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq36.gif"/></alternatives></inline-formula> characterize whether a transcript is successfully captured in the library. This model will later be used to adjust for technical noise in ASE.</p><p>As input to SCALE, we recommend scRNA-seq data from cells of the same type. Unwanted heterogeneity, however, still persists as the cells may differ in size or may be in different phases of the cell cycle. Through a series of single-cell FISH experiments, Padovan-Merhar et al. [<xref ref-type="bibr" rid="CR36">36</xref>] showed how gene transcription depends on these exogenous factors: burst size is independent of cell cycle but is kept proportional to cell size by a <italic>trans</italic> mechanism; burst frequency is independent of cell size but is reduced approximately by half, through a <italic>cis</italic> mechanism, between G1 and G2 phases to compensate for the doubling of DNA content. Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S2 illustrates how burst size and burst frequency change with cell size and cell cycle phase. Note that while the burst frequency from <italic>each</italic> DNA copy is halved when the amount of DNA is doubled, the total burst frequency remains roughly constant through the cell cycle. Thus, SCALE adjusts for variation in cell size through modulation of burst size and does not adjust for variation in cell cycle phase. Details will be given below.</p><p>Cell size can be measured in multiple ways. Padovan-Merhar et al. [<xref ref-type="bibr" rid="CR36">36</xref>] proposed using the expression level of <italic>GAPDH</italic> as a cell size marker. When spike-ins are available, we use the ratio of the total number of endogenous RNA reads over the total number of spike-in reads as a measure (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S2) of the total RNA volume, which was shown to be a good proxy for cell size [<xref ref-type="bibr" rid="CR28">28</xref>]. SCALE allows the user to input the cell sizes <inline-formula id="IEq37"><alternatives><tex-math id="M77">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\phi}_c $$\end{document}</tex-math><mml:math id="M78"><mml:msub><mml:mi>&#x003d5;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq37.gif"/></alternatives></inline-formula> if these are available through other means.</p></sec><sec id="Sec7"><title>Modeling transcriptional bursting with adjustment for technical and cell-size variation</title><p>We are now ready to formulate the allele-specific bursting model for scRNA-seq data. For genes that are categorized as biallelic bursty (with the proportion of cells expressing each allele between 5 and 95% from the Bayes framework), SCALE proceeds to estimate the allele-specific bursting parameters using a hierarchical model:<disp-formula id="Equc"><alternatives><tex-math id="M79">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{ll}{Y}_{c g}^A \sim \mathrm{Poisson}\left({\phi}_c{s}_g^A{p}_{c g}^A\right)\hfill &#x00026; {Y}_{c g}^B \sim \mathrm{Poisson}\left({\phi}_c{s}_g^B{p}_{c g}^B\right)\hfill \\ {}{p}_{c g}^A \sim \mathrm{Beta}\left({k}_{on, g}^A,{k}_{off, g}^A\right)\hfill &#x00026; {p}_{c g}^B \sim \mathrm{Beta}\left({k}_{on, g}^B,{k}_{off, g}^B\right),\hfill \end{array} $$\end{document}</tex-math><mml:math id="M80"><mml:mtable columnalign="left"><mml:mtr columnalign="left"><mml:mtd columnalign="left"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Poisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003d5;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:msubsup><mml:mi>s</mml:mi><mml:mi>g</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>p</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced></mml:mtd><mml:mtd columnalign="left"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Poisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003d5;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:msubsup><mml:mi>s</mml:mi><mml:mi>g</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:msubsup><mml:mi>p</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mrow></mml:mfenced></mml:mtd></mml:mtr><mml:mtr columnalign="left"><mml:mtd columnalign="left"><mml:msubsup><mml:mi>p</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Beta</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced></mml:mtd><mml:mtd columnalign="left"><mml:msubsup><mml:mi>p</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Beta</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup></mml:mfenced><mml:mtext>,</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equc.gif" position="anchor"/></alternatives></disp-formula>
</p><p>where <inline-formula id="IEq38"><alternatives><tex-math id="M81">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_{cg}^A $$\end{document}</tex-math><mml:math id="M82"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq38.gif"/></alternatives></inline-formula> and <inline-formula id="IEq39"><alternatives><tex-math id="M83">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_{cg}^B $$\end{document}</tex-math><mml:math id="M84"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq39.gif"/></alternatives></inline-formula> are the true ASE for gene <inline-formula id="IEq40"><alternatives><tex-math id="M85">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ g $$\end{document}</tex-math><mml:math id="M86"><mml:mi>g</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq40.gif"/></alternatives></inline-formula> in cell <inline-formula id="IEq41"><alternatives><tex-math id="M87">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ c $$\end{document}</tex-math><mml:math id="M88"><mml:mi>c</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq41.gif"/></alternatives></inline-formula>. The two alleles of each gene are modeled by separate Poisson-Beta distributions with kinetic parameters that are gene- and allele-specific. These two Poisson-Beta distributions share the same cell size factor <inline-formula id="IEq42"><alternatives><tex-math id="M89">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\phi}_c $$\end{document}</tex-math><mml:math id="M90"><mml:msub><mml:mi>&#x003d5;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq42.gif"/></alternatives></inline-formula>, which affects burst size. The true ASE <inline-formula id="IEq43"><alternatives><tex-math id="M91">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_{cg}^A $$\end{document}</tex-math><mml:math id="M92"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq43.gif"/></alternatives></inline-formula> and <inline-formula id="IEq44"><alternatives><tex-math id="M93">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_{cg}^B $$\end{document}</tex-math><mml:math id="M94"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq44.gif"/></alternatives></inline-formula> are not directly observable. The observed allele-specific read counts <inline-formula id="IEq45"><alternatives><tex-math id="M95">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg}^A $$\end{document}</tex-math><mml:math id="M96"><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq45.gif"/></alternatives></inline-formula> and <inline-formula id="IEq46"><alternatives><tex-math id="M97">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg}^B $$\end{document}</tex-math><mml:math id="M98"><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq46.gif"/></alternatives></inline-formula> are confounded by technical noise and follow the Poisson mixture model outlined in the previous section:<disp-formula id="Equd"><alternatives><tex-math id="M99">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{ll}{Q}_{c g}^A \sim {Z}_{c g}^A\mathrm{Poisson}\left({\alpha}_c{\left({Y}_{c g}^A\right)}^{\beta_c}\right)\hfill &#x00026; {Q}_{c g}^B \sim {Z}_{c g}^B\mathrm{Poisson}\left({\alpha}_c{\left({Y}_{c g}^B\right)}^{\beta_c}\right)\hfill \\ {}{Z}_{c g}^A \sim \mathrm{Bernoulli}\left({\pi}_{c g}^A\right)\hfill &#x00026; {Z}_{c g}^B \sim \mathrm{Bernoulli}\left({\pi}_{c g}^B\right)\hfill \\ {}{\pi}_{c g}^A=\mathrm{expit}\left({\kappa}_c+{\tau}_c \log \left({Y}_{c g}^A\right)\right)\hfill &#x00026; {\pi}_{c g}^B=\mathrm{expit}\left({\kappa}_c+{\tau}_c \log \left({Y}_{c g}^B\right)\right).\hfill \end{array} $$\end{document}</tex-math><mml:math id="M100"><mml:mtable columnalign="left"><mml:mtr columnalign="left"><mml:mtd columnalign="left"><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:msubsup><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mi mathvariant="normal">Poisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003b1;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:msup></mml:mrow></mml:mfenced></mml:mtd><mml:mtd columnalign="left"><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:msubsup><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mi mathvariant="normal">Poisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003b1;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mfenced><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:msup></mml:mrow></mml:mfenced></mml:mtd></mml:mtr><mml:mtr columnalign="left"><mml:mtd columnalign="left"><mml:msubsup><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Bernoulli</mml:mi><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>&#x003c0;</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced></mml:mtd><mml:mtd columnalign="left"><mml:msubsup><mml:mi>Z</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Bernoulli</mml:mi><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>&#x003c0;</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mfenced></mml:mtd></mml:mtr><mml:mtr columnalign="left"><mml:mtd columnalign="left"><mml:msubsup><mml:mi>&#x003c0;</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003ba;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced></mml:mrow></mml:mfenced></mml:mtd><mml:mtd columnalign="left"><mml:msubsup><mml:mi>&#x003c0;</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>&#x003ba;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mfenced></mml:mrow></mml:mfenced><mml:mtext>.</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equd.gif" position="anchor"/></alternatives></disp-formula>
</p><p>How to generate input for SCALE for both endogenous RNAs and exogenous spike-ins is included in &#x0201c;<xref rid="Sec14" ref-type="sec">Methods</xref>&#x0201d; and Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Supplementary methods. For parameter estimation, we developed a new &#x0201c;histogram-repiling&#x0201d; method to obtain the distribution of <inline-formula id="IEq47"><alternatives><tex-math id="M101">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_{cg} $$\end{document}</tex-math><mml:math id="M102"><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq47.gif"/></alternatives></inline-formula> from the observed distribution of <inline-formula id="IEq48"><alternatives><tex-math id="M103">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg} $$\end{document}</tex-math><mml:math id="M104"><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq48.gif"/></alternatives></inline-formula>. The bursting parameters are then derived from the distribution of <inline-formula id="IEq49"><alternatives><tex-math id="M105">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_{cg} $$\end{document}</tex-math><mml:math id="M106"><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq49.gif"/></alternatives></inline-formula> by moment estimators. Standard errors and confidence intervals of the parameters are obtained using nonparametric bootstrapping. The details are shown in &#x0201c;<xref rid="Sec14" ref-type="sec">Methods</xref>&#x0201d;.</p></sec><sec id="Sec8"><title>Hypothesis testing</title><p>For biallelic bursty genes, we use nonparametric bootstrapping to test the null hypothesis that the burst frequency and burst size of the two alleles are the same (<inline-formula id="IEq50"><alternatives><tex-math id="M107">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on}^A={k}_{on}^B $$\end{document}</tex-math><mml:math id="M108"><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq50.gif"/></alternatives></inline-formula>, <inline-formula id="IEq51"><alternatives><tex-math id="M109">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {s}^A/{k}_{off}^A={s}^B/{k}_{off}^B $$\end{document}</tex-math><mml:math id="M110"><mml:msup><mml:mi>s</mml:mi><mml:mi>A</mml:mi></mml:msup><mml:mo stretchy="true">/</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:msup><mml:mi>s</mml:mi><mml:mi>B</mml:mi></mml:msup><mml:mo stretchy="true">/</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq51.gif"/></alternatives></inline-formula>) against the alternative hypothesis that either or both parameters differ between alleles. For each gene, we also a perform chi-square test to determine if the transcription of each of the two alleles is independent by comparing the observed proportions of cells from the gene categorization framework against the expected proportions under independence. For genes where the proportion of cells expressing both alleles is significantly higher than expected, we define their bursting as coordinated; for genes where the proportion of cells expressing only one allele is significantly higher than expected, we define their bursting as repulsed (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). We use false discovery rate (FDR) to adjust for multiple comparisons. Details of the testing procedures are outlined in &#x0201c;<xref rid="Sec14" ref-type="sec">Methods</xref>&#x0201d;.</p></sec></sec><sec id="Sec9"><title>Analysis of scRNA-seq dataset of mouse cells during preimplantation development</title><p>We re-analyzed the scRNA-seq dataset of mouse blastocyst cells dissociated from in vivo F1 embryos (CAST/female x C57/male) from Deng et al. [<xref ref-type="bibr" rid="CR2">2</xref>]. Transcriptomic profiles of each individual cell were generated using the Smart-seq [<xref ref-type="bibr" rid="CR37">37</xref>] protocol. For 22,958 genes, reads per kilobase per million reads (RPKM) and total number of read counts across all cells are available. Parental allele-specific read counts are also available at heterozygous loci (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S3). Principal component analysis was performed on cells from oocyte to blastocyst stages of mouse preimplantation development and showed that the first three principal components separate well the early-stage cells from the blastocyst cells (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S4). The clusters of early-, mid-, and late-blastocyst cells are combined to gain a sufficient sample size. In the &#x0201c;Discussion&#x0201d;, we give further insights into the potential effects of cell subtype confounding. A quality control procedure was used to remove outliers in library size, mean, and standard deviation of allelic read counts/proportions. We applied SCALE to this dataset of 122 mouse blastocyst cells, with a focus on addressing the issue of technical variability and modeling of transcriptional bursting.</p><p>Eight exogenous RNAs with known serial dilutions were added to late blastocyst cells (Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S1) and used to estimate the technical noise-associated parameters (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S5a). We applied the Bayes gene classification framework to these cells to get the genome-wide distribution of gene categories. Specifically, out of the 22,958 genes profiled across all cells, ~43% are biallelically expressed (~33% of the total are biallelic bursty, and ~10% of the total are biallelic non-bursty), ~7% are monoallelically expressed, and ~50% are silent. Our empirical Bayes categorization results show that, on the genome-wide scale, the two alleles of most biallelic bursty genes share the same bursting kinetics and burst independently (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S6a), as has been reported by Deng et al. [<xref ref-type="bibr" rid="CR2">2</xref>].</p><p>For the 7486 genes that are categorized as biallelic bursty, we applied SCALE to identify genes whose alleles have different bursting kinetic parameters by the bootstrap-based hypothesis tests as previously described. After FDR control, we identified 425 genes whose two alleles have significantly different burst frequencies (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>) and two genes whose two alleles have significantly different burst sizes (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>). Figure&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> shows the allelic read counts of a gene that has different burst frequencies (<italic>Btf3l4</italic>) and a gene that has different burst sizes (<italic>Fdps</italic>). The two genes with significantly different allelic burst sizes (<italic>Fdps</italic> and <italic>Atp6ap2</italic>) are also significant in having different burst frequencies between the two alleles. <inline-formula id="IEq52"><alternatives><tex-math id="M111">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ P $$\end{document}</tex-math><mml:math id="M112"><mml:mi>P</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq52.gif"/></alternatives></inline-formula> values from differential burst frequency testing have a spike below the significance level after FDR control (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>), while those from differential burst size testing are roughly uniformly distributed (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>).<fig id="Fig3"><label>Fig. 3</label><caption><p>Allele-specific transcriptional kinetics of 7486 genes from 122 mouse blastocyst cells. <bold>a</bold> Burst frequency of the two alleles has a correlation of 0.852; 425 genes show significant allelic differences in burst frequency after FDR control. <bold>b</bold> Burst size of the two alleles has a correlation of 0.746; two genes show significant allelic difference in burst size. X-chromosome genes as positive controls show significantly higher burst frequencies of the maternal alleles than those of the paternal alleles. The <italic>p</italic> values for allelic burst size difference (<italic>bottom right</italic>) are uniformly distributed as expected under the null, whereas those for allelic burst frequency difference (<italic>bottom left</italic>) have a spike below significance level after FDR control</p></caption><graphic xlink:href="13059_2017_1200_Fig3_HTML" id="MO3"/></fig>
<fig id="Fig4"><label>Fig. 4</label><caption><p>Examples of significant genes from hypothesis testing. <bold>a</bold> The two alleles of the gene have significantly different burst frequencies from the bootstrap-based testing. <bold>b</bold> The two alleles of the gene have significantly different burst sizes and burst frequencies. <bold>c</bold> The two alleles of the gene fire non-independently from the chi-square test of independence</p></caption><graphic xlink:href="13059_2017_1200_Fig4_HTML" id="MO4"/></fig>
</p><p>At the whole genome level, these results show that allelic differences in the expression of bursty genes during embryo development are achieved through differential modulation of burst frequency rather than burst size. This seems to agree with intuition, since allelic differences must be caused by factors that act in <italic>cis</italic> to regulate gene expression, and <italic>cis</italic> factors are likely to change burst frequency by affecting promoter accessibility [<xref ref-type="bibr" rid="CR36">36</xref>, <xref ref-type="bibr" rid="CR38">38</xref>&#x02013;<xref ref-type="bibr" rid="CR40">40</xref>]. On the contrary, while it is plausible for <italic>cis</italic> factors to affect allelic burst size through, for example, the efficiency of RNA polymerase II recruitment or the speed of elongation, the few known cases of burst size modulation are controlled in <italic>trans</italic> [<xref ref-type="bibr" rid="CR36">36</xref>]. Furthermore, previous studies have shown that the kinetic parameter that varies the most&#x02014;along the cell cycle [<xref ref-type="bibr" rid="CR36">36</xref>], between different genes [<xref ref-type="bibr" rid="CR41">41</xref>], between different growth conditions [<xref ref-type="bibr" rid="CR42">42</xref>], or under regulation by a transcription factor [<xref ref-type="bibr" rid="CR43">43</xref>]&#x02014;is the probabilistic rate of switching to the active state <inline-formula id="IEq53"><alternatives><tex-math id="M113">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M114"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq53.gif"/></alternatives></inline-formula>, while the rates of gene inactivation <inline-formula id="IEq54"><alternatives><tex-math id="M115">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M116"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq54.gif"/></alternatives></inline-formula> and of transcription <inline-formula id="IEq55"><alternatives><tex-math id="M117">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ s $$\end{document}</tex-math><mml:math id="M118"><mml:mi>s</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq55.gif"/></alternatives></inline-formula> vary much less.</p><p>Our analysis includes 107 male cells (X<sup>A</sup>Y) and 15 female cells (X<sup>A</sup>X<sup>B</sup>) and this allows us to use those bursty X-chromosome genes as positive controls. As a result of this gender mixture, more cells express the maternal X<sup>A</sup> allele compared to the paternal X<sup>B</sup> allele. As shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>, SCALE successfully detects these bursty X-chromosome genes with significant difference in allelic burst frequencies but not in allelic burst sizes. If we keep only the 107 male cells, these X-chromosome genes are correctly categorized as monoallelically expressed&#x02014;the bursting kinetics for the paternal X<sup>B</sup> allele are not estimable&#x02014;and in this case there is no longer a cluster of significant X-chromosome genes separated from the autosomal genes (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S8).</p><p>For biallelic bursty genes, we also used a simple binomial test to determine if the mean allelic coverage across cells is biased towards either allele. This is comparable to existing tests of allelic imbalance in bulk tissue, although the total coverage across cells in this dataset is much higher than standard bulk tissue RNA-seq data. After multiple hypothesis testing correction, we identified 417 genes with significant allelic imbalance, out of which 238 overlap with the significant genes from the testing of differential bursting kinetics (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5a</xref>). Inspection of the estimated bursting kinetic parameters in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5a</xref> shows that, when the burst size and burst frequency of the two alleles change in the same direction (e.g., gene <italic>Gprc5a</italic> in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5b</xref>), testing of allelic imbalance can detect more significant genes with higher power. This is not unexpected&#x02014;a small insignificant increase in burst size adds on top of an insignificant increase in burst frequency, resulting in a significant increase in overall expression levels between the two alleles. However, for genes shown in red in the top left and bottom right quadrants of Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5a</xref>, the test for differential bursting kinetics detects more genes than the allelic imbalance test. This is due to the fact that when burst size and burst frequency change in opposite directions (e.g., gene <italic>Dhrs7</italic> in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5b</xref>), their effects cancel out when looking at the mean expression. Furthermore, even when the burst size does not change, if the change in burst frequency is small, by using a more specific model SCALE has higher power to detect it compared to an analysis based on mean allelic imbalance. Overall, the allelic imbalance test and differential bursting test report overlapping but substantially different sets of genes, with each test having its benefits. Compared to the allelic imbalance test, SCALE gives more detailed characterization of the nature of the difference by attributing the change in mean expression to a change in the burst frequency and/or burst size.<fig id="Fig5"><label>Fig. 5</label><caption><p>Testing of bursting kinetics by scRNA-seq and testing mean difference by bulk-tissue sequencing. <bold>a</bold> Genes that are significant from testing of shared burst frequency and allelic imbalance. *Also includes the two genes that are significant from testing of shared burst size. Change in burst frequency and burst size in the same direction leads to higher detection power of allelic imbalance; change in different directions leads to allelic imbalance testing being underpowered. <bold>b</bold> Gene <italic>Dhrs7</italic>, whose two alleles have bursting kinetics in different directions, and gene <italic>Gprc5a</italic>, whose two alleles have bursting kinetics in the same direction. <italic>Dhrs7</italic> is significant from testing of differential allelic bursting kinetics; <italic>Gprc5a</italic> is significant from the testing of mean difference between the two alleles</p></caption><graphic xlink:href="13059_2017_1200_Fig5_HTML" id="MO5"/></fig>
</p><p>It is also noticeable that in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5a</xref> the vertical axis, <inline-formula id="IEq56"><alternatives><tex-math id="M119">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta \mathrm{freq} $$\end{document}</tex-math><mml:math id="M120"><mml:mo>&#x02206;</mml:mo><mml:mi mathvariant="italic">freq</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq56.gif"/></alternatives></inline-formula>, has a 50% wider range than the horizontal axis, <inline-formula id="IEq57"><alternatives><tex-math id="M121">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta \mathrm{size} $$\end{document}</tex-math><mml:math id="M122"><mml:mo>&#x02206;</mml:mo><mml:mi mathvariant="italic">size</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq57.gif"/></alternatives></inline-formula>. Therefore, while it is visually not obvious from this scatter plot, much more genes have large absolute <inline-formula id="IEq58"><alternatives><tex-math id="M123">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta \mathrm{freq} $$\end{document}</tex-math><mml:math id="M124"><mml:mo>&#x02206;</mml:mo><mml:mi mathvariant="italic">freq</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq58.gif"/></alternatives></inline-formula> than large absolute <inline-formula id="IEq59"><alternatives><tex-math id="M125">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta \mathrm{size} $$\end{document}</tex-math><mml:math id="M126"><mml:mo>&#x02206;</mml:mo><mml:mi mathvariant="italic">size</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq59.gif"/></alternatives></inline-formula>. Although the standard errors of these estimated differences are not reflected in the plot, given our testing results, those genes with large estimated differences in <inline-formula id="IEq60"><alternatives><tex-math id="M127">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta \mathrm{size} $$\end{document}</tex-math><mml:math id="M128"><mml:mo>&#x02206;</mml:mo><mml:mi mathvariant="italic">size</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq60.gif"/></alternatives></inline-formula> also have large standard errors in their estimates, which is further confirmed via simulations.</p><p>Further chi-squared test of the null hypothesis of independence (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4c</xref>) shows that 424 genes have two alleles that fire in a significantly non-independent fashion. We find that all significant genes have higher proportions of cells expressing both alleles than expected, indicating coordinated expression between the two alleles. In this dataset, there are no significant genes with repulsed bursting between the two alleles. Repulsed bursting, in the extreme case where at most one allele is expressed in any cell, is also referred to as stochastic ME [<xref ref-type="bibr" rid="CR31">31</xref>]. Our testing results indicate that, in mouse embryo development, all cases of stochastic ME (i.e., repulsion between the two alleles) can be explained by independent and infrequent stochastic bursting. The burst synchronization in the 424 significant genes is not unexpected and is possibly due to a shared <italic>trans</italic> factor between the two alleles (e.g., co-activation of both alleles by a shared enhancer). This result is concordant with the findings from a mouse embryonic stem cell scRNA-seq study by Kim et al. [<xref ref-type="bibr" rid="CR31">31</xref>], which reported that the two alleles of a gene show correlated allelic expression across cells more often than expected by chance, potentially suggesting regulation by extrinsic factors [<xref ref-type="bibr" rid="CR31">31</xref>]. We further discuss the sharing of such extrinsic factors under the context of cell population admixtures in the &#x0201c;<xref rid="Sec12" ref-type="sec">Discussion</xref>&#x0201d;.</p><p>In summary, our results using SCALE suggest that: (i) the two alleles from 10% of the bursty genes show either significant deviations from independent firing or significant differences in bursting kinetic parameters; (ii) for genes whose alleles differ in their bursting kinetic parameters, the difference is found mostly in the burst frequency instead of the burst size; (iii) for genes whose alleles violate independence, their expression tends to be coordinated. Refer to Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Table S2 for genome-wide output from SCALE.</p></sec><sec id="Sec10"><title>Analysis of scRNA-seq dataset of human fibroblast cells</title><p>To further examine our findings in a dataset without potential confounding of cell type admixtures, we applied SCALE to a scRNA-seq dataset of 104 cells from female human newborn primary fibroblast culture from Borel et al. [<xref ref-type="bibr" rid="CR17">17</xref>]. The cells were captured by Fluidigm C1 with 22 PCR cycles and were sequenced with, on average, 36 million reads (100&#x000a0;bp, paired end) per cell. Bulk-tissue whole-genome sequencing was performed on two different lanes with 26-fold coverage on average and was used to identify heterozygous loci in coding regions. After quality control procedures, 9016 heterozygous loci from 9016 genes were identified (if multiple loci coexist in the same gene, we picked the one with the highest mean depth of coverage). At each locus, we used SAMtools [<xref ref-type="bibr" rid="CR44">44</xref>] mpileup to obtain allelic read counts in each single cell from scRNA-seq, which are further used as input for SCALE. Ninety-two ERCC synthesized RNAs were added in the lysis buffer of 12 fibroblast cells with a final dilution of 1:40,000. The true concentrations and the observed number of reads for all spike-ins were used as baselines to estimate technical variability (Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Table S3; Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S5b). Refer to Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Supplementary methods for details on the bioinformatic pipeline.</p><p>We applied the gene categorization framework using SCALE and found that out of the 9016 genes, the proportions of monoallelically expressed, biallelically expressed, and silent genes are 11.5, 45.7, and 42.8%, respectively. For the 2277 genes that are categorized as biallelic bursty, we estimated their allele-specific bursting kinetic parameters and found that the correlations between the estimated burst frequency and burst size between the two alleles are 0.859 and 0.692 (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>). We then carried out hypothesis testing on differential allelic bursting kinetics. After FDR correction, we identified 26 genes with significantly different burst frequencies between the two alleles (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6a</xref>) and one gene <italic>Nfx1</italic> with significantly different burst sizes between the two alleles, which is also significant in burst frequency testing (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6b</xref>). We further carried out testing of non-independent bursting between the two alleles and identified 35 significant genes after FDR correction (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S6b). Out of the 35 significant genes, 27 showed patterns of coordinated bursting while the other eight showed repulsed patterns. Refer to Additional file <xref rid="MOESM5" ref-type="media">5</xref>: Table S4 for detailed output from SCALE across all tested genes.<fig id="Fig6"><label>Fig. 6</label><caption><p>Allele-specific transcriptional kinetics of 2277 genes from 104 human fibroblast cells. <bold>a</bold> Burst frequency of the two alleles has a correlation of 0.859; 26 genes show significant allelic difference in burst frequency after FDR. <bold>b</bold> Burst size of the two alleles has a correlation of 0.692. One gene has significant allelic difference in burst size. The results are concordant with the findings from the mouse embryonic development study</p></caption><graphic xlink:href="13059_2017_1200_Fig6_HTML" id="MO6"/></fig>
</p><p>We also carried out pairwise correlation analysis between the estimated allelic bursting kinetics, the proportion of unit time that the gene stays in the active state <inline-formula id="IEq61"><alternatives><tex-math id="M129">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on}/\left({k}_{on}+{k}_{off}\right) $$\end{document}</tex-math><mml:math id="M130"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub><mml:mo stretchy="true">/</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq61.gif"/></alternatives></inline-formula> for each allele, as well as the overall ASE levels (taken as the sum across all cells at the heterozygous locus). Notably, we found that the overall ASE correlates strongly with the burst frequency and the proportion of time that the gene stays active, but not with the burst size (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S9), in concordance with Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>]. This further supports our previous conclusion that ASE at the single-cell level manifests as differences in burst frequency in a <italic>cis</italic>-manner.</p></sec><sec id="Sec11"><title>Assessment of estimation accuracy and testing power</title><p>First, we investigated the accuracy of the moment estimators for the bursting parameters under four different scenarios in the Poisson-Beta transcription model: (i) small <inline-formula id="IEq62"><alternatives><tex-math id="M131">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M132"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq62.gif"/></alternatives></inline-formula> and small <inline-formula id="IEq63"><alternatives><tex-math id="M133">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M134"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq63.gif"/></alternatives></inline-formula>, which we call bursty and leads to relatively few transitions between the ON and OFF states with a bimodal mRNA distribution across cells (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S10a); (ii) large <inline-formula id="IEq64"><alternatives><tex-math id="M135">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M136"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq64.gif"/></alternatives></inline-formula> and small <inline-formula id="IEq65"><alternatives><tex-math id="M137">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M138"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq65.gif"/></alternatives></inline-formula>, which leads to long durations in the ON state and resembles constitutive expression with the mRNA having a Poisson-like distribution (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S10B); (iii) small <inline-formula id="IEq66"><alternatives><tex-math id="M139">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M140"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq66.gif"/></alternatives></inline-formula> and large <inline-formula id="IEq67"><alternatives><tex-math id="M141">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M142"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq67.gif"/></alternatives></inline-formula>, which leads to most cells being silent (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S10c); (iv) and large <inline-formula id="IEq68"><alternatives><tex-math id="M143">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M144"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq68.gif"/></alternatives></inline-formula> and large <inline-formula id="IEq69"><alternatives><tex-math id="M145">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M146"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq69.gif"/></alternatives></inline-formula>, which leads to constitutive expression (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S10d).</p><p>We generated simulated data for 100 cells from the four cases above and started with no technical noise or cell size confounding. Within each case, we vary <inline-formula id="IEq70"><alternatives><tex-math id="M147">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M148"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq70.gif"/></alternatives></inline-formula>, <inline-formula id="IEq71"><alternatives><tex-math id="M149">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M150"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq71.gif"/></alternatives></inline-formula>, and <italic>s</italic> and use relative absolute error <inline-formula id="IEq72"><alternatives><tex-math id="M151">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left|\widehat{\theta}-\theta \right|/\theta $$\end{document}</tex-math><mml:math id="M152"><mml:mfenced close="|" open="|"><mml:mrow><mml:mover accent="true"><mml:mi>&#x003b8;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>-</mml:mo><mml:mi>&#x003b8;</mml:mi></mml:mrow></mml:mfenced><mml:mo stretchy="true">/</mml:mo><mml:mi>&#x003b8;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq72.gif"/></alternatives></inline-formula> as a measurement of accuracy (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S11). Our results show that genes with large <inline-formula id="IEq73"><alternatives><tex-math id="M153">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M154"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq73.gif"/></alternatives></inline-formula> and small <inline-formula id="IEq74"><alternatives><tex-math id="M155">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M156"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq74.gif"/></alternatives></inline-formula> (shown as the black curves in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S11) have the largest estimation errors of the bursting parameters. Statistically it is hard to distinguish these constitutively expressed genes from genes with large <inline-formula id="IEq75"><alternatives><tex-math id="M157">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M158"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq75.gif"/></alternatives></inline-formula> and large <inline-formula id="IEq76"><alternatives><tex-math id="M159">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M160"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq76.gif"/></alternatives></inline-formula> and thus the kinetic parameters in this case cannot be accurately estimated, which has been previously reported [<xref ref-type="bibr" rid="CR25">25</xref>, <xref ref-type="bibr" rid="CR45">45</xref>]. Furthermore, the estimation errors are large for genes with small <inline-formula id="IEq77"><alternatives><tex-math id="M161">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on} $$\end{document}</tex-math><mml:math id="M162"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq77.gif"/></alternatives></inline-formula>, large <inline-formula id="IEq78"><alternatives><tex-math id="M163">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M164"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq78.gif"/></alternatives></inline-formula>, and small <italic>s</italic> (shown as red curves in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S11) due to lack of cells with non-zero expression. The standard errors and confidence intervals of the estimated kinetics from bootstrap resampling further confirm the underperformance for the above two classes (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S5). This emphasizes the need to adopt the Bayes categorization framework as a first step so that kinetic parameters are stably estimated only for genes whose both alleles are bursty. For genes whose alleles are perpetually silent or constitutively expressed across cells, there is no good method, nor any need, to estimate their bursting parameters.</p><p>Importantly, we see that the estimation bias in transcription rate <italic>s</italic> and deactivation rate <inline-formula id="IEq79"><alternatives><tex-math id="M165">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M166"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq79.gif"/></alternatives></inline-formula> cancel&#x02014;over/underestimation of <italic>s</italic> is compensated by over/underestimation of <inline-formula id="IEq80"><alternatives><tex-math id="M167">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M168"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq80.gif"/></alternatives></inline-formula>&#x02014;and as a consequence the burst size <inline-formula id="IEq81"><alternatives><tex-math id="M169">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ s/{k}_{off} $$\end{document}</tex-math><mml:math id="M170"><mml:mi>s</mml:mi><mml:mo stretchy="true">/</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq81.gif"/></alternatives></inline-formula> can be more stably estimated than either parameter alone, especially when <inline-formula id="IEq82"><alternatives><tex-math id="M171">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on}\ll {k}_{off} $$\end{document}</tex-math><mml:math id="M172"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi></mml:msub><mml:mo>&#x0226a;</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq82.gif"/></alternatives></inline-formula> (shown as red curves in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S11). This is further confirmed by empirical results that allelic burst size has much higher correlation (0.746 from the mouse blastocyst dataset and 0.692 from the human fibroblast dataset) than allelic transcription and deactivation rate (0.464 and 0.265 for mouse blastocyst, and 0.458 and 0.33 for human fibroblast) (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S12). For this reason, all of our results on real data are based on <inline-formula id="IEq83"><alternatives><tex-math id="M173">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ s/{k}_{off} $$\end{document}</tex-math><mml:math id="M174"><mml:mi>s</mml:mi><mml:mo stretchy="true">/</mml:mo><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq83.gif"/></alternatives></inline-formula> and we do not consider <italic>s</italic> and <inline-formula id="IEq84"><alternatives><tex-math id="M175">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M176"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq84.gif"/></alternatives></inline-formula> separately.</p><p>We further carried out power analysis of the testing of differential burst frequency and burst size between the two alleles. The null hypothesis is both alleles sharing the same bursting kinetics (<inline-formula id="IEq85"><alternatives><tex-math id="M177">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on}^A={k}_{on}^B=0.2,{k}_{off}^A={k}_{off}^B=0.2,{s}^A={s}^B=50 $$\end{document}</tex-math><mml:math id="M178"><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:mn>0.2</mml:mn><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:mn>0.2</mml:mn><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:msup><mml:mi>s</mml:mi><mml:mi>A</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:msup><mml:mi>s</mml:mi><mml:mi>B</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mn>50</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq85.gif"/></alternatives></inline-formula>), while the alternative hypotheses with differential burst frequency or burst size are shown in the legends in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S13. The detailed setup of the simulation procedures is as follows. (i) Simulate the true allele-specific read counts <inline-formula id="IEq86"><alternatives><tex-math id="M179">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}^A $$\end{document}</tex-math><mml:math id="M180"><mml:msup><mml:mi>Y</mml:mi><mml:mi>A</mml:mi></mml:msup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq86.gif"/></alternatives></inline-formula> and <inline-formula id="IEq87"><alternatives><tex-math id="M181">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}^B $$\end{document}</tex-math><mml:math id="M182"><mml:msup><mml:mi>Y</mml:mi><mml:mi>B</mml:mi></mml:msup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq87.gif"/></alternatives></inline-formula> across 100 cells from the Poisson-Beta model under the alternative hypothesis. Technical noise is then added based on the noise model described earlier with technical noise parameters <inline-formula id="IEq88"><alternatives><tex-math id="M183">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\alpha, \beta, \kappa, \tau \right\} $$\end{document}</tex-math><mml:math id="M184"><mml:mfenced close="}" open="{" separators=",,,"><mml:mi>&#x003b1;</mml:mi><mml:mi>&#x003b2;</mml:mi><mml:mi>&#x003ba;</mml:mi><mml:mi>&#x003c4;</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq88.gif"/></alternatives></inline-formula> estimated from the mouse blastocyst cell dataset. (ii) Apply SCALE to the observed expression level <inline-formula id="IEq89"><alternatives><tex-math id="M185">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}^A $$\end{document}</tex-math><mml:math id="M186"><mml:msup><mml:mi>Q</mml:mi><mml:mi>A</mml:mi></mml:msup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq89.gif"/></alternatives></inline-formula> and <inline-formula id="IEq90"><alternatives><tex-math id="M187">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}^B $$\end{document}</tex-math><mml:math id="M188"><mml:msup><mml:mi>Q</mml:mi><mml:mi>B</mml:mi></mml:msup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq90.gif"/></alternatives></inline-formula>, which returns a <inline-formula id="IEq91"><alternatives><tex-math id="M189">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ p $$\end{document}</tex-math><mml:math id="M190"><mml:mi>p</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq91.gif"/></alternatives></inline-formula> value for testing differential burst size or burst frequency. If the <inline-formula id="IEq92"><alternatives><tex-math id="M191">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ p $$\end{document}</tex-math><mml:math id="M192"><mml:mi>p</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq92.gif"/></alternatives></inline-formula> value is less than the significance level, we reject the null hypothesis. (iii) Repeat (i) and (ii) <inline-formula id="IEq93"><alternatives><tex-math id="M193">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ N $$\end{document}</tex-math><mml:math id="M194"><mml:mi>N</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq93.gif"/></alternatives></inline-formula> times with the power estimated as <inline-formula id="IEq94"><alternatives><tex-math id="M195">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \frac{\mathrm{Number}\;\mathrm{of}\; p-\mathrm{values}\le 0.05\;}{N} $$\end{document}</tex-math><mml:math id="M196"><mml:mfrac><mml:mrow><mml:mi mathvariant="normal">Number</mml:mi><mml:mspace width="0.12em"/><mml:mi mathvariant="normal">of</mml:mi><mml:mspace width="0.12em"/><mml:mi>p</mml:mi><mml:mo>-</mml:mo><mml:mi mathvariant="normal">values</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mn>0.05</mml:mn><mml:mspace width="0.12em"/></mml:mrow><mml:mi>N</mml:mi></mml:mfrac></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq94.gif"/></alternatives></inline-formula>. Our results indicate that the testing of burst frequency and burst size have similar power overall with relatively reduced power if the difference in allelic burst size is due to a difference in the deactivation rate <inline-formula id="IEq95"><alternatives><tex-math id="M197">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M198"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq95.gif"/></alternatives></inline-formula>.</p><p>We then simulated allele-specific counts from the full model including technical noise as well as variations in cell size with the ground truth <inline-formula id="IEq96"><alternatives><tex-math id="M199">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on}^A={k}_{on}^B={k}_{off}^A={k}_{off}^B=0.2,{s}^A={s}^B=100 $$\end{document}</tex-math><mml:math id="M200"><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:mn>0.2</mml:mn><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:msup><mml:mi>s</mml:mi><mml:mi>A</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:msup><mml:mi>s</mml:mi><mml:mi>B</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mn>100</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq96.gif"/></alternatives></inline-formula> (bursty with small activation and deactivation rate). For parameters quantifying the degree of technical noise, we used the estimates from the mouse blastocyst cells (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S5a) as well as the human fibroblast cells (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S5b). Cell sizes were simulated from a normal distribution with mean 0 and standard deviation 0.1 and 0.01. We ran SCALE under four different settings: (i) in its default setting, (ii) without accounting for cell size, (iii) without adjusting for technical variability, (iv) not in an allele-specific fashion but using total coverage as input. Each was repeated 5000 times with a sample size of 100 and 400 cells, respectively. Relative estimation errors of burst size and burst frequency were summarized across all simulation runs. Our results show that SCALE in its default setting has the smallest estimation errors for both burst size and burst frequency (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S14 and S15). Not surprisingly, cell size has a larger effect on burst size estimation than burst frequency estimation, while technical variability leads to biased estimation of both burst frequency and burst size. The estimates taking total expression instead of ASE as input are completely off. Furthermore, the estimation accuracy improved as the number of cells increased. These results indicate the necessity to profile transcriptional kinetics in an allele-specific fashion with adjustment for technical variability and cell size.</p></sec></sec><sec id="Sec12"><title>Discussion</title><p>We propose SCALE, a statistical framework to study ASE using scRNA-seq data. The input data to SCALE are allele-specific read counts at heterozygous loci across all cells. In the two datasets that we analyzed, we used F1 mouse crossing and bulk-tissue sequencing to profile the true heterozygous loci. When these are not available, scRNA-seq itself can be used to retrieve ASE and, more specifically, haplotype information, as described in Edsgard et al. [<xref ref-type="bibr" rid="CR46">46</xref>]. SCALE estimates parameters that characterize allele-specific transcriptional bursting after accounting for technical biases in scRNA-seq and size differences between cells. This allows us to detect genes that exhibit allelic differences in burst frequency and burst size and genes whose alleles show coordinated or repulsed bursting patterns. Differences in mean expression between two alleles have long been observed in bulk RNA-seq. By scRNA-seq, we now move beyond the mean and characterize the difference in expression distributions between the two alleles, specifically in terms of their transcriptional bursting parameters.</p><p>Transcriptional bursting is a fundamental property of gene expression, yet its global patterns in the genome have not been well characterized, and most studies consider bursting at the gene level by ignoring the allelic origin of transcription. In this paper, we reanalyzed the Deng et al. [<xref ref-type="bibr" rid="CR2">2</xref>] and Borel et al. [<xref ref-type="bibr" rid="CR17">17</xref>] data. We confirmed the findings from Levesque and Raj [<xref ref-type="bibr" rid="CR32">32</xref>] and Deng et al. [<xref ref-type="bibr" rid="CR2">2</xref>] that, for most genes across the genome, there is no sufficient evidence against the assumption of independent bursting with shared bursting kinetics between the two alleles. For genes where significant deviations are observed, SCALE allows us to attribute the deviation to differential bursting kinetics and/or non-independent bursting between the two alleles.</p><p>More specifically, for genes that are transcribed in a bursty fashion, we compared the burst frequency and burst size between their two alleles. For both scRNA-seq datasets, we identified a significant number of genes whose allele-specific bursting differs according to burst frequency but not burst size. Our findings provide evidence that burst frequency, which represents the rate of gene activation, is modified in <italic>cis</italic> and that burst size, which represents the ratio of transcription rate to gene inactivation rate, is less likely to be modulated in <italic>cis</italic>. Although our testing framework may have slightly reduced power in detecting the differential deactivation rate (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S13), regulation of burst size can result from either a global <italic>trans</italic> factor or extrinsic factors that act upon both alleles. Similar findings have been previously reported, from different perspectives and on different scales, using various technologies, platforms, and model organisms [<xref ref-type="bibr" rid="CR31">31</xref>, <xref ref-type="bibr" rid="CR36">36</xref>, <xref ref-type="bibr" rid="CR41">41</xref>&#x02013;<xref ref-type="bibr" rid="CR43">43</xref>].</p><p>It is worth noting that the bursting parameters estimated by SCALE are normalized by the decay rate, where the inverse <inline-formula id="IEq97"><alternatives><tex-math id="M201">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 1/ d $$\end{document}</tex-math><mml:math id="M202"><mml:mn>1</mml:mn><mml:mo stretchy="true">/</mml:mo><mml:mi>d</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq97.gif"/></alternatives></inline-formula> denotes the average lifetime of an mRNA molecule. Here we implicitly make the assumptions that, for each allele, the gene-specific decay rates (<inline-formula id="IEq98"><alternatives><tex-math id="M203">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_g^A $$\end{document}</tex-math><mml:math id="M204"><mml:msubsup><mml:mi>d</mml:mi><mml:mi>g</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq98.gif"/></alternatives></inline-formula> and <inline-formula id="IEq99"><alternatives><tex-math id="M205">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_g^B $$\end{document}</tex-math><mml:math id="M206"><mml:msubsup><mml:mi>d</mml:mi><mml:mi>g</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq99.gif"/></alternatives></inline-formula>) are constant, and thus the estimated allelic burst frequencies are the ratio of true burst frequency over decay rate (that is <inline-formula id="IEq100"><alternatives><tex-math id="M207">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on, g}^A/{d}_g^A $$\end{document}</tex-math><mml:math id="M208"><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup><mml:mo stretchy="true">/</mml:mo><mml:msubsup><mml:mi>d</mml:mi><mml:mi>g</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq100.gif"/></alternatives></inline-formula> and <inline-formula id="IEq101"><alternatives><tex-math id="M209">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{on, g}^B/{d}_g^B $$\end{document}</tex-math><mml:math id="M210"><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">/</mml:mo><mml:msubsup><mml:mi>d</mml:mi><mml:mi>g</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq101.gif"/></alternatives></inline-formula>). The decay rates, however, cancel out in the numerator and denominator in the allelic burst sizes, <inline-formula id="IEq102"><alternatives><tex-math id="M211">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {s}_g^A/{k}_{off, g}^A $$\end{document}</tex-math><mml:math id="M212"><mml:msubsup><mml:mi>s</mml:mi><mml:mi>g</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo stretchy="true">/</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq102.gif"/></alternatives></inline-formula> and <inline-formula id="IEq103"><alternatives><tex-math id="M213">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {s}_g^B/{k}_{off, g}^B $$\end{document}</tex-math><mml:math id="M214"><mml:msubsup><mml:mi>s</mml:mi><mml:mi>g</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">/</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq103.gif"/></alternatives></inline-formula>. Therefore, the differences that we observe in the allelic burst frequencies can also potentially be due to different decay rates between the two alleles, which has been previously reported to be regulated by microRNAs [<xref ref-type="bibr" rid="CR47">47</xref>].</p><p>It is also important to note that 44% of the genes found to be significant for differential burst frequency are not significant in the allelic imbalance test based on mean expression across cells. This suggests that expression quantitative trait loci (eQTL) affecting gene expression through modulation of bursting kinetics are likely to escape detection in existing eQTL studies by bulk sequencing, especially when burst size and burst frequency change in different directions. This is further underscored by the study of Wills et al. [<xref ref-type="bibr" rid="CR48">48</xref>], which measured the expression of 92 genes affected by Wnt signaling in 1440 single cells from 15 individuals and then correlated SNPs with various gene-expression phenotypes. They found bursting kinetics as characterized by burst size and burst frequency to be heritable, thus suggesting the existence of bursting QTLs. Taken together, these results should further motivate more large scale genome-wide studies to systematically characterize the impact of eQTLs on various aspects of transcriptional bursting.</p><p>Kim et al. [<xref ref-type="bibr" rid="CR31">31</xref>] described a statistical framework to quantify the extent of stochastic ASE in scRNA-seq data by using spike-ins, where stochastic ASE is defined as excessive variability in the ratio of the expression level of the paternal (or maternal) allele between cells after controlling for mean allelic expression levels. While they attributed 18% of the stochastic ASE to biological variability, they did not examine what biological factors lead to this stochastic ASE. In this study, we attribute the observed stochastic ASE to differences in allelic bursting kinetics. By studying bursting kinetics in an allele-specific manner, we can compare the transcriptional differences between the two alleles at a finer scale.</p><p>Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>] described a procedure to estimate bursting kinetic parameters using scRNA-seq data. Our method differs from that of Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>] in several ways. First, our model is an allele-specific model that infers kinetic parameters for each allele separately, thus allowing comparisons between alleles. Second, we infer kinetic parameters based on the distribution of &#x0201c;true expression&#x0201d; rather than the distribution of observed expression. We are able to do this through the use of a simple and novel deconvolution approach, which allows us to eliminate the impact of technical noise when making inference on the kinetic parameters. Appropriate modeling of technical noise, particularly gene dropouts, is critical in this context, as failing to do so could lead to the overestimation of <inline-formula id="IEq104"><alternatives><tex-math id="M215">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {k}_{off} $$\end{document}</tex-math><mml:math id="M216"><mml:msub><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq104.gif"/></alternatives></inline-formula>. Third, we employ a gene categorization procedure prior to fitting the bursting model. This is important because the bursting parameters can only be reliably estimated for genes that have sufficient expression and that are bursty.</p><p>As a by-product, SCALE also allows us to rigorously test, for scRNA-seq data, whether the paternal and maternal alleles of a gene are independently expressed. In both scRNA-seq datasets we analyzed, we identified more genes whose allele-specific bursting is in a coordinated fashion than those for which it is in a repulsed fashion. The tendency towards coordination is not surprising, since the two alleles of a gene share the same nuclear environment and thus the same ensemble of transcription factors. We are aware that this degree of coordination can also arise from the mixture of non-homogeneous cell populations, e.g., different lineages of cells during mouse embryonic development, as we combine the early-, mid-, and late-blastocyst cells to gain a large enough sample size. While it is possible that this might lead to false positives in identifying coordinated bursting events, it will result in a decrease in power for the testing of differential bursting kinetics. Given the amount of stochasticity that is observed in the ASE data, how to define cell sub-types and how to quantify between-cell heterogeneity need further investigation.</p></sec><sec id="Sec13"><title>Conclusions</title><p>We have developed SCALE, a statistical framework for systematic characterization of ASE using data generated from scRNA-seq experiments. Our approach allows us to profile allele-specific bursting kinetics while accounting for technical variability and cell size difference. For genes that are classified as biallelic bursty through a Bayes categorization framework, we further examine whether transcription of the paternal and maternal alleles are independent and whether there are any kinetic differences, as represented by burst frequency and burst size, between the two alleles. Our results from the re-analysis of Deng et al. [<xref ref-type="bibr" rid="CR2">2</xref>] and Borel et al. [<xref ref-type="bibr" rid="CR17">17</xref>] provide insights into the extent of differences, coordination, and repulsion between alleles in transcriptional bursting.</p></sec><sec id="Sec14"><title>Methods</title><sec id="Sec15"><title>Input for endogenous RNAs and exogenous spike-ins</title><p>For endogenous RNAs, SCALE takes as input the observed allele-specific read counts at heterozygous loci <inline-formula id="IEq105"><alternatives><tex-math id="M217">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg}^A $$\end{document}</tex-math><mml:math id="M218"><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq105.gif"/></alternatives></inline-formula> and <inline-formula id="IEq106"><alternatives><tex-math id="M219">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg}^B $$\end{document}</tex-math><mml:math id="M220"><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq106.gif"/></alternatives></inline-formula>, with adjustment by library size factor:<disp-formula id="Eque"><alternatives><tex-math id="M221">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\eta}_c=\underset{g}{\mathrm{median}}\frac{Q_{c g}^A+{Q}_{c g}^B}{{\left[{\displaystyle {\prod}_{c^{*}=1}^C}\left({Q}_{c^{*} g}^A+{Q}_{c^{*} g}^B\right)\right]}^{1/ C}}. $$\end{document}</tex-math><mml:math id="M222"><mml:msub><mml:mi>&#x003b7;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:munder><mml:mi mathvariant="normal">median</mml:mi><mml:mi>g</mml:mi></mml:munder><mml:mfrac><mml:mrow><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mrow><mml:msup><mml:mfenced close="]" open="["><mml:mrow><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo stretchy="true">&#x0220f;</mml:mo><mml:mrow><mml:msup><mml:mi>c</mml:mi><mml:mo>*</mml:mo></mml:msup><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi mathvariant="italic">C</mml:mi></mml:msubsup></mml:mstyle><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:msup><mml:mi>c</mml:mi><mml:mo>*</mml:mo></mml:msup><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:msup><mml:mi>c</mml:mi><mml:mo>*</mml:mo></mml:msup><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mrow><mml:mn>1</mml:mn><mml:mo stretchy="true">/</mml:mo><mml:mi>C</mml:mi></mml:mrow></mml:msup></mml:mfrac><mml:mtext>.</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Eque.gif" position="anchor"/></alternatives></disp-formula>
</p><p>In addition, for spike-ins, SCALE takes as input the true concentrations of the spike-in molecules, the lengths of the molecules, as well as the depths of coverage for each spike-in sequence across all cells (Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S1; Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Table S3). The true concentration of each spike-in molecule is calculated according to the known concentration (denoted as <italic>C</italic> attomoles/&#x003bc;L) and the dilution factor (&#x000d7;40,000):<disp-formula id="Equf"><alternatives><tex-math id="M223">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \frac{C\times {10}^{-18}\ \mathrm{moles}/\upmu \mathrm{L}\kern0.37em \times 6.02214\times {10}^{23}{\mathrm{mole}}^{-1}\ \left(\mathrm{Avogadro}\ \mathrm{constant}\right)}{40000\ \left(\mathrm{dilution}\ \mathrm{factor}\right)}. $$\end{document}</tex-math><mml:math id="M224"><mml:mfrac><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>18</mml:mn></mml:mrow></mml:msup><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">moles</mml:mi><mml:mo stretchy="true">/</mml:mo><mml:mi mathvariant="normal">&#x003bc;L</mml:mi><mml:mspace width="0.37em"/><mml:mo>&#x000d7;</mml:mo><mml:mn>6.02214</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>23</mml:mn></mml:msup><mml:msup><mml:mi mathvariant="normal">mole</mml:mi><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:mspace width="0.25em"/><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="normal">Avogadro</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">constant</mml:mi></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mn>40000</mml:mn><mml:mspace width="0.25em"/><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="normal">dilution</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">factor</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac><mml:mrow><mml:mtext>.</mml:mtext></mml:mrow></mml:math><graphic xlink:href="13059_2017_1200_Article_Equf.gif" position="anchor"/></alternatives></disp-formula>
</p><p>The observed number of reads for each spike-in is calculated by adjusting for the library size factor, the read length, and the length of the spike-in RNA. The bioinformatic pipeline to generate the input for SCALE is included in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Supplementary methods.</p></sec><sec id="Sec16"><title>Empirical Bayes method for gene categorization</title><p>We propose an empirical Bayes method that categorizes gene expression across cells into silent, monoallelic, or biallelic states based on their ASE data. Without loss of generality, we focus on one gene here with the goal of determining the most likely gene category based on its ASE pattern. Let <inline-formula id="IEq107"><alternatives><tex-math id="M225">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {n}_c^A $$\end{document}</tex-math><mml:math id="M226"><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq107.gif"/></alternatives></inline-formula> and <inline-formula id="IEq108"><alternatives><tex-math id="M227">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {n}_c^B $$\end{document}</tex-math><mml:math id="M228"><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq108.gif"/></alternatives></inline-formula> be the allele-specific read counts in cell <italic>c</italic> for alleles A and B, respectively. For <italic>each</italic> cell, there are four different categories based on its ASE&#x02014;<inline-formula id="IEq109"><alternatives><tex-math id="M229">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\varnothing, A, B, A B\right\} $$\end{document}</tex-math><mml:math id="M230"><mml:mfenced close="}" open="{"><mml:mrow><mml:mo>&#x02205;</mml:mo><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:mi>A</mml:mi><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:mi>B</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="italic">AB</mml:mi></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq109.gif"/></alternatives></inline-formula> corresponding to scenarios where both alleles are off, only the A allele is expressed, only the B allele is expressed, and both alleles are expressed, respectively. Let <inline-formula id="IEq110"><alternatives><tex-math id="M231">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ k\in \left\{1,2,3,4\right\} $$\end{document}</tex-math><mml:math id="M232"><mml:mi>k</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="}" open="{" separators=",,,"><mml:mn>1</mml:mn><mml:mn>2</mml:mn><mml:mn>3</mml:mn><mml:mn>4</mml:mn></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq110.gif"/></alternatives></inline-formula> represent this cell-specific category. The log-likelihood for the gene across all cells can be written as:<disp-formula id="Equg"><alternatives><tex-math id="M233">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \log \left(\mathrm{\mathcal{L}}\left(\Theta \Big|{n}^A,{n}^B\right)\right)= \log {\prod}_c f\left({n}_c^A,{n}_c^B\Big|\Theta \right)={\sum}_c \log \left[{\sum}_{k=1}^4{\varphi}_k{f}_k\left({n}_c^A,{n}_c^B\Big|\upepsilon, a, b\right)\right], $$\end{document}</tex-math><mml:math id="M234"><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="normal">&#x02112;</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="normal">&#x00398;</mml:mi><mml:mo stretchy="true">|</mml:mo><mml:msup><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:msup><mml:mo>,</mml:mo><mml:msup><mml:mi>n</mml:mi><mml:mi>B</mml:mi></mml:msup></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mo>=</mml:mo><mml:mo>log</mml:mo><mml:msub><mml:mo stretchy="true">&#x0220f;</mml:mo><mml:mi>c</mml:mi></mml:msub><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x00398;</mml:mi></mml:mrow></mml:mfenced><mml:mo>=</mml:mo><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub><mml:mo>log</mml:mo><mml:mfenced close="]" open="["><mml:mrow><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mn>4</mml:mn></mml:msubsup><mml:mrow><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:msub><mml:mi>f</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mrow></mml:mfenced><mml:mtext>,</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equg.gif" position="anchor"/></alternatives></disp-formula>
</p><p>where the parameters are <inline-formula id="IEq111"><alternatives><tex-math id="M235">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Theta =\left\{{\varphi}_1,\dots, {\varphi}_4,\epsilon, a, b\right\} $$\end{document}</tex-math><mml:math id="M236"><mml:mi mathvariant="normal">&#x00398;</mml:mi><mml:mo>=</mml:mo><mml:mfenced close="}" open="{" separators=",,,,,"><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>&#x02026;</mml:mo><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mn>4</mml:mn></mml:msub><mml:mi>&#x003f5;</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq111.gif"/></alternatives></inline-formula> with <inline-formula id="IEq112"><alternatives><tex-math id="M237">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\sum}_{k=1}^4{\varphi}_k=1 $$\end{document}</tex-math><mml:math id="M238"><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mn>4</mml:mn></mml:msubsup><mml:mrow><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq112.gif"/></alternatives></inline-formula> and each <inline-formula id="IEq113"><alternatives><tex-math id="M239">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {f}_k $$\end{document}</tex-math><mml:math id="M240"><mml:msub><mml:mi>f</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq113.gif"/></alternatives></inline-formula> is a density function parameterized by <inline-formula id="IEq114"><alternatives><tex-math id="M241">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \epsilon, a, b $$\end{document}</tex-math><mml:math id="M242"><mml:mi>&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq114.gif"/></alternatives></inline-formula>. <inline-formula id="IEq115"><alternatives><tex-math id="M243">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \epsilon $$\end{document}</tex-math><mml:math id="M244"><mml:mi>&#x003f5;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq115.gif"/></alternatives></inline-formula> is the per-base sequencing error rate, and <italic>a</italic> and <italic>b</italic> are hyper-parameters for a Beta distribution, where <inline-formula id="IEq116"><alternatives><tex-math id="M245">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\theta}_c\sim \mathrm{Beta}\left( a, b\right) $$\end{document}</tex-math><mml:math id="M246"><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>~</mml:mo><mml:mi mathvariant="normal">Beta</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq116.gif"/></alternatives></inline-formula> corresponds to the relative expression of A allele when both alleles are expressed. It is easy to show that:<disp-formula id="Equh"><alternatives><tex-math id="M247">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{l}{f}_1\left({n}_c^A,{n}_c^B\Big|\upepsilon, a, b\right)\propto {\upepsilon}^{n_c^A+{n}_c^B},\\ {}{f}_2\left({n}_c^A,{n}_c^B\Big|\upepsilon, a, b\right)\propto {\left(1-\upepsilon \right)}^{n_c^A}{\upepsilon}^{n_c^B},\ \\ {}{f}_3\left({n}_c^A,{n}_c^B\Big|\upepsilon, a, b\right)\propto {\upepsilon}^{n_c^A}{\left(1-\upepsilon \right)}^{n_c^B},\\ {}{f}_4\left({n}_c^A,{n}_c^B\Big|\upepsilon, a, b\right)\propto {\displaystyle \underset{0}{\overset{1}{\int }}}{\left[{\theta}_c\left(1-\upepsilon \right)+\left(1-{\theta}_c\right)\upepsilon \right]}^{n_c^A}{\left[{\theta}_c\upepsilon +\left(1-{\theta}_c\right)\left(1-\upepsilon \right)\right]}^{n_c^B}\frac{{\theta_c}^{a-1}{\left(1-{\theta}_c\right)}^{b-1}}{B\left( a, b\right)} d{\theta}_c.\end{array} $$\end{document}</tex-math><mml:math id="M248"><mml:mtable columnalign="left"><mml:mtr><mml:mtd><mml:msub><mml:mi>f</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mfenced><mml:mo>&#x0221d;</mml:mo><mml:msup><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mrow></mml:msup><mml:mo>,</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>f</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mfenced><mml:mo>&#x0221d;</mml:mo><mml:msup><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi></mml:mrow></mml:mfenced><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:msup><mml:msup><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:msup><mml:mo>,</mml:mo><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>f</mml:mi><mml:mn>3</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mfenced><mml:mo>&#x0221d;</mml:mo><mml:msup><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:msubsup><mml:mi>n</mml:mi><mml:mi mathvariant="italic">c</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:msup><mml:msup><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi></mml:mrow></mml:mfenced><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:msup><mml:mo>,</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>f</mml:mi><mml:mn>4</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mfenced><mml:mo>&#x0221d;</mml:mo><mml:mstyle displaystyle="true"><mml:munderover><mml:mo stretchy="true">&#x0222b;</mml:mo><mml:mn>0</mml:mn><mml:mn>1</mml:mn></mml:munderover></mml:mstyle><mml:msup><mml:mfenced close="]" open="["><mml:mrow><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi></mml:mrow></mml:mfenced><mml:mo>+</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mi mathvariant="normal">&#x003f5;</mml:mi></mml:mrow></mml:mfenced><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:msup><mml:msup><mml:mfenced close="]" open="["><mml:mrow><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>+</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:msup><mml:mfrac><mml:mrow><mml:msup><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mrow><mml:mi>a</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:msup><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mrow><mml:mi>b</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup></mml:mrow><mml:mrow><mml:mi>B</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mfenced></mml:mrow></mml:mfrac><mml:mi>d</mml:mi><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mtext>.</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equh.gif" position="anchor"/></alternatives></disp-formula>
</p><p>
<inline-formula id="IEq117"><alternatives><tex-math id="M249">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \epsilon $$\end{document}</tex-math><mml:math id="M250"><mml:mi>&#x003f5;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq117.gif"/></alternatives></inline-formula> can be estimated using sex chromosome mismatching or be prefixed at the default value, 0.001. We require <inline-formula id="IEq118"><alternatives><tex-math id="M251">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ a= b\ge 3 $$\end{document}</tex-math><mml:math id="M252"><mml:mi>a</mml:mi><mml:mo>=</mml:mo><mml:mi>b</mml:mi><mml:mo>&#x02265;</mml:mo><mml:mn>3</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq118.gif"/></alternatives></inline-formula> in the prior on <inline-formula id="IEq119"><alternatives><tex-math id="M253">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\theta}_c $$\end{document}</tex-math><mml:math id="M254"><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq119.gif"/></alternatives></inline-formula> so that the AB state is distinguishable from the A and B states. This is a reasonable assumption in that most genes have balanced ASE on average and the use of Beta distribution allows variability of allelic ratio across cells. We adopt an EM algorithm for estimation, with <italic>Z</italic> being the missing variables:<disp-formula id="Equi"><alternatives><tex-math id="M255">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Z}_{ck}=\left\{\begin{array}{c}\hfill 1\hfill \\ {}\hfill 0\hfill \end{array}\right.\begin{array}{c}\hfill \mathrm{if}\ \mathrm{cell}\  c\ \mathrm{belongs}\ \mathrm{to}\ \mathrm{category}\  k\hfill \\ {}\hfill \kern1em \mathrm{otherwise}\hfill \end{array}. $$\end{document}</tex-math><mml:math id="M256"><mml:msub><mml:mi>Z</mml:mi><mml:mrow><mml:mi>c</mml:mi><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable columnalign="center"><mml:mtr columnalign="center"><mml:mtd columnalign="center"><mml:mn>1</mml:mn></mml:mtd></mml:mtr><mml:mtr columnalign="center"><mml:mtd columnalign="center"><mml:mn>0</mml:mn></mml:mtd></mml:mtr></mml:mtable></mml:mfenced><mml:mtable columnalign="center"><mml:mtr columnalign="center"><mml:mtd columnalign="center"><mml:mi mathvariant="normal">if</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">cell</mml:mi><mml:mspace width="0.25em"/><mml:mi>c</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">belongs</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">t</mml:mi><mml:mi mathvariant="normal">o</mml:mi><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">category</mml:mi><mml:mspace width="0.25em"/><mml:mi>k</mml:mi></mml:mtd></mml:mtr><mml:mtr columnalign="center"><mml:mtd columnalign="center"><mml:mspace width="1em"/><mml:mi mathvariant="normal">otherwise</mml:mi></mml:mtd></mml:mtr></mml:mtable><mml:mtext>.</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equi.gif" position="anchor"/></alternatives></disp-formula>
</p><p>The complete-data log-likelihood is given as:<disp-formula id="Equj"><alternatives><tex-math id="M257">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{c} \log \left(\mathrm{\mathcal{L}}\left(\Theta \Big|{n}^A,{n}^B, Z\right)\right)= \log \left[{\displaystyle \prod_c}{\displaystyle \prod_{k=1}^4}{f}_k{\left({n}_c^A,{n}_c^B\Big|\upepsilon, a, b\right)}^{Z_{c k}}{\varphi_k}^{Z_{c k}}\right]\\ {}={\sum}_c{\sum}_{k=1}^4{Z}_{c k} \log \left({\varphi}_k\right)+{\sum}_c{\sum}_{k=1}^4{Z}_{c k} \log \left[{f}_k\left({n}_c^A,{n}_c^B\Big|\upepsilon, a, b\right)\right].\end{array} $$\end{document}</tex-math><mml:math id="M258"><mml:mtable><mml:mtr><mml:mtd><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="normal">&#x02112;</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="normal">&#x00398;</mml:mi><mml:mo stretchy="true">|</mml:mo><mml:msup><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:msup><mml:mo>,</mml:mo><mml:msup><mml:mi>n</mml:mi><mml:mi>B</mml:mi></mml:msup><mml:mo>,</mml:mo><mml:mi>Z</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mo>=</mml:mo><mml:mo>log</mml:mo><mml:mfenced close="]" open="["><mml:mrow><mml:mstyle displaystyle="true"><mml:munder><mml:mo stretchy="true">&#x0220f;</mml:mo><mml:mi>c</mml:mi></mml:munder></mml:mstyle><mml:mstyle displaystyle="true"><mml:munderover><mml:mo stretchy="true">&#x0220f;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mn>4</mml:mn></mml:munderover></mml:mstyle><mml:msub><mml:mi>f</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:msup><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mfenced><mml:msub><mml:mi>Z</mml:mi><mml:mrow><mml:mi>c</mml:mi><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:msup><mml:msup><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:msub><mml:mi>Z</mml:mi><mml:mrow><mml:mi>c</mml:mi><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:msup></mml:mrow></mml:mfenced></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mo>=</mml:mo><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub><mml:mrow><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mn>4</mml:mn></mml:msubsup></mml:mrow><mml:mrow><mml:msub><mml:mi>Z</mml:mi><mml:mrow><mml:mi>c</mml:mi><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfenced><mml:mo>+</mml:mo><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub><mml:mrow><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mn>4</mml:mn></mml:msubsup></mml:mrow><mml:mrow><mml:msub><mml:mi>Z</mml:mi><mml:mrow><mml:mi>c</mml:mi><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>log</mml:mo><mml:mfenced close="]" open="["><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>,</mml:mo><mml:msubsup><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup><mml:mo stretchy="true">|</mml:mo><mml:mi mathvariant="normal">&#x003f5;</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mtext>.</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equj.gif" position="anchor"/></alternatives></disp-formula>
</p><p>For each cell, we assign the state that has the maximum posterior probability and only keep a cell if its maximum posterior probability is greater than 0.8. Let <inline-formula id="IEq120"><alternatives><tex-math id="M259">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {N}_{\varnothing } $$\end{document}</tex-math><mml:math id="M260"><mml:msub><mml:mi>N</mml:mi><mml:mo>&#x02205;</mml:mo></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq120.gif"/></alternatives></inline-formula>, <inline-formula id="IEq121"><alternatives><tex-math id="M261">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {N}_A $$\end{document}</tex-math><mml:math id="M262"><mml:msub><mml:mi>N</mml:mi><mml:mi>A</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq121.gif"/></alternatives></inline-formula>, <inline-formula id="IEq122"><alternatives><tex-math id="M263">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {N}_B $$\end{document}</tex-math><mml:math id="M264"><mml:msub><mml:mi>N</mml:mi><mml:mi>B</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq122.gif"/></alternatives></inline-formula>, and <inline-formula id="IEq123"><alternatives><tex-math id="M265">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {N}_{AB} $$\end{document}</tex-math><mml:math id="M266"><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq123.gif"/></alternatives></inline-formula> be the number of cells in state <inline-formula id="IEq124"><alternatives><tex-math id="M267">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\varnothing \right\} $$\end{document}</tex-math><mml:math id="M268"><mml:mfenced close="}" open="{"><mml:mo>&#x02205;</mml:mo></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq124.gif"/></alternatives></inline-formula>, <inline-formula id="IEq125"><alternatives><tex-math id="M269">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{ A\right\} $$\end{document}</tex-math><mml:math id="M270"><mml:mfenced close="}" open="{"><mml:mi>A</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq125.gif"/></alternatives></inline-formula>, <inline-formula id="IEq126"><alternatives><tex-math id="M271">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{ B\right\} $$\end{document}</tex-math><mml:math id="M272"><mml:mfenced close="}" open="{"><mml:mi>B</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq126.gif"/></alternatives></inline-formula>, and <inline-formula id="IEq127"><alternatives><tex-math id="M273">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{ AB\right\} $$\end{document}</tex-math><mml:math id="M274"><mml:mfenced close="}" open="{"><mml:mi mathvariant="italic">AB</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq127.gif"/></alternatives></inline-formula>, respectively. We then assign a gene to be: (i) silent if <inline-formula id="IEq128"><alternatives><tex-math id="M275">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {N}_A={N}_B={N}_{A B}=0 $$\end{document}</tex-math><mml:math id="M276"><mml:msub><mml:mi>N</mml:mi><mml:mi>A</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq128.gif"/></alternatives></inline-formula>; (ii) A-allele monoallelic if <inline-formula id="IEq129"><alternatives><tex-math id="M277">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {N}_A&#x0003e;0,{N}_B={N}_{A B}=0 $$\end{document}</tex-math><mml:math id="M278"><mml:msub><mml:mi>N</mml:mi><mml:mi>A</mml:mi></mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:msub><mml:mi>N</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq129.gif"/></alternatives></inline-formula>; (iii) B-allele monoallelic if <inline-formula id="IEq130"><alternatives><tex-math id="M279">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {N}_B&#x0003e;0,{N}_A={N}_{A B}=0 $$\end{document}</tex-math><mml:math id="M280"><mml:msub><mml:mi>N</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:msub><mml:mi>N</mml:mi><mml:mi>A</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq130.gif"/></alternatives></inline-formula>; (iv) biallelic otherwise (biallelic bursty if <inline-formula id="IEq131"><alternatives><tex-math id="M281">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 0.05\le \left({N}_A+{N}_{A B}\right)/\left({N}_{\varnothing }+{N}_A+{N}_B+{N}_{A B}\right)\le 0.95 $$\end{document}</tex-math><mml:math id="M282"><mml:mn>0.05</mml:mn><mml:mo>&#x02264;</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>A</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mo stretchy="true">/</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mo>&#x02205;</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>A</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mo>&#x02264;</mml:mo><mml:mn>0.95</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq131.gif"/></alternatives></inline-formula> and <inline-formula id="IEq132"><alternatives><tex-math id="M283">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 0.05\le \left({N}_B+{N}_{A B}\right)/\left({N}_{\varnothing }+{N}_A+{N}_B+{N}_{A B}\right)\le 0.95 $$\end{document}</tex-math><mml:math id="M284"><mml:mn>0.05</mml:mn><mml:mo>&#x02264;</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mo stretchy="true">/</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mo>&#x02205;</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>A</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi mathvariant="italic">AB</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mo>&#x02264;</mml:mo><mml:mn>0.95</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq132.gif"/></alternatives></inline-formula>).</p></sec><sec id="Sec17"><title>Parameter estimation for Poisson-Beta hierarchical model</title><p>Since exogenous spike-ins are added in a fixed amount and don&#x02019;t undergo transcriptional bursting, they can be used to directly estimate the technical variability-associated parameters <inline-formula id="IEq133"><alternatives><tex-math id="M285">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\alpha, \beta, \kappa, \tau \right\} $$\end{document}</tex-math><mml:math id="M286"><mml:mfenced close="}" open="{" separators=",,,"><mml:mi>&#x003b1;</mml:mi><mml:mi>&#x003b2;</mml:mi><mml:mi>&#x003ba;</mml:mi><mml:mi>&#x003c4;</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq133.gif"/></alternatives></inline-formula> that are shared across all cells from the same sequencing batch. Specifically, we use non-zero read counts to estimate <inline-formula id="IEq134"><alternatives><tex-math id="M287">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \alpha $$\end{document}</tex-math><mml:math id="M288"><mml:mi>&#x003b1;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq134.gif"/></alternatives></inline-formula> and <inline-formula id="IEq135"><alternatives><tex-math id="M289">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \beta $$\end{document}</tex-math><mml:math id="M290"><mml:mi>&#x003b2;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq135.gif"/></alternatives></inline-formula> through log-linear regression:<disp-formula id="Equk"><alternatives><tex-math id="M291">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg} \sim \mathrm{Poisson}\left(\alpha {\left({Y}_{cg}\right)}^{\beta}\right), $$\end{document}</tex-math><mml:math id="M292"><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Poisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi>&#x003b1;</mml:mi><mml:msup><mml:mfenced close=")" open="("><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:mfenced><mml:mi>&#x003b2;</mml:mi></mml:msup></mml:mrow></mml:mfenced><mml:mtext>,</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equk.gif" position="anchor"/></alternatives></disp-formula>
</p><p>where <inline-formula id="IEq136"><alternatives><tex-math id="M293">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg}&#x0003e;0 $$\end{document}</tex-math><mml:math id="M294"><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq136.gif"/></alternatives></inline-formula>, capture and sequencing efficiencies are confounded in <inline-formula id="IEq137"><alternatives><tex-math id="M295">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \alpha $$\end{document}</tex-math><mml:math id="M296"><mml:mi>&#x003b1;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq137.gif"/></alternatives></inline-formula>, and amplification bias is modeled by <inline-formula id="IEq138"><alternatives><tex-math id="M297">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \beta $$\end{document}</tex-math><mml:math id="M298"><mml:mi>&#x003b2;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq138.gif"/></alternatives></inline-formula> (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S5). We then use the Nelder-Mead simplex algorithm to jointly optimize <inline-formula id="IEq139"><alternatives><tex-math id="M299">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \kappa $$\end{document}</tex-math><mml:math id="M300"><mml:mi>&#x003ba;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq139.gif"/></alternatives></inline-formula> and <inline-formula id="IEq140"><alternatives><tex-math id="M301">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \tau $$\end{document}</tex-math><mml:math id="M302"><mml:mi>&#x003c4;</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq140.gif"/></alternatives></inline-formula>, which models the probability of non-dropout, using the likelihood function:<disp-formula id="Equl"><alternatives><tex-math id="M303">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{l} \log \left(\mathrm{\mathcal{L}}\left(\kappa, \tau \Big| Q, Y,\widehat{\alpha},\widehat{\beta}\right)\right)={\prod}_c{\prod}_g \log \left\{\mathrm{pPoisson}\left({Q}_{c g},\widehat{\alpha}{\left({Y}_{c g}\right)}^{\widehat{\beta}}\right)\right.\mathrm{expit}\left(\kappa +\tau \log {Y}_{c g}\right)+\\ {}\kern9.48em \left.\left(1-\mathrm{expit}\left(\kappa +\tau \log {Y}_{c g}\right)\right)\mathbb{l}\left({Q}_{c g}=0\right)\right\},\end{array} $$\end{document}</tex-math><mml:math id="M304"><mml:mtable columnalign="left"><mml:mtr><mml:mtd><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="normal">&#x02112;</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi>&#x003ba;</mml:mi><mml:mo>,</mml:mo><mml:mi>&#x003c4;</mml:mi><mml:mo stretchy="true">|</mml:mo><mml:mi>Q</mml:mi><mml:mo>,</mml:mo><mml:mi>Y</mml:mi><mml:mo>,</mml:mo><mml:mover accent="true"><mml:mi>&#x003b1;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>,</mml:mo><mml:mover accent="true"><mml:mi>&#x003b2;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mo>=</mml:mo><mml:msub><mml:mo stretchy="true">&#x0220f;</mml:mo><mml:mi>c</mml:mi></mml:msub><mml:mrow><mml:msub><mml:mo stretchy="true">&#x0220f;</mml:mo><mml:mi>g</mml:mi></mml:msub></mml:mrow><mml:mo>log</mml:mo><mml:mfenced close="" open="{"><mml:mrow><mml:mi mathvariant="normal">pPoisson</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mover accent="true"><mml:mi>&#x003b1;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:msup><mml:mfenced close=")" open="("><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:mfenced><mml:mover accent="true"><mml:mi>&#x003b2;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover></mml:msup></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi>&#x003ba;</mml:mi><mml:mo>+</mml:mo><mml:mi>&#x003c4;</mml:mi><mml:mo>log</mml:mo><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mo>+</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mspace width="9.48em"/><mml:mfenced close="}" open=""><mml:mrow><mml:mfenced close=")" open="("><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi>&#x003ba;</mml:mi><mml:mo>+</mml:mo><mml:mi>&#x003c4;</mml:mi><mml:mo>log</mml:mo><mml:msub><mml:mi>Y</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mi mathvariant="double-struck">l</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mtext>,</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equl.gif" position="anchor"/></alternatives></disp-formula>
</p><p>where <inline-formula id="IEq141"><alternatives><tex-math id="M305">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{pPoisson}\left( x, y\right) $$\end{document}</tex-math><mml:math id="M306"><mml:mi mathvariant="normal">pPoisson</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>x</mml:mi><mml:mi>y</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq141.gif"/></alternatives></inline-formula> specifies the Poisson likelihood of getting <inline-formula id="IEq142"><alternatives><tex-math id="M307">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ x $$\end{document}</tex-math><mml:math id="M308"><mml:mi>x</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq142.gif"/></alternatives></inline-formula> from a Poisson distribution with mean <inline-formula id="IEq143"><alternatives><tex-math id="M309">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ y $$\end{document}</tex-math><mml:math id="M310"><mml:mi>y</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq143.gif"/></alternatives></inline-formula>. This log-likelihood function together with the estimated parameters decomposes the zero read counts (<inline-formula id="IEq144"><alternatives><tex-math id="M311">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Q}_{cg}=0 $$\end{document}</tex-math><mml:math id="M312"><mml:msub><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">cg</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq144.gif"/></alternatives></inline-formula>) into being from the dropout events or from being sampled as zero from the Poisson sampling during sequencing (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S5a).</p><p>The allele-specific kinetic parameters are estimated via the moment estimator methods, which is more computationally efficient than the Gibbs sampler method adopted by Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>]. For each gene, the distribution moments of the A allele given true expression levels <inline-formula id="IEq145"><alternatives><tex-math id="M313">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_c^A $$\end{document}</tex-math><mml:math id="M314"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq145.gif"/></alternatives></inline-formula> and <inline-formula id="IEq146"><alternatives><tex-math id="M315">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {Y}_c^B $$\end{document}</tex-math><mml:math id="M316"><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq146.gif"/></alternatives></inline-formula> are:<disp-formula id="Equm"><alternatives><tex-math id="M317">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{l}{m}_1^A\equiv \frac{E\left[{\displaystyle {\sum}_c}{Y}_c^A\right]}{{\displaystyle {\sum}_c}{\phi}_c}=\frac{k_{on}^A{s}^A}{k_{on}^A+{k}_{off}^A}\\ {}{m}_2^A\equiv \frac{E\left[{\displaystyle {\sum}_c}{Y}_c^A\left({Y}_c^A-1\right)\right]}{{\displaystyle {\sum}_c}{\phi}_c^2}=\frac{k_{on}^A\left({k}_{on}^A+1\right){\left({s}^A\right)}^2}{\left({k}_{on}^A+{k}_{off}^A\right)\left({k}_{on}^A+{k}_{off}^A+1\right)}\\ {}{m}_3^A\equiv \frac{E\left[{\displaystyle {\sum}_c}{Y}_c^A\left({Y}_c^A-1\right)\left({Y}_c^A-2\right)\right]}{{\displaystyle {\sum}_c}{\phi}_c^3}=\frac{k_{on}^A\left({k}_{on}^A+1\right)\left({k}_{on}^A+2\right){\left({s}^A\right)}^3}{\left({k}_{on}^A+{k}_{off}^A\right)\left({k}_{on}^A+{k}_{off}^A+1\right)\left({k}_{on}^A+{k}_{off}^A+2\right)}.\end{array} $$\end{document}</tex-math><mml:math id="M318"><mml:mtable columnalign="left"><mml:mtr><mml:mtd><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mtext>&#x02261;</mml:mtext><mml:mfrac><mml:mrow><mml:mi>E</mml:mi><mml:mfenced close="]" open="["><mml:mrow><mml:mstyle displaystyle="true"><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub></mml:mstyle><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mstyle displaystyle="true"><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub></mml:mstyle><mml:msub><mml:mi>&#x003d5;</mml:mi><mml:mi>c</mml:mi></mml:msub></mml:mrow></mml:mfrac><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:msup><mml:mi>s</mml:mi><mml:mi>A</mml:mi></mml:msup></mml:mrow><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfrac></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mtext>&#x02261;</mml:mtext><mml:mfrac><mml:mrow><mml:mi>E</mml:mi><mml:mfenced close="]" open="["><mml:mrow><mml:mstyle displaystyle="true"><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub></mml:mstyle><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mstyle displaystyle="true"><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub></mml:mstyle><mml:msubsup><mml:mi>&#x003d5;</mml:mi><mml:mi>c</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mfrac><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced><mml:msup><mml:mfenced close=")" open="("><mml:msup><mml:mi>s</mml:mi><mml:mi>A</mml:mi></mml:msup></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mrow><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mtext>&#x02261;</mml:mtext><mml:mfrac><mml:mrow><mml:mi>E</mml:mi><mml:mfenced close="]" open="["><mml:mrow><mml:mstyle displaystyle="true"><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub></mml:mstyle><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>Y</mml:mi><mml:mi>c</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mstyle displaystyle="true"><mml:msub><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mi>c</mml:mi></mml:msub></mml:mstyle><mml:msubsup><mml:mi>&#x003d5;</mml:mi><mml:mi>c</mml:mi><mml:mn>3</mml:mn></mml:msubsup></mml:mrow></mml:mfrac><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:mfenced><mml:msup><mml:mfenced close=")" open="("><mml:msup><mml:mi>s</mml:mi><mml:mi>A</mml:mi></mml:msup></mml:mfenced><mml:mn>3</mml:mn></mml:msup></mml:mrow><mml:mrow><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>k</mml:mi><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>+</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac><mml:mtext>.</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equm.gif" position="anchor"/></alternatives></disp-formula>
</p><p>Solving this system of three equations, we have:<disp-formula id="Equn"><alternatives><tex-math id="M319">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{l}{\widehat{k}}_{on}^A=\frac{-2\left(-{m}_1^A{\left({m}_2^A\right)}^2+{\left({m}_1^A\right)}^2{m}_3^A\right)}{-{m}_1^A{\left({m}_2^A\right)}^2+2{\left({m}_1^A\right)}^2{m}_3^A-{m}_2^A{m}_3^A}\\ {}{\widehat{k}}_{off}^A=\frac{2\left({\left({m}_1^A\right)}^2-{m}_2^A\right)\left({m}_1^A{m}_2^A-{m}_3^A\right)\left({m}_1^A{m}_3^A-{\left({m}_2^A\right)}^2\right)}{\left({\left({m}_1^A\right)}^2{m}_2^A-2{\left({m}_2^A\right)}^2+{m}_1^A{m}_3^A\right)\left(2{\left({m}_1^A\right)}^2{m}_3^A-{m}_1^A{\left({m}_2^A\right)}^2-{m}_2^A{m}_3^A\right)}\\ {}{\widehat{s}}^A=\frac{-{m}_1^A{\left({m}_2^A\right)}^2+2{\left({m}_1^A\right)}^2{m}_3^A-{m}_2^A{m}_3^A}{{\left({m}_1^A\right)}^2{m}_2^A-2{\left({m}_2^A\right)}^2+{m}_1^A{m}_3^A}.\end{array} $$\end{document}</tex-math><mml:math id="M320"><mml:mtable columnalign="left"><mml:mtr><mml:mtd><mml:msubsup><mml:mover accent="true"><mml:mi>k</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi mathvariant="italic">on</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>2</mml:mn><mml:mfenced close=")" open="("><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:mn>2</mml:mn><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfrac></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msubsup><mml:mover accent="true"><mml:mi>k</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi mathvariant="italic">off</mml:mi><mml:mi>A</mml:mi></mml:msubsup><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mn>2</mml:mn><mml:mfenced close=")" open="("><mml:mrow><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mfenced close=")" open="("><mml:mrow><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:mn>2</mml:mn><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced><mml:mfenced close=")" open="("><mml:mrow><mml:mn>2</mml:mn><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msup><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>A</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:mn>2</mml:mn><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow><mml:mrow><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:mn>2</mml:mn><mml:msup><mml:mfenced close=")" open="("><mml:msubsup><mml:mi>m</mml:mi><mml:mn>2</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>m</mml:mi><mml:mn>1</mml:mn><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>m</mml:mi><mml:mn>3</mml:mn><mml:mi>A</mml:mi></mml:msubsup></mml:mrow></mml:mfrac><mml:mtext>.</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="13059_2017_1200_Article_Equn.gif" position="anchor"/></alternatives></disp-formula>
</p><p>Substituting A with B we get the kinetic parameters for the B allele. To get the sample moments, we propose a novel histogram repiling method that gives the sample distribution and sample moment estimates of the true expression from the distribution of the observed expression (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S7). Specifically, for each gene we denote <inline-formula id="IEq147"><alternatives><tex-math id="M321">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ c(Q) $$\end{document}</tex-math><mml:math id="M322"><mml:mi>c</mml:mi><mml:mfenced close=")" open="("><mml:mi>Q</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq147.gif"/></alternatives></inline-formula> as the number of cells with observed expression <inline-formula id="IEq148"><alternatives><tex-math id="M323">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ Q $$\end{document}</tex-math><mml:math id="M324"><mml:mi>Q</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq148.gif"/></alternatives></inline-formula> and <inline-formula id="IEq149"><alternatives><tex-math id="M325">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ n(Y) $$\end{document}</tex-math><mml:math id="M326"><mml:mi>n</mml:mi><mml:mfenced close=")" open="("><mml:mi>Y</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq149.gif"/></alternatives></inline-formula> as the number of cells with the corresponding true expression <inline-formula id="IEq150"><alternatives><tex-math id="M327">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ Y $$\end{document}</tex-math><mml:math id="M328"><mml:mi>Y</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq150.gif"/></alternatives></inline-formula>. <inline-formula id="IEq151"><alternatives><tex-math id="M329">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ c(Q) $$\end{document}</tex-math><mml:math id="M330"><mml:mi>c</mml:mi><mml:mfenced close=")" open="("><mml:mi>Q</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq151.gif"/></alternatives></inline-formula> follows a Binomial distribution indexed at <inline-formula id="IEq152"><alternatives><tex-math id="M331">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ n(Y) $$\end{document}</tex-math><mml:math id="M332"><mml:mi>n</mml:mi><mml:mfenced close=")" open="("><mml:mi>Y</mml:mi></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq152.gif"/></alternatives></inline-formula> with probability of no dropout:<disp-formula id="Equo"><alternatives><tex-math id="M333">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ c(Q) \sim \mathrm{Binomial}\left( n(Y),\mathrm{expit}\left(\widehat{\kappa}+\widehat{\tau} \log Y\right)\right). $$\end{document}</tex-math><mml:math id="M334"><mml:mi>c</mml:mi><mml:mfenced close=")" open="("><mml:mi>Q</mml:mi></mml:mfenced><mml:mspace width="0.25em"/><mml:mo>~</mml:mo><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">Binomial</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi>n</mml:mi><mml:mfenced close=")" open="("><mml:mi>Y</mml:mi></mml:mfenced><mml:mo>,</mml:mo><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mover accent="true"><mml:mi>&#x003ba;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>+</mml:mo><mml:mover accent="true"><mml:mi>&#x003c4;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>log</mml:mo><mml:mi>Y</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced><mml:mtext>.</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equo.gif" position="anchor"/></alternatives></disp-formula>
</p><p>Then:<disp-formula id="Equp"><alternatives><tex-math id="M335">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \widehat{n}(Y)=\frac{c(Q)}{\mathrm{expit}\left(\widehat{\kappa}+\widehat{\tau} \log Y\right)}=\frac{c(Q)}{\mathrm{expit}\left(\widehat{\kappa}+\frac{\widehat{\tau}}{\widehat{\beta}} \log \frac{Q}{\widehat{\alpha}}\right)}. $$\end{document}</tex-math><mml:math id="M336"><mml:mover accent="true"><mml:mi>n</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mfenced close=")" open="("><mml:mi>Y</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>c</mml:mi><mml:mfenced close=")" open="("><mml:mi>Q</mml:mi></mml:mfenced></mml:mrow><mml:mrow><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mover accent="true"><mml:mi>&#x003ba;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>+</mml:mo><mml:mover accent="true"><mml:mi>&#x003c4;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>log</mml:mo><mml:mi>Y</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>c</mml:mi><mml:mfenced close=")" open="("><mml:mi>Q</mml:mi></mml:mfenced></mml:mrow><mml:mrow><mml:mi mathvariant="normal">expit</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mover accent="true"><mml:mi>&#x003ba;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mo>+</mml:mo><mml:mfrac><mml:mover accent="true"><mml:mi>&#x003c4;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mover accent="true"><mml:mi>&#x003b2;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover></mml:mfrac><mml:mo>log</mml:mo><mml:mfrac><mml:mrow><mml:mi>Q</mml:mi></mml:mrow><mml:mrow><mml:mover accent="true"><mml:mi>&#x003b1;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac><mml:mtext>.</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equp.gif" position="anchor"/></alternatives></disp-formula>
</p><p>These moment estimates of the kinetic parameters are sometimes negative as is pointed out by Kim and Marioni [<xref ref-type="bibr" rid="CR25">25</xref>]. Using in silico simulation studies, we investigate the estimation accuracy and robustness under different settings.</p></sec><sec id="Sec18"><title>Hypothesis testing framework</title><p>We carry out a nonparametric bootstrap hypothesis testing procedure with the null hypothesis that the two alleles of a gene share the same kinetic parameters (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4a</xref>, <xref rid="Fig4" ref-type="fig">b</xref>). The procedures are as follow.<list list-type="simple"><list-item><label>(i)</label><p>For gene g, let <inline-formula id="IEq153"><alternatives><tex-math id="M337">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{{Q}_{1 g}^A,{Q}_{2 g}^A,\dots, {Q}_{ng}^A\right\} $$\end{document}</tex-math><mml:math id="M338"><mml:mfenced close="}" open="{" separators=",,,"><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>1</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup><mml:mo>&#x02026;</mml:mo><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">ng</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq153.gif"/></alternatives></inline-formula> and <inline-formula id="IEq154"><alternatives><tex-math id="M339">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{{Q}_{1 g}^B,{Q}_{2 g}^B,\dots, {Q}_{ng}^B\right\} $$\end{document}</tex-math><mml:math id="M340"><mml:mfenced close="}" open="{" separators=",,,"><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>1</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup><mml:mo>&#x02026;</mml:mo><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">ng</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq154.gif"/></alternatives></inline-formula> be the observed allele-specific read counts. Estimate allele-specific kinetic parameters with adjustment of technical variability:</p></list-item></list>
<disp-formula id="Equq"><alternatives><tex-math id="M341">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\widehat{\theta}}^A=\left\{{\widehat{k}}_{on, g}^A,{\widehat{k}}_{off, g}^A,{\widehat{s}}_g^A\right\};\ {\widehat{\theta}}^B=\left\{{\widehat{k}}_{on, g}^B,{\widehat{k}}_{off, g}^B,{\widehat{s}}_g^B\right\}. $$\end{document}</tex-math><mml:math id="M342"><mml:msup><mml:mover accent="true"><mml:mi>&#x003b8;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>A</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mfenced close="}" open="{" separators=",,"><mml:msubsup><mml:mover accent="true"><mml:mi>k</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mover accent="true"><mml:mi>k</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msubsup><mml:msubsup><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>g</mml:mi><mml:mi>A</mml:mi></mml:msubsup></mml:mfenced><mml:mo>;</mml:mo><mml:mspace width="0.25em"/><mml:msup><mml:mover accent="true"><mml:mi>&#x003b8;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>B</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mfenced close="}" open="{" separators=",,"><mml:msubsup><mml:mover accent="true"><mml:mi>k</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup><mml:msubsup><mml:mover accent="true"><mml:mi>k</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mi>B</mml:mi></mml:msubsup><mml:msubsup><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>g</mml:mi><mml:mi>B</mml:mi></mml:msubsup></mml:mfenced><mml:mtext>.</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equq.gif" position="anchor"/></alternatives></disp-formula>
<list list-type="simple"><list-item><label>(ii)</label><p>Combine the <inline-formula id="IEq155"><alternatives><tex-math id="M343">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 2 n $$\end{document}</tex-math><mml:math id="M344"><mml:mn>2</mml:mn><mml:mi>n</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq155.gif"/></alternatives></inline-formula> observed allelic measurements and draw samples of size <inline-formula id="IEq156"><alternatives><tex-math id="M345">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 2 n $$\end{document}</tex-math><mml:math id="M346"><mml:mn>2</mml:mn><mml:mi>n</mml:mi></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq156.gif"/></alternatives></inline-formula> from the combined pool with replacement. Assign the first <italic>n</italic> with their corresponding cell sizes to allele A as <inline-formula id="IEq157"><alternatives><tex-math id="M347">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{{Q}_{1 g}^{A*},{Q}_{2 g}^{A*},\dots, {Q}_{ng}^{A*}\right\} $$\end{document}</tex-math><mml:math id="M348"><mml:mfenced close="}" open="{" separators=",,,"><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>1</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:mo>&#x02026;</mml:mo><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">ng</mml:mi><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq157.gif"/></alternatives></inline-formula>, the next <italic>n</italic> to allele B <inline-formula id="IEq158"><alternatives><tex-math id="M349">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{{Q}_{1 g}^{B*},{Q}_{2 g}^{B*},\dots, {Q}_{ng}^{B*}\right\} $$\end{document}</tex-math><mml:math id="M350"><mml:mfenced close="}" open="{" separators=",,,"><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>1</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:msubsup><mml:mi>Q</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:mo>&#x02026;</mml:mo><mml:msubsup><mml:mi>Q</mml:mi><mml:mi mathvariant="italic">ng</mml:mi><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2017_1200_Article_IEq158.gif"/></alternatives></inline-formula>. Estimate kinetic parameters with adjustment of technical variability from the bootstrap samples:</p></list-item></list>
<disp-formula id="Equr"><alternatives><tex-math id="M351">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\theta}^{A*}=\left\{{k}_{on, g}^{A*},{k}_{off, g}^{A*},{s}_g^{A*}\right\};\ {\theta}^{B*}=\left\{{k}_{on, g}^{B*},{k}_{off, g}^{B*},{s}_g^{B*}\right\}. $$\end{document}</tex-math><mml:math id="M352"><mml:msup><mml:mi>&#x003b8;</mml:mi><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:mfenced close="}" open="{" separators=",,"><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:msubsup><mml:mi>s</mml:mi><mml:mi>g</mml:mi><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup></mml:mfenced><mml:mo>;</mml:mo><mml:mspace width="0.25em"/><mml:msup><mml:mi>&#x003b8;</mml:mi><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:mfenced close="}" open="{" separators=",,"><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">on</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:msubsup><mml:mi>k</mml:mi><mml:mrow><mml:mi mathvariant="italic">off</mml:mi><mml:mo>,</mml:mo><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup><mml:msubsup><mml:mi>s</mml:mi><mml:mi>g</mml:mi><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msubsup></mml:mfenced><mml:mtext>.</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equr.gif" position="anchor"/></alternatives></disp-formula>
</p><p>Iterate this <italic>N</italic> times.<list list-type="simple"><list-item><label>(iii)</label><p>Compute the <italic>p</italic> values:</p></list-item></list>
<disp-formula id="Equs"><alternatives><tex-math id="M353">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ p=\frac{{\displaystyle \sum}\mathbb{l}\left(\left|{\theta}^{A*}-{\theta}^{B*}\right|\ge \left|{\widehat{\theta}}^A-{\widehat{\theta}}^B\right|\right)}{N}. $$\end{document}</tex-math><mml:math id="M354"><mml:mi>p</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mstyle displaystyle="true"><mml:mo stretchy="true">&#x02211;</mml:mo></mml:mstyle><mml:mi mathvariant="double-struck">l</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mfenced close="|" open="|"><mml:mrow><mml:msup><mml:mi>&#x003b8;</mml:mi><mml:mrow><mml:mi>A</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msup><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mi>&#x003b8;</mml:mi><mml:mrow><mml:mi>B</mml:mi><mml:mo>*</mml:mo></mml:mrow></mml:msup></mml:mrow></mml:mfenced><mml:mo>&#x02265;</mml:mo><mml:mfenced close="|" open="|"><mml:mrow><mml:msup><mml:mover accent="true"><mml:mi>&#x003b8;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>A</mml:mi></mml:msup><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mover accent="true"><mml:mi>&#x003b8;</mml:mi><mml:mo stretchy="true">^</mml:mo></mml:mover><mml:mi>B</mml:mi></mml:msup></mml:mrow></mml:mfenced></mml:mrow></mml:mfenced></mml:mrow><mml:mi>N</mml:mi></mml:mfrac><mml:mtext>.</mml:mtext></mml:math><graphic xlink:href="13059_2017_1200_Article_Equs.gif" position="anchor"/></alternatives></disp-formula>
</p><p>We use a binomial test of allelic imbalance with the null hypothesis that the allelic ratio of the mean expression across all cells is 0.5. A chi-square test of independence is further performed to test whether the two alleles of a gene fire independently (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4c</xref>). The observed number of cells is from the direct output of the Bayes gene categorization framework. For all hypothesis testing, we adopt FDR to adjust for multiple comparisons.</p></sec></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec19"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13059_2017_1200_MOESM1_ESM.pdf"><label>Additional file 1:</label><caption><p>
<bold>Figures S1</bold>&#x02013;<bold>S15.</bold>
<bold>Table S5.</bold> Supplementary Methods. (PDF 3472 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13059_2017_1200_MOESM2_ESM.xlsx"><label>Additional file 2: Table S1.</label><caption><p>Spike-in input for mouse blastocyst dataset. (XLSX 11 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13059_2017_1200_MOESM3_ESM.xlsx"><label>Additional file 3: Table S2.</label><caption><p>SCALE output for mouse blastocyst dataset. (XLSX 2118 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13059_2017_1200_MOESM4_ESM.xlsx"><label>Additional file 4: Table S3.</label><caption><p>Spike-in input for human fibroblast dataset. (XLSX 17 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13059_2017_1200_MOESM5_ESM.xlsx"><label>Additional file 5: Table S4.</label><caption><p>SCALE output for human fibroblast dataset. (XLSX 785 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>ASE</term><def><p>Allele-specific expression</p></def></def-item><def-item><term>EM</term><def><p>Expectation-maximization</p></def></def-item><def-item><term>eQTL</term><def><p>Expression quantitative trait loci</p></def></def-item><def-item><term>FDR</term><def><p>False discovery rate</p></def></def-item><def-item><term>FISH</term><def><p>Fluorescence in situ hybridization</p></def></def-item><def-item><term>ME</term><def><p>Monoallelic expression</p></def></def-item><def-item><term>QTL</term><def><p>Quantitative trait loci</p></def></def-item><def-item><term>RNA-seq</term><def><p>RNA sequencing</p></def></def-item><def-item><term>scRNA-seq</term><def><p>Single-cell RNA sequencing</p></def></def-item><def-item><term>SNP</term><def><p>Single-nucleotide polymorphism</p></def></def-item></def-list></glossary><ack><title>Acknowledgements</title><p>We thank Dr. Daniel Ramsk&#x000f6;ld for providing the mouse preimplantation embryo dataset, Dr. Christelle Borel for providing the human fibroblast dataset, and Cheng Jia, Dr. Arjun Raj, and Dr. Uschi Symmons for helpful comments and suggestions.</p><sec id="FPar1"><title>Funding</title><p>This work was supported by National Institutes of Health (NIH) grant R01HG006137 to NRZ, and R01GM108600 and R01HL113147 to ML.</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p>SCALE is an open-source R package available at <ext-link ext-link-type="uri" xlink:href="https://github.com/yuchaojiang/SCALE">https://github.com/yuchaojiang/SCALE</ext-link> with license GPL-3.0. Source code used in the manuscript is available via Zenodo with DOI 10.5281/zenodo.437554.</p></sec><sec id="FPar3"><title>Authors&#x02019; contributions</title><p>NRZ and ML initiated and envisioned the study. YJ, NRZ, and ML formulated the model. YJ developed and implemented the algorithm. YJ, NRZ, and ML conducted the analysis and wrote the manuscript. All authors read and approved the final manuscript.</p></sec><sec id="FPar4"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="FPar5"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="FPar6"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec id="FPar7"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Buckland</surname><given-names>PR</given-names></name></person-group><article-title>Allele-specific gene expression differences in humans</article-title><source>Hum Mol Genet</source><year>2004</year><volume>13 Spec No 2</volume><fpage>R255</fpage><lpage>60</lpage><pub-id pub-id-type="doi">10.1093/hmg/ddh227</pub-id><?supplied-pmid 15358732?><pub-id pub-id-type="pmid">15358732</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Deng</surname><given-names>Q</given-names></name><name><surname>Ramskold</surname><given-names>D</given-names></name><name><surname>Reinius</surname><given-names>B</given-names></name><name><surname>Sandberg</surname><given-names>R</given-names></name></person-group><article-title>Single-cell RNA-seq reveals dynamic, random monoallelic gene expression in mammalian cells</article-title><source>Science.</source><year>2014</year><volume>343</volume><fpage>193</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1126/science.1245316</pub-id><?supplied-pmid 24408435?><pub-id pub-id-type="pmid">24408435</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gendrel</surname><given-names>AV</given-names></name><name><surname>Attia</surname><given-names>M</given-names></name><name><surname>Chen</surname><given-names>CJ</given-names></name><name><surname>Diabangouaya</surname><given-names>P</given-names></name><name><surname>Servant</surname><given-names>N</given-names></name><name><surname>Barillot</surname><given-names>E</given-names></name><name><surname>Heard</surname><given-names>E</given-names></name></person-group><article-title>Developmental dynamics and disease potential of random monoallelic gene expression</article-title><source>Dev Cell.</source><year>2014</year><volume>28</volume><fpage>366</fpage><lpage>80</lpage><pub-id pub-id-type="doi">10.1016/j.devcel.2014.01.016</pub-id><?supplied-pmid 24576422?><pub-id pub-id-type="pmid">24576422</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eckersley-Maslin</surname><given-names>MA</given-names></name><name><surname>Spector</surname><given-names>DL</given-names></name></person-group><article-title>Random monoallelic expression: regulating gene expression one allele at a time</article-title><source>Trends Genet.</source><year>2014</year><volume>30</volume><fpage>237</fpage><lpage>44</lpage><pub-id pub-id-type="doi">10.1016/j.tig.2014.03.003</pub-id><?supplied-pmid 24780084?><pub-id pub-id-type="pmid">24780084</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eckersley-Maslin</surname><given-names>MA</given-names></name><name><surname>Thybert</surname><given-names>D</given-names></name><name><surname>Bergmann</surname><given-names>JH</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name><name><surname>Flicek</surname><given-names>P</given-names></name><name><surname>Spector</surname><given-names>DL</given-names></name></person-group><article-title>Random monoallelic gene expression increases upon embryonic stem cell differentiation</article-title><source>Dev Cell.</source><year>2014</year><volume>28</volume><fpage>351</fpage><lpage>65</lpage><pub-id pub-id-type="doi">10.1016/j.devcel.2014.01.017</pub-id><?supplied-pmid 24576421?><pub-id pub-id-type="pmid">24576421</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Reinius</surname><given-names>B</given-names></name><name><surname>Sandberg</surname><given-names>R</given-names></name></person-group><article-title>Random monoallelic expression of autosomal genes: stochastic transcription and allele-level regulation</article-title><source>Nat Rev Genet.</source><year>2015</year><volume>16</volume><fpage>653</fpage><lpage>64</lpage><pub-id pub-id-type="doi">10.1038/nrg3888</pub-id><?supplied-pmid 26442639?><pub-id pub-id-type="pmid">26442639</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Reinius</surname><given-names>B</given-names></name><name><surname>Mold</surname><given-names>JE</given-names></name><name><surname>Ramskold</surname><given-names>D</given-names></name><name><surname>Deng</surname><given-names>Q</given-names></name><name><surname>Johnsson</surname><given-names>P</given-names></name><name><surname>Michaelsson</surname><given-names>J</given-names></name><name><surname>Frisen</surname><given-names>J</given-names></name><name><surname>Sandberg</surname><given-names>R</given-names></name></person-group><article-title>Analysis of allelic expression patterns in clonal somatic cells by single-cell RNA-seq</article-title><source>Nat Genet.</source><year>2016</year><volume>48</volume><fpage>1430</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1038/ng.3678</pub-id><?supplied-pmid 27668657?><pub-id pub-id-type="pmid">27668657</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bjornsson</surname><given-names>HT</given-names></name><name><surname>Albert</surname><given-names>TJ</given-names></name><name><surname>Ladd-Acosta</surname><given-names>CM</given-names></name><name><surname>Green</surname><given-names>RD</given-names></name><name><surname>Rongione</surname><given-names>MA</given-names></name><name><surname>Middle</surname><given-names>CM</given-names></name><name><surname>Irizarry</surname><given-names>RA</given-names></name><name><surname>Broman</surname><given-names>KW</given-names></name><name><surname>Feinberg</surname><given-names>AP</given-names></name></person-group><article-title>SNP-specific array-based allele-specific expression analysis</article-title><source>Genome Res.</source><year>2008</year><volume>18</volume><fpage>771</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1101/gr.073254.107</pub-id><?supplied-pmid 18369178?><pub-id pub-id-type="pmid">18369178</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Skelly</surname><given-names>DA</given-names></name><name><surname>Johansson</surname><given-names>M</given-names></name><name><surname>Madeoy</surname><given-names>J</given-names></name><name><surname>Wakefield</surname><given-names>J</given-names></name><name><surname>Akey</surname><given-names>JM</given-names></name></person-group><article-title>A powerful and flexible statistical framework for testing hypotheses of allele-specific gene expression from RNA-seq data</article-title><source>Genome Res.</source><year>2011</year><volume>21</volume><fpage>1728</fpage><lpage>37</lpage><pub-id pub-id-type="doi">10.1101/gr.119784.110</pub-id><?supplied-pmid 21873452?><pub-id pub-id-type="pmid">21873452</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leon-Novelo</surname><given-names>LG</given-names></name><name><surname>McIntyre</surname><given-names>LM</given-names></name><name><surname>Fear</surname><given-names>JM</given-names></name><name><surname>Graze</surname><given-names>RM</given-names></name></person-group><article-title>A flexible Bayesian method for detecting allelic imbalance in RNA-seq data</article-title><source>BMC Genomics.</source><year>2014</year><volume>15</volume><fpage>920</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-15-920</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Castel</surname><given-names>SE</given-names></name><name><surname>Levy-Moonshine</surname><given-names>A</given-names></name><name><surname>Mohammadi</surname><given-names>P</given-names></name><name><surname>Banks</surname><given-names>E</given-names></name><name><surname>Lappalainen</surname><given-names>T</given-names></name></person-group><article-title>Tools and best practices for data processing in allelic expression analysis</article-title><source>Genome Biol.</source><year>2015</year><volume>16</volume><fpage>195</fpage><pub-id pub-id-type="doi">10.1186/s13059-015-0762-6</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Knight</surname><given-names>JC</given-names></name></person-group><article-title>Allele-specific gene expression uncovered</article-title><source>Trends Genet.</source><year>2004</year><volume>20</volume><fpage>113</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1016/j.tig.2004.01.001</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bell</surname><given-names>CG</given-names></name><name><surname>Beck</surname><given-names>S</given-names></name></person-group><article-title>Advances in the identification and analysis of allele-specific expression</article-title><source>Genome Med.</source><year>2009</year><volume>1</volume><fpage>56</fpage><pub-id pub-id-type="doi">10.1186/gm56</pub-id><?supplied-pmid 19490587?><pub-id pub-id-type="pmid">19490587</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>de la Chapelle</surname><given-names>A</given-names></name></person-group><article-title>Genetic predisposition to human disease: allele-specific expression and low-penetrance regulatory loci</article-title><source>Oncogene.</source><year>2009</year><volume>28</volume><fpage>3345</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1038/onc.2009.194</pub-id><?supplied-pmid 19597467?><pub-id pub-id-type="pmid">19597467</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stegle</surname><given-names>O</given-names></name><name><surname>Teichmann</surname><given-names>SA</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name></person-group><article-title>Computational and analytical challenges in single-cell transcriptomics</article-title><source>Nat Rev Genet.</source><year>2015</year><volume>16</volume><fpage>133</fpage><lpage>45</lpage><pub-id pub-id-type="doi">10.1038/nrg3833</pub-id><?supplied-pmid 25628217?><pub-id pub-id-type="pmid">25628217</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kolodziejczyk</surname><given-names>AA</given-names></name><name><surname>Kim</surname><given-names>JK</given-names></name><name><surname>Svensson</surname><given-names>V</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name><name><surname>Teichmann</surname><given-names>SA</given-names></name></person-group><article-title>The technology and biology of single-cell RNA sequencing</article-title><source>Mol Cell.</source><year>2015</year><volume>58</volume><fpage>610</fpage><lpage>20</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2015.04.005</pub-id><?supplied-pmid 26000846?><pub-id pub-id-type="pmid">26000846</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Borel</surname><given-names>C</given-names></name><name><surname>Ferreira</surname><given-names>PG</given-names></name><name><surname>Santoni</surname><given-names>F</given-names></name><name><surname>Delaneau</surname><given-names>O</given-names></name><name><surname>Fort</surname><given-names>A</given-names></name><name><surname>Popadin</surname><given-names>KY</given-names></name><name><surname>Garieri</surname><given-names>M</given-names></name><name><surname>Falconnet</surname><given-names>E</given-names></name><name><surname>Ribaux</surname><given-names>P</given-names></name><name><surname>Guipponi</surname><given-names>M</given-names></name><etal/></person-group><article-title>Biased allelic expression in human primary fibroblast single cells</article-title><source>Am J Hum Genet.</source><year>2015</year><volume>96</volume><fpage>70</fpage><lpage>80</lpage><pub-id pub-id-type="doi">10.1016/j.ajhg.2014.12.001</pub-id><?supplied-pmid 25557783?><pub-id pub-id-type="pmid">25557783</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chubb</surname><given-names>JR</given-names></name><name><surname>Trcek</surname><given-names>T</given-names></name><name><surname>Shenoy</surname><given-names>SM</given-names></name><name><surname>Singer</surname><given-names>RH</given-names></name></person-group><article-title>Transcriptional pulsing of a developmental gene</article-title><source>Curr Biol.</source><year>2006</year><volume>16</volume><fpage>1018</fpage><lpage>25</lpage><pub-id pub-id-type="doi">10.1016/j.cub.2006.03.092</pub-id><?supplied-pmid 16713960?><pub-id pub-id-type="pmid">16713960</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Raj</surname><given-names>A</given-names></name><name><surname>van Oudenaarden</surname><given-names>A</given-names></name></person-group><article-title>Nature, nurture, or chance: stochastic gene expression and its consequences</article-title><source>Cell.</source><year>2008</year><volume>135</volume><fpage>216</fpage><lpage>26</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2008.09.050</pub-id><?supplied-pmid 18957198?><pub-id pub-id-type="pmid">18957198</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chong</surname><given-names>S</given-names></name><name><surname>Chen</surname><given-names>C</given-names></name><name><surname>Ge</surname><given-names>H</given-names></name><name><surname>Xie</surname><given-names>XS</given-names></name></person-group><article-title>Mechanism of transcriptional bursting in bacteria</article-title><source>Cell.</source><year>2014</year><volume>158</volume><fpage>314</fpage><lpage>26</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2014.05.038</pub-id><?supplied-pmid 25036631?><pub-id pub-id-type="pmid">25036631</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Blake</surname><given-names>WJ</given-names></name><name><surname>Balazsi</surname><given-names>G</given-names></name><name><surname>Kohanski</surname><given-names>MA</given-names></name><name><surname>Isaacs</surname><given-names>FJ</given-names></name><name><surname>Murphy</surname><given-names>KF</given-names></name><name><surname>Kuang</surname><given-names>Y</given-names></name><name><surname>Cantor</surname><given-names>CR</given-names></name><name><surname>Walt</surname><given-names>DR</given-names></name><name><surname>Collins</surname><given-names>JJ</given-names></name></person-group><article-title>Phenotypic consequences of promoter-mediated transcriptional noise</article-title><source>Mol Cell.</source><year>2006</year><volume>24</volume><fpage>853</fpage><lpage>65</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2006.11.003</pub-id><?supplied-pmid 17189188?><pub-id pub-id-type="pmid">17189188</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fukaya</surname><given-names>T</given-names></name><name><surname>Lim</surname><given-names>B</given-names></name><name><surname>Levine</surname><given-names>M</given-names></name></person-group><article-title>Enhancer control of transcriptional bursting</article-title><source>Cell.</source><year>2016</year><volume>166</volume><fpage>358</fpage><lpage>68</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2016.05.025</pub-id><?supplied-pmid 27293191?><pub-id pub-id-type="pmid">27293191</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Raj</surname><given-names>A</given-names></name><name><surname>Peskin</surname><given-names>CS</given-names></name><name><surname>Tranchina</surname><given-names>D</given-names></name><name><surname>Vargas</surname><given-names>DY</given-names></name><name><surname>Tyagi</surname><given-names>S</given-names></name></person-group><article-title>Stochastic mRNA synthesis in mammalian cells</article-title><source>PLoS Biol.</source><year>2006</year><volume>4</volume><pub-id pub-id-type="doi">10.1371/journal.pbio.0040309</pub-id><?supplied-pmid 17048983?><pub-id pub-id-type="pmid">17048983</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Suter</surname><given-names>DM</given-names></name><name><surname>Molina</surname><given-names>N</given-names></name><name><surname>Gatfield</surname><given-names>D</given-names></name><name><surname>Schneider</surname><given-names>K</given-names></name><name><surname>Schibler</surname><given-names>U</given-names></name><name><surname>Naef</surname><given-names>F</given-names></name></person-group><article-title>Mammalian genes are transcribed with widely different bursting kinetics</article-title><source>Science.</source><year>2011</year><volume>332</volume><fpage>472</fpage><lpage>4</lpage><pub-id pub-id-type="doi">10.1126/science.1198817</pub-id><?supplied-pmid 21415320?><pub-id pub-id-type="pmid">21415320</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>JK</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name></person-group><article-title>Inferring the kinetics of stochastic gene expression from single-cell RNA-sequencing data</article-title><source>Genome Biol.</source><year>2013</year><volume>14</volume><fpage>R7</fpage><pub-id pub-id-type="doi">10.1186/gb-2013-14-1-r7</pub-id><?supplied-pmid 23360624?><pub-id pub-id-type="pmid">23360624</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brennecke</surname><given-names>P</given-names></name><name><surname>Anders</surname><given-names>S</given-names></name><name><surname>Kim</surname><given-names>JK</given-names></name><name><surname>Kolodziejczyk</surname><given-names>AA</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>Proserpio</surname><given-names>V</given-names></name><name><surname>Baying</surname><given-names>B</given-names></name><name><surname>Benes</surname><given-names>V</given-names></name><name><surname>Teichmann</surname><given-names>SA</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name><name><surname>Heisler</surname><given-names>MG</given-names></name></person-group><article-title>Accounting for technical noise in single-cell RNA-seq experiments</article-title><source>Nat Methods.</source><year>2013</year><volume>10</volume><fpage>1093</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2645</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pierson</surname><given-names>E</given-names></name><name><surname>Yau</surname><given-names>C</given-names></name></person-group><article-title>ZIFA: Dimensionality reduction for zero-inflated single-cell gene expression analysis</article-title><source>Genome Biol.</source><year>2015</year><volume>16</volume><fpage>241</fpage><pub-id pub-id-type="doi">10.1186/s13059-015-0805-z</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vallejos</surname><given-names>CA</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name><name><surname>Richardson</surname><given-names>S</given-names></name></person-group><article-title>BASiCS: Bayesian analysis of single-cell sequencing data</article-title><source>PLoS Comput Biol.</source><year>2015</year><volume>11</volume><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004333</pub-id><?supplied-pmid 26107944?><pub-id pub-id-type="pmid">26107944</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ding</surname><given-names>B</given-names></name><name><surname>Zheng</surname><given-names>L</given-names></name><name><surname>Zhu</surname><given-names>Y</given-names></name><name><surname>Li</surname><given-names>N</given-names></name><name><surname>Jia</surname><given-names>H</given-names></name><name><surname>Ai</surname><given-names>R</given-names></name><name><surname>Wildberg</surname><given-names>A</given-names></name><name><surname>Wang</surname><given-names>W</given-names></name></person-group><article-title>Normalization and noise reduction for single cell RNA-seq experiments</article-title><source>Bioinformatics.</source><year>2015</year><volume>31</volume><fpage>2225</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btv122</pub-id><?supplied-pmid 25717193?><pub-id pub-id-type="pmid">25717193</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Qiu</surname><given-names>X</given-names></name><name><surname>Hill</surname><given-names>A</given-names></name><name><surname>Packer</surname><given-names>J</given-names></name><name><surname>Lin</surname><given-names>D</given-names></name><name><surname>Ma</surname><given-names>YA</given-names></name><name><surname>Trapnell</surname><given-names>C</given-names></name></person-group><article-title>Single-cell mRNA quantification and differential analysis with Census</article-title><source>Nat Methods</source><year>2017</year><volume>14</volume><issue>3</issue><fpage>309</fpage><lpage>15</lpage><pub-id pub-id-type="doi">10.1038/nmeth.4150</pub-id><?supplied-pmid 28114287?><pub-id pub-id-type="pmid">28114287</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>JK</given-names></name><name><surname>Kolodziejczyk</surname><given-names>AA</given-names></name><name><surname>Illicic</surname><given-names>T</given-names></name><name><surname>Teichmann</surname><given-names>SA</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name></person-group><article-title>Characterizing noise structure in single-cell RNA-seq distinguishes genuine from technical stochastic allelic expression</article-title><source>Nat Commun.</source><year>2015</year><volume>6</volume><fpage>8687</fpage><pub-id pub-id-type="doi">10.1038/ncomms9687</pub-id><?supplied-pmid 26489834?><pub-id pub-id-type="pmid">26489834</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Levesque</surname><given-names>MJ</given-names></name><name><surname>Raj</surname><given-names>A</given-names></name></person-group><article-title>Single-chromosome transcriptional profiling reveals chromosomal gene expression regulation</article-title><source>Nat Methods.</source><year>2013</year><volume>10</volume><fpage>246</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2372</pub-id><?supplied-pmid 23416756?><pub-id pub-id-type="pmid">23416756</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kepler</surname><given-names>TB</given-names></name><name><surname>Elston</surname><given-names>TC</given-names></name></person-group><article-title>Stochasticity in transcriptional regulation: origins, consequences, and mathematical representations</article-title><source>Biophys J.</source><year>2001</year><volume>81</volume><fpage>3116</fpage><lpage>36</lpage><pub-id pub-id-type="doi">10.1016/S0006-3495(01)75949-8</pub-id><?supplied-pmid 11720979?><pub-id pub-id-type="pmid">11720979</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bix</surname><given-names>M</given-names></name><name><surname>Locksley</surname><given-names>RM</given-names></name></person-group><article-title>Independent and epigenetic regulation of the interleukin-4 alleles in CD4+ T cells</article-title><source>Science.</source><year>1998</year><volume>281</volume><fpage>1352</fpage><lpage>4</lpage><pub-id pub-id-type="doi">10.1126/science.281.5381.1352</pub-id><?supplied-pmid 9721100?><pub-id pub-id-type="pmid">9721100</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Levesque</surname><given-names>MJ</given-names></name><name><surname>Ginart</surname><given-names>P</given-names></name><name><surname>Wei</surname><given-names>Y</given-names></name><name><surname>Raj</surname><given-names>A</given-names></name></person-group><article-title>Visualizing SNVs to quantify allele-specific expression in single cells</article-title><source>Nat Methods.</source><year>2013</year><volume>10</volume><fpage>865</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2589</pub-id><?supplied-pmid 23913259?><pub-id pub-id-type="pmid">23913259</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Padovan-Merhar</surname><given-names>O</given-names></name><name><surname>Nair</surname><given-names>GP</given-names></name><name><surname>Biaesch</surname><given-names>AG</given-names></name><name><surname>Mayer</surname><given-names>A</given-names></name><name><surname>Scarfone</surname><given-names>S</given-names></name><name><surname>Foley</surname><given-names>SW</given-names></name><name><surname>Wu</surname><given-names>AR</given-names></name><name><surname>Churchman</surname><given-names>LS</given-names></name><name><surname>Singh</surname><given-names>A</given-names></name><name><surname>Raj</surname><given-names>A</given-names></name></person-group><article-title>Single mammalian cells compensate for differences in cellular volume and DNA copy number through independent global transcriptional mechanisms</article-title><source>Mol Cell.</source><year>2015</year><volume>58</volume><fpage>339</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2015.03.005</pub-id><?supplied-pmid 25866248?><pub-id pub-id-type="pmid">25866248</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ramskold</surname><given-names>D</given-names></name><name><surname>Luo</surname><given-names>S</given-names></name><name><surname>Wang</surname><given-names>YC</given-names></name><name><surname>Li</surname><given-names>R</given-names></name><name><surname>Deng</surname><given-names>Q</given-names></name><name><surname>Faridani</surname><given-names>OR</given-names></name><name><surname>Daniels</surname><given-names>GA</given-names></name><name><surname>Khrebtukova</surname><given-names>I</given-names></name><name><surname>Loring</surname><given-names>JF</given-names></name><name><surname>Laurent</surname><given-names>LC</given-names></name><etal/></person-group><article-title>Full-length mRNA-Seq from single-cell levels of RNA and individual circulating tumor cells</article-title><source>Nat Biotechnol.</source><year>2012</year><volume>30</volume><fpage>777</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1038/nbt.2282</pub-id><?supplied-pmid 22820318?><pub-id pub-id-type="pmid">22820318</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dadiani</surname><given-names>M</given-names></name><name><surname>van Dijk</surname><given-names>D</given-names></name><name><surname>Segal</surname><given-names>B</given-names></name><name><surname>Field</surname><given-names>Y</given-names></name><name><surname>Ben-Artzi</surname><given-names>G</given-names></name><name><surname>Raveh-Sadka</surname><given-names>T</given-names></name><name><surname>Levo</surname><given-names>M</given-names></name><name><surname>Kaplow</surname><given-names>I</given-names></name><name><surname>Weinberger</surname><given-names>A</given-names></name><name><surname>Segal</surname><given-names>E</given-names></name></person-group><article-title>Two DNA-encoded strategies for increasing expression with opposing effects on promoter dynamics and transcriptional noise</article-title><source>Genome Res.</source><year>2013</year><volume>23</volume><fpage>966</fpage><lpage>76</lpage><pub-id pub-id-type="doi">10.1101/gr.149096.112</pub-id><?supplied-pmid 23403035?><pub-id pub-id-type="pmid">23403035</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bartman</surname><given-names>CR</given-names></name><name><surname>Hsu</surname><given-names>SC</given-names></name><name><surname>Hsiung</surname><given-names>CC</given-names></name><name><surname>Raj</surname><given-names>A</given-names></name><name><surname>Blobel</surname><given-names>GA</given-names></name></person-group><article-title>Enhancer regulation of transcriptional bursting parameters revealed by forced chromatin looping</article-title><source>Mol Cell.</source><year>2016</year><volume>62</volume><fpage>237</fpage><lpage>47</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2016.03.007</pub-id><?supplied-pmid 27067601?><pub-id pub-id-type="pmid">27067601</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sepulveda</surname><given-names>LA</given-names></name><name><surname>Xu</surname><given-names>H</given-names></name><name><surname>Zhang</surname><given-names>J</given-names></name><name><surname>Wang</surname><given-names>M</given-names></name><name><surname>Golding</surname><given-names>I</given-names></name></person-group><article-title>Measurement of gene regulation in individual cells reveals rapid switching between promoter states</article-title><source>Science.</source><year>2016</year><volume>351</volume><fpage>1218</fpage><lpage>22</lpage><pub-id pub-id-type="doi">10.1126/science.aad0635</pub-id><?supplied-pmid 26965629?><pub-id pub-id-type="pmid">26965629</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Skinner</surname><given-names>SO</given-names></name><name><surname>Xu</surname><given-names>H</given-names></name><name><surname>Nagarkar-Jaiswal</surname><given-names>S</given-names></name><name><surname>Freire</surname><given-names>PR</given-names></name><name><surname>Zwaka</surname><given-names>TP</given-names></name><name><surname>Golding</surname><given-names>I</given-names></name></person-group><article-title>Single-cell analysis of transcription kinetics across the cell cycle</article-title><source>Elife.</source><year>2016</year><volume>5</volume><pub-id pub-id-type="doi">10.7554/eLife.12175</pub-id><?supplied-pmid 26824388?><pub-id pub-id-type="pmid">26824388</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ochiai</surname><given-names>H</given-names></name><name><surname>Sugawara</surname><given-names>T</given-names></name><name><surname>Sakuma</surname><given-names>T</given-names></name><name><surname>Yamamoto</surname><given-names>T</given-names></name></person-group><article-title>Stochastic promoter activation affects Nanog expression variability in mouse embryonic stem cells</article-title><source>Sci Rep.</source><year>2014</year><volume>4</volume><fpage>7125</fpage><pub-id pub-id-type="doi">10.1038/srep07125</pub-id><?supplied-pmid 25410303?><pub-id pub-id-type="pmid">25410303</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>H</given-names></name><name><surname>Sepulveda</surname><given-names>LA</given-names></name><name><surname>Figard</surname><given-names>L</given-names></name><name><surname>Sokac</surname><given-names>AM</given-names></name><name><surname>Golding</surname><given-names>I</given-names></name></person-group><article-title>Combining protein and mRNA quantification to decipher transcriptional regulation</article-title><source>Nat Methods.</source><year>2015</year><volume>12</volume><fpage>739</fpage><lpage>42</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3446</pub-id><?supplied-pmid 26098021?><pub-id pub-id-type="pmid">26098021</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Handsaker</surname><given-names>B</given-names></name><name><surname>Wysoker</surname><given-names>A</given-names></name><name><surname>Fennell</surname><given-names>T</given-names></name><name><surname>Ruan</surname><given-names>J</given-names></name><name><surname>Homer</surname><given-names>N</given-names></name><name><surname>Marth</surname><given-names>G</given-names></name><name><surname>Abecasis</surname><given-names>G</given-names></name><name><surname>Durbin</surname><given-names>R</given-names></name><collab>Genome Project Data Processing S</collab></person-group><article-title>The Sequence Alignment/Map format and SAMtools</article-title><source>Bioinformatics</source><year>2009</year><volume>25</volume><fpage>2078</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btp352</pub-id><?supplied-pmid 19505943?><pub-id pub-id-type="pmid">19505943</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Munsky</surname><given-names>B</given-names></name><name><surname>Neuert</surname><given-names>G</given-names></name><name><surname>van Oudenaarden</surname><given-names>A</given-names></name></person-group><article-title>Using gene expression noise to understand gene regulation</article-title><source>Science.</source><year>2012</year><volume>336</volume><fpage>183</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1126/science.1216379</pub-id><?supplied-pmid 22499939?><pub-id pub-id-type="pmid">22499939</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edsgard</surname><given-names>D</given-names></name><name><surname>Reinius</surname><given-names>B</given-names></name><name><surname>Sandberg</surname><given-names>R</given-names></name></person-group><article-title>scphaser: haplotype inference using single-cell RNA-seq data</article-title><source>Bioinformatics</source><year>2016</year><volume>32</volume><fpage>3038</fpage><lpage>40</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btw484</pub-id><?supplied-pmid 27497440?><pub-id pub-id-type="pmid">27497440</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Valencia-Sanchez</surname><given-names>MA</given-names></name><name><surname>Liu</surname><given-names>J</given-names></name><name><surname>Hannon</surname><given-names>GJ</given-names></name><name><surname>Parker</surname><given-names>R</given-names></name></person-group><article-title>Control of translation and mRNA degradation by miRNAs and siRNAs</article-title><source>Genes Dev.</source><year>2006</year><volume>20</volume><fpage>515</fpage><lpage>24</lpage><pub-id pub-id-type="doi">10.1101/gad.1399806</pub-id><?supplied-pmid 16510870?><pub-id pub-id-type="pmid">16510870</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wills</surname><given-names>QF</given-names></name><name><surname>Livak</surname><given-names>KJ</given-names></name><name><surname>Tipping</surname><given-names>AJ</given-names></name><name><surname>Enver</surname><given-names>T</given-names></name><name><surname>Goldson</surname><given-names>AJ</given-names></name><name><surname>Sexton</surname><given-names>DW</given-names></name><name><surname>Holmes</surname><given-names>C</given-names></name></person-group><article-title>Single-cell gene expression analysis reveals genetic associations masked in whole-tissue experiments</article-title><source>Nat Biotechnol.</source><year>2013</year><volume>31</volume><fpage>748</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1038/nbt.2642</pub-id><?supplied-pmid 23873083?><pub-id pub-id-type="pmid">23873083</pub-id></element-citation></ref></ref-list></back></article>