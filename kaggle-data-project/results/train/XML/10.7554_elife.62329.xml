<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?subarticle sa1?><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-archivearticle1.dtd?><?SourceDTD.Version 1.1?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">eLife</journal-id><journal-id journal-id-type="iso-abbrev">Elife</journal-id><journal-id journal-id-type="publisher-id">eLife</journal-id><journal-title-group><journal-title>eLife</journal-title></journal-title-group><issn pub-type="epub">2050-084X</issn><publisher><publisher-name>eLife Sciences Publications, Ltd</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7946424</article-id><article-id pub-id-type="pmid">33594974</article-id><article-id pub-id-type="publisher-id">62329</article-id><article-id pub-id-type="doi">10.7554/eLife.62329</article-id><article-categories><subj-group subj-group-type="display-channel"><subject>Tools and Resources</subject></subj-group><subj-group subj-group-type="heading"><subject>Neuroscience</subject></subj-group></article-categories><title-group><article-title>Exposing distinct subcortical components of the auditory brainstem response evoked by continuous naturalistic speech</article-title></title-group><contrib-group><contrib id="author-178906" contrib-type="author"><name><surname>Polonenko</surname><given-names>Melissa J</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0003-1914-6117</contrib-id><xref ref-type="aff" rid="aff1">1</xref><xref ref-type="aff" rid="aff2">2</xref><xref ref-type="aff" rid="aff3">3</xref><xref ref-type="fn" rid="con1"/><xref ref-type="fn" rid="conf1"/></contrib><contrib id="author-19568" contrib-type="author" corresp="yes"><name><surname>Maddox</surname><given-names>Ross K</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0003-2668-0238</contrib-id><email><EMAIL></email><xref ref-type="aff" rid="aff1">1</xref><xref ref-type="aff" rid="aff2">2</xref><xref ref-type="aff" rid="aff3">3</xref><xref ref-type="aff" rid="aff4">4</xref><xref ref-type="other" rid="fund1"/><xref ref-type="fn" rid="con2"/><xref ref-type="fn" rid="conf1"/></contrib><aff id="aff1"><label>1</label><institution>Department of Neuroscience, University of Rochester</institution><addr-line>Rochester</addr-line><country>United States</country></aff><aff id="aff2"><label>2</label><institution>Del Monte Institute for Neuroscience, University of Rochester</institution><addr-line>Rochester</addr-line><country>United States</country></aff><aff id="aff3"><label>3</label><institution>Center for Visual Science, University of Rochester</institution><addr-line>Rochester</addr-line><country>United States</country></aff><aff id="aff4"><label>4</label><institution>Department of Biomedical Engineering, University of Rochester</institution><addr-line>Rochester</addr-line><country>United States</country></aff></contrib-group><contrib-group><contrib contrib-type="editor"><name><surname>Reichenbach</surname><given-names>Tobias</given-names></name><role>Reviewing Editor</role><aff><institution>Imperial College London</institution><country>United Kingdom</country></aff></contrib><contrib contrib-type="editor"><name><surname>King</surname><given-names>Andrew J</given-names></name><role>Senior Editor</role><aff><institution>University of Oxford</institution><country>United Kingdom</country></aff></contrib></contrib-group><pub-date date-type="pub" publication-format="electronic"><day>17</day><month>2</month><year>2021</year></pub-date><pub-date pub-type="collection"><year>2021</year></pub-date><volume>10</volume><elocation-id>e62329</elocation-id><history><date date-type="received" iso-8601-date="2020-08-21"><day>21</day><month>8</month><year>2020</year></date><date date-type="accepted" iso-8601-date="2021-02-16"><day>16</day><month>2</month><year>2021</year></date></history><permissions><copyright-statement>&#x000a9; 2021, Polonenko and Maddox</copyright-statement><copyright-year>2021</copyright-year><copyright-holder>Polonenko and Maddox</copyright-holder><ali:free_to_read xmlns:ali="http://www.niso.org/schemas/ali/1.0/"/><license xlink:href="http://creativecommons.org/licenses/by/4.0/"><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/">http://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This article is distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use and redistribution provided that the original author and source are credited.</license-p></license></permissions><self-uri content-type="pdf" xlink:href="elife-62329.pdf"/><abstract><p>Speech processing is built upon encoding by the auditory nerve and brainstem, yet we know very little about how these processes unfold in specific subcortical structures. These structures are deep and respond quickly, making them difficult to study during ongoing speech. Recent techniques have begun to address this problem, but yield temporally broad responses with consequently ambiguous neural origins. Here, we describe a method that pairs re-synthesized &#x02018;peaky&#x02019; speech with deconvolution analysis of electroencephalography recordings. We show that in adults with normal hearing the method quickly yields robust responses whose component waves reflect activity from distinct subcortical structures spanning auditory nerve to rostral brainstem. We further demonstrate the versatility of peaky speech by simultaneously measuring bilateral and ear-specific responses across different frequency bands and discuss the important practical considerations such as talker choice. The peaky speech method holds promise as a tool for investigating speech encoding and processing, and for clinical applications.</p></abstract><kwd-group kwd-group-type="author-keywords"><kwd>speech</kwd><kwd>auditory brainstem response</kwd><kwd>evoked potentials</kwd><kwd>electroencephalography</kwd><kwd>assessment</kwd><kwd>EEG</kwd></kwd-group><kwd-group kwd-group-type="research-organism"><title>Research organism</title><kwd>Human</kwd></kwd-group><funding-group><award-group id="fund1"><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000055</institution-id><institution>National Institute on Deafness and Other Communication Disorders</institution></institution-wrap></funding-source><award-id>R00DC014288</award-id><principal-award-recipient><name><surname>Maddox</surname><given-names>Ross K</given-names></name></principal-award-recipient></award-group><funding-statement>The funders had no role in study design, data collection and interpretation, or the decision to submit the work for publication.</funding-statement></funding-group><custom-meta-group><custom-meta specific-use="meta-only"><meta-name>Author impact statement</meta-name><meta-value>The response from discrete stages of the early auditory pathway can be measured by subtle manipulations to long-form natural speech stimuli paired with deconvolution analysis of electroencephalography data.</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec sec-type="intro" id="s1"><title>Introduction</title><p>Understanding speech is an important, complex process that spans the auditory system from cochlea to cortex. A temporally precise network transforms the strikingly dynamic fluctuations in amplitude and spectral content of natural, ongoing speech into meaningful information and modifies that information based on attention or other priors (<xref rid="bib41" ref-type="bibr">Mesgarani et al., 2009</xref>). Subcortical structures play a critical role in this process &#x02013; they do not merely relay information from the periphery to the cortex, but also perform important functions for speech understanding, such as localizing sound (e.g., <xref rid="bib30" ref-type="bibr">Grothe and Pecka, 2014</xref>) and encoding vowels across different levels and in background noise (e.g., <xref rid="bib12" ref-type="bibr">Carney et al., 2015</xref>). Furthermore, subcortical structures receive descending information from the cortex through corticofugal pathways (<xref rid="bib3" ref-type="bibr">Bajo et al., 2010</xref>; <xref rid="bib4" ref-type="bibr">Bajo and King, 2012</xref>; <xref rid="bib63" ref-type="bibr">Winer, 2005</xref>), suggesting that&#x000a0;they may also play an important role in modulating speech and auditory streaming. Given the complexity of speech processing, it is important to parse and understand contributions from different neural generators. However, these subcortical structures are deep and respond to stimuli with very short latencies, making them difficult to study during ecologically&#x000a0;salient stimuli such as continuous and naturalistic speech. We created a novel paradigm aimed at elucidating the contributions from distinct subcortical structures to ongoing, naturalistic speech.</p><p>Activity in deep brainstem structures can be &#x02018;imaged&#x02019; by the latency of waves in a surface electrical potential (electroencephalography [EEG]) called the auditory brainstem response (ABR). The ABR&#x02019;s component waves have been attributed to activity in different subcortical structures with characteristic latencies: the auditory nerve contributes to waves I and II (~1.5&#x02013;3 ms), the cochlear nucleus to wave III (~4 ms), the superior olivary complex and lateral lemniscus to wave IV (~5 ms), and the lateral lemniscus and inferior colliculus to wave V (~6 ms) (<xref rid="bib42" ref-type="bibr">M&#x000f8;ller and Jannetta, 1983</xref>; review by <xref rid="bib43" ref-type="bibr">Moore, 1987</xref>; <xref rid="bib59" ref-type="bibr">Starr and Hamilton, 1976</xref>). Waves I, III, and V are most often easily distinguished in the human response. Subcortical structures may also contribute to the earlier P<sub>0&#x000a0;</sub>(12&#x02013;14 ms) and N<sub>a&#x000a0;</sub>(15&#x02013;25 ms) waves (<xref rid="bib32" ref-type="bibr">Hashimoto, 1982</xref>; <xref rid="bib35" ref-type="bibr">Kileny et al., 1987</xref>; <xref rid="bib47" ref-type="bibr">Picton et al., 1974</xref>) of the middle latency response (MLR), which are then followed by thalamo-cortically generated waves P<sub>a</sub>, N<sub>b</sub>, and P<sub>b</sub>/P<sub>1</sub> (<xref rid="bib22" ref-type="bibr">Geisler et al., 1958</xref>; <xref rid="bib23" ref-type="bibr">Goldstein and Rodman, 1967</xref>). ABR and MLR waves have a low signal-to-noise ratio (SNR) and require numerous stimulus repetitions to record a good response. Furthermore, they are quick and often occur before the stimulus has ended. Therefore, out of necessity, most human brainstem studies have focused on brief stimuli such as clicks, tone pips, or speech syllables rather than more natural speech.</p><p>Recent analytical techniques have overcome limitations on stimuli, allowing continuous naturally uttered speech to be used. One such technique extracts the fundamental waveform from the speech stimulus and finds the envelope of the cross-correlation between that waveform and the recorded EEG data (<xref rid="bib21" ref-type="bibr">Forte et al., 2017</xref>). The response has an average peak time of about 9 ms, with contributions primarily from the inferior colliculus (<xref rid="bib55" ref-type="bibr">Saiz-Al&#x000ed;a and Reichenbach, 2020</xref>). A second technique considers the rectified broadband speech waveform as the input to a linear system and the EEG data as the output, and uses deconvolution to compute the ABR waveform as the impulse response of the system (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>). The speech-derived ABR shows a wave V peak whose latency is highly correlated with the click response wave V across subjects, demonstrating that the component is generated in the rostral brainstem. A third technique averages responses to each chirp (click-like transients that quickly increase in frequency) in re-synthesized &#x02018;cheech&#x02019; stimuli (CHirp spEECH; <xref rid="bib2" ref-type="bibr">Backer et al., 2019</xref>) that interleaves alternating octave frequency bands of speech and chirps aligned with some glottal pulses. Brainstem responses to these stimuli also show a wave V, but do not show earlier waves (<xref rid="bib2" ref-type="bibr">Backer et al., 2019</xref>). While these methods reflect subcortical activity, the first two provide temporally broad responses with a lack of specificity regarding underlying neural sources. None of the three methods shows the earlier canonical components such as waves I and III that would allow rostral brainstem activity to be distinguished from, for example, the auditory nerve. Such activity is important to assess, especially given the current interest in the potential contributions of auditory nerve loss in disordered processing of speech in noise (<xref rid="bib9" ref-type="bibr">Bramhall et al., 2019</xref>; <xref rid="bib38" ref-type="bibr">Liberman et al., 2016</xref>; <xref rid="bib50" ref-type="bibr">Prendergast et al., 2017</xref>).</p><p>Although click responses can assess sound encoding (of clicks) at early stages of the auditory system, a speech-evoked response with the same components would assess subcortical structure-specific encoding within the acoustical context of the dynamic spectrotemporal characteristics of speech &#x02013; information that is not possible to obtain from click responses. Furthermore, changes to the amplitudes and latencies of these early components could inform our understanding of speech processing if deployed in experiments that compare conditions requiring different states of processing, such as attended/unattended speech or understood/foreign language. For example, if a wave I from the auditory nerve differed between speech stimuli that were attended versus unattended, then this would add to our current understanding of the brainstem&#x02019;s role in speech processing. Therefore, a click-like response that is evoked by speech stimuli facilitates new investigations into speech encoding and processing.</p><p>Thus, we asked if we could further assess underlying speech encoding and processing in multiple distinct early stages of the auditory system by (1) evoking additional waves than wave V of the canonical ABR and (2) measuring responses to different frequency ranges of speech (corresponding to different places of origin on the cochlea). The ABR is strongest to very short stimuli such as clicks, so we created &#x02018;peaky&#x02019; speech. The design goal of peaky speech is to re-synthesize natural speech so that its defining spectrotemporal content is unaltered &#x02013; maintaining the speech as intelligible and identifiable &#x02013; but its pressure waveform consists of maximally sharp peaks so that it drives the ABR as effectively as possible (giving a very slight &#x02018;buzzy&#x02019; quality when listening under good headphones;&#x000a0;<xref ref-type="supplementary-material" rid="supp2">Audio files 1</xref>&#x02013;<xref ref-type="supplementary-material" rid="supp7">6</xref>). The results show that peaky speech evokes canonical brainstem responses and frequency-specific responses, paving the way for novel studies of subcortical contributions to speech processing.</p></sec><sec sec-type="results" id="s2"><title>Results</title><sec id="s2-1"><title>Broadband peaky speech yields more robust responses than unaltered speech</title><sec id="s2-1-1"><title>Broadband peaky speech elicits canonical brainstem responses</title><p>We re-synthesized speech to be &#x02018;peaky&#x02019; with the primary aim to evoke additional, earlier waves of the ABR that identify different neural generators. Indeed, <xref ref-type="fig" rid="fig1">Figure 1</xref> shows that waves I, III, and V of the canonical ABR are clearly visible in the group average and the individual responses to broadband peaky speech, which were filtered at a typical high-pass cutoff of 150 Hz to highlight the earlier ABR waves. This means that broadband peaky speech, unlike the unaltered speech, can be used to assess naturalistic speech processing at discrete parts of the subcortical auditory system, from the auditory nerve to rostral brainstem.</p><fig id="fig1" orientation="portrait" position="float"><label>Figure 1.</label><caption><title>Single-subject and group average (bottom right) weighted-average auditory brainstem responses (ABRs) to&#x000a0;~43 min of broadband peaky speech.</title><p>Areas for the group average show&#x000a0;&#x000b1;1 SEM. Responses were high-pass filtered at 150 Hz using a first-order Butterworth filter. Waves I, III, and V of the canonical ABR are evident in most of the single-subject responses (N&#x000a0;=&#x000a0;22, 16, and&#x000a0;22, respectively) and are marked by the average peak latencies on the average response.</p></caption><graphic xlink:href="elife-62329-fig1"/></fig><p>Waves I and V were identifiable in responses from all subjects (<italic>N</italic>&#x000a0;=&#x000a0;22), and wave III was identifiable in 19 of the 22 subjects. The numbers of subjects with identifiable waves I and III in these peaky speech responses were similar to the 24 and 16 out of 24 subjects for the click-evoked responses in <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>. These waves are marked on the individual responses in <xref ref-type="fig" rid="fig1">Figure 1</xref>. Mean &#x000b1; SEM peak latencies for ABR waves I, III, and V were 3.23&#x000a0;&#x000b1;&#x000a0;0.09 ms, 5.51&#x000a0;&#x000b1;&#x000a0;0.07 ms, and 7.22&#x000a0;&#x000b1;&#x000a0;0.07 ms, respectively. These mean peak latencies are shown superimposed on the group average response in <xref ref-type="fig" rid="fig1">Figure 1</xref> (bottom right). Inter-wave latencies were 2.24&#x000a0;&#x000b1;&#x000a0;0.06 ms (<italic>N</italic>&#x000a0;=&#x000a0;19) for I&#x02013;III, 1.68&#x000a0;&#x000b1;&#x000a0;0.05 ms (<italic>N</italic>&#x000a0;=&#x000a0;19) for III&#x02013;V, and 4.00&#x000a0;&#x000b1;&#x000a0;0.08 (<italic>N</italic>&#x000a0;=&#x000a0;22) for I&#x02013;V. These peak inter-wave latencies fall within a range expected for brainstem responses, but the absolute peak latencies were later than those reported for a click ABR at a level of 60 dB sensation level (SL) and rate between 50 and 100 Hz (<xref rid="bib11" ref-type="bibr">Burkard and Hecox, 1983</xref>; <xref rid="bib13" ref-type="bibr">Chiappa et al., 1979</xref>; <xref rid="bib15" ref-type="bibr">Don et al., 1977</xref>).</p></sec><sec id="s2-1-2"><title>More components of the ABR and MLR are present with broadband peaky than unaltered speech</title><p>Having established that broadband peaky speech evokes robust canonical ABRs, we next compared both ABR and MLR responses to those evoked by unaltered speech. To simultaneously evaluate ABR and MLR components, a high-pass filter with a 30-Hz cutoff was used (unlike the broadband peaky response, the 150&#x000a0;Hz high-pass cutoff does not reveal earlier components in the response to unaltered speech). <xref ref-type="fig" rid="fig2">Figure 2A</xref> shows that overall there were morphological similarities between responses to both types of speech; however, there were more early and late component waves to broadband peaky speech. More specifically, whereas both types of speech evoked waves V, N<sub>a</sub>,&#x000a0;and P<sub>a</sub>, broadband peaky speech also evoked waves I, often III (14&#x02013;19 of 22 subjects depending on&#x000a0;whether a 30 or 150&#x000a0;Hz high-pass filter cutoff was used), and P<sub>0</sub>. With a lower cutoff for the high-pass filter, wave III rode on the slope of wave V and was less identifiable in the grand average shown in <xref ref-type="fig" rid="fig2">Figure 2A</xref> than that shown with a higher cutoff in <xref ref-type="fig" rid="fig1">Figure 1</xref>. Wave V was more robust and sharper to broadband peaky speech but peaked slightly later than the broader wave V to unaltered speech. For reasons unknown to us, the half-rectified speech method missed the MLR wave P<sub>0</sub>, and consequently had a broader and earlier N<sub>a</sub> than the broadband peaky speech method, though this missing P<sub>0</sub> was consistent with the results of <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>. These waveforms indicate that broadband peaky speech is better than unaltered speech at evoking canonical responses that distinguish activity from distinct subcortical and cortical neural generators.</p><fig id="fig2" orientation="portrait" position="float"><label>Figure 2.</label><caption><title>Comparison of auditory brainstem response&#x000a0;(ABR) and middle latency response (MLR) to ~43 min each of unaltered speech and broadband peaky speech.</title><p>(<bold>A</bold>) The average waveform to broadband peaky speech (blue) shows additional, and sharper, waves of the canonical ABR and MLR than the broader average waveform to unaltered speech (black). Responses were high-pass filtered at 30 Hz with a first-order Butterworth filter. Areas show&#x000a0;&#x000b1;1 SEM. (<bold>B</bold>) Comparison of peak latencies for ABR wave V (circles) and MLR waves N<sub>a</sub> (downward triangles) and P<sub>a</sub> (upward triangles) that were common between responses to broadband peaky and unaltered speech. Blue symbols depict individual subjects, and black symbols depict the mean.</p></caption><graphic xlink:href="elife-62329-fig2"/></fig><p>Peak latencies for the waves common to both types of speech are shown in <xref ref-type="fig" rid="fig2">Figure 2B</xref>. As suggested by the waveforms in <xref ref-type="fig" rid="fig2">Figure 2A</xref>, mean &#x000b1; SEM peak latencies for waves V, N<sub>a</sub>, and P<sub>a</sub> were longer for broadband peaky than unaltered speech by 0.87&#x000a0;&#x000b1;&#x000a0;0.06 ms (independent <italic>t</italic>-test, <italic>t</italic><sub>(21)</sub> = 14.7, p&#x0003c;0.01, <italic>d</italic>&#x000a0;=&#x000a0;3.22), 6.92&#x000a0;&#x000b1;&#x000a0;0.44 ms (<italic>t</italic><sub>(21)</sub> = 15.4, p&#x0003c;0.01, <italic>d</italic>&#x000a0;=&#x000a0;3.44), and 1.07&#x000a0;&#x000b1;&#x000a0;0.45 ms (<italic>t</italic><sub>(20)</sub> = 2.3, p=0.032, <italic>d</italic>&#x000a0;=&#x000a0;0.52), respectively.</p><p>The response to broadband peaky speech showed a small but consistent response at negative and early positive lags (i.e., pre-stimulus) when using the pulse train as a regressor in the deconvolution, particularly when using the lower high-pass filter cutoff of 30 Hz (<xref ref-type="fig" rid="fig2">Figure 2A</xref>) compared to 150 Hz (<xref ref-type="fig" rid="fig1">Figure 1</xref>). For a perfectly linear causal system, this would not be expected. To better understand the source of this pre-stimulus component &#x02013; and to determine whether later components were influencing the earliest components &#x02013; we performed two simulations. (1)&#x000a0;A simple linear deconvolution model in which EEG for each 64 s epoch was simulated by convolving the rectified broadband peaky speech audio with an ABR kernel that did not show the pre-stimulus component (<xref ref-type="fig" rid="fig3s1">Figure 3&#x02014;figure supplement 1</xref>): the average broadband peaky speech ABR from 0 to 16 ms was zero-padded from 0 ms to the beginning of wave I (1.6 ms), windowed with a Hann function, normalized, and then zero-padded from &#x02212;16 ms to 0 ms to center the kernel. (2) A more nuanced and well-known model of the auditory nerve and periphery that accounts for acoustics and some of the nonlinearities of the auditory system (<xref rid="bib53" ref-type="bibr">Rudnicki et al., 2015</xref>; <xref rid="bib62" ref-type="bibr">Verhulst et al., 2018</xref>; <xref rid="bib64" ref-type="bibr">Zilany et al., 2014</xref>) &#x02013; we simulated EEG for waves I, III, and V using the framework described by <xref rid="bib62" ref-type="bibr">Verhulst et al., 2018</xref>, but due to computation constraints, modified the implementation to use the peripheral model by <xref rid="bib64" ref-type="bibr">Zilany et al., 2014</xref>. The simulated EEG of each model was then deconvolved with the pulse trains to derive the modeled responses, which are shown in comparison with the measured response in <xref ref-type="fig" rid="fig3">Figure 3</xref>. The pre-stimulus component of the measured response was present in both models, suggesting that there are nonlinear parts of the response that are not accounted for in the deconvolution with the pulse train regressor. However, the pre-stimulus component was temporally broad compared to the components representing activity from the auditory nerve and cochlear nucleus (i.e., waves I and III), and could thus be dealt with by high-pass filtering. The pre-stimulus component was reduced with a 150-Hz first-order Butterworth high-pass filter (<xref ref-type="fig" rid="fig1">Figure 1</xref>) and minimized with a more aggressive 200-Hz second-order Butterworth high-pass filter (<xref ref-type="fig" rid="fig3">Figure 3</xref>, bottom). As expected from more aggressive high-pass filtering, wave V became smaller, sharper, and earlier, and was followed by a significant negative deflection. Therefore, when doing an experiment where the analysis needs to evaluate specific contributions to the earliest ABR components, we recommend high-pass filtering to help mitigate the complex and time-varying nonlinearities inherent in the auditory system, as well as potential influences by responses from later sources.</p><fig id="fig3" position="float" orientation="portrait"><label>Figure 3.</label><caption><title>Comparison of grand average (N&#x000a0;=&#x000a0;22) measured and modeled responses to&#x000a0;~43 min of broadband peaky speech.</title><p>Amplitudes of the linear (dashed line) and auditory nerve (AN; dotted line) modeled responses were in arbitrary units, and thus scaled to match the amplitude of the measured response (solid line) over the 0&#x02013;20 ms lags. The pre-stimulus component was present in all three responses using a first-order 30&#x000a0;Hz high-pass Butterworth filter (left&#x000a0;column), but was minimized by aggressive high-pass filtering with a second-order 200&#x000a0;Hz high-pass Butterworth filter (right&#x000a0;column).</p></caption><graphic xlink:href="elife-62329-fig3"/><p content-type="supplemental-figure"><fig id="fig3s1" specific-use="child-fig" orientation="portrait" position="anchor"><label>Figure 3&#x02014;figure supplement 1.</label><caption><title>Auditory brainstem response (ABR) kernel used for the simple linear deconvolution model.</title><p>The average broadband peaky speech ABR (N&#x000a0;=&#x000a0;22) from 0 to 16 ms was zero-padded from 0 ms to the beginning of wave I (1.6 ms), windowed with a Hann function, normalized, and then zero-padded from &#x02212;16 ms to 0 ms to center the kernel.</p></caption><graphic xlink:href="elife-62329-fig3-figsupp1"/></fig></p></fig><p>We verified that the EEG data collected in response to broadband peaky speech could be regressed with the half-wave rectified speech to generate a response. <xref ref-type="fig" rid="fig4">Figure 4A</xref> shows that the derived responses to unaltered and broadband peaky speech were similar in morphology when using the half-wave rectified audio as the regressor. Correlation coefficients from the 22 subjects for the 0&#x02013;40 ms lags had a median (interquartile range) of 0.84 (0.72&#x02013;0.91). This means that the same EEG collected to broadband peaky speech can be flexibly used to generate the robust canonical brainstem response to the pulse train, as well as the broader response to the half-wave rectified speech.</p><fig id="fig4" position="float" orientation="portrait"><label>Figure 4.</label><caption><title>Comparison of responses derived by using the same type of regressor in the deconvolution.</title><p>Average waveforms (areas show&#x000a0;&#x000b1;1 SEM) are shown for ~43 min each of unaltered speech (black) and broadband peaky speech (blue). EEG was regressed with the (<bold>A</bold>) half-wave rectified audio and (<bold>B</bold>) pulse train. Responses were high-pass filtered at 30 Hz using a first-order Butterworth filter.</p></caption><graphic xlink:href="elife-62329-fig4"/><p content-type="supplemental-figure"><fig id="fig4s1" specific-use="child-fig" orientation="portrait" position="anchor"><label>Figure 4&#x02014;figure supplement 1.</label><caption><title>Comparison of responses derived by using the half-wave rectified audio as the regressor in the deconvolution with electroencephalography (EEG) recorded in response to&#x000a0;~43 min of unaltered speech and multiband peaky speech.</title><p>Average waveforms (areas show&#x000a0;&#x000b1;1&#x000a0;SEM) are shown for EEG recorded to unaltered speech (black) relative to EEG recorded to multiband peaky speech (red). Responses were high-pass filtered at 30 Hz using a first-order Butterworth filter.</p></caption><graphic xlink:href="elife-62329-fig4-figsupp1"/></fig></p></fig><p>Although responses to broadband peaky speech can be derived from both the half-wave rectified audio and pulse train regressors, the same cannot be said about unaltered speech. We also regressed the EEG data collected in response to unaltered speech with the pulse train. <xref ref-type="fig" rid="fig4">Figure 4B</xref> shows that simply using the pulse train as a regressor does not give a robust canonical response &#x02013; the response contained a wave that slightly resembled wave V of the response to broadband peaky speech, albeit at a later latency and smaller amplitude, but there were no other earlier waves of the ABR or later waves of the MLR. The correlation coefficients comparing the unaltered and broadband peaky speech responses to the pulse train had a median (interquartile range) of 0.20 (0.05&#x02013;0.39). The response morphology was abnormal, with an acausal response at 0 ms and a smearing of the response in time, particularly at lags of the MLR &#x02013; these effects are consistent with some phases in the unaltered speech that come pre-pulse, which differs from that of the peaky speech that has aligned phases at each glottal pulse. The aligned phases of the broadband peaky response allow for the distinct waves of the canonical brainstem and MLRs to be derived using the pulse train regressor.</p></sec></sec><sec id="s2-2"><title>Broadband peaky speech responses differ across talkers</title><p>We next sought to determine whether response morphologies depended on the talker identity. To determine to&#x000a0;what extent the morphology and robustness of peaky speech responses depend on a specific narrator&#x02019;s voice and fundamental frequency &#x02013; and therefore, rate of stimulation &#x02013; we compared waveforms and peak wave latencies of male- and female-narrated broadband peaky speech in 11 subjects.</p><p>The group average waveforms to female- and male-narrated broadband peaky speech showed similar canonical morphologies but were smaller and later for female-narrated ABR responses (<xref ref-type="fig" rid="fig5">Figure 5A</xref>), much as they would be for click stimuli presented at higher rates (e.g., <xref rid="bib10" ref-type="bibr">Burkard et al., 1990</xref>; <xref rid="bib11" ref-type="bibr">Burkard and Hecox, 1983</xref>; <xref rid="bib13" ref-type="bibr">Chiappa et al., 1979</xref>; <xref rid="bib15" ref-type="bibr">Don et al., 1977</xref>; <xref rid="bib34" ref-type="bibr">Jiang et al., 2009</xref>). All component waves of the ABR and MLR were visible in the group average, although fewer subjects exhibited a clear wave III in the female-narrated response (9 versus all 11 subjects). The median (interquartile range) male&#x02013;female correlation coefficients were 0.68 (0.56&#x02013;0.78) for ABR lags of 0&#x02013;15 ms with a 150&#x000a0;Hz high-pass filter and 0.53 (0.50&#x02013;0.60) for ABR/MLR lags of 0&#x02013;40 ms with a 30&#x000a0;Hz high-pass filter (<xref ref-type="fig" rid="fig5">Figure 5B</xref>). This stimulus dependence was significantly different than the variability introduced by averaging only half the epochs (i.e., splitting by male- and female-narrated epochs) &#x02013; the correlation coefficients for the data reanalyzed into even and odd epochs (each of the even/odd splits contained the same number of male- and female-narrated epochs) had a median (interquartile range) of 0.89 (0.79&#x02013;0.95) for ABR lags and 0.66 (0.39&#x02013;0.78) for ABR/MLR lags. These odd&#x02013;even coefficients were significantly higher than the male&#x02013;female coefficients for the ABR (<italic>W</italic><sub>(10)</sub> = 0.0, p=0.001; Wilcoxon signed-rank test) but not when the response included all lags of the MLR (<italic>W</italic><sub>(10)</sub> = 17.0, p=0.175), which is indicative of the increased variability of these later waves. These high odd&#x02013;even correlations also show that responses from a single narrator are similar even if the text spoken is different. The overall male&#x02013;female narrator differences for the ABR indicate that the choice of narrator for using peaky speech impacts the morphology of the early response.</p><fig id="fig5" orientation="portrait" position="float"><label>Figure 5.</label><caption><title>Comparison of responses to 32 min each of male- (dark blue) and female-narrated (light blue) re-synthesized broadband peaky speech.</title><p>(<bold>A</bold>) Average waveforms across subjects (areas show&#x000a0;&#x000b1;1&#x000a0;SEM) are shown for auditory brainstem response (ABR) time lags with high-pass filtering at 150 Hz (top), and both ABR and middle latency response (MLR) time lags with a lower high-pass filtering cutoff of 30 Hz (bottom). (<bold>B</bold>) Histograms of the correlation coefficients between responses evoked by male- and female-narrated broadband peaky speech during ABR (top) and ABR/MLR (bottom) time lags. Solid lines denote the median and dotted lines the interquartile range. (<bold>C</bold>) Comparison of ABR (top) and MLR (bottom) wave peak latencies for individual subjects (gray) and the group mean (black). ABR and MLR responses were similar to both types of input but are smaller for female-narrated speech, which has a higher glottal pulse rate. Peak latencies for female-evoked speech were delayed during ABR time lags but faster for early MLR time lags.</p></caption><graphic xlink:href="elife-62329-fig5"/></fig><p>As expected from the waveforms, peak latencies of component waves differed between male- and female-narrated broadband peaky speech (<xref ref-type="fig" rid="fig5">Figure 5C</xref>). Mean &#x000b1; SEM peak latency differences (female &#x02013; male) for waves I, III, and V of the ABR were 0.21&#x000a0;&#x000b1;&#x000a0;0.13 ms (<italic>t</italic><sub>(10)</sub> = 1.59, p=0.144, <italic>d</italic>&#x000a0;=&#x000a0;0.50), 0.42&#x000a0;&#x000b1;&#x000a0;0.11 ms (<italic>t</italic><sub>(9)</sub> = 3.47, p=0.007, <italic>d</italic>&#x000a0;=&#x000a0;1.16), and 0.54&#x000a0;&#x000b1;&#x000a0;0.09 ms (<italic>t</italic><sub>(10)</sub> = 5.56, p&#x0003c;0.001, <italic>d</italic>&#x000a0;=&#x000a0;1.76), respectively. Latency differences were not significant for MLR peaks (P<sub>0</sub>: &#x02212;0.89&#x000a0;&#x000b1;&#x000a0;0.40 ms, <italic>t</italic><sub>(9)</sub> = &#x02212;2.13, p=0.062, <italic>d</italic> = &#x02212;0.71; N<sub>a</sub>: &#x02212;0.91&#x000a0;&#x000b1;&#x000a0;0.55 ms, <italic>t</italic><sub>(8)</sub> = &#x02212;1.56, p=0.158, <italic>d</italic> = &#x02212;0.55; P<sub>a</sub>: 0.09&#x000a0;&#x000b1;&#x000a0;0.55 ms, <italic>t</italic><sub>(9)</sub> = &#x02212;0.16, p=0.880, <italic>d</italic> = &#x02212;0.05).</p></sec><sec id="s2-3"><title>Multiband peaky speech yields frequency-specific brainstem responses to speech</title><sec id="s2-3-1"><title>Frequency-specific responses show frequency-specific lags</title><p>Broadband peaky speech gives new insights into subcortical processing of naturalistic speech. Not only are brainstem responses used to evaluate processing at different stages of auditory processing but ABRs can also be used to assess hearing function across different frequencies. Traditionally, frequency-specific ABRs are measured using clicks with high-pass masking noise or frequency-specific tone pips. We tested the flexibility of using our new peaky speech technique to investigate how speech processing differs across frequency regions, such as 0&#x02013;1, 1&#x02013;2, 2&#x02013;4, and 4&#x02013;8 kHz frequency bands (for details, see 'Multiband peaky speech'&#x000a0;section in 'Materials and methods').</p><p>Mean &#x000b1; SEM responses from 22 subjects to the four frequency bands (0&#x02013;1, 1&#x02013;2, 2&#x02013;4, and 4&#x02013;8 kHz) of male-narrated multiband peaky speech are shown as colored waveforms with solid lines in <xref ref-type="fig" rid="fig6">Figure 6A</xref>. A high-pass filter with a cutoff of 30 Hz was used. Each frequency band response comprises a frequency-band-specific component as well as a band-independent common component, both of which are due to spectral characteristics of the stimuli and neural activity. The pulse trains are independent over time in the vocal frequency range &#x02013; thereby allowing us to pull out responses to each different pulse train and frequency band from the same EEG &#x02013; but they became coherent at frequencies lower than 72 Hz for the male-narrated speech and 126 Hz for the female speech (see Figure 15 in Materials and methods). This coherence was due to all pulse trains beginning and ending together at the onset and offset of voiced segments and was the source of the low-frequency common component of each band&#x02019;s response. The way to remove the common component is to calculate the common activity across the frequency band responses and subtract this waveform from each of the frequency band responses (see 'Response derivation' section&#x000a0;in 'Materials and methods'). This common component waveform is shown by the dot-dashed gray line, which is superimposed with each response to the frequency bands in <xref ref-type="fig" rid="fig6">Figure 6A</xref>. The subtracted, frequency-specific waveforms to each frequency band are shown by the solid lines in <xref ref-type="fig" rid="fig6">Figure 6B</xref>. Of course, the subtracted waveforms could also then be high-pass filtered at 150 Hz to highlight earlier waves of the brainstem responses, as shown by the dashed lines in <xref ref-type="fig" rid="fig6">Figure 6B</xref>. However, this method reduces the amplitude of the responses, which in turn affects response SNR and detectability. In some scenarios, due to the low-pass nature of the common component, high-passing the waveforms at a high enough frequency may obviate the need to formally subtract the common component. For example, at least for the narrators used in these experiments, the common component contained minimal energy above 150 Hz, so if the waveforms are already high-passed at 150 Hz to focus on the early waves of the ABR, then the step of formally subtracting the common component may not be necessary. But beyond the computational cost, there is no reason not to subtract the common component and doing so allows lower filter cutoffs to be used.</p><fig id="fig6" position="float" orientation="portrait"><label>Figure 6.</label><caption><title>Comparison of responses to&#x000a0;~43 min of male-narrated multiband peaky speech.</title><p>(<bold>A</bold>) Average waveforms across subjects (areas show&#x000a0;&#x000b1;1 SEM) are shown for each band (colored solid lines) and common component (dot-dash gray line, same waveform replicated as a reference for each band), which was calculated using six false pulse trains. (<bold>B</bold>) The common component was subtracted from each band&#x02019;s response to give the frequency-specific waveforms (areas show&#x000a0;&#x000b1;1&#x000a0;SEM), which are shown with high-pass filtering at 30 Hz (solid lines) and 150 Hz (dashed lines). (<bold>C</bold>) Mean &#x000b1; SEM peak latencies for each wave decreased with increasing band frequency. Numbers of subjects with an identifiable wave are given for each wave and band. Details of the mixed effects models for (<bold>C</bold>) are provided in <xref ref-type="supplementary-material" rid="supp1">Supplementary file 1A</xref>.</p></caption><graphic xlink:href="elife-62329-fig6"/><p content-type="supplemental-figure"><fig id="fig6s1" specific-use="child-fig" orientation="portrait" position="anchor"><label>Figure 6&#x02014;figure supplement 1.</label><caption><title>Comparison of responses to 64 min each of male- (left) and female-narrated (right) multiband peaky speech created with the dynamic random frequency shift method.</title><p>(<bold>A</bold>) Weighted-average waveforms for one subject are shown for each band (colored solid lines) and common component (dot-dashed gray line, same waveform replicated as a reference for each band), which was calculated using six false pulse trains. (<bold>B</bold>) The common component was subtracted from each band&#x02019;s response to give the frequency-specific waveforms, which are shown with high-pass filtering at 30 Hz (solid lines) and 150 Hz (dashed lines). Responses from all four bands show more consistent resemblance to the common component, indicating that this method is effective at reducing stimulus-related bias. However, differences still remain in the lowest frequency band for latencies&#x000a0;&#x0003e;30 ms, suggesting that this new method reveals true underlying low-frequency neural activity that is unique.</p></caption><graphic xlink:href="elife-62329-fig6-figsupp1"/></fig></p></fig><p>Overall, the frequency-specific responses showed characteristic ABR and MLR waves with longer latencies for lower frequency bands, as would be expected from responses arising from different cochlear regions. Also, waves I and III of the ABR were visible in the group average waveforms of the 2&#x02013;4 kHz (&#x02265;41% of subjects) and 4&#x02013;8 kHz (&#x02265;86% of subjects) bands, whereas the MLR waves were more prominent in the 0&#x02013;1 kHz (&#x02265;95% of subjects) and 1&#x02013;2 kHz (&#x02265;54% of subjects) bands.</p><p>These frequency-dependent latency changes for the frequency-specific responses are highlighted further in <xref ref-type="fig" rid="fig6">Figure 6C</xref>, which shows mean &#x000b1; SEM peak latencies and the number of subjects who had a clearly identifiable wave. The change in peak latency with frequency band was modeled using a power law regression (<xref rid="bib31" ref-type="bibr">Harte et al., 2009</xref>; <xref rid="bib44" ref-type="bibr">Neely et al., 1988</xref>; <xref rid="bib52" ref-type="bibr">Rasetshwane et al., 2013</xref>; <xref rid="bib60" ref-type="bibr">Strelcyk et al., 2009</xref>). The fixed effect of wave and its interaction with frequency were also included. Details of the statistical model are described in <xref ref-type="supplementary-material" rid="supp1">Supplementary file 1A</xref>. Modeled parameters that can be meaningfully compared to existing norms are given in <xref rid="table1" ref-type="table">Table 1A</xref>. In general, there was good agreement with these norms. There was a significant decrease in wave V latency with increasing frequency band (slope p&#x0003c;0.001 for 30 and 150 Hz), which was shallower (i.e., less negative) for MLR waves compared to the ABR wave V (all p&#x0003c;0.001 for interactions between wave and frequency term). Unsurprisingly there were significantly different latencies for each MLR wave P<sub>0</sub>, N<sub>a</sub>,&#x000a0;and P<sub>a</sub> compared to the ABR wave V (all effects of wave on the intercept p&#x0003c;0.001).</p><table-wrap id="table1" orientation="portrait" position="float"><label>Table 1.</label><caption><title>Parameter estimates and SEM for power&#x000a0;law fits to the multiband peaky speech auditory brainstem response wave V data in the three experiments*.</title></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="1" colspan="1">Type</th><th rowspan="1" colspan="1">Narrator</th><th rowspan="1" colspan="1">High-pass cutoff</th><th rowspan="1" colspan="1">a</th><th rowspan="1" colspan="1">b</th><th rowspan="1" colspan="1">d</th></tr></thead><tbody><tr><td colspan="3" valign="bottom" rowspan="1">Norms<sup>&#x02020;</sup></td><td rowspan="1" colspan="1">4.70&#x02013;5.00 ms</td><td rowspan="1" colspan="1">3.46&#x02013;5.39 ms</td><td rowspan="1" colspan="1">0.22&#x02013;0.50</td></tr><tr><td colspan="6" rowspan="1"><italic>A. Experiment 1</italic></td></tr><tr><td rowspan="2" colspan="1">Diotic <break/>four-bands</td><td rowspan="2" colspan="1">Male</td><td rowspan="1" colspan="1">30&#x000a0;Hz</td><td rowspan="1" colspan="1">5.13&#x000a0;&#x000b1;&#x000a0;0.08 ms</td><td rowspan="1" colspan="1">3.95&#x000a0;&#x000b1;&#x000a0;1.03 ms</td><td rowspan="1" colspan="1">0.41&#x000a0;&#x000b1;&#x000a0;0.02</td></tr><tr><td rowspan="1" colspan="1">150&#x000a0;Hz</td><td rowspan="1" colspan="1">4.80&#x000a0;&#x000b1;&#x000a0;0.08 ms</td><td rowspan="1" colspan="1">3.95&#x000a0;&#x000b1;&#x000a0;1.03 ms</td><td rowspan="1" colspan="1">0.37&#x000a0;&#x000b1;&#x000a0;0.02</td></tr><tr><td colspan="6" rowspan="1"><italic>B. Experiment 2</italic></td></tr><tr><td rowspan="2" colspan="1">Diotic <break/>four-bands</td><td rowspan="1" colspan="1">Male</td><td rowspan="1" colspan="1">30&#x000a0;Hz</td><td rowspan="1" colspan="1">5.06&#x000a0;&#x000b1;&#x000a0;0.14 ms</td><td rowspan="1" colspan="1">4.42&#x000a0;&#x000b1;&#x000a0;1.04 ms</td><td rowspan="1" colspan="1">0.45&#x000a0;&#x000b1;&#x000a0;0.03</td></tr><tr><td rowspan="1" colspan="1">Female</td><td rowspan="1" colspan="1">30&#x000a0;Hz</td><td rowspan="1" colspan="1">5.58&#x000a0;&#x000b1;&#x000a0;0.12 ms</td><td rowspan="1" colspan="1">3.94&#x000a0;&#x000b1;&#x000a0;1.08 ms</td><td rowspan="1" colspan="1">0.44&#x000a0;&#x000b1;&#x000a0;0.05</td></tr><tr><td colspan="6" rowspan="1"><italic>C. Experiment 3</italic></td></tr><tr><td rowspan="2" colspan="1">Dichotic <break/>five-bands</td><td rowspan="1" colspan="1">Male</td><td rowspan="1" colspan="1">30&#x000a0;Hz</td><td rowspan="1" colspan="1">5.06&#x000a0;&#x000b1;&#x000a0;0.14 ms<sup>&#x02021;</sup></td><td rowspan="1" colspan="1">4.13&#x000a0;&#x000b1;&#x000a0;1.04 ms<sup>&#x000a7;</sup></td><td rowspan="1" colspan="1">0.36&#x000a0;&#x000b1;&#x000a0;0.02</td></tr><tr><td rowspan="1" colspan="1">Female</td><td rowspan="1" colspan="1">30&#x000a0;Hz</td><td rowspan="1" colspan="1">5.58&#x000a0;&#x000b1;&#x000a0;0.12 ms<sup>&#x02021;</sup></td><td rowspan="1" colspan="1">3.75&#x000a0;&#x000b1;&#x000a0;1.07 ms<sup>&#x000a7;</sup></td><td rowspan="1" colspan="1">0.41&#x000a0;&#x000b1;&#x000a0;0.03</td></tr></tbody></table><table-wrap-foot><fn><p>*Power model: <inline-formula><mml:math id="inf1"><mml:mrow><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>f</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mi>a</mml:mi><mml:mo>+</mml:mo><mml:mi>b</mml:mi><mml:msup><mml:mi>f</mml:mi><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:math></inline-formula> where <inline-formula><mml:math id="inf2"><mml:mrow><mml:mi>a</mml:mi><mml:mo>=</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>y</mml:mi><mml:mi>n</mml:mi><mml:mi>a</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi><mml:mi>i</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>I</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf3"><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>y</mml:mi><mml:mi>n</mml:mi><mml:mi>a</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi><mml:mi>i</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> = 0.8 ms. See 'Statistical analyses' section&#x000a0;in 'Materials and methods' for more detail.</p><p><sup>&#x02020;</sup>Norms for tone pips and derived bands were calculated for 65 dBppeSPL using the model's level-dependent parameter when appropriate (<xref rid="bib44" ref-type="bibr">Neely et al., 1988</xref>; <xref rid="bib52" ref-type="bibr">Rasetshwane et al., 2013</xref>; <xref rid="bib60" ref-type="bibr">Strelcyk et al., 2009</xref>).</p></fn><fn><p><sup>&#x02021;</sup>Estimates from experiment 2 were used.</p><p><sup>&#x000a7;</sup>Estimates given for the left ear; there was not a significant difference for the right ear.</p></fn></table-wrap-foot></table-wrap><p>Next, the frequency-specific responses (i.e., multiband responses with common component subtracted) were summed and the common component added to derive the entire response to multiband peaky speech. As shown in <xref ref-type="fig" rid="fig7">Figure 7</xref>, this summed multiband response was strikingly similar in morphology to the broadband peaky speech. Both responses were high-passed filtered at 150 Hz and 30 Hz to highlight the earlier ABR waves and later MLR waves, respectively. The median (interquartile range) correlation coefficients from the 22 subjects were 0.90 (0.84&#x02013;0.94) for 0&#x02013;15 ms ABR lags and 0.66 (0.55&#x02013;0.83) for 0&#x02013;40 ms MLR lags. The similarity verifies that the frequency-dependent responses are complementary to each other to the common component such that these components add linearly into a &#x02018;whole&#x02019; broadband response. If there were significant overlap in the cochlear regions, for example, the summed response would not resemble the broadband response to such a degree and would instead be larger. The similarity also verified that the additional changes we made to create re-synthesized multiband peaky speech did not significantly affect responses compared to broadband peaky speech.</p><fig id="fig7" orientation="portrait" position="float"><label>Figure 7.</label><caption><title>Comparison of responses to&#x000a0;~43 min of male-narrated peaky speech in the same subjects.</title><p>Average waveforms across subjects (areas show&#x000a0;&#x000b1;1&#x000a0;SEM) are shown for broadband peaky speech (blue) and summed frequency-specific responses to multiband peaky speech with the common component added (red), high-pass filtered at 150 Hz (left) and 30 Hz (right). Regressors in the deconvolution were pulse trains.</p></caption><graphic xlink:href="elife-62329-fig7"/></fig><p>We also verified that the EEG data collected in response to multiband peaky speech could be regressed with the half-wave rectified speech to generate a response. <xref ref-type="fig" rid="fig4s1">Figure 4&#x02014;figure supplement 1</xref> shows that the derived responses to unaltered and multiband peaky speech were similar in morphology when using the half-wave audio as the regressor, although the multiband peaky speech response was broader in the earlier latencies. Correlation coefficients from the 22 subjects for the 0&#x02013;40 ms lags had a median (interquartile range) of 0.83 (0.77&#x02013;0.88). This means that the same EEG collected to multiband peaky speech can be flexibly used to generate the frequency-specific brainstem responses to the pulse train, as well as the broader response to the half-wave rectified speech.</p></sec><sec id="s2-3-2"><title>Frequency-specific responses also differ by narrator</title><p>We also investigated the effects of male- versus female-narrated multiband peaky speech in the same 11 subjects. As with broadband peaky speech, responses to both narrators showed similar morphology, but the responses were smaller and the MLR waves more variable for the female than the&#x000a0;male narrator (<xref ref-type="fig" rid="fig8">Figure 8A</xref>). <xref ref-type="fig" rid="fig8">Figure 8B</xref> shows the male&#x02013;female correlation coefficients for responses between 0&#x02013;40 ms with a high-pass filter of 30 Hz and between 0&#x02013;15 ms with a high-pass filter of 150 Hz. The median (interquartile range) male&#x02013;female correlation coefficients were better for higher frequency bands, ranging from 0.12 (&#x02212;0.11&#x02013;0.34) for the 1&#x02013;2 kHz band to 0.44 (0.32&#x02013;0.49) for the 4&#x02013;8 kHz band for MLR lags (<xref ref-type="fig" rid="fig8">Figure 8B</xref>, left), and from 0.49 (0.30&#x02013;0.69) for the 1&#x02013;2 kHz band to 0.79 (0.64&#x02013;0.81) for the 4&#x02013;8 kHz band for ABR lags (<xref ref-type="fig" rid="fig8">Figure 8B</xref>, right). These male&#x02212;female correlation coefficients were significantly weaker than those of the same EEG split into even and odd trials for all but the 2&#x02013;4 kHz frequency band when responses were high-pass filtered at 30 Hz and correlated across 0&#x02013;40 ms lags (2&#x02013;4 kHz: <italic>W</italic><sub>(10)</sub> = 17.0, p=0.175; other bands: <italic>W</italic><sub>(10)</sub> &#x02264; 5.0, p&#x02264;0.010), but were similar to the even/odd trials for responses from all frequency bands high-pass filtered at 150 Hz (<italic>W</italic><sub>(10)</sub> &#x02265; 11.0, p&#x02265;0.054). These results indicate that the specific narrator can affect the robustness of frequency-specific responses, particularly for the MLR waves.</p><fig id="fig8" orientation="portrait" position="float"><label>Figure 8.</label><caption><title>Comparison of responses to 32 min each of male- and female-narrated re-synthesized multiband peaky speech.</title><p>(<bold>A</bold>) Average frequency-specific waveforms across subjects (areas show&#x000a0;&#x000b1;1 SEM; common component removed) are shown for each band in response to male- (dark red lines) and female-narrated (light red lines) speech. Responses were high-pass filtered at 30 Hz (left) and 150 Hz (right) to highlight the middle latency response&#x000a0;(MLR) and auditory brainstem response&#x000a0;(ABR), respectively. (<bold>B</bold>) Correlation coefficients between responses evoked by male- and female-narrated multiband peaky speech during ABR/MLR (left) and ABR (right) time lags for each frequency band. Black lines denote the median. (<bold>C</bold>) Mean &#x000b1; SEM peak latencies for male- (dark) and female-narrated (light) speech for each wave decreased with increasing frequency band. Numbers of subjects with an identifiable wave are given for each wave, band, and narrator. Lines are given a slight horizontal offset to make the error bars easier to see. Details of the mixed effects models for (<bold>C</bold>) are provided in <xref ref-type="supplementary-material" rid="supp1">Supplementary file 1B</xref>.</p></caption><graphic xlink:href="elife-62329-fig8"/></fig><p>As expected from the grand average waveforms and male&#x02013;female correlations, there were fewer subjects who had identifiable waves across frequency bands for the female- than male-narrated speech. These numbers are shown in <xref ref-type="fig" rid="fig8">Figure 8C</xref>, along with the mean &#x000b1; SEM peak latencies for each wave, frequency band, and narrator. Again, there were few numbers of subjects with identifiable waves I and III for the lower frequency bands. Therefore, a similar power law model as used above was performed for waves V, P<sub>0</sub>, N<sub>a</sub>, and P<sub>a</sub> of responses in the four frequency bands that were high-pass filtered at 30 Hz. This model also included fixed effects of narrator and its associated interactions. Details of the statistical model are described in <xref ref-type="supplementary-material" rid="supp1">Supplementary file 1B</xref>. The post-cochlear delay estimate for the female narrator of 5.58 ms was slightly outside the normal range of 4.7&#x02013;5.0 ms, which is likely a result of high-passing at 30 Hz here compared to 100 Hz in the previous studies. All other values were consistent with their norms and appear in <xref rid="table1" ref-type="table">Table 1B</xref>. As before, peak latencies were different for each wave and decreased with increasing frequency in a manner more pronounced for wave V than later waves (all p&#x0003c;0.001). There was a main effect of narrator on peak latencies but no interaction with wave (narrator p=0.001, wave&#x02013;narrator interactions p&#x0003e;0.087). Therefore, as with broadband peaky speech, frequency-specific peaky responses were more robust with the male narrator and the frequency-specific responses peaked earlier for a narrator with a lower fundamental frequency.</p></sec><sec id="s2-3-3"><title>Frequency-specific responses can be measured simultaneously in each ear (dichotically)</title><p>We have so far demonstrated the effectiveness of peaky speech for diotic stimuli, but there is often a need to evaluate auditory function in each ear, and the most efficient tests assess both ears simultaneously. Applying this principle to generate multiband peaky speech, we investigated whether ear-specific responses could be evoked across the five standard audiological octave frequency bands (500&#x02013;8000 Hz) using dichotic multiband speech. We created 10 independent pulse trains, two for each ear in each of the five frequency bands (see 'Multiband peaky speech' and 'Band filters' sections&#x000a0;in 'Materials and methods').</p><p>We recorded responses to male- and female-narrated dichotic multiband peaky speech in 11 subjects. The frequency-specific (i.e., common component-subtracted) group average waveforms for each ear and frequency band are shown in <xref ref-type="fig" rid="fig9">Figure 9A</xref>. The 10 waveforms were small, especially for female-narrated speech, but a wave V was identifiable for both narrators. Also, waves I and III of the ABR were visible in the group average waveforms of the 4 kHz band (&#x02265;45% and 18% of subjects for the male and female narrator, respectively) and 8 kHz band (&#x02265;90% and 72% of subjects for the male and female narrator, respectively). MLR waves were not clearly identifiable for responses to female-narrated speech. Therefore, correlations between responses were performed for ABR lags between 0&#x02013;15 ms. As shown in <xref ref-type="fig" rid="fig9">Figure 9B</xref>, the median (interquartile range) left&#x02013;right ear correlation coefficients (averaged across narrators) ranged from 0.28 (0.02&#x02013;0.52) for the 0.5 kHz band to 0.73 (0.62&#x02013;0.84) for the 8 kHz band. Male&#x02013;female correlation coefficients (averaged across ear) ranged from 0.44 (0.00&#x02013;0.58) for the 0.5 kHz band to 0.76 (0.51&#x02013;0.80) for the 8 kHz band. Although the female-narrated responses were smaller than the male-narrated responses, these male&#x02013;female coefficients did not significantly differ from correlations of same EEG split into even&#x02013;odd trials and averaged across ear (<italic>W</italic><sub>(10)</sub> &#x02265; 12.0, p&#x02265;0.067), likely reflecting the variability in such small responses.</p><fig id="fig9" orientation="portrait" position="float"><label>Figure 9.</label><caption><title>Comparison of responses to&#x000a0;~60 min each of male- and female-narrated dichotic multiband peaky speech with standard audiological frequency bands.</title><p>(<bold>A</bold>) Average frequency-specific waveforms across subjects (areas show&#x000a0;&#x000b1;1 SEM; common component removed) are shown for each band for the left ear (dotted lines) and right ear (solid lines). Responses were high-pass filtered at 30 Hz. (<bold>B</bold>) Left&#x02013;right ear correlation coefficients (top, averaged across gender) and male&#x02013;female correlation coefficients (bottom, averaged across ear) during auditory brainstem response time lags (0&#x02013;15 ms) for each frequency band. Black lines denote the median. (<bold>C</bold>) Mean &#x000b1; SEM wave V latencies for male- (dark red) and female-narrated (light red) speech for the left (dotted line, cross symbol) and right ear (solid line, circle symbol) decreased with increasing frequency band. Lines are given a slight horizontal offset to make the error bars easier to see. Details of the mixed effects model for (<bold>C</bold>) are provided in <xref ref-type="supplementary-material" rid="supp1">Supplementary file 1C</xref>.</p></caption><graphic xlink:href="elife-62329-fig9"/></fig><p><xref ref-type="fig" rid="fig9">Figure 9C</xref> shows the mean &#x000b1; SEM peak latencies of wave V for each ear and frequency band for the male- and female-narrated dichotic multiband peaky speech. The change in wave V latency with frequency was modeled by a similar power law as used above. This model also included fixed effects of narrator, ear, and the interactions between narrator and frequency. Details of the statistical model are described in <xref ref-type="supplementary-material" rid="supp1">Supplementary file 1C</xref>. For wave V, the estimated mean parameters for both narrators were consistent with norms and appear in <xref rid="table1" ref-type="table">Table 1C</xref>. Latency decreased with increasing frequency (slope, p&#x0003c;0.001) in a similar way for both narrators (interaction with slope p=0.085). Peak latency differed by narrator (interaction with intercept p=0.004) but not between ears (p=0.265). Taken together, these results confirm that, while small in amplitude, frequency-specific responses can be elicited in both ears across five different frequency bands and show characteristic latency changes across the different frequency bands.&#x000a0;</p></sec></sec><sec id="s2-4"><title>Responses are obtained quickly for male-narrated broadband peaky speech but not multiband&#x000a0;peaky speech</title><p>Having demonstrated that peaky broadband and multiband speech provides canonical waveforms with characteristic changes in latency with frequency, we next evaluated the acquisition time required for waveforms to reach a decent SNR (see 'Response SNR calculation' section&#x000a0;in 'Materials and methods' for details).</p><p><xref ref-type="fig" rid="fig10">Figure 10</xref> shows the cumulative proportion of subjects who had responses with&#x000a0;&#x02265;0 dB SNR to unaltered and broadband peaky speech as a function of recording time. Acquisition times for 22 subjects were similar for responses to both unaltered and broadband peaky male-narrated speech, with 0 dB SNR achieved by 7&#x02013;8 min in 50% of subjects and about 20 min for all subjects. For responses high-pass filtered at 150 Hz to highlight the ABR (0&#x02013;15 ms interval), the time reduced to 2 and 5 min for 50% and 100% of subjects respectively for broadband peaky speech but increased to 8 and&#x000a0;&#x0003e;20 min for 50% and 100% of subjects respectively for unaltered speech. The increased time for the unaltered speech reflects the broad morphology of the response during ABR lags. These times for male-narrated broadband peaky speech were confirmed in our second cohort of 11 subjects. However, acquisition times were at least 2.1 times &#x02013; but in some cases over 10 times &#x02013; longer for female-narrated broadband peaky speech. In contrast to male-narrated speech, not all subjects achieved this threshold for female-narrated speech by the end of the 32 min recording. Taken together, these acquisition times confirm that responses with useful SNRs can be measured quickly for male-narrated broadband peaky speech but longer recording sessions are necessary for narrators with higher fundamental frequencies.</p><fig id="fig10" orientation="portrait" position="float"><label>Figure 10.</label><caption><title>Cumulative proportion of subjects who have responses with&#x000a0;&#x02265;0 dB signal-to-noise ratio&#x000a0;(SNR) as a function of recording time.</title><p>Time required for unaltered (black) and broadband peaky speech (dark blue) of a male narrator is shown for 22 subjects in the left plot, and for male (dark blue) and female (light blue) broadband peaky speech is shown for 11 subjects in the right plot. Solid lines denote SNRs calculated using variance of the signal high-pass filtered at 30 Hz over the auditory brainstem response&#x000a0;(ABR)/middle latency response&#x000a0;(MLR) interval 0&#x02013;30 ms, and dashed lines denote SNR variances calculated on signals high-pass filtered at 150 Hz over the ABR interval 0&#x02013;15 ms. Noise variance was calculated in the pre-stimulus interval &#x02212;480 to &#x02212;20 ms.</p></caption><graphic xlink:href="elife-62329-fig10"/></fig><p>The longer recording times necessary for a female narrator became more pronounced for the multiband peaky speech. <xref ref-type="fig" rid="fig11">Figure 11A</xref> shows the cumulative density function for responses high-pass filtered at 150 Hz and the SNR estimated over the ABR interval. Many subjects (72%) had frequency-specific responses (common component subtracted) with&#x000a0;&#x02265;0 dB SNR for all four frequency bands by the end of the 32 min recording for the male-narrated speech, but this was achieved in only 45% of subjects for the female-narrated speech. Multiband peaky speech required significantly longer recording times than broadband peaky speech, with 50% of subjects achieving 0 dB SNR by 15 min compared to 2 min for the male-narrated responses across the ABR 0&#x02013;15 ms interval and 17 min compared to 5 min for the MLR 0&#x02013;30 ms interval. Even more time was required for dichotic multiband speech, which comprised a larger number of frequency bands (<xref ref-type="fig" rid="fig11">Figure 11B</xref>). All 10 audiological band responses achieved&#x000a0;&#x02265;&#x000a0;0 dB SNR in 45% of ears (10/22 ears from 11 subjects) by 64 min for male-narrated speech and in 27% of ears (6/22 ears) for female-narrated speech. The smaller and broader responses in the low-frequency bands were slower to obtain and were the main constraint on testing time. These recording times suggest that deriving multiple frequency-specific responses will require at least more than 30 min per condition for&#x000a0;&#x0003c;5 bands and more than an hour session for one condition of peaky multiband speech with 10 bands.</p><fig id="fig11" orientation="portrait" position="float"><label>Figure 11.</label><caption><title>Cumulative proportion of subjects who have frequency-specific responses (common component subtracted) with&#x000a0;&#x02265;0 dB signal-to-noise ratio&#x000a0;(SNR) as a function of recording time.</title><p>Acquisition time was faster for male- (left) than female-narrated (right) multiband peaky speech with (<bold>A</bold>) four frequency bands presented diotically and (<bold>B</bold>) five frequency bands presented dichotically (total of 10 responses, five bands in each ear). SNR was calculated by comparing variance of signals high-pass filtered at 150 Hz across the auditory brainstem response interval of 0&#x02013;15 ms to variance of noise in the pre-stimulus interval &#x02212;480 to &#x02212;20 ms.</p></caption><graphic xlink:href="elife-62329-fig11"/></fig></sec></sec><sec sec-type="discussion" id="s3"><title>Discussion</title><p>The major goal of this work was to develop a method to investigate early stages of naturalistic speech processing. We re-synthesized continuous speech taken from audiobooks so that the phases of all harmonics aligned at each glottal pulse during voiced segments, thereby making speech as impulse-like (peaky) as possible to drive the auditory brainstem. Then we used the glottal pulse trains as the regressor in deconvolution to derive the responses. Indeed, comparing waveforms to broadband peaky and unaltered speech validated the superior ability of peaky speech to evoke additional waves of the canonical ABR and MLR, reflecting neural activity from multiple subcortical structures. Robust ABR and MLR responses were recorded in less than 5 and 20 min, respectively, for all subjects, with half of the subjects exhibiting a strong ABR within 2 min and MLR within 8 min. Longer recording times were required for the smaller responses generated by a narrator with a higher fundamental frequency. We also demonstrated the flexibility of this stimulus paradigm by simultaneously recording up to 10 frequency-specific responses to multiband peaky speech that was presented either diotically or dichotically, although these responses required much longer recording times. Taken together, our results show that peaky speech effectively yields responses from distinct subcortical structures and different frequency bands, paving the way for new investigations of speech processing and new tools for clinical application.</p><sec id="s3-1"><title>Peaky speech responses reflect activity from distinct subcortical components</title><sec id="s3-1-1"><title>Canonical responses can be derived from speech with impulse-like characteristics</title><p>For the purpose of investigating responses from different subcortical structures, we accomplished our goal of creating a stimulus paradigm that overcame some of the limitations of current methods using natural speech. Methods that do not use re-synthesized impulse-like speech generate responses characterized by a broad peak between 6&#x02013;9 ms (<xref rid="bib21" ref-type="bibr">Forte et al., 2017</xref>; <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>), with contributions predominantly from the inferior colliculus (<xref rid="bib55" ref-type="bibr">Saiz-Al&#x000ed;a and Reichenbach, 2020</xref>). In contrast, for the majority of our subjects, peaky speech evoked responses with canonical morphology comprising waves I, III, V, P<sub>0</sub>, N<sub>a</sub>, and&#x000a0;P<sub>a</sub> (<xref ref-type="fig" rid="fig1">Figure 1</xref>), reflecting neural activity from distinct stages of the auditory system from the auditory nerve to thalamus and primary auditory cortex (e.g., <xref rid="bib47" ref-type="bibr">Picton et al., 1974</xref>). Although clicks also evoke responses with multiple component waves, current studies of synaptopathy show quite varied results with clicks and poor correlation with speech in noise (<xref rid="bib9" ref-type="bibr">Bramhall et al., 2019</xref>; <xref rid="bib50" ref-type="bibr">Prendergast et al., 2017</xref>). Obtaining click-like responses to stimuli with all of speech&#x02019;s spectrotemporal richness may provide a better connection to the specific neural underpinnings of speech encoding, similar to how the complex cross-correlation to a fundamental waveform can change based on the context of attention (<xref rid="bib21" ref-type="bibr">Forte et al., 2017</xref>; <xref rid="bib54" ref-type="bibr">Saiz-Al&#x000ed;a et al., 2019</xref>).</p><p>The ABR wave V and similar MLR waves evoked here were also evoked by a method using embedded chirps intermixed within alternating octave bands of speech (<xref rid="bib2" ref-type="bibr">Backer et al., 2019</xref>). Chirps are transients that compensate for the cochlear traveling delay wave by introducing different phases across frequency, leading to a more synchronized response across the cochlea and a larger brainstem response than for clicks (<xref rid="bib14" ref-type="bibr">Dau et al., 2000</xref>; <xref rid="bib19" ref-type="bibr">Elberling and Don, 2008</xref>; <xref rid="bib57" ref-type="bibr">Shore and Nuttall, 1985</xref>). The responses to embedded chirps elicited waves with larger mean amplitude than those to our broadband peaky speech (~0.4 versus&#x000a0;~0.2 &#x003bc;V, respectively), although a similar proportion of subjects had identifiable waves, the SNR was good, and several other factors may contribute to amplitude differences. For example, higher click rates (e.g., <xref rid="bib10" ref-type="bibr">Burkard et al., 1990</xref>; <xref rid="bib11" ref-type="bibr">Burkard and Hecox, 1983</xref>; <xref rid="bib13" ref-type="bibr">Chiappa et al., 1979</xref>; <xref rid="bib15" ref-type="bibr">Don et al., 1977</xref>; <xref rid="bib34" ref-type="bibr">Jiang et al., 2009</xref>) and higher fundamental frequencies (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>; <xref rid="bib54" ref-type="bibr">Saiz-Al&#x000ed;a et al., 2019</xref>; <xref rid="bib55" ref-type="bibr">Saiz-Al&#x000ed;a and Reichenbach, 2020</xref>) reduce the brainstem response amplitude, and dynamic changes in rate may create interactions across neural populations that lead to smaller amplitudes. Our stimuli kept the dynamic changes in pitch across all frequencies (instead of alternate octave bands of chirps and speech) and created impulses at every glottal pulse, with an average pitch of&#x000a0;~115 Hz and&#x000a0;~198 Hz for the male and female narrators, respectively. These presentation rates were much higher and more variable than the flat 42 Hz rate at which the embedded chirps were presented (pitch flattened to 82 Hz and chirps presented every other glottal pulse). We could evaluate whether chirps would improve response amplitude to our dynamic peaky speech by simply all-pass filtering the re-synthesized voiced segments by convolving with a chirp prior to mixing the re-synthesized parts with the unvoiced segments. While maintaining the amplitude spectrum of speech, the harmonics would then have the different phases associated with chirps at each glottal pulse instead of all phases set to 0. Regardless, our peaky speech generated robust canonical responses with good SNR while maintaining a natural-sounding, if very slightly &#x02018;buzzy&#x02019;, quality to the speech. Overall, continuous speech re-synthesized to contain impulse-like characteristics is an effective way to elicit responses that distinguish contributions from different subcortical structures.</p></sec><sec id="s3-1-2"><title>Latencies of component waves are consistent with distinct subcortical structures</title><p>The latencies of the component waves of the responses to peaky speech are consistent with activity arising from known subcortical structures. The inter-wave latencies between I&#x02013;III, III&#x02013;V, and I&#x02013;V fall within the expected range for brainstem responses elicited by transients at 50&#x02013;60 dB SL and 50&#x02013;100 Hz rates (<xref rid="bib11" ref-type="bibr">Burkard and Hecox, 1983</xref>; <xref rid="bib13" ref-type="bibr">Chiappa et al., 1979</xref>; <xref rid="bib15" ref-type="bibr">Don et al., 1977</xref>), suggesting that&#x000a0;the transmission times between auditory nerve, cochlear nucleus, and rostral brainstem remain similar for speech stimuli. However, these speech-evoked waves peak at later absolute latencies than responses to transient stimuli at 60 dB SL and 90&#x02013;100 Hz, but at latencies more similar to those presented at 50 dB SL or 50 dB nHL in the presence of some masking noise (<xref rid="bib2" ref-type="bibr">Backer et al., 2019</xref>; <xref rid="bib11" ref-type="bibr">Burkard and Hecox, 1983</xref>; <xref rid="bib13" ref-type="bibr">Chiappa et al., 1979</xref>; <xref rid="bib15" ref-type="bibr">Don et al., 1977</xref>; <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>). There are reasons why the speech-evoked latencies may be later. First, our level of 65 dB sound pressure level (SPL) may be more similar to click levels of 50 dB SL. Second, although spectra of both speech and transients are broad, clicks, chirps, and even our previous speech stimuli (which was high-pass filtered at 1 kHz; <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>) have relatively greater high-frequency energy than the unaltered and peaky broadband speech used in the present work. Neurons with higher characteristic frequencies respond earlier due to their basal cochlear location and contribute relatively more to brainstem responses (e.g., <xref rid="bib1" ref-type="bibr">Abdala and Folsom, 1995</xref>), leading to quicker latencies for stimuli that have greater high-frequency energy. Also consistent with having greater lower frequency energy, our unaltered and peaky speech responses were later than the response from the same speech segments that were high-pass filtered at 1 kHz (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>). In fact, the ABR to broadband peaky speech bore a close resemblance to the summation of each frequency-specific response and the common component to peaky multiband speech (<xref ref-type="fig" rid="fig7">Figure 7</xref>), with peak wave latencies representing the relative contribution of each frequency band. Third, higher stimulation rates prolong latencies due to neural adaptation, and the 115&#x02013;198 Hz average fundamental frequencies of our speech were much higher than the 41 Hz embedded chirps and 50&#x02013;100 Hz click rates (e.g., <xref rid="bib10" ref-type="bibr">Burkard et al., 1990</xref>; <xref rid="bib11" ref-type="bibr">Burkard and Hecox, 1983</xref>; <xref rid="bib13" ref-type="bibr">Chiappa et al., 1979</xref>; <xref rid="bib15" ref-type="bibr">Don et al., 1977</xref>; <xref rid="bib34" ref-type="bibr">Jiang et al., 2009</xref>). The effect of stimulation rate was also demonstrated by the later ABR wave I, III, and V peak latencies for the female narrator with the higher average fundamental frequency of 198 Hz (<xref ref-type="fig" rid="fig6">Figure 6A,&#x000a0;C</xref>). Therefore, the differing characteristics of typical periodic transients (such as clicks and chirps) and continuous speech may give rise to differences in brainstem responses, even though they share canonical waveforms arising from similar contributing subcortical structures.</p><p>The latency of the peaky speech-evoked response also differed from the non-standard, broad responses to unaltered speech. However, latencies from these waveforms are difficult to compare due to the differing morphology and the different analyses that were used to derive the responses. Evidence for the effect of analysis comes from the fact that the same EEG collected in response to peaky speech could be regressed with pulse trains to give canonical ABRs&#x000a0;(<xref ref-type="fig" rid="fig1">Figure 1</xref>, <xref ref-type="fig" rid="fig2">Figure 2</xref>), or regressed with the half-wave rectified peaky speech to give the different, broad waveform (<xref ref-type="fig" rid="fig4">Figure 4</xref>). Furthermore, non-peaky continuous speech stimuli with similar ranges of fundamental frequencies (between 100&#x02013;300 Hz) evoke non-standard, broad brainstem responses that also differ in morphology and latency depending on whether the EEG is analyzed by deconvolution with the half-wave rectified speech (<xref ref-type="fig" rid="fig2">Figure 2</xref>, <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>) or complex cross-correlation with the fundamental frequency waveform (<xref rid="bib21" ref-type="bibr">Forte et al., 2017</xref>). Therefore, again, even though the inferior colliculus and lateral lemniscus may contribute to generating these different responses (<xref rid="bib42" ref-type="bibr">M&#x000f8;ller and Jannetta, 1983</xref>; <xref rid="bib55" ref-type="bibr">Saiz-Al&#x000ed;a and Reichenbach, 2020</xref>; <xref rid="bib59" ref-type="bibr">Starr and Hamilton, 1976</xref>), the morphology and latency may differ (sometimes substantially) depending on the analysis technique used.</p></sec><sec id="s3-1-3"><title>Responses reflect activity from different frequency regions</title><p>In addition to evoking canonical brainstem responses, peaky speech can be exploited for other traditional uses of ABR, such as investigating subcortical responses across different frequencies. Frequency-specific responses were measurable to two different types of multiband peaky speech: four frequency bands presented diotically&#x000a0;(<xref ref-type="fig" rid="fig6">Figure 6</xref>, <xref ref-type="fig" rid="fig8">Figure 8</xref>) and five frequency bands presented dichotically (<xref ref-type="fig" rid="fig9">Figure 9</xref>). Peak wave latencies of these responses decreased with increasing band frequency in a similar way to responses evoked by tone pips and derived&#x000a0;bands from clicks in noise (<xref rid="bib24" ref-type="bibr">Gorga et al., 1988</xref>; <xref rid="bib44" ref-type="bibr">Neely et al., 1988</xref>; <xref rid="bib52" ref-type="bibr">Rasetshwane et al., 2013</xref>; <xref rid="bib60" ref-type="bibr">Strelcyk et al., 2009</xref>), thereby representing activity evoked from different areas across the cochlea. In fact, our estimates of the power law parameters of <italic>a</italic> (the central conduction time), <italic>d</italic> (the frequency dependence), and <italic>b</italic> (the latency corresponding to 1 kHz and 65 dB SPL) for wave V fell within the corresponding ranges that were previously reported for tone pips and derived&#x000a0;bands at 65 dB ppeSPL (<xref rid="bib44" ref-type="bibr">Neely et al., 1988</xref>; <xref rid="bib52" ref-type="bibr">Rasetshwane et al., 2013</xref>; <xref rid="bib60" ref-type="bibr">Strelcyk et al., 2009</xref>). Interestingly, the frequency-specific responses across frequency band were similar in amplitude or possibly slightly smaller for the lower frequency bands&#x000a0;(<xref ref-type="fig" rid="fig6">Figure 6</xref>, <xref ref-type="fig" rid="fig8">Figure 8</xref>, <xref ref-type="fig" rid="fig9">Figure 9</xref>), even though the relative energy of each band decreased with increasing frequency, resulting in an ~30 dB difference between the lowest and highest frequency bands (Figure 14). A greater response elicited by higher frequency bands is consistent with the relatively greater contribution of neurons with higher characteristic frequencies to ABRs (<xref rid="bib1" ref-type="bibr">Abdala and Folsom, 1995</xref>), as well as the need for higher levels to elicit low-frequency responses to tone pips that are close to threshold (<xref rid="bib26" ref-type="bibr">Gorga et al., 2006</xref>; <xref rid="bib25" ref-type="bibr">Gorga et al., 1993</xref>; <xref rid="bib33" ref-type="bibr">Hyde, 2008</xref>; <xref rid="bib58" ref-type="bibr">Stapells and Oates, 1997</xref>). Also, canonical waveforms were derived in the higher frequency bands of diotically presented speech, with waves I and III identifiable in most subjects. Multiband peaky speech will not replace the current frequency-specific ABR, but there are situations where it may be advantageous to use speech over tone pips. Measuring waves I, III, and V of high-frequency responses in the context of all the dynamics of speech may have applications to studying effects of cochlear synaptopathy on speech comprehension (<xref rid="bib7" ref-type="bibr">Bharadwaj et al., 2014</xref>; <xref rid="bib38" ref-type="bibr">Liberman et al., 2016</xref>). Another exciting potential application is the evaluation of supra-threshold hearing across frequency in toddlers and individuals who do not provide reliable behavioral responses as they may be more responsive to sitting for longer periods of time while listening to a narrated story than to a series of tone pips. Giving a squirmy toddler an iPad and presenting a story for 30 min could allow responses to multiband peaky speech that confirm audibility at normal speech levels. Such a screening or metric of audibility is a useful piece of knowledge in pediatric clinical management and is also being investigated for the frequency following response (<xref rid="bib17" ref-type="bibr">Easwar et al., 2020</xref>; <xref rid="bib16" ref-type="bibr">Easwar et al., 2015</xref>), which provides different information than the canonical responses. An extension of this assessment would be to evaluate neural speech processing in the context of hearing loss, as well as rehabilitation strategies such as hearing aids and cochlear implants. Auditory prostheses have algorithms specifically tuned for the spectrotemporal dynamics of speech that behave very differently in response to standard diagnostic stimuli such as trains of clicks or tone pips. Peaky speech responses could allow us to assess how the auditory system is encoding the amplified speech and validate audibility of the hearing aid fittings before the infant or toddler is old enough to provide reliable speech perception testing. Therefore, the ability of peaky speech to yield both canonical waveforms and frequency-specific responses makes this paradigm a flexible method that assesses speech encoding in new ways.</p></sec></sec><sec id="s3-2"><title>Practical considerations for using peaky speech and deconvolution</title><sec id="s3-2-1"><title>Filtering</title><p>Having established that peaky speech is a flexible stimulus for investigating different aspects of speech processing, there are several practical considerations for using the peaky speech paradigm. First, filtering should be performed carefully. As recommended in <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>, causal filters &#x02013; which have impulse responses with non-zero values at positive lags only &#x02013; should be used to ensure cortical activity at later peak latencies does not spuriously influence earlier peaks corresponding to subcortical origins. Applying less aggressive, low-order filters (i.e., broadband with shallow roll-offs) will help reduce the effects of causal filtering on delaying response latency. The choice of high-pass cutoff will also affect the response amplitude and morphology. After evaluating several orders and cutoffs to the high-pass filters, we determined that early waves of the peaky broadband ABRs were best visualized with a 150 Hz cutoff, whereas a lower cutoff frequency of 30 Hz was necessary to view the ABR and MLR of the broadband responses. When evaluating specific contributions to the earliest waves, we recommend at least a first-order 150 Hz high-pass filter or a more aggressive second-order 200 Hz high-pass filter to deal with artifacts arising from nonlinearities that are not taken into account by the pulse train regressor or any potential influences by responses from later sources (<xref ref-type="fig" rid="fig3">Figure 3</xref>). For multiband responses, the 150 Hz high-pass filter significantly reduced the response but also decreased the low-frequency noise in the pre-stimulus interval. For the 4-band multiband peaky speech, the 150 Hz and 30 Hz filters provided similar acquisition times for 0 dB SNR, but better SNRs were obtained quicker with 150 Hz filtering for the 10-band multiband peaky speech.</p></sec><sec id="s3-2-2"><title>Choice of narrator (stimulation rate)</title><p>Second, the choice of narrator impacts the responses to both broadband and multiband peaky speech. Although overall morphology was similar, the male-narrated responses were larger, contained more clearly identifiable component waves in a greater proportion of subjects, and achieved a 0 dB SNR at least 2.1 to over 10 times faster than those evoked by a female narrator. These differences likely stemmed from the&#x000a0;~77 Hz difference in average pitch as higher stimulation rates evoke smaller responses due to adaptation and refractoriness (e.g., <xref rid="bib10" ref-type="bibr">Burkard et al., 1990</xref>; <xref rid="bib11" ref-type="bibr">Burkard and Hecox, 1983</xref>; <xref rid="bib13" ref-type="bibr">Chiappa et al., 1979</xref>; <xref rid="bib15" ref-type="bibr">Don et al., 1977</xref>; <xref rid="bib34" ref-type="bibr">Jiang et al., 2009</xref>). Indeed, a 50 Hz change in fundamental frequency yields a 24% reduction in the modeled ABR that was derived as the complex cross-correlation with the fundamental frequency (<xref rid="bib55" ref-type="bibr">Saiz-Al&#x000ed;a and Reichenbach, 2020</xref>). The narrator differences exhibited in the present study may be larger than those in other studies with continuous speech (<xref rid="bib21" ref-type="bibr">Forte et al., 2017</xref>; <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>; <xref rid="bib54" ref-type="bibr">Saiz-Al&#x000ed;a et al., 2019</xref>) as a result of the different regressors. These response differences do not preclude using narrators with higher fundamental frequencies in future studies, but the time required for usable responses from each narrator must be considered when planning experiments, and caution taken when interpreting comparisons between conditions with differing narrators. The strongest results will come from comparing responses to the same narrator (or even the same speech recordings) under different experimental conditions.</p></sec><sec id="s3-2-3"><title>SNR and recording time for multiple responses</title><p>Third, the necessary recording time depends on the chosen SNR threshold, experimental demands, and stimulus. We chose a threshold SNR of 0 dB based on when waveforms became clearly identifiable, but of course a different threshold would change our recording time estimates (though, notably, not the ratios between them). With this SNR threshold, acquisition times were quick enough for broadband peaky responses to allow multiple conditions in a reasonable recording session. With male-narrated broadband peaky speech, all subjects achieved 0 dB SNR ABRs in&#x000a0;&#x0003c;5 min and MLRs in&#x000a0;&#x0003c;20 min, thereby affording between 3 and 12 conditions in an hour recording session. These recording times are comparable, if not faster, than the 8 min for the broad response to unaltered speech, 6&#x02013;12 min for the chirp-embedded speech (<xref rid="bib2" ref-type="bibr">Backer et al., 2019</xref>),&#x000a0;~10 min for the broad complex-cross correlation response to the fundamental waveform (<xref rid="bib21" ref-type="bibr">Forte et al., 2017</xref>), and 33 min for the broad response to high-passed continuous speech (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>). However, using a narrator with a higher fundamental frequency could increase testing time by two- to over tenfold. In this experiment, at most two conditions per hour could be tested with the female-narrated broadband peaky speech. Unlike broadband peaky speech, the testing times required for all frequency-specific responses to reach 0 dB SNR were significantly longer, making only one condition feasible within a recording session. At least 30 min was necessary for the multiband peaky speech, but based on extrapolated testing times, about 1 hr is required for 90% of subjects and 2 hr for 75% of subjects to achieve this threshold for all 4 bands of diotic speech and all 10 bands of dichotic speech, respectively. It is also possible that there was some contralateral suppression in our dichotic recordings; however, it is unlikely that separate monaural presentation would enlarge responses enough to be worth the doubled recording time. The longer testing times here are important to consider when planning studies using multiband peaky speech with several frequency bands.</p></sec><sec id="s3-2-4"><title>Number of frequency bands</title><p>Fourth, as mentioned above, the number of frequency bands incorporated into multiband peaky speech decreases SNR and increases testing time. Although it is possible to simultaneously record up to 10 frequency-specific responses, the significant time required to obtain decent SNRs reduces the feasibility of testing multiple conditions or having recording sessions lasting less than 1&#x02013;2 hr. However, pursuing shorter testing times with multiband peaky speech is possible. Depending on the experimental question, different multiband options could be considered. For male-narrated speech, the 2&#x02013;4 and 4&#x02013;8 kHz responses had good SNRs and exhibited waves I, III, and V within 9 min for 90% of subjects. Therefore, if researchers were more interested in comparing responses in these higher frequency bands, they could stop recording once these bands reach threshold but before the lower frequency bands reach criterion (i.e., within 9 min). Alternatively, the lower frequencies could be combined into a single broader band in order to reduce the total number of bands or the intensity could be increased to evoke responses with larger amplitudes. Therefore, different band and parameter considerations could reduce testing time and improve the feasibility, and thus utility, of multiband peaky speech.</p></sec><sec id="s3-2-5"><title>Flexible analysis windows for deriving responses from auditory nerve to cortex</title><p>Fifth, and finally, a major advantage of deconvolution analysis is that the analysis window for the response can be extended arbitrarily in either direction to include a broader range of latencies (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>). Extending the pre-stimulus window leftward provides a better estimate of the SNR, and extending the window rightward allows parts of the response that come after the ABR and MLR to be analyzed as well, which are driven by the cortex. These later responses can be evaluated in response to broadband peaky speech, but as shown in <xref ref-type="fig" rid="fig8">Figures 8</xref> and <xref ref-type="fig" rid="fig9">9</xref>, only ABR and early MLR waves are present in the frequency-specific responses. The same broadband peaky speech data from <xref ref-type="fig" rid="fig5">Figure 5</xref> are high-pass filtered at 1 Hz and displayed with an extended time window in <xref ref-type="fig" rid="fig12">Figure 12</xref>, which shows component waves of the ABR, MLR, and late latency responses (LLR). Thus, this method allows us to simultaneously investigate speech processing ranging from the earliest level of the auditory nerve all the way through the cortex without requiring extra recording time. Usually the LLR is larger than the ABR/MLR, but our subjects were encouraged to relax and rest, yielding a passive LLR response. Awake and attentive subjects may improve the LLR; however, other studies that present continuous speech to attentive subjects also report smaller and different LLR (<xref rid="bib2" ref-type="bibr">Backer et al., 2019</xref>; <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>), possibly from cortical adaptation to a continuous stimulus. Here, we used a simple two-channel montage that is optimized for recording ABRs, but a full multichannel montage could also be used to more fully explore the interactions between subcortical and cortical processing of naturalistic speech. The potential for new knowledge about how the brain processes naturalistic and engaging stimuli cannot be undersold.</p><fig id="fig12" orientation="portrait" position="float"><label>Figure 12.</label><caption><title>The range of lags can be extended to allow early, middle, and late latency responses to be analyzed from the same recording to broadband peaky speech.</title><p>Average waveforms across subjects (areas show&#x000a0;&#x000b1;1 SEM) are shown for responses measured to 32 min of broadband peaky speech narrated by a male (dark blue) and female (light blue). Responses were high-pass filtered at 1 Hz using a first-order Butterworth filter, but different filter parameters can be used to focus on each stage of processing. Canonical waves of the auditory brainstem response, middle latency response, and late latency response are labeled for the male-narrated speech. Due to adaptation, amplitudes of the late potentials are smaller than typically seen with other stimuli that are shorter in duration with longer inter-stimulus intervals than our continuous speech. Waves I and III become more clearly visible by applying a 150 Hz high-pass cutoff.</p></caption><graphic xlink:href="elife-62329-fig12"/></fig></sec></sec><sec id="s3-3"><title>Peaky speech is a tool that opens up new lines of query</title><p>The peaky speech paradigm is a viable method for recording broadband and frequency-specific responses from distinct subcortical structures using an engaging, continuous speech stimulus. The customizability and flexibility of peaky speech facilitates new lines of query, both in neuroscientific and clinical domains. Speech often occurs within a mixture of sounds, such as other speech sources, background noise, or music. Furthermore, visual cues from a talker&#x02019;s face are often available to aid speech understanding, particularly in environments with low SNR (e.g., <xref rid="bib6" ref-type="bibr">Bernstein and Grant, 2009</xref>; <xref rid="bib28" ref-type="bibr">Grant et al., 2007</xref>). Peaky speech facilitates investigation into the complex subcortical encoding and processing that underpins successful listening in these scenarios using naturalistic, engaging tasks. Indeed, previous methods have been quite successful in elucidating cortical processing of speech under these conditions (<xref rid="bib45" ref-type="bibr">O'Sullivan et al., 2019</xref>; <xref rid="bib61" ref-type="bibr">Teoh and Lalor, 2019</xref>). Whereas these cortical studies could use regressors that are not acoustically&#x000a0;based &#x02013; such as semantics or surprisal &#x02013; the fast responses of subcortical structures necessitate a regressor that can allow timing of components to separate subcortical from cortical origins. The similarity of the peaky speech response to the click response is an advantage because it is the only way to understand what is occurring in separate subcortical regions during the dynamic spectrotemporal context of speech. As with other work in this area, how informative the response is about speech processing will depend on how it is deployed experimentally. Experiments that measure the response to the same stimuli in different cognitive states (unattended/attended, understood/not understood) will illuminate the relationship between those states and subcortical encoding. Such an approach was recently used to show how the brainstem complex cross-correlation to a fundamental waveform can change based on top-down attention (<xref rid="bib21" ref-type="bibr">Forte et al., 2017</xref>). Finally, the ability to customize peaky speech for measuring frequency-specific responses provides potential applications to clinical research in the context of facilitating assessment of supra-threshold hearing function and changes to how speech may be encoded following intervention strategies and technologies while using a speech stimulus that algorithms in hearing aids and cochlear implants are designed to process.</p></sec></sec><sec sec-type="materials|methods" id="s4"><title>Materials and methods</title><sec id="s4-1"><title>Participants</title><p>Data were collected over three experiments that were conducted under a protocol approved by the University of Rochester Research Subjects Review Board (#1227). All subjects gave informed consent before the experiment began and were compensated for their time. In each of experiments 1 and 2, there were equipment problems during testing for one subject, rendering data unusable in the analyses. Therefore, there were a total of 22, 11, and 11 subjects included in experiments 1, 2, and 3 respectively. Four subjects completed both experiments 1 and 2, and two subjects completed both experiments 2 and 3. The 38 unique subjects (25 females, 66%) were aged 18&#x02013;32 years with a mean &#x000b1; SD age of 23.0&#x000a0;&#x000b1;&#x000a0;3.6 years. Audiometric screening confirmed subjects had normal hearing in both ears, defined as thresholds&#x000a0;&#x02264;20 dB HL from 250 to 8000 Hz. All subjects identified English as their primary language.</p></sec><sec id="s4-2"><title>Stimulus presentation and EEG measurement</title><p>In each experiment, subjects listened to 128 min of continuous speech stimuli while reclined in a darkened sound booth. They were not required to attend to the speech and were encouraged to relax and sleep. Speech was presented at an average level of 65 dB SPL over ER-2 insert earphones (Etymotic Research, Elk Grove, IL) plugged into an RME Babyface Pro digital sound&#x000a0;card (RME, Haimhausen, Germany) via an HB7 headphone amplifier (Tucker Davis Technologies, Alachua, FL). Stimulus presentation was controlled by a custom python (Python Programming Language, RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_008394">SCR_008394</ext-link>) script using publicly available software (Expyfun, RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_019285">SCR_019285</ext-link>; available at <ext-link ext-link-type="uri" xlink:href="https://github.com/LABSN/expyfun">https://github.com/LABSN/expyfun</ext-link>; <xref rid="bib37" ref-type="bibr">Larson et al., 2014</xref>). We interleaved conditions in order to prevent slow impedance drifts or transient periods of higher EEG noise from unevenly affecting one condition over the others. Physical measures to reduce stimulus artifact included (1) hanging earphones from the ceiling so that they were as far away from the EEG cap as possible and (2) sending an inverted signal to a dummy earphone (blocked tube) attached in the same physical orientation to the stimulus presentation earphones in order to cancel electromagnetic fields away from transducers. The sound&#x000a0;card also produced a digital signal at the start of each epoch, which was converted to trigger pulses through a custom trigger box (modified from a design by the National Acoustic Laboratories, Sydney, NSW, Australia) and sent to the EEG system so that audio and EEG data could be synchronized with sub-millisecond precision.</p><p>EEG was recorded using BrainVision&#x02019;s PyCorder software (RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_019286">SCR_019286</ext-link>). Ag/AgCl electrodes were placed at the high forehead (FCz, active non-inverting), left and right earlobes (A1, A2, inverting references), and the frontal pole (Fpz, ground). These were plugged into an EP-Preamp system specifically for recording ABRs, connected to an ActiCHamp recording system, both manufactured by BrainVision. Data were sampled at 10,000 Hz and high-pass filtered at 0.1 Hz. Offline, raw data were high-pass filtered at 1 Hz using a first-order causal Butterworth filter to remove slow drift in the signal, and then notch filtered with 5 Hz wide second-order infinite impulse response notch filters to remove 60 Hz and its first three odd harmonics (180, 300, 420 Hz). To optimize parameters for viewing the ABR and MLR components of peaky speech responses, we evaluated several orders and high-pass cutoffs to the filters. Early waves of the broadband peaky ABRs were best visualized with a 150 Hz cutoff, whereas a lower cutoff frequency of 30 Hz was necessary to view the ABR and MLR of the broadband responses. Conservative filtering with a first-order filter was sufficient with these cutoff frequencies.</p></sec><sec id="s4-3"><title>Speech stimuli and conditions</title><p>Speech stimuli were taken from two audiobooks. The first was <italic>The Alchemyst</italic> (<xref rid="bib56" ref-type="bibr">Scott, 2007</xref>), read by a male narrator and used in all three experiments. The second was <italic>A Wrinkle in Time</italic> (<xref rid="bib39" ref-type="bibr">L&#x02019;Engle, 2012</xref>), read by a female narrator and used in experiments 2 and 3. The average fundamental frequency was 115 Hz for the male narrator and 198 Hz for the female narrator. These stimuli were used in <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>, but in that study a gentle high-pass filter was applied, which was not done for this study. Briefly, the audiobooks were resampled to 44,100 Hz and then silent pauses were truncated to 0.5 s. Speech was segmented into 64 s epochs with 1 s raised cosine fade-in and fade-out. Because conditions were interleaved, the last 4 s of a segment were repeated in the next segment so that subjects could pick up where they left off if they were listening.</p><p>In experiment 1, subjects listened to three conditions of male speech (42.7 min each): unaltered speech, re-synthesized broadband peaky speech, and re-synthesized multiband peaky speech (see below for a description of re-synthesized speech). In experiment 2, subjects listened to four conditions of re-synthesized peaky speech (32 min each): male and female narrators of both broadband and multiband peaky speech. For these first two experiments, speech was presented diotically (same speech to both ears). In experiment 3, subjects listened to both male and female dichotic (slightly different stereo speech of the same narrator in each ear) multiband peaky speech designed for audiological applications (64 min of each narrator). The same 64 s of speech was presented simultaneously to each ear, but the stimuli were dichotic due to how the re-synthesized multiband speech was created (see below).</p></sec><sec id="s4-4"><title>Stimulus design</title><p>The brainstem responds best to impulse-like stimuli, so we re-synthesized the speech segments from the audiobooks (termed &#x02018;unaltered&#x02019;) to create three types of &#x02018;peaky&#x02019; speech, with the objectives of (1) evoking additional waves of the ABR reflecting other neural generators and (2) measuring responses to different frequency regions of the speech. The process is described in detail below but is best read in tandem with the code that is publicly available (<ext-link ext-link-type="uri" xlink:href="https://github.com/maddoxlab">https://github.com/maddoxlab</ext-link>). <xref ref-type="fig" rid="fig13">Figure 13</xref> compares the unaltered speech and re-synthesized broadband and multiband peaky speech. Comparing the pressure waveforms shows that the peaky speech is as click-like as possible, but comparing the spectrograms (how sound varies in amplitude at every frequency and time point) shows that the overall spectrotemporal content that defines speech is basically unchanged by the re-synthesis. <xref ref-type="supplementary-material" rid="supp2">Audio files 1&#x02013;&#x0feff;6</xref> provide examples of each stimulus type for both narrators, which demonstrate the barely perceptible difference between unaltered and peaky speech.</p><fig id="fig13" orientation="portrait" position="float"><label>Figure 13.</label><caption><title>Unaltered speech waveform (top left) and spectrogram (top right) compared to re-synthesized broadband peaky speech (middle left and right) and multiband peaky speech (bottom left and right).</title><p>Comparing waveforms shows that the peaky speech is as &#x02018;click-like&#x02019; as possible, while comparing the spectrograms shows that the overall spectrotemporal content that defines speech is basically unchanged by the re-synthesis. A na&#x000ef;ve listener is unlikely to notice that any modification has been performed, and subjective listening confirms the similarity. Yellow/lighter colors represent larger amplitudes than purple/darker colors in the spectrogram. See supplementary files for audio examples of each stimulus type for both narrators.</p></caption><graphic xlink:href="elife-62329-fig13"/></fig><sec id="s4-4-1"><title>Broadband peaky speech</title><p>Voiced speech comprises rapid openings and closings of the vocal folds, which are then filtered by the mouth and vocal tract to create different vowel and consonant sounds. The first processing step in creating peaky speech was to use speech processing software (PRAAT, RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_016564">SCR_016564</ext-link>; <xref rid="bib8" ref-type="bibr">Boersma and Weenink, 2018</xref>) to extract the times of these glottal pulses. Sections of speech where glottal pulses were within 17 ms of each other were considered voiced (vowels and voiced consonants like /z/), as 17 ms is the longest inter-pulse interval one would expect in natural speech because it is the inverse of 60 Hz, the lowest pitch at which someone with a deep voice would likely speak. A longer gap in pulse times was considered a break between voiced sections. These segments were identified in a &#x02018;mixer&#x02019; function of time, with indices of 1 indicating unvoiced and 0 indicating voiced segments (and would later be responsible for time-dependent blending of re-synthesized and natural speech, hence its name). Transitions of the binary mixer function were smoothed using a raised cosine envelope spanning the time between the first and second pulses, as well as the last two pulses of each voiced segment. During voiced segments, the glottal pulses set the fundamental frequency of speech (i.e., pitch), which were allowed to vary from a minimum to maximum of 60&#x02013;350 Hz for the male narrator and 90&#x02013;500 Hz for the female narrator. For the male and female narrators, these pulses gave a mean &#x000b1; SD fundamental frequency (i.e., pulse rate) in voiced segments of 115.1&#x000a0;&#x000b1;&#x000a0;6.7 Hz and 198.1&#x000a0;&#x000b1;&#x000a0;20 Hz, respectively, and a mean &#x000b1; SD pulses per second over the entire 64 s, inclusive of unvoiced periods and silences, of 69.1&#x000a0;&#x000b1;&#x000a0;5.7 Hz and 110.8&#x000a0;&#x000b1;&#x000a0;11.4, respectively. These pulse times were smoothed using 10 iterations of replacing pulse time <inline-formula><mml:math id="inf4"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> with the mean of pulse times <inline-formula><mml:math id="inf5"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> to <inline-formula><mml:math id="inf6"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> if the <inline-formula><mml:math id="inf7"><mml:mrow><mml:msub><mml:mrow><mml:mi>log</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msub><mml:mo>&#x000a0;</mml:mo></mml:mrow></mml:math></inline-formula> absolute difference in the time between <inline-formula><mml:math id="inf8"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf9"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf10"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> was less than <inline-formula><mml:math id="inf11"><mml:mrow><mml:msub><mml:mrow><mml:mi>log</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mn>1.6</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>.</p><p>The fundamental frequency of voiced speech is dynamic, but the signal always consists of a set of integer-related frequencies (harmonics) with different amplitudes and phases. To create the waveform component at the fundamental frequency, <inline-formula><mml:math id="inf12"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, we first created a phase function, <inline-formula><mml:math id="inf13"><mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, which increased smoothly by <inline-formula><mml:math id="inf14"><mml:mrow><mml:mn>2</mml:mn><mml:mi>&#x003c0;</mml:mi></mml:mrow></mml:math></inline-formula> between glottal pulses within the voiced sections as a result of cubic interpolation. We then computed the spectrogram of the unaltered speech waveform &#x02013; which is a way of analyzing sound that shows its amplitude at every time and frequency (<xref ref-type="fig" rid="fig13">Figure 13</xref>, top&#x000a0;right) &#x02013; that we called <inline-formula><mml:math id="inf15"><mml:mrow><mml:mi>A</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mi>t</mml:mi><mml:mo>,</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi>f</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>. We then created the fundamental component of the peaky speech waveform as<disp-formula id="equ1"><mml:math id="m1"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>h</mml:mi><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mi>A</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mi>t</mml:mi><mml:mo>,</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mi>cos</mml:mi><mml:mo>&#x02061;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mo>.</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>This waveform has an amplitude that changes according to the spectrogram but always peaks at the time of the glottal pulses.</p><p>Next the harmonics of the speech were synthesized. The <inline-formula><mml:math id="inf16"><mml:mi>k</mml:mi></mml:math></inline-formula>&#x000a0;th harmonic of speech is at a frequency of <inline-formula><mml:math id="inf17"><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mn>0</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula> so we synthesized each harmonic waveform as<disp-formula id="equ2"><mml:math id="m2"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>h</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mi>A</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mi>t</mml:mi><mml:mo>,</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mi>cos</mml:mi><mml:mo>&#x02061;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mo>.</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>Each of these harmonic waveforms has multiple peaks per period of the fundamental, but every harmonic also has a peak at exactly the time of the glottal pulse. Because of these coincident peaks, when the harmonics are summed to create the re-synthesized voiced speech, there is always a large peak at the time of the glottal pulse. In other words, the phases of all the harmonics align at each glottal pulse, making the pressure waveform of the speech appear &#x02018;peaky&#x02019; (<xref ref-type="fig" rid="fig13">Figure 13</xref>,&#x000a0;left&#x000a0;middle).</p><p>The resultant re-synthesized speech contained only the voiced segments of speech and was missing unvoiced sounds like /s/ and /k/. Thus the last step was to mix the re-synthesized voiced segments with the original unvoiced parts. This was done by cross-fading back and forth between the unaltered speech and re-synthesized speech during the unvoiced and voiced segments, respectively, using the binary mixer function created when determining where the voiced segments occurred. We also filtered the peaky speech to an upper limit of 8 kHz and used the unaltered speech above 8 kHz to improve the quality of voiced consonants such as /z/. Filter properties for the broadband peaky speech are further described below in the &#x02018;Band filters&#x02019; subsection.</p></sec><sec id="s4-4-2"><title>Multiband peaky speech</title><p>The same principles to generate broadband peaky speech were applied to create stimuli designed to investigate the brainstem&#x02019;s response to different frequency bands that comprise speech. This makes use of the fact that over time speech signals with slightly different <inline-formula><mml:math id="inf18"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mn>0</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula> are independent, or have (nearly) zero cross-correlation, at the lags for the ABR. To make each frequency band of interest independent, we shifted the fundamental frequency and created a fundamental waveform and its harmonics as<disp-formula id="equ3"><mml:math id="m3"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>h</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mi>A</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mi>t</mml:mi><mml:mo>,</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mi>cos</mml:mi><mml:mo>&#x02061;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mo>,</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula>where<disp-formula id="equ4"><mml:math id="m4"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mi>&#x003c6;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mn>2</mml:mn><mml:mi>&#x003c0;</mml:mi><mml:munderover><mml:mo>&#x0222b;</mml:mo><mml:mrow><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>t</mml:mi></mml:mrow></mml:munderover><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>&#x003c4;</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>+</mml:mo><mml:msub><mml:mi>f</mml:mi><mml:mi mathvariant="normal">&#x00394;</mml:mi></mml:msub></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mi>d</mml:mi><mml:mi>&#x003c4;</mml:mi><mml:mo>,</mml:mo></mml:mstyle></mml:mrow></mml:mstyle></mml:math></disp-formula>and where <inline-formula><mml:math id="inf19"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mi mathvariant="normal">&#x00394;</mml:mi></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula> is the small shift in fundamental frequency.</p><p>In these studies, we increased fundamentals for each frequency band by the square root of each successive prime number and subtracting 1, resulting in a few tenths of a hertz difference between bands. The first, lowest frequency band contained the unshifted <inline-formula><mml:math id="inf20"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mn>0</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>. Responses to this lowest, unshifted frequency band showed some differences from the common component for latencies&#x000a0;&#x0003e;30 ms that were not present in the other, higher frequency bands (<xref ref-type="fig" rid="fig6">Figure 6</xref>, 0&#x02013;1 kHz band), suggesting some low-frequency privilege/bias in this response. Therefore, we suggest that the&#x000a0;following studies create independent frequency bands by synthesizing a new fundamental for each band. The static shifts described above could be used, but we suggest an alternative method that introduces random dynamic frequency shifts of up to&#x000a0;&#x000b1;1 Hz over the duration of the stimulus. From this random frequency shift, we can compute a dynamic random phase shift, to which we also add a random starting phase, <inline-formula><mml:math id="inf21"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi mathvariant="normal">&#x00394;</mml:mi></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula>, which is drawn from a uniform distribution between 0 and <inline-formula><mml:math id="inf22"><mml:mrow><mml:mn>2</mml:mn><mml:mi>&#x003c0;</mml:mi></mml:mrow></mml:math></inline-formula>. The phase function from the above set of formulae would be replaced with this random dynamic phase function:<disp-formula id="equ5"><mml:math id="m5"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>&#x003c6;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mn>2</mml:mn><mml:mi>&#x003c0;</mml:mi><mml:munderover><mml:mo>&#x0222b;</mml:mo><mml:mrow><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>t</mml:mi></mml:mrow></mml:munderover><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>&#x003c4;</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>+</mml:mo><mml:msub><mml:mi>f</mml:mi><mml:mi mathvariant="normal">&#x00394;</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi>&#x003c4;</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mi>d</mml:mi><mml:mi>&#x003c4;</mml:mi><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi mathvariant="normal">&#x00394;</mml:mi></mml:msub></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>Validation data from one subject is provided in <xref ref-type="fig" rid="fig6s1">Figure 6&#x02014;figure supplement 1</xref>. Responses from all four bands show more consistent resemblance to the common component, indicating that this method is effective at reducing stimulus-related bias. However, low-frequency-dependent differences remained, suggesting that&#x000a0;there is also unique neural-based low-frequency activity to the speech-evoked responses.</p><p>This re-synthesized speech was then band-pass filtered to the frequency band of interest (e.g., from 0 to 1 kHz or 2&#x02013;4 kHz). This process was repeated for each independent frequency band, the bands were mixed together, and then these re-synthesized voiced parts were mixed with the original unaltered voiceless speech. This peaky speech comprised octave bands with center frequencies of 707, 1414, 2929, and&#x000a0;5656 Hz for experiments 1 and 2, and of 500, 1000, 2000, 4000, and&#x000a0;8000 Hz for experiment 3. Note that for the lowest band the actual center frequency was slightly lower because the filters were set to pass all frequencies below the upper cutoff. Filter properties for these two types of multiband speech are shown in the middle and right panels of Figure 16 and further described below in the &#x02018;Band filters&#x02019; subsection. For the dichotic multiband peaky speech, we created 10 fundamental waveforms &#x02013; two in each of the five filter bands for the two different ears, making the output audio file stereo (or dichotic). We also filtered this dichotic multiband peaky speech to an upper limit of 11.36 kHz to allow for the highest band to have a center frequency of 8 kHz and octave width. The relative mean-squared magnitude in decibels for components of the multiband peaky speech (four filter bands) and dichotic (audiological) multiband peaky speech (five&#x000a0;filter bands) are shown in <xref ref-type="fig" rid="fig14">Figure 14</xref>.</p><fig id="fig14" orientation="portrait" position="float"><label>Figure 14.</label><caption><title>Relative mean-squared magnitude in decibels of multiband peaky speech with four filter bands (left) and five filter bands (right) for male- (dark red circles) and female-narrated (light red triangles) speech.</title><p>The full audio comprises unvoiced and re-synthesized voiced sections, which was presented to the subjects during the experiments. The other bands reflect the relative magnitude of the voiced sections (voiced only), and each filtered frequency band.</p></caption><graphic xlink:href="elife-62329-fig14"/></fig><p>For peaky speech, the re-synthesized speech waveform was presented during the experiment, but the pulse trains were used as the input stimulus for calculating the response (i.e., the regressor, see Response derivation section below). These pulse trains all began and ended together in conjunction with the onset and offset of voiced sections of the speech. To verify which frequency ranges of the multiband pulse trains were independent across frequency bands and would thus yield truly band-specific responses, we conducted spectral coherence analyses on the pulse trains. All 60 unique 64 s sections of each male- and female-narrated multiband peaky speech used in the three experiments were sliced into 1 s segments for a total of 3840 slices. Phase coherence across frequency was then computed across these slices for each combination of pulse trains according to the formula<disp-formula id="equ6"><mml:math id="m6"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:mi>x</mml:mi><mml:mi>y</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:mfrac><mml:mrow><mml:mrow><mml:mo stretchy="false">|</mml:mo></mml:mrow><mml:mtext>&#x000a0;</mml:mtext><mml:mi>E</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mo stretchy="false">|</mml:mo></mml:mrow></mml:mrow><mml:msqrt><mml:mi>E</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mtext>&#x000a0;</mml:mtext><mml:mi>E</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mtext>&#x000a0;</mml:mtext></mml:msqrt></mml:mfrac></mml:mrow></mml:mstyle></mml:math></disp-formula>where <inline-formula><mml:math id="inf23"><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:mi>x</mml:mi><mml:mi>y</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> denotes coherence between bands <inline-formula><mml:math id="inf24"><mml:mi>x</mml:mi></mml:math></inline-formula> and <inline-formula><mml:math id="inf25"><mml:mi>y</mml:mi></mml:math></inline-formula>, <inline-formula><mml:math id="inf26"><mml:mrow><mml:mi>E</mml:mi><mml:mrow><mml:mo>[</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> the average across slices, <inline-formula><mml:math id="inf27"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow></mml:mrow></mml:mstyle></mml:math></inline-formula> the fast Fourier transform, * the&#x000a0;complex conjugation, <inline-formula><mml:math id="inf28"><mml:mrow><mml:msub><mml:mi>x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> the pulse train for slice <inline-formula><mml:math id="inf29"><mml:mi>i</mml:mi></mml:math></inline-formula> in band <inline-formula><mml:math id="inf30"><mml:mrow><mml:mo>&#x000a0;</mml:mo><mml:mi>x</mml:mi></mml:mrow></mml:math></inline-formula>, and <inline-formula><mml:math id="inf31"><mml:mrow><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> the pulse train for slice <inline-formula><mml:math id="inf32"><mml:mi>i</mml:mi></mml:math></inline-formula> in band <inline-formula><mml:math id="inf33"><mml:mi>y</mml:mi></mml:math></inline-formula>.</p><p>Spectral coherence for each narrator is shown in <xref ref-type="fig" rid="fig15">Figure 15</xref>. For the four-band multiband peaky speech used in experiments 1 and 2, there were six pulse train comparisons. For the audiological multiband peaky speech used in experiment 3, there were five&#x000a0;bands for each of the&#x000a0;two ears, resulting in 10 pulse trains and 45 comparisons. All 45 comparisons are shown in <xref ref-type="fig" rid="fig15">Figure 15A</xref> for the stimuli created with static frequency shifts, and the six comparisons for the stimuli created with random dynamic frequency shifts (used in the pilot experiment) are shown in <xref ref-type="fig" rid="fig15">Figure 15B</xref>. Pulse trains were coherent (&#x0003e;0.1) up to a maximum of 71 and 126 Hz for male- and female-narrated speech, respectively, which roughly correspond to the mean &#x000b1; SD pulse rates (calculated as total pulses/64 s) of 69.1&#x000a0;&#x000b1;&#x000a0;5.7 Hz and 110.8&#x000a0;&#x000b1;&#x000a0;11.4, respectively. This means that above ~130 Hz the stimuli were no longer coherent and evoked frequency-specific responses. Importantly, responses would be to correlated stimuli, that&#x000a0;is, not frequency-specific, at frequencies below this cutoff and would result in a low-frequency response component that is present in (or common to) all band responses. There were two separated collections of curves for the stimuli with static frequency shifts, which stemmed from the shifted frequency bands having different coherence with each other than the lowest unshifted band. This stimulus-related bias in the lowest frequency band was reduced by using dynamic random frequency shifts (used in the pilot experiment), as indicated by the similar spectral coherence curves shown in <xref ref-type="fig" rid="fig15">Figure 15B</xref>.</p><fig id="fig15" position="float" orientation="portrait"><label>Figure 15.</label><caption><title>Spectral coherence of pulse trains for multiband peaky speech narrated by a male (left) and female (right).</title><p>Spectral coherence was computed across 1 s slices from 60 unique 64 s multiband peaky speech segments (3840 total slices) for each combination of bands. Each light gray line represents the coherence for one band comparison. (<bold>A</bold>) There were 45 comparisons across the 10-band (audiological) speech used in experiment 3 (5 frequency bands &#x000d7; 2 ears). The lowest band was unshifted, and the other nine bands had static frequency shifts. (<bold>B</bold>) There were six comparisons across four pulse trains of the bands in the pilot experiment, which all had dynamic random frequency shifts. Pulse trains (i.e., the input stimuli, or regressors, for the deconvolution) were frequency-dependent (coherent) below 72 Hz for the male multiband speech and 126 Hz for the female multiband speech.</p></caption><graphic xlink:href="elife-62329-fig15"/><p content-type="supplemental-figure"><fig id="fig15s1" specific-use="child-fig" orientation="portrait" position="anchor"><label>Figure 15&#x02014;figure supplement 1.</label><caption><title>Comparison of the common component derived from the average response to six fake pulse trains that were created using static frequency shifts (solid, darker lines; used in the paper) or dynamic random frequency shifts (dashed, lighter lines, pilot data and suggested in 'Materials&#x000a0;and&#x000a0;methods').</title><p>Responses were to 32 min each of male- (left) and female-narrated&#x000a0;(right) re-synthesized diotic multiband peaky speech. Areas show&#x000a0;&#x000b1;1 SEM. The electroencephalography to diotic multiband peaky speech (four-bands) was regressed with six fake pulse trains created using the static shifts (used in the paper; the same common component displayed in <xref ref-type="fig" rid="fig4">Figure 4</xref>), as well as six fake pulse trains created using the dynamic random frequency shift method (used to create the common component in <xref ref-type="fig" rid="fig15s2">Figure 15&#x02014;figure supplement 2</xref>).</p></caption><graphic xlink:href="elife-62329-fig15-figsupp1"/></fig></p><p content-type="supplemental-figure"><fig id="fig15s2" specific-use="child-fig" orientation="portrait" position="anchor"><label>Figure 15&#x02014;figure supplement 2.</label><caption><title>Multiband stimuli responses for male (left) and female (right) derived by deconvolving the absolute value of the dichotic (stereo) multiband peaky audio (from experiment 3) with the 10 associated pulse trains&#x000a0;&#x02013; five pulse trains were used for each band in each ear (&#x02018;correct&#x02019; pulse trains, top row).</title><p>Each pulse train acted as a &#x02018;wrong&#x02019; pulse train for the associated band in the other ear (bottom row). Left ear responses are shown by dotted lines and right ear responses by solid lines. Higher frequency bands are shown by lighter red colors. The non-zero responses only occurred when the correct pulse train was paired with the correct audio. Both male- and female-narrated speech responses symmetrically surrounded 0 ms and were largest for the first (lowest frequency) band when the correct, but not fake, pulse trains were used as the regressor in the deconvolution.</p></caption><graphic xlink:href="elife-62329-fig15-figsupp2"/></fig></p></fig><p>To identify the effect of the low-frequency stimulus coherence in the responses, we computed the common component across pulse trains by creating an averaged response to six additional &#x02018;fake&#x02019; pulses trains that were created during stimulus design but were not used during the&#x000a0;creation of the multiband peaky speech wav files. The common component was assessed for both &#x02018;fake&#x02019; pulse trains taken from shifts lower than the original fundamental frequency and those taken from shifts higher than the highest &#x02018;true&#x02019; re-synthesized fundamental frequency. For the dynamic frequency shift method (pilot experiment), an additional six pulse trains were created. <xref ref-type="fig" rid="fig15s1">Figure 15&#x02014;figure supplement 1</xref> shows that the common component was similar for &#x02018;fake&#x02019; pulse trains created using static or dynamic random frequency shifts. To assess frequency-specific responses to multiband speech, we subtracted this common component from the band responses. Alternatively, one could simply high-pass the stimuli at 150 Hz using a first-order causal Butterworth filter (being mindful of edge artifacts). However, this high-pass filtering reduces response amplitude and may affect response detection (see Results for more details).</p><p>We also verified the independence of the stimulus bands by treating the regressor pulse train as the input to a system whose output was the rectified stimulus audio and performed deconvolution (see Deconvolution and Response derivation sections below). Further details are provided in <xref ref-type="fig" rid="fig15s2">Figure 15&#x02014;figure supplement 2</xref>. The responses showed that the non-zero responses only occurred when the correct pulse train was paired with the correct audio.</p></sec><sec id="s4-4-3"><title>Band filters</title><p>Because the fundamental frequencies for each frequency band were designed to be independent over time, the band filters for the speech were designed to cross over in frequency at half power. To make the filter, the amplitude was set by taking the square root of the specified power at each frequency. Octave band filters were constructed in the frequency domain by applying trapezoids &#x02013; with center bandwidth and roll-off widths of 0.5 octaves. For the first (lowest frequency) band, all frequencies below the high-pass cutoff were set to 1, and likewise for all frequencies above the low-pass cutoff for the last (highest frequency) band were set to 1 (<xref ref-type="fig" rid="fig16">Figure 16</xref>, top row). The impulse response of the filters was assessed by shifting the inverse fast Fourier transform (IFFT) of the bands so that time zero was in the center, and then applied a Nuttall window, thereby truncating the impulse response to length of 5 ms (<xref ref-type="fig" rid="fig16">Figure 16</xref>,&#x000a0;middle row). The actual frequency response of the filter bands was assessed by taking the fast Fourier transform&#x000a0;(FFT)&#x000a0;of the impulse response and plotting the magnitude (<xref ref-type="fig" rid="fig16">Figure 16</xref>, bottom row).</p><fig id="fig16" orientation="portrait" position="float"><label>Figure 16.</label><caption><title>Octave band filters used to create re-synthesized broadband peaky speech (left, blue), diotic multiband peaky speech with four bands (middle, red), and dichotic multiband peaky speech using five bands with audiological center frequencies (right, red).</title><p>The last band (2nd, 5th, and&#x000a0;6th, respectively, black line) was used to filter the high&#x000a0;frequencies of unaltered speech during mixing to improve the quality of voiced consonants. The designed frequency response using trapezoids (top) was converted into time-domain using IFFT, shifted, and Nuttall windowed to create impulse responses (middle), which were then used to assess the actual frequency response by converting into the frequency domain using FFT (bottom).</p></caption><graphic xlink:href="elife-62329-fig16"/></fig><p>As mentioned above, broadband peaky speech was filtered to an upper limit of 8 kHz for diotic peaky speech and 11.36 kHz for dichotic peaky speech. This band filter was constructed from the second last octave band filter from the multiband filters (i.e., the 4&#x02013;8 kHz band from the top-middle of <xref ref-type="fig" rid="fig16">Figure 16</xref>, dark red line) by setting the amplitude of all frequencies less than the high-pass cutoff frequency to 1 (<xref ref-type="fig" rid="fig16">Figure 16</xref>, top&#x000a0;left, blue line). As mentioned above, unaltered (unvoiced) speech above 8 kHz (diotic) or 11.36 kHz (dichotic) was mixed with the broadband and multiband peaky speech, which was accomplished by applying the last (highest) octave band filter (8+ or 11.36+ kHz band, black line) to the unaltered speech and mixing this band with the re-synthesized speech using the other bands.</p></sec><sec id="s4-4-4"><title>Alternating polarity</title><p>To limit stimulus artifact, we also alternated polarity between segments of speech. To identify regions to flip polarity, the envelope of speech was extracted using a first-order causal Butterworth low-pass filter with a cutoff frequency of 6 Hz applied to the absolute value of the waveform. Then, flip indices were identified where the envelope became less than 1% of the median envelope value, and a function that changed back and forth between 1 and &#x02212;1 at each flip index was created. This function of spikes was smoothed using another first-order causal Butterworth low-pass filter with a cutoff frequency of 10,000 Hz, which was then multiplied with the re-synthesized speech before saving to a wav file.</p></sec></sec><sec id="s4-5"><title>Response derivation</title><sec id="s4-5-1"><title>Deconvolution</title><p>The peaky&#x000a0;speech ABR was derived by using deconvolution, as in previous work (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>), though the computation was performed in the frequency domain for efficiency. The speech was considered the input to a linear system whose output was the recorded EEG signal, with the ABR computed as the system&#x02019;s impulse response. As in <xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>, for the unaltered speech, we used the half-wave rectified audio as the input waveform. Half-wave rectification was accomplished by separately calculating the response to all positive and negative values of the input waveform for each epoch and then combining the responses together during averaging. For our new re-synthesized peaky speech, the input waveform was the sequence of impulses that occurred at the glottal pulse times and corresponded to the peaks in the waveform. <xref ref-type="fig" rid="fig17">Figure 17</xref> shows a section of stimulus and the corresponding input signal of glottal pulses used in the deconvolution.</p><fig id="fig17" orientation="portrait" position="float"><label>Figure 17.</label><caption><title>Example stimulus, regressor, and deconvolved response.</title><p>Left: A segment of broadband peaky speech stimulus (top) and the corresponding glottal pulse train (bottom) used in calculating the broadband peaky speech response. Right: An example broadband peaky speech response from a single subject. The response shows auditory brainstem response waves I, III, and V at ~3, 5, and 7 ms, respectively. It also shows later peaks corresponding to thalamic and cortical activity at ~17 and 27 ms, respectively.</p></caption><graphic xlink:href="elife-62329-fig17"/></fig><p>The half-wave rectified waveforms and glottal pulse sequences were corrected for the small clock differences between the sound card and EEG system and then downsampled to the EEG sampling frequency prior to deconvolution. To avoid temporal splatter due to standard downsampling, the pulse sequences were resampled by placing unit impulses at sample indices closest to each pulse time. Regularization was not necessary because the amplitude spectra of these regressors were sufficiently broadband. For efficiency, the time&#x000a0;domain response waveform, <inline-formula><mml:math id="inf34"><mml:mi>w</mml:mi></mml:math></inline-formula>, for a given 64 s epoch was calculated using frequency&#x000a0;domain division for the deconvolution, with the numerator the cross-spectral density (corresponding to the cross-correlation in the time domain) of the stimulus regressor and EEG response, and the denominator the power spectral density of the stimulus regressor (corresponding to its autocorrelation in the time domain). For a single epoch, that would be<disp-formula id="equ7"><mml:math id="m7"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>w</mml:mi><mml:mo>=</mml:mo><mml:msup><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:mfrac><mml:mrow><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:mi>x</mml:mi><mml:mo>}</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:mi>y</mml:mi><mml:mo>}</mml:mo></mml:mrow><mml:mtext>&#x000a0;</mml:mtext></mml:mrow><mml:mrow><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:mi>x</mml:mi><mml:mo>}</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:mi>x</mml:mi><mml:mo>}</mml:mo></mml:mrow></mml:mrow></mml:mfrac><mml:mo>}</mml:mo></mml:mrow></mml:mrow></mml:mstyle></mml:math></disp-formula>where <inline-formula><mml:math id="inf35"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow></mml:mrow></mml:mstyle></mml:math></inline-formula> denotes the fast Fourier transform, <inline-formula><mml:math id="inf36"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup></mml:mrow></mml:mstyle></mml:math></inline-formula> the inverse fast Fourier transform, * the&#x000a0;complex conjugation, <inline-formula><mml:math id="inf37"><mml:mi>x</mml:mi></mml:math></inline-formula> the input stimulus regressor (half-wave rectified waveform or glottal pulse sequence), and <inline-formula><mml:math id="inf38"><mml:mi>y</mml:mi></mml:math></inline-formula> the EEG data for each epoch. We used methods incorporated into the MNE-python package (RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_005972">SCR_005972</ext-link>; <xref rid="bib27" ref-type="bibr">Gramfort et al., 2013</xref>). In practice, we made adaptations to this formula to improve the SNR with Bayesian-like averaging (see below). For multiband peaky speech, stimuli with slightly different fundamental frequencies will be independent, yielding independent ABRs. Therefore, the same EEG was deconvolved with each band&#x02019;s pulse train to derive the ABR and MLR to each frequency band, and then with an additional six &#x02018;fake&#x02019; pulse trains &#x02013; pulse trains with slightly different fundamental frequencies that were not used to create the presented multiband peaky speech stimuli &#x02013; to derive the common component across bands due to the pulse train coherence at low frequencies (shown in <xref ref-type="fig" rid="fig15">Figure 15</xref>). The averaged response across these six fake pulse trains, or common component, was then subtracted from the multiband responses to identify the frequency-specific band responses.</p></sec><sec id="s4-5-2"><title>Response averaging</title><p>The quality of the ABR waveforms as a function of each type of stimulus was of interest, so we calculated the averaged response after each 64 s epoch. We followed a Bayesian-like process (<xref rid="bib20" ref-type="bibr">Elberling and Wahlgreen, 1985</xref>) to account for variations in noise level across the recording time (such as slow drifts or movement artifacts) and avoid rejecting data based on thresholds. Each epoch was weighted by its inverse variance, <inline-formula><mml:math id="inf39"><mml:mrow><mml:mn>1</mml:mn><mml:mo>/</mml:mo><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mi>i</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:math></inline-formula>, to the sum of the inverse variances of all epochs. Thus, epoch weights,&#x000a0;<inline-formula><mml:math id="inf40"><mml:mrow><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi>b</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, were calculated as follows:<disp-formula id="equ8"><mml:math id="m8"><mml:mrow><mml:msub><mml:mi>b</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mfrac><mml:mrow><mml:mn>1</mml:mn><mml:mo>/</mml:mo><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mi>i</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow><mml:mrow><mml:msubsup><mml:mstyle displaystyle="true" mathsize="140%"><mml:mo>&#x02211;</mml:mo></mml:mstyle><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>n</mml:mi></mml:msubsup><mml:mn>1</mml:mn><mml:mo>/</mml:mo><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mi>i</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mfrac></mml:mrow></mml:math></disp-formula>where <inline-formula><mml:math id="inf41"><mml:mi>i</mml:mi></mml:math></inline-formula> is the epoch number and <inline-formula><mml:math id="inf42"><mml:mi>n</mml:mi></mml:math></inline-formula> is the number of epochs collected. For efficiency, weighted averaging was completed during deconvolution. Because auto-correlation of the input stimulus (denominator of the frequency domain division) was similar across epochs, it was averaged with equal weighting. Therefore, the numerator of the frequency domain division was summed across weighted epochs and the denominator averaged across epochs, according to the following formula:<disp-formula id="equ9"><mml:math id="m9"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>w</mml:mi><mml:mo>=</mml:mo><mml:msup><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:mfrac><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:munderover><mml:msub><mml:mi>b</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow><mml:mtext>&#x000a0;</mml:mtext></mml:mrow><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:munderover><mml:mfrac><mml:mn>1</mml:mn><mml:mi>n</mml:mi></mml:mfrac><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:msup><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mtext>&#x000a0;</mml:mtext><mml:mrow><mml:mi mathvariant="script">&#x02131;</mml:mi></mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>}</mml:mo></mml:mrow></mml:mrow></mml:mfrac><mml:mo>}</mml:mo></mml:mrow></mml:mrow></mml:mstyle></mml:math></disp-formula>where <inline-formula><mml:math id="inf43"><mml:mi>w</mml:mi></mml:math></inline-formula> is the average response waveform, <inline-formula><mml:math id="inf44"><mml:mi>i</mml:mi></mml:math></inline-formula> is again the epoch number,&#x000a0;and <inline-formula><mml:math id="inf45"><mml:mi>n</mml:mi></mml:math></inline-formula> is the number of epochs collected.</p><p>Due to the circular nature of the discrete frequency domain deconvolution, the resulting response has an effective time interval of [0, 32] s at the beginning and [&#x02212;32, 0] s at the end, so that concatenating the two &#x02013; with the end first &#x02013; yields the response from [&#x02212;32, 32] s. Consequently, to avoid edge artifacts, all filtering was performed after the response was shifted to the middle of the 64 s time window. To remove high-frequency noise and some low-frequency noise, the average waveform was band-pass filtered between 30&#x02013;2000 Hz using a first-order causal Butterworth filter. An example of this weighted average response to broadband peaky speech is shown in <xref ref-type="fig" rid="fig17">Figure 17</xref>&#x000a0;(right). This bandwidth of 30&#x02013;2000 Hz is sufficient to identify additional waves in the brainstem and MLR (ABR and MLR, respectively). To further identify earlier waves of the ABRs (i.e., waves I and III), responses were high-pass filtered at 150 Hz using a first-order causal Butterworth filter. This filter was determined to provide the best morphology without compromising the response by comparing responses filtered with common high-pass cutoffs of 1, 30, 50, 100, and 150 Hz each combined with first-, second-, and fourth-order causal Butterworth filters.</p></sec><sec id="s4-5-3"><title>Response normalization</title><p>An advantage of this method over our previous one (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>) is that because the regressor comprises unit impulses the deconvolved response is given in meaningful units, which are the same as the EEG recording, namely microvolts. With a continuous regressor, like the half-wave rectified speech waveform, this is not the case. Therefore, to compare responses to half-wave rectified speech versus glottal pulses, we calculated a normalization factor, <italic>g</italic>, based on data from all subjects:<disp-formula id="equ10"><mml:math id="m10"><mml:mrow><mml:mi>g</mml:mi><mml:mo>=</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mfrac><mml:mrow><mml:mn>1</mml:mn><mml:mo>/</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:msubsup><mml:mstyle displaystyle="true" mathsize="140%"><mml:mo>&#x02211;</mml:mo></mml:mstyle><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>n</mml:mi></mml:msubsup><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>u</mml:mi><mml:mo>,</mml:mo><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mn>1</mml:mn><mml:mo>/</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:msubsup><mml:mstyle displaystyle="true" mathsize="140%"><mml:mo>&#x02211;</mml:mo></mml:mstyle><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>n</mml:mi></mml:msubsup><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>p</mml:mi><mml:mo>,</mml:mo><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfrac></mml:mrow></mml:math></disp-formula>where <inline-formula><mml:math id="inf46"><mml:mi>n</mml:mi></mml:math></inline-formula> is the number of subjects, <inline-formula><mml:math id="inf47"><mml:mrow><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>u</mml:mi><mml:mo>,</mml:mo><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> is the SD of subject <inline-formula><mml:math id="inf48"><mml:mi>i</mml:mi></mml:math></inline-formula>&#x02019;s response to unaltered speech between 0&#x02013;20 ms, and <inline-formula><mml:math id="inf49"><mml:mrow><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>p</mml:mi><mml:mo>,</mml:mo><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> is the same for the broadband peaky speech. Each subject&#x02019;s responses to unaltered speech were multiplied by this normalization factor to bring these responses within a comparable amplitude range as those to broadband peaky speech. Consequently, amplitudes were not compared between responses to unaltered and peaky speech. This was not our prime interest, rather we were interested in latency and presence of canonical component waves. In this study, the normalization factor was 0.26, which cannot be applied to other studies because this number also depends on the scale when storing the digital audio. In our study, this unitless scale was based on a root-mean-square amplitude of 0.01. The same normalization factor was used when the half-wave rectified speech was used as the regressor with EEG collected in response to unaltered speech, broadband peaky speech, and multiband peaky speech (<xref ref-type="fig" rid="fig2">Figure 2</xref>, <xref ref-type="fig" rid="fig4">Figure 4</xref>, <xref ref-type="fig" rid="fig4s1">Figure 4&#x02014;figure supplement 1</xref>).</p></sec><sec id="s4-5-4"><title>Response SNR calculation</title><p>We were also interested in the recording time required to obtain robust responses to re-synthesized peaky speech. Therefore, we calculated the time it took for the ABR and MLR to reach a 0 dB SNR. We chose 0 dB SNR based on visual assessment of when waveforms were easily inspected and based on what we have done previously (<xref rid="bib40" ref-type="bibr">Maddox and Lee, 2018</xref>; <xref rid="bib49" ref-type="bibr">Polonenko and Maddox, 2019</xref>). The SNR of each waveform in dB, <inline-formula><mml:math id="inf50"><mml:mrow><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mi>w</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, was estimated as<disp-formula id="equ11"><mml:math id="m11"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mrow><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mtext>&#x000a0;</mml:mtext><mml:mo>=</mml:mo><mml:mn>10</mml:mn><mml:msub><mml:mi>log</mml:mi><mml:mrow><mml:mn>10</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02061;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mfrac><mml:mrow><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>S</mml:mi><mml:mo>+</mml:mo><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msubsup></mml:mrow><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msubsup></mml:mfrac><mml:mo>]</mml:mo></mml:mrow><mml:mo>,</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula>where <inline-formula><mml:math id="inf51"><mml:mrow><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:mi>S</mml:mi><mml:mo>+</mml:mo><mml:mi>N</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:math></inline-formula> represents the variance (i.e., mean-subtracted energy) of the waveform between 0 and 15 ms or 30 ms for the ABR and MLR, respectively (contains both component signals and noise, <inline-formula><mml:math id="inf52"><mml:mrow><mml:mi>S</mml:mi><mml:mo>+</mml:mo><mml:mi>N</mml:mi></mml:mrow></mml:math></inline-formula>), and <inline-formula><mml:math id="inf53"><mml:mrow><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mi>N</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:math></inline-formula> represents the variance of the noise, <inline-formula><mml:math id="inf54"><mml:mi>N</mml:mi></mml:math></inline-formula>, estimated by averaging the variances of 15 ms (ABR) to 30 ms (MLR) segments of the pre-stimulus baseline between &#x02212;480 and &#x02212;20 ms. Then, the SNR for 1 min of recording, <inline-formula><mml:math id="inf55"><mml:mrow><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mrow><mml:mn>60</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>, was computed from the <inline-formula><mml:math id="inf56"><mml:mrow><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mi>w</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> as<disp-formula id="equ12"><mml:math id="m12"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mrow><mml:mn>60</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mrow><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mn>10</mml:mn><mml:mtext>&#x000a0;</mml:mtext><mml:msub><mml:mi>log</mml:mi><mml:mrow><mml:mn>10</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02061;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mn>60</mml:mn><mml:mrow><mml:mo>/</mml:mo></mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>w</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>]</mml:mo></mml:mrow><mml:mo>,</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula>where <inline-formula><mml:math id="inf57"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mi>w</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the duration of the recording in seconds, as specified in the &#x02018;Speech stimuli and conditions&#x02019; section. For example, in experiment 3, the average waveform resulted from 64 min of recording, or a <inline-formula><mml:math id="inf58"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mi>w</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> of 3840 s. The time to reach 0 dB SNR for each subject, <inline-formula><mml:math id="inf59"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mn>0</mml:mn><mml:mi>d</mml:mi><mml:mi>B</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:mi>R</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>, was estimated from this <inline-formula><mml:math id="inf60"><mml:mrow><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mrow><mml:mn>60</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> by<disp-formula id="equ13"><mml:math id="m13"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mn>0</mml:mn><mml:mi>d</mml:mi><mml:mi>B</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:mi>R</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mn>60</mml:mn><mml:mo>&#x000a0;</mml:mo><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mrow><mml:mn>10</mml:mn></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:msub><mml:mi>R</mml:mi><mml:mrow><mml:mn>60</mml:mn></mml:mrow></mml:msub><mml:mo>/</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:msup><mml:mo>.</mml:mo></mml:mrow></mml:math></disp-formula></p><p>Cumulative density functions were used to show the proportion of subjects that reached an SNR&#x000a0;&#x02265;&#x000a0;0 dB and determine the necessary acquisition times that can be expected for each stimulus on a group level.</p></sec></sec><sec id="s4-6"><title>Statistical analyses</title><sec id="s4-6-1"><title>Waveform morphology</title><p>Data were checked for normality using the Shapiro&#x02013;Wilk test. Waveform morphology of responses to different narrators was compared using Pearson correlations of the responses between 0 and 15 ms for the ABR waveforms or 0 and 40 ms for both ABR and MLR waveforms. The Wilcoxon signed-rank test was used to determine whether narrator differences (waveform correlations) were significantly different than the correlations of the same EEG split into even and odd epochs with equal numbers of epochs from each narrator. For experiment 1, responses containing an equal number of epochs from the first and second half of the recording had a median (interquartile range) correlation coefficient of 0.96 (0.95&#x02013;0.98) for the 0&#x02013;15 ms lags, indicating good replication.</p></sec><sec id="s4-6-2"><title>Peak latencies</title><p>The intraclass correlation coefficient type 3 (absolute agreement) was used to verify good agreement in peak latencies chosen by an experienced audiologist and neuroscientist (MJP) at two different time points, 3 months apart. The ICC3 for all peaks were&#x000a0;&#x02265;0.90, indicating excellent reliability for the&#x000a0;chosen peak latencies.</p><p>Independent t-tests with &#x003bc;&#x000a0;=&#x000a0;0 were conducted on the peak latency differences of ABR/MLR waves for unaltered and broadband peaky speech. For multiband peaky speech, the change in peak latency, <inline-formula><mml:math id="inf61"><mml:mrow><mml:mo>&#x000a0;</mml:mo><mml:mi>&#x003c4;</mml:mi></mml:mrow></mml:math></inline-formula>, with frequency band was modeled using a power law regression (<xref rid="bib31" ref-type="bibr">Harte et al., 2009</xref>; <xref rid="bib44" ref-type="bibr">Neely et al., 1988</xref>; <xref rid="bib52" ref-type="bibr">Rasetshwane et al., 2013</xref>; <xref rid="bib60" ref-type="bibr">Strelcyk et al., 2009</xref>) according to the formula<disp-formula id="equ14"><mml:math id="m14"><mml:mrow><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>f</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mi>a</mml:mi><mml:mo>+</mml:mo><mml:mi>b</mml:mi><mml:msup><mml:mi>f</mml:mi><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:math></disp-formula>where<disp-formula id="equ15"><mml:math id="m15"><mml:mrow><mml:mi>a</mml:mi><mml:mo>=</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>y</mml:mi><mml:mi>n</mml:mi><mml:mi>a</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi><mml:mi>i</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>I</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:msub><mml:mo>,</mml:mo></mml:mrow></mml:math></disp-formula>and where <inline-formula><mml:math id="inf62"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>f</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> is the band center frequency normalized to 1 kHz (i.e., divided by 1000), <inline-formula><mml:math id="inf63"><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>y</mml:mi><mml:mi>n</mml:mi><mml:mi>a</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi><mml:mi>i</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>&#x000a0;is&#x000a0;the synaptic delay (assumed to be 0.8 ms; <xref rid="bib18" ref-type="bibr">Eggermont, 1979</xref>; <xref rid="bib60" ref-type="bibr">Strelcyk et al., 2009</xref>), and <inline-formula><mml:math id="inf64"><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>I</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> is the I&#x02013;V inter-wave delay from the subjects' responses to broadband peaky speech. The power law model was completed for each filter cutoff of 30 and 150 Hz in the log&#x02013;log domain, according to the formula<disp-formula id="equ16"><mml:math id="m16"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>log</mml:mi><mml:mrow><mml:mn>10</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02061;</mml:mo><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>f</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>a</mml:mi></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mtext>&#x000a0;</mml:mtext><mml:msub><mml:mi>log</mml:mi><mml:mrow><mml:mn>10</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02061;</mml:mo><mml:mi>b</mml:mi><mml:mo>+</mml:mo><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>d</mml:mi></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:msub><mml:mi>log</mml:mi><mml:mrow><mml:mn>10</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02061;</mml:mo><mml:mi>f</mml:mi></mml:mrow></mml:mstyle></mml:math></disp-formula>and using linear mixed effects regression to estimate the terms <inline-formula><mml:math id="inf65"><mml:mrow><mml:mo>&#x000a0;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf66"><mml:mi>b</mml:mi></mml:math></inline-formula> (calculated as <inline-formula><mml:math id="inf67"><mml:mi>b</mml:mi></mml:math></inline-formula> = 10<sup>intercept</sup>). For subjects who did not have an identifiable wave I in the broadband peaky response, the group mean I&#x02013;V delay was used &#x02013; this occurred for 1 of 22 subjects in experiment 1 and 2 of 11 subjects for responses to the female narrator in experiment 2. Because only multiband peaky speech was presented in experiment 3, the mean I&#x02013;V intervals from experiment 2 were used for each subject for experiment 3. Random effects of subject and each frequency band term were included to account for individual variability that is not generalizable to the fixed effects. The mixed effects regressions were performed using the lme4 (RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_015654">SCR_015654</ext-link>) and lmerTest (RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_015656">SCR_015656</ext-link>) packages in R (RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_001905">SCR_001905</ext-link>) and RStudio (RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_000432">SCR_000432</ext-link>) (<xref rid="bib5" ref-type="bibr">Bates et al., 2015</xref>; <xref rid="bib36" ref-type="bibr">Kuznetsova et al., 2017</xref>; <xref rid="bib51" ref-type="bibr">R Development Core Team, 2020</xref>). A power analysis was completed using the simR package (RRID:<ext-link ext-link-type="uri" xlink:href="https://identifiers.org/RRID/RRID:SCR_019287">SCR_019287</ext-link>; <xref rid="bib29" ref-type="bibr">Green and MacLeod, 2016</xref>), which uses a likelihood ratio test on 1000 Monte Carlo permutations of the response variables based on the fitted model.</p></sec></sec><sec sec-type="data-availability" id="s4-7"><title>Data availability</title><p>Python code is available on the lab GitHub account (<ext-link ext-link-type="uri" xlink:href="https://github.com/maddoxlab/peaky-speech">https://github.com/maddoxlab/peaky-speech</ext-link>;&#x000a0;<xref rid="bib48" ref-type="bibr">Polonenko, 2021</xref>; copy archived at <ext-link ext-link-type="uri" xlink:href="https://archive.softwareheritage.org/swh:1:dir:4d3bb4efd8291f72c193489adf9ceb62ee980132;origin=https://github.com/maddoxlab/peaky-speech;visit=swh:1:snp:181a76ed2bdf5be24d9397cf8fc287e2879a861e;anchor=swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd/">swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd</ext-link>). All EEG recordings are available in the EEG-BIDS format (<xref rid="bib46" ref-type="bibr">Pernet et al., 2019</xref>) on Dryad (<ext-link ext-link-type="uri" xlink:href="https://doi.org/10.5061/dryad.12jm63xwd">https://doi.org/10.5061/dryad.12jm63xwd</ext-link>). Stimulus files and python code necessary to derive the peaky speech responses are deposited to the same Dryad repository.</p></sec></sec></body><back><sec sec-type="funding-information"><title>Funding Information</title><p>This paper was supported by the following grant:</p><list list-type="bullet"><list-item><p><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000055</institution-id><institution>National Institute on Deafness and Other Communication Disorders</institution></institution-wrap></funding-source>
<award-id>R00DC014288</award-id> to Ross K Maddox.</p></list-item></list></sec><ack id="ack"><title>Acknowledgements</title><p>We thank Sara Fiscella for assistance with recruitment. We would also like to thank the reviewers, whose suggestions significantly strengthened the manuscript. This work was supported by the&#x000a0;National Institute for Deafness and Other Communication Disorders (R00DC014288) awarded to RKM.</p></ack><sec id="s5" sec-type="additional-information"><title>Additional information</title><fn-group content-type="competing-interest"><title><bold>Competing interests</bold></title><fn fn-type="COI-statement" id="conf1"><p>No competing interests declared.</p></fn></fn-group><fn-group content-type="author-contribution"><title><bold>Author contributions</bold></title><fn fn-type="con" id="con1"><p>Conceptualization, Data curation, Software, Formal analysis, Supervision, Validation, Investigation, Visualization, Methodology, Writing - original draft, Project administration, Writing - review and editing.</p></fn><fn fn-type="con" id="con2"><p>Conceptualization, Resources, Software, Formal analysis, Supervision, Funding acquisition, Validation, Investigation, Visualization, Methodology, Writing - original draft, Project administration, Writing - review and editing.</p></fn></fn-group><fn-group content-type="ethics-information"><title><bold>Ethics</bold></title><fn fn-type="other"><p>Human subjects: All subjects gave written informed consent before the experiment began. All experimental procedures were approved by the University of Rochester Research Subjects Review Board. (#1227).</p></fn></fn-group></sec><sec id="s6" sec-type="supplementary-material"><title>Additional files</title><supplementary-material content-type="local-data" id="supp1"><label>Supplementary file 1.</label><caption><title>Details of statistical models.</title><p>(<bold>A</bold>) LMER model formula and summary output for multiband peaky speech in experiment 1.&#x000a0;(<bold>B</bold>) LMER model formula and summary output for multiband peaky speech in experiment 2. (<bold>C</bold>) LMER model formula and summary output for multiband peaky speech in experiment 3.</p></caption><media mime-subtype="docx" mimetype="application" xlink:href="elife-62329-supp1.docx" orientation="portrait" id="d39e3971" position="anchor"/></supplementary-material><supplementary-material content-type="local-data" id="supp2"><label>Audio file 1.</label><caption><title>Unaltered speech sample from the male narrator (<italic>The Alchemyst</italic>; <xref rid="bib56" ref-type="bibr">Scott, 2007</xref>).</title></caption><media mime-subtype="zip" mimetype="application" xlink:href="elife-62329-fig2.zip" orientation="portrait" id="d39e3984" position="anchor"/></supplementary-material><supplementary-material content-type="local-data" id="supp3"><label>Audio file 2.</label><caption><title>Broadband peaky speech sample from the male narrator (<italic>The Alchemyst</italic>; <xref rid="bib56" ref-type="bibr">Scott, 2007</xref>).</title></caption><media mime-subtype="zip" mimetype="application" xlink:href="elife-62329-fig3.zip" orientation="portrait" id="d39e3997" position="anchor"/></supplementary-material><supplementary-material content-type="local-data" id="supp4"><label>Audio file 3.</label><caption><title>Multiband peaky speech sample from the male narrator (<italic>The Alchemyst</italic>; <xref rid="bib56" ref-type="bibr">Scott, 2007</xref>).</title></caption><media mime-subtype="zip" mimetype="application" xlink:href="elife-62329-fig4.zip" orientation="portrait" id="d39e4010" position="anchor"/></supplementary-material><supplementary-material content-type="local-data" id="supp5"><label>Audio file 4.</label><caption><title>Unaltered speech sample from the female narrator (<italic>A Wrinkle in Time</italic>; <xref rid="bib39" ref-type="bibr">L&#x02019;Engle, 2012</xref>).</title></caption><media mime-subtype="zip" mimetype="application" xlink:href="elife-62329-fig5.zip" orientation="portrait" id="d39e4023" position="anchor"/></supplementary-material><supplementary-material content-type="local-data" id="supp6"><label>Audio file 5.</label><caption><title>Broadband peaky speech sample from the female narrator (<italic>A Wrinkle in Time</italic>; <xref rid="bib39" ref-type="bibr">L&#x02019;Engle, 2012</xref>).</title></caption><media mime-subtype="zip" mimetype="application" xlink:href="elife-62329-fig6.zip" orientation="portrait" id="d39e4036" position="anchor"/></supplementary-material><supplementary-material content-type="local-data" id="supp7"><label>Audio file 6.</label><caption><title>Multiband peaky speech sample from the female narrator (<italic>A Wrinkle in Time</italic>; <xref rid="bib39" ref-type="bibr">L&#x02019;Engle, 2012</xref>).</title></caption><media mime-subtype="zip" mimetype="application" xlink:href="elife-62329-fig7.zip" orientation="portrait" id="d39e4049" position="anchor"/></supplementary-material><supplementary-material content-type="local-data" id="transrepform"><label>Transparent reporting form</label><media mime-subtype="pdf" mimetype="application" xlink:href="elife-62329-transrepform.pdf" orientation="portrait" id="d39e4053" position="anchor"/></supplementary-material></sec><sec id="s7" sec-type="data-availability"><title>Data availability</title><p>Python code is available on the lab GitHub account (<ext-link ext-link-type="uri" xlink:href="https://github.com/maddoxlab/peaky-speech">https://github.com/maddoxlab/peaky-speech</ext-link>; copy archived at <ext-link ext-link-type="uri" xlink:href="https://archive.softwareheritage.org/swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd/">https://archive.softwareheritage.org/swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd/</ext-link>). All EEG recordings are posted in the EEG-BIDS format (Pernet et al., 2019) to Dryad (<ext-link ext-link-type="uri" xlink:href="https://doi.org/10.5061/dryad.12jm63xwd">https://doi.org/10.5061/dryad.12jm63xwd</ext-link>). Stimulus files necessary to derive the peaky speech responses are deposited in the same Dryad repository.</p><p>The following dataset was generated:</p><p><element-citation publication-type="data" id="dataset1"><person-group person-group-type="author"><name><surname>Polonenko</surname><given-names>MJ</given-names></name><name><surname>Maddox</surname><given-names>RK</given-names></name></person-group><year iso-8601-date="2020">2020</year><data-title>Exposing distinct subcortical components of the auditory brainstem response evoked by continuous naturalistic speech</data-title><source>Dryad Digital Repository</source><pub-id pub-id-type="doi">10.5061/dryad.12jm63xwd</pub-id></element-citation></p></sec><ref-list><title>References</title><ref id="bib1"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abdala</surname><given-names>C</given-names></name><name><surname>Folsom</surname><given-names>RC</given-names></name></person-group><year>1995</year><article-title>Frequency contribution to the click-evoked auditory brain-stem response in human adults and infants</article-title><source>The Journal of the Acoustical Society of America</source><volume>97</volume><fpage>2394</fpage><lpage>2404</lpage><pub-id pub-id-type="doi">10.1121/1.411961</pub-id><?supplied-pmid 7714257?><pub-id pub-id-type="pmid">7714257</pub-id></element-citation></ref><ref id="bib2"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Backer</surname><given-names>KC</given-names></name><name><surname>Kessler</surname><given-names>AS</given-names></name><name><surname>Lawyer</surname><given-names>LA</given-names></name><name><surname>Corina</surname><given-names>DP</given-names></name><name><surname>Miller</surname><given-names>LM</given-names></name></person-group><year>2019</year><article-title>A novel EEG paradigm to simultaneously and rapidly assess the functioning of auditory and visual pathways</article-title><source>Journal of Neurophysiology</source><volume>122</volume><fpage>1312</fpage><lpage>1329</lpage><pub-id pub-id-type="doi">10.1152/jn.00868.2018</pub-id><?supplied-pmid 31268796?><pub-id pub-id-type="pmid">31268796</pub-id></element-citation></ref><ref id="bib3"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bajo</surname><given-names>VM</given-names></name><name><surname>Nodal</surname><given-names>FR</given-names></name><name><surname>Moore</surname><given-names>DR</given-names></name><name><surname>King</surname><given-names>AJ</given-names></name></person-group><year>2010</year><article-title>The descending corticocollicular pathway mediates learning-induced auditory plasticity</article-title><source>Nature Neuroscience</source><volume>13</volume><fpage>253</fpage><lpage>260</lpage><pub-id pub-id-type="doi">10.1038/nn.2466</pub-id><?supplied-pmid 20037578?><pub-id pub-id-type="pmid">20037578</pub-id></element-citation></ref><ref id="bib4"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bajo</surname><given-names>VM</given-names></name><name><surname>King</surname><given-names>AJ</given-names></name></person-group><year>2012</year><article-title>Cortical modulation of auditory processing in the midbrain</article-title><source>Frontiers in Neural Circuits</source><volume>6</volume><elocation-id>114</elocation-id><pub-id pub-id-type="doi">10.3389/fncir.2012.00114</pub-id><?supplied-pmid 23316140?><pub-id pub-id-type="pmid">23316140</pub-id></element-citation></ref><ref id="bib5"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bates</surname><given-names>D</given-names></name><name><surname>M&#x000e4;chler</surname><given-names>M</given-names></name><name><surname>Bolker</surname><given-names>B</given-names></name><name><surname>Walker</surname><given-names>S</given-names></name></person-group><year>2015</year><article-title>Fitting linear Mixed-Effects models using lme4</article-title><source>Journal of Statistical Software</source><volume>67</volume><fpage>1</fpage><lpage>48</lpage></element-citation></ref><ref id="bib6"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bernstein</surname><given-names>JG</given-names></name><name><surname>Grant</surname><given-names>KW</given-names></name></person-group><year>2009</year><article-title>Auditory and auditory-visual intelligibility of speech in fluctuating maskers for normal-hearing and hearing-impaired listeners</article-title><source>The Journal of the Acoustical Society of America</source><volume>125</volume><fpage>3358</fpage><lpage>3372</lpage><pub-id pub-id-type="doi">10.1121/1.3110132</pub-id><?supplied-pmid 19425676?><pub-id pub-id-type="pmid">19425676</pub-id></element-citation></ref><ref id="bib7"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bharadwaj</surname><given-names>HM</given-names></name><name><surname>Verhulst</surname><given-names>S</given-names></name><name><surname>Shaheen</surname><given-names>L</given-names></name><name><surname>Liberman</surname><given-names>MC</given-names></name><name><surname>Shinn-Cunningham</surname><given-names>BG</given-names></name></person-group><year>2014</year><article-title>Cochlear neuropathy and the coding of supra-threshold sound</article-title><source>Frontiers in Systems Neuroscience</source><volume>8</volume><elocation-id>26</elocation-id><pub-id pub-id-type="doi">10.3389/fnsys.2014.00026</pub-id><?supplied-pmid 24600357?><pub-id pub-id-type="pmid">24600357</pub-id></element-citation></ref><ref id="bib8"><element-citation publication-type="software"><person-group person-group-type="author"><name><surname>Boersma</surname><given-names>P</given-names></name><name><surname>Weenink</surname><given-names>D</given-names></name></person-group><year>2018</year><source>Praat: Doing Phonetics by Computer</source></element-citation></ref><ref id="bib9"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bramhall</surname><given-names>N</given-names></name><name><surname>Beach</surname><given-names>EF</given-names></name><name><surname>Epp</surname><given-names>B</given-names></name><name><surname>Le Prell</surname><given-names>CG</given-names></name><name><surname>Lopez-Poveda</surname><given-names>EA</given-names></name><name><surname>Plack</surname><given-names>CJ</given-names></name><name><surname>Schaette</surname><given-names>R</given-names></name><name><surname>Verhulst</surname><given-names>S</given-names></name><name><surname>Canlon</surname><given-names>B</given-names></name></person-group><year>2019</year><article-title>The search for noise-induced cochlear synaptopathy in humans: mission impossible?</article-title><source>Hearing Research</source><volume>377</volume><fpage>88</fpage><lpage>103</lpage><pub-id pub-id-type="doi">10.1016/j.heares.2019.02.016</pub-id><?supplied-pmid 30921644?><pub-id pub-id-type="pmid">30921644</pub-id></element-citation></ref><ref id="bib10"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Burkard</surname><given-names>R</given-names></name><name><surname>Shi</surname><given-names>Y</given-names></name><name><surname>Hecox</surname><given-names>KE</given-names></name></person-group><year>1990</year><article-title>A comparison of maximum length and Legendre sequences for the derivation of brain-stem auditory-evoked responses at rapid rates of stimulation</article-title><source>The Journal of the Acoustical Society of America</source><volume>87</volume><fpage>1656</fpage><lpage>1664</lpage><pub-id pub-id-type="doi">10.1121/1.399413</pub-id><?supplied-pmid 2341669?><pub-id pub-id-type="pmid">2341669</pub-id></element-citation></ref><ref id="bib11"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Burkard</surname><given-names>R</given-names></name><name><surname>Hecox</surname><given-names>K</given-names></name></person-group><year>1983</year><article-title>The effect of broadband noise on the human brainstem auditory evoked response. I. rate and intensity effects</article-title><source>The Journal of the Acoustical Society of America</source><volume>74</volume><fpage>1204</fpage><lpage>1213</lpage><pub-id pub-id-type="doi">10.1121/1.390024</pub-id><?supplied-pmid 6643843?><pub-id pub-id-type="pmid">6643843</pub-id></element-citation></ref><ref id="bib12"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Carney</surname><given-names>LH</given-names></name><name><surname>Li</surname><given-names>T</given-names></name><name><surname>McDonough</surname><given-names>JM</given-names></name></person-group><year>2015</year><article-title>Speech coding in the brain: representation of vowel formants by midbrain neurons tuned to sound fluctuations</article-title><source>Eneuro</source><volume>2</volume><elocation-id>ENEURO.0004-15.2015</elocation-id><pub-id pub-id-type="doi">10.1523/ENEURO.0004-15.2015</pub-id><?supplied-pmid 26464993?><pub-id pub-id-type="pmid">26464993</pub-id></element-citation></ref><ref id="bib13"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chiappa</surname><given-names>KH</given-names></name><name><surname>Gladstone</surname><given-names>KJ</given-names></name><name><surname>Young</surname><given-names>RR</given-names></name></person-group><year>1979</year><article-title>Brain stem auditory evoked responses: studies of waveform variations in 50 normal human subjects</article-title><source>Archives of Neurology</source><volume>36</volume><fpage>81</fpage><lpage>87</lpage><pub-id pub-id-type="doi">10.1001/archneur.1979.00500380051005</pub-id><?supplied-pmid 420627?><pub-id pub-id-type="pmid">420627</pub-id></element-citation></ref><ref id="bib14"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dau</surname><given-names>T</given-names></name><name><surname>Wegner</surname><given-names>O</given-names></name><name><surname>Mellert</surname><given-names>V</given-names></name><name><surname>Kollmeier</surname><given-names>B</given-names></name></person-group><year>2000</year><article-title>Auditory brainstem responses with optimized chirp signals compensating basilar-membrane dispersion</article-title><source>The Journal of the Acoustical Society of America</source><volume>107</volume><fpage>1530</fpage><lpage>1540</lpage><pub-id pub-id-type="doi">10.1121/1.428438</pub-id><?supplied-pmid 10738807?><pub-id pub-id-type="pmid">10738807</pub-id></element-citation></ref><ref id="bib15"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Don</surname><given-names>M</given-names></name><name><surname>Allen</surname><given-names>AR</given-names></name><name><surname>Starr</surname><given-names>A</given-names></name></person-group><year>1977</year><article-title>Effect of click rate on the latency of auditory brain stem responses in humans</article-title><source>Annals of Otology, Rhinology &#x00026; Laryngology</source><volume>86</volume><fpage>186</fpage><lpage>195</lpage><pub-id pub-id-type="doi">10.1177/000348947708600209</pub-id></element-citation></ref><ref id="bib16"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Easwar</surname><given-names>V</given-names></name><name><surname>Purcell</surname><given-names>DW</given-names></name><name><surname>Aiken</surname><given-names>SJ</given-names></name><name><surname>Parsa</surname><given-names>V</given-names></name><name><surname>Scollie</surname><given-names>SD</given-names></name></person-group><year>2015</year><article-title>Evaluation of Speech-Evoked envelope following responses as an objective aided outcome measure: effect of stimulus level, bandwidth, and amplification in adults with hearing loss</article-title><source>Ear and Hearing</source><volume>36</volume><fpage>635</fpage><lpage>652</lpage><pub-id pub-id-type="doi">10.1097/AUD.0000000000000199</pub-id><?supplied-pmid 26226606?><pub-id pub-id-type="pmid">26226606</pub-id></element-citation></ref><ref id="bib17"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Easwar</surname><given-names>V</given-names></name><name><surname>Scollie</surname><given-names>S</given-names></name><name><surname>Aiken</surname><given-names>S</given-names></name><name><surname>Purcell</surname><given-names>D</given-names></name></person-group><year>2020</year><article-title>Test-Retest variability in the characteristics of envelope following responses evoked by speech stimuli</article-title><source>Ear &#x00026; Hearing</source><volume>41</volume><fpage>150</fpage><lpage>164</lpage><pub-id pub-id-type="doi">10.1097/AUD.0000000000000739</pub-id><?supplied-pmid 31136317?><pub-id pub-id-type="pmid">31136317</pub-id></element-citation></ref><ref id="bib18"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eggermont</surname><given-names>JJ</given-names></name></person-group><year>1979</year><article-title>Narrow-band AP latencies in normal and recruiting human ears</article-title><source>The Journal of the Acoustical Society of America</source><volume>65</volume><fpage>463</fpage><lpage>470</lpage><pub-id pub-id-type="doi">10.1121/1.382345</pub-id><?supplied-pmid 489815?><pub-id pub-id-type="pmid">489815</pub-id></element-citation></ref><ref id="bib19"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Elberling</surname><given-names>C</given-names></name><name><surname>Don</surname><given-names>M</given-names></name></person-group><year>2008</year><article-title>Auditory brainstem responses to a chirp stimulus designed from derived-band latencies in normal-hearing subjects</article-title><source>The Journal of the Acoustical Society of America</source><volume>124</volume><fpage>3022</fpage><lpage>3037</lpage><pub-id pub-id-type="doi">10.1121/1.2990709</pub-id><?supplied-pmid 19045789?><pub-id pub-id-type="pmid">19045789</pub-id></element-citation></ref><ref id="bib20"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Elberling</surname><given-names>C</given-names></name><name><surname>Wahlgreen</surname><given-names>O</given-names></name></person-group><year>1985</year><article-title>Estimation of auditory brainstem response, ABR, by means of bayesian inference</article-title><source>Scandinavian Audiology</source><volume>14</volume><fpage>89</fpage><lpage>96</lpage><pub-id pub-id-type="doi">10.3109/01050398509045928</pub-id><?supplied-pmid 4023604?><pub-id pub-id-type="pmid">4023604</pub-id></element-citation></ref><ref id="bib21"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Forte</surname><given-names>AE</given-names></name><name><surname>Etard</surname><given-names>O</given-names></name><name><surname>Reichenbach</surname><given-names>T</given-names></name></person-group><year>2017</year><article-title>The human auditory brainstem response to running speech reveals a subcortical mechanism for selective attention</article-title><source>eLife</source><volume>6</volume><elocation-id>e27203</elocation-id><pub-id pub-id-type="doi">10.7554/eLife.27203</pub-id><?supplied-pmid 28992445?><pub-id pub-id-type="pmid">28992445</pub-id></element-citation></ref><ref id="bib22"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Geisler</surname><given-names>CD</given-names></name><name><surname>Frishkopf</surname><given-names>LS</given-names></name><name><surname>Rosenblith</surname><given-names>WA</given-names></name></person-group><year>1958</year><article-title>Extracranial responses to acoustic clicks in man</article-title><source>Science</source><volume>128</volume><fpage>1210</fpage><lpage>1211</lpage><pub-id pub-id-type="doi">10.1126/science.128.3333.1210</pub-id><?supplied-pmid 13592309?><pub-id pub-id-type="pmid">13592309</pub-id></element-citation></ref><ref id="bib23"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Goldstein</surname><given-names>R</given-names></name><name><surname>Rodman</surname><given-names>LB</given-names></name></person-group><year>1967</year><article-title>Early components of averaged evoked responses to rapidly repeated auditory stimuli</article-title><source>Journal of Speech and Hearing Research</source><volume>10</volume><fpage>697</fpage><lpage>705</lpage><pub-id pub-id-type="doi">10.1044/jshr.1004.697</pub-id><?supplied-pmid 5586935?><pub-id pub-id-type="pmid">5586935</pub-id></element-citation></ref><ref id="bib24"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gorga</surname><given-names>MP</given-names></name><name><surname>Kaminski</surname><given-names>JR</given-names></name><name><surname>Beauchaine</surname><given-names>KA</given-names></name><name><surname>Jesteadt</surname><given-names>W</given-names></name></person-group><year>1988</year><article-title>Auditory brainstem responses to tone bursts in normally hearing subjects</article-title><source>Journal of Speech, Language, and Hearing Research</source><volume>31</volume><fpage>87</fpage><lpage>97</lpage><pub-id pub-id-type="doi">10.1044/jshr.3101.87</pub-id></element-citation></ref><ref id="bib25"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gorga</surname><given-names>MP</given-names></name><name><surname>Kaminski</surname><given-names>JR</given-names></name><name><surname>Beauchaine</surname><given-names>KL</given-names></name><name><surname>Bergman</surname><given-names>BM</given-names></name></person-group><year>1993</year><article-title>A comparison of auditory brain stem response thresholds and latencies elicited by air- and bone-conducted stimuli</article-title><source>Ear and Hearing</source><volume>14</volume><fpage>85</fpage><lpage>94</lpage><pub-id pub-id-type="doi">10.1097/00003446-199304000-00003</pub-id><?supplied-pmid 8472882?><pub-id pub-id-type="pmid">8472882</pub-id></element-citation></ref><ref id="bib26"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gorga</surname><given-names>MP</given-names></name><name><surname>Johnson</surname><given-names>TA</given-names></name><name><surname>Kaminski</surname><given-names>JR</given-names></name><name><surname>Beauchaine</surname><given-names>KL</given-names></name><name><surname>Garner</surname><given-names>CA</given-names></name><name><surname>Neely</surname><given-names>ST</given-names></name></person-group><year>2006</year><article-title>Using a combination of click- and tone burst-evoked auditory brain stem response measurements to estimate pure-tone thresholds</article-title><source>Ear &#x00026; Hearing</source><volume>27</volume><fpage>60</fpage><lpage>74</lpage><pub-id pub-id-type="doi">10.1097/01.aud.0000194511.14740.9c</pub-id><?supplied-pmid 16446565?><pub-id pub-id-type="pmid">16446565</pub-id></element-citation></ref><ref id="bib27"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gramfort</surname><given-names>A</given-names></name><name><surname>Luessi</surname><given-names>M</given-names></name><name><surname>Larson</surname><given-names>E</given-names></name><name><surname>Engemann</surname><given-names>DA</given-names></name><name><surname>Strohmeier</surname><given-names>D</given-names></name><name><surname>Brodbeck</surname><given-names>C</given-names></name><name><surname>Goj</surname><given-names>R</given-names></name><name><surname>Jas</surname><given-names>M</given-names></name><name><surname>Brooks</surname><given-names>T</given-names></name><name><surname>Parkkonen</surname><given-names>L</given-names></name><name><surname>H&#x000e4;m&#x000e4;l&#x000e4;inen</surname><given-names>M</given-names></name></person-group><year>2013</year><article-title>MEG and EEG data analysis with MNE-Python</article-title><source>Frontiers in Neuroscience</source><volume>7</volume><elocation-id>267</elocation-id><pub-id pub-id-type="doi">10.3389/fnins.2013.00267</pub-id><?supplied-pmid 24431986?><pub-id pub-id-type="pmid">24431986</pub-id></element-citation></ref><ref id="bib28"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grant</surname><given-names>KW</given-names></name><name><surname>Tufts</surname><given-names>JB</given-names></name><name><surname>Greenberg</surname><given-names>S</given-names></name></person-group><year>2007</year><article-title>Integration efficiency for speech perception within and across sensory modalities by normal-hearing and hearing-impaired individuals</article-title><source>The Journal of the Acoustical Society of America</source><volume>121</volume><fpage>1164</fpage><lpage>1176</lpage><pub-id pub-id-type="doi">10.1121/1.2405859</pub-id><?supplied-pmid 17348537?><pub-id pub-id-type="pmid">17348537</pub-id></element-citation></ref><ref id="bib29"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Green</surname><given-names>P</given-names></name><name><surname>MacLeod</surname><given-names>CJ</given-names></name></person-group><year>2016</year><article-title>SIMR : an R package for power analysis of generalized linear mixed models by simulation</article-title><source>Methods in Ecology and Evolution</source><volume>7</volume><fpage>493</fpage><lpage>498</lpage><pub-id pub-id-type="doi">10.1111/2041-210X.12504</pub-id></element-citation></ref><ref id="bib30"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grothe</surname><given-names>B</given-names></name><name><surname>Pecka</surname><given-names>M</given-names></name></person-group><year>2014</year><article-title>The natural history of sound localization in mammals--a story of neuronal inhibition</article-title><source>Frontiers in Neural Circuits</source><volume>8</volume><elocation-id>116</elocation-id><pub-id pub-id-type="doi">10.3389/fncir.2014.00116</pub-id><?supplied-pmid 25324726?><pub-id pub-id-type="pmid">25324726</pub-id></element-citation></ref><ref id="bib31"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Harte</surname><given-names>JM</given-names></name><name><surname>Pigasse</surname><given-names>G</given-names></name><name><surname>Dau</surname><given-names>T</given-names></name></person-group><year>2009</year><article-title>Comparison of cochlear delay estimates using otoacoustic emissions and auditory brainstem responses</article-title><source>The Journal of the Acoustical Society of America</source><volume>126</volume><fpage>1291</fpage><lpage>1301</lpage><pub-id pub-id-type="doi">10.1121/1.3168508</pub-id><?supplied-pmid 19739743?><pub-id pub-id-type="pmid">19739743</pub-id></element-citation></ref><ref id="bib32"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hashimoto</surname><given-names>I</given-names></name></person-group><year>1982</year><article-title>Auditory evoked potentials from the human midbrain: slow brain stem responses</article-title><source>Electroencephalography and Clinical Neurophysiology</source><volume>53</volume><fpage>652</fpage><lpage>657</lpage><pub-id pub-id-type="doi">10.1016/0013-4694(82)90141-9</pub-id><?supplied-pmid 6177510?><pub-id pub-id-type="pmid">6177510</pub-id></element-citation></ref><ref id="bib33"><element-citation publication-type="software"><person-group person-group-type="author"><name><surname>Hyde</surname><given-names>M</given-names></name></person-group><year>2008</year><source>Ontario Infant Hearing Program Audiologic Assessment Protocol</source><version designator="3.1">3.1</version></element-citation></ref><ref id="bib34"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jiang</surname><given-names>ZD</given-names></name><name><surname>Wu</surname><given-names>YY</given-names></name><name><surname>Wilkinson</surname><given-names>AR</given-names></name></person-group><year>2009</year><article-title>Age-related changes in BAER at different click rates from neonates to adults</article-title><source>Acta Paediatrica</source><volume>98</volume><fpage>1284</fpage><lpage>1287</lpage><pub-id pub-id-type="doi">10.1111/j.1651-2227.2009.01312.x</pub-id><?supplied-pmid 19397545?><pub-id pub-id-type="pmid">19397545</pub-id></element-citation></ref><ref id="bib35"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kileny</surname><given-names>P</given-names></name><name><surname>Paccioretti</surname><given-names>D</given-names></name><name><surname>Wilson</surname><given-names>AF</given-names></name></person-group><year>1987</year><article-title>Effects of cortical lesions on middle-latency auditory evoked responses (MLR)</article-title><source>Electroencephalography and Clinical Neurophysiology</source><volume>66</volume><fpage>108</fpage><lpage>120</lpage><pub-id pub-id-type="doi">10.1016/0013-4694(87)90180-5</pub-id><?supplied-pmid 2431875?><pub-id pub-id-type="pmid">2431875</pub-id></element-citation></ref><ref id="bib36"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kuznetsova</surname><given-names>A</given-names></name><name><surname>Brockhoff</surname><given-names>PB</given-names></name><name><surname>Christensen</surname><given-names>RHB</given-names></name></person-group><year>2017</year><article-title>lmerTest Package: Tests in Linear Mixed Effects Models</article-title><source>Journal of Statistical Software</source><volume>82</volume><fpage>1</fpage><lpage>26</lpage><pub-id pub-id-type="doi">10.18637/jss.v082.i13</pub-id></element-citation></ref><ref id="bib37"><element-citation publication-type="software"><person-group person-group-type="author"><name><surname>Larson</surname><given-names>E</given-names></name><name><surname>McCloy</surname><given-names>D</given-names></name><name><surname>Maddox</surname><given-names>R</given-names></name><name><surname>Pospisil</surname><given-names>D</given-names></name></person-group><year>2014</year><data-title>expyfun: Python experimental paradigm functions</data-title><source>Zenodo</source><version designator="2.0.0">2.0.0</version><pub-id pub-id-type="doi">10.5281/zenodo.11640</pub-id></element-citation></ref><ref id="bib38"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liberman</surname><given-names>MC</given-names></name><name><surname>Epstein</surname><given-names>MJ</given-names></name><name><surname>Cleveland</surname><given-names>SS</given-names></name><name><surname>Wang</surname><given-names>H</given-names></name><name><surname>Maison</surname><given-names>SF</given-names></name></person-group><year>2016</year><article-title>Toward a differential diagnosis of hidden hearing loss in humans</article-title><source>PLOS ONE</source><volume>11</volume><elocation-id>e0162726</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0162726</pub-id><?supplied-pmid 27618300?><pub-id pub-id-type="pmid">27618300</pub-id></element-citation></ref><ref id="bib39"><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>L&#x02019;Engle</surname><given-names>M</given-names></name></person-group><year>2012</year><source>A Wrinkle in Time</source><publisher-loc>New York</publisher-loc><publisher-name>Listening Library</publisher-name></element-citation></ref><ref id="bib40"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maddox</surname><given-names>RK</given-names></name><name><surname>Lee</surname><given-names>AKC</given-names></name></person-group><year>2018</year><article-title>Auditory brainstem responses to continuous natural speech in human listeners</article-title><source>Eneuro</source><volume>5</volume><elocation-id>ENEURO.0441-17.2018</elocation-id><pub-id pub-id-type="doi">10.1523/ENEURO.0441-17.2018</pub-id><?supplied-pmid 29435487?><pub-id pub-id-type="pmid">29435487</pub-id></element-citation></ref><ref id="bib41"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mesgarani</surname><given-names>N</given-names></name><name><surname>David</surname><given-names>SV</given-names></name><name><surname>Fritz</surname><given-names>JB</given-names></name><name><surname>Shamma</surname><given-names>SA</given-names></name></person-group><year>2009</year><article-title>Influence of context and behavior on stimulus reconstruction from neural activity in primary auditory cortex</article-title><source>Journal of Neurophysiology</source><volume>102</volume><fpage>3329</fpage><lpage>3339</lpage><pub-id pub-id-type="doi">10.1152/jn.91128.2008</pub-id><?supplied-pmid 19759321?><pub-id pub-id-type="pmid">19759321</pub-id></element-citation></ref><ref id="bib42"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>M&#x000f8;ller</surname><given-names>AR</given-names></name><name><surname>Jannetta</surname><given-names>PJ</given-names></name></person-group><year>1983</year><article-title>Interpretation of brainstem auditory evoked potentials: results from intracranial recordings in humans</article-title><source>Scandinavian Audiology</source><volume>12</volume><fpage>125</fpage><lpage>133</lpage><pub-id pub-id-type="doi">10.3109/01050398309076235</pub-id><?supplied-pmid 6612213?><pub-id pub-id-type="pmid">6612213</pub-id></element-citation></ref><ref id="bib43"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Moore</surname><given-names>JK</given-names></name></person-group><year>1987</year><article-title>The human auditory brain stem as a generator of auditory evoked potentials</article-title><source>Hearing Research</source><volume>29</volume><fpage>33</fpage><lpage>43</lpage><pub-id pub-id-type="doi">10.1016/0378-5955(87)90203-6</pub-id><?supplied-pmid 3654395?><pub-id pub-id-type="pmid">3654395</pub-id></element-citation></ref><ref id="bib44"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Neely</surname><given-names>ST</given-names></name><name><surname>Norton</surname><given-names>SJ</given-names></name><name><surname>Gorga</surname><given-names>MP</given-names></name><name><surname>Jesteadt</surname><given-names>W</given-names></name></person-group><year>1988</year><article-title>Latency of auditory brain-stem responses and otoacoustic emissions using tone-burst stimuli</article-title><source>The Journal of the Acoustical Society of America</source><volume>83</volume><fpage>652</fpage><lpage>656</lpage><pub-id pub-id-type="doi">10.1121/1.396542</pub-id><?supplied-pmid 3351122?><pub-id pub-id-type="pmid">3351122</pub-id></element-citation></ref><ref id="bib45"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>O'Sullivan</surname><given-names>AE</given-names></name><name><surname>Lim</surname><given-names>CY</given-names></name><name><surname>Lalor</surname><given-names>EC</given-names></name></person-group><year>2019</year><article-title>Look at me when I'm talking to you: selective attention at a multisensory cocktail party can be decoded using stimulus reconstruction and alpha power modulations</article-title><source>European Journal of Neuroscience</source><volume>50</volume><fpage>3282</fpage><lpage>3295</lpage><pub-id pub-id-type="doi">10.1111/ejn.14425</pub-id></element-citation></ref><ref id="bib46"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pernet</surname><given-names>CR</given-names></name><name><surname>Appelhoff</surname><given-names>S</given-names></name><name><surname>Gorgolewski</surname><given-names>KJ</given-names></name><name><surname>Flandin</surname><given-names>G</given-names></name><name><surname>Phillips</surname><given-names>C</given-names></name><name><surname>Delorme</surname><given-names>A</given-names></name><name><surname>Oostenveld</surname><given-names>R</given-names></name></person-group><year>2019</year><article-title>EEG-BIDS, an extension to the brain imaging data structure for electroencephalography</article-title><source>Scientific Data</source><volume>6</volume><elocation-id>103</elocation-id><pub-id pub-id-type="doi">10.1038/s41597-019-0104-8</pub-id><?supplied-pmid 31239435?><pub-id pub-id-type="pmid">31239435</pub-id></element-citation></ref><ref id="bib47"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Picton</surname><given-names>TW</given-names></name><name><surname>Hillyard</surname><given-names>SA</given-names></name><name><surname>Krausz</surname><given-names>HI</given-names></name><name><surname>Galambos</surname><given-names>R</given-names></name></person-group><year>1974</year><article-title>Human auditory evoked potentials. I. evaluation of components</article-title><source>Electroencephalography and Clinical Neurophysiology</source><volume>36</volume><fpage>179</fpage><lpage>190</lpage><pub-id pub-id-type="doi">10.1016/0013-4694(74)90155-2</pub-id><?supplied-pmid 4129630?><pub-id pub-id-type="pmid">4129630</pub-id></element-citation></ref><ref id="bib48"><element-citation publication-type="software"><person-group person-group-type="author"><name><surname>Polonenko</surname><given-names>MJ</given-names></name></person-group><year>2021</year><data-title>peaky-speech</data-title><source>Software Heritage</source><version designator="swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd">swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd</version><ext-link ext-link-type="uri" xlink:href="https://archive.softwareheritage.org/swh:1:dir:4d3bb4efd8291f72c193489adf9ceb62ee980132;origin=https://github.com/maddoxlab/peaky-speech;visit=swh:1:snp:181a76ed2bdf5be24d9397cf8fc287e2879a861e;anchor=swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd/">https://archive.softwareheritage.org/swh:1:dir:4d3bb4efd8291f72c193489adf9ceb62ee980132;origin=https://github.com/maddoxlab/peaky-speech;visit=swh:1:snp:181a76ed2bdf5be24d9397cf8fc287e2879a861e;anchor=swh:1:rev:3c1e5b62d5bb3a5b9cc6130cb3db651bc73b3ecd/</ext-link></element-citation></ref><ref id="bib49"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Polonenko</surname><given-names>MJ</given-names></name><name><surname>Maddox</surname><given-names>RK</given-names></name></person-group><year>2019</year><article-title>The parallel auditory brainstem response</article-title><source>Trends in Hearing</source><volume>23</volume><elocation-id>233121651987139</elocation-id><pub-id pub-id-type="doi">10.1177/2331216519871395</pub-id></element-citation></ref><ref id="bib50"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Prendergast</surname><given-names>G</given-names></name><name><surname>Guest</surname><given-names>H</given-names></name><name><surname>Munro</surname><given-names>KJ</given-names></name><name><surname>Kluk</surname><given-names>K</given-names></name><name><surname>L&#x000e9;ger</surname><given-names>A</given-names></name><name><surname>Hall</surname><given-names>DA</given-names></name><name><surname>Heinz</surname><given-names>MG</given-names></name><name><surname>Plack</surname><given-names>CJ</given-names></name></person-group><year>2017</year><article-title>Effects of noise exposure on young adults with normal audiograms I: electrophysiology</article-title><source>Hearing Research</source><volume>344</volume><fpage>68</fpage><lpage>81</lpage><pub-id pub-id-type="doi">10.1016/j.heares.2016.10.028</pub-id><?supplied-pmid 27816499?><pub-id pub-id-type="pmid">27816499</pub-id></element-citation></ref><ref id="bib51"><element-citation publication-type="book"><person-group person-group-type="author"><collab>R Development Core Team</collab></person-group><year>2020</year><data-title>R: A language and environment for statistical computing</data-title><publisher-loc>Vienna, Austria</publisher-loc><publisher-name>R Foundation for Statistical Computing</publisher-name><ext-link ext-link-type="uri" xlink:href="http://www.r-project.org">http://www.r-project.org</ext-link></element-citation></ref><ref id="bib52"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rasetshwane</surname><given-names>DM</given-names></name><name><surname>Argenyi</surname><given-names>M</given-names></name><name><surname>Neely</surname><given-names>ST</given-names></name><name><surname>Kopun</surname><given-names>JG</given-names></name><name><surname>Gorga</surname><given-names>MP</given-names></name></person-group><year>2013</year><article-title>Latency of tone-burst-evoked auditory brain stem responses and otoacoustic emissions: level, frequency, and rise-time effects</article-title><source>The Journal of the Acoustical Society of America</source><volume>133</volume><fpage>2803</fpage><lpage>2817</lpage><pub-id pub-id-type="doi">10.1121/1.4798666</pub-id><?supplied-pmid 23654387?><pub-id pub-id-type="pmid">23654387</pub-id></element-citation></ref><ref id="bib53"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rudnicki</surname><given-names>M</given-names></name><name><surname>Schoppe</surname><given-names>O</given-names></name><name><surname>Isik</surname><given-names>M</given-names></name><name><surname>V&#x000f6;lk</surname><given-names>F</given-names></name><name><surname>Hemmert</surname><given-names>W</given-names></name></person-group><year>2015</year><article-title>Modeling auditory coding: from sound to spikes</article-title><source>Cell and Tissue Research</source><volume>361</volume><fpage>159</fpage><lpage>175</lpage><pub-id pub-id-type="doi">10.1007/s00441-015-2202-z</pub-id><?supplied-pmid 26048258?><pub-id pub-id-type="pmid">26048258</pub-id></element-citation></ref><ref id="bib54"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Saiz-Al&#x000ed;a</surname><given-names>M</given-names></name><name><surname>Forte</surname><given-names>AE</given-names></name><name><surname>Reichenbach</surname><given-names>T</given-names></name></person-group><year>2019</year><article-title>Individual differences in the attentional modulation of the human auditory brainstem response to speech inform on speech-in-noise deficits</article-title><source>Scientific Reports</source><volume>9</volume><elocation-id>14131</elocation-id><pub-id pub-id-type="doi">10.1038/s41598-019-50773-1</pub-id><?supplied-pmid 31575950?><pub-id pub-id-type="pmid">31575950</pub-id></element-citation></ref><ref id="bib55"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Saiz-Al&#x000ed;a</surname><given-names>M</given-names></name><name><surname>Reichenbach</surname><given-names>T</given-names></name></person-group><year>2020</year><article-title>Computational modeling of the auditory brainstem response to continuous speech</article-title><source>Journal of Neural Engineering</source><volume>17</volume><elocation-id>036035</elocation-id><pub-id pub-id-type="doi">10.1088/1741-2552/ab970d</pub-id><?supplied-pmid 32460257?><pub-id pub-id-type="pmid">32460257</pub-id></element-citation></ref><ref id="bib56"><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Scott</surname><given-names>M</given-names></name></person-group><year>2007</year><source>The Alchemyst: The Secrets of the Immortal Nicholas Flamel</source><publisher-name>Audiobook</publisher-name></element-citation></ref><ref id="bib57"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shore</surname><given-names>SE</given-names></name><name><surname>Nuttall</surname><given-names>AL</given-names></name></person-group><year>1985</year><article-title>High-synchrony cochlear compound action potentials evoked by rising frequency-swept tone bursts</article-title><source>The Journal of the Acoustical Society of America</source><volume>78</volume><fpage>1286</fpage><lpage>1295</lpage><pub-id pub-id-type="doi">10.1121/1.392898</pub-id><?supplied-pmid 3840500?><pub-id pub-id-type="pmid">3840500</pub-id></element-citation></ref><ref id="bib58"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stapells</surname><given-names>DR</given-names></name><name><surname>Oates</surname><given-names>P</given-names></name></person-group><year>1997</year><article-title>Estimation of the pure-tone audiogram by the auditory brainstem response: a review</article-title><source>Audiology and Neurotology</source><volume>2</volume><fpage>257</fpage><lpage>280</lpage><pub-id pub-id-type="doi">10.1159/000259252</pub-id><?supplied-pmid 9390836?><pub-id pub-id-type="pmid">9390836</pub-id></element-citation></ref><ref id="bib59"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Starr</surname><given-names>A</given-names></name><name><surname>Hamilton</surname><given-names>AE</given-names></name></person-group><year>1976</year><article-title>Correlation between confirmed sites of neurological lesions and abnormalities of far-field auditory brainstem responses</article-title><source>Electroencephalography and Clinical Neurophysiology</source><volume>41</volume><fpage>595</fpage><lpage>608</lpage><pub-id pub-id-type="doi">10.1016/0013-4694(76)90005-5</pub-id><?supplied-pmid 62654?><pub-id pub-id-type="pmid">62654</pub-id></element-citation></ref><ref id="bib60"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Strelcyk</surname><given-names>O</given-names></name><name><surname>Christoforidis</surname><given-names>D</given-names></name><name><surname>Dau</surname><given-names>T</given-names></name></person-group><year>2009</year><article-title>Relation between derived-band auditory brainstem response latencies and behavioral frequency selectivity</article-title><source>The Journal of the Acoustical Society of America</source><volume>126</volume><fpage>1878</fpage><lpage>1888</lpage><pub-id pub-id-type="doi">10.1121/1.3203310</pub-id><?supplied-pmid 19813802?><pub-id pub-id-type="pmid">19813802</pub-id></element-citation></ref><ref id="bib61"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Teoh</surname><given-names>ES</given-names></name><name><surname>Lalor</surname><given-names>EC</given-names></name></person-group><year>2019</year><article-title>EEG decoding of the target speaker in a cocktail party scenario: considerations regarding dynamic switching of talker location</article-title><source>Journal of Neural Engineering</source><volume>16</volume><elocation-id>036017</elocation-id><pub-id pub-id-type="doi">10.1088/1741-2552/ab0cf1</pub-id><?supplied-pmid 30836345?><pub-id pub-id-type="pmid">30836345</pub-id></element-citation></ref><ref id="bib62"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Verhulst</surname><given-names>S</given-names></name><name><surname>Alto&#x000e8;</surname><given-names>A</given-names></name><name><surname>Vasilkov</surname><given-names>V</given-names></name></person-group><year>2018</year><article-title>Computational modeling of the human auditory periphery: auditory-nerve responses, evoked potentials and hearing loss</article-title><source>Hearing Research</source><volume>360</volume><fpage>55</fpage><lpage>75</lpage><pub-id pub-id-type="doi">10.1016/j.heares.2017.12.018</pub-id><?supplied-pmid 29472062?><pub-id pub-id-type="pmid">29472062</pub-id></element-citation></ref><ref id="bib63"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Winer</surname><given-names>JA</given-names></name></person-group><year>2005</year><article-title>Decoding the auditory corticofugal systems</article-title><source>Hearing Research</source><volume>207</volume><fpage>1</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1016/j.heares.2005.06.007</pub-id><?supplied-pmid 16091301?><pub-id pub-id-type="pmid">16091301</pub-id></element-citation></ref><ref id="bib64"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zilany</surname><given-names>MS</given-names></name><name><surname>Bruce</surname><given-names>IC</given-names></name><name><surname>Carney</surname><given-names>LH</given-names></name></person-group><year>2014</year><article-title>Updated parameters and expanded simulation options for a model of the auditory periphery</article-title><source>The Journal of the Acoustical Society of America</source><volume>135</volume><fpage>283</fpage><lpage>286</lpage><pub-id pub-id-type="doi">10.1121/1.4837815</pub-id><?supplied-pmid 24437768?><pub-id pub-id-type="pmid">24437768</pub-id></element-citation></ref></ref-list></back><sub-article id="sa1" article-type="decision-letter"><front-stub><article-id pub-id-type="doi">10.7554/eLife.62329.sa1</article-id><title-group><article-title>Decision letter</article-title></title-group><contrib-group><contrib contrib-type="editor"><name><surname>Reichenbach</surname><given-names>Tobias</given-names></name><role>Reviewing Editor</role><aff><institution>Imperial College London</institution><country>United Kingdom</country></aff></contrib></contrib-group><contrib-group><contrib contrib-type="reviewer"><name><surname>Reichenbach</surname><given-names>Tobias</given-names></name><role>Reviewer</role><aff><institution>Imperial College London</institution><country>United Kingdom</country></aff></contrib><contrib contrib-type="reviewer"><name><surname>Simon</surname><given-names>Jonathan Z</given-names></name><role>Reviewer</role><aff><institution>University of Maryland</institution><country>United States</country></aff></contrib></contrib-group></front-stub><body><boxed-text position="float" orientation="portrait"><p>In the interests of transparency, eLife publishes the most substantive revision requests and the accompanying author responses.</p></boxed-text><p><bold>Acceptance summary:</bold></p><p>This manuscript describes an alteration to speech to make it more peaky, The authors show that the developed peaky speech, in contrast to naturalistic speech, allows measurements of distinct subcortical components of the neural response. This development may allow further and more refined investigations of the contribution of different subcortical structures to speech processing as well as to hearing deficits.</p><p><bold>Decision letter after peer review:</bold></p><p>Thank you for submitting your article "Exposing distinct subcortical components of the auditory brainstem response evoked by continuous naturalistic speech" for consideration by <italic>eLife</italic>. Your article has been reviewed by three peer reviewers, including Tobias Reichenbach as the Reviewing Editor and Reviewer #1, and the evaluation has been overseen by Andrew King as the Senior Editor. The following individual involved in review of your submission has agreed to reveal their identity: Jonathan Z Simon (Reviewer #3).</p><p>The reviewers have discussed the reviews with one another and the Reviewing Editor has drafted this decision to help you prepare a revised submission.</p><p>As the editors have judged that your manuscript is of interest, but as described below that additional experiments are required before it is published, we would like to draw your attention to changes in our revision policy that we have made in response to COVID-19 (https://elifesciences.org/articles/57162). First, because many researchers have temporarily lost access to the labs, we will give authors as much time as they need to submit revised manuscripts. We are also offering, if you choose, to post the manuscript to bioRxiv (if it is not already there) along with this decision letter and a formal designation that the manuscript is "in revision at <italic>eLife</italic>". Please let us know if you would like to pursue this option. (If your work is more suitable for medRxiv, you will need to post the preprint yourself, as the mechanisms for us to do so are still in development.)</p><p>Summary:</p><p>This manuscript describes a type of alteration to speech to make it more peaky, with the goal of inducing stronger responses in the auditory brainstem. Recent work has employed naturalistic speech to investigate subcortical mechanisms of speech processing. However, previous methods were ill equipped to tease apart the neural responses in different parts of the brainstem. The authors show that their speech manipulation improves this: the peaky speech that they develop allows segregation of different waves of the brainstem response. This development may allow further and more refined investigations of the contribution of different parts of the brainstem to speech processing, as well as to hearing deficits.</p><p>Essential revisions:</p><p>&#x02013; Despite repeated claims, we are not sure that a convincing case is made here that this method can indeed provide insight into how speech is processed in early auditory pathway. The response is essentially a click-like response elicited by the glottal pulses in the stimulus; it averages out information related to dynamic variations in envelope and pitch that are essential for speech perception; at the same time, it is highly sensitive to sound features that do not affect speech perception. What reason is there to assume that these responses contain information that is specific or informative about speech processing?</p><p>&#x02013; Similarly, the claim that the methodology can be used as a clinical application is not convincing. It is not made clear what pathology these responses can detect that current ABR methods cannot, or why. As explained in the Discussion, the response size is inherently smaller than standard ABRs because of the higher repetition rate of the glottal pulses, and the response may depend on more complex neural interactions that would be difficult to quantify. Do these features not make them less suitable for clinical use?</p><p>&#x02013; It needs to be rigorously confirmed that the earliest responses are not contaminated or influenced by responses from later sources. There seems to be some coherent activity or offset in the baseline (pre 0 ms), in particular with the lower filter cut off. One way to test this might be to simulate a simple response by filtering and time shifting the stimulus waveforms, adding these up plus realistic noise, and applying the deconvolution to see whether the input is accurately reproduced. It might be useful to see how the response latencies and amplitudes correlate to those of conventional click responses, and how they depend on stimulus level.</p><p>&#x02013; The multiband responses show a variation of latency with frequency band that indicates a degree of cochlear frequency specificity. The latency functions reported here looks similar to those obtained by Don et al. 1993 for derived band click responses, but the actual numbers for the frequency dependent delays (as estimated by eye from Figures 4,6 and 7) seem shorter than those reported for wave V at 65 dB SPL (Don et al. 1993 table II). The latency function would be better fitted to an exponential, as in Strelcyk et al. 2009 (Equation 1), than a quadratic function; the fitted exponent could be directly compared to their reported value.</p><p>&#x02013; The fact that differences between narrators leads to changes to the ABR response is to be expected, and was already reported in Maddox and Lee, 2018. We are not sure why this issue needs to be examined and discussed at such length here. The space devoted to discussing the recording time also seems very long. Neither Abstract or Introduction refers to these topics, and they seem to be side-issues that could be summarised and discussed much more briefly.</p><p>&#x02013; The authors motivate the work from the use of naturalistic speech, and the application of the developed method to investigate, for instance, speech-in-noise deficits. But they do not discuss how comprehensible the peaky speech in fact is. We would therefore like to see behavioural experiments that quantitatively compare speech-in-noise comprehension, for example SRTs, for the unaltered speech and the peaky speech. Without such a quantification, it is impossible to fully judge the usefulness of the reported method for further research and clinical applications.</p><p>&#x02013; The neural responses to unaltered speech and to peaky speech are analysed by two different methods. For unaltered speech, the authors use the half-wave rectified waveform as the regressor. For peaky speech, however, the regressor is a series of spikes that are located at the timings of the glottal pulses. Due to this rather different analysis, it is impossible to know to which degree the differences in the neural responses to the two types of speech that the authors report are due to the different speech types, or due to the different analysis techniques. The authors should therefore use the same analysis technique for both types of speech. It might be most sensible to analyse the unaltered speech through a regressor with spikes at the glottal pulses a well. In addition, it would be good to see a comparison, say of a SNR, when the peaky speech is analysed through the half-wave rectified waveform and through the series of spikes. This would also further motivate the usage of the regressor with the series of spikes.</p><p>&#x02013; Subsection &#x0201c;Frequency-specific responses show frequency-specific lags&#x0201d;. What causes the difference between the effect of high-pass filtering and subtracting the common response? If they serve the same purpose, but have different results, this raises the question which is more appropriate.</p><p>&#x02013; Subsection &#x0201c;Frequency-specific responses show frequency-specific lags&#x0201d; paragraph four. This seems a misinterpretation. The similarity between broadband and summated multiband responses indicates that the band filtered components in the multiband stimulus elicited responses that add linearly in the broadband response. It does not imply that the responses to the different bands originate from non-overlapping cochlear frequency regions.</p><p>&#x02013; Is this measure of SNR appropriate, when the baseline is artificially constructed by deconvolution and filtering? Perhaps noise level could be assessed by applying the deconvolution to a silent recording instead? It might also be useful to have a measure of the replicability of the response.</p><p>&#x02013; "wave III was clearly identifiable in 16 of the 22 subjects": Figure 1 indicates that the word "clearly" may be somewhat generous. It would be worthwhile to discuss wave III and its identifiability in more detail (perhaps its identifiability/non-universality could be compared with that of another less prominent peak in traditionally obtained ABRs?).</p><p>[Editors' note: further revisions were suggested prior to acceptance, as described below.]</p><p>Thank you for submitting your article "Exposing distinct subcortical components of the auditory brainstem response evoked by continuous naturalistic speech" for consideration by <italic>eLife</italic>. Your article has been reviewed by three peer reviewers, including Tobias Reichenbach as the Reviewing Editor and Reviewer #1, and the evaluation has been overseen by Andrew King as the Senior Editor. The following individual involved in review of your submission has agreed to reveal their identity: Jonathan Z Simon (Reviewer #3).</p><p>The reviewers have discussed the reviews with one another and the Reviewing Editor has drafted this decision to help you prepare a revised submission.</p><p>We would like to draw your attention to changes in our revision policy that we have made in response to COVID-19 (https://elifesciences.org/articles/57162). Specifically, we are asking editors to accept without delay manuscripts, like yours, that they judge can stand as <italic>eLife</italic> papers without additional data, even if they feel that they would make the manuscript stronger. Thus the revisions requested below only address clarity and presentation.</p><p>This manuscript presents an innovative alteration to speech to make it more peaky, resulting in stronger and better delineated responses in the auditory brainstem. Recent work has indeed employed naturalistic speech to investigate subcortical mechanisms of speech processing. However, previous methods were ill equipped to tease apart the neural responses in different parts of the brainstem. The authors show that their speech manipulation improves this issue: the peaky speech that they develop allows them to segregate different waves of the brainstem response. This development allows further and more refined investigations of the contribution of different parts of the brainstem to speech processing, as well as to hearing deficits.</p><p>The authors have made substantial additions and changes in the revised manuscript to complete and improve the analysis. These are overall satisfactory, but we still have some remaining points that we would like the authors to address.</p><p>&#x02013; Due to the additions, the Results section is now even longer, as none of the original text has been removed. We suggest to shorten it by moving some technical details from the Results section to the Materials and methods. For instance, this could concern the details of the peak picking, recording time, waveform repeatability (SNR analysis is implicit in the Recording time section) and details of statistical models. Furthermore, some sentences in the Results seem to serve only to summarize the previous section, which seems unnecessary, or make a general statement that is more suitable for the Discussion.</p><p>&#x02013; "unless presented monaurally by headphones". So these studies did show detectable wave I/III waves when using monaural headphone presentation? This should be more clearly phrased. It seems strange to make this presentation mode sound like a limitation &#x02013; monaural presentation is standard in clinical audiology. This point raises further questions that had not previously occurred to us: can you clarify whether the peaky speech has also been tested using monaural presentation, and if so how the responses differ from diotic/dichotic presentation (and if not, why was diotic presentation chosen)? How would you confirm that dichotic presentation of the peaky speech produces purely ear-specific responses? Could there be potential binaural interactions or contralateral suppression mechanisms with diotic or dichotic presentation? These questions are relevant and should be briefly addressed.</p><p>&#x02013; We are not sure what "region-specific" means here &#x02013; please clarify.</p><p>&#x02013; It is more common and reliable to use different peak pickers, rather than the same twice. It probably won't matter much here, but it would be preferable in future analyses.</p><p>&#x02013; It should be explained why the comparison is shown only for the high-pass filter at 30 Hz. The high-pass filter at 150 Hz seems more interesting, as it shows the wave I and III peaks in the peaky speech response that the unaltered speech does not.</p><p>&#x02013; "completed two models". It might be more accurate to write "performed two simulations".</p><p>&#x02013; Subsection &#x0201c;More components of the ABR and MLR are present with broadband peaky than unaltered speech&#x0201d; paragraph three: Does "trial" here mean the time period starting at a glottal pulse? How long did this time period extend from there? From what time point on was the ABR zero-padded? These details should be clarified. It could be informative to also show the input kernels in Figure 3, i.e. the simulated "ABR" that did not show the pre-stimulus component.</p><p>&#x02013; Cortical responses are much larger and likely to be more affected by attention than the much smaller earliest responses. Is it known whether a high-pass filter at 150 Hz is sufficient to reliably remove all cortical contributions within the amplitude range of the ABR wave I? Can a reference be provided for this?</p><p>&#x02013; Strictly speaking, the stimuli differed not only in the narrator's identity but also in text. It would be interesting to comment on whether different texts with the same narrator are likely to give different responses.</p><p>&#x02013; Figure 3 and text related to the more aggressive high-pass filtering at 200 Hz:</p><p>With this more aggressive filter, the 8 ms peak has flipped sign and gained additional latency. This needs to be explicitly acknowledged and addressed. It might be due to the peak of that filter's non-linear group delay at its cutoff frequency, combined with an abundance of signal power at the cutoff frequency. But there could be other reasons as well.</p><p>&#x02013; A number of parameter estimates from the power law regression results are written as dimensionless but actually have units of ms. This includes &#x003c4;<sub>synaptic</sub> (should be 0.8 ms in several locations), &#x003c4;<sub>I-V</sub>(in several locations), and a (also in several locations).</p></body></sub-article><sub-article id="sa2" article-type="reply"><front-stub><article-id pub-id-type="doi">10.7554/eLife.62329.sa2</article-id><title-group><article-title>Author response</article-title></title-group></front-stub><body><disp-quote content-type="editor-comment"><p>Essential revisions:</p><p>&#x02013; Despite repeated claims, we are not sure that a convincing case is made here that this method can indeed provide insight into how speech is processed in early auditory pathway. The response is essentially a click-like response elicited by the glottal pulses in the stimulus; it averages out information related to dynamic variations in envelope and pitch that are essential for speech perception; at the same time, it is highly sensitive to sound features that do not affect speech perception. What reason is there to assume that these responses contain information that is specific or informative about speech processing?</p></disp-quote><p>The fact that the peaky speech response resembles the click-evoked ABR is its primary strength&#x02014;not a weakness. Clicks provide high-fidelity waveforms that can be tied to distinct subcortical sources. Speech stimuli have been used by Kraus and many others to relate neural encoding of natural stimuli to cognition, learning, and intellectual and developmental disorders&#x02014;but the response components are not easily tied to specific neural sources. Here we offer a tool that allows a subject or patient to listen to long-form natural speech, while acquiring a response that is as interpretable as the click ABR. We believe this strength was not well articulated in our first submission, and have made several changes. We have also emphasized the importance of the spectrotemporal context (namely, speech) in which these responses are acquired, and have been more careful to discuss encoding in addition to processing, while making a case for how this transient-evoked response will indeed allow us to study the latter. Our changes are summarized below:</p><p>Added paragraph to Introduction: &#x0201c;Although click responses can assess sound encoding (of clicks) at early stages of the auditory system, a speech-evoked response with the same components would assess region-specific encoding within the acoustical context of the dynamic spectrotemporal characteristics of speech &#x02013; information that is not possible to obtain from click responses. Furthermore, changes to the amplitudes and latencies of these early components could inform our understanding of speech processing if deployed in experiments that compare conditions requiring different states of processing, such as attended/unattended speech or understood/foreign language. For example, if a wave I from the auditory nerve differed between speech stimuli that were attended versus unattended, then this would add to our current understanding of the brainstem&#x02019;s role in speech processing. Therefore, a click-like response that is evoked by speech stimuli facilitates new investigations into speech encoding and processing.&#x0201d;</p><p>&#x0201c;&#x02026;Although clicks also evoke responses with multiple component waves, current studies of synaptopathy show quite varied results with clicks and poor correlation with speech in noise (Bramhall et al., 2019; Prendergast et al., 2017). Obtaining click-like responses to stimuli with all of speech&#x02019;s spectrotemporal richness may provide a better connection to the specific neural underpinnings of speech encoding, similar to how the complex cross-correlation to a fundamental waveform can change based on the context of attention (Forte et al., 2017; Saiz-Al&#x000ed;a et al., 2019).&#x0201d;</p><p>&#x0201c;Multiband peaky speech will not replace the current frequency specific ABR, but there are situations where it may be advantageous to use speech over tone pips. Measuring waves I, III, and V of high frequency responses in the context of all the dynamics of speech may have applications to studying effects of cochlear synaptopathy on speech comprehension (Bharadwaj et al., 2014; Liberman et al., 2016).&#x0201d;</p><p>&#x0201c;Auditory prostheses have algorithms specifically tuned for the spectrotemporal dynamics of speech that behave very differently in response to standard diagnostic stimuli such as trains of clicks or tone pips. Peaky speech responses could allow us to assess how the auditory system is encoding the amplified speech and validate audibility of the hearing aid fittings before the infant or toddler is old enough to provide reliable speech perception testing. Therefore, the ability of peaky speech to yield both canonical waveforms and frequency-specific responses makes this paradigm a flexible method that assesses speech encoding in new ways.&#x0201d;</p><p>&#x0201c;&#x02026;Indeed, previous methods have been quite successful in elucidating cortical processing of speech under these conditions (O&#x02019;Sullivan et al., 2019; Teoh and Lalor, 2019). [&#x02026;] Finally, the ability to customize peaky speech for measuring frequency-specific responses provides potential applications to clinical research in the context of facilitating assessment of supra-threshold hearing function and changes to how speech may be encoded following intervention strategies and technologies while using a speech stimulus that algorithms in hearing aids and cochlear implants are designed to process.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; Similarly, the claim that the methodology can be used as a clinical application is not convincing. It is not made clear what pathology these responses can detect that current ABR methods cannot, or why. As explained in the Discussion, the response size is inherently smaller than standard ABRs because of the higher repetition rate of the glottal pulses, and the response may depend on more complex neural interactions that would be difficult to quantify. Do these features not make them less suitable for clinical use?</p></disp-quote><p>We have tempered the text and added more text and specific examples to make it clearer why there could be clinical applications. Even though the responses are smaller, the SNR is appropriate, and the responses reflect the context of rates experienced for salient, more naturally experienced stimuli rather than trains of clicks and pips (to which we don&#x02019;t normally listen).</p><p>We do not claim or aim to replace the click and tone pip ABR, but propose ways that these peaky speech responses can add value to clinical applications. The ABR is an index of subcortical activity, and its clinical utility is not confined to diagnosing pathology through a click ABR, or hearing thresholds through the tone pip ABR. For example, we currently do not have a way of measuring how an infant is receiving speech through an auditory prosthesis (beyond parental observation until the infant is old enough to reliably respond) because hearing aids process clicks and tone pips (of traditional ABRs) in a very different way than speech.</p><p>The added text:</p><p>In the Discussion: &#x0201c;&#x02026;peaky speech evoked responses with canonical morphology comprised of waves I, III, V, P0, Na, Pa (Figure 1), reflecting neural activity from distinct stages of the auditory system from the auditory nerve to thalamus and primary auditory cortex (e.g., Picton et al., 1974). Although clicks also evoke responses with multiple component waves, current studies of synaptopathy show quite varied results with clicks and poor correlation with speech in noise (Bramhall et al., 2019; Prendergast et al., 2017). Obtaining click-like responses to stimuli with all of speech&#x02019;s spectrotemporal richness may provide a better connection to the specific neural underpinnings of speech encoding, similar to how the complex cross-correlation to a fundamental waveform can change based on the context of attention (Forte et al., 2017; Saiz-Al&#x000ed;a et al., 2019).&#x0201d;</p><p>In the Discussion: &#x0201c;&#x02026; Also, canonical waveforms were derived in the higher frequency bands of diotically presented speech, with waves I and III identifiable in most subjects. [&#x02026;] Therefore, the ability of peaky speech to yield both canonical waveforms and frequency-specific responses makes this paradigm a flexible method that assesses speech encoding in new ways.&#x0201d;</p><p>In the Discussion: &#x0201c;Finally, the ability to customize peaky speech for measuring frequency-specific responses provides potential applications to clinical research in the context of facilitating assessment of supra-threshold hearing function and changes to how speech may be encoded following intervention strategies and technologies while using a speech stimulus that algorithms in hearing aids and cochlear implants are designed to process.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; It needs to be rigorously confirmed that the earliest responses are not contaminated or influenced by responses from later sources. There seems to be some coherent activity or offset in the baseline (pre 0 ms), in particular with the lower filter cut off. One way to test this might be to simulate a simple response by filtering and time shifting the stimulus waveforms, adding these up plus realistic noise, and applying the deconvolution to see whether the input is accurately reproduced. It might be useful to see how the response latencies and amplitudes correlate to those of conventional click responses, and how they depend on stimulus level.</p></disp-quote><p>Thank you for this very important suggestion. We performed two models to better understand the pre/peri-stimulus component. A linear model (relating rectified speech to EEG), as suggested, and an additional physiological model of the periphery. We have added a paragraph and figure to the Results section. In a follow-up paper we will investigate how the response latencies and amplitudes correlate with those of the conventional click responses, and how they depend on stimulus level. We believe this is important to investigate as a follow-up but we worry that adding even more analyses or figures could bog down the paper.</p><p>In sum, what we found is that there was indeed some spreading of post-stimulus responses into the pre-stimulus period. This spread component was broader than the response components themselves, and so could be removed with more aggressive high-pass filtering when the experiment calls for it. We appreciate the reviewer&#x02019;s observation and insightful suggestion for how to address it.</p><p>The added paragraph and figure with the modeling work is as follows:</p><p>&#x0201c;The response to broadband peaky speech showed a small but consistent response at negative and early positive lags (i.e., pre-stimulus) when using the pulse train as a regressor in the deconvolution, particularly when using the lower high-pass filter cutoff of 30 Hz (Figure 2A) compared to 150 Hz (Figure 1). [&#x02026;] Therefore, when doing an experiment where the analysis needs to evaluate specific contributions to the earliest ABR components, we recommend high-pass filtering to help mitigate the complex and time-varying nonlinearities inherent in the auditory system, as well as potential influences by responses from later sources.&#x0201d;</p><p>We added this to the filtering considerations in the Discussion:</p><p>&#x0201c;When evaluating specific contributions to the earliest waves, we recommend at least a first order 150 Hz high-pass filter or a more aggressive second order 200 Hz high-pass filter to deal with artifacts arising from nonlinearities that are not taken into account by the pulse train regressor or any potential influences by responses from later sources (Figure 3).&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; The multiband responses show a variation of latency with frequency band that indicates a degree of cochlear frequency specificity. The latency functions reported here looks similar to those obtained by Don et al. 1993 for derived band click responses, but the actual numbers for the frequency dependent delays (as estimated by eye from Figures 4,6 and 7) seem shorter than those reported for wave V at 65 dB SPL (Don et al. 1993 table II). The latency function would be better fitted to an exponential, as in Strelcyk et al., 2009 (Equation 1), than a quadratic function; the fitted exponent could be directly compared to their reported value.</p></disp-quote><p>Thank you for this suggestion. We re-fit the data with the power law regression performed in the log-log domain using linear mixed effects regression. The Results sections were updated, as well as the supplementary file and statistical analysis section. We updated the Discussion to directly compare our model parameters for wave V to previously reported values. Note: formatting for the equations did not carry over to this document.</p><p>&#x0201c;The nonlinear change in peak latency, <italic>&#x003c4;</italic>, with frequency band was modeled using power law regression according to the formula (Harte et al., 2009; Neely et al., 1988; Rasetshwane et al., 2013; Strelcyk et al., 2009): <italic>&#x003c4;(f) = a + bf<sup>-d</sup></italic> where <italic>a = &#x003c4;<sub>synaptic</sub>+ &#x003c4;<sub>(I-V)</sub></italic>, and where <italic>f</italic> is the band center frequency normalized to 1 kHz (i.e., divided by 1000), <italic>&#x003c4;<sub>synaptic</sub></italic> is the synaptic delay (assumed to be 0.8; Eggermont, 1979; Strelcyk et al., 2009), and <italic>&#x003c4;<sub>(I-V)</sub></italic> is the I-V inter-wave delay from the subjects' responses to broadband peaky speech. [&#x02026;] The significant decrease in latency with frequency band (linear term, slope, <italic>d</italic> : p &#x0003c; 0.001 for 30 and 150 Hz) was shallower (i.e., less negative) for MLR waves compared to the ABR wave V (all p &#x0003c; 0.001 for interactions between wave and the linear frequency band term for 30 and 150 Hz).&#x0201d;</p><p>&#x0201c;Therefore, a power law model (see previous subsection for the formula) was completed for waves V, P0, Na, and Pa of responses in the 4 frequency bands that were high-pass filtered at 30 Hz. [&#x02026;] This change with frequency was smaller (i.e., shallower slope) for each MLR wave compared to wave V (p &#x0003c; 0.001 for all interactions between wave and the term for the frequency band). There was a main effect of narrator on peak latencies but no interaction with wave (narrator p = 0.001, wave-narrator interactions p &#x0003e; 0.087).&#x0201d;</p><p>&#x0201c;The nonlinear change in wave V latency with frequency was modeled by a power law using log-log mixed effects regression with fixed effects of narrator, ear, logged band center frequency normalized to 1 kHz, and the interactions between narrator and frequency. Random effects included an intercept and frequency term for each subject. The experiment 2 average estimates for <italic>a</italic> of 5.06 and 5.58 for the male- and female-narrated responses were used for each subject. Details of the model are described in Supplementary file 1C. For wave V, the estimated mean parameters were <italic>b</italic> = 4.13 ms (calculated as <italic>b = 10<sup>intercept</sup></italic>) and <italic>d</italic> = -0.36 for the male narrator, and <italic>b</italic> = 4.25 ms and <italic>d</italic> = -0.41 for the female narrator, which corresponded to previously reported ranges for tone pips and derived-bands at 65 dB ppeSPL (Neely et al., 1988; Rasetshwane et al., 2013; Strelcyk et al., 2009). Latency decreased with increasing frequency (slope, <italic>d</italic>, <italic>p</italic> &#x0003c; 0.001) but did not differ between ears (<italic>p</italic> = 0.265). The <italic>b</italic> parameter differed by narrator but the slope did not change with narrator (interaction with intercept <italic>p</italic> = 0.004, interaction with slope <italic>p</italic> = 0.085).&#x0201d;</p><p>In the Discussion: &#x0201c;Peak wave latencies of these responses decreased with increasing band frequency in a similar way to responses evoked by tone pips and derived-bands from clicks in noise (Gorga et al., 1988; Neely et al., 1988; Rasetshwane et al., 2013; Strelcyk et al., 2009), thereby representing activity evoked from different areas across the cochlea. In fact, our estimates of the power law parameters of <italic>a</italic> (the central conduction time), <italic>d</italic> (the frequency dependence) and <italic>b</italic> (the latency corresponding to 1 kHz and 65 dB SPL) for wave V fell within the corresponding ranges that were previously reported for tone pips and derived-bands at 65 dB ppeSPL (Neely et al., 1988; Rasetshwane et al., 2013; Strelcyk et al., 2009).&#x0201d;</p><p>In the Materials and methods: &#x0201c;For multiband peaky speech, the component wave peak latency changes across frequency band were assessed with power law regression (Harte et al., 2009; Neely et al., 1988; Rasetshwane et al., 2013; Strelcyk et al., 2009), conducted in the log-log domain with linear mixed effects regression using the lme4 and lmerTest packages in R (Bates et al., 2015; Kuznetsova et al., 2017; R Core Team, 2020). The parameter <italic>a</italic> of the power law regression was estimated by adding an assumed synaptic delay of 0.8 (Eggermont, 1979; Strelcyk et al., 2009) to the I-V inter-wave delay from the subjects' responses to broadband peaky speech. For subjects who did not have an identifiable wave I in the broadband peaky response, the group mean I-V delay was used &#x02013; this occurred for 1 of 22 subjects in experiment 1, and 2 of 11 subjects for responses to the female narrator in experiment 2. Because only multiband peaky speech was presented in experiment 3, the mean I-V intervals from experiment 2 were used for each subject for experiment 3. Random effects of subject and each frequency band term were included to account for individual variability that is not generalizable to the fixed effects.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; The fact that differences between narrators leads to changes to the ABR response is to be expected, and was already reported in Maddox and Lee, 2018. We are not sure why this issue needs to be examined and discussed at such length here. The space devoted to discussing the recording time also seems very long. Neither Abstract or Introduction refers to these topics, and they seem to be side-issues that could be summarised and discussed much more briefly.</p></disp-quote><p>It&#x02019;s true that there were subtle differences between narrators in the 2018 paper, but the differences here are larger and likely stem from differences in mean F0, which is not necessarily the case for the previous paper. As a methods paper we feel it is important to quantify how choice of narrator impacts the response as well as the recording time (and thus how many conditions may be feasible in an experiment). Recording time and the effect of narrator choice are important practical considerations for using this new technique. We added this to the Abstract: &#x0201c;We further demonstrate the versatility of peaky speech by simultaneously measuring bilateral and ear-specific responses across different frequency bands, and discuss important practical considerations such as talker choice.&#x0201d; We also cropped some text in the Results and Discussion sections.</p><disp-quote content-type="editor-comment"><p>&#x02013; The authors motivate the work from the use of naturalistic speech, and the application of the developed method to investigate, for instance, speech-in-noise deficits. But they do not discuss how comprehensible the peaky speech in fact is. We would therefore like to see behavioural experiments that quantitatively compare speech-in-noise comprehension, for example SRTs, for the unaltered speech and the peaky speech. Without such a quantification, it is impossible to fully judge the usefulness of the reported method for further research and clinical applications.</p></disp-quote><p>The reason we did not do any experiments quantifying the intelligibility of the speech is because it sounds pretty much like normal speech and the spectrograms (as shown in Figure 13 in the Materials and methods section) are essentially identical, so it never occurred to us to test it in this methods paper. There were some example stimulus files attached as supplementary data with reference to these files in the manuscript, but we could have done a better job pointing out how intelligible the speech is in the manuscript, and we have done so in our revisions:</p><p>In the Discussion we say &#x0201c;Regardless, our peaky speech generated robust canonical responses with good SNR while maintaining a natural-sounding, if very slightly &#x0201c;buzzy,&#x0201d; quality to the speech.&#x0201d;</p><p>We added the following to the Introduction: &#x0201c;The design goal of peaky speech is to re-synthesize natural speech so that its defining spectrotemporal content is unaltered &#x02013; maintaining the speech as intelligible and identifiable &#x02013; but its pressure waveform consists of maximally sharp peaks so that it drives the ABR as effectively as possible (giving a very slight &#x0201c;buzzy&#x0201d; quality when listening under good headphones; Audio files 1&#x02013;6).&#x0201d;</p><p>The comment says that it's impossible to judge the usefulness, but we feel that this behavioral experiment testing the intelligibility in noise is best left for a follow-up study with such a focus. Addressing it experimentally now, especially with major slowdowns related to a worsening coronavirus situation, would add a lengthy delay to publication.</p><p>We asked the editors for their advice before we submitted this revision that does not include the behavioral data. They indicated that they would be happy for us to submit a revised version of our manuscript on the basis of these revisions including a discussion of the sample files pointing out that the peaky speech is clearly intelligible.</p><disp-quote content-type="editor-comment"><p>&#x02013; The neural responses to unaltered speech and to peaky speech are analysed by two different methods. For unaltered speech, the authors use the half-wave rectified waveform as the regressor. For peaky speech, however, the regressor is a series of spikes that are located at the timings of the glottal pulses. Due to this rather different analysis, it is impossible to know to which degree the differences in the neural responses to the two types of speech that the authors report are due to the different speech types, or due to the different analysis techniques. The authors should therefore use the same analysis technique for both types of speech. It might be most sensible to analyse the unaltered speech through a regressor with spikes at the glottal pulses a well. In addition, it would be good to see a comparison, say of a SNR, when the peaky speech is analysed through the half-wave rectified waveform and through the series of spikes. This would also further motivate the usage of the regressor with the series of spikes.</p></disp-quote><p>We wondered the same, and these analyses were in the supplemental information where they were easy to miss. Therefore, we moved the analyses and figures to the main body of the paper and added the responses of unaltered speech derived with a pulse train regressor. We kept the figure of the multiband peaky speech vs unaltered speech with the half-wave rectified audio regressor as a supplemental figure (Figure 4&#x02014;figure supplement 1) since we already have a lot of figures in the main paper. The results show that the new analysis is only possible with the new stimuli. The responses for unaltered and broadband peaky speech are similar when using the half-wave rectified audio as the regressor, and the SNR is similar. The SNR is indirectly shown in the cumulative density functions, which show the time for responses to reach 0 dB SNR. The CDF for using the half-wave rectified audio shows that for ABR lags the rectified regressor takes much longer to reach 0 dB SNR.</p><p>The following text from the supplemental figure caption was added to the main body text of the broadband peaky speech results:</p><p>&#x0201c;We verified that the EEG data collected in response to broadband peaky speech could be regressed with the half-wave rectified speech to generate a response. [&#x02026;] The aligned phases of the broadband peaky response allow for the distinct waves of the canonical brainstem and middle latency responses to be derived using the pulse train regressor.&#x0201d;</p><p>The following text from the supplemental figure caption was added to the main body text of the multiband peaky speech results:</p><p>&#x0201c;We also verified that the EEG data collected in response to multiband peaky speech could be regressed with the half-wave rectified speech to generate a response. Figure 4&#x02014;figure supplement 1 shows that the derived responses to unaltered and multiband peaky speech were similar in morphology when using the half-wave audio as the regressor, although the multiband peaky speech response was broader in the earlier latencies. Correlation coefficients from the 22 subjects for the 0&#x02013;40 ms lags had a median (interquartile range) of 0.83 (0.77&#x02013;0.88), which were slightly smaller than the correlation coefficients obtained by re-analyzing the data split into even/odd epochs that each contained an equal number of epochs with EEG to unaltered speech and peaky broadband speech (0.89, interquartile range 0.83&#x02013;0.94; Wilcoxon signed-rank test W(21) = 58.0, p = 0.025). This means that the same EEG collected to multiband peaky speech can be flexibly used to generate the frequency-specific brainstem responses to the pulse train, as well as the broader response to the half-wave rectified speech.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; Subsection &#x0201c;Frequency-specific responses show frequency-specific lags&#x0201d;. What causes the difference between the effect of high-pass filtering and subtracting the common response? If they serve the same purpose, but have different results, this raises the question which is more appropriate.</p></disp-quote><p>Thank you for this comment&#x02014;we can see how people following our methods could have been confused or misguided. The way to remove the common component is to calculate it and subtract it. At least for the narrators we tested, the common component contains minimal energy above 150 Hz, so if you&#x02019;re high-passing at 150 Hz anyway for the ABR, then the step of formally subtracting the common component may not be necessary. We revised the section, which now reads:</p><p>&#x0201c;This coherence was due to all pulse trains beginning and ending together at the onset and offset of voiced segments and was the source of the low-frequency common component of each band&#x02019;s response. The way to remove the common component is to calculate the common activity across the frequency band responses and subtract this waveform from each of the frequency band responses &#x02026;. Of course, the subtracted waveforms could also then be high-pass filtered at 150 Hz to highlight earlier waves of the brainstem responses, as shown by the dashed lines in Figure 6B. However, this method reduces the amplitude of the responses, which in turn affects response SNR and detectability. In some scenarios, due to the low-pass nature of the common component, high-passing the waveforms at a high enough frequency may obviate the need to formally subtract the common component. For example, at least for the narrators used in these experiments, the common component contained minimal energy above 150 Hz, so if the waveforms are already high-passed at 150 Hz to focus on the early waves of the ABR, then the step of formally subtracting the common component may not be necessary. But beyond the computational cost, there is no reason not to subtract the common component, and doing so allows lower filter cut-offs to be used.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; Subsection &#x0201c;Frequency-specific responses show frequency-specific lags&#x0201d; paragraph four. This seems a misinterpretation. The similarity between broadband and summated multiband responses indicates that the band filtered components in the multiband stimulus elicited responses that add linearly in the broadband response. It does not imply that the responses to the different bands originate from non-overlapping cochlear frequency regions.</p></disp-quote><p>We agree that it shows that they represent a &#x0201c;whole&#x0201d; response separated into parts. They would not add linearly if certain parts of the cochlea were 100% active for two different ones. We moderated this claim as follows:</p><p>&#x0201c;The similarity verifies that the frequency-dependent responses are complementary to each other and to the common component, such that these components add linearly into a &#x0201c;whole&#x0201d; broadband response. If there were significant overlap in the cochlear regions, for example, the summed response would not resemble the broadband response to such a degree, and would instead be larger.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; Is this measure of SNR appropriate, when the baseline is artificially constructed by deconvolution and filtering? Perhaps noise level could be assessed by applying the deconvolution to a silent recording instead? It might also be useful to have a measure of the replicability of the response.</p></disp-quote><p>SNR estimate: Looking at the prestimulus baseline is akin to looking at the recording during the wrong stimulus, but with the advantage of having the same EEG state that the subject was in at the time of that recording (and thus the same amount of noise for that epoch) rather than at some other time in the recording session.</p><p>To confirm this we computed the wrong-stimulus responses (i.e, we shifted the stimulus trial by 1 so that we used the same EEG but the wrong stimulus trial for the regressor) for every subject and condition. We correlated the SNR estimates based on these shifted responses (using both the pre-stimulus baseline time windows and the response time windows) to the SNR estimates based on the pre-stimulus baseline of the real responses, which are shown in <xref ref-type="fig" rid="respfig1">Author response image 1</xref>. The correlations indicate that both methods provide similar estimates of SNR, but we prefer our method because it applies to a broader range of scenarios. If the reviewer feels like we should add this figure to the paper we can but out of respect for how lengthy the paper already is, we have left it out for now.</p><fig id="respfig1" orientation="portrait" position="float"><label>Author response image 1.</label><caption><title>SNR (dB) calculations based on the response pre-stimulus baseline as used in the paper versus based off the pre-stimulus baseline and stimulus response window of the responses calculated to the wrong stimulus.</title><p>The same EEG was used for each calculation. SNRs groups around the unity line, except for similar numbers of responses that were better/worse than the paper&#x02019;s SNR calculator for those responses that have poor SNR (&#x0003c;-5 dB SNR).</p></caption><graphic xlink:href="elife-62329-resp-fig1"/></fig><p>Replication: We have included in <xref ref-type="fig" rid="respfig2">Author response image 2</xref> a figure of replicated ABRs of the broadband peaky speech high-pass filtered at 150 Hz. The correlation coefficients are very high. We added added text to first Results section at the end of first paragraph:</p><p>&#x0201c;Responses containing an equal number of epochs form the first and second half of the recording had a median (interquartile range) correlation coefficient of 0.96 (0.95 &#x02013; 0.98) for the 0 &#x02013; 15 ms lags, indicating good replication.&#x0201d;</p><p>But for other sections in the paper we already report several correlations of even/odd splits, which are one kind of measure of replicability. We worry that adding even more analyses or figures could bog down the paper.</p><fig id="respfig2" orientation="portrait" position="float"><label>Author response image 2.</label><caption><title>Replication of ABR responses to broadband peaky speech for the 22 subjects in experiment 1.</title><p>Equal numbers of epochs for the first (black line) and second (gray line) halves of the recording were included in each replication. Correlation coefficients are provided in the top right corner for individual subjects. The median and interquartile range (IQR) are provided in the top right corner for the grand mean responses in the bottom right subplot.</p></caption><graphic xlink:href="elife-62329-resp-fig2"/></fig><disp-quote content-type="editor-comment"><p>&#x02013; "wave III was clearly identifiable in 16 of the 22 subjects": Figure 1 indicates that the word "clearly" may be somewhat generous. It would be worthwhile to discuss wave III and its identifiability in more detail (perhaps its identifiability/non-universality could be compared with that of another less prominent peak in traditionally obtained ABRs?).</p></disp-quote><p>An addition to our analyses, which adjusts our analysis for the small clock differences between the sound card and EEG equipment, has improved the early wave components of the responses. Now 19 of 22 subjects had responses with visible wave III. But we take the point that we should state rather than opine. We tempered our description of how clearly identifiable was the wave III. To get a sense for how these rates compare, we went back to the click-evoked responses from Maddox and Lee (2018), and 16/24 showed an identifiable wave III. Morphology was similar. We updated the text to read:</p><p>&#x0201c;Waves I and V were identifiable in responses from all subjects (N = 22), and wave III was identifiable in 19 of the 22 subjects. The numbers of subjects with identifiable waves I and III in these peaky speech responses were similar to the 24 and 16 out of 24 subjects for the click-evoked responses in Maddox and Lee (2018).&#x0201d;</p><p>[Editors' note: further revisions were suggested prior to acceptance, as described below.]</p><disp-quote content-type="editor-comment"><p>This manuscript presents an innovative alteration to speech to make it more peaky, resulting in stronger and better delineated responses in the auditory brainstem. Recent work has indeed employed naturalistic speech to investigate subcortical mechanisms of speech processing. However, previous methods were ill equipped to tease apart the neural responses in different parts of the brainstem. The authors show that their speech manipulation improves this issue: the peaky speech that they develop allows them to segregate different waves of the brainstem response. This development allows further and more refined investigations of the contribution of different parts of the brainstem to speech processing, as well as to hearing deficits.</p><p>The authors have made substantial additions and changes in the revised manuscript to complete and improve the analysis. These are overall satisfactory, but we still have some remaining points that we would like the authors to address.</p><p>&#x02013; Due to the additions, the Results section is now even longer, as none of the original text has been removed. We suggest to shorten it by moving some technical details from the Results section to the Materials and methods. For instance, this could concern the details of the peak picking, recording time, waveform repeatability (SNR analysis is implicit in the Recording time section) and details of statistical models. Furthermore, some sentences in the Results seem to serve only to summarize the previous section, which seems unnecessary, or make a general statement that is more suitable for the Discussion.</p></disp-quote><p>We have made several cuts to the Results section, making it more than 1200 words shorter than the previous version. Some information (e.g., technical details) has been moved to Materials and methods, some has been condensed into tables (parameters from latency fits), and many other details have been deemed extraneous and removed (e.g., specific details of ICC fits, when all were &#x02265;0.9).</p><disp-quote content-type="editor-comment"><p>&#x02013; "unless presented monaurally by headphones". So these studies did show detectable wave I/III waves when using monaural headphone presentation? This should be more clearly phrased. It seems strange to make this presentation mode sound like a limitation &#x02013; monaural presentation is standard in clinical audiology. This point raises further questions that had not previously occurred to us: can you clarify whether the peaky speech has also been tested using monaural presentation, and if so how the responses differ from diotic/dichotic presentation (and if not, why was diotic presentation chosen)? How would you confirm that dichotic presentation of the peaky speech produces purely ear-specific responses? Could there be potential binaural interactions or contralateral suppression mechanisms with diotic or dichotic presentation? These questions are relevant and should be briefly addressed.</p></disp-quote><p>We have changed this to say that no waves before wave V were reported. The original text was based on a figure from Miller&#x02019;s 2017 patent showing a single subject&#x02019;s response with apparent earlier waves, but their subsequent 2019 paper shows no hint of waves I or III. We are taking the peer-reviewed paper as the source of record, and have removed the patent citation.</p><p>We did diotic recordings because it makes responses bigger. We did dichotic recordings when we wanted separate responses from each ear, and we now note that &#x0201c;It is also possible that there was some contralateral suppression in our dichotic recordings, however it is unlikely that separate monaural presentation would enlarge responses enough to be worth the doubled recording time.&#x0201d;</p><p>The dichotic responses are guaranteed mathematically to be ear-specific because the regressors are orthogonal and correspond to stimuli which were presented to one ear and not the other (much in the same way the sham regressors in Figure 15&#x02014;figure supplement 2 produce zero responses.)</p><disp-quote content-type="editor-comment"><p>&#x02013; We are not sure what "region-specific" means here &#x02013; please clarify.</p></disp-quote><p>We revised the text to: &#x0201c;&#x02026;a speech-evoked response with the same components would assess subcortical structure-specific encoding&#x02026;&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; It is more common and reliable to use different peak pickers, rather than the same twice. It probably won't matter much here, but it would be preferable in future analyses.</p></disp-quote><p>Agreed, we will do this in future analyses.</p><disp-quote content-type="editor-comment"><p>&#x02013; It should be explained why the comparison is shown only for the high-pass filter at 30 Hz. The high-pass filter at 150 Hz seems more interesting, as it shows the wave I and III peaks in the peaky speech response that the unaltered speech does not.</p></disp-quote><p>This is a good point. We now explain that &#x0201c;&#x02026;(unlike the broadband peaky response, the 150 Hz high-pass cutoff does not reveal earlier components in the response to unaltered speech).&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; "completed two models". It might be more accurate to write "performed two simulations".</p></disp-quote><p>The revision was made: &#x0201c;To better understand the source of this pre-stimulus component &#x02013; and to determine whether later components were influencing the earliest components &#x02013; we performed two simulations:.&#x02026;&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; Subsection &#x0201c;More components of the ABR and MLR are present with broadband peaky than unaltered speech&#x0201d; paragraph three: Does "trial" here mean the time period starting at a glottal pulse? How long did this time period extend from there? From what time point on was the ABR zero-padded? These details should be clarified. It could be informative to also show the input kernels in Figure 3, i.e. the simulated "ABR" that did not show the pre-stimulus component.</p></disp-quote><p>We have clarified these details and included the ABR kernel for the linear simulation as a supplemental figure. &#x0201c;&#x02026; we performed two simulations: (1) a simple linear deconvolution model in which EEG for each 64 s epoch was simulated by convolving the rectified broadband peaky speech audio with an ABR kernel that did not show the pre-stimulus component (Figure 3&#x02014;figure supplement 1): the average broadband peaky speech ABR from 0 to 16 ms was zero-padded from 0 ms to the beginning of wave I (1.6 ms), windowed with a Hann function, normalized, and then zero-padded from &#x02212;16 ms to 0 ms to center the kernel;&#x02026;&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; Cortical responses are much larger and likely to be more affected by attention than the much smaller earliest responses. Is it known whether a high-pass filter at 150 Hz is sufficient to reliably remove all cortical contributions within the amplitude range of the ABR wave I? Can a reference be provided for this?</p></disp-quote><p>The cortical contribution to responses in this frequency range is debated (by, e.g., Coffey, Bidelman, and others). However, our modeling &#x02013; which only includes brainstem components &#x02013;strongly suggests that the broader pre-stimulus component can result from an imperfect fit of brainstem responses alone, and that the filtering can effectively remove it.</p><disp-quote content-type="editor-comment"><p>&#x02013; Strictly speaking, the stimuli differed not only in the narrator's identity but also in text. It would be interesting to comment on whether different texts with the same narrator are likely to give different responses.</p></disp-quote><p>We now note in the text that &#x0201c;These high odd-even correlations also show that responses from a single narrator are similar even if the text spoken is different. The overall male-female narrator differences for the ABR indicate that the choice of narrator for using peaky speech impacts the morphology of the early response.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; Figure 3 and text related to the more aggressive high-pass filtering at 200 Hz:</p><p>With this more aggressive filter, the 8 ms peak has flipped sign and gained additional latency. This needs to be explicitly acknowledged and addressed. It might be due to the peak of that filter's non-linear group delay at its cutoff frequency, combined with an abundance of signal power at the cutoff frequency. But there could be other reasons as well.</p></disp-quote><p>That large negative peak, while it draws the eye, is actually a common result of the more aggressive high-pass filtering. Wave V is still positive going and in fact has a shorter latency (another expected effect). We added this to the text: &#x0201c;As expected from more aggressive high-pass filtering, wave V became smaller, sharper, and earlier, and was followed by a significant negative deflection.&#x0201d;</p><disp-quote content-type="editor-comment"><p>&#x02013; A number of parameter estimates from the power law regression results are written as dimensionless but actually have units of ms. This includes &#x003c4;<sub>synaptic</sub> (should be 0.8 ms in several locations), &#x003c4;<sub>I-V</sub> (in several locations), and a (also in several locations).</p></disp-quote><p>Thank you for catching this. For brevity&#x02019;s sake, these parameters have been moved to a table and their units are now specified.</p></body></sub-article></article>