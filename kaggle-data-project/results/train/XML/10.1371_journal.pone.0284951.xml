<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.1d3 20150301//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1.dtd?><?SourceDTD.Version 39.96?><?ConverterInfo.XSLTName jats2jats3.xsl?><?ConverterInfo.Version 1?><?subarticle pone.0284951.r001?><?properties open_access?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">PLoS One</journal-id><journal-id journal-id-type="iso-abbrev">PLoS One</journal-id><journal-id journal-id-type="publisher-id">plos</journal-id><journal-title-group><journal-title>PLOS ONE</journal-title></journal-title-group><issn pub-type="epub">1932-6203</issn><publisher><publisher-name>Public Library of Science</publisher-name><publisher-loc>San Francisco, CA USA</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">10174584</article-id><article-id pub-id-type="doi">10.1371/journal.pone.0284951</article-id><article-id pub-id-type="publisher-id">PONE-D-21-37916</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Organisms</subject><subj-group><subject>Eukaryota</subject><subj-group><subject>Animals</subject><subj-group><subject>Vertebrates</subject><subj-group><subject>Amniotes</subject><subj-group><subject>Mammals</subject><subj-group><subject>Swine</subject></subj-group></subj-group></subj-group></subj-group></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Zoology</subject><subj-group><subject>Animals</subject><subj-group><subject>Vertebrates</subject><subj-group><subject>Amniotes</subject><subj-group><subject>Mammals</subject><subj-group><subject>Swine</subject></subj-group></subj-group></subj-group></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Medicine and Health Sciences</subject><subj-group><subject>Diagnostic Medicine</subject><subj-group><subject>Diagnostic Radiology</subject><subj-group><subject>Magnetic Resonance Imaging</subject></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Research and Analysis Methods</subject><subj-group><subject>Imaging Techniques</subject><subj-group><subject>Diagnostic Radiology</subject><subj-group><subject>Magnetic Resonance Imaging</subject></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Medicine and Health Sciences</subject><subj-group><subject>Radiology and Imaging</subject><subj-group><subject>Diagnostic Radiology</subject><subj-group><subject>Magnetic Resonance Imaging</subject></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Research and Analysis Methods</subject><subj-group><subject>Animal Studies</subject><subj-group><subject>Experimental Organism Systems</subject><subj-group><subject>Animal Models</subject><subj-group><subject>Pig Models</subject></subj-group></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Computer and Information Sciences</subject><subj-group><subject>Neural Networks</subject></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Neuroscience</subject><subj-group><subject>Neural Networks</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Engineering and Technology</subject><subj-group><subject>Industrial Engineering</subject><subj-group><subject>Quality Control</subject><subj-group><subject>Visual Inspection</subject></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Anatomy</subject><subj-group><subject>Musculoskeletal System</subject><subj-group><subject>Skeleton</subject><subj-group><subject>Skull</subject></subj-group></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Medicine and Health Sciences</subject><subj-group><subject>Anatomy</subject><subj-group><subject>Musculoskeletal System</subject><subj-group><subject>Skeleton</subject><subj-group><subject>Skull</subject></subj-group></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Research and Analysis Methods</subject><subj-group><subject>Imaging Techniques</subject><subj-group><subject>Neuroimaging</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Neuroscience</subject><subj-group><subject>Neuroimaging</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Anatomy</subject><subj-group><subject>Head</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Medicine and Health Sciences</subject><subj-group><subject>Anatomy</subject><subj-group><subject>Head</subject></subj-group></subj-group></subj-group></article-categories><title-group><article-title>Automated identification of piglet brain tissue from MRI images using Region-based Convolutional Neural Networks</article-title><alt-title alt-title-type="running-head">Automated identification of piglet brain tissue using neural networks</alt-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Stanke</surname><given-names>Kayla L.</given-names></name><role content-type="http://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role content-type="http://credit.niso.org/contributor-roles/data-curation/">Data curation</role><role content-type="http://credit.niso.org/contributor-roles/formal-analysis/">Formal analysis</role><role content-type="http://credit.niso.org/contributor-roles/investigation/">Investigation</role><role content-type="http://credit.niso.org/contributor-roles/methodology/">Methodology</role><role content-type="http://credit.niso.org/contributor-roles/software/">Software</role><role content-type="http://credit.niso.org/contributor-roles/validation/">Validation</role><role content-type="http://credit.niso.org/contributor-roles/visualization/">Visualization</role><role content-type="http://credit.niso.org/contributor-roles/writing-original-draft/">Writing &#x02013; original draft</role><role content-type="http://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><xref rid="aff001" ref-type="aff">
<sup>1</sup>
</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0003-0661-4321</contrib-id><name><surname>Larsen</surname><given-names>Ryan J.</given-names></name><role content-type="http://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role content-type="http://credit.niso.org/contributor-roles/data-curation/">Data curation</role><role content-type="http://credit.niso.org/contributor-roles/formal-analysis/">Formal analysis</role><role content-type="http://credit.niso.org/contributor-roles/funding-acquisition/">Funding acquisition</role><role content-type="http://credit.niso.org/contributor-roles/investigation/">Investigation</role><role content-type="http://credit.niso.org/contributor-roles/methodology/">Methodology</role><role content-type="http://credit.niso.org/contributor-roles/project-administration/">Project administration</role><role content-type="http://credit.niso.org/contributor-roles/resources/">Resources</role><role content-type="http://credit.niso.org/contributor-roles/software/">Software</role><role content-type="http://credit.niso.org/contributor-roles/supervision/">Supervision</role><role content-type="http://credit.niso.org/contributor-roles/validation/">Validation</role><role content-type="http://credit.niso.org/contributor-roles/visualization/">Visualization</role><role content-type="http://credit.niso.org/contributor-roles/writing-original-draft/">Writing &#x02013; original draft</role><role content-type="http://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><xref rid="aff001" ref-type="aff">
<sup>1</sup>
</xref><xref rid="cor001" ref-type="corresp">*</xref></contrib><contrib contrib-type="author"><name><surname>Rund</surname><given-names>Laurie</given-names></name><role content-type="http://credit.niso.org/contributor-roles/funding-acquisition/">Funding acquisition</role><role content-type="http://credit.niso.org/contributor-roles/investigation/">Investigation</role><role content-type="http://credit.niso.org/contributor-roles/project-administration/">Project administration</role><role content-type="http://credit.niso.org/contributor-roles/resources/">Resources</role><role content-type="http://credit.niso.org/contributor-roles/supervision/">Supervision</role><xref rid="aff001" ref-type="aff">
<sup>1</sup>
</xref></contrib><contrib contrib-type="author"><name><surname>Leyshon</surname><given-names>Brian J.</given-names></name><role content-type="http://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role content-type="http://credit.niso.org/contributor-roles/funding-acquisition/">Funding acquisition</role><role content-type="http://credit.niso.org/contributor-roles/investigation/">Investigation</role><role content-type="http://credit.niso.org/contributor-roles/project-administration/">Project administration</role><role content-type="http://credit.niso.org/contributor-roles/supervision/">Supervision</role><role content-type="http://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><xref rid="aff002" ref-type="aff">
<sup>2</sup>
</xref></contrib><contrib contrib-type="author"><name><surname>Louie</surname><given-names>Allison Y.</given-names></name><role content-type="http://credit.niso.org/contributor-roles/investigation/">Investigation</role><role content-type="http://credit.niso.org/contributor-roles/resources/">Resources</role><xref rid="aff003" ref-type="aff">
<sup>3</sup>
</xref></contrib><contrib contrib-type="author"><name><surname>Steelman</surname><given-names>Andrew J.</given-names></name><role content-type="http://credit.niso.org/contributor-roles/funding-acquisition/">Funding acquisition</role><role content-type="http://credit.niso.org/contributor-roles/supervision/">Supervision</role><role content-type="http://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><xref rid="aff001" ref-type="aff">
<sup>1</sup>
</xref><xref rid="aff003" ref-type="aff">
<sup>3</sup>
</xref><xref rid="aff004" ref-type="aff">
<sup>4</sup>
</xref><xref rid="aff005" ref-type="aff">
<sup>5</sup>
</xref></contrib></contrib-group><aff id="aff001"><label>1</label>
<addr-line>Department of Animal Sciences, University of Illinois Urbana-Champaign, Champaign, Illinois, United States of America</addr-line></aff><aff id="aff002"><label>2</label>
<addr-line>Abbott Nutrition, Discovery Research, Columbus, Ohio, United States of America</addr-line></aff><aff id="aff003"><label>3</label>
<addr-line>Division of Nutritional Sciences, University of Illinois Urbana-Champaign, Champaign, Illinois, United States of America</addr-line></aff><aff id="aff004"><label>4</label>
<addr-line>Neuroscience Program, University of Illinois Urbana-Champaign, Champaign, Illinois, United States of America</addr-line></aff><aff id="aff005"><label>5</label>
<addr-line>Carl R. Woese Institute for Genomic Biology, University of Illinois Urbana-Champaign, Champaign, Illinois, United States of America</addr-line></aff><contrib-group><contrib contrib-type="editor"><name><surname>Wang</surname><given-names>Zhishun</given-names></name><role>Editor</role><xref rid="edit1" ref-type="aff"/></contrib></contrib-group><aff id="edit1">
<addr-line>Columbia University, UNITED STATES</addr-line>
</aff><author-notes><fn fn-type="COI-statement" id="coi001"><p><bold>Competing Interests: </bold>The authors have declared that no competing interests exist.</p></fn><corresp id="cor001">* E-mail: <email><EMAIL></email></corresp></author-notes><pub-date pub-type="epub"><day>11</day><month>5</month><year>2023</year></pub-date><pub-date pub-type="collection"><year>2023</year></pub-date><volume>18</volume><issue>5</issue><elocation-id>e0284951</elocation-id><history><date date-type="received"><day>2</day><month>12</month><year>2021</year></date><date date-type="accepted"><day>12</day><month>4</month><year>2023</year></date></history><permissions><copyright-statement>&#x000a9; 2023 Stanke et al</copyright-statement><copyright-year>2023</copyright-year><copyright-holder>Stanke et al</copyright-holder><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This is an open access article distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use, distribution, and reproduction in any medium, provided the original author and source are credited.</license-p></license></permissions><self-uri content-type="pdf" xlink:href="pone.0284951.pdf"/><abstract><p>Magnetic resonance imaging is an important tool for characterizing volumetric changes of the piglet brain during development. Typically, an early step of an imaging analysis pipeline is brain extraction, or skull stripping. Brain extractions are usually performed manually; however, this approach is time-intensive and can lead to variation between brain extractions when multiple raters are used. Automated brain extractions are important for reducing the time required for analyses and improving the uniformity of the extractions. Here we demonstrate the use of Mask R-CNN, a Region-based Convolutional Neural Network (R-CNN), for automated brain extractions of piglet brains. We validate our approach using Nested Cross-Validation on six sets of training/validation data drawn from 32 pigs. Visual inspection of the extractions shows acceptable accuracy, Dice coefficients are in the range of 0.95&#x02013;0.97, and Hausdorff Distance values in the range of 4.1&#x02013;8.3 voxels. These results demonstrate that R-CNNs provide a viable tool for skull stripping of piglet brains.</p></abstract><funding-group><award-group id="award001"><funding-source>
<institution-wrap><institution-id institution-id-type="funder-id">http://dx.doi.org/10.13039/100011947</institution-id><institution>Abbott Nutrition</institution></institution-wrap>
</funding-source><award-id>********</award-id><principal-award-recipient>
<name><surname>Steelman</surname><given-names>Andrew J.</given-names></name>
</principal-award-recipient></award-group><funding-statement>The study was funded by Abbott Nutrition, <ext-link xlink:href="http://www.abbott.com" ext-link-type="uri">www.abbott.com</ext-link>, grant number ******** to A.S. The funder aided with study design, data analysis, decision to publish, and preparation of the manuscript.</funding-statement></funding-group><counts><fig-count count="6"/><table-count count="0"/><page-count count="11"/></counts><custom-meta-group><custom-meta id="data-availability"><meta-name>Data Availability</meta-name><meta-value>All relevant data have been uploaded to the Illinois Data Bank: <ext-link xlink:href="https://doi.org/10.13012/B2IDB-5784165_V1" ext-link-type="uri">https://doi.org/10.13012/B2IDB-5784165_V1</ext-link>.</meta-value></custom-meta></custom-meta-group></article-meta><notes><title>Data Availability</title><p>All relevant data have been uploaded to the Illinois Data Bank: <ext-link xlink:href="https://doi.org/10.13012/B2IDB-5784165_V1" ext-link-type="uri">https://doi.org/10.13012/B2IDB-5784165_V1</ext-link>.</p></notes></front><body><sec sec-type="intro" id="sec001"><title>Introduction</title><p>Piglets are an important translational model for measuring the effect of nutrition on brain development. Not only do piglet brain stages of development correlate with human infant development, but their nutritional requirements are also comparable [<xref rid="pone.0284951.ref001" ref-type="bibr">1</xref>]. Magnetic resonance imaging (MRI) is an important technique for obtaining non-invasive measurements of brain volumes. An early step in volumetric analysis is the identification or &#x0201c;extraction&#x0201d; of the brain from the surrounding tissue. Manual tracing has been the gold standard for brain extraction and is performed by creating an outline that separates the surrounding skull, muscles, tissues, and fat from the brain [<xref rid="pone.0284951.ref001" ref-type="bibr">1</xref>&#x02013;<xref rid="pone.0284951.ref006" ref-type="bibr">6</xref>]. However, the method is not ideal when working with large data sets because it is time intensive and is subject to inconsistencies between raters/evaluators. Automated brain extraction techniques are needed to overcome these limitations. However, reports of automated extractions of pig brains are limited. A graph theory approach that makes use of prior information of anatomical structures has been used to perform automated extraction of piglet brains [<xref rid="pone.0284951.ref007" ref-type="bibr">7</xref>]. However, deep learning technologies offer the possibility of automating the training of anatomical structures to enable brain extractions [<xref rid="pone.0284951.ref008" ref-type="bibr">8</xref>&#x02013;<xref rid="pone.0284951.ref010" ref-type="bibr">10</xref>]. A U-Net model trained on humans, non-human primates, and 3 pig scans has been shown to successfully perform brain extractions on 2 pig scans [<xref rid="pone.0284951.ref011" ref-type="bibr">11</xref>]. Also, a patch-based 3D U-Net trained on piglets has been used for successful brain extractions of piglets of multiple ages, via transfer learning [<xref rid="pone.0284951.ref012" ref-type="bibr">12</xref>]. An alternative segmentation tool is Mask R-CNN, a Region-based Convolutional Neural Network (R-CNN). This tool has been used to create a mouse brain atlas that is generalizable across developmental ages and imaging modalities [<xref rid="pone.0284951.ref010" ref-type="bibr">10</xref>]. These results suggest that Mask R-CNN may be effective for piglet brain extraction. Here we demonstrate the use of Mask R-CNN for automated brain extractions of piglet brains.</p></sec><sec sec-type="materials|methods" id="sec002"><title>Methods</title><sec id="sec003"><title>Animals and care practices</title><p>All animal care and handling procedures were approved by the University of Illinois Institutional Animal Care and Use Committee (Protocol #18256) and were in accordance with federal guidelines. Male and female average-for-gestational age, Yorkshire crossbred, full-term, naturally-delivered piglets were obtained from University of Illinois Swine Farm at postnatal day 2 to allow for colostrum consumption. All piglets remained intact but did undergo routine processing on the farm including iron dextran (Henry Schein Animal Health, Dublin, OH, USA) and antibiotic injection (EXCEDE&#x000ae;, Zoetis, Parsippany, NJ 07054, USA) per routine farm practice and according to label. Four groups of piglets were placed individually into a caging system under standard conditions as described in a previous publication [<xref rid="pone.0284951.ref013" ref-type="bibr">13</xref>] and randomly assigned to five diet treatment groups. The control group of piglets remained with the sow until day 28 of age with free access to suckle and were weighed daily. The current study does not distinguish between diet groups.</p><p>Sow-raised piglets were handled daily, as were the artificially raised animals to diminish differences between the two types of rearing. After arrival at the Edward R. Madigan Laboratory (ERML) Animal Facility, experimental piglets received one dose of antibiotic: the first of two cohorts received Spectraguard<sup>TM</sup> (Bimeda, Inc, Oakbrook Terrace, IL 60181) on postnatal day 2 and the second cohort received Baytril&#x000ae; (Bayer healthcare LLC Shawnee Mission, KS 66201) on postnatal day 4. Additional doses of antibiotic were administered during the experiment only under the direction of the staff veterinarian. Animals were individually housed in racks of metabolism cages specifically designed to artificially rear neonatal piglets under constant 12-h light/dark cycles. Piglet housing at ERML was as follows: space allowance for individual piglets was 30" deep, 23" wide, and 18.5" high, providing 4.8 square feet of floor space per piglet. Each piglet was supplied with a heating pad, toy, and blanket. Room temperatures were kept at 85&#x02013;95&#x000b0;F using space heaters. Animals were also offered water or BlueLite&#x000ae; electrolyte solution ad libitum. Cages and heating pads were disinfected, and toys and blankets replaced daily. Animal care protocols were in accordance with National Institutes of Health Guidelines for Care and Use of Laboratory Animals and were approved by the University of Illinois Laboratory Animal Care and Use Committee.</p></sec><sec id="sec004"><title>MRI acquisition</title><p>MRI data were acquired using 3 T Prisma scanner (Siemens, Erlangen) housed at the Biomedical Imaging Center at the University of Illinois. Pigs were anesthetized using TKX (combination of 2.5 ml of xylazine (100 mg/ml) and 2.5 ml of ketamine (100mg/ml) added to a Telazol vial and administered at a dosage of 0.02&#x02013;0.03 ml/kg IM) then maintained on isofluorane (1&#x02013;3%) throughout the imaging. Animals were scanned in the supine position using a specialized piglet head coil (Rapid Biomed, Rimpar). During scanning, the respiration rate, heart rate and blood oxygen levels were monitored using a LifeWindow LW9x monitor (Digicare, Boynton Beach, FL).</p><p>MRI Structural Imaging: Our structural MRI scan consisted of a 3D MPRAGE acquisition (voxel size = 0.6 x 0.6 x 0.6 mm<sup>3</sup>, FOV = 173 x 173 mm<sup>2</sup>, 256 slices, GRAPPA&#x02014;GeneRalized Autocalibrating Partial Parallel Acquisition&#x02014;acceleration factor R = 2; TR = 2060 ms, TI = 1060 ms, flip angle = 9&#x02070;, for an overall scan time of 5:21 min).</p></sec><sec id="sec005"><title>Manual brain extraction</title><p>Manual brain extraction was facilitated by first performing a rigid alignment of T<sub>1</sub>-weighted images from all piglets to the brain atlas [<xref rid="pone.0284951.ref014" ref-type="bibr">14</xref>]. This was done using SPM12 imaging analysis software. First, we performed a manual rotation to approximately align the T<sub>1</sub>-weighted images with 28-day piglet template [<xref rid="pone.0284951.ref014" ref-type="bibr">14</xref>], without resampling. We then used the &#x0201c;coregistration&#x0201d; function of SPM12 to further align the T<sub>1</sub>-weighted images to the template, again without resampling. The resulting alignment was accurate for all piglets, even though it was performed without first performing brain extraction, as shown by a representative image in <xref rid="pone.0284951.g001" ref-type="fig">Fig 1</xref>.</p><fig position="float" id="pone.0284951.g001"><object-id pub-id-type="doi">10.1371/journal.pone.0284951.g001</object-id><label>Fig 1</label><caption><title>Results of coregistration of piglet brain to the average brain template.</title><p>The top row shows images from a representative piglet brain and bottom row shows the same slices from the average brain template. Blue lines within each image indicate the locations of the perpendicular slices. The consistency of two sets of images confirms a good approximate alignment of the T<sub>1</sub>-weighted image with the average brain template.</p></caption><graphic xlink:href="pone.0284951.g001" position="float"/></fig><p>An atlas-based brain mask was then resampled into the native space of each piglet, providing initial estimates of the brain masks for each piglet. These initial estimates were then modified to create individualized brain masks using Slicer3D, as shown in <xref rid="pone.0284951.g002" ref-type="fig">Fig 2</xref>. Most revisions were done in the sagittal plane, but all three orthogonal views were reviewed and modified for improved precision. All extractions were performed by one rater to minimize variability. No image intensity normalization was performed prior to manual or automated brain extraction.</p><fig position="float" id="pone.0284951.g002"><object-id pub-id-type="doi">10.1371/journal.pone.0284951.g002</object-id><label>Fig 2</label><caption><title>Demonstration of the creation of manually-defined brain mask via modification of the template brain mask.</title><p>A representative T<sub>1</sub>-weighted image is overlayed with the template brain mask, creating a brighter region. The green outline shows the manual-designated brain mask. The manual brain mask was created by editing the template brain mask for each individual piglet.</p></caption><graphic xlink:href="pone.0284951.g002" position="float"/></fig></sec><sec id="sec006"><title>Automated brain extraction</title><p>This study uses a deep learning instance segmentation neural network using object detection model Mask R-CNN with Inception Resnet v2 architecture [<xref rid="pone.0284951.ref015" ref-type="bibr">15</xref>], pretrained on the COCO 2017 data set [<xref rid="pone.0284951.ref016" ref-type="bibr">16</xref>]. Our model uses a Tensor Flow 2.4.0 implementation of Mask R-CNN [<xref rid="pone.0284951.ref015" ref-type="bibr">15</xref>], with feature extractor Faster R-CNN [<xref rid="pone.0284951.ref017" ref-type="bibr">17</xref>]. Faster R-CNN utilizes a Region Proposal Network (RPN) to select object proposals from a backbone, which for our study was generated using a combined ResNet101 and Feature Pyramid Network (FPN) [<xref rid="pone.0284951.ref018" ref-type="bibr">18</xref>].</p><p>Training was performed using a single NVIDIA GeForce GTX 1070 Ti GPU, with NVIDIA developer driver 465.21, CUDA 11.0, and CUDNN library 8.0.4. The network was trained with a cosine decay learning rate of 0.008 and a momentum optimizer value of 0.9, and a batch size of two images, or slices, per iteration. We performed 200,000 iterations, or 48.8 epochs. The training and segmentation were performed only in 2D sagittal planes. During evaluation, predicted masks were binarized at a confidence parameter threshold of 0.5. The masks created from the 2D slices were then combined into 3D datasets for final cleaning. This involved largest connected component (LCC) filtering to remove several small and spurious masks, typically occurring in slices that did not include brain. Cleaning was done by first using the Matlab function &#x0201c;bwconncomp&#x0201d; to identify all isolated masks, consisting of one or more connected voxels, where connected voxels are defined as those with touching faces. Then we discarded all but the largest mask, or brain mask.</p></sec><sec id="sec007"><title>Validation</title><p>Nested Cross-Validation was used to evaluate the performance of the training models. This method has been shown to produce unbiased performance estimates, even in small datasets [<xref rid="pone.0284951.ref019" ref-type="bibr">19</xref>]. We randomly assigned each of the 32 pigs to one of six test groups. Four of the test groups consisted of five pigs, and two of the test groups consisted of six pigs. For each test group, a training model was generated using the remaining pigs, beginning with the same architecture which was na&#x000ef;ve to the test images. Validation of the test groups was performed by comparing the machine-generated masks with the manually generated masks by visual inspection, by computing Dice coefficients, 3D Hausdorff Distance (HD) values, and Pearson correlations between the manual and machine-generated masks. Dice coefficients were calculated using the formula 2|<italic toggle="yes">X</italic>&#x02229;<italic toggle="yes">Y</italic>|/(|<italic toggle="yes">X</italic>|+|<italic toggle="yes">Y</italic>|), where |&#x02026;| indicates the number of voxels within the mask, &#x02229; indicates the union, and <italic toggle="yes">X</italic> and Y indicate the manual and machine-generated masks. Dice coefficients were calculated before and after LCC filtering. We calculated 3D Hausdorff Distance (HD) values, using the freely-available &#x0201c;EvaluateSegmentation&#x0201d; command-line tool (<ext-link xlink:href="https://github.com/Visceral-Project/EvaluateSegmentation" ext-link-type="uri">https://github.com/Visceral-Project/EvaluateSegmentation</ext-link>) [<xref rid="pone.0284951.ref020" ref-type="bibr">20</xref>]. Because HD values are sensitive to outliers eliminated by LCC filtering, HD values were only calculated after LCC filtering [<xref rid="pone.0284951.ref020" ref-type="bibr">20</xref>].</p></sec></sec><sec sec-type="results" id="sec008"><title>Results</title><p>Visual inspection of the brain extractions reveals good accuracy of automatic brain extractions (<xref rid="pone.0284951.g003" ref-type="fig">Fig 3</xref>). The six models were labelled with letters from A to F. We found that Model D failed to identify the brain within several sagittal slices of one of the test pigs, as shown in <xref rid="pone.0284951.g004" ref-type="fig">Fig 4(A) and 4(B)</xref>. These slices included an unusually bright region in the subcutaneous fat layer near the superior area of the head (see <xref rid="pone.0284951.g004" ref-type="fig">Fig 4(B)</xref>). This bright region was removed by manually outlining it in one of the slices, and then removing the traced voxels from all the slices (see <xref rid="pone.0284951.g004" ref-type="fig">Fig 4(D)</xref>). The modified structural images were then re-evaluated using the same model, producing a more accurate brain mask (see <xref rid="pone.0284951.g004" ref-type="fig">Fig 4(C) and 4(D)</xref>). This manual correction boosted the Dice coefficient of the pre-LCC filtered images from 0.91 to 0.96. The brain mask from the modified images was used for subsequent validation.</p><fig position="float" id="pone.0284951.g003"><object-id pub-id-type="doi">10.1371/journal.pone.0284951.g003</object-id><label>Fig 3</label><caption><title>Sample extractions from two piglets.</title><p>The overlay that creates a brighter region indicates machine extractions, and the green outline shows the manual brain masks. The top row shows a piglet tested using Model A, and the bottom row shows a piglet tested using Model B.</p></caption><graphic xlink:href="pone.0284951.g003" position="float"/></fig><fig position="float" id="pone.0284951.g004"><object-id pub-id-type="doi">10.1371/journal.pone.0284951.g004</object-id><label>Fig 4</label><caption><title>A segmentation error generated by Model D that was subsequently corrected.</title><p>Panels (a) and (b) show the inaccurate mask, indicated by green lines, and panels (c) and (d) show the mask generated by the same model after image modification, with the same views and the same slices. Axial slices are shown in (a) and (c); sagittal slices are shown in (b) and (d). The yellow lines in the axial views, (a) and (c), show the location of the sagittal view and the red lines in the sagittal views, (b) and (d), show the location of the axial views. For the inaccurate mask there are several sagittal slices in which no brain regions were identified, as shown in (a). These slices included a bright region in the fat layer of the superior region of the head, as shown in (b). The bright region was manually traced and removed from the same voxels of all sagittal slices as shown in (d). A re-evaluation of the edited images using the same model, Model D, produced an accurate brain extraction, as shown in (c) and (d).</p></caption><graphic xlink:href="pone.0284951.g004" position="float"/></fig><p>Visual inspection of the results also revealed that the LCC filter was important for one of the pigs, for which the automated brain extraction inaccurately identified large brain patches within 5 slices outside of the head. This pig exhibited a Dice coefficient of 0.92, which improved to 0.957 after LCC filtration. The LCC filter improved Dice coefficients for all other pigs as well. However, this benefit was smaller due to the lower volumes of outlier voxels; for the remaining 31 pigs, the maximum improvement in Dice coefficient was 0.008 and the mean improvement was 0.001. The final brain extractions, after application of the LCC filter, exhibited Dice coefficients in the range of 0.95&#x02013;0.97 (mean: 0.961, standard deviation: 0.0036, see histogram in <xref rid="pone.0284951.g005" ref-type="fig">Fig 5(A)</xref>), and HD values in the range of 4.1&#x02013;8.3 voxels (mean: 5.48, standard deviation: 1.16, see histogram in <xref rid="pone.0284951.g005" ref-type="fig">Fig 5(B)</xref>), or 2.5&#x02013;5.0 mm (mean: 3.3, standard deviation: 0.7). The Pearson correlation coefficient, <italic toggle="yes">R</italic>, of the volumes, <italic toggle="yes">V<sub>manual</sub></italic> and <italic toggle="yes">V<sub>machine</sub></italic>, of the manual and machine-generated masks was R = 0.90, with p&#x0003c;0.001 (see <xref rid="pone.0284951.g006" ref-type="fig">Fig 6</xref>).</p><fig position="float" id="pone.0284951.g005"><object-id pub-id-type="doi">10.1371/journal.pone.0284951.g005</object-id><label>Fig 5</label><caption><p>Histograms of (a) Dice coefficients and (b) Hausdorff Distance (HD) values calculated from each of the 32 test cases after LCC filtration.</p></caption><graphic xlink:href="pone.0284951.g005" position="float"/></fig><fig position="float" id="pone.0284951.g006"><object-id pub-id-type="doi">10.1371/journal.pone.0284951.g006</object-id><label>Fig 6</label><caption><title>The consistency of brain volumes, <italic toggle="yes">V<sub>manual</sub></italic> and <italic toggle="yes">V<sub>machine</sub></italic>, from manual and machine brain extractions, respectively.</title><p>The solid line indicates unity and the colors denote the models used. Brain volume units are cubic centimeters.</p></caption><graphic xlink:href="pone.0284951.g006" position="float"/></fig></sec><sec sec-type="conclusions" id="sec009"><title>Discussion</title><p>We have shown that Mask R-CNN trained on manually generated masks can be used to perform accurate piglet brain extractions. However, for one of the 32 test cases, a model failed to identify brain tissue within several slices. This problem was eliminated when the evaluation was repeated after the removal of an unusually bright region of subcutaneous fat from the images. It is possible that that problem could have been avoided by using more training data, or by performing a bias field correction, or intensity normalization, before training. Image intensity normalization is often important for automated segmentation [<xref rid="pone.0284951.ref021" ref-type="bibr">21</xref>, <xref rid="pone.0284951.ref022" ref-type="bibr">22</xref>]. We did not perform this step before brain extraction, because in SPM, a bias field correction is typically done simultaneous to tissue segmentation [<xref rid="pone.0284951.ref023" ref-type="bibr">23</xref>]. However, incorporation of a normalization before brain extraction might improve the generalization of our model to other scanning conditions, such as different scanners and coils.</p><p>Dice coefficients were &#x0003e;0.95 and HD values were &#x0003c;5 mm; these are similar to values that have been achieved by neural networks for the skull-stripping of non-human primates [<xref rid="pone.0284951.ref011" ref-type="bibr">11</xref>], rodents [<xref rid="pone.0284951.ref024" ref-type="bibr">24</xref>, <xref rid="pone.0284951.ref025" ref-type="bibr">25</xref>], and piglets [<xref rid="pone.0284951.ref012" ref-type="bibr">12</xref>]. In contrast to our study, Ref [<xref rid="pone.0284951.ref012" ref-type="bibr">12</xref>] employed a 3D patch-based U-net architecture. The potential strength of a 3D patch is that the added depth dimension enhances the local information available to the neural network. However, because of the higher memory demands of using 3D images, the model training and inference steps in Ref. [<xref rid="pone.0284951.ref012" ref-type="bibr">12</xref>] were performed on cubic patches of 32<sup>3</sup> voxels. A potential disadvantage of this approach is that the segmented patches from the test images must be aggregated and reconciled. By contrast, a potential strength of a 2D approach is the potential to train on complete slices, thereby simplifying post-processing. In Ref. [<xref rid="pone.0284951.ref012" ref-type="bibr">12</xref>] the Dice coefficients from the final method, including post-processing, were in the range of 0.94&#x02013;0.96 (mean 0.952, standard deviation: 0.0069), slightly lower than the Dice coefficients observed in our study (0.95&#x02013;0.97, mean: 0.961, standard deviation: 0.0036). Similarly, HD values from Ref. [<xref rid="pone.0284951.ref012" ref-type="bibr">12</xref>] were 5.4&#x02013;14.3 voxels (mean: 8.51, standard deviation: 2.20), slightly higher than the HD values of our study (4.1&#x02013;8.3 voxels, mean: 5.48, standard deviation: 1.16). The higher Dice coefficients and lower HD values of our study could be influenced by multiple factors, including image quality differences, which are sensitive to factors such as age differences in piglets, equipment used, and acquisition times. Clearly, a quantitatively accurate comparison of brain extraction methods would require use of the same MRI data. It is possible that the differences in performance metrics were driven by the choice of network architecture. Direct comparisons of Mask R-CNN and U-Net architectures have favored U-Net architectures for segmentation [<xref rid="pone.0284951.ref026" ref-type="bibr">26</xref>] and Mask R-CNN object detection [<xref rid="pone.0284951.ref027" ref-type="bibr">27</xref>, <xref rid="pone.0284951.ref028" ref-type="bibr">28</xref>]; giving rise to a hybrid methods that exploit the relative strengths of both methods [<xref rid="pone.0284951.ref029" ref-type="bibr">29</xref>], or that improve upon segmentation abilities of Mask R-CNN [<xref rid="pone.0284951.ref030" ref-type="bibr">30</xref>]. Despite the potential drawbacks of Mask R-CNN for segmentation, our results demonstrate that it can be suitable for this application.</p><p>Improvements to our approach could be implemented in a variety of ways. Training with a larger sample size is expected to increase performance and accuracy of machine learning algorithms [<xref rid="pone.0284951.ref031" ref-type="bibr">31</xref>]. Performance may also be improved by hyperparameter tuning and optimization [<xref rid="pone.0284951.ref032" ref-type="bibr">32</xref>, <xref rid="pone.0284951.ref033" ref-type="bibr">33</xref>]. Improved performance might have been obtained by using 3D Mask R-CNN; however we employed 2D Mask R-CNN to obtain shorter training times [<xref rid="pone.0284951.ref034" ref-type="bibr">34</xref>]. The use of 3D, or 2.5D, segmentation has the potential to create a brain mask with greater smoothness at the edge of the brain between adjacent slices. However, the non-smoothness of the automated segmentations is similar to that seen with manual brain extractions, which are also performed in 2D. Also, any inaccuracies due to non-smoothness appear to be localized in the CSF layer surrounding the brain and unlikely to influence the results of automated segmentation of grey matter on the edge of the brain, performed on the extracted brain images. Brain exaction performance may also be improved by using quantitative imaging techniques, such as MT saturation [<xref rid="pone.0284951.ref035" ref-type="bibr">35</xref>, <xref rid="pone.0284951.ref036" ref-type="bibr">36</xref>], to improve the contrast between brain and non-brain tissues. Further research is required to access whether network architectures such as U-Net [<xref rid="pone.0284951.ref037" ref-type="bibr">37</xref>], may improve upon results obtained with Mask R-CNN.</p><p>In summary, the use of automated brain extraction has the potential to reduce analysis time because it requires minimal supervision. This process is scalable to a high number of piglets, avoiding complications and inconsistencies that might arise from having multiple raters perform manual brain extractions. The effectiveness of Mask R-CNN for performing piglet brain extractions implies that it may be a useful tool for segmenting sub-regions of the brain. Further research is needed to assess whether such an approach may compliment or improve upon existing methods for volumetric analysis of piglet brain MRI data [<xref rid="pone.0284951.ref004" ref-type="bibr">4</xref>, <xref rid="pone.0284951.ref023" ref-type="bibr">23</xref>, <xref rid="pone.0284951.ref038" ref-type="bibr">38</xref>].</p></sec></body><back><ack><p>This work was conducted in part at the Biomedical Imaging Center of the Beckman Institute for Advanced Science and Technology at the University of Illinois Urbana-Champaign (UIUC-BI-BIC).</p></ack><ref-list><title>References</title><ref id="pone.0284951.ref001"><label>1</label><mixed-citation publication-type="journal"><name><surname>Mudd</surname><given-names>AT</given-names></name>, <name><surname>Dilger</surname><given-names>RN</given-names></name>. <article-title>Early-Life Nutrition and Neurodevelopment: Use of the Piglet as a Translational Model</article-title>. <source>Advances in Nutrition</source>. <year>2017</year>;<volume>8</volume>(<issue>1</issue>):<fpage>92</fpage>&#x02013;<lpage>104</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.3945/an.116.013243</pub-id>
<?supplied-pmid 28096130?><pub-id pub-id-type="pmid">28096130</pub-id></mixed-citation></ref><ref id="pone.0284951.ref002"><label>2</label><mixed-citation publication-type="journal"><name><surname>Bokde</surname><given-names>AL</given-names></name>, <name><surname>Teipel</surname><given-names>SJ</given-names></name>, <name><surname>Schwarz</surname><given-names>R</given-names></name>, <name><surname>Leinsinger</surname><given-names>G</given-names></name>, <name><surname>Buerger</surname><given-names>K</given-names></name>, <name><surname>Moeller</surname><given-names>T</given-names></name>, <etal>et al</etal>. <article-title>Reliable manual segmentation of the frontal, parietal, temporal, and occipital lobes on magnetic resonance images of healthy subjects</article-title>. <source>Brain Res Brain Res Protoc</source>. <year>2005</year>;<volume>14</volume>(<issue>3</issue>):<fpage>135</fpage>&#x02013;<lpage>45</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.brainresprot.2004.10.001</pub-id>
<?supplied-pmid 15795167?><pub-id pub-id-type="pmid">15795167</pub-id></mixed-citation></ref><ref id="pone.0284951.ref003"><label>3</label><mixed-citation publication-type="journal"><name><surname>Conrad</surname><given-names>MS</given-names></name>, <name><surname>Dilger</surname><given-names>RN</given-names></name>, <name><surname>Nickolls</surname><given-names>A</given-names></name>, <name><surname>Johnson</surname><given-names>RW</given-names></name>. <article-title>Magnetic resonance imaging of the neonatal piglet brain</article-title>. <source>Pediatr Res</source>. <year>2012</year>;<volume>71</volume>(<issue>2</issue>):<fpage>179</fpage>&#x02013;<lpage>84</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1038/pr.2011.21</pub-id>
<?supplied-pmid 22258129?><pub-id pub-id-type="pmid">22258129</pub-id></mixed-citation></ref><ref id="pone.0284951.ref004"><label>4</label><mixed-citation publication-type="journal"><name><surname>Gan</surname><given-names>H</given-names></name>, <name><surname>Zhang</surname><given-names>Q</given-names></name>, <name><surname>Zhang</surname><given-names>H</given-names></name>, <name><surname>Chen</surname><given-names>Y</given-names></name>, <name><surname>Lin</surname><given-names>J</given-names></name>, <name><surname>Kang</surname><given-names>T</given-names></name>, <etal>et al</etal>. <article-title>Development of new population-averaged standard templates for spatial normalization and segmentation of MR images for postnatal piglet brains</article-title>. <source>Magn Reson Imaging</source>. <year>2014</year>;<volume>32</volume>(<issue>10</issue>):<fpage>1396</fpage>&#x02013;<lpage>402</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.mri.2014.08.036</pub-id>
<?supplied-pmid 25179132?><pub-id pub-id-type="pmid">25179132</pub-id></mixed-citation></ref><ref id="pone.0284951.ref005"><label>5</label><mixed-citation publication-type="journal"><name><surname>Mudd</surname><given-names>AT</given-names></name>, <name><surname>Alexander</surname><given-names>LS</given-names></name>, <name><surname>Berding</surname><given-names>K</given-names></name>, <name><surname>Waworuntu</surname><given-names>RV</given-names></name>, <name><surname>Berg</surname><given-names>BM</given-names></name>, <name><surname>Donovan</surname><given-names>SM</given-names></name>, <etal>et al</etal>. <article-title>Dietary Prebiotics, Milk Fat Globule Membrane, and Lactoferrin Affects Structural Neurodevelopment in the Young Piglet</article-title>. <source>Front Pediatr</source>. <year>2016</year>;<volume>4</volume>:<fpage>4</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.3389/fped.2016.00004</pub-id>
<?supplied-pmid 26870719?><pub-id pub-id-type="pmid">26870719</pub-id></mixed-citation></ref><ref id="pone.0284951.ref006"><label>6</label><mixed-citation publication-type="journal"><name><surname>Leyshon</surname><given-names>BJ</given-names></name>, <name><surname>Radlowski</surname><given-names>EC</given-names></name>, <name><surname>Mudd</surname><given-names>AT</given-names></name>, <name><surname>Steelman</surname><given-names>AJ</given-names></name>, <name><surname>Johnson</surname><given-names>RW</given-names></name>. <article-title>Postnatal Iron Deficiency Alters Brain Development in Piglets</article-title>. <source>J Nutr</source>. <year>2016</year>;<volume>146</volume>(<issue>7</issue>):<fpage>1420</fpage>&#x02013;<lpage>7</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.3945/jn.115.223636</pub-id>
<?supplied-pmid 27281804?><pub-id pub-id-type="pmid">27281804</pub-id></mixed-citation></ref><ref id="pone.0284951.ref007"><label>7</label><mixed-citation publication-type="journal"><name><surname>Durandeau AF</surname><given-names>J.-B.</given-names></name>; <name><surname>Bloch</surname><given-names>I.</given-names></name>; <name><surname>Mazerand</surname><given-names>E.</given-names></name>; <name><surname>Menei</surname><given-names>P.</given-names></name>; <name><surname>Montero-Menei</surname><given-names>C.</given-names></name>; <name><surname>Dinomais</surname><given-names>M.</given-names></name>, editor <article-title>Structural information and (hyper)graph matching for MRI piglet brain extraction</article-title>. <source>International Conference on Pattern Recognition Systems</source>; <year>2019</year>
<day>8&#x02013;10</day>
<month>July</month>
<year>2019</year>; Tours, France.</mixed-citation></ref><ref id="pone.0284951.ref008"><label>8</label><mixed-citation publication-type="journal"><name><surname>Akkus</surname><given-names>Z</given-names></name>, <name><surname>Galimzianova</surname><given-names>A</given-names></name>, <name><surname>Hoogi</surname><given-names>A</given-names></name>, <name><surname>Rubin</surname><given-names>DL</given-names></name>, <name><surname>Erickson</surname><given-names>BJ</given-names></name>. <article-title>Deep Learning for Brain MRI Segmentation: State of the Art and Future Directions.</article-title>
<source>J Digit Imaging</source>. <year>2017</year>;<volume>30</volume>(<issue>4</issue>):<fpage>449</fpage>&#x02013;<lpage>59</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1007/s10278-017-9983-4</pub-id>
<?supplied-pmid 28577131?><pub-id pub-id-type="pmid">28577131</pub-id></mixed-citation></ref><ref id="pone.0284951.ref009"><label>9</label><mixed-citation publication-type="journal"><name><surname>Hurtz</surname><given-names>S</given-names></name>, <name><surname>Chow</surname><given-names>N</given-names></name>, <name><surname>Watson</surname><given-names>AE</given-names></name>, <name><surname>Somme</surname><given-names>JH</given-names></name>, <name><surname>Goukasian</surname><given-names>N</given-names></name>, <name><surname>Hwang</surname><given-names>KS</given-names></name>, <etal>et al</etal>. <article-title>Automated and manual hippocampal segmentation techniques: Comparison of results, reproducibility and clinical applicability</article-title>. <source>Neuroimage Clin.</source>
<year>2019</year>;<volume>21</volume>:<fpage>101574</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.nicl.2018.10.012</pub-id>
<?supplied-pmid 30553759?><pub-id pub-id-type="pmid">30553759</pub-id></mixed-citation></ref><ref id="pone.0284951.ref010"><label>10</label><mixed-citation publication-type="journal"><name><surname>Iqbal</surname><given-names>A</given-names></name>, <name><surname>Khan</surname><given-names>R</given-names></name>, <name><surname>Karayannis</surname><given-names>T</given-names></name>. <article-title>Developing a brain atlas through deep learning</article-title>. <source>Nature Machine Intelligence</source>. <year>2019</year>;<volume>1</volume>(<issue>6</issue>):<fpage>277</fpage>&#x02013;<lpage>87</lpage>.</mixed-citation></ref><ref id="pone.0284951.ref011"><label>11</label><mixed-citation publication-type="journal"><name><surname>Wang</surname><given-names>X</given-names></name>, <name><surname>Li</surname><given-names>XH</given-names></name>, <name><surname>Cho</surname><given-names>JW</given-names></name>, <name><surname>Russ</surname><given-names>BE</given-names></name>, <name><surname>Rajamani</surname><given-names>N</given-names></name>, <name><surname>Omelchenko</surname><given-names>A</given-names></name>, <etal>et al</etal>. <article-title>U-net model for brain extraction: Trained on humans for transfer to non-human primates</article-title>. <source>Neuroimage</source>. <year>2021</year>;<volume>235</volume>:<fpage>118001</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.neuroimage.2021.118001</pub-id>
<?supplied-pmid 33789137?><pub-id pub-id-type="pmid">33789137</pub-id></mixed-citation></ref><ref id="pone.0284951.ref012"><label>12</label><mixed-citation publication-type="journal"><name><surname>Coupeau</surname><given-names>P</given-names></name>, <name><surname>Fasquel</surname><given-names>JB</given-names></name>, <name><surname>Mazerand</surname><given-names>E</given-names></name>, <name><surname>Menei</surname><given-names>P</given-names></name>, <name><surname>Montero-Menei</surname><given-names>CN</given-names></name>, <name><surname>Dinomais</surname><given-names>M</given-names></name>. <article-title>Patch-based 3D U-Net and transfer learning for longitudinal piglet brain segmentation on MRI</article-title>. <source>Comput Meth Prog Bio</source>. <year>2022</year>;<fpage>214</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.cmpb.2021.106563</pub-id>
<?supplied-pmid 34890993?><pub-id pub-id-type="pmid">34890993</pub-id></mixed-citation></ref><ref id="pone.0284951.ref013"><label>13</label><mixed-citation publication-type="journal"><name><surname>Rytych</surname><given-names>JL</given-names></name>, <name><surname>Elmore</surname><given-names>MR</given-names></name>, <name><surname>Burton</surname><given-names>MD</given-names></name>, <name><surname>Conrad</surname><given-names>MS</given-names></name>, <name><surname>Donovan</surname><given-names>SM</given-names></name>, <name><surname>Dilger</surname><given-names>RN</given-names></name>, <etal>et al</etal>. <article-title>Early life iron deficiency impairs spatial cognition in neonatal piglets</article-title>. <source>J Nutr</source>. <year>2012</year>;<volume>142</volume>(<issue>11</issue>):<fpage>2050</fpage>&#x02013;<lpage>6</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.3945/jn.112.165522</pub-id>
<?supplied-pmid 23014488?><pub-id pub-id-type="pmid">23014488</pub-id></mixed-citation></ref><ref id="pone.0284951.ref014"><label>14</label><mixed-citation publication-type="journal"><name><surname>Conrad</surname><given-names>MS</given-names></name>, <name><surname>Sutton</surname><given-names>BP</given-names></name>, <name><surname>Dilger</surname><given-names>RN</given-names></name>, <name><surname>Johnson</surname><given-names>RW</given-names></name>. <article-title>An in vivo three-dimensional magnetic resonance imaging-based averaged brain collection of the neonatal piglet (Sus scrofa).</article-title>
<source>PLoS One</source>. <year>2014</year>;<volume>9</volume>(<issue>9</issue>):<fpage>e107650</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1371/journal.pone.0107650</pub-id>
<?supplied-pmid 25254955?><pub-id pub-id-type="pmid">25254955</pub-id></mixed-citation></ref><ref id="pone.0284951.ref015"><label>15</label><mixed-citation publication-type="journal"><name><surname>He</surname><given-names>KM</given-names></name>, <name><surname>Gkioxari</surname><given-names>G</given-names></name>, <name><surname>Dollar</surname><given-names>P</given-names></name>, <name><surname>Girshick</surname><given-names>R</given-names></name>. <article-title>Mask R-CNN.</article-title>
<source>Ieee I Conf Comp Vis</source>. <year>2017</year>:<fpage>2980</fpage>&#x02013;<lpage>8</lpage>.</mixed-citation></ref><ref id="pone.0284951.ref016"><label>16</label><mixed-citation publication-type="book"><name><surname>Lin</surname><given-names>T-Y</given-names></name>, <name><surname>Maire</surname><given-names>M</given-names></name>, <name><surname>Belongie</surname><given-names>S</given-names></name>, <name><surname>Hays</surname><given-names>J</given-names></name>, <name><surname>Perona</surname><given-names>P</given-names></name>, <name><surname>Ramanan</surname><given-names>D</given-names></name>, <etal>et al</etal>., editors. <source>Microsoft COCO: Common Objects in Context2014</source>; <publisher-loc>Cham</publisher-loc>: <publisher-name>Springer International Publishing</publisher-name>.</mixed-citation></ref><ref id="pone.0284951.ref017"><label>17</label><mixed-citation publication-type="journal"><name><surname>Ren</surname><given-names>SQ</given-names></name>, <name><surname>He</surname><given-names>KM</given-names></name>, <name><surname>Girshick</surname><given-names>R</given-names></name>, <name><surname>Sun</surname><given-names>J</given-names></name>. <article-title>Faster R-CNN: Towards Real-Time Object Detection with Region Proposal Networks</article-title>. <source>Ieee T Pattern Anal</source>. <year>2017</year>;<volume>39</volume>(<issue>6</issue>):<fpage>1137</fpage>&#x02013;<lpage>49</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1109/TPAMI.2016.2577031</pub-id>
<?supplied-pmid 27295650?><pub-id pub-id-type="pmid">27295650</pub-id></mixed-citation></ref><ref id="pone.0284951.ref018"><label>18</label><mixed-citation publication-type="journal"><name><surname>Lin</surname><given-names>TY</given-names></name>, <name><surname>Dollar</surname><given-names>P</given-names></name>, <name><surname>Girshick</surname><given-names>R</given-names></name>, <name><surname>He</surname><given-names>KM</given-names></name>, <name><surname>Hariharan</surname><given-names>B</given-names></name>, <name><surname>Belongie</surname><given-names>S</given-names></name>. <article-title>Feature Pyramid Networks for Object Detection</article-title>. <source>Proc Cvpr Ieee</source>. <year>2017</year>:<fpage>936</fpage>&#x02013;<lpage>44</lpage>.</mixed-citation></ref><ref id="pone.0284951.ref019"><label>19</label><mixed-citation publication-type="journal"><name><surname>Vabalas</surname><given-names>A</given-names></name>, <name><surname>Gowen</surname><given-names>E</given-names></name>, <name><surname>Poliakoff</surname><given-names>E</given-names></name>, <name><surname>Casson</surname><given-names>AJ</given-names></name>. <article-title>Machine learning algorithm validation with a limited sample size</article-title>. <source>PLoS One</source>. <year>2019</year>;<volume>14</volume>(<issue>11</issue>):<fpage>e0224365</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1371/journal.pone.0224365</pub-id>
<?supplied-pmid 31697686?><pub-id pub-id-type="pmid">31697686</pub-id></mixed-citation></ref><ref id="pone.0284951.ref020"><label>20</label><mixed-citation publication-type="journal"><name><surname>Taha</surname><given-names>AA</given-names></name>, <name><surname>Hanbury</surname><given-names>A</given-names></name>. <article-title>Metrics for evaluating 3D medical image segmentation: analysis, selection, and tool.</article-title>
<source>BMC Med Imaging</source>. <year>2015</year>;<volume>15</volume>:<fpage>29</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1186/s12880-015-0068-x</pub-id>
<?supplied-pmid 26263899?><pub-id pub-id-type="pmid">26263899</pub-id></mixed-citation></ref><ref id="pone.0284951.ref021"><label>21</label><mixed-citation publication-type="journal"><name><surname>Birenbaum</surname><given-names>A</given-names></name>, <name><surname>Greenspan</surname><given-names>H</given-names></name>. <article-title>Longitudinal Multiple Sclerosis Lesion Segmentation Using Multi-view Convolutional Neural Networks</article-title>. <source>Lect Notes Comput Sc.</source>
<year>2016</year>;<volume>10008</volume>:<fpage>58</fpage>&#x02013;<lpage>67</lpage>.</mixed-citation></ref><ref id="pone.0284951.ref022"><label>22</label><mixed-citation publication-type="journal"><name><surname>Drozdzal</surname><given-names>M</given-names></name>, <name><surname>Chartrand</surname><given-names>G</given-names></name>, <name><surname>Vorontsov</surname><given-names>E</given-names></name>, <name><surname>Shakeri</surname><given-names>M</given-names></name>, <name><surname>Di Jorio</surname><given-names>L</given-names></name>, <name><surname>Tang</surname><given-names>A</given-names></name>, <etal>et al</etal>. <article-title>Learning normalized inputs for iterative estimation in medical image segmentation</article-title>. <source>Med Image Anal</source>. <year>2018</year>;<volume>44</volume>:<fpage>1</fpage>&#x02013;<lpage>13</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.media.2017.11.005</pub-id>
<?supplied-pmid 29169029?><pub-id pub-id-type="pmid">29169029</pub-id></mixed-citation></ref><ref id="pone.0284951.ref023"><label>23</label><mixed-citation publication-type="journal"><name><surname>Ashburner</surname><given-names>J</given-names></name>, <name><surname>Friston</surname><given-names>KJ</given-names></name>. <article-title>Unified segmentation</article-title>. <source>Neuroimage</source>. <year>2005</year>;<volume>26</volume>(<issue>3</issue>):<fpage>839</fpage>&#x02013;<lpage>51</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.neuroimage.2005.02.018</pub-id>
<?supplied-pmid 15955494?><pub-id pub-id-type="pmid">15955494</pub-id></mixed-citation></ref><ref id="pone.0284951.ref024"><label>24</label><mixed-citation publication-type="journal"><name><surname>Hsu</surname><given-names>LM</given-names></name>, <name><surname>Wang</surname><given-names>S</given-names></name>, <name><surname>Ranadive</surname><given-names>P</given-names></name>, <name><surname>Ban</surname><given-names>W</given-names></name>, <name><surname>Chao</surname><given-names>THH</given-names></name>, <name><surname>Song</surname><given-names>S</given-names></name>, <etal>et al</etal>. <article-title>Automatic Skull Stripping of Rat and Mouse Brain MRI Data Using U-Net.</article-title>
<source>Front Neurosci-Switz</source>. <year>2020</year>;<fpage>14</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.3389/fnins.2020.568614</pub-id>
<?supplied-pmid 33117118?><pub-id pub-id-type="pmid">33117118</pub-id></mixed-citation></ref><ref id="pone.0284951.ref025"><label>25</label><mixed-citation publication-type="journal"><name><surname>Oguz</surname><given-names>I</given-names></name>, <name><surname>Zhang</surname><given-names>H</given-names></name>, <name><surname>Rumple</surname><given-names>A</given-names></name>, <name><surname>Sonka</surname><given-names>M</given-names></name>. <article-title>RATS: Rapid Automatic Tissue Segmentation in rodent brain MRI</article-title>. <source>J Neurosci Methods</source>. <year>2014</year>;<volume>221</volume>:<fpage>175</fpage>&#x02013;<lpage>82</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.jneumeth.2013.09.021</pub-id>
<?supplied-pmid 24140478?><pub-id pub-id-type="pmid">24140478</pub-id></mixed-citation></ref><ref id="pone.0284951.ref026"><label>26</label><mixed-citation publication-type="book"><name><surname>Vuola</surname><given-names>AO</given-names></name>, <name><surname>Akram</surname><given-names>SU</given-names></name>, <name><surname>Kannala</surname><given-names>J</given-names></name>, editors. <part-title>Mask-RCNN and U-net ensembled for nuclei segmentation</part-title>. <publisher-name>2019 IEEE 16th international symposium on biomedical imaging (ISBI 2019)</publisher-name>; <year>2019</year>: <publisher-name>IEEE</publisher-name>.</mixed-citation></ref><ref id="pone.0284951.ref027"><label>27</label><mixed-citation publication-type="book"><name><surname>Durkee</surname><given-names>MS</given-names></name>, <name><surname>Abraham</surname><given-names>R</given-names></name>, <name><surname>Ai</surname><given-names>J</given-names></name>, <name><surname>Fuhrman</surname><given-names>JD</given-names></name>, <name><surname>Clark</surname><given-names>MR</given-names></name>, <name><surname>Giger</surname><given-names>ML</given-names></name>, editors. <part-title>Comparing Mask R-CNN and U-Net architectures for robust automatic segmentation of immune cells in immunofluorescence images of Lupus Nephritis biopsies</part-title>. <source>Imaging, Manipulation, and Analysis of Biomolecules, Cells, and Tissues XIX</source>; <year>2021</year>: <publisher-name>SPIE</publisher-name>.</mixed-citation></ref><ref id="pone.0284951.ref028"><label>28</label><mixed-citation publication-type="journal"><name><surname>Pathan</surname><given-names>RK</given-names></name>, <name><surname>Lim</surname><given-names>WL</given-names></name>, <name><surname>Lau</surname><given-names>SL</given-names></name>, <name><surname>Ho</surname><given-names>CC</given-names></name>, <name><surname>Khare</surname><given-names>P</given-names></name>, <name><surname>Koneru</surname><given-names>RB</given-names></name>. <source>Experimental Analysis of U-Net and Mask R-CNN for Segmentation of Synthetic Liquid Spray</source>.</mixed-citation></ref><ref id="pone.0284951.ref029"><label>29</label><mixed-citation publication-type="book"><name><surname>Konopczy&#x00144;ski</surname><given-names>T</given-names></name>, <name><surname>Heiman</surname><given-names>R</given-names></name>, <name><surname>Wo&#x0017a;nicki</surname><given-names>P</given-names></name>, <name><surname>Gniewek</surname><given-names>P</given-names></name>, <name><surname>Duvernoy</surname><given-names>M-C</given-names></name>, <name><surname>Hallatschek</surname><given-names>O</given-names></name>, <etal>et al</etal>., editors. <part-title>Instance segmentation of densely packed cells using a hybrid model of U-net and mask R-CNN</part-title>. <source>Artificial Intelligence and Soft Computing: 19th International Conference, ICAISC 2020, Zakopane, Poland, October 12&#x02013;14, 2020, Proceedings, Part I 19</source>; <year>2020</year>: <collab>Springer</collab>.</mixed-citation></ref><ref id="pone.0284951.ref030"><label>30</label><mixed-citation publication-type="journal"><name><surname>Shu</surname><given-names>JH</given-names></name>, <name><surname>Nian</surname><given-names>FD</given-names></name>, <name><surname>Yu</surname><given-names>MH</given-names></name>, <name><surname>Li</surname><given-names>X</given-names></name>. <article-title>An Improved Mask R-CNN Model for Multiorgan Segmentation</article-title>. <source>Math Probl Eng.</source>
<year>2020</year>;<fpage>2020</fpage>.</mixed-citation></ref><ref id="pone.0284951.ref031"><label>31</label><mixed-citation publication-type="journal"><name><surname>Jollans</surname><given-names>L</given-names></name>, <name><surname>Boyle</surname><given-names>R</given-names></name>, <name><surname>Artiges</surname><given-names>E</given-names></name>, <name><surname>Banaschewski</surname><given-names>T</given-names></name>, <name><surname>Desrivieres</surname><given-names>S</given-names></name>, <name><surname>Grigis</surname><given-names>A</given-names></name>, <etal>et al</etal>. <article-title>Quantifying performance of machine learning methods for neuroimaging data</article-title>. <source>Neuroimage</source>. <year>2019</year>;<volume>199</volume>:<fpage>351</fpage>&#x02013;<lpage>65</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.neuroimage.2019.05.082</pub-id>
<?supplied-pmid 31173905?><pub-id pub-id-type="pmid">31173905</pub-id></mixed-citation></ref><ref id="pone.0284951.ref032"><label>32</label><mixed-citation publication-type="journal"><name><surname>Yang</surname><given-names>L</given-names></name>, <name><surname>Shami</surname><given-names>A</given-names></name>. <article-title>On hyperparameter optimization of machine learning algorithms: Theory and practice</article-title>. <source>Neurocomputing</source>. <year>2020</year>;<volume>415</volume>:<fpage>295</fpage>&#x02013;<lpage>316</lpage>.</mixed-citation></ref><ref id="pone.0284951.ref033"><label>33</label><mixed-citation publication-type="journal"><name><surname>Wu</surname><given-names>J</given-names></name>, <name><surname>Chen</surname><given-names>X-Y</given-names></name>, <name><surname>Zhang</surname><given-names>H</given-names></name>, <name><surname>Xiong</surname><given-names>L-D</given-names></name>, <name><surname>Lei</surname><given-names>H</given-names></name>, <name><surname>Deng</surname><given-names>S-H</given-names></name>. <article-title>Hyperparameter Optimization for Machine Learning Models Based on Bayesian Optimizationb</article-title>. <source>Journal of Electronic Science and Technology</source>. <year>2019</year>;<volume>17</volume>(<issue>1</issue>):<fpage>26</fpage>&#x02013;<lpage>40</lpage>.</mixed-citation></ref><ref id="pone.0284951.ref034"><label>34</label><mixed-citation publication-type="journal"><name><surname>Jeong</surname><given-names>J</given-names></name>, <name><surname>Lei</surname><given-names>Y</given-names></name>, <name><surname>Kahn</surname><given-names>S</given-names></name>, <name><surname>Liu</surname><given-names>T</given-names></name>, <name><surname>Curran</surname><given-names>WJ</given-names></name>, <name><surname>Shu</surname><given-names>HK</given-names></name>, <etal>et al</etal>. <article-title>Brain tumor segmentation using 3D Mask R-CNN for dynamic susceptibility contrast enhanced perfusion imaging</article-title>. <source>Phys Med Biol</source>. <year>2020</year>;<volume>65</volume>(<issue>18</issue>). <comment>doi: </comment><pub-id pub-id-type="doi">10.1088/1361-6560/aba6d4</pub-id>
<?supplied-pmid 32674075?><pub-id pub-id-type="pmid">32674075</pub-id></mixed-citation></ref><ref id="pone.0284951.ref035"><label>35</label><mixed-citation publication-type="journal"><name><surname>Helms</surname><given-names>G</given-names></name>, <name><surname>Draganski</surname><given-names>B</given-names></name>, <name><surname>Frackowiak</surname><given-names>R</given-names></name>, <name><surname>Ashburner</surname><given-names>J</given-names></name>, <name><surname>Weiskopf</surname><given-names>N</given-names></name>. <article-title>Improved segmentation of deep brain grey matter structures using magnetization transfer (MT) parameter maps</article-title>. <source>Neuroimage</source>. <year>2009</year>;<volume>47</volume>(<issue>1</issue>):<fpage>194</fpage>&#x02013;<lpage>8</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1016/j.neuroimage.2009.03.053</pub-id>
<?supplied-pmid 19344771?><pub-id pub-id-type="pmid">19344771</pub-id></mixed-citation></ref><ref id="pone.0284951.ref036"><label>36</label><mixed-citation publication-type="journal"><name><surname>Helms</surname><given-names>G</given-names></name>, <name><surname>Dathe</surname><given-names>H</given-names></name>, <name><surname>Kallenberg</surname><given-names>K</given-names></name>, <name><surname>Dechent</surname><given-names>P</given-names></name>. <article-title>High-resolution maps of magnetization transfer with inherent correction for RF inhomogeneity and T1 relaxation obtained from 3D FLASH MRI</article-title>. <source>Magn Reson Med</source>. <year>2008</year>;<volume>60</volume>(<issue>6</issue>):<fpage>1396</fpage>&#x02013;<lpage>407</lpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1002/mrm.21732</pub-id>
<?supplied-pmid 19025906?><pub-id pub-id-type="pmid">19025906</pub-id></mixed-citation></ref><ref id="pone.0284951.ref037"><label>37</label><mixed-citation publication-type="book"><name><surname>Ronneberger</surname><given-names>O</given-names></name>, <name><surname>Fischer</surname><given-names>P</given-names></name>, <name><surname>Brox</surname><given-names>T</given-names></name>, editors. <source>U-Net: Convolutional Networks for Biomedical Image Segmentation2015</source>; <publisher-loc>Cham</publisher-loc>: <publisher-name>Springer International Publishing</publisher-name>.</mixed-citation></ref><ref id="pone.0284951.ref038"><label>38</label><mixed-citation publication-type="journal"><name><surname>Radlowski</surname><given-names>EC</given-names></name>, <name><surname>Conrad</surname><given-names>MS</given-names></name>, <name><surname>Lezmi</surname><given-names>S</given-names></name>, <name><surname>Dilger</surname><given-names>RN</given-names></name>, <name><surname>Sutton</surname><given-names>B</given-names></name>, <name><surname>Larsen</surname><given-names>R</given-names></name>, <etal>et al</etal>. <article-title>A neonatal piglet model for investigating brain and cognitive development in small for gestational age human infants</article-title>. <source>PLoS One</source>. <year>2014</year>;<volume>9</volume>(<issue>3</issue>):<fpage>e91951</fpage>. <comment>doi: </comment><pub-id pub-id-type="doi">10.1371/journal.pone.0091951</pub-id>
<?supplied-pmid 24637829?><pub-id pub-id-type="pmid">24637829</pub-id></mixed-citation></ref></ref-list></back><sub-article article-type="author-comment" id="pone.0284951.r001" specific-use="rebutted-decision-letter-unavailable"><front-stub><article-id pub-id-type="doi">10.1371/journal.pone.0284951.r001</article-id><title-group><article-title>Author response to previous submission</article-title></title-group><custom-meta-group><custom-meta><meta-name>Submission Version</meta-name><meta-value>0</meta-value></custom-meta></custom-meta-group></front-stub><body><p>
<named-content content-type="author-response-date">2 Dec 2021</named-content>
</p><supplementary-material id="pone.0284951.s001" position="float" content-type="local-data"><label>Attachment</label><caption><p>Submitted filename: <named-content content-type="submitted-filename">response_to_reviewer.docx</named-content></p></caption><media xlink:href="pone.0284951.s001.docx"><caption><p>Click here for additional data file.</p></caption></media></supplementary-material></body></sub-article><sub-article article-type="aggregated-review-documents" id="pone.0284951.r002" specific-use="decision-letter"><front-stub><article-id pub-id-type="doi">10.1371/journal.pone.0284951.r002</article-id><title-group><article-title>Decision Letter 0</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Wang</surname><given-names>Zhishun</given-names></name><role>Academic Editor</role></contrib></contrib-group><permissions><copyright-statement>&#x000a9; 2023 Zhishun Wang</copyright-statement><copyright-year>2023</copyright-year><copyright-holder>Zhishun Wang</copyright-holder><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This is an open access article distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use, distribution, and reproduction in any medium, provided the original author and source are credited.</license-p></license></permissions><related-article ext-link-type="doi" xlink:href="10.1371/journal.pone.0284951" id="rel-obj002" related-article-type="reviewed-article"/><custom-meta-group><custom-meta><meta-name>Submission Version</meta-name><meta-value>0</meta-value></custom-meta></custom-meta-group></front-stub><body><p>
<named-content content-type="letter-date">16 Jul 2022</named-content>
</p><p><!-- <div> -->PONE-D-21-37916<!-- </div> --><!-- <div> -->Automated identification of piglet brain tissue from MRI images using Region-Based Convolutional Neural Networks<!-- </div> --><!-- <div> -->PLOS ONE</p><p>Dear Dr. Larsen,</p><p>Thank you for submitting your manuscript to PLOS ONE. After careful consideration, we feel that it has merit but does not fully meet PLOS ONE&#x02019;s publication criteria as it currently stands. Therefore, we invite you to submit a revised version of the manuscript that addresses the points raised during the review process.</p><p>Please submit your revised manuscript by Aug 20 2022 11:59PM. If you will need more time than this to complete your revisions, please reply to this message or contact the journal office at&#x000a0;<email><EMAIL></email>. When you're ready to submit your revision, log on to <ext-link xlink:href="https://www.editorialmanager.com/pone/" ext-link-type="uri">https://www.editorialmanager.com/pone/</ext-link> and select the 'Submissions Needing Revision' folder to locate your manuscript file.</p><p>Please include the following items when submitting your revised manuscript:<!-- </div> --><list list-type="bullet"><list-item><p>A rebuttal letter that responds to each point raised by the academic editor and reviewer(s). You should upload this letter as a separate file labeled 'Response to Reviewers'.</p></list-item><list-item><p>A marked-up copy of your manuscript that highlights changes made to the original version. You should upload this as a separate file labeled 'Revised Manuscript with Track Changes'.</p></list-item><list-item><p>An unmarked version of your revised paper without tracked changes. You should upload this as a separate file labeled 'Manuscript'.</p></list-item></list></p><p>If you would like to make changes to your financial disclosure, please include your updated statement in your cover letter. Guidelines for resubmitting your figure files are available below the reviewer comments at the end of this letter.</p><p>If applicable, we recommend that you deposit your laboratory protocols in protocols.io to enhance the reproducibility of your results. Protocols.io assigns your protocol its own identifier (DOI) so that it can be cited independently in the future. For instructions see: <ext-link xlink:href="https://journals.plos.org/plosone/s/submission-guidelines#loc-laboratory-protocols" ext-link-type="uri">https://journals.plos.org/plosone/s/submission-guidelines#loc-laboratory-protocols</ext-link>. Additionally, PLOS ONE offers an option for publishing peer-reviewed Lab Protocol articles, which describe protocols hosted on protocols.io. Read more information on sharing protocols at <ext-link xlink:href="https://plos.org/protocols?utm_medium=editorial-email&#x00026;utm_source=authorletters&#x00026;utm_campaign=protocols" ext-link-type="uri">https://plos.org/protocols?utm_medium=editorial-email&#x00026;utm_source=authorletters&#x00026;utm_campaign=protocols</ext-link>.</p><p>We look forward to receiving your revised manuscript.</p><p>Kind regards,</p><p>Zhishun Wang, Ph.D.</p><p>Academic Editor</p><p>PLOS ONE</p><p>Journal Requirements:</p><p>When submitting your revision, we need you to address these additional requirements.</p><p>1. Please ensure that your manuscript meets PLOS ONE's style requirements, including those for file naming. The PLOS ONE style templates can be found at</p><p><ext-link xlink:href="https://journals.plos.org/plosone/s/file?id=wjVg/PLOSOne_formatting_sample_main_body.pdf" ext-link-type="uri">https://journals.plos.org/plosone/s/file?id=wjVg/PLOSOne_formatting_sample_main_body.pdf</ext-link> and</p><p><ext-link xlink:href="https://journals.plos.org/plosone/s/file?id=ba62/PLOSOne_formatting_sample_title_authors_affiliations.pdf" ext-link-type="uri">https://journals.plos.org/plosone/s/file?id=ba62/PLOSOne_formatting_sample_title_authors_affiliations.pdf</ext-link>.</p><p>2. Thank you for stating the following in the Acknowledgments Section of your manuscript:</p><p>&#x0201c;The study was funded by Abbott Nutrition, <ext-link xlink:href="http://www.abbott.com" ext-link-type="uri">www.abbott.com</ext-link>, grant number ******** to A.S. The funder aided with study design, data analysis, decision to publish, and preparation of the manuscript. We thank the staff members of the Biomedical Imaging Center at the Beckman Institute for Advanced Science and Technology for imaging support.&#x000a0; &#x0201c;</p><p>We note that you have provided additional information within the Acknowledgements Section that is not currently declared in your Funding Statement. Please note that funding information should not appear in the Acknowledgments section or other areas of your manuscript. We will only publish funding information present in the Funding Statement section of the online submission form.</p><p>Please remove any funding-related text from the manuscript and let us know how you would like to update your Funding Statement. Currently, your Funding Statement reads as follows:</p><p>&#x000a0;&#x0201c;The study was funded by Abbott Nutrition, <ext-link xlink:href="http://www.abbott.com" ext-link-type="uri">www.abbott.com</ext-link>, grant number ******** to</p><p>A.S. The funder aided with study design, data analysis, decision to publish, and</p><p>preparation of the manuscript.&#x0201d;</p><p>Please include your amended statements within your cover letter; we will change the online submission form on your behalf.</p><p>[Note: HTML markup is below. Please do not edit.]</p><p>Reviewers' comments:</p><p>Reviewer's Responses to Questions</p><p>
<!-- <font color="black"> -->
<bold>Comments to the Author</bold>
</p><p>1. Is the manuscript technically sound, and do the data support the conclusions?</p><p>The manuscript must describe a technically sound piece of scientific research with data that supports the conclusions. Experiments must have been conducted rigorously, with appropriate controls, replication, and sample sizes. The conclusions must be drawn appropriately based on the data presented. <!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;Partly</p><p>**********</p><p><!-- <font color="black"> -->2. Has the statistical analysis been performed appropriately and rigorously? <!-- </font> --></p><p>Reviewer #1:&#x000a0;N/A</p><p>Reviewer #2:&#x000a0;No</p><p>**********</p><p><!-- <font color="black"> -->3. Have the authors made all data underlying the findings in their manuscript fully available?</p><p>The <ext-link xlink:href="http://www.plosone.org/static/policies.action#sharing" ext-link-type="uri">PLOS Data policy</ext-link> requires authors to make all data underlying the findings described in their manuscript fully available without restriction, with rare exception (please refer to the Data Availability Statement in the manuscript PDF file). The data should be provided as part of the manuscript or its supporting information, or deposited to a public repository. For example, in addition to summary statistics, the data points behind means, medians and variance measures should be available. If there are restrictions on publicly sharing data&#x02014;e.g. participant privacy or use of data from a third party&#x02014;those must be specified.<!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;No</p><p>**********</p><p><!-- <font color="black"> -->4. Is the manuscript presented in an intelligible fashion and written in standard English?</p><p>PLOS ONE does not copyedit accepted manuscripts, so the language in submitted articles must be clear, correct, and unambiguous. Any typographical or grammatical errors should be corrected at revision, so please note any specific errors here.<!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;No</p><p>**********</p><p><!-- <font color="black"> -->5. Review Comments to the Author</p><p>Please use the space provided to explain your answers to the questions above. You may also include additional comments for the author, including concerns about dual publication, research ethics, or publication ethics. (Please upload your review as an attachment if it exceeds 20,000 characters)<!-- </font> --></p><p>Reviewer #1:&#x000a0;Overall, the segmentation results appear to be very good, and the cross-fold validation study is rigorous. Splitting the folds over pigs (subject-wise split) avoids data contamination issues between training and testing, which is good.</p><p>However, I do not like how the outlier case of one pig was manually-corrected and then the segmentation values were used to report final Dice scores. Manual-correction is fine, but I feel that this is a biased presentation of results. Instead, I encourage you to report both the Dice overlap scores using the original data (that includes the failure case) as well as the results after manual correction for full transparency of results. Imaging artefacts happen all the time, and I think presenting these full results would be more useful for readers.</p><p>What form of image intensity normalization was performed? Intensity normalization for MR images is often a critical component. It might not be problem here since all images were from the same scanner, but it would be problem if different scanners were used (or the same scanner was used after any upgrades). Intensity normalization could also help to control for intensity outliers (as seen in your failure case). Please elaborate on any normalization methods used.</p><p>Largest connected component filtering was performed as a post-processing step. It would be helpful to report the Dice segmentation values prior to post-processing as well. This would give readers a sense of how well the original segmentation performed compared to how much the post-processing helped.</p><p>The inclusion of Dice overlap as similarity metric is good and gives a measure of overall segmentation performance. But, typically, Dice by itself is not the only metric used for segmentation evaluation. Other metrics that are commonly used are Harsdorff Distance (HD) and Mean Surface Distance. In particular, HD is of interest because this gives a measure of worst-case segmentation performance. Worst case segmentation performance is often of interest to readers. Please consider including HD (or its less extreme variant the 95-th percentile of HD).</p><p>Training and inference used 2D image slices. During inference, all 2D slices of a piglet were combined into a 3D volume. This has the potential to result in mask results that are not smooth at the boundary because each 2D slice is independent of its neighbors (this is where 2.5D or 3D processing would help). Please discuss the spatial consistency of results between neighboring slices within the 3D volume.</p><p>Grammar/Typographical:</p><p>Line 134: &#x0201c;all by the&#x0201d; -&#x0003e; &#x0201c;all but the&#x0201d;</p><p>Reviewer #2:&#x000a0;I have the following concerns about this manuscript:</p><p>1- This manuscript is written by a way different from usual known articles including sectioning and subsectioning, references ..etc.</p><p>2- There is no mathematical or statistical analysis at all.</p><p>3- The methodology used in training, validation, and testing the used model is unclear.</p><p>4- The author should compare their results with related published references performing the segmentation task using the same data set without restriction themselves to the piglet MRI images.</p><p>**********</p><p><!-- <font color="black"> -->6. PLOS authors have the option to publish the peer review history of their article (<ext-link xlink:href="https://journals.plos.org/plosone/s/editorial-and-peer-review-process#loc-peer-review-history" ext-link-type="uri">what does this mean?</ext-link>). If published, this will include your full peer review and any attached files.</p><p>If you choose &#x0201c;no&#x0201d;, your identity will remain anonymous but your review may still be made public.</p><p><bold>Do you want your identity to be public for this peer review?</bold> For information about this choice, including consent withdrawal, please see our <ext-link xlink:href="https://www.plos.org/privacy-policy" ext-link-type="uri">Privacy Policy</ext-link>.<!-- </font> --></p><p>Reviewer #1:&#x000a0;No</p><p>Reviewer #2:&#x000a0;<bold>Yes:&#x000a0;</bold>Ashraf A. M. Khalaf</p><p>**********</p><p>[NOTE: If reviewer comments were submitted as an attachment file, they will be attached to this email and accessible via the submission site. Please log into your account, locate the manuscript record, and check for the action link "View Attachments". If this link does not appear, there are no attachment files.]</p><p>While revising your submission, please upload your figure files to the Preflight Analysis and Conversion Engine (PACE) digital diagnostic tool,&#x000a0;<ext-link xlink:href="https://pacev2.apexcovantage.com/" ext-link-type="uri">https://pacev2.apexcovantage.com/</ext-link>. PACE helps ensure that figures meet PLOS requirements. To use PACE, you must first register as a user. Registration is free. Then, login and navigate to the UPLOAD tab, where you will find detailed instructions on how to use the tool. If you encounter any issues or have any questions when using PACE, please email PLOS at&#x000a0;<email><EMAIL></email>. Please note that Supporting Information files do not need this step.</p></body></sub-article><sub-article article-type="author-comment" id="pone.0284951.r003"><front-stub><article-id pub-id-type="doi">10.1371/journal.pone.0284951.r003</article-id><title-group><article-title>Author response to Decision Letter 0</article-title></title-group><related-article ext-link-type="doi" xlink:href="10.1371/journal.pone.0284951" id="rel-obj003" related-article-type="editor-report"/><custom-meta-group><custom-meta><meta-name>Submission Version</meta-name><meta-value>1</meta-value></custom-meta></custom-meta-group></front-stub><body><p>
<named-content content-type="author-response-date">12 Dec 2022</named-content>
</p><p>Reviewer #1: Overall, the segmentation results appear to be very good, and the cross-fold validation study is rigorous. Splitting the folds over pigs (subject-wise split) avoids data contamination issues between training and testing, which is good.</p><p>However, I do not like how the outlier case of one pig was manually-corrected and then the segmentation values were used to report final Dice scores. Manual-correction is fine, but I feel that this is a biased presentation of results. Instead, I encourage you to report both the Dice overlap scores using the original data (that includes the failure case) as well as the results after manual correction for full transparency of results. Imaging artefacts happen all the time, and I think presenting these full results would be more useful for readers.</p><p>Response: We thank the reviewer for their helpful review. We agree with the reviewer and we have revised the manuscript to report Dices scores before and after this manual correction.</p><p>What form of image intensity normalization was performed? Intensity normalization for MR images is often a critical component. It might not be problem here since all images were from the same scanner, but it would be problem if different scanners were used (or the same scanner was used after any upgrades). Intensity normalization could also help to control for intensity outliers (as seen in your failure case). Please elaborate on any normalization methods used.</p><p>Response: We agree with the reviewer on the importance of intensity. We have revised the manuscript to state that we did not perform normalization, and to state that might have prevented the segmentation error mentioned by the reviewer, and that it would likely be important for use with data from other scanning conditions.</p><p>Largest connected component filtering was performed as a post-processing step. It would be helpful to report the Dice segmentation values prior to post-processing as well. This would give readers a sense of how well the original segmentation performed compared to how much the post-processing helped.</p><p>Response: We have updated the manuscript to include this information, and to quantify the improvements in Dice coefficients due to largest connected component filtering</p><p>The inclusion of Dice overlap as similarity metric is good and gives a measure of overall segmentation performance. But, typically, Dice by itself is not the only metric used for segmentation evaluation. Other metrics that are commonly used are Harsdorff Distance (HD) and Mean Surface Distance. In particular, HD is of interest because this gives a measure of worst-case segmentation performance. Worst case segmentation performance is often of interest to readers. </p><p>Response: We have updated the manuscript to report Hausdorff Distance measures. </p><p>Training and inference used 2D image slices. During inference, all 2D slices of a piglet were combined into a 3D volume. This has the potential to result in mask results that are not smooth at the boundary because each 2D slice is independent of its neighbors (this is where 2.5D or 3D processing would help). Please discuss the spatial consistency of results between neighboring slices within the 3D volume.</p><p>Response: We have updated the manuscript with a discussion of the potential advantages of 3D processing for improvement of consistency between slices.</p><p>Grammar/Typographical:</p><p>Line 134: &#x0201c;all by the&#x0201d; -&#x0003e; &#x0201c;all but the&#x0201d;</p><p>Response: Thank you for pointing this out; it has been corrected.</p><p>Reviewer #2: I have the following concerns about this manuscript:</p><p>1- This manuscript is written by a way different from usual known articles including sectioning and subsectioning, references ..etc.</p><p>Response: We thank the reviewer for their review and comments. We believe that the sectioning is consistent with the usual pattern, including Introduction, Methods, Results, and Discussion. The Methods section is sub-sectioned into Animals and Care Practices, MRI Acquisition, Manual Brain Extraction, Automated Brain Extraction, and Validation.</p><p>2- There is no mathematical or statistical analysis at all.</p><p>Response: We have updated the manuscript to include Hausdorff Distance values, and some descriptive statistics of them. </p><p>3- The methodology used in training, validation, and testing the used model is unclear.</p><p>Response: We tested the model using nested cross-validation. We do not have a separate validation set because we did not perform hyperparameter tuning. The method of nested cross-validation and the potential usefulness of hyperparameter tuning is described in the paper. </p><p>4- The author should compare their results with related published references performing the segmentation task using the same data set without restriction themselves to the piglet MRI images.</p><p>Response: We agree that the inclusion of more data could strengthen the manuscript; however, we believe that the inclusion of non-piglet MRI images is beyond the scope of the manuscript.</p><supplementary-material id="pone.0284951.s002" position="float" content-type="local-data"><label>Attachment</label><caption><p>Submitted filename: <named-content content-type="submitted-filename">reviewer_comments_responses.docx</named-content></p></caption><media xlink:href="pone.0284951.s002.docx"><caption><p>Click here for additional data file.</p></caption></media></supplementary-material></body></sub-article><sub-article article-type="aggregated-review-documents" id="pone.0284951.r004" specific-use="decision-letter"><front-stub><article-id pub-id-type="doi">10.1371/journal.pone.0284951.r004</article-id><title-group><article-title>Decision Letter 1</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Wang</surname><given-names>Zhishun</given-names></name><role>Academic Editor</role></contrib></contrib-group><permissions><copyright-statement>&#x000a9; 2023 Zhishun Wang</copyright-statement><copyright-year>2023</copyright-year><copyright-holder>Zhishun Wang</copyright-holder><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This is an open access article distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use, distribution, and reproduction in any medium, provided the original author and source are credited.</license-p></license></permissions><related-article ext-link-type="doi" xlink:href="10.1371/journal.pone.0284951" id="rel-obj004" related-article-type="reviewed-article"/><custom-meta-group><custom-meta><meta-name>Submission Version</meta-name><meta-value>1</meta-value></custom-meta></custom-meta-group></front-stub><body><p>
<named-content content-type="letter-date">26 Jan 2023</named-content>
</p><p><!-- <div> -->PONE-D-21-37916R1<!-- </div> --><!-- <div> -->Automated identification of piglet brain tissue from MRI images using Region-based Convolutional Neural Networks<!-- </div> --><!-- <div> -->PLOS ONE</p><p>Dear Dr. Larsen,</p><p>Thank you for submitting your manuscript to PLOS ONE. After careful consideration, we feel that it has merit but does not fully meet PLOS ONE&#x02019;s publication criteria as it currently stands. Therefore, we invite you to submit a revised version of the manuscript that addresses the points raised during the review process.</p><p>Please submit your revised manuscript by Mar 12 2023 11:59PM. If you will need more time than this to complete your revisions, please reply to this message or contact the journal office at&#x000a0;<email><EMAIL></email>. When you're ready to submit your revision, log on to <ext-link xlink:href="https://www.editorialmanager.com/pone/" ext-link-type="uri">https://www.editorialmanager.com/pone/</ext-link> and select the 'Submissions Needing Revision' folder to locate your manuscript file.</p><p>Please include the following items when submitting your revised manuscript:<!-- </div> --><list list-type="bullet"><list-item><p>A rebuttal letter that responds to each point raised by the academic editor and reviewer(s). You should upload this letter as a separate file labeled 'Response to Reviewers'.</p></list-item><list-item><p>A marked-up copy of your manuscript that highlights changes made to the original version. You should upload this as a separate file labeled 'Revised Manuscript with Track Changes'.</p></list-item><list-item><p>An unmarked version of your revised paper without tracked changes. You should upload this as a separate file labeled 'Manuscript'.</p></list-item></list><!-- <div> -->If you would like to make changes to your financial disclosure, please include your updated statement in your cover letter. Guidelines for resubmitting your figure files are available below the reviewer comments at the end of this letter.</p><p>If applicable, we recommend that you deposit your laboratory protocols in protocols.io to enhance the reproducibility of your results. Protocols.io assigns your protocol its own identifier (DOI) so that it can be cited independently in the future. For instructions see: <ext-link xlink:href="https://journals.plos.org/plosone/s/submission-guidelines#loc-laboratory-protocols" ext-link-type="uri">https://journals.plos.org/plosone/s/submission-guidelines#loc-laboratory-protocols</ext-link>. Additionally, PLOS ONE offers an option for publishing peer-reviewed Lab Protocol articles, which describe protocols hosted on protocols.io. Read more information on sharing protocols at <ext-link xlink:href="https://plos.org/protocols?utm_medium=editorial-email&#x00026;utm_source=authorletters&#x00026;utm_campaign=protocols" ext-link-type="uri">https://plos.org/protocols?utm_medium=editorial-email&#x00026;utm_source=authorletters&#x00026;utm_campaign=protocols</ext-link>.</p><p>We look forward to receiving your revised manuscript.</p><p>Kind regards,</p><p>Zhishun Wang, Ph.D.</p><p>Academic Editor</p><p>PLOS ONE</p><p>Journal Requirements:</p><p>Please review your reference list to ensure that it is complete and correct. If you have cited papers that have been retracted, please include the rationale for doing so in the manuscript text, or remove these references and replace them with relevant current references. Any changes to the reference list should be mentioned in the rebuttal letter that accompanies your revised manuscript. If you need to cite a retracted article, indicate the article&#x02019;s retracted status in the References list and also include a citation and full reference for the retraction notice.</p><p>[Note: HTML markup is below. Please do not edit.]</p><p>Reviewers' comments:</p><p>Reviewer's Responses to Questions</p><p>
<!-- <font color="black"> -->
<bold>Comments to the Author</bold>
</p><p>1. If the authors have adequately addressed your comments raised in a previous round of review and you feel that this manuscript is now acceptable for publication, you may indicate that here to bypass the &#x0201c;Comments to the Author&#x0201d; section, enter your conflict of interest statement in the &#x0201c;Confidential to Editor&#x0201d; section, and submit your "Accept" recommendation.<!-- </font> --></p><p>Reviewer #1:&#x000a0;All comments have been addressed</p><p>Reviewer #2:&#x000a0;(No Response)</p><p>**********</p><p><!-- <font color="black"> -->2. Is the manuscript technically sound, and do the data support the conclusions?</p><p>The manuscript must describe a technically sound piece of scientific research with data that supports the conclusions. Experiments must have been conducted rigorously, with appropriate controls, replication, and sample sizes. The conclusions must be drawn appropriately based on the data presented. <!-- </font> --></p><p>Reviewer #1:&#x000a0;Partly</p><p>Reviewer #2:&#x000a0;Partly</p><p>**********</p><p><!-- <font color="black"> -->3. Has the statistical analysis been performed appropriately and rigorously? <!-- </font> --></p><p>Reviewer #1:&#x000a0;N/A</p><p>Reviewer #2:&#x000a0;No</p><p>**********</p><p><!-- <font color="black"> -->4. Have the authors made all data underlying the findings in their manuscript fully available?</p><p>The <ext-link xlink:href="http://www.plosone.org/static/policies.action#sharing" ext-link-type="uri">PLOS Data policy</ext-link> requires authors to make all data underlying the findings described in their manuscript fully available without restriction, with rare exception (please refer to the Data Availability Statement in the manuscript PDF file). The data should be provided as part of the manuscript or its supporting information, or deposited to a public repository. For example, in addition to summary statistics, the data points behind means, medians and variance measures should be available. If there are restrictions on publicly sharing data&#x02014;e.g. participant privacy or use of data from a third party&#x02014;those must be specified.<!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;Yes</p><p>**********</p><p><!-- <font color="black"> -->5. Is the manuscript presented in an intelligible fashion and written in standard English?</p><p>PLOS ONE does not copyedit accepted manuscripts, so the language in submitted articles must be clear, correct, and unambiguous. Any typographical or grammatical errors should be corrected at revision, so please note any specific errors here.<!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;No</p><p>**********</p><p><!-- <font color="black"> -->6. Review Comments to the Author</p><p>Please use the space provided to explain your answers to the questions above. You may also include additional comments for the author, including concerns about dual publication, research ethics, or publication ethics. (Please upload your review as an attachment if it exceeds 20,000 characters)<!-- </font> --></p><p>Reviewer #1:&#x000a0;The authors have addressed my comments.</p><p>The main limitation of the paper&#x02019;s evaluation is that the segmentation approach is not compared to alternative segmentation methods as a benchmark. For example, comparison to the 3D U-net approach in [12] using the data from this paper would be ideal. The method in [12] is another segmentation approach applied to very similar data, piglet brain MRI. The proposed approach to use Mask R-CNN would be rigorously justified with a direct head-to-head comparison with the 3D U-net using your dataset.</p><p>Absent this numerical comparison, additional discussion comparing to the U-net approach to the proposed approach would be helpful in the Discussion section. The performance of [12] is briefly mentioned in the Discussion (line 223), but a more thorough comparison to the proposed method (both strengths and weaknesses) may be warranted since it is a such a similar application area. However, this discussion must note that a true comparison cannot be made because the two approaches used different datasets.</p><p>Reviewer #2:&#x000a0;It is the second time to review this manuscript, and almost all my concerns didn't addressed:</p><p>1- This manuscript is written by a way different from usual known articles including</p><p>sectioning and subsectioning, references ..etc.</p><p>Response: We thank the reviewer for their review and comments. We believe that the</p><p>sectioning is consistent with the usual pattern, including Introduction, Methods,</p><p>Results, and Discussion. The Methods section is sub-sectioned into Animals and Care</p><p>Practices, MRI Acquisition, Manual Brain Extraction, Automated Brain Extraction, and</p><p>Validation.</p><p>Reviewer again: Where is the "Conclusion" section?</p><p>Please, see one of the papers published y PloS One:</p><p>Zhai Y, Davenport B, Schuetz K, Pappu HR (2022) An on-site adaptable test for rapid and sensitive detection of Potato mop-top virus, a soilborne virus of potato (Solanum tuberosum). PLoS ONE 17(8): e0270918.</p><p>
<ext-link xlink:href="https://doi.org/10.1371/journal.pone.0270918" ext-link-type="uri">https://doi.org/10.1371/journal.pone.0270918</ext-link>
</p><p>---------------------------------</p><p>2- There is no mathematical or statistical analysis at all.</p><p>Response: We have updated the manuscript to include Hausdorff Distance values, and</p><p>some descriptive statistics of them.</p><p>Reviewer again: Not addressed</p><p>The authors can say that they didn't have any mathematical analysis since they are doing empirical research, then the editorial staff take their decision.</p><p>3- The methodology used in training, validation, and testing used in the model is unclear.</p><p>Response: We tested the model using nested cross-validation. We do not have a</p><p>separate validation set because we did not perform hyperparameter tuning. The</p><p>method of nested cross-validation and the potential usefulness of hyperparameter</p><p>tuning is described in the paper.</p><p>Reviewer again: point Addressed</p><p>4- The authors should compare their results with related published references</p><p>performing the segmentation task using the same data set without restriction</p><p>themselves to the piglet MRI images.</p><p>Reviewer again: Not addressed</p><p>My opinion is: The comparison with the state-of-the-art is necessary or at least the authors should describe why their approach gives sufficient results that will e an added value to this research area without evidences!. Especially the tool used in this research (Mask R-CNN) is not new!</p><p>-------------------</p><p>New sample comments:</p><p>A- Line 17, the first sentence in the "Abstract" : Magnetic Resonance Imaging.... &#x0003e;&#x0003e;&#x0003e; Magnetic Resonance Imaging (MRI)....</p><p>B- How can we evaluate the automated technique visually?</p><p>Line 158 : "Visual inspection of the brain extractions reveals good accuracy of automatic brain extractions</p><p>(Fig 3)....</p><p>**********</p><p><!-- <font color="black"> -->7. PLOS authors have the option to publish the peer review history of their article (<ext-link xlink:href="https://journals.plos.org/plosone/s/editorial-and-peer-review-process#loc-peer-review-history" ext-link-type="uri">what does this mean?</ext-link>). If published, this will include your full peer review and any attached files.</p><p>If you choose &#x0201c;no&#x0201d;, your identity will remain anonymous but your review may still be made public.</p><p><bold>Do you want your identity to be public for this peer review?</bold> For information about this choice, including consent withdrawal, please see our <ext-link xlink:href="https://www.plos.org/privacy-policy" ext-link-type="uri">Privacy Policy</ext-link>.<!-- </font> --></p><p>Reviewer #1:&#x000a0;No</p><p>Reviewer #2:&#x000a0;No</p><p>**********</p><p>[NOTE: If reviewer comments were submitted as an attachment file, they will be attached to this email and accessible via the submission site. Please log into your account, locate the manuscript record, and check for the action link "View Attachments". If this link does not appear, there are no attachment files.]</p><p>While revising your submission, please upload your figure files to the Preflight Analysis and Conversion Engine (PACE) digital diagnostic tool,&#x000a0;<ext-link xlink:href="https://pacev2.apexcovantage.com/" ext-link-type="uri">https://pacev2.apexcovantage.com/</ext-link>. PACE helps ensure that figures meet PLOS requirements. To use PACE, you must first register as a user. Registration is free. Then, login and navigate to the UPLOAD tab, where you will find detailed instructions on how to use the tool. If you encounter any issues or have any questions when using PACE, please email PLOS at&#x000a0;<email><EMAIL></email>. Please note that Supporting Information files do not need this step.</p></body></sub-article><sub-article article-type="author-comment" id="pone.0284951.r005"><front-stub><article-id pub-id-type="doi">10.1371/journal.pone.0284951.r005</article-id><title-group><article-title>Author response to Decision Letter 1</article-title></title-group><related-article ext-link-type="doi" xlink:href="10.1371/journal.pone.0284951" id="rel-obj005" related-article-type="editor-report"/><custom-meta-group><custom-meta><meta-name>Submission Version</meta-name><meta-value>2</meta-value></custom-meta></custom-meta-group></front-stub><body><p>
<named-content content-type="author-response-date">21 Mar 2023</named-content>
</p><p>We have written our responses to the reviewers in the attached file, "Response to Reviewers".</p><supplementary-material id="pone.0284951.s003" position="float" content-type="local-data"><label>Attachment</label><caption><p>Submitted filename: <named-content content-type="submitted-filename">Response to Reviewers.docx</named-content></p></caption><media xlink:href="pone.0284951.s003.docx"><caption><p>Click here for additional data file.</p></caption></media></supplementary-material></body></sub-article><sub-article article-type="aggregated-review-documents" id="pone.0284951.r006" specific-use="decision-letter"><front-stub><article-id pub-id-type="doi">10.1371/journal.pone.0284951.r006</article-id><title-group><article-title>Decision Letter 2</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Wang</surname><given-names>Zhishun</given-names></name><role>Academic Editor</role></contrib></contrib-group><permissions><copyright-statement>&#x000a9; 2023 Zhishun Wang</copyright-statement><copyright-year>2023</copyright-year><copyright-holder>Zhishun Wang</copyright-holder><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This is an open access article distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use, distribution, and reproduction in any medium, provided the original author and source are credited.</license-p></license></permissions><related-article ext-link-type="doi" xlink:href="10.1371/journal.pone.0284951" id="rel-obj006" related-article-type="reviewed-article"/><custom-meta-group><custom-meta><meta-name>Submission Version</meta-name><meta-value>2</meta-value></custom-meta></custom-meta-group></front-stub><body><p>
<named-content content-type="letter-date">13 Apr 2023</named-content>
</p><p>Automated identification of piglet brain tissue from MRI images using Region-based Convolutional Neural Networks</p><p>PONE-D-21-37916R2</p><p>Dear Dr. Larsen,</p><p>We&#x02019;re pleased to inform you that your manuscript has been judged scientifically suitable for publication and will be formally accepted for publication once it meets all outstanding technical requirements.</p><p>Within one week, you&#x02019;ll receive an e-mail detailing the required amendments. When these have been addressed, you&#x02019;ll receive a formal acceptance letter and your manuscript will be scheduled for publication.</p><p>An invoice for payment will follow shortly after the formal acceptance. To ensure an efficient process, please log into Editorial Manager at <ext-link xlink:href="http://www.editorialmanager.com/pone/" ext-link-type="uri">http://www.editorialmanager.com/pone/</ext-link>, click the 'Update My Information' link at the top of the page, and double check that your user information is up-to-date. If you have any billing related questions, please contact our Author Billing department directly at <email><EMAIL></email>.</p><p>If your institution or institutions have a press office, please notify them about your upcoming paper to help maximize its impact. If they&#x02019;ll be preparing press materials, please inform our press team as soon as possible -- no later than 48 hours after receiving the formal acceptance. Your manuscript will remain under strict press embargo until 2 pm Eastern Time on the date of publication. For more information, please contact <email><EMAIL></email>.</p><p>Kind regards,</p><p>Zhishun Wang, Ph.D.</p><p>Academic Editor</p><p>PLOS ONE</p><p>Additional Editor Comments (optional):</p><p>Reviewers' comments:</p><p>Reviewer's Responses to Questions</p><p>
<!-- <font color="black"> -->
<bold>Comments to the Author</bold>
</p><p>1. If the authors have adequately addressed your comments raised in a previous round of review and you feel that this manuscript is now acceptable for publication, you may indicate that here to bypass the &#x0201c;Comments to the Author&#x0201d; section, enter your conflict of interest statement in the &#x0201c;Confidential to Editor&#x0201d; section, and submit your "Accept" recommendation.<!-- </font> --></p><p>Reviewer #1:&#x000a0;All comments have been addressed</p><p>Reviewer #2:&#x000a0;All comments have been addressed</p><p>**********</p><p><!-- <font color="black"> -->2. Is the manuscript technically sound, and do the data support the conclusions?</p><p>The manuscript must describe a technically sound piece of scientific research with data that supports the conclusions. Experiments must have been conducted rigorously, with appropriate controls, replication, and sample sizes. The conclusions must be drawn appropriately based on the data presented. <!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;Partly</p><p>**********</p><p><!-- <font color="black"> -->3. Has the statistical analysis been performed appropriately and rigorously? <!-- </font> --></p><p>Reviewer #1:&#x000a0;N/A</p><p>Reviewer #2:&#x000a0;No</p><p>**********</p><p><!-- <font color="black"> -->4. Have the authors made all data underlying the findings in their manuscript fully available?</p><p>The <ext-link xlink:href="http://www.plosone.org/static/policies.action#sharing" ext-link-type="uri">PLOS Data policy</ext-link> requires authors to make all data underlying the findings described in their manuscript fully available without restriction, with rare exception (please refer to the Data Availability Statement in the manuscript PDF file). The data should be provided as part of the manuscript or its supporting information, or deposited to a public repository. For example, in addition to summary statistics, the data points behind means, medians and variance measures should be available. If there are restrictions on publicly sharing data&#x02014;e.g. participant privacy or use of data from a third party&#x02014;those must be specified.<!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;Yes</p><p>**********</p><p><!-- <font color="black"> -->5. Is the manuscript presented in an intelligible fashion and written in standard English?</p><p>PLOS ONE does not copyedit accepted manuscripts, so the language in submitted articles must be clear, correct, and unambiguous. Any typographical or grammatical errors should be corrected at revision, so please note any specific errors here.<!-- </font> --></p><p>Reviewer #1:&#x000a0;Yes</p><p>Reviewer #2:&#x000a0;Yes</p><p>**********</p><p><!-- <font color="black"> -->6. Review Comments to the Author</p><p>Please use the space provided to explain your answers to the questions above. You may also include additional comments for the author, including concerns about dual publication, research ethics, or publication ethics. (Please upload your review as an attachment if it exceeds 20,000 characters)<!-- </font> --></p><p>Reviewer #1:&#x000a0;While the authors have addressed my comments for the most part, I am a little disappointed that the effort was not made to train another network for comparison. Training and testing another network for benchmarking is standard practice in image analysis and it does not require tremendous resources in the case of a U-net. The discussion comparing to the alternative U-net is ok, but it is a lesser replacement for head-to-head comparison.</p><p>Reviewer #2:&#x000a0;Almost all my previous comments in the previous review round have been addressed in this revised version.</p><p>**********</p><p><!-- <font color="black"> -->7. PLOS authors have the option to publish the peer review history of their article (<ext-link xlink:href="https://journals.plos.org/plosone/s/editorial-and-peer-review-process#loc-peer-review-history" ext-link-type="uri">what does this mean?</ext-link>). If published, this will include your full peer review and any attached files.</p><p>If you choose &#x0201c;no&#x0201d;, your identity will remain anonymous but your review may still be made public.</p><p><bold>Do you want your identity to be public for this peer review?</bold> For information about this choice, including consent withdrawal, please see our <ext-link xlink:href="https://www.plos.org/privacy-policy" ext-link-type="uri">Privacy Policy</ext-link>.<!-- </font> --></p><p>Reviewer #1:&#x000a0;No</p><p>Reviewer #2:&#x000a0;<bold>Yes:&#x000a0;</bold>Ashraf A. M. Khalaf</p><p>**********</p></body></sub-article><sub-article article-type="editor-report" id="pone.0284951.r007" specific-use="acceptance-letter"><front-stub><article-id pub-id-type="doi">10.1371/journal.pone.0284951.r007</article-id><title-group><article-title>Acceptance letter</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Wang</surname><given-names>Zhishun</given-names></name><role>Academic Editor</role></contrib></contrib-group><permissions><copyright-statement>&#x000a9; 2023 Zhishun Wang</copyright-statement><copyright-year>2023</copyright-year><copyright-holder>Zhishun Wang</copyright-holder><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This is an open access article distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use, distribution, and reproduction in any medium, provided the original author and source are credited.</license-p></license></permissions><related-article ext-link-type="doi" xlink:href="10.1371/journal.pone.0284951" id="rel-obj007" related-article-type="reviewed-article"/></front-stub><body><p>
<named-content content-type="letter-date">19 Apr 2023</named-content>
</p><p>PONE-D-21-37916R2 </p><p>Automated identification of piglet brain tissue from MRI images using Region-based Convolutional Neural Networks </p><p>Dear Dr. Larsen:</p><p>I'm pleased to inform you that your manuscript has been deemed suitable for publication in PLOS ONE. Congratulations! Your manuscript is now with our production department. </p><p>If your institution or institutions have a press office, please let them know about your upcoming paper now to help maximize its impact. If they'll be preparing press materials, please inform our press team within the next 48 hours. Your manuscript will remain under strict press embargo until 2 pm Eastern Time on the date of publication. For more information please contact <email><EMAIL></email>.</p><p>If we can help with anything else, please email us at <email><EMAIL></email>. </p><p>Thank you for submitting your work to PLOS ONE and supporting open access. </p><p>Kind regards, </p><p>PLOS ONE Editorial Office Staff</p><p>on behalf of</p><p>Dr. Zhishun Wang </p><p>Academic Editor</p><p>PLOS ONE</p></body></sub-article></article>