<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.3 20210610//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1-3.dtd?><?SourceDTD.Version 1.3?><?ConverterInfo.XSLTName jats2jats3.xsl?><?ConverterInfo.Version 1?><?properties open_access?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">Sensors (Basel)</journal-id><journal-id journal-id-type="iso-abbrev">Sensors (Basel)</journal-id><journal-id journal-id-type="publisher-id">sensors</journal-id><journal-title-group><journal-title>Sensors (Basel, Switzerland)</journal-title></journal-title-group><issn pub-type="epub">1424-8220</issn><publisher><publisher-name>MDPI</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">10490483</article-id><article-id pub-id-type="doi">10.3390/s23177333</article-id><article-id pub-id-type="publisher-id">sensors-23-07333</article-id><article-categories><subj-group subj-group-type="heading"><subject>Article</subject></subj-group></article-categories><title-group><article-title>Exploring LoRaWAN Traffic: In-Depth Analysis of IoT Network Communications</article-title></title-group><contrib-group><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0001-7693-9901</contrib-id><name><surname>Povalac</surname><given-names>Ales</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Conceptualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Methodology" vocab-term-identifier="https://credit.niso.org/contributor-roles/methodology/">Methodology</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Software" vocab-term-identifier="https://credit.niso.org/contributor-roles/software/">Software</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Validation" vocab-term-identifier="https://credit.niso.org/contributor-roles/validation/">Validation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Formal analysis" vocab-term-identifier="https://credit.niso.org/contributor-roles/formal-analysis/">Formal analysis</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Investigation" vocab-term-identifier="https://credit.niso.org/contributor-roles/investigation/">Investigation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Data curation" vocab-term-identifier="https://credit.niso.org/contributor-roles/data-curation/">Data curation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; original draft" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-original-draft/">Writing &#x02013; original draft</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Visualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/visualization/">Visualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Project administration" vocab-term-identifier="https://credit.niso.org/contributor-roles/project-administration/">Project administration</role><xref rid="af1-sensors-23-07333" ref-type="aff">1</xref><xref rid="c1-sensors-23-07333" ref-type="corresp">*</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0002-6255-5365</contrib-id><name><surname>Kral</surname><given-names>Jan</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Conceptualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Methodology" vocab-term-identifier="https://credit.niso.org/contributor-roles/methodology/">Methodology</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Resources" vocab-term-identifier="https://credit.niso.org/contributor-roles/resources/">Resources</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Supervision" vocab-term-identifier="https://credit.niso.org/contributor-roles/supervision/">Supervision</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Project administration" vocab-term-identifier="https://credit.niso.org/contributor-roles/project-administration/">Project administration</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Funding acquisition" vocab-term-identifier="https://credit.niso.org/contributor-roles/funding-acquisition/">Funding acquisition</role><xref rid="af1-sensors-23-07333" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0002-9218-5510</contrib-id><name><surname>Arthaber</surname><given-names>Holger</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Resources" vocab-term-identifier="https://credit.niso.org/contributor-roles/resources/">Resources</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Supervision" vocab-term-identifier="https://credit.niso.org/contributor-roles/supervision/">Supervision</role><xref rid="af2-sensors-23-07333" ref-type="aff">2</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0003-4187-4890</contrib-id><name><surname>Kolar</surname><given-names>Ondrej</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Software" vocab-term-identifier="https://credit.niso.org/contributor-roles/software/">Software</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Validation" vocab-term-identifier="https://credit.niso.org/contributor-roles/validation/">Validation</role><xref rid="af1-sensors-23-07333" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0002-1827-4873</contrib-id><name><surname>Novak</surname><given-names>Marek</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Validation" vocab-term-identifier="https://credit.niso.org/contributor-roles/validation/">Validation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Resources" vocab-term-identifier="https://credit.niso.org/contributor-roles/resources/">Resources</role><xref rid="af1-sensors-23-07333" ref-type="aff">1</xref></contrib></contrib-group><contrib-group><contrib contrib-type="editor"><name><surname>Ferrari</surname><given-names>Gianluigi</given-names></name><role>Academic Editor</role></contrib></contrib-group><aff id="af1-sensors-23-07333"><label>1</label>Faculty of Electrical Engineering and Communication, Brno University of Technology, Technicka 12, 61600 Brno, Czech Republic; <email><EMAIL></email> (J.K.); <email><EMAIL></email> (O.K.); <email><EMAIL></email> (M.N.)</aff><aff id="af2-sensors-23-07333"><label>2</label>Institute of Electrodynamics, Microwave and Circuit Engineering, TU Wien, Gusshausstrasse 25/354, 1040 Vienna, Austria; <email><EMAIL></email></aff><author-notes><corresp id="c1-sensors-23-07333"><label>*</label>Correspondence: <email><EMAIL></email></corresp></author-notes><pub-date pub-type="epub"><day>22</day><month>8</month><year>2023</year></pub-date><pub-date pub-type="collection"><month>9</month><year>2023</year></pub-date><volume>23</volume><issue>17</issue><elocation-id>7333</elocation-id><history><date date-type="received"><day>29</day><month>6</month><year>2023</year></date><date date-type="rev-recd"><day>21</day><month>7</month><year>2023</year></date><date date-type="accepted"><day>17</day><month>8</month><year>2023</year></date></history><permissions><copyright-statement>&#x000a9; 2023 by the authors.</copyright-statement><copyright-year>2023</copyright-year><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (<ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">https://creativecommons.org/licenses/by/4.0/</ext-link>).</license-p></license></permissions><abstract><p>In the past decade, Long-Range Wire-Area Network (LoRaWAN) has emerged as one of the most widely adopted Low Power Wide Area Network (LPWAN) standards. Significant efforts have been devoted to optimizing the operation of this network. However, research in this domain heavily relies on simulations and demands high-quality real-world traffic data. To address this need, we monitored and analyzed LoRaWAN traffic in four European cities, making the obtained data and post-processing scripts publicly available. For monitoring purposes, we developed an open-source sniffer capable of capturing all LoRaWAN communication within the EU868 band. Our analysis discovered significant issues in current LoRaWAN deployments, including violations of fundamental security principles, such as the use of default and exposed encryption keys, potential breaches of spectrum regulations including duty cycle violations, SyncWord issues, and misaligned Class-B beacons. This misalignment can render Class-B unusable, as the beacons cannot be validated. Furthermore, we enhanced Wireshark&#x02019;s LoRaWAN protocol dissector to accurately decode recorded traffic. Additionally, we proposed the passive reception of Class-B beacons as an alternative timebase source for devices operating within LoRaWAN coverage under the assumption that the issue of misaligned beacons can be addressed or mitigated in the future. The identified issues and the published dataset can serve as valuable resources for researchers simulating real-world traffic and for the LoRaWAN Alliance to enhance the standard to facilitate more reliable Class-B communication.</p></abstract><kwd-group><kwd>IoT</kwd><kwd>LoRa</kwd><kwd>LoRaWAN</kwd><kwd>Class-B</kwd><kwd>dataset</kwd><kwd>network sniffer</kwd><kwd>traffic monitoring</kwd><kwd>time synchronization</kwd></kwd-group><funding-group><award-group><funding-source>Brno University of Technology</funding-source><award-id>FEKT-S-23-8191</award-id></award-group><funding-statement>This research was supported by the Internal Grant Agency of Brno University of Technology under project no. FEKT-S-23-8191. The dataset was created with the support of the Technology Agency of the Czech Republic under grant agreement no. TK04020173.</funding-statement></funding-group></article-meta></front><body><sec sec-type="intro" id="sec1-sensors-23-07333"><title>1. Introduction</title><p>The Internet of Things (IoT) has revolutionized the way we interact with our environment, enabling a wide range of applications from smart cities to industrial automation. LPWANs have emerged as key technology for IoT, providing a balance between low power consumption and long-range communication.</p><p>LoRaWAN, a popular LPWAN technology, is based on the Long-Range (LoRa) physical layer and provides features such as adaptive data rates, bidirectional communication, and various device classes, making it suitable for different use cases. Given the limited Radio Frequency (RF) power of 25&#x000a0;mW, LoRaWAN facilitates a communication distance of up to 5&#x000a0;km in urban areas&#x000a0;[<xref rid="B1-sensors-23-07333" ref-type="bibr">1</xref>]. These diverse capabilities have led to widespread adoption across various industries, establishing it as a vital component in the growing IoT ecosystem&#x000a0;[<xref rid="B2-sensors-23-07333" ref-type="bibr">2</xref>,<xref rid="B3-sensors-23-07333" ref-type="bibr">3</xref>].</p><p>The LoRa Physical (PHY) layer employs a unique modulation technique known as Chirp Spread Spectrum (CSS). CSS facilitates long-range communication and robustness against narrow-band interference by spreading the information signal over a wider bandwidth&#x000a0;[<xref rid="B4-sensors-23-07333" ref-type="bibr">4</xref>]. Above this, the LoRaWAN Medium Access Control (MAC) layer provides a standardized protocol for IoT devices&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>].</p><p>LoRaWAN features three distinct device classes&#x02014;A, B, and C&#x02014;addressing different application requirements and power constraints&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>]. Class-A devices offer the highest energy efficiency, suitable for applications with infrequent communication needs, with brief receive windows after each transmission. Class-B devices provide predictable downlink communication latency by synchronizing with network beacons and enabling scheduled receive slots, maintaining moderate power consumption. Class-C devices prioritize downlink latency over power efficiency, offering continuous receive windows for near real-time communication. End devices use a random access transmission method (ALOHA), which allows them communication without the need for pairing with a specific gateway.</p><p>Given the complexity and diverse operating conditions of LoRaWAN, it is essential to gain insight into its actual internal functionality in real deployments using tools for network communication analysis. To address this need, we developed a dedicated hardware sniffer&#x02014;a specialized device designed to capture and decode wireless traffic. In the context of LoRaWAN, this sniffer can be used to collect a dataset and subsequently investigate various aspects of the network, such as signal strength, coverage, data rates, and communication protocols. These insights can help identify potential issues, evaluate network deployments, and optimize configurations for better performance. To provide the greatest flexibility in analyzing the recorded packets, we selected Wireshark&#x02014;a widely recognized open-source network protocol analyzer.</p><p>Our research is guided by several key questions related to the dataset. Firstly, we aim to determine which information can be extracted from captured real-world traffic within a LoRaWAN network, with particular attention to downlink traffic and Class-B beacons. Furthermore, we investigate how Class-B beacons and their optional extensions are used in actual installations. It is also crucial to assess whether security and spectrum regulations are followed in current LoRaWAN deployments. Another key aspect of our research is to examine the accuracy and reliability of time synchronization in LoRaWAN, notably regarding the Class-B beacons, and their susceptibility to interference and misconfiguration. Finally, we explore the potential for new applications of Class-B beacons.</p><sec><title>Contribution of This Work</title><p>We collected and analyzed a large dataset&#x000a0;[<xref rid="B6-sensors-23-07333" ref-type="bibr">6</xref>] of real-world LoRaWAN traffic from four European locations. Unlike previous datasets&#x000a0;[<xref rid="B7-sensors-23-07333" ref-type="bibr">7</xref>,<xref rid="B8-sensors-23-07333" ref-type="bibr">8</xref>,<xref rid="B9-sensors-23-07333" ref-type="bibr">9</xref>,<xref rid="B10-sensors-23-07333" ref-type="bibr">10</xref>], our collection includes uplink, downlink, and Class-B traffic. In the Results and Discussion section, we present an analysis that encompasses the Class-B beacons and highlights potential issues of LoRaWAN deployments.</p><p>To obtain this dataset, we used a custom LoRaWAN hardware sniffer. Both the hardware and software sources of this device are available online&#x000a0;[<xref rid="B11-sensors-23-07333" ref-type="bibr">11</xref>]. Recognizing the outdated LoRaWAN protocol support in Wireshark, we enhanced its capabilities for decoding real-world traffic. These improvements are incorporated into the Wireshark development branch and are now publicly accessible.</p><p>Furthermore, we proposed an innovative approach of using Class-B beacons as a timebase source in urban environments. This method offers several advantages over alternative time sources such as Global Navigation Satellite System (GNSS), DCF77, and Network Time Protocol (NTP), including better indoor reception, smaller and more cost-effective antennas, and independence from internet connectivity.</p><p>Hence, the main contributions of this work are as follows:<list list-type="bullet"><list-item><p>it describes a novel LoRaWAN sniffer with open hardware design files and software framework that allows capturing all LoRaWAN traffic and its examination in&#x000a0;Wireshark;</p></list-item><list-item><p>it provides a large public dataset with real-world traffic captured in multiple locations;</p></list-item><list-item><p>it analyzes the unencrypted part of captured packets, providing insights into network operators, end device manufacturers, and LoRaWAN feature support;</p></list-item><list-item><p>it provides an analysis of Class-B beacons regarding precise timing and gateway localization;</p></list-item><list-item><p>it points out to several identified issues, like invalid Class-B beacons, compromised encryption keys, and invalid LoRaWAN traffic;</p></list-item><list-item><p>it proposes the novel use of Class-B beacons as a timebase source.</p></list-item></list></p></sec></sec><sec id="sec2-sensors-23-07333"><title>2. Related Research</title><p>The IoT research community recognizes the significance of real-world, quantitative data for studying the network environments and deployments. Several LoRaWAN datasets have been made available&#x000a0;[<xref rid="B7-sensors-23-07333" ref-type="bibr">7</xref>,<xref rid="B8-sensors-23-07333" ref-type="bibr">8</xref>,<xref rid="B9-sensors-23-07333" ref-type="bibr">9</xref>,<xref rid="B10-sensors-23-07333" ref-type="bibr">10</xref>]. Bhatia et al.&#x000a0;[<xref rid="B7-sensors-23-07333" ref-type="bibr">7</xref>] gathered uplink packets from gateways in the dense urban environment of London (UK). They included packet header information and PHY layer properties reported by the gateways, making the dataset one of the largest and most extensive&#x000a0;[<xref rid="B12-sensors-23-07333" ref-type="bibr">12</xref>]. Aernouts et al.&#x000a0;[<xref rid="B8-sensors-23-07333" ref-type="bibr">8</xref>] collected data focused on fingerprint localization in Antwerp (Belgium). Their dataset contains a large volume of traces with known end device position.</p><p>Blenn et al.&#x000a0;[<xref rid="B9-sensors-23-07333" ref-type="bibr">9</xref>] presented an analysis of The Things Network (TTN), obtaining a dataset of packets through the TTN Application Programming Interface (API) using a known default network key. However, their findings were constrained to TTN uplink traffic due to its API limitations. Presently, the acquisition of such dataset is no longer feasible due to the evolution of the TTN backend.</p><p>Choi et al.&#x000a0;[<xref rid="B10-sensors-23-07333" ref-type="bibr">10</xref>] developed LoRadar, a passive packet monitoring tool, and conducted an analysis of traffic within an anonymized city-wide area. Their study closely resembles our research. However, they were limited to monitoring uplink sessions due to hardware constraints. To the best of our knowledge, no previous study has attempted to capture both uplink and downlink simultaneously.</p><p>An overview of the existing sniffers is provided in&#x000a0;[<xref rid="B13-sensors-23-07333" ref-type="bibr">13</xref>]. These sniffers are limited to a single RF channel&#x000a0;[<xref rid="B14-sensors-23-07333" ref-type="bibr">14</xref>] or employ one multichannel concentrator&#x000a0;[<xref rid="B9-sensors-23-07333" ref-type="bibr">9</xref>,<xref rid="B10-sensors-23-07333" ref-type="bibr">10</xref>]. They are either based on a gateway (concentrator type)&#x000a0;[<xref rid="B9-sensors-23-07333" ref-type="bibr">9</xref>,<xref rid="B10-sensors-23-07333" ref-type="bibr">10</xref>] or developed using the GNU radio (SDR-type)&#x000a0;[<xref rid="B15-sensors-23-07333" ref-type="bibr">15</xref>]. Software-Defined Radios (SDRs) were deemed unsuitable due to high Signal-to-Noise Ratio (SNR) requirements&#x000a0;[<xref rid="B15-sensors-23-07333" ref-type="bibr">15</xref>,<xref rid="B16-sensors-23-07333" ref-type="bibr">16</xref>,<xref rid="B17-sensors-23-07333" ref-type="bibr">17</xref>]. Recently, an SDR-based demodulator competitive in SNR requirements was made available&#x000a0;[<xref rid="B18-sensors-23-07333" ref-type="bibr">18</xref>]. However, it still demodulates one frequency and Spreading Factor (SF) per block, requiring over 100 differently configured LoRa demodulator blocks for the intended sniffer functionality, which is computationally demanding.</p><p>Other papers focus on simulating various LoRaWAN issues (overview in&#x000a0;[<xref rid="B19-sensors-23-07333" ref-type="bibr">19</xref>]) and the deployment of custom experimental setups (controlled environments of nodes and one or multiple gateways)&#x000a0;[<xref rid="B20-sensors-23-07333" ref-type="bibr">20</xref>,<xref rid="B21-sensors-23-07333" ref-type="bibr">21</xref>]. Our work focuses on passive monitoring of real-world traffic, similar to&#x000a0;[<xref rid="B10-sensors-23-07333" ref-type="bibr">10</xref>], but also includes an important study of downlink messages and Class-B&#x000a0;beacons.</p><p>Time synchronization in LoRaWAN has been analyzed in several studies, such as&#x000a0;[<xref rid="B22-sensors-23-07333" ref-type="bibr">22</xref>,<xref rid="B23-sensors-23-07333" ref-type="bibr">23</xref>]. Ramirez et al.&#x000a0;[<xref rid="B22-sensors-23-07333" ref-type="bibr">22</xref>] achieved an excellent time error below 10&#x000a0;<inline-formula><mml:math id="mm1" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s using a custom protocol in a Class-A network. Rizzi et al.&#x000a0;[<xref rid="B23-sensors-23-07333" ref-type="bibr">23</xref>] employed a posteriori synchronization, enabling time sync with an uncertainty in the order of tens of milliseconds. No studies have suggested passive listening to Class-B beacons for time synchronization.</p></sec><sec id="sec3-sensors-23-07333"><title>3. Sniffer Design</title><p>The sniffer is based on commercially available modules, and its software is customized for capturing network traffic. It operates autonomously when connected to a power source, storing the collected records locally and simultaneously transmitting them to a server over the Long Term Evolution (LTE) modem.</p><p>To overcome the limitations of currently available devices, our new sniffer needs to capture all LoRaWAN traffic according to the EU868 frequency plan, including the RX2 channel&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>]. This necessitates supporting both uplink and downlink reception, which differ in the chirp signal polarity at the physical LoRa layer. Additionally, we aimed to receive Class-B beacons transmitted on RX2 channel with a non-inverted chirp signal. The combinations of these parameters are summarized in <xref rid="sensors-23-07333-t001" ref-type="table">Table 1</xref>.</p><p>The sniffer is based on the industry-standard IMST iC880A LoRaWAN concentrator&#x000a0;[<xref rid="B27-sensors-23-07333" ref-type="bibr">27</xref>], a hardware device designed for receiving and processing LoRa signals in LoRaWAN network gateways. The module is equipped with a Semtech SX1301 digital baseband chip&#x000a0;[<xref rid="B28-sensors-23-07333" ref-type="bibr">28</xref>] and two Semtech SX1257 RF front end chips&#x000a0;[<xref rid="B29-sensors-23-07333" ref-type="bibr">29</xref>], providing up to 10 programmable parallel demodulation paths. It supports multiple LoRaWAN channels in the 868&#x000a0;MHz frequency band, enabling the simultaneous reception of data from multiple end devices. Additionally, the module is also capable of performing time-stamping of incoming packets, which is essential for precise time synchronization.</p><p>The main baseband chip SX1301 provides eight LoRa demodulators with automatic SF selection on IF0&#x02013;IF7 signal paths. Moreover, an additional LoRa demodulator with fixed parameters and implicit header mode support, referenced as a SingleSF modem, is available on the IF8 signal path.</p><p>There are several limitations introduced by the chipset. In the LoRa physical layer, the modulated signal is represented by a chirp, which is a sinusoidal waveform whose frequency increases or decreases linearly over time&#x000a0;[<xref rid="B4-sensors-23-07333" ref-type="bibr">4</xref>]. The SX1301 demodulator can only detect chirps with one of two different polarities, each representing its inverse. Each LoRa demodulator needs to know the polarity of a LoRa chirp signal in advance. As a result, at least two iC880A concentrator modules need to be used for a simultaneous reception of uplink and downlink transmission, each configured to demodulate a different chirp signal polarity (GW #1 and GW #2).</p><p>The IF0&#x02013;IF7 LoRa channels may be connected individually to radio front ends, referenced as Radio A or Radio B&#x000a0;[<xref rid="B27-sensors-23-07333" ref-type="bibr">27</xref>,<xref rid="B28-sensors-23-07333" ref-type="bibr">28</xref>]. However, the useful bandwidth of SX1257 radios is approximately only 925&#x000a0;kHz&#x000a0;[<xref rid="B30-sensors-23-07333" ref-type="bibr">30</xref>], assuming typical 125&#x000a0;kHz channels in the EU868 band&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>]. This bandwidth is sufficient for the simultaneous reception of all RX1 channels using both front ends. Nevertheless, the RX2 channel operates at a significantly different frequency, making it impossible to receive using the typical configuration. This is not an issue for a standard concentrator, as it only transmits on RX2 without receiving. However, for a sniffer, complete data reception is desired. To overcome this limitation, a third iC880A concentrator must be added to the sniffer system (GW #3). This concentrator enables reception in the RX2 downlink with one of its eight LoRa demodulators. <xref rid="sensors-23-07333-f001" ref-type="fig">Figure 1</xref> illustrates the relationship between channels, bands, and radio front ends.</p><p>Another goal of the sniffer is to receive Class-B beacons. These beacons are transmitted on the RX2 frequency with specific parameters involving the implicit LoRa header&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>]. Demodulation of the header is supported by the SingleSF modem on the IF8 signal path. An implicit header refers to a packet format where the length of the packet is not explicitly included in the packet header. Instead, the packet length is assumed to be fixed and known in advance. This reception is handled by the third concentrator module (GW #3).</p><sec id="sec3dot1-sensors-23-07333"><title>3.1. Sniffer Hardware Overview</title><p><xref rid="sensors-23-07333-f002" ref-type="fig">Figure 2</xref> shows the block diagram of our LoRaWAN sniffer. Initially, the radio signal is received by an Ultra-High Frequency (UHF) omnidirectional antenna with a gain of 2&#x000a0;dBi and vertical polarization. This signal is subsequently filtered by a narrow bandpass filter, amplified by a Low Noise Amplifier (LNA), and then distributed to the inputs of three iC880A modules via a power splitter. <xref rid="sensors-23-07333-t002" ref-type="table">Table 2</xref> outlines the function of each iC880A module.</p><p>A Raspberry Pi minicomputer serves as the central processing unit, which communicates with the iC880A modules through its integrated Serial Peripheral Interfaces (SPIs). In addition, it obtains the current time from a GNSS receiver module for accurate timestamping of the received packets. For this purpose, a 1 pps signal is distributed from the GNSS module to all iC880A concentrators. The Raspberry Pi also has a Real Time Circuit (RTC) connected to its I<inline-formula><mml:math id="mm2" overflow="scroll"><mml:mrow><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:math></inline-formula>C interface and an LTE modem connected via USB for remote management and sending the measured data to the server. An external 24&#x000a0;V adapter powers the whole device. <xref rid="sensors-23-07333-f003" ref-type="fig">Figure 3</xref> shows the photo of the sniffer internal hardware. Complete schematics and hardware design files are available online&#x000a0;[<xref rid="B11-sensors-23-07333" ref-type="bibr">11</xref>].</p><p>From a mechanical perspective, the complete LoRaWAN sniffer is enclosed in an IP68-rated aluminum box, enabling safe outdoor installations. To accommodate the sniffer&#x02019;s requirement for GNSS-based time synchronization and LTE communication, an additional plastic container conceals the GNSS and LTE antennas, eliminating the need for waterproof external antennas. The two containers are securely bonded together and all openings are sealed to maintain watertight integrity.</p></sec><sec id="sec3dot2-sensors-23-07333"><title>3.2. Sniffer Software Overview</title><p>The software relies on adapted open-source utilities supplied by Semtech, specifically <monospace>libloragw</monospace> from the <monospace>lora_gateway</monospace> repository&#x000a0;[<xref rid="B30-sensors-23-07333" ref-type="bibr">30</xref>] and <monospace>lora_pkt_fwd</monospace> from the <monospace>packet_forwarder</monospace> repository&#x000a0;[<xref rid="B31-sensors-23-07333" ref-type="bibr">31</xref>]. The LoRa gateway library manages SPI communication between the host computer and the SX1301 baseband chip. The packet forwarder employs the gateway library to receive packets, incorporate detailed data, and transmit the packet via a standardized UDP socket.</p><p>It was necessary to add support for handling multiple SPIs, switching chirp signal polarity, receiving packets without a valid Cyclic Redundancy Check (CRC), and decoding the Class-B beacon implicit header. As a result, the packet forwarder was modified to parse configuration JavaScript Object Notation (JSON) files and pass the relevant settings to the library, enhancing its versatility and adaptability. The complete software framework is available online&#x000a0;[<xref rid="B11-sensors-23-07333" ref-type="bibr">11</xref>].</p></sec><sec id="sec3dot3-sensors-23-07333"><title>3.3. Data Processing</title><p>To address the limitations of original Wireshark LoRa encapsulation, we developed an updated version of the LoRaTap header to efficiently manage the additional PHY layer information, such as frequency channel, signal level, timestamp, and other relevant details&#x000a0;[<xref rid="B32-sensors-23-07333" ref-type="bibr">32</xref>]. The sniffer&#x02019;s JSON output produced by the packet forwarder utility can be converted to the <monospace>pcap</monospace> format by conversion utility&#x000a0;[<xref rid="B11-sensors-23-07333" ref-type="bibr">11</xref>].</p><p>We also significantly updated the Wireshark LoRaWAN dissector. Key enhancements include the addition of a LoRaWAN Class-B beacon dissector, <italic toggle="yes">Join Accept</italic> decryption, support for MAC commands from the LoRaWAN v1.0.4 specification&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>], and various improvements to enable successful decoding of real-world traffic captured by the sniffer. These modifications are integrated into the development branch for future official release and are currently available through the Wireshark automated builds&#x000a0;[<xref rid="B33-sensors-23-07333" ref-type="bibr">33</xref>].</p></sec><sec id="sec3dot4-sensors-23-07333"><title>3.4. Analysis and Decryption</title><p>Subsequent data processing can be performed manually in Wireshark or through automated scripts in Wireshark&#x02019;s console version, TShark. We employed an automated approach for the quantitative analysis of captured packets. Data post-processing from the TShark utility is executed with Python scripts, while final statistical and visual processing is carried out in MATLAB. The scripts are available online&#x000a0;[<xref rid="B11-sensors-23-07333" ref-type="bibr">11</xref>].</p><p>LoRaWAN packets are usually partially encrypted, with the keys generally unknown to a sniffer device. However, there are several properties of LoRaWAN communication that can be analyzed without knowing the decryption keys. The following fields of a LoRaWAN packet are not encrypted:</p><list list-type="bullet"><list-item><p>Message Header (MHDR): Contains information about the message type (MType) and LoRaWAN version.</p></list-item><list-item><p>Device Address (DevAddr): A unique 32-bit identifier for the end device within a specific network.</p></list-item><list-item><p>Frame Control (FCtrl): Contains information about the Adaptive Data Rate (ADR), Frame Options Length, and other control flags.</p></list-item><list-item><p>Frame Counter (FCnt): A 16-bit counter value that increments with each uplink frame to prevent replay attacks.</p></list-item><list-item><p>Frame Options (FOpts): Contains optional MAC commands.</p></list-item><list-item><p>Frame Port (FPort): Indicates the port number for application-specific or MAC layer communication.</p></list-item></list><p>The application payload (FRMPayload) and Message Integrity Check (MIC) are encrypted for both uplink and downlink packets, requiring the corresponding keys for decryption and verification&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>].</p><p>LoRaWAN activation processes include the Over-the-Air Activation (OTAA) and Activation By Personalization (ABP). OTAA involves an end device transmitting a <italic toggle="yes">Join Request</italic>, encrypted with a pre-shared Application Key (AppKey). The network server verifies the request, generates session keys, namely the Network Session Key (NwkSKey) for the MIC and the Application Session Key (AppSKey) for the payload, and responds with a <italic toggle="yes">Join Accept</italic> message, which includes the assigned Device Address (DevAddr). Given the necessary keys, Wireshark can decrypt the join process packets, allowing for a more comprehensive analysis.</p><p>ABP, on the other hand, involves pre-configuring the end device with session keys (NwkSKey and AppSKey) and a DevAddr, enabling immediate communication without a join procedure. While this approach simplifies the process, it may increase security risks due to prolonged use of the same keys.</p></sec></sec><sec sec-type="results" id="sec4-sensors-23-07333"><title>4. Results and Discussion</title><p>Data from the LoRaWAN networks were collected in four cities: Liege (Belgium), Graz (Austria), Vienna (Austria), and Brno (Czechia). These cities were chosen for data gathering due to various factors, such as their central European location, their prominence as major urban areas with well-established LoRaWAN networks, and the intention to capture a diverse range of city environments for data collection. <xref rid="sensors-23-07333-t003" ref-type="table">Table 3</xref> provides a summary of the characteristics and details associated with each capture.</p><p>Ideal placement of the sniffer in Vienna and Brno is evident in the distribution of the number of packets received in uplink, downlink, and independent downlink (RX2), as depicted in <xref rid="sensors-23-07333-f004" ref-type="fig">Figure 4</xref>. To account for varying time periods across the datasets, packet counts in all histograms were normalized to display the number of packets per day.</p><p>In Liege, the site is primarily characterized by the downlink traffic&#x02014;unconfirmed data without a checksum, particularly on the RX2 channel. The Graz data also suggest a suboptimal sniffer placement, as the sniffer predominantly captured downlink signals from gateways (better positioned than nodes). Consequently, most of the received uplink traffic was discarded due to wrong checksums, as shown in <xref rid="sensors-23-07333-f004" ref-type="fig">Figure 4</xref>.</p><sec id="sec4dot1-sensors-23-07333"><title>4.1. Selected Results of Data Post-Processing</title><p>Despite optimal sniffer placement in Vienna and Brno, a higher number of packets was received in the downlink compared to the uplink. The distribution of valid LoRaWAN message types is depicted in <xref rid="sensors-23-07333-f005" ref-type="fig">Figure 5</xref>. To determine the validity of real LoRaWAN messages, the CRC verification was applied at the physical LoRa packet level, and packet headers were checked for errors. Payload checksums were verified for the Class-B beacons.</p><p>Suboptimal placement in Liege and Graz resulted in the reception of predominantly downlink packets. Class-B beacons were observed in Brno, Liege, and Vienna. In some instances, particularly in Liege, these beacons also conveyed additional information regarding the geographic position of the gateway.</p><p>The Vienna dataset can be considered a representative source of data. The histograms in <xref rid="sensors-23-07333-f006" ref-type="fig">Figure 6</xref> demonstrate the identified transmission parameters. Spreading factors SF7 and SF12 are dominant, with a coding rate of 4/5 required by the standard&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>]. Channels are occupied almost uniformly (except for the 867.5&#x000a0;MHz frequency), and most packets are relatively short, with lengths of 12&#x02013;19&#x000a0;bytes in the downlink and 20&#x02013;40&#x000a0;bytes in the uplink. The Received Signal Strength Indicator (RSSI) and the SNR confirm the superior placement of gateways compared to nodes in terms of radio coverage.</p><p><xref rid="sensors-23-07333-t004" ref-type="table">Table 4</xref> shows the percentage of traffic with declared Adaptive Data Rate (ADR) support from end devices (extracted from uplink frames) and network servers (from downlink frames), declared end device Class-B support, and the percentage of downlink messages containing valid payload CRC.</p><p>ADR is a feature that optimizes the data rate, transmission power, and airtime for end devices based on their connectivity conditions&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>]. In uplink frames, the ADR flag set by the end device indicates its support for the ADR feature and requests the network server to manage its data rate and transmission power settings. When the ADR bit is set in a downlink frame, it informs the end device that the network server can send ADR commands. The ClassB flag in the uplink packet header indicates to the network server that the end device activated Class-B mode and is ready to receive scheduled downlink&#x000a0;pings.</p><p>In accordance with the LoRaWAN standard, uplink and downlink packets are distinguished by the presence of payload CRC. While payload CRC is mandatory in the uplink packets, the standard does not require it in the downlink, allowing for reduced airtime and associated duty cycle for gateway transmissions&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>]. However, the observed data indicate that, aside from the Liege site, payload CRC is appended in the downlink by the majority of LoRaWAN gateways.</p></sec><sec id="sec4dot2-sensors-23-07333"><title>4.2. Network Operators</title><p>The DevAddr field serves as an identifier for the end device within the LoRaWAN network&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>,<xref rid="B34-sensors-23-07333" ref-type="bibr">34</xref>]. It is transmitted unencrypted in both the uplink and downlink. To determine the network operator, we cross-referenced the prefix of DevAddr with the LoRa Alliance list&#x000a0;[<xref rid="B35-sensors-23-07333" ref-type="bibr">35</xref>]. The corresponding findings are presented in <xref rid="sensors-23-07333-t005" ref-type="table">Table 5</xref>, which highlights significant traffic (over 400 packets per day) from various locations. Identifying the source gateway from a captured downlink packet is infeasible without access to the network server because it lacks explicit gateway-related information.</p></sec><sec id="sec4dot3-sensors-23-07333"><title>4.3. End Device Manufacturers</title><p>In addition to analyzing the network operators, we also examined the end device manufacturers by analyzing the Device Extended Unique Identifier (DevEUI) field in the <italic toggle="yes">Join Request</italic> messages. The DevEUI is a globally unique number assigned to a LoRaWAN device and complies with the 64-bit Extended Unique Identifier (EUI-64) format&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>].</p><p>To identify the manufacturers of end devices within the captured dataset, we cross-referenced the DevEUIs with the IEEE EUI-64 address space&#x000a0;[<xref rid="B36-sensors-23-07333" ref-type="bibr">36</xref>]. <xref rid="sensors-23-07333-t006" ref-type="table">Table 6</xref> presents the results of this analysis, highlighting the major traffic (over 10 join packets per day) from different manufacturers.</p></sec><sec id="sec4dot4-sensors-23-07333"><title>4.4. Class-B Beacon Analysis</title><p>In a LoRaWAN Class-B network, gateways must be synchronized to broadcast the Class-B beacons. Two possible modes of the transmission exist: tightly synchronized, with gateways synchronized to Global Positioning System (GPS) time with an accuracy better than 1&#x000a0;<inline-formula><mml:math id="mm3" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s, allowing them to transmit beacons every 128&#x000a0;seconds; and loosely synchronized, where gateways can synchronize with GPS time with an accuracy better than 1&#x000a0;ms but not 1&#x000a0;<inline-formula><mml:math id="mm4" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s, requiring randomized beacon transmission. Tightly synchronized gateways capitalize on the single-frequency network, while loosely synchronized gateways utilize randomization to counteract beacon interference caused by lower transmit timing accuracy.</p><p>To effectively filter the sniffer data, we utilized specific settings for beacon reception at the LoRa physical layer. These settings include an implicit header mode, SF9BW125, CR 4/5, no CRC, a payload length of 17 bytes, a preamble length of 10 symbols, and a non-inverted LoRa signal&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>]. The payload comprises a timestamp (representing seconds elapsed since the start of the GPS epoch) and a gateway-specific parameter (e.g., geographic coordinates or network/gateway identification). Each part is protected by an independent CRC checksum.</p><p>Significant differences were observed between the various locations included in the dataset. Ideally, 675 beacons per day should be received, considering the beacon interval of 128 s and tightly synchronized gateways without transmitting randomization. As expected due to interference, the actual numbers were lower. However, the data from Vienna and Brno also contained a substantial number of packets violating the LoRaWAN standard, as discussed in later sections.</p><p><xref rid="sensors-23-07333-t007" ref-type="table">Table 7</xref> presents an analysis of the captured LoRaWAN Class-B beacons across different locations. All packets included in the table have both of their CRC checksums valid. The timestamp correctness was determined by comparing the precise time of the beacon arrival and the timestamp value contained in its payload. No Class-B beacons were included in the captured data from Graz.</p><p>The locations of Class-B gateways broadcasting their coordinates in the Liege and Vienna datasets, as well as lines connecting each gateway to the respective receiving sniffer, are depicted in <xref rid="sensors-23-07333-f007" ref-type="fig">Figure 7</xref>.</p><p>One of the most crucial pieces of data obtained was the accuracy of the beacon timestamps. <xref rid="sensors-23-07333-f008" ref-type="fig">Figure 8</xref> displays the difference between the sniffer reference time, synchronized by the 1&#x000a0;pps signal from the GNSS receiver, and the time of the received beacon packet. This difference should ideally represent the beacon signal propagation delay. The time of the received packet is adjusted by 154,143&#x000a0;<inline-formula><mml:math id="mm5" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s to incorporate the following corrections:<list list-type="bullet"><list-item><p>1500&#x000a0;<inline-formula><mml:math id="mm6" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s, the time delay specified in the LoRaWAN standard as <inline-formula><mml:math id="mm7" overflow="scroll"><mml:mrow><mml:mrow><mml:msub><mml:mi>T</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mi>e</mml:mi><mml:mi>a</mml:mi><mml:mi>c</mml:mi><mml:mi>o</mml:mi><mml:mi>n</mml:mi><mml:mi>D</mml:mi><mml:mi>e</mml:mi><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>y</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>&#x000a0;[<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>];</p></list-item><list-item><p>152,576&#x000a0;<inline-formula><mml:math id="mm8" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s, the beacon packet transmission time (calculated from the EU868 beacon channel settings&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>] with tool&#x000a0;[<xref rid="B37-sensors-23-07333" ref-type="bibr">37</xref>]);</p></list-item><list-item><p>67&#x000a0;<inline-formula><mml:math id="mm9" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s, the empirically determined delay, likely due to sniffer signal processing.</p></list-item></list></p><p>For gateways that broadcast their geographic position as part of the beacon payload, we calculated the distance from the sniffer and displayed it as triangular marks in <xref rid="sensors-23-07333-f008" ref-type="fig">Figure 8</xref>. The figure reveals the low jitter in the arrival times of beacons across all sites, enabling the identification of individual gateways based on their distance from the sniffer. In addition to the gateways confirmed through the geographic coordinates, <xref rid="sensors-23-07333-f008" ref-type="fig">Figure 8</xref> displays the reception of two more gateways in Vienna (at distances of approximately 31 km and 58 km) and a gateway in Brno (at a distance of approximately 90 km). It is important to note that this distance calculation assumes tightly synchronized gateways. Gateways that appear to be significantly distant may be loosely synchronized, transmitting their beacons with a small delay.</p><p>It is also evident that the beacons with invalid timestamps described earlier are in close proximity to the sniffer. Considering the sniffer&#x02019;s location on university campuses in both Vienna and Brno, these gateways might be experimental and utilized for research and development purposes.</p><sec id="sec4dot4dot1-sensors-23-07333"><title>4.4.1. Beacons in the Liege Region</title><p>At the Liege location, well-configured gateways were found near the border in the Netherlands, specifically in the Maastricht and Haarlen area. A total of seven gateways with unique coordinates were identified, situated between 15 and 36 km from the sniffer. No gateways lacking the position or with invalid beacon frames were detected.</p></sec><sec id="sec4dot4dot2-sensors-23-07333"><title>4.4.2. Beacons in the Vienna Region</title><p>Two nearby gateways broadcasting their geographic coordinates were identified. Based on the timing analysis shown in <xref rid="sensors-23-07333-f008" ref-type="fig">Figure 8</xref>, it appears that two additional, more distant gateways also contributed to beacon broadcasting.</p><p>However, alongside valid packets, a number of frames transmitted at incorrect times were captured. The vast majority of these erroneous frames were offset by 18&#x000a0;seconds. This offset is likely due to a faulty implementation of the conversion between the GPS time used in Class-B beacons and the commonly used Coordinated Universal Time (UTC). GPS time does not include leap seconds&#x000a0;[<xref rid="B38-sensors-23-07333" ref-type="bibr">38</xref>,<xref rid="B39-sensors-23-07333" ref-type="bibr">39</xref>] and was synchronized to UTC on 5&#x000a0;January 1980. In 2023, the number of leap seconds, i.e., the difference between GPS time and UTC, is precisely 18&#x000a0;s.</p><p>Although the majority of recorded beacons were either valid or shifted by 18 s, the sniffer also recorded a significant number (0.9%) of packets with different time shifts, as demonstrated in <xref rid="sensors-23-07333-f009" ref-type="fig">Figure 9</xref>. The frames are always shifted by a whole number of seconds, meaning their synchronization within a one-second window is maintained. These packets are likely sent by a malfunctioning gateway, where the system clock is not correct, even though the actual beacon transmission is initiated accurately by the 1 pps signal. Together with the packets shifted by 18&#x000a0;seconds, they pose a significant issue for Class-B synchronization in the Vienna area and are likely to cause random network problems, resulting in LoRaWAN downlink latency degraded to Class-A. In this situation, the Class-B functionality of the end device depends on whether it synchronizes to a correct or invalid signal during the beacon acquisition phase&#x000a0;[<xref rid="B40-sensors-23-07333" ref-type="bibr">40</xref>].</p></sec><sec id="sec4dot4dot3-sensors-23-07333"><title>4.4.3. Beacons in the Brno Region</title><p>Two beacon signals were identified in the captured data from Brno. One correct signal was received from a gateway with a time offset corresponding to an approximate distance of 90 km from the sniffer&#x02019;s location. No geographic coordinates that could confirm this distance were found, and approximately 0.1% of the frames were invalid.</p><p>Another beacon was identified at a distance of about 2&#x000a0;km. The timing information contained in this beacon was incorrect, shifted by 315,964,782&#x000a0;seconds. This value corresponds to the 315,964,800&#x000a0;seconds difference between the GPS and UNIX time. By subtracting the 18&#x000a0;leap seconds from this difference, we obtain the observed time shift. In other words, the gateway transmits a UNIX timestamp instead of the GPS time required by the LoRaWAN&#x000a0;standard.</p></sec></sec><sec id="sec4dot5-sensors-23-07333"><title>4.5. Channel Occupation and Duty Cycle Violations</title><p>LoRaWAN transmissions in the EU868 band must adhere to regulations specified in the ETSI EN 300 220 standard&#x000a0;[<xref rid="B25-sensors-23-07333" ref-type="bibr">25</xref>]. The duty cycle limitations for end devices and gateways vary depending on the specific frequency sub-bands. However, it should be noted that these duty cycle limitations are only required if the Listen Before Talk (LBT) is not used:<list list-type="bullet"><list-item><p>band L, 865&#x000a0;MHz to 868&#x000a0;MHz, &#x02264;1% duty cycle, includes RX1 channels 4 to 8;</p></list-item><list-item><p>band M, 868.000&#x000a0;MHz to 868.600&#x000a0;MHz, &#x02264;1% duty cycle, includes RX1 channels 1 to 3;</p></list-item><list-item><p>band P, 869.400&#x000a0;MHz to 869.650&#x000a0;MHz, &#x02264;10% duty cycle, includes RX2 channel.</p></list-item></list></p><p>The EU868 band is shared with other short-range devices that comply with the regulations, typically employing narrow-band Frequency Shift Keying (FSK) and Amplitude Shift Keying (ASK) modulations. We assessed the shared spectrum usage by calculating the on-air time of each captured packet, a value derived from the spreading factor, bandwidth, coding rate, preamble length, and packet length&#x000a0;[<xref rid="B37-sensors-23-07333" ref-type="bibr">37</xref>,<xref rid="B41-sensors-23-07333" ref-type="bibr">41</xref>].</p><p>Furthermore, we computed the total air time for all sites, separately for the uplink and downlink, since different transmit directions utilize inverted, uncorrelated chirps. All captured packets, including those with invalid CRCs, were considered in the calculation to evaluate the total LoRa transmission time on the respective channel. The highest channel occupation was observed at Vienna and Brno, as shown in <xref rid="sensors-23-07333-f010" ref-type="fig">Figure 10</xref>.</p><p>Associating a downlink packet with a specific gateway is unfortunately not feasible. Nonetheless, it is possible to calculate the uplink on-air time for individual end devices based on their DevAddr field. The ETSI EN 300 220 duty cycle limits are not applicable to individual LoRaWAN channels; instead, they apply to specified bands that encompass several adjacent channels.</p><p>Considering this criterion, we identified a total of eight devices in the Brno dataset that, while adhering to the 1% duty cycle limitation on individual channels, exceed this limitation by several times for the L and M bands (with duty cycles reaching up to 3.9% for the L band and up to 2.6% for the M band). In the Vienna dataset, a single device was discovered to violate the limitation (2.7% duty cycle in the L band). However, it is worth noting that the devices we identified as exceeding the duty cycle limitation could potentially be using the LBT strategy.</p></sec><sec id="sec4dot6-sensors-23-07333"><title>4.6. Compromised Encryption Keys</title><p>The captured data show that exposed encryption keys are used in existing LoRaWAN networks. Semtech&#x02019;s default key (<monospace>2B7E151628AED2A6ABF7158809CF4F3C</monospace>)&#x000a0;[<xref rid="B9-sensors-23-07333" ref-type="bibr">9</xref>] was identified in the Brno dataset. This key is used as the AppKey for the OTAA by RisingHF devices by default, according to the DevEUI identifier. A significant number of data packets (15.5% of all valid packets) from ABP-activated devices in Brno use it as both the NwkSKey and AppSKey. A smaller number of such devices were also discovered in the Vienna dataset (0.2%).</p><p>Similarly, the Milesight default key (<monospace>5572404C696E6B4C6F52613230313823</monospace>)&#x000a0;[<xref rid="B42-sensors-23-07333" ref-type="bibr">42</xref>] was identified in OTAA-activated devices across Vienna, Brno, and Liege, primarily utilizing TTN. If an eavesdropper intercepts the entire <italic toggle="yes">Join Request</italic>&#x02013;<italic toggle="yes">Join Accept</italic> pair, they could derive the NwkSKey and AppSKey, enabling them to decrypt the entire communication of the affected device.</p><p>A small number of packets in the Brno and Vienna datasets were also found to use the empty key (<monospace>00000000000000000000000000000000</monospace>). In these cases, the devices appear to be unconfigured or experimental.</p></sec><sec id="sec4dot7-sensors-23-07333"><title>4.7. Limited Front End Image Frequency Rejection</title><p>Strong packets can sometimes be received on two different channels with different chirp polarities. This phenomenon arises due to the limited value of the Image Frequency Rejection Ratio (IMRR) of the radio front ends found in LoRaWAN gateways and the sniffer. An example of this can be identified in packets #306 and #307 in the Brno dataset, with the key characteristics depicted in <xref rid="sensors-23-07333-f011" ref-type="fig">Figure 11</xref>.</p><p>The packets were received almost simultaneously, with a negligible 6 <inline-formula><mml:math id="mm10" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s difference. Frame #307 is a valid uplink transmission with a signal strength of <inline-formula><mml:math id="mm11" overflow="scroll"><mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>59</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> dBm and a non-inverted chirp. Given a received frequency of 867.7 MHz and a front end center frequency of 867.5 MHz (as shown in <xref rid="sensors-23-07333-t001" ref-type="table">Table 1</xref>), we can anticipate a mirror signal at 867.3 MHz. This is confirmed by frame #306, which has a signal strength of <inline-formula><mml:math id="mm12" overflow="scroll"><mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>112</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> dBm. The difference of 53 dB corresponds to the IMRR value of the SX1257 front end employed in the sniffer.</p><p>Due to the signal spectrum inversion, such invalid packets can be easily identified by the inverted chirp flag, because data marked as uplink in the LoRaWAN header should not be detected by the downlink sniffer. To ensure data accuracy, these packets were filtered out during processing. The described behavior could potentially overshadow a legitimate weak packet at the mirror frequency. However, the likelihood of its occurrence is almost negligible, given the low usage of the channels.</p></sec><sec id="sec4dot8-sensors-23-07333"><title>4.8. Invalid LoRaWAN Traffic with Valid Checksum</title><p>LoRa packets at the PHY layer contain a Synchronization Word (SyncWord), which serves to differentiate the contents of the following payload. The use of the SyncWord can be confusing due to limited information from the manufacturer.</p><p>Semtech recommends only two SyncWord values: 0x12 for private networks, and 0x34 for public/LoRaWAN networks [<xref rid="B41-sensors-23-07333" ref-type="bibr">41</xref>,<xref rid="B43-sensors-23-07333" ref-type="bibr">43</xref>]. Documents from the LoRaWAN Alliance [<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>] and certain source codes [<xref rid="B30-sensors-23-07333" ref-type="bibr">30</xref>] imply that SyncWord 0x34 is designated for all networks utilizing the LoRaWAN protocol at the MAC layer. This interpretation suggests that both publicly and privately designed networks following the LoRaWAN standard should employ SyncWord 0x34. The private SyncWord 0x12 appears to be reserved for devices utilizing LoRa modulation at the PHY layer without engaging the LoRaWAN MAC layer.</p><p>The SyncWord setting is crucial for both modulation and demodulation, as the receiver does not accept packets transmitted with a different SyncWord [<xref rid="B44-sensors-23-07333" ref-type="bibr">44</xref>]. This issue is not merely about discarding packets in the case of a mismatch; it arises from the inability to synchronize on the preamble&#x02013;SyncWord pair [<xref rid="B45-sensors-23-07333" ref-type="bibr">45</xref>].</p><p>All datasets contain packets with errors that the LoRaWAN dissector cannot decode. The Liege dataset includes a significant number of invalid packets (4.7% of the total). Invalid packets are identified by dissector errors or invalid MAC header entries, which include a non-zero Reserved for Future Use (RFU) field and a Major version that is not equal to R1.</p><p>Receiving an invalid LoRaWAN packet can be attributed to a misconfiguration of the LoRa transmitter, which uses a custom payload for packets set with a public SyncWord. The correct approach would be to use a dedicated private SyncWord, which appears to be the issue occurring in the Liege dataset. Another possibility involves accepting invalid packets that are erroneously evaluated as valid due to various factors. This could be attributed to the limited reliability of the 16-bit payload CRC [<xref rid="B41-sensors-23-07333" ref-type="bibr">41</xref>], which may occasionally fail to identify packet corruption, or it could be due to the unwanted acceptance of packets with a private SyncWord.</p><p>The SyncWord issue was investigated in the InterOP project ATCZ175 [<xref rid="B46-sensors-23-07333" ref-type="bibr">46</xref>], and its results indicate a relatively low capability of the gateway to filter packets based on SyncWord. The success rate of receiving a private SyncWord packet when the gateway is set to the public SyncWord depends on the signal strength and the SF used, with the possibility of reaching up to 10%. Consequently, any traffic with a private SyncWord may lead to the observation of invalid packets in sniffer datasets.</p></sec><sec id="sec4dot9-sensors-23-07333"><title>4.9. Class-B Beacons as a Timebase Source</title><p>Class-B beacons in LoRaWAN networks have the potential to serve as alternative timebase source in urban environments. Beacon receivers typically lock within 128 s, providing excellent long-term stability, as their timing is usually derived from a GNSS receiver. According to the LoRaWAN standard, beacon timing is accurate within &#x000b1;1 <inline-formula><mml:math id="mm13" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s, while measurements taken by the sniffer without further optimizations revealed an accuracy of &#x000b1;5 <inline-formula><mml:math id="mm14" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s. This accuracy is further reduced by the wireless propagation delay&#x02014;every 300 m of distance represents an additional 1 <inline-formula><mml:math id="mm15" overflow="scroll"><mml:mrow><mml:mi mathvariant="sans-serif">&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula>s offset.</p><p>Compared to GNSS, Class-B beacons can be received indoors, making them suitable for time synchronization in buildings and other structures where GNSS signals are weak or unavailable. Unlike GNSS, Class-B beacons do not require a clear view to the sky, enhancing their reliability in urban environments where tall buildings, trees, or other obstacles might obstruct GNSS signals.</p><p>Class-B beacons also offer several benefits compared to the DCF77, a Long-Wave (LW) time signal broadcast from Germany. They exhibit high immunity to noise, making them more reliable in urban environments where specific types of RF interference are common, e.g., the LW interference affecting DCF77 signals. Class-B beacon receivers can use small, cheap antennas, lowering the overall cost and making them more accessible for a wide range of applications. Moreover, Class-B beacons have a similar lock speed to DCF77, with a lock time of up to 128 s compared to DCF77&#x02019;s typical lock time of 2&#x02013;3 min [<xref rid="B47-sensors-23-07333" ref-type="bibr">47</xref>].</p><p>Compared to NTP, Class-B beacons do not require an internet connection for time synchronization, making them suitable for environments with limited or no internet access. This independence from internet connections makes Class-B beacons a compelling alternative for various applications.</p><p>To further enhance the lock time, a multichannel (e.g., SDR-based) device may listen for Class-A downlink traffic, which may contain the <italic toggle="yes">DeviceTimeAns</italic> time command in its unencrypted MAC header. Despite the limited accuracy of &#x000b1;100 ms as defined in [<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>], this may allow for a coarse lock. The listening device can also derive the time window for Class-B beacon reception from this information, potentially reducing continuous receive time.</p><p>However, this proposed time synchronization may encounter difficulties if nearby gateways transmit beacons that violate the LoRaWAN standard. Such issues have already been observed in the Vienna and Brno regions, as previously discussed. Currently, no method exists to verify the authenticity of a received beacon. Moreover, due to the harsh RF environment, beacons may be disrupted by a wide-band UHF interference, resulting in decoding errors and significantly longer lock time.</p><p>Despite these challenges, by leveraging the benefits of Class-B beacons, time synchronization in urban environments can be significantly improved. The indoor reception capabilities, noise immunity, cost effectiveness, and independence from satellite availability and internet connections make Class-B beacons an attractive alternative for existing time synchronization methods, provided that the associated disadvantages can be effectively managed.</p></sec></sec><sec sec-type="conclusions" id="sec5-sensors-23-07333"><title>5. Conclusions</title><p>In this study, we created an extensive, publicly available dataset encompassing complete LoRaWAN traffic from four European cities. This dataset enabled rigorous examination of real-world LoRaWAN network functionality. Our analysis revealed security and system challenges, which include:<list list-type="bullet"><list-item><p>invalid Class-B beacon packets, which pose a significant synchronization issue and are likely to cause random Class-B network problems;</p></list-item><list-item><p>default encryption keys from Semtech and Milesight in existing LoRaWAN installations, which pose a security risk;</p></list-item><list-item><p>end devices violating the duty cycle limitation for EU868 sub-bands, which could potentially degrade the quality of service for other wireless devices.</p></list-item></list></p><p>We enhanced Wireshark&#x02019;s LoRaWAN protocol dissector to accurately decode recorded traffic, including data and MAC command decryption for packets with known keys. These improvements are now publicly accessible. Additionally, we proposed the use of Class-B beacons as a timebase source in urban environments.</p><p>Future research should incorporate datasets from a broader range of locations to enhance understanding of LoRaWAN networks. Additionally, addressing the issues related to invalid Class-B beacons is a critical next step. Class-B devices currently allow the fallback to Class-A when they experience difficulties in tracking the beacon. However, this depends on the specific device implementation, since the documentation only suggests an initial non-specific synchronization [<xref rid="B5-sensors-23-07333" ref-type="bibr">5</xref>,<xref rid="B40-sensors-23-07333" ref-type="bibr">40</xref>].</p><p>Validating received beacons remains a challenge. The beacon payload may contain an optional network/gateway identification. However, to the best of our knowledge, no beacon filtering implementation has been introduced yet. Another approach could involve transmitting the initial synchronization over a secure channel, specifically within a unicast packet with a MIC signature. This method can be employed to acquire the correct Class-B beacon. While a solution that utilizes the <italic toggle="yes">DeviceTimeAns</italic> command to acquire coarse time has been implemented, its use remains optional.</p></sec></body><back><ack><title>Acknowledgments</title><p>During the preparation of this work, the authors utilized ChatGPT to help refine language and style. After using this service, the authors reviewed and edited the paper as necessary, and they take full responsibility for the content of the publication.</p></ack><fn-group><fn><p><bold>Disclaimer/Publisher&#x02019;s Note:</bold> The statements, opinions and data contained in all publications are solely those of the individual author(s) and contributor(s) and not of MDPI and/or the editor(s). MDPI and/or the editor(s) disclaim responsibility for any injury to people or property resulting from any ideas, methods, instructions or products referred to in the content.</p></fn></fn-group><notes><title>Author Contributions</title><p>Conceptualization, A.P. and J.K.; methodology, A.P. and J.K.; software, A.P. and O.K.; validation, A.P., O.K. and M.N.; formal analysis, A.P.; investigation, A.P.; resources, J.K., H.A. and M.N.; data curation, A.P.; writing&#x02014;original draft preparation, A.P.; writing&#x02014;review and editing, A.P., J.K. and H.A.; visualization, A.P.; supervision, J.K. and H.A.; project administration, A.P. and J.K.; funding acquisition, J.K. All authors have read and agreed to the published version of the manuscript.</p></notes><notes><title>Institutional Review Board Statement</title><p>Not applicable.</p></notes><notes><title>Informed Consent Statement</title><p>Not applicable.</p></notes><notes notes-type="data-availability"><title>Data Availability Statement</title><p>The data presented in this study are openly available on Zenodo at 10.5281/zenodo.8090619, reference number [<xref rid="B6-sensors-23-07333" ref-type="bibr">6</xref>].</p></notes><notes notes-type="COI-statement"><title>Conflicts of Interest</title><p>The authors declare no conflict of interest.</p></notes><ref-list><title>References</title><ref id="B1-sensors-23-07333"><label>1.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>LoRa&#x000ae; and LoRaWAN&#x000ae;: A Technical Overview</article-title><comment>Available online: <ext-link xlink:href="https://lora-developers.semtech.com/uploads/documents/files/LoRa_and_LoRaWAN-A_Tech_Overview-Downloadable.pdf" ext-link-type="uri">https://lora-developers.semtech.com/uploads/documents/files/LoRa_and_LoRaWAN-A_Tech_Overview-Downloadable.pdf</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-07-12">(accessed on 12 July 2023)</date-in-citation></element-citation></ref><ref id="B2-sensors-23-07333"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Raza</surname><given-names>U.</given-names></name>
<name><surname>Kulkarni</surname><given-names>P.</given-names></name>
<name><surname>Sooriyabandara</surname><given-names>M.</given-names></name>
</person-group><article-title>Low Power Wide Area Networks: An Overview</article-title><source>IEEE Commun. Surv. Tutorials</source><year>2017</year><volume>19</volume><fpage>855</fpage><lpage>873</lpage><pub-id pub-id-type="doi">10.1109/COMST.2017.2652320</pub-id></element-citation></ref><ref id="B3-sensors-23-07333"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Haxhibeqiri</surname><given-names>J.</given-names></name>
<name><surname>De Poorter</surname><given-names>E.</given-names></name>
<name><surname>Moerman</surname><given-names>I.</given-names></name>
<name><surname>Hoebeke</surname><given-names>J.</given-names></name>
</person-group><article-title>A Survey of LoRaWAN for IoT: From Technology to Application</article-title><source>Sensors</source><year>2018</year><volume>18</volume><elocation-id>3995</elocation-id><pub-id pub-id-type="doi">10.3390/s18113995</pub-id><?supplied-pmid 30453524?><pub-id pub-id-type="pmid">30453524</pub-id></element-citation></ref><ref id="B4-sensors-23-07333"><label>4.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>AN1200.22 LoRa&#x02122; Modulation Basics</article-title><comment>Available online: <ext-link xlink:href="https://www.semtech.com/products/wireless-rf/lora-connect/sx1276" ext-link-type="uri">https://www.semtech.com/products/wireless-rf/lora-connect/sx1276</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B5-sensors-23-07333"><label>5.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>LoRa Alliance</collab>
</person-group><article-title>TS001-1.0.4 LoRaWAN&#x000ae; L2 1.0.4 Specification</article-title><comment>Available online: <ext-link xlink:href="https://lora-alliance.org/resource_hub/ts001-1-0-4-lorawan-l2-1-0-4-specification/" ext-link-type="uri">https://lora-alliance.org/resource_hub/ts001-1-0-4-lorawan-l2-1-0-4-specification/</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B6-sensors-23-07333"><label>6.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<name><surname>Povalac</surname><given-names>A.</given-names></name>
<name><surname>Kral</surname><given-names>J.</given-names></name>
<collab>LoRaWAN Traffic Analysis Dataset</collab>
</person-group><article-title>Version 2</article-title><source>Zenodo.</source><year>2023</year><comment>Available online: <ext-link xlink:href="https://zenodo.org/record/8090619" ext-link-type="uri">https://zenodo.org/record/8090619</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-08-16">(accessed on 16 August 2023)</date-in-citation></element-citation></ref><ref id="B7-sensors-23-07333"><label>7.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Bhatia</surname><given-names>L.</given-names></name>
<name><surname>Breza</surname><given-names>M.</given-names></name>
<name><surname>Marfievici</surname><given-names>R.</given-names></name>
<name><surname>McCann</surname><given-names>J.A.</given-names></name>
</person-group><article-title>LoED: The LoRaWAN at the Edge Dataset: Dataset</article-title><source>Proceedings of the Third Workshop on Data: Acquisition To Analysis</source><conf-loc>New York, NY, USA</conf-loc><conf-date>16&#x02013;19 November 2020</conf-date><comment>DATA &#x02019;20</comment><fpage>7</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1145/3419016.3431491</pub-id></element-citation></ref><ref id="B8-sensors-23-07333"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Aernouts</surname><given-names>M.</given-names></name>
<name><surname>Berkvens</surname><given-names>R.</given-names></name>
<name><surname>Van Vlaenderen</surname><given-names>K.</given-names></name>
<name><surname>Weyn</surname><given-names>M.</given-names></name>
</person-group><article-title>Sigfox and LoRaWAN Datasets for Fingerprint Localization in Large Urban and Rural Areas</article-title><source>Data</source><year>2018</year><volume>3</volume><elocation-id>13</elocation-id><pub-id pub-id-type="doi">10.3390/data3020013</pub-id></element-citation></ref><ref id="B9-sensors-23-07333"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Blenn</surname><given-names>N.</given-names></name>
<name><surname>Kuipers</surname><given-names>F.</given-names></name>
</person-group><article-title>LoRaWAN in the Wild: Measurements from The Things Network</article-title><source>arXiv</source><year>2017</year><pub-id pub-id-type="doi">10.48550/arXiv.1706.03086</pub-id><pub-id pub-id-type="arxiv">1706.03086</pub-id></element-citation></ref><ref id="B10-sensors-23-07333"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Choi</surname><given-names>K.N.</given-names></name>
<name><surname>Kolamunna</surname><given-names>H.</given-names></name>
<name><surname>Uyanwatta</surname><given-names>A.</given-names></name>
<name><surname>Thilakarathna</surname><given-names>K.</given-names></name>
<name><surname>Seneviratne</surname><given-names>S.</given-names></name>
<name><surname>Holz</surname><given-names>R.</given-names></name>
<name><surname>Hassan</surname><given-names>M.</given-names></name>
<name><surname>Zomaya</surname><given-names>A.Y.</given-names></name>
</person-group><article-title>LoRadar: LoRa Sensor Network Monitoring through Passive Packet Sniffing</article-title><source>SIGCOMM Comput. Commun. Rev.</source><year>2020</year><volume>50</volume><fpage>10</fpage><lpage>24</lpage><pub-id pub-id-type="doi">10.1145/3431832.3431835</pub-id></element-citation></ref><ref id="B11-sensors-23-07333"><label>11.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<name><surname>Povalac</surname><given-names>A.</given-names></name>
</person-group><article-title>LoRaWAN Traffic Analysis Tools</article-title><comment>Available online: <ext-link xlink:href="https://github.com/alpov/lorawan-sniffer" ext-link-type="uri">https://github.com/alpov/lorawan-sniffer</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-10">(accessed on 10 May 2023)</date-in-citation></element-citation></ref><ref id="B12-sensors-23-07333"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Spadaccino</surname><given-names>P.</given-names></name>
<name><surname>Crin&#x000f3;</surname><given-names>F.G.</given-names></name>
<name><surname>Cuomo</surname><given-names>F.</given-names></name>
</person-group><article-title>LoRaWAN Behaviour Analysis through Dataset Traffic Investigation</article-title><source>Sensors</source><year>2022</year><volume>22</volume><elocation-id>2470</elocation-id><pub-id pub-id-type="doi">10.3390/s22072470</pub-id><?supplied-pmid 35408085?><pub-id pub-id-type="pmid">35408085</pub-id></element-citation></ref><ref id="B13-sensors-23-07333"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ruotsalainen</surname><given-names>H.</given-names></name>
<name><surname>Shen</surname><given-names>G.</given-names></name>
<name><surname>Zhang</surname><given-names>J.</given-names></name>
<name><surname>Fujdiak</surname><given-names>R.</given-names></name>
</person-group><article-title>LoRaWAN Physical Layer-Based Attacks and Countermeasures, A Review</article-title><source>Sensors</source><year>2022</year><volume>22</volume><elocation-id>3127</elocation-id><pub-id pub-id-type="doi">10.3390/s22093127</pub-id><?supplied-pmid 35590817?><pub-id pub-id-type="pmid">35590817</pub-id></element-citation></ref><ref id="B14-sensors-23-07333"><label>14.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<name><surname>Broxson</surname><given-names>J.</given-names></name>
</person-group><article-title>Feather TFT LoRa Sniffer</article-title><comment>Available online: <ext-link xlink:href="https://github.com/ImprobableStudios/Feather_TFT_LoRa_Sniffer" ext-link-type="uri">https://github.com/ImprobableStudios/Feather_TFT_LoRa_Sniffer</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B15-sensors-23-07333"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bravo-Montoya</surname><given-names>A.F.</given-names></name>
<name><surname>Rond&#x000f3;n-Sanabria</surname><given-names>J.S.</given-names></name>
<name><surname>Gaona-Garc&#x000ed;a</surname><given-names>E.E.</given-names></name>
</person-group><article-title>Development and Testing of a Real-Time LoRawan Sniffer Based on GNU-Radio</article-title><source>TecnoL&#x000f3;gicas</source><year>2019</year><volume>22</volume><fpage>130</fpage><lpage>139</lpage><pub-id pub-id-type="doi">10.22430/22565337.1491</pub-id></element-citation></ref><ref id="B16-sensors-23-07333"><label>16.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Robyns</surname><given-names>P.</given-names></name>
<name><surname>Quax</surname><given-names>P.</given-names></name>
<name><surname>Lamotte</surname><given-names>W.</given-names></name>
<name><surname>Thenaers</surname><given-names>W.</given-names></name>
</person-group><article-title>A Multi-Channel Software Decoder for the LoRa Modulation Scheme</article-title><source>Proceedings of the 3rd International Conference on Internet of Things, Big Data and Security</source><conf-loc>Funchal, Portugal</conf-loc><conf-date>19&#x02013;21 March 2018</conf-date><fpage>41</fpage><lpage>51</lpage><pub-id pub-id-type="doi">10.5220/0006668400410051</pub-id></element-citation></ref><ref id="B17-sensors-23-07333"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Marquet</surname><given-names>A.</given-names></name>
<name><surname>Montavont</surname><given-names>N.</given-names></name>
<name><surname>Papadopoulos</surname><given-names>G.Z.</given-names></name>
</person-group><article-title>Towards an SDR implementation of LoRa: Reverse-engineering, demodulation strategies and assessment over Rayleigh channel</article-title><source>Comput. Commun.</source><year>2020</year><volume>153</volume><fpage>595</fpage><lpage>605</lpage><pub-id pub-id-type="doi">10.1016/j.comcom.2020.02.034</pub-id></element-citation></ref><ref id="B18-sensors-23-07333"><label>18.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Tapparel</surname><given-names>J.</given-names></name>
<name><surname>Afisiadis</surname><given-names>O.</given-names></name>
<name><surname>Mayoraz</surname><given-names>P.</given-names></name>
<name><surname>Balatsoukas-Stimming</surname><given-names>A.</given-names></name>
<name><surname>Burg</surname><given-names>A.</given-names></name>
</person-group><article-title>An Open-Source LoRa Physical Layer Prototype on GNU Radio</article-title><source>Proceedings of the 2020 IEEE 21st International Workshop on Signal Processing Advances in Wireless Communications (SPAWC)</source><conf-loc>Atlanta, GA, USA</conf-loc><conf-date>26&#x02013;29 May 2020</conf-date><fpage>1</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1109/SPAWC48557.2020.9154273</pub-id></element-citation></ref><ref id="B19-sensors-23-07333"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Silva</surname><given-names>F.S.D.</given-names></name>
<name><surname>Neto</surname><given-names>E.P.</given-names></name>
<name><surname>Oliveira</surname><given-names>H.</given-names></name>
<name><surname>Ros&#x000e1;rio</surname><given-names>D.</given-names></name>
<name><surname>Cerqueira</surname><given-names>E.</given-names></name>
<name><surname>Both</surname><given-names>C.</given-names></name>
<name><surname>Zeadally</surname><given-names>S.</given-names></name>
<name><surname>Neto</surname><given-names>A.V.</given-names></name>
</person-group><article-title>A Survey on Long-Range Wide-Area Network Technology Optimizations</article-title><source>IEEE Access</source><year>2021</year><volume>9</volume><fpage>106079</fpage><lpage>106106</lpage><pub-id pub-id-type="doi">10.1109/ACCESS.2021.3079095</pub-id></element-citation></ref><ref id="B20-sensors-23-07333"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Marais</surname><given-names>J.M.</given-names></name>
<name><surname>Malekian</surname><given-names>R.</given-names></name>
<name><surname>Abu-Mahfouz</surname><given-names>A.M.</given-names></name>
</person-group><article-title>Evaluating the LoRaWAN Protocol Using a Permanent Outdoor Testbed</article-title><source>IEEE Sens. J.</source><year>2019</year><volume>19</volume><fpage>4726</fpage><lpage>4733</lpage><pub-id pub-id-type="doi">10.1109/JSEN.2019.2900735</pub-id></element-citation></ref><ref id="B21-sensors-23-07333"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fujdiak</surname><given-names>R.</given-names></name>
<name><surname>Mikhaylov</surname><given-names>K.</given-names></name>
<name><surname>Pospisil</surname><given-names>J.</given-names></name>
<name><surname>Povalac</surname><given-names>A.</given-names></name>
<name><surname>Misurec</surname><given-names>J.</given-names></name>
</person-group><article-title>Insights into the Issue of Deploying a Private LoRaWAN</article-title><source>Sensors</source><year>2022</year><volume>22</volume><elocation-id>2024</elocation-id><pub-id pub-id-type="doi">10.3390/s22052042</pub-id><?supplied-pmid 35271189?><pub-id pub-id-type="pmid">35271171</pub-id></element-citation></ref><ref id="B22-sensors-23-07333"><label>22.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Ramirez</surname><given-names>C.G.</given-names></name>
<name><surname>Dyussenova</surname><given-names>A.</given-names></name>
<name><surname>Sergeyev</surname><given-names>A.</given-names></name>
<name><surname>Iannucci</surname><given-names>B.</given-names></name>
</person-group><article-title>LongShoT: Long-Range Synchronization of Time</article-title><source>Proceedings of the 2019 18th ACM/IEEE International Conference on Information Processing in Sensor Networks (IPSN)</source><conf-loc>Montreal, QC, Canada</conf-loc><conf-date>16&#x02013;18 April 2019</conf-date><fpage>289</fpage><lpage>300</lpage><pub-id pub-id-type="doi">10.1145/3302506.3310408</pub-id></element-citation></ref><ref id="B23-sensors-23-07333"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Rizzi</surname><given-names>M.</given-names></name>
<name><surname>Depari</surname><given-names>A.</given-names></name>
<name><surname>Ferrari</surname><given-names>P.</given-names></name>
<name><surname>Flammini</surname><given-names>A.</given-names></name>
<name><surname>Rinaldi</surname><given-names>S.</given-names></name>
<name><surname>Sisinni</surname><given-names>E.</given-names></name>
</person-group><article-title>Synchronization Uncertainty Versus Power Efficiency in LoRaWAN Networks</article-title><source>IEEE Trans. Instrum. Meas.</source><year>2019</year><volume>68</volume><fpage>1101</fpage><lpage>1111</lpage><pub-id pub-id-type="doi">10.1109/TIM.2018.2859639</pub-id></element-citation></ref><ref id="B24-sensors-23-07333"><label>24.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>LoRa Alliance</collab>
</person-group><article-title>RP002-1.0.3 LoRaWAN&#x000ae; Regional Parameters</article-title><comment>Available online: <ext-link xlink:href="https://lora-alliance.org/resource_hub/rp2-1-0-3-lorawan-regional-parameters/" ext-link-type="uri">https://lora-alliance.org/resource_hub/rp2-1-0-3-lorawan-regional-parameters/</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B25-sensors-23-07333"><label>25.</label><element-citation publication-type="webpage"><article-title>ETSI EN 300 220-2 V3.2.1: Short Range Devices (SRD) Operating in the Frequency Range 25 MHz to 1 000 MHz; Part 2: Harmonised Standard for Access to Radio Spectrum for Non Specific Radio Equipment</article-title><comment>Available online: <ext-link xlink:href="https://www.etsi.org/deliver/etsi_en/300200_300299/30022002/03.02.01_60/en_30022002v030201p.pdf" ext-link-type="uri">https://www.etsi.org/deliver/etsi_en/300200_300299/30022002/03.02.01_60/en_30022002v030201p.pdf</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-04">(accessed on 4 May 2023)</date-in-citation></element-citation></ref><ref id="B26-sensors-23-07333"><label>26.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>The Things Network</collab>
</person-group><article-title>LoRaWAN Frequency Plans</article-title><comment>Available online: <ext-link xlink:href="https://www.thethingsnetwork.org/docs/lorawan/frequency-plans/" ext-link-type="uri">https://www.thethingsnetwork.org/docs/lorawan/frequency-plans/</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B27-sensors-23-07333"><label>27.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>IMST GmbH</collab>
</person-group><article-title>WiMOD iC880A Datasheet</article-title><comment>Available online: <ext-link xlink:href="https://wireless-solutions.de/downloadfile/ic880a-spi-documents/" ext-link-type="uri">https://wireless-solutions.de/downloadfile/ic880a-spi-documents/</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B28-sensors-23-07333"><label>28.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>SX1301: LoRa Core&#x02122; Digital Baseband Chip for Outdoor LoRaWAN&#x000ae; Network Macro Gateways</article-title><comment>Available online: <ext-link xlink:href="https://www.semtech.com/products/wireless-rf/lora-core/sx1301" ext-link-type="uri">https://www.semtech.com/products/wireless-rf/lora-core/sx1301</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B29-sensors-23-07333"><label>29.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>SX1257: LoRa Core&#x02122; Low Power Digital I and Q RF Multi-PHY Mode Analog Front End 860-1000MHz</article-title><comment>Available online: <ext-link xlink:href="https://www.semtech.com/products/wireless-rf/lora-core/sx1257" ext-link-type="uri">https://www.semtech.com/products/wireless-rf/lora-core/sx1257</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B30-sensors-23-07333"><label>30.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>LoRa Gateway Project</article-title><comment>Available online: <ext-link xlink:href="https://github.com/Lora-net/lora_gateway" ext-link-type="uri">https://github.com/Lora-net/lora_gateway</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B31-sensors-23-07333"><label>31.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>LoRa Network Packet Forwarder Project</article-title><comment>Available online: <ext-link xlink:href="https://github.com/Lora-net/packet_forwarder" ext-link-type="uri">https://github.com/Lora-net/packet_forwarder</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B32-sensors-23-07333"><label>32.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<name><surname>De Jong</surname><given-names>E.</given-names></name>
</person-group><article-title>LoRaTap: Encapsulation Format to be Used to Store LoRa Traffic in Pcap Files</article-title><comment>Available online: <ext-link xlink:href="https://github.com/eriknl/LoRaTap" ext-link-type="uri">https://github.com/eriknl/LoRaTap</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B33-sensors-23-07333"><label>33.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Wireshark</collab>
</person-group><article-title>Automated Builds</article-title><comment>Available online: <ext-link xlink:href="https://www.wireshark.org/download/automated/win64/" ext-link-type="uri">https://www.wireshark.org/download/automated/win64/</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-06-19">(accessed on 19 June 2023)</date-in-citation></element-citation></ref><ref id="B34-sensors-23-07333"><label>34.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>LoRa Alliance</collab>
</person-group><article-title>TS002-1.1.0 LoRaWAN Backend Interfaces Specification</article-title><comment>Available online: <ext-link xlink:href="https://lora-alliance.org/resource_hub/ts002-110-lorawan-backend-interfaces/" ext-link-type="uri">https://lora-alliance.org/resource_hub/ts002-110-lorawan-backend-interfaces/</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B35-sensors-23-07333"><label>35.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>LoRa Alliance</collab>
</person-group><article-title>NetID Allocation</article-title><comment>Available online: <ext-link xlink:href="https://lora-alliance.org/wp-content/uploads/2022/06/LoRa-Alliance-NetID-Allocation-1.pdf" ext-link-type="uri">https://lora-alliance.org/wp-content/uploads/2022/06/LoRa-Alliance-NetID-Allocation-1.pdf</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B36-sensors-23-07333"><label>36.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>IEEE Standards Association</collab>
</person-group><article-title>Guidelines for Use of EUI, OUI, and CID</article-title><comment>Available online: <ext-link xlink:href="https://standards.ieee.org/wp-content/uploads/import/documents/tutorials/eui.pdf" ext-link-type="uri">https://standards.ieee.org/wp-content/uploads/import/documents/tutorials/eui.pdf</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-03">(accessed on 3 May 2023)</date-in-citation></element-citation></ref><ref id="B37-sensors-23-07333"><label>37.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<name><surname>Hu</surname><given-names>Y.H.</given-names></name>
</person-group><article-title>LoRa Air-Time Calculator</article-title><comment>Available online: <ext-link xlink:href="https://github.com/ifTNT/lora-air-time" ext-link-type="uri">https://github.com/ifTNT/lora-air-time</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B38-sensors-23-07333"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Lewandowski</surname><given-names>W.</given-names></name>
<name><surname>Arias</surname><given-names>E.F.</given-names></name>
</person-group><article-title>GNSS times and UTC</article-title><source>Metrologia</source><year>2011</year><volume>48</volume><fpage>S219</fpage><pub-id pub-id-type="doi">10.1088/0026-1394/48/4/S14</pub-id></element-citation></ref><ref id="B39-sensors-23-07333"><label>39.</label><element-citation publication-type="gov"><person-group person-group-type="author">
<collab>NIST</collab>
</person-group><article-title>Leap Second and UT1-UTC Information</article-title><comment>Available online: <ext-link xlink:href="https://www.nist.gov/pml/time-and-frequency-division/time-realization/leap-seconds" ext-link-type="uri">https://www.nist.gov/pml/time-and-frequency-division/time-realization/leap-seconds</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B40-sensors-23-07333"><label>40.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>An In-Depth Look at LoRaWAN&#x000ae; Class B Devices</article-title><comment>Available online: <ext-link xlink:href="https://lora-developers.semtech.com/uploads/documents/files/LoRaWAN_Class_B_Devices_In_Depth_Downloadable.pdf" ext-link-type="uri">https://lora-developers.semtech.com/uploads/documents/files/LoRaWAN_Class_B_Devices_In_Depth_Downloadable.pdf</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-26">(accessed on 26 May 2023)</date-in-citation></element-citation></ref><ref id="B41-sensors-23-07333"><label>41.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>SX1272: Long Range, Low Power RF Transceiver 860-1000MHz with LoRa&#x000ae; Technology</article-title><comment>Available online: <ext-link xlink:href="https://www.semtech.com/products/wireless-rf/lora-connect/sx1272" ext-link-type="uri">https://www.semtech.com/products/wireless-rf/lora-connect/sx1272</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B42-sensors-23-07333"><label>42.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Milesight</collab>
</person-group><article-title>3D ToF People Counting Sensor User Manual</article-title><comment>Available online: <ext-link xlink:href="https://www.milesight.com/static/file/en/download/datasheet/3d-tof/Milesight-3D-ToF-People-Counting-Sensor-User-Manual-en.pdf" ext-link-type="uri">https://www.milesight.com/static/file/en/download/datasheet/3d-tof/Milesight-3D-ToF-People-Counting-Sensor-User-Manual-en.pdf</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-04">(accessed on 4 May 2023)</date-in-citation></element-citation></ref><ref id="B43-sensors-23-07333"><label>43.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>Semtech</collab>
</person-group><article-title>SX1261: LoRa Connect&#x02122; Long Range Low Power LoRa&#x000ae; RF Transceiver +15 dBm, Global Frequency Coverage</article-title><comment>Available online: <ext-link xlink:href="https://www.semtech.com/products/wireless-rf/lora-connect/sx1261" ext-link-type="uri">https://www.semtech.com/products/wireless-rf/lora-connect/sx1261</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B44-sensors-23-07333"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Haxhibeqiri</surname><given-names>J.</given-names></name>
<name><surname>Van den Abeele</surname><given-names>F.</given-names></name>
<name><surname>Moerman</surname><given-names>I.</given-names></name>
<name><surname>Hoebeke</surname><given-names>J.</given-names></name>
</person-group><article-title>LoRa Scalability: A Simulation Model Based on Interference Measurements</article-title><source>Sensors</source><year>2017</year><volume>17</volume><elocation-id>1193</elocation-id><pub-id pub-id-type="doi">10.3390/s17061193</pub-id><?supplied-pmid 28545239?><pub-id pub-id-type="pmid">28545239</pub-id></element-citation></ref><ref id="B45-sensors-23-07333"><label>45.</label><element-citation publication-type="patent"><person-group person-group-type="author">
<name><surname>Seller</surname><given-names>O.B.</given-names></name>
<name><surname>Sornin</surname><given-names>N.</given-names></name>
</person-group><article-title>Low Power Long Range</article-title><source>Transmitter. Patent</source><patent>US9252834B2</patent><day>2</day><month>February</month><year>2016</year></element-citation></ref><ref id="B46-sensors-23-07333"><label>46.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<collab>ATCZ175 InterOP</collab>
</person-group><article-title>Private/Public-Syncword Crosstalk</article-title><comment>Available online: <ext-link xlink:href="https://www.interreg-interop.eu/results/lorawan/privatepublic_syncword_crosstalk/index.html" ext-link-type="uri">https://www.interreg-interop.eu/results/lorawan/privatepublic_syncword_crosstalk/index.html</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-05-02">(accessed on 2 May 2023)</date-in-citation></element-citation></ref><ref id="B47-sensors-23-07333"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Engeler</surname><given-names>D.</given-names></name>
</person-group><article-title>Performance analysis and receiver architectures of DCF77 radio-controlled clocks</article-title><source>IEEE Trans. Ultrason. Ferroelectr. Freq. Control</source><year>2012</year><volume>59</volume><fpage>869</fpage><lpage>884</lpage><pub-id pub-id-type="doi">10.1109/TUFFC.2012.2272</pub-id><?supplied-pmid 22622972?><pub-id pub-id-type="pmid">22622972</pub-id></element-citation></ref></ref-list></back><floats-group><fig position="float" id="sensors-23-07333-f001"><label>Figure 1</label><caption><p>LoRaWAN EU868 channels and sniffer front ends.</p></caption><graphic xlink:href="sensors-23-07333-g001" position="float"/></fig><fig position="float" id="sensors-23-07333-f002"><label>Figure 2</label><caption><p>Block diagram of the developed LoRaWAN sniffer.</p></caption><graphic xlink:href="sensors-23-07333-g002" position="float"/></fig><fig position="float" id="sensors-23-07333-f003"><label>Figure 3</label><caption><p>Photo of the LoRaWAN sniffer internal hardware.</p></caption><graphic xlink:href="sensors-23-07333-g003" position="float"/></fig><fig position="float" id="sensors-23-07333-f004"><label>Figure 4</label><caption><p>Distribution of LoRaWAN packets for individual receive chains for packets with valid, invalid, and missing CRC in: (<bold>a</bold>) Liege dataset; (<bold>b</bold>) Graz dataset; (<bold>c</bold>) Vienna dataset; (<bold>d</bold>) Brno dataset.</p></caption><graphic xlink:href="sensors-23-07333-g004" position="float"/></fig><fig position="float" id="sensors-23-07333-f005"><label>Figure 5</label><caption><p>LoRaWAN message types: Join Request, Join Accept, Unconfirmed/Confirmed Data Up/Down, RFU, Proprietary, and Class-B Beacon in: (<bold>a</bold>) Vienna dataset; (<bold>b</bold>) Brno dataset.</p></caption><graphic xlink:href="sensors-23-07333-g005" position="float"/></fig><fig position="float" id="sensors-23-07333-f006"><label>Figure 6</label><caption><p>Parameters of captured LoRaWAN messages in the Vienna dataset: (<bold>a</bold>) Spreading factor; (<bold>b</bold>) Coding ratio; (<bold>c</bold>) Channel occupation; (<bold>d</bold>) Payload length; (<bold>e</bold>) Received Signal Strength Indicator (RSSI); (<bold>f</bold>) Signal-to-Noise Ratio (SNR).</p></caption><graphic xlink:href="sensors-23-07333-g006" position="float"/></fig><fig position="float" id="sensors-23-07333-f007"><label>Figure 7</label><caption><p>LoRaWAN sniffer placement and identified Class-B gateways beaconing its position in: (<bold>a</bold>) Liege; (<bold>b</bold>) Vienna. Map source: &#x0201c;Mapy.cz&#x0201d;.</p></caption><graphic xlink:href="sensors-23-07333-g007" position="float"/></fig><fig position="float" id="sensors-23-07333-f008"><label>Figure 8</label><caption><p>Time offset and corresponding distance between the GNSS reference and the received Class-B beacons.</p></caption><graphic xlink:href="sensors-23-07333-g008" position="float"/></fig><fig position="float" id="sensors-23-07333-f009"><label>Figure 9</label><caption><p>Difference between the actual reception time and the reported time in invalid Class-B beacons from the Vienna dataset.</p></caption><graphic xlink:href="sensors-23-07333-g009" position="float"/></fig><fig position="float" id="sensors-23-07333-f010"><label>Figure 10</label><caption><p>Channel utilization by LoRaWAN packets in (<bold>a</bold>) Vienna dataset; (<bold>b</bold>) Brno dataset.</p></caption><graphic xlink:href="sensors-23-07333-g010" position="float"/></fig><fig position="float" id="sensors-23-07333-f011"><label>Figure 11</label><caption><p>Duplicate packets with different chirp polarities in the Brno dataset.</p></caption><graphic xlink:href="sensors-23-07333-g011" position="float"/></fig><table-wrap position="float" id="sensors-23-07333-t001"><object-id pub-id-type="pii">sensors-23-07333-t001_Table 1</object-id><label>Table 1</label><caption><p>LoRaWAN EU868 frequency plan with possible combinations of LoRa parameters&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>,<xref rid="B25-sensors-23-07333" ref-type="bibr">25</xref>,<xref rid="B26-sensors-23-07333" ref-type="bibr">26</xref>].</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Transmission Kind</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Frequency (MHz)</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Spreading Factor</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Uplink Signal Polarity</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Downlink Signal Polarity</th></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 1</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm16" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>868.5</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mn>0.4</mml:mn><mml:mo>=</mml:mo><mml:mn>868.1</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 2</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm17" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>868.5</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mn>0.2</mml:mn><mml:mo>=</mml:mo><mml:mn>868.3</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 3</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm18" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>868.5</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 4</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm19" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>867.5</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mn>0.4</mml:mn><mml:mo>=</mml:mo><mml:mn>867.1</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 5</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm20" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>867.5</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mn>0.2</mml:mn><mml:mo>=</mml:mo><mml:mn>867.3</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 6</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm21" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>867.5</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 7</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm22" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>867.5</mml:mn><mml:mo>+</mml:mo><mml:mn>0.2</mml:mn><mml:mo>=</mml:mo><mml:mn>867.7</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 8</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm23" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>867.5</mml:mn><mml:mo>+</mml:mo><mml:mn>0.4</mml:mn><mml:mo>=</mml:mo><mml:mn>867.9</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12</td><td align="center" valign="middle" rowspan="1" colspan="1">non-inverted</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">RX2</td><td align="center" valign="middle" rowspan="1" colspan="1">
<inline-formula>
<mml:math id="mm24" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>869.525</mml:mn></mml:mrow></mml:mrow></mml:math>
</inline-formula>
</td><td align="center" valign="middle" rowspan="1" colspan="1">SF7&#x02013;SF12 <sup>1</sup></td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">inverted</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Class-B beacon <sup>2</sup></td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">869.525</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">SF9</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">non-inverted</td></tr></tbody></table><table-wrap-foot><fn><p><sup>1</sup>&#x000a0;SF12 for the LoRaWAN standard, SF9 for The Things Network&#x000a0;[<xref rid="B26-sensors-23-07333" ref-type="bibr">26</xref>]. The sniffer supports all spreading factors. <sup>2</sup>&#x000a0;Class-B beacons use implicit header mode with specific settings&#x000a0;[<xref rid="B24-sensors-23-07333" ref-type="bibr">24</xref>].</p></fn></table-wrap-foot></table-wrap><table-wrap position="float" id="sensors-23-07333-t002"><object-id pub-id-type="pii">sensors-23-07333-t002_Table 2</object-id><label>Table 2</label><caption><p>Roles of iC880A modules in the LoRaWAN sniffer.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Concentrator</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Receives on</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IF0&#x02013;IF7 Paths</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IF8 Path</th></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">GW #1</td><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 1&#x02013;8</td><td align="center" valign="middle" rowspan="1" colspan="1">downlink</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">GW #2</td><td align="center" valign="middle" rowspan="1" colspan="1">RX1 channel 1&#x02013;8</td><td align="center" valign="middle" rowspan="1" colspan="1">uplink</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">GW #3</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">RX2</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">downlink (IF0 only)</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Class-B beacon</td></tr></tbody></table></table-wrap><table-wrap position="float" id="sensors-23-07333-t003"><object-id pub-id-type="pii">sensors-23-07333-t003_Table 3</object-id><label>Table 3</label><caption><p>Dataset details.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Location</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Geographic Coordinates</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Sniffer Placement</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Capture Interval</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Days</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Average Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Valid LoRaWAN Packets per Day</th></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">Liege (Belgium)</td><td align="center" valign="middle" rowspan="1" colspan="1">50.66445&#x000b0;&#x000a0;N 5.59276&#x000b0;&#x000a0;E</td><td align="center" valign="middle" rowspan="1" colspan="1">Roof of a residential building in a suburb area; limited view.</td><td align="center" valign="middle" rowspan="1" colspan="1">25 August 2022&#x02013;19 September 2022</td><td align="center" valign="middle" rowspan="1" colspan="1">17.8</td><td align="center" valign="middle" rowspan="1" colspan="1">14,088</td><td align="center" valign="middle" rowspan="1" colspan="1">6609</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">Graz (Austria)</td><td align="center" valign="middle" rowspan="1" colspan="1">47.07049&#x000b0;&#x000a0;N 15.44506&#x000b0;&#x000a0;E</td><td align="center" valign="middle" rowspan="1" colspan="1">Enclosed balcony of a historical building in the city center; indoor.</td><td align="center" valign="middle" rowspan="1" colspan="1">26 October 2022&#x02013;29 November 2022</td><td align="center" valign="middle" rowspan="1" colspan="1">26.3</td><td align="center" valign="middle" rowspan="1" colspan="1">6225</td><td align="center" valign="middle" rowspan="1" colspan="1">3215</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">Vienna (Austria)</td><td align="center" valign="middle" rowspan="1" colspan="1">48.19666&#x000b0;&#x000a0;N 16.37101&#x000b0;&#x000a0;E</td><td align="center" valign="middle" rowspan="1" colspan="1">Roof of a university building in the city center; clear view.</td><td align="center" valign="middle" rowspan="1" colspan="1">1 December 2022&#x02013;4 January 2023</td><td align="center" valign="middle" rowspan="1" colspan="1">34.1</td><td align="center" valign="middle" rowspan="1" colspan="1">72,892</td><td align="center" valign="middle" rowspan="1" colspan="1">58,330</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Brno (Czechia)</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">49.22685&#x000b0;&#x000a0;N 16.57536&#x000b0;&#x000a0;E</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Roof of a university building in a suburb area; clear view.</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">16 February 2023&#x02013;30 March 2023</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">42.0</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">46,467</td><td align="left" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">30,937</td></tr></tbody></table></table-wrap><table-wrap position="float" id="sensors-23-07333-t004"><object-id pub-id-type="pii">sensors-23-07333-t004_Table 4</object-id><label>Table 4</label><caption><p>Support for ADR and Class-B features along with the occurrence of payload CRC in downlink messages found in captured LoRaWAN messages.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Location</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Gateway Packets with ADR Support</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">End Device Packets with ADR Support</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">End Device Packets with Class-B Support</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Downlink Messages with Payload CRC</th></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">Liege (Belgium)</td><td align="center" valign="middle" rowspan="1" colspan="1">3.9%</td><td align="center" valign="middle" rowspan="1" colspan="1">79.8%</td><td align="center" valign="middle" rowspan="1" colspan="1">2.3%</td><td align="center" valign="middle" rowspan="1" colspan="1">1.2%</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">Graz (Austria)</td><td align="center" valign="middle" rowspan="1" colspan="1">99.7%</td><td align="center" valign="middle" rowspan="1" colspan="1">57.4%</td><td align="center" valign="middle" rowspan="1" colspan="1">34.1%</td><td align="center" valign="middle" rowspan="1" colspan="1">99.7%</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">Vienna (Austria)</td><td align="center" valign="middle" rowspan="1" colspan="1">79.2%</td><td align="center" valign="middle" rowspan="1" colspan="1">83.6%</td><td align="center" valign="middle" rowspan="1" colspan="1">1.4%</td><td align="center" valign="middle" rowspan="1" colspan="1">81.9%</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Brno (Czechia)</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">96.6%</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">86.6%</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.0%</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">99.3%</td></tr></tbody></table></table-wrap><table-wrap position="float" id="sensors-23-07333-t005"><object-id pub-id-type="pii">sensors-23-07333-t005_Table 5</object-id><label>Table 5</label><caption><p>Major network operators identified from the captured LoRaWAN traffic.</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2" align="left" valign="middle" style="border-top:solid thin;border-bottom:solid thin" colspan="1">Network Operator</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Liege</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Graz</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Vienna</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Brno</th></tr><tr><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1"> % of Total</th></tr></thead><tbody><tr><td align="left" valign="middle" rowspan="1" colspan="1">Private/experimental nodes</td><td align="center" valign="middle" rowspan="1" colspan="1">1312</td><td align="center" valign="middle" rowspan="1" colspan="1">21.6</td><td align="center" valign="middle" rowspan="1" colspan="1">3152</td><td align="center" valign="middle" rowspan="1" colspan="1">99.1</td><td align="center" valign="middle" rowspan="1" colspan="1">33,886</td><td align="center" valign="middle" rowspan="1" colspan="1">66.1</td><td align="center" valign="middle" rowspan="1" colspan="1">19,759</td><td align="center" valign="middle" rowspan="1" colspan="1">72.8</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Minol ZENNER Connect</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">6138</td><td align="center" valign="middle" rowspan="1" colspan="1">12.0</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">The Things Network</td><td align="center" valign="middle" rowspan="1" colspan="1">59</td><td align="center" valign="middle" rowspan="1" colspan="1">1.0</td><td align="center" valign="middle" rowspan="1" colspan="1">9</td><td align="center" valign="middle" rowspan="1" colspan="1">0.3</td><td align="center" valign="middle" rowspan="1" colspan="1">4600</td><td align="center" valign="middle" rowspan="1" colspan="1">9.0</td><td align="center" valign="middle" rowspan="1" colspan="1">2194</td><td align="center" valign="middle" rowspan="1" colspan="1">8.1</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Proximus</td><td align="center" valign="middle" rowspan="1" colspan="1">2751</td><td align="center" valign="middle" rowspan="1" colspan="1">45.3</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">4158</td><td align="center" valign="middle" rowspan="1" colspan="1">8.1</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Actility</td><td align="center" valign="middle" rowspan="1" colspan="1">972</td><td align="center" valign="middle" rowspan="1" colspan="1">16.0</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">KPN</td><td align="center" valign="middle" rowspan="1" colspan="1">757</td><td align="center" valign="middle" rowspan="1" colspan="1">12.5</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Orbiwise</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">412</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Other/unassigned</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">218</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">3.6</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">19</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.6</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">2070</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">4.0</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">5193</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">19.1</td></tr></tbody></table></table-wrap><table-wrap position="float" id="sensors-23-07333-t006"><object-id pub-id-type="pii">sensors-23-07333-t006_Table 6</object-id><label>Table 6</label><caption><p>Major end device manufacturers identified from the captured LoRaWAN traffic.</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2" align="left" valign="middle" style="border-top:solid thin;border-bottom:solid thin" colspan="1">End Device Manufacturer</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Liege</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Graz</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Vienna</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Brno</th></tr><tr><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th></tr></thead><tbody><tr><td align="left" valign="middle" rowspan="1" colspan="1">DZG Metering</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">603</td><td align="center" valign="middle" rowspan="1" colspan="1">38.6</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">RisingHF</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">288</td><td align="center" valign="middle" rowspan="1" colspan="1">77.7</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Milesight</td><td align="center" valign="middle" rowspan="1" colspan="1">13</td><td align="center" valign="middle" rowspan="1" colspan="1">26.2</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x0003c;1</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7</td><td align="center" valign="middle" rowspan="1" colspan="1">106</td><td align="center" valign="middle" rowspan="1" colspan="1">6.8</td><td align="center" valign="middle" rowspan="1" colspan="1">7</td><td align="center" valign="middle" rowspan="1" colspan="1">1.9</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Microchip Technology</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">93</td><td align="center" valign="middle" rowspan="1" colspan="1">6.0</td><td align="center" valign="middle" rowspan="1" colspan="1">6</td><td align="center" valign="middle" rowspan="1" colspan="1">1.6</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Invoxia</td><td align="center" valign="middle" rowspan="1" colspan="1">1</td><td align="center" valign="middle" rowspan="1" colspan="1">1.9</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">90</td><td align="center" valign="middle" rowspan="1" colspan="1">5.8</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Laird Connectivity</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">74</td><td align="center" valign="middle" rowspan="1" colspan="1">4.7</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Adeunis RF</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">45</td><td align="center" valign="middle" rowspan="1" colspan="1">2.9</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x0003c;1</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x0223c;0</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">MClimate</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">2</td><td align="center" valign="middle" rowspan="1" colspan="1">0.1</td><td align="center" valign="middle" rowspan="1" colspan="1">42</td><td align="center" valign="middle" rowspan="1" colspan="1">11.4</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Holley Metering</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">30</td><td align="center" valign="middle" rowspan="1" colspan="1">1.9</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">ELSYS</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">23</td><td align="center" valign="middle" rowspan="1" colspan="1">1.5</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Dragino Technology</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">17</td><td align="center" valign="middle" rowspan="1" colspan="1">1.1</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x0003c;1</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x0223c;0</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Seeed Technology</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">12</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Viloc</td><td align="center" valign="middle" rowspan="1" colspan="1">11</td><td align="center" valign="middle" rowspan="1" colspan="1">21.8</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Homerider Systems</td><td align="center" valign="middle" rowspan="1" colspan="1">10</td><td align="center" valign="middle" rowspan="1" colspan="1">18.8</td><td align="center" valign="middle" rowspan="1" colspan="1">5</td><td align="center" valign="middle" rowspan="1" colspan="1">43.0</td><td align="center" valign="middle" rowspan="1" colspan="1">1</td><td align="center" valign="middle" rowspan="1" colspan="1">0.1</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Other/unassigned</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">16</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">31.4</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">9</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">56.3</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">893</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">29.9</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">65</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">7.4</td></tr></tbody></table></table-wrap><table-wrap position="float" id="sensors-23-07333-t007"><object-id pub-id-type="pii">sensors-23-07333-t007_Table 7</object-id><label>Table 7</label><caption><p>Analysis of the captured LoRaWAN Class-B beacons.</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2" align="left" valign="middle" style="border-top:solid thin;border-bottom:solid thin" colspan="1">Timestamp</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Liege</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Graz</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Vienna</th><th colspan="2" align="center" valign="middle" style="border-top:solid thin" rowspan="1">Brno</th></tr><tr><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Packets per Day</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">% of Total</th></tr></thead><tbody><tr><td align="left" valign="middle" rowspan="1" colspan="1">Correct (includes location)</td><td align="center" valign="middle" rowspan="1" colspan="1">484</td><td align="center" valign="middle" rowspan="1" colspan="1">100.0</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">383</td><td align="center" valign="middle" rowspan="1" colspan="1">40.3</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Correct (no location)</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">55</td><td align="center" valign="middle" rowspan="1" colspan="1">5.8</td><td align="center" valign="middle" rowspan="1" colspan="1">495</td><td align="center" valign="middle" rowspan="1" colspan="1">57.7</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Incorrect, shifted by 18 s</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">505</td><td align="center" valign="middle" rowspan="1" colspan="1">53.1</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td></tr><tr><td align="left" valign="middle" rowspan="1" colspan="1">Incorrect, in UNIX format</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" rowspan="1" colspan="1">361</td><td align="center" valign="middle" rowspan="1" colspan="1">42.1</td></tr><tr><td align="left" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Incorrect (other)</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">&#x02013;</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">9</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">1</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.1</td></tr></tbody></table></table-wrap></floats-group></article>