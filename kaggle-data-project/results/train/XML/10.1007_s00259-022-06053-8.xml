<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ali="http://www.niso.org/schemas/ali/1.0/" article-type="research-article" dtd-version="1.3"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">Eur J Nucl Med Mol Imaging</journal-id><journal-id journal-id-type="iso-abbrev">Eur J Nucl Med Mol Imaging</journal-id><journal-title-group><journal-title>European Journal of Nuclear Medicine and Molecular Imaging</journal-title></journal-title-group><issn pub-type="ppub">1619-7070</issn><issn pub-type="epub">1619-7089</issn><publisher><publisher-name>Springer Berlin Heidelberg</publisher-name><publisher-loc>Berlin/Heidelberg</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">9742659</article-id><article-id pub-id-type="pmid">36508026</article-id><article-id pub-id-type="publisher-id">6053</article-id><article-id pub-id-type="doi">10.1007/s00259-022-06053-8</article-id><article-categories><subj-group subj-group-type="heading"><subject>Original Article</subject></subj-group></article-categories><title-group><article-title>Decentralized collaborative multi-institutional PET attenuation and scatter correction using federated deep learning</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Shiri</surname><given-names>Isaac</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Vafaei Sadr</surname><given-names>Alireza</given-names></name><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Akhavan</surname><given-names>Azadeh</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Salimi</surname><given-names>Yazdan</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Sanaat</surname><given-names>Amirhossein</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Amini</surname><given-names>Mehdi</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Razeghi</surname><given-names>Behrooz</given-names></name><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Saberi</surname><given-names>Abdollah</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Arabi</surname><given-names>Hossein</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Ferdowsi</surname><given-names>Sohrab</given-names></name><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Voloshynovskiy</surname><given-names>Slava</given-names></name><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>G&#x000fc;nd&#x000fc;z</surname><given-names>Deniz</given-names></name><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Rahmim</surname><given-names>Arman</given-names></name><xref ref-type="aff" rid="Aff7">7</xref><xref ref-type="aff" rid="Aff8">8</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0001-7559-5297</contrib-id><name><surname>Zaidi</surname><given-names>Habib</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff9">9</xref><xref ref-type="aff" rid="Aff10">10</xref><xref ref-type="aff" rid="Aff11">11</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.150338.c</institution-id><institution-id institution-id-type="ISNI">0000 0001 0721 9812</institution-id><institution>Division of Nuclear Medicine and Molecular Imaging, </institution><institution>Geneva University Hospital, </institution></institution-wrap>CH-1211 Geneva 4, Switzerland </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.8591.5</institution-id><institution-id institution-id-type="ISNI">0000 0001 2322 4988</institution-id><institution>Department of Theoretical Physics and Center for Astroparticle Physics, </institution><institution>University of Geneva, </institution></institution-wrap>Geneva, Switzerland </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="GRID">grid.412301.5</institution-id><institution-id institution-id-type="ISNI">0000 0000 8653 1507</institution-id><institution>Institute of Pathology, </institution><institution>RWTH Aachen University Hospital, </institution></institution-wrap>Aachen, Germany </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="GRID">grid.8591.5</institution-id><institution-id institution-id-type="ISNI">0000 0001 2322 4988</institution-id><institution>Department of Computer Science, </institution><institution>University of Geneva, </institution></institution-wrap>Geneva, Switzerland </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="GRID">grid.8591.5</institution-id><institution-id institution-id-type="ISNI">0000 0001 2322 4988</institution-id><institution>HES-SO, University of Geneva, </institution></institution-wrap>Geneva, Switzerland </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="GRID">grid.7445.2</institution-id><institution-id institution-id-type="ISNI">0000 0001 2113 8111</institution-id><institution>Department of Electrical and Electronic Engineering, </institution><institution>Imperial College London, </institution></institution-wrap>London, UK </aff><aff id="Aff7"><label>7</label><institution-wrap><institution-id institution-id-type="GRID">grid.17091.3e</institution-id><institution-id institution-id-type="ISNI">0000 0001 2288 9830</institution-id><institution>Departments of Radiology and Physics, </institution><institution>University of British Columbia, </institution></institution-wrap>Vancouver, Canada </aff><aff id="Aff8"><label>8</label>Department of Integrative Oncology, BC Cancer Research Institute, Vancouver, BC Canada </aff><aff id="Aff9"><label>9</label><institution-wrap><institution-id institution-id-type="GRID">grid.8591.5</institution-id><institution-id institution-id-type="ISNI">0000 0001 2322 4988</institution-id><institution>Geneva University Neurocenter, Geneva University, </institution></institution-wrap>Geneva, Switzerland </aff><aff id="Aff10"><label>10</label><institution-wrap><institution-id institution-id-type="GRID">grid.4494.d</institution-id><institution-id institution-id-type="ISNI">0000 0000 9558 4598</institution-id><institution>Department of Nuclear Medicine and Molecular Imaging, </institution><institution>University of Groningen, University Medical Center Groningen, </institution></institution-wrap>Groningen, Netherlands </aff><aff id="Aff11"><label>11</label><institution-wrap><institution-id institution-id-type="GRID">grid.10825.3e</institution-id><institution-id institution-id-type="ISNI">0000 0001 0728 0170</institution-id><institution>Department of Nuclear Medicine, </institution><institution>University of Southern Denmark, </institution></institution-wrap>Odense, Denmark </aff></contrib-group><pub-date pub-type="epub"><day>12</day><month>12</month><year>2022</year></pub-date><pub-date pub-type="pmc-release"><day>12</day><month>12</month><year>2022</year></pub-date><pub-date pub-type="ppub"><year>2023</year></pub-date><volume>50</volume><issue>4</issue><fpage>1034</fpage><lpage>1050</lpage><history><date date-type="received"><day>13</day><month>8</month><year>2022</year></date><date date-type="accepted"><day>18</day><month>11</month><year>2022</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2022</copyright-statement><license><ali:license_ref specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p><bold>Open Access</bold>This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article's Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article's Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>.</license-p></license></permissions><abstract id="Abs1"><sec><title>Purpose</title><p id="Par1">Attenuation correction and scatter compensation (AC/SC) are two main steps toward quantitative PET imaging, which remain challenging in PET-only and PET/MRI systems. These can be effectively tackled via deep learning (DL) methods. However, trustworthy, and generalizable DL models commonly require well-curated, heterogeneous, and large datasets from multiple clinical centers. At the same time, owing to legal/ethical issues and privacy concerns, forming a large collective, centralized dataset poses significant challenges. In this work, we aimed to develop a DL-based model in a multicenter setting without direct sharing of data using federated learning (FL) for AC/SC of PET images.</p></sec><sec><title>Methods</title><p id="Par2">Non-attenuation/scatter corrected and CT-based attenuation/scatter corrected (CT-ASC) <sup>18</sup>F-FDG PET images of 300 patients were enrolled in this study. The dataset consisted of 6 different centers, each with 50 patients, with scanner, image acquisition, and reconstruction protocols varying across the centers. CT-based ASC PET images served as the standard reference. All images were reviewed to include high-quality and artifact-free PET images. Both corrected and uncorrected PET images were converted to standardized uptake values (SUVs). We used a modified nested U-Net utilizing residual U-block in a U-shape architecture. We evaluated two FL models, namely sequential (FL-SQ) and parallel (FL-PL) and compared their performance with the baseline centralized (CZ) learning model wherein the data were pooled to one server, as well as center-based (CB) models where for each center the model was built and evaluated separately. Data from each center were divided to contribute to training (30 patients), validation (10 patients), and test sets (10 patients). Final evaluations and reports were performed on 60 patients (10 patients from each center).</p></sec><sec><title>Results</title><p id="Par3">In terms of percent SUV absolute relative error (ARE%), both FL-SQ (CI:12.21&#x02013;14.81%) and FL-PL (CI:11.82&#x02013;13.84%) models demonstrated excellent agreement with the centralized framework (CI:10.32&#x02013;12.00%), while FL-based algorithms improved model performance by over 11% compared to CB training strategy (CI: 22.34&#x02013;26.10%). Furthermore, the Mann&#x02013;Whitney test between different strategies revealed no significant differences between CZ and FL-based algorithms (<italic>p</italic>-value&#x02009;&#x0003e;&#x02009;0.05) in center-categorized mode. At the same time, a significant difference was observed between the different training approaches on the overall dataset (<italic>p</italic>-value&#x02009;&#x0003c;&#x02009;0.05). In addition, voxel-wise comparison, with respect to reference CT-ASC, exhibited similar performance for images predicted by CZ (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.94), FL-SQ (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.93), and FL-PL (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.92), while CB model achieved a far lower coefficient of determination (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.74). Despite the strong correlations between CZ and FL-based methods compared to reference CT-ASC, a slight underestimation of predicted voxel values was observed.</p></sec><sec><title>Conclusion</title><p id="Par4">Deep learning-based models provide promising results toward quantitative PET image reconstruction. Specifically, we developed two FL models and compared their performance with center-based and centralized models. The proposed FL-based models achieved higher performance compared to center-based models, comparable with centralized models. Our work provided strong empirical evidence that the FL framework can fully benefit from the generalizability and robustness of DL models used for AC/SC in PET, while obviating the need for the direct sharing of datasets between clinical imaging centers.</p></sec><sec><title>Supplementary Information</title><p>The online version contains supplementary material available at 10.1007/s00259-022-06053-8.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>PET</kwd><kwd>Attenuation correction</kwd><kwd>Deep learning</kwd><kwd>Federated learning</kwd><kwd>Distributed learning</kwd></kwd-group><funding-group><award-group><funding-source><institution>University of Geneva</institution></funding-source></award-group><open-access><p>Open access funding provided by University of Geneva</p></open-access></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; Springer-Verlag GmbH Germany, part of Springer Nature 2023</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Introduction</title><p id="Par5">PET is widely used for in vivo quantification of physiological processes at the molecular level [<xref ref-type="bibr" rid="CR1">1</xref>]. The introduction of hybrid imaging, in the form of PET/CT has thrived its adoption in a clinical setting, particularly for oncological applications [<xref ref-type="bibr" rid="CR1">1</xref>]. Corrections for physical degrading factors mainly linked to the interaction of annihilation photons with matter, such as attenuation and Compton scattering, are needed to achieve the full potential of quantitative PET imaging [<xref ref-type="bibr" rid="CR2">2</xref>]. During the image formation process, a significant number of annihilation photons undergo photoelectric absorption and multiple Compton interactions with underlying material along their trajectory (patient body, scanner hardware, etc.) before reaching the PET detectors [<xref ref-type="bibr" rid="CR2">2</xref>, <xref ref-type="bibr" rid="CR3">3</xref>]. Attenuation and scattering interactions result in undetected annihilation events and the recording of anomalous coincidences, respectively [<xref ref-type="bibr" rid="CR4">4</xref>]. This leads to a large tracer uptake quantification bias. It has been reported that a fraction of around 30&#x02013;35% of all detected events in 3D brain scanning are recorded from scattered photons, while this fraction exceeds 50&#x02013;60% in whole-body scanning [<xref ref-type="bibr" rid="CR5">5</xref>]. The probability of photon interactions increases either with the traveling distance (patient&#x02019;s size) or the electron density of the medium [<xref ref-type="bibr" rid="CR4">4</xref>]. Hence, for an effective attenuation/scatter correction (AC/SC) of PET images, a prior knowledge of the attenuation map at 511&#x000a0;keV through the traveling medium is required [<xref ref-type="bibr" rid="CR4">4</xref>].</p><p id="Par6">The problem of AC/SC to achieve quantitative PET imaging has been relatively successfully resolved following the commercial emergence of hybrid PET/CT modality where CT-based correction algorithms are commonly implemented on commercial systems [<xref ref-type="bibr" rid="CR4">4</xref>, <xref ref-type="bibr" rid="CR6">6</xref>]. However, AC and SC remain challenging on PET/MRI and PET-only scanners [<xref ref-type="bibr" rid="CR7">7</xref>, <xref ref-type="bibr" rid="CR8">8</xref>]. Unlike PET/CT, direct attenuation correction in PET/MRI is not straightforward owing to the lack of direct correlation between MR signals, i.e., proton density and time-relaxation properties of tissues and electron density [<xref ref-type="bibr" rid="CR8">8</xref>]. Hence, various strategies have been devised for MRI-guided AC/SC, including bulk segmentation, atlas-based algorithms, and emission-based techniques. Although these methods improve the quantification accuracy of PET images, they are affected by the misclassification of tissues (segmentation-based approach), as well as inter/intra-subject variability of MR images for co-registration to the best-fitted atlas model (atlas-based approach) [<xref ref-type="bibr" rid="CR8">8</xref>]. Furthermore, in PET-only scanners, emission-based algorithms that estimate directly the attenuation map from the emission data, time-of-flight (TOF) information, and anatomical prior knowledge have been proposed [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>].</p><p id="Par7">The past decade has witnessed significant progress in the development and implementation of artificial intelligence (AI)-based methods in different areas of medical image analysis, e.g., detection, segmentation, classification, regression, and outcome prediction [<xref ref-type="bibr" rid="CR11">11</xref>&#x02013;<xref ref-type="bibr" rid="CR14">14</xref>]. Several AI-based algorithms, in particular deep convolutional neural networks, have been developed to address the limitations of conventional attenuation correction techniques, demonstrating significant benefits in terms of improved image quality and quantitative accuracy of PET imaging [<xref ref-type="bibr" rid="CR15">15</xref>, <xref ref-type="bibr" rid="CR16">16</xref>]. In this context, four main learning-based approaches for AC/SC of PET data, including (i) the generation of synthesized CT from MR images [<xref ref-type="bibr" rid="CR17">17</xref>], (ii) generating synthesized CT from non-corrected PET images [<xref ref-type="bibr" rid="CR18">18</xref>], (iii) predicting the scattered component from emission information (TOF, event position) in either the image or sinogram domain [<xref ref-type="bibr" rid="CR10">10</xref>, <xref ref-type="bibr" rid="CR19">19</xref>], and (iv) generating directly AC/SC PET images from non-attenuation/scatter corrected images [<xref ref-type="bibr" rid="CR20">20</xref>]. Although, a number of studies reported promising performance of deep learning (DL)-based algorithms within an acceptable clinical tolerance, the size of training and testing datasets is a major limitation of these methods [<xref ref-type="bibr" rid="CR21">21</xref>]. To build a generalizable and trustworthy DL model, a large multicenter dataset is required to tune millions of model parameters [<xref ref-type="bibr" rid="CR22">22</xref>&#x02013;<xref ref-type="bibr" rid="CR24">24</xref>]. However, the sensitivity of medical images, and the ensuing ethical/legal considerations and regulations, challenge gathering large datasets to feed such data-hungry algorithms [<xref ref-type="bibr" rid="CR22">22</xref>&#x02013;<xref ref-type="bibr" rid="CR24">24</xref>]. To address this issue, federated learning (FL), initially developed for mobile technologies, is being increasingly considered in the healthcare domain [<xref ref-type="bibr" rid="CR4">4</xref>].</p><p id="Par8">A single hospital, often, cannot provide a sufficient number of samples, as required for successful training of machine learning models with acceptable accuracy, generalizability, and trustworthiness [<xref ref-type="bibr" rid="CR22">22</xref>&#x02013;<xref ref-type="bibr" rid="CR25">25</xref>]. As such, it may not be feasible to train a high-quality model for PET AC/SC images based on a limited sample dataset available from a single hospital. Moreover, all hospitals do not have infrastructures and expertise for machine learning model developments. One strategy involves collecting data from different hospitals to train a more accurate model. However, this approach is challenged by various privacy regulations and policies on data sharing. FL techniques enable the collaborative training of machine learning models among multiple parties without exchanging the local data to preserve privacy and solve the concerns of data users and data owners [<xref ref-type="bibr" rid="CR22">22</xref>&#x02013;<xref ref-type="bibr" rid="CR24">24</xref>].</p><p id="Par9">A typical FL protocol consists of three main components: (i) the manager (e.g., trusted server), (ii) participating parties as data owners (e.g., hospitals and departments), and (iii) computation-communication framework to train the local and global models [<xref ref-type="bibr" rid="CR26">26</xref>]. Depending on the parties, FL protocols can be divided into two settings: (i) cross-device FL, where the parties are edge devices; and (ii) cross-silo FL, where the parties are reliable organizations (e.g., hospitals). In designing an FL system, one needs to consider three properties regarding the participating parties namely, (a) computational and storage capacity of the parties, (b) stability and scale of the parties, and (c) data distribution among the parties [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. The manager (trusted server or party) supervises the training procedure of the global model and manages the communication between the data owners and itself. To produce an accurate model, the stability and reliability of the server need to be guaranteed [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. In the cross-device setting, various solutions have been proposed to increase the reliability of the system [<xref ref-type="bibr" rid="CR29">29</xref>&#x02013;<xref ref-type="bibr" rid="CR32">32</xref>]. Fortunately, in the cross-silo setting, organizations have powerful computational machines, better facilitating FL [<xref ref-type="bibr" rid="CR29">29</xref>&#x02013;<xref ref-type="bibr" rid="CR32">32</xref>]. Hence, one possible option is to consider one of the organizations as the manager of the FL model [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. Alternatively, the organizations can act in a fully decentralized setting. In this setting, all the participated parties communicate with each other directly [<xref ref-type="bibr" rid="CR29">29</xref>&#x02013;<xref ref-type="bibr" rid="CR32">32</xref>].</p><p id="Par10">Collaborative models could be trained in a decentralized manner using an FL framework without exchanging data between the different centers/hospitals [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. In recent years, FL-based DL models have been applied to multi-institutional data for different medical imaging tasks, including image segmentation [<xref ref-type="bibr" rid="CR33">33</xref>&#x02013;<xref ref-type="bibr" rid="CR35">35</xref>] and abnormality detection and classification [<xref ref-type="bibr" rid="CR36">36</xref>&#x02013;<xref ref-type="bibr" rid="CR38">38</xref>]. The main contribution of the present study is to propose, implement, and assess a robust FL algorithm for attenuation/scatter correction of PET data to achieve a generalized model using a limited data obtained from each center without direct sharing of data amongst the different centers. The hope is to propose this development for potential applications on standalone CT-less PET scanners or enhanced quality assurance in PET/CT scanners.</p></sec><sec id="Sec2"><title>Materials and methods</title><sec id="Sec3"><title>PET/CT datasets</title><p id="Par11">Non-attenuation-corrected and CT-based attenuation-corrected <sup>18</sup>F-FDG PET images of 300 patients were included in this study. The dataset were acquired at 6 different centers, each providing 50 patients acquired on various PET scanners, using different image acquisition and reconstruction protocols across the different centers, more information of dataset is provided in Table <xref rid="Tab1" ref-type="table">1</xref> [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR47">47</xref>]. All images were reviewed to include only high-quality and artifact-free PET images. PET images were converted to standardized uptake values (SUVs) for both corrected and non-corrected images.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Patients demographics and PET/CT image acquisition and reconstruction settings across the six different centers</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left"/><th align="left"/><th align="left">Centre 1</th><th align="left">Centre 2</th><th align="left">Centre 3</th><th align="left">Centre 4</th><th align="left">Centre 5</th><th align="left">Centre 6</th></tr></thead><tbody><tr><td align="left" rowspan="3">Demographic</td><td align="left">Sex (F/M)</td><td align="left">15/35</td><td align="left">17/33</td><td align="left">19/31</td><td align="left">22/28</td><td align="left">6/44</td><td align="left">21/29</td></tr><tr><td align="left">Age</td><td align="left">54&#x02009;&#x000b1;&#x02009;22.7</td><td align="left">62.6&#x02009;&#x000b1;&#x02009;8.8</td><td align="left">63.9&#x02009;&#x000b1;&#x02009;12.2</td><td align="left">68&#x02009;&#x000b1;&#x02009;9.4</td><td align="left">58.2&#x02009;&#x000b1;&#x02009;9</td><td align="left">52.6&#x02009;&#x000b1;&#x02009;20.2</td></tr><tr><td align="left">Weight</td><td align="left">69.1&#x02009;&#x000b1;&#x02009;15.9</td><td align="left">68.2&#x02009;&#x000b1;&#x02009;18.4</td><td align="left">77.3&#x02009;&#x000b1;&#x02009;18.7</td><td align="left">74.5&#x02009;&#x000b1;&#x02009;16.1</td><td align="left">84.3&#x02009;&#x000b1;&#x02009;18</td><td align="left">70.2&#x02009;&#x000b1;&#x02009;23.3</td></tr><tr><td align="left" rowspan="2">Scanners</td><td align="left">Manufacture</td><td align="left">GE</td><td align="left">GE</td><td align="left">GE</td><td align="left">GE</td><td align="left">GE</td><td align="left">Siemens</td></tr><tr><td align="left">Model</td><td align="left">Duo</td><td align="left">LS</td><td align="left">ST</td><td align="left">Discovery 690</td><td align="left">RX</td><td align="left">Biograph</td></tr><tr><td align="left" rowspan="2">CT acquisition</td><td align="left">Average tube current</td><td align="left">115.7&#x02009;&#x000b1;&#x02009;9.2</td><td align="left">120.6&#x02009;&#x000b1;&#x02009;41</td><td align="left">149.2&#x02009;&#x000b1;&#x02009;51.9</td><td align="left">98.3&#x02009;&#x000b1;&#x02009;61</td><td align="left">264.1&#x02009;&#x000b1;&#x02009;41.9</td><td align="left">176&#x02009;&#x000b1;&#x02009;32.0</td></tr><tr><td align="left">kVp</td><td align="left">130&#x02009;&#x000b1;&#x02009;0</td><td align="left">135&#x02009;&#x000b1;&#x02009;8.8</td><td align="left">134&#x02009;&#x000b1;&#x02009;9.3</td><td align="left">134&#x02009;&#x000b1;&#x02009;9.3</td><td align="left">119.2&#x02009;&#x000b1;&#x02009;4</td><td align="left">130&#x000b1;&#x02009;0</td></tr><tr><td align="left" rowspan="8">PET acquisition and reconstruction parameters</td><td align="left">Injected dose</td><td align="left">487.2&#x02009;&#x000b1;&#x02009;72.9</td><td align="left">514.3&#x02009;&#x000b1;&#x02009;118.1</td><td align="left">549.7&#x02009;&#x000b1;&#x02009;95.2</td><td align="left">425.5&#x02009;&#x000b1;&#x02009;91.2</td><td align="left">448.9&#x02009;&#x000b1;&#x02009;121.8</td><td align="left">373.9&#x02009;&#x000b1;&#x02009;92.6</td></tr><tr><td align="left">Time to scan</td><td align="left">75.7&#x02009;&#x000b1;&#x02009;18.9</td><td align="left">72.1&#x02009;&#x000b1;&#x02009;25.5</td><td align="left">75.2&#x02009;&#x000b1;&#x02009;17.6</td><td align="left">73.1&#x02009;&#x000b1;&#x02009;18.8</td><td align="left">86.7&#x02009;&#x000b1;&#x02009;13.6</td><td align="left">97.6&#x02009;&#x000b1;&#x02009;13.9</td></tr><tr><td align="left">Time Per Bed</td><td align="left">2.6&#x02009;&#x000b1;&#x02009;0.5</td><td align="left">4.6&#x02009;&#x000b1;&#x02009;1</td><td align="left">3.6&#x02009;&#x000b1;&#x02009;0.6</td><td align="left">2.4&#x02009;&#x000b1;&#x02009;1</td><td align="left">3.1&#x02009;&#x000b1;&#x02009;0.3</td><td align="left">3.1&#x02009;&#x000b1;&#x02009;0.4</td></tr><tr><td align="left"><p>Scatter</p><p>Correction</p></td><td align="left">Model-based</td><td align="left">Convolution subtraction</td><td align="left">Convolution subtraction</td><td align="left">Model-based</td><td align="left">Model-based</td><td align="left">Model-based</td></tr><tr><td align="left">Reconstruction</td><td align="left">OSEM</td><td align="left">OSEM</td><td align="left">OSEM</td><td align="left">VPHD, VPHDS</td><td align="left">OSEM</td><td align="left">OSEM&#x02009;+&#x02009;PSF</td></tr><tr><td align="left">Matrix&#x000a0;size</td><td align="left">256&#x02009;&#x000d7;&#x02009;256</td><td align="left">128&#x02009;&#x000d7;&#x02009;128</td><td align="left">128&#x02009;&#x000d7;&#x02009;128</td><td align="left">192&#x02009;&#x000d7;&#x02009;192</td><td align="left">128&#x02009;&#x000d7;&#x02009;128</td><td align="left">168&#x02009;&#x000d7;&#x02009;168</td></tr><tr><td align="left">Slice&#x000a0;thickness</td><td align="left">3.4</td><td align="left">4.3</td><td align="left">3.3</td><td align="left">3.5</td><td align="left">3.3</td><td align="left">3</td></tr><tr><td align="left">Slice numbers</td><td align="left">14,598</td><td align="left">10,647</td><td align="left">13,683</td><td align="left">16,282</td><td align="left">11,002</td><td align="left">26,210</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec4"><title>Image preprocessing</title><p id="Par12">We converted all PET images to SUV values and resampled both NAC and CT-ASC PET images to the same voxel spacing (3&#x02009;&#x000d7;&#x02009;3&#x02009;&#x000d7;&#x02009;4 mm<sup>3</sup>) and finally normalized by empirical values of 3 and 9 for NAC and CT-ASC, respectively. To harmonize the intensity range of PET images across the different centers, the voxel values of both non-AC and CT-AC PET images were converted to SUV. Subsequently, non-ASC and CT-ASC PET images were normalized by SUV factors of 3 and 9, respectively. In this way, the intensity range of all PET images across the different centers was between 0 and 5.</p></sec><sec id="Sec5"><title>Global FL training</title><p id="Par13">In typical machine learning problems, the goal is to minimize an appropriate loss function <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${F}\left(\uptheta \right)$$\end{document}</tex-math><mml:math id="M2"><mml:mrow><mml:mi>F</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">&#x003b8;</mml:mi></mml:mfenced></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq1.gif"/></alternatives></inline-formula>, where <inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\uptheta \in {\mathbb{R}}^{d}$$\end{document}</tex-math><mml:math id="M4"><mml:mrow><mml:mi mathvariant="normal">&#x003b8;</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mrow><mml:mi mathvariant="double-struck">R</mml:mi></mml:mrow><mml:mi>d</mml:mi></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq2.gif"/></alternatives></inline-formula> denotes the parameters of the model. The loss function <inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${F}\left(\uptheta \right)$$\end{document}</tex-math><mml:math id="M6"><mml:mrow><mml:mi>F</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">&#x003b8;</mml:mi></mml:mfenced></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq3.gif"/></alternatives></inline-formula> represents the average of empirical loss functions over the available data samples with respect to model parameter <inline-formula id="IEq4"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\uptheta$$\end{document}</tex-math><mml:math id="M8"><mml:mi mathvariant="normal">&#x003b8;</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq4.gif"/></alternatives></inline-formula>. A common approach to minimize the loss function <inline-formula id="IEq5"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${F}\left(\uptheta \right)$$\end{document}</tex-math><mml:math id="M10"><mml:mrow><mml:mi>F</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">&#x003b8;</mml:mi></mml:mfenced></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq5.gif"/></alternatives></inline-formula> is to use the iterative Stochastic Gradient Descent (SGD) algorithm. The idea of federated DL originates from the fact that SGD allows parallelization [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR48">48</xref>&#x02013;<xref ref-type="bibr" rid="CR53">53</xref>]. Hence, one can optimize a machine learning model using <italic>distributed</italic> SGD. The framework is as follows.</p><p id="Par14">Consider the FL system with <inline-formula id="IEq6"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$K$$\end{document}</tex-math><mml:math id="M12"><mml:mi>K</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq6.gif"/></alternatives></inline-formula> parties, where the <inline-formula id="IEq7"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k$$\end{document}</tex-math><mml:math id="M14"><mml:mi>k</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq7.gif"/></alternatives></inline-formula>-th party has a local training dataset&#x000a0;<inline-formula id="IEq8"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${{\mathcal{D}}_{k} = \left\{{X}_{i} , {Y}_{i}\right\}}_{i=1}^{{N}_{k}}$$\end{document}</tex-math><mml:math id="M16"><mml:msubsup><mml:mrow><mml:msub><mml:mi mathvariant="script">D</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfenced close="}" open="{"><mml:msub><mml:mi>X</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>Y</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:msubsup></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq8.gif"/></alternatives></inline-formula>, where <inline-formula id="IEq9"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${X}_{i}$$\end{document}</tex-math><mml:math id="M18"><mml:msub><mml:mi>X</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq9.gif"/></alternatives></inline-formula> and <inline-formula id="IEq10"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${Y}_{i}$$\end{document}</tex-math><mml:math id="M20"><mml:msub><mml:mi>Y</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq10.gif"/></alternatives></inline-formula> are the feature vector and the ground-truth label vector, respectively, and <inline-formula id="IEq11"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${N}_{k}$$\end{document}</tex-math><mml:math id="M22"><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq11.gif"/></alternatives></inline-formula> is the sample size available at party<inline-formula id="IEq12"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k\in \left\{\mathrm{1,2}, ..., K\right\}$$\end{document}</tex-math><mml:math id="M24"><mml:mrow><mml:mi>k</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="}" open="{"><mml:mrow><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mn>2</mml:mn></mml:mrow><mml:mo>,</mml:mo><mml:mo>.</mml:mo><mml:mo>.</mml:mo><mml:mo>.</mml:mo><mml:mo>,</mml:mo><mml:mi>K</mml:mi></mml:mfenced></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq12.gif"/></alternatives></inline-formula>. Let all parties have <inline-formula id="IEq13"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$N=\sum_{k=1}^{K}{N}_{k}$$\end{document}</tex-math><mml:math id="M26"><mml:mrow><mml:mi>N</mml:mi><mml:mo>=</mml:mo><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>K</mml:mi></mml:msubsup><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq13.gif"/></alternatives></inline-formula> samples, and let <inline-formula id="IEq14"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${F}_{k}\left(\uptheta \right)$$\end{document}</tex-math><mml:math id="M28"><mml:mrow><mml:msub><mml:mi>F</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">&#x003b8;</mml:mi></mml:mfenced></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq14.gif"/></alternatives></inline-formula> denotes the local objective function of the <inline-formula id="IEq15"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k$$\end{document}</tex-math><mml:math id="M30"><mml:mi>k</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq15.gif"/></alternatives></inline-formula>-th client, i.e., we have:
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${F}_{k}\left(\uptheta \right) = \frac{1}{{N}_{k}} \sum_{i=1}^{{N}_{k}}\mathcal{L}\left(\theta ;\left({X}_{i}, {Y}_{i}\right)\right) ,$$\end{document}</tex-math><mml:math id="M32" display="block"><mml:mrow><mml:msub><mml:mi>F</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">&#x003b8;</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfrac><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:munderover><mml:mi mathvariant="script">L</mml:mi><mml:mfenced close=")" open="("><mml:mi>&#x003b8;</mml:mi><mml:mo>&#x0037e;</mml:mo><mml:mfenced close=")" open="("><mml:msub><mml:mi>X</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>Y</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mfenced><mml:mo>,</mml:mo></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par15">where <inline-formula id="IEq16"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\uptheta \in {\mathbb{R}}^{d}$$\end{document}</tex-math><mml:math id="M34"><mml:mrow><mml:mi mathvariant="normal">&#x003b8;</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mrow><mml:mi mathvariant="double-struck">R</mml:mi></mml:mrow><mml:mi>d</mml:mi></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq16.gif"/></alternatives></inline-formula> denotes the model parameters to be optimized and <inline-formula id="IEq17"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\mathcal{L}(.;.)$$\end{document}</tex-math><mml:math id="M36"><mml:mrow><mml:mi mathvariant="script">L</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mo>.</mml:mo><mml:mo>&#x0037e;</mml:mo><mml:mo>.</mml:mo><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq17.gif"/></alternatives></inline-formula> is the specific loss function. As an example, one can consider the mean square error loss function<inline-formula id="IEq18"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${\mathcal{L}\left(\Theta ;\left({X}_{i}, {Y}_{i}\right)\right)=\frac{1}{2}\Vert {y}_{i}- \widehat{{y}_{i}}\Vert }_{2}^{2}$$\end{document}</tex-math><mml:math id="M38"><mml:msubsup><mml:mrow><mml:mi mathvariant="script">L</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">&#x00398;</mml:mi><mml:mo>&#x0037e;</mml:mo><mml:mfenced close=")" open="("><mml:msub><mml:mi>X</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>Y</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mfenced><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mn>2</mml:mn></mml:mfrac><mml:mrow><mml:mo stretchy="false">&#x02016;</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:mover accent="true"><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo stretchy="false">&#x02016;</mml:mo></mml:mrow></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mn>2</mml:mn></mml:msubsup></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq18.gif"/></alternatives></inline-formula>, where <inline-formula id="IEq19"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\widehat{{y}_{i}}$$\end{document}</tex-math><mml:math id="M40"><mml:mover accent="true"><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo stretchy="false">^</mml:mo></mml:mover></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq19.gif"/></alternatives></inline-formula> is the corresponding predicted label, and <inline-formula id="IEq20"><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\Vert .\Vert$$\end{document}</tex-math><mml:math id="M42"><mml:mrow><mml:mo stretchy="false">&#x02016;</mml:mo><mml:mo>.</mml:mo><mml:mo stretchy="false">&#x02016;</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq20.gif"/></alternatives></inline-formula> is the <inline-formula id="IEq21"><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${l}_{2}$$\end{document}</tex-math><mml:math id="M44"><mml:msub><mml:mi>l</mml:mi><mml:mn>2</mml:mn></mml:msub></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq21.gif"/></alternatives></inline-formula>-norm. In this case, we consider the <italic>global</italic> optimization problem of our FL system as follows:<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\underset{\uptheta }{\mathrm{min}}\left(F\left(\theta \right)=\sum_{k=1}^{K}\frac{{N}_{k}}{N}{F}_{k}\left(\theta \right) \right) ,$$\end{document}</tex-math><mml:math id="M46" display="block"><mml:mrow><mml:munder><mml:mi mathvariant="normal">min</mml:mi><mml:mi mathvariant="normal">&#x003b8;</mml:mi></mml:munder><mml:mfenced close=")" open="("><mml:mi>F</mml:mi><mml:mfenced close=")" open="("><mml:mi>&#x003b8;</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>K</mml:mi></mml:munderover><mml:mfrac><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mi>N</mml:mi></mml:mfrac><mml:msub><mml:mi>F</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi>&#x003b8;</mml:mi></mml:mfenced></mml:mfenced><mml:mo>,</mml:mo></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par16">In this framework, the local objective function for each center is weighted by the fraction of data emerging from that center. In order to solve the above optimization problem, the SGD algorithm can be utilized. Therefore, at the <inline-formula id="IEq22"><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$t$$\end{document}</tex-math><mml:math id="M48"><mml:mi>t</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq22.gif"/></alternatives></inline-formula> th iteration, each party computes local gradients using the SGD method, and sends them back to the manager (server) for aggregation and updating. Let <inline-formula id="IEq23"><alternatives><tex-math id="M49">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\nabla {F}_{k}\left({\theta }_{t}\right)$$\end{document}</tex-math><mml:math id="M50"><mml:mrow><mml:mi mathvariant="normal">&#x02207;</mml:mi><mml:msub><mml:mi>F</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq23.gif"/></alternatives></inline-formula> denote the local gradient on the local data of the <inline-formula id="IEq24"><alternatives><tex-math id="M51">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$k$$\end{document}</tex-math><mml:math id="M52"><mml:mi>k</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq24.gif"/></alternatives></inline-formula> th party at the <inline-formula id="IEq25"><alternatives><tex-math id="M53">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$t$$\end{document}</tex-math><mml:math id="M54"><mml:mi>t</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq25.gif"/></alternatives></inline-formula> th iteration. Let <inline-formula id="IEq26"><alternatives><tex-math id="M55">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\eta$$\end{document}</tex-math><mml:math id="M56"><mml:mi>&#x003b7;</mml:mi></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq26.gif"/></alternatives></inline-formula> represent the learning rate and let <inline-formula id="IEq27"><alternatives><tex-math id="M57">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${\theta }_{t}$$\end{document}</tex-math><mml:math id="M58"><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq27.gif"/></alternatives></inline-formula> denote the model at <italic>t</italic>th iteration. The server aggregates and updates the model parameters as follows:<disp-formula id="Equ3"><label>3</label><alternatives><tex-math id="M59">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${\theta }_{t+1}\leftarrow {\theta }_{t}- \eta \sum\nolimits_{k=1}^{K}\frac{{N}_{k}}{N}\nabla {F}_{k}\left({\theta }_{t}\right) ,$$\end{document}</tex-math><mml:math id="M60" display="block"><mml:mrow><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo stretchy="false">&#x02190;</mml:mo><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:mi>&#x003b7;</mml:mi><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>K</mml:mi></mml:msubsup><mml:mfrac><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mi>N</mml:mi></mml:mfrac><mml:mi mathvariant="normal">&#x02207;</mml:mi><mml:msub><mml:mi>F</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ3.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par17">Note that in the case of massive datasets, the SGD becomes prohibitively demanding. Hence, the parameter vector is updated with the stochastic gradient:<disp-formula id="Equ4"><label>4</label><alternatives><tex-math id="M61">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${\theta }_{t+1}= {\theta }_{t}- {\eta }_{t}G\left({\theta }_{t}\right)$$\end{document}</tex-math><mml:math id="M62" display="block"><mml:mrow><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:msub><mml:mi>&#x003b7;</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mi>G</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ4.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par18">where <inline-formula id="IEq28"><alternatives><tex-math id="M63">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${\mathbb{E}}\left[G\left({\theta }_{t}\right)\right]= \nabla F\left({\theta }_{t}\right)$$\end{document}</tex-math><mml:math id="M64"><mml:mrow><mml:mi mathvariant="double-struck">E</mml:mi><mml:mfenced close="]" open="["><mml:mi>G</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub></mml:mfenced></mml:mfenced><mml:mo>=</mml:mo><mml:mi mathvariant="normal">&#x02207;</mml:mi><mml:mi>F</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi>t</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:math><inline-graphic xlink:href="259_2022_6053_Article_IEq28.gif"/></alternatives></inline-formula>.</p><p id="Par19">We evaluate two different training strategies for our federated pipeline. In the first training approach, a server aggregates the FL workflow as summarized in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>. We refer to this first strategy as parallel federated learning (FL-PL): first (step A), the central global model is distributed through different departments and then (step B) the models are trained in each center separately, and finally (step C) the locally trained models return to the central server and aggregate the results to the central global model. Steps A&#x02013;C are repeated until the model is fully trained and converges. In the second approach, referred to as sequential federated learning (FL-SQ), the model meets the data serially center-after-center. First (step A in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>), model training begins in one center for a predefined number of epochs, and then (step B) the model passes sequentially through all centers. Finally (step C), this process will be repeated for a predefined number of rounds to generate the ultimate model. FL-SQ requires longer training time since the learning procedure is sequential. As for the implementation of our experiments, all FL algorithms and DL models were implemented in TensorFlow 2.6 (details on DL models are provided below). The FL process in this work was performed on a server with multiple local GPUs similar to previous studies [<xref ref-type="bibr" rid="CR36">36</xref>, <xref ref-type="bibr" rid="CR54">54</xref>&#x02013;<xref ref-type="bibr" rid="CR59">59</xref>], where each local GPU was considered as our node and center.<fig id="Fig1"><label>Fig. 1</label><caption><p>Schematic of two FL algorithms and network architectures as implemented in this study. In parallel federated learning (FL-PL), in the first step (<bold>A</bold>), the central global model is distributed through different centers and then (<bold>B</bold>) the models are trained in each center separately, and finally (<bold>C</bold>), local-trained models return to the central server and aggregate the results to the central global model. Steps (<bold>A</bold>&#x02013;<bold>C</bold>) are repeated until the model is fully trained and converged. In sequential federated learning (FL-SQ), the model meets the data serially center-after-center. First (step <bold>A</bold> in Fig.&#x000a0;1), model training begins in one center for a predefined number of epochs, and then (step <bold>B</bold>) the model passes sequentially through all centers. Finally (step <bold>C</bold>), this process will be repeated for a predefined number of rounds to generate the ultimate model. The bottom image depicts our U2Net architecture; each blue block in the main body (left) consists of a residual U-Net (right)</p></caption><graphic xlink:href="259_2022_6053_Fig1_HTML" id="MO1"/></fig></p></sec><sec id="Sec6"><title>Deep neural network</title><p id="Par20">In this study, we used the modified U<sup>2</sup>-Net [<xref ref-type="bibr" rid="CR60">60</xref>] which utilizes residual U-blocks in a U-shaped architecture. It employs a deep network supervision strategy, where the training loss includes information in all scales. Deep supervision allows to extract both local and global contextual information [<xref ref-type="bibr" rid="CR60">60</xref>]. The advantage is that unlike the prevalently utilized U-Net based on successive down-sampling of the image and hence gradually losing high-resolution information [<xref ref-type="bibr" rid="CR60">60</xref>], the U<sup>2</sup>-Net does not sacrifice the high-resolution content of the images [<xref ref-type="bibr" rid="CR60">60</xref>], which is crucial for many image-to-image conversion tasks, such as attenuation scatter correction.</p><p id="Par21">This is performed using a nested two-level U-structure inspired from the classical U-Net. The idea is to keep the general U-structure of the U-Net [<xref ref-type="bibr" rid="CR61">61</xref>], but inside each convolutional block, it uses another structure which again has a U-shaped form with its symmetric encoder-decoder architecture [<xref ref-type="bibr" rid="CR60">60</xref>]. This block is known as ReSidual U-block (RSU), which enables intra-stage multi-scale features to be extracted. The RSU is motivated by the classic U-Net [<xref ref-type="bibr" rid="CR61">61</xref>] with a symmetric encoder-decoder structure. It provides a mixture of receptive fields with different sizes, which is highly desirable for fine-grained image-to-image tasks [<xref ref-type="bibr" rid="CR60">60</xref>]. This is equivalent to drastically increasing network layers, but with the important advantage of keeping the computational and memory footprint low and hence the training procedure simple [<xref ref-type="bibr" rid="CR60">60</xref>]. Note that the idea of having nested U-Net is different from the more common strategy of cascading multiple U-Nets together, which increases the computational burden proportional to the number of networks used [<xref ref-type="bibr" rid="CR60">60</xref>]. The nested structure enables the U<sup>2</sup>-Net to extract intra-stage multi-scale, as well as aggregated inter-stage multi-level features [<xref ref-type="bibr" rid="CR60">60</xref>]. As for the training strategy, the network further uses deep supervision, where the training loss includes information in all scales [<xref ref-type="bibr" rid="CR60">60</xref>]. Deep supervision allows to further extract both local and global contextual information [<xref ref-type="bibr" rid="CR60">60</xref>].</p><p id="Par22">It can evoke intra-stage features in different scales depending on the depth and kernel size [<xref ref-type="bibr" rid="CR60">60</xref>]. One can select an optional depth to achieve various single-level or multi-level nested U-shape structures [<xref ref-type="bibr" rid="CR60">60</xref>]. Although too deep models might get too complex with respect to implementation and employment in training procedures and real-world applications. In this work, non-attenuation/scatter-corrected images were used as input to the modified U<sup>2</sup>-Net to generate attenuation/scatter-corrected PET images directly. The network was trained in a 2D manner with an Adam optimizer, a learning rate of 0.001, an L2-norm loss, as well as a weight decay of 0.0001. The schema of the network is depicted in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>.</p></sec><sec id="Sec7"><title>Evaluation strategy</title><p id="Par23">In this study, we evaluated two federated models, referred to as FL-SQ and FL-PL, and compared their performance with the centralized (CZ) approach, wherein the data are pooled to one server. Moreover, center-based (CB) models were built and evaluated separately using only the training/test datasets from the same center. Each center&#x02019;s data were divided into training (30 patients), validation (10 patients), and test sets (10 patients). A standard train/validation/test data splitting was followed for the training of all models and the results were reported on untouched test sets to avoid the risk of overfitting. There was no overlap between training, validation, and testing sets. The same patients were used for evaluation of the different non-CB models to facilitate comparison of the various models. In the three non-CB strategies, including FL-SQ, FL-PL, and CS, the models were built using a 180/60 train/validation set, and the results were reported using 60 test sets (the 10 test datasets from each of the six centers). In CB models, six different models were developed using 30/10 train/validation, and only 10 test sets from the same center were employed for model evaluation.</p><p id="Par24">For model performance evaluation, voxel-wise mean error (ME), mean absolute error (MAE), relative error (RE%), absolute relative error (ARE%), and peak signal-to-noise ratio (PSNR) were computed between ground truth CT-based attenuation/scatter corrected and the predicted corrected PET images, as follows:<disp-formula id="Equ5"><label>5</label><alternatives><tex-math id="M65">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ME=\frac{1}{vxl}\sum\nolimits_{v=1}^{vxl}{PET}_{Predicted}(v)-{PET}_{CT-ASC}(v)$$\end{document}</tex-math><mml:math id="M66" display="block"><mml:mrow><mml:mi>M</mml:mi><mml:mi>E</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:mfrac><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>v</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:msubsup><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">Predicted</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>v</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>-</mml:mo><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mi>T</mml:mi><mml:mo>-</mml:mo><mml:mi>A</mml:mi><mml:mi>S</mml:mi><mml:mi>C</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>v</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ5.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ6"><label>6</label><alternatives><tex-math id="M67">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$MAE=\frac{1}{vxl}\sum\nolimits_{v=1}^{vxl}\left|{PET}_{predicted}(v)-{PET}_{CT-ASC}(v)\right|$$\end{document}</tex-math><mml:math id="M68" display="block"><mml:mrow><mml:mi>M</mml:mi><mml:mi>A</mml:mi><mml:mi>E</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:mfrac><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>v</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:msubsup><mml:mfenced close="|" open="|"><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">predicted</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>v</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>-</mml:mo><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mi>T</mml:mi><mml:mo>-</mml:mo><mml:mi>A</mml:mi><mml:mi>S</mml:mi><mml:mi>C</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>v</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfenced></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ6.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ7"><label>7</label><alternatives><tex-math id="M69">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$RE(\mathrm{\%})=\frac{1}{vxl}\sum\nolimits_{v=1}^{vxl}\frac{{\left({PET}_{predicted}\right)}_{v}-{\left({PET}_{CT-ASC}\right)}_{v}}{{\left({PET}_{CT-ASC}\right)}_{v}}\times 100\mathrm{\%}$$\end{document}</tex-math><mml:math id="M70" display="block"><mml:mrow><mml:mi>R</mml:mi><mml:mi>E</mml:mi><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mo>%</mml:mo><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:mfrac><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>v</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:msubsup><mml:mfrac><mml:mrow><mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">predicted</mml:mi></mml:mrow></mml:msub></mml:mfenced><mml:mi>v</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mi>T</mml:mi><mml:mo>-</mml:mo><mml:mi>A</mml:mi><mml:mi>S</mml:mi><mml:mi>C</mml:mi></mml:mrow></mml:msub></mml:mfenced><mml:mi>v</mml:mi></mml:msub></mml:mrow><mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mi>T</mml:mi><mml:mo>-</mml:mo><mml:mi>A</mml:mi><mml:mi>S</mml:mi><mml:mi>C</mml:mi></mml:mrow></mml:msub></mml:mfenced><mml:mi>v</mml:mi></mml:msub></mml:mfrac><mml:mo>&#x000d7;</mml:mo><mml:mn>100</mml:mn><mml:mo>%</mml:mo></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ7.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ8"><label>8</label><alternatives><tex-math id="M71">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ARE(\mathrm{\%})=\frac{1}{vxl}\sum\nolimits_{v=1}^{vxl}\left|\frac{{\left({PET}_{predicted}\right)}_{v}-{\left({PET}_{CT-ASC}\right)}_{v}}{{\left({PET}_{CT-ASC}\right)}_{v}}\right|\times 100\mathrm{\%}$$\end{document}</tex-math><mml:math id="M72" display="block"><mml:mrow><mml:mi>A</mml:mi><mml:mi>R</mml:mi><mml:mi>E</mml:mi><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mo>%</mml:mo><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:mfrac><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>v</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi mathvariant="italic">vxl</mml:mi></mml:mrow></mml:msubsup><mml:mfenced close="|" open="|"><mml:mfrac><mml:mrow><mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">predicted</mml:mi></mml:mrow></mml:msub></mml:mfenced><mml:mi>v</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mi>T</mml:mi><mml:mo>-</mml:mo><mml:mi>A</mml:mi><mml:mi>S</mml:mi><mml:mi>C</mml:mi></mml:mrow></mml:msub></mml:mfenced><mml:mi>v</mml:mi></mml:msub></mml:mrow><mml:msub><mml:mfenced close=")" open="("><mml:msub><mml:mrow><mml:mi mathvariant="italic">PET</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mi>T</mml:mi><mml:mo>-</mml:mo><mml:mi>A</mml:mi><mml:mi>S</mml:mi><mml:mi>C</mml:mi></mml:mrow></mml:msub></mml:mfenced><mml:mi>v</mml:mi></mml:msub></mml:mfrac></mml:mfenced><mml:mo>&#x000d7;</mml:mo><mml:mn>100</mml:mn><mml:mo>%</mml:mo></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ8.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ9"><label>9</label><alternatives><tex-math id="M73">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$PSNR(dB)=10{\mathrm{log}}_{10}(\frac{{Peak}^{2}}{MSE})$$\end{document}</tex-math><mml:math id="M74" display="block"><mml:mrow><mml:mi>P</mml:mi><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:mi>R</mml:mi><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:mi>B</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mn>10</mml:mn><mml:msub><mml:mi mathvariant="normal">log</mml:mi><mml:mn>10</mml:mn></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mfrac><mml:msup><mml:mrow><mml:mi mathvariant="italic">Peak</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mrow><mml:mi mathvariant="italic">MSE</mml:mi></mml:mrow></mml:mfrac><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math><graphic xlink:href="259_2022_6053_Article_Equ9.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par25">where <italic>PET</italic><sub><italic>predicted</italic></sub> denotes DL-based corrected PET image while <italic>PET</italic><sub><italic>CT-ASC</italic></sub> stands for the reference PET-CT-ASC image, and <italic>vxl</italic> and <italic>v</italic> denote the total number of voxels and voxel index, respectively. Moreover, the structural similarity index (SSIM) was calculated based on [<xref ref-type="bibr" rid="CR62">62</xref>].</p><p id="Par26">The different plots (box, bar, and scatter plots) were provided to enable different comparisons. Two-sample Wilcoxon test (Wilcoxon rank sum test or Mann&#x02013;Whitney test) was used for the statistical comparison of image-derived metrics between the different training models. We corrected <italic>p</italic>-values using Benjamin Homberg to provide an adjusted <italic>p</italic>-value (<italic>q</italic>-value). A threshold of 0.05 was considered as the significance level of <italic>q</italic>-values. In addition, we used joint histogram analysis to depict the distribution of voxel-wise PET SUV correlations between the reference CT-based ASC images and different DL approaches.</p></sec></sec><sec id="Sec8"><title>Results</title><p id="Par27">Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> represents an example of non-ASC, CT-ASC, CB model, CZ model, FL-SQ model, FL-PL model, and the corresponding bias maps generated for DL models with respect to CT-based ASC (CT-ASC) images. As can be seen, the CZ-based model, FL-SQ model, and FL-PL model generated high-quality images. More examples of images for each of the centers are provided in supplemental Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>.<fig id="Fig2"><label>Fig. 2</label><caption><p>Example of non-ASC, CT-ASC, CB model, CZ model, FL-SQ model, FL-PL model, and their corresponding bias maps generated for DL models with respect to CT-based ASC (CT-ASC) images. Sequential federated learning (FL-SQ), and parallel federated learning (FL-PL), centralized (CZ), and center based (CB)</p></caption><graphic xlink:href="259_2022_6053_Fig2_HTML" id="MO2"/></fig></p><p id="Par28">Figure&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> compares quantitative image quality metrics, i.e., RE (%), ARE (%), ME, MAE, SSIM, and PSNR, calculated on SUV images between the different training strategies with respect to CT-ASC images serving as ground truth. As expected, the CB training strategy is the worst method in terms of quantitative analysis, resulting in the highest absolute error (MAE&#x02009;=&#x02009;0.21&#x02009;&#x000b1;&#x02009;0.07). The performance of the FL-based algorithms is comparable with the centralized training strategy, while the CZ method shows a lower deviation and smaller variance compared to FL-SQ and FL-PL, in terms of MAE (0.10&#x02009;&#x000b1;&#x02009;0.03 versus 0.14&#x02009;&#x000b1;&#x02009;0.07 and 0.14&#x02009;&#x000b1;&#x02009;0.06, respectively). Table <xref rid="Tab2" ref-type="table">2</xref> summarizes the statistical comparisons of quantitative metrics between these four training strategies. In terms of overall structural similarity, the different approaches demonstrated comparable performance against ground truth (CZ&#x02009;=&#x02009;0.93&#x02009;&#x000b1;&#x02009;0.01, FL-SQ&#x02009;=&#x02009;0.93&#x02009;&#x000b1;&#x02009;0.01, and FL-PL&#x02009;=&#x02009;0.92&#x02009;&#x000b1;&#x02009;0.03), except for the CB achieving an SSIM of 0.70&#x02009;&#x000b1;&#x02009;0.04. Table <xref rid="Tab3" ref-type="table">3</xref> summarizes the statistical comparison of quantitative metrics calculated between these four training strategies separately for each center. The same pattern of quantitative metrics in Table <xref rid="Tab2" ref-type="table">2</xref>&#x000a0;is repeated for each center and all metrics across the different frameworks.<fig id="Fig3"><label>Fig. 3</label><caption><p>Comparison of quantitative image quality metrics, including RE (%), ARE (%), ME, MAE, SSIM, and PSNR, calculated on SUV images between different training strategies with respect to CT-ASC images serving as ground truth sequential federated learning (FL-SQ), and parallel federated learning (FL-PL), centralized (CZ), and center-based (CB)</p></caption><graphic xlink:href="259_2022_6053_Fig3_HTML" id="MO3"/></fig><table-wrap id="Tab2"><label>Table 2</label><caption><p>Statistical comparison of quantitative metrics between the four training strategies used in this study. Sequential federated learning (FL-SQ) and parallel federated learning (FL-PL), centralized (CZ), and center based (CB)</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" rowspan="2"/><th align="left" colspan="2">CB</th><th align="left" colspan="2">CZ</th><th align="left" colspan="2">FL_SQ</th><th align="left" colspan="2">FL_PL</th></tr><tr><th align="left">Mean</th><th align="left">95% CI</th><th align="left">Mean</th><th align="left">95% CI</th><th align="left">Mean</th><th align="left">95% CI</th><th align="left">Mean</th><th align="left">95% CI</th></tr></thead><tbody><tr><td align="left">MAE</td><td align="left">0.21&#x02009;&#x000b1;&#x02009;0.07</td><td align="left">0.19 to 0.22</td><td align="left">0.10&#x02009;&#x000b1;&#x02009;0.03</td><td align="left">0.09 to 0.11</td><td align="left">0.14&#x02009;&#x000b1;&#x02009;0.07</td><td align="left">0.12 to 0.15</td><td align="left">0.14&#x02009;&#x000b1;&#x02009;0.06</td><td align="left">0.12 to 0.15</td></tr><tr><td align="left">ME</td><td align="left">&#x02009;&#x02212;&#x02009;0.03&#x02009;&#x000b1;&#x02009;0.12</td><td align="left">&#x02009;&#x02212;&#x02009;0.06 to&#x02009;&#x02212;&#x02009;0.003</td><td align="left">&#x02009;&#x02212;&#x02009;0.01&#x02009;&#x000b1;&#x02009;0.06</td><td align="left">&#x02009;&#x02212;&#x02009;0.02 to 0.01</td><td align="left">&#x02009;&#x02212;&#x02009;0.10&#x02009;&#x000b1;&#x02009;0.09</td><td align="left">&#x02009;&#x02212;&#x02009;0.12 to&#x02009;&#x02212;&#x02009;0.07</td><td align="left">&#x02009;&#x02212;&#x02009;0.10&#x02009;&#x000b1;&#x02009;0.09</td><td align="left">&#x02009;&#x02212;&#x02009;0.12 to&#x02009;&#x02212;&#x02009;0.07</td></tr><tr><td align="left">PSNR</td><td align="left">28.66&#x02009;&#x000b1;&#x02009;2.70</td><td align="left">27.95 to 29.35</td><td align="left">34.77&#x02009;&#x000b1;&#x02009;2.56</td><td align="left">34.11 to 35.43</td><td align="left">33.17&#x02009;&#x000b1;&#x02009;3.53</td><td align="left">32.25 to 34.08</td><td align="left">33.11&#x02009;&#x000b1;&#x02009;3.49</td><td align="left">32.20 to 34.00</td></tr><tr><td align="left">ARE (%)</td><td align="left">24.22&#x02009;&#x000b1;&#x02009;7.28</td><td align="left">22.34 to 26.10</td><td align="left">11.16&#x02009;&#x000b1;&#x02009;3.24</td><td align="left">10.32 to 12.00</td><td align="left">13.51&#x02009;&#x000b1;&#x02009;5.04</td><td align="left">12.21 to 14.81</td><td align="left">12.83&#x02009;&#x000b1;&#x02009;3.91</td><td align="left">11.82 to 13.84</td></tr><tr><td align="left">RE (%)</td><td align="left">4.70&#x02009;&#x000b1;&#x02009;11.47</td><td align="left">1.73 to 7.66</td><td align="left">2.05&#x02009;&#x000b1;&#x02009;6.07</td><td align="left">0.48 to 3.61</td><td align="left">&#x02009;&#x02212;&#x02009;6.01&#x02009;&#x000b1;&#x02009;9.18</td><td align="left">&#x02009;&#x02212;&#x02009;8.38 to 3.63</td><td align="left">&#x02009;&#x02212;&#x02009;5.64&#x02009;&#x000b1;&#x02009;8.04</td><td align="left">&#x02009;&#x02212;&#x02009;7.71 to 3.55</td></tr><tr><td align="left">SSIM</td><td align="left">0.70&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">0.68 to 0.70</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.92 to 0.93</td><td align="left">0.92&#x02009;&#x000b1;&#x02009;0.03</td><td align="left">0.91 to 0.93</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.92 to 0.93</td></tr></tbody></table></table-wrap><table-wrap id="Tab3"><label>Table 3</label><caption><p>Comparison of various image quality metrics (mean&#x02009;&#x000b1;&#x02009;SD) for the different training models performed at the different centers</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left"/><th align="left"/><th align="left">Center 1</th><th align="left">Center 2</th><th align="left">Center 3</th><th align="left">Center 4</th><th align="left">Center 5</th><th align="left">Center 6</th></tr></thead><tbody><tr><td align="left" rowspan="6">Center based</td><td align="left">MAE</td><td align="left">0.18&#x02009;&#x000b1;&#x02009;0.03</td><td align="left">0.23&#x02009;&#x000b1;&#x02009;0.07</td><td align="left">0.19&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">0.24&#x02009;&#x000b1;&#x02009;0.07</td><td align="left">0.23&#x02009;&#x000b1;&#x02009;0.11</td><td align="left">0.19&#x02009;&#x000b1;&#x02009;0.03</td></tr><tr><td align="left">ME</td><td align="left">0&#x02009;&#x000b1;&#x02009;0.08</td><td align="left">&#x02009;&#x02212;&#x02009;0.06&#x02009;&#x000b1;&#x02009;0.12</td><td align="left">0&#x02009;&#x000b1;&#x02009;0.09</td><td align="left">&#x02009;&#x02212;&#x02009;0.09&#x02009;&#x000b1;&#x02009;0.13</td><td align="left">&#x02009;&#x02212;&#x02009;0.03&#x02009;&#x000b1;&#x02009;0.18</td><td align="left">&#x02009;&#x02212;&#x02009;0.03&#x02009;&#x000b1;&#x02009;0.09</td></tr><tr><td align="left">PSNR</td><td align="left">29.79&#x02009;&#x000b1;&#x02009;1.56</td><td align="left">27.87&#x02009;&#x000b1;&#x02009;3.96</td><td align="left">29.45&#x02009;&#x000b1;&#x02009;1.94</td><td align="left">27.52&#x02009;&#x000b1;&#x02009;3.28</td><td align="left">28.16&#x02009;&#x000b1;&#x02009;2.87</td><td align="left">29.16&#x02009;&#x000b1;&#x02009;1.53</td></tr><tr><td align="left">ARE (%)</td><td align="left">21.96&#x02009;&#x000b1;&#x02009;4.64</td><td align="left">24.45&#x02009;&#x000b1;&#x02009;5.92</td><td align="left">25&#x02009;&#x000b1;&#x02009;7.07</td><td align="left">25.34&#x02009;&#x000b1;&#x02009;9.19</td><td align="left">27.42&#x02009;&#x000b1;&#x02009;10.79</td><td align="left">21.18&#x02009;&#x000b1;&#x02009;3.19</td></tr><tr><td align="left">RE (%)</td><td align="left">7.24&#x02009;&#x000b1;&#x02009;9.71</td><td align="left">2.82&#x02009;&#x000b1;&#x02009;11.22</td><td align="left">8.63&#x02009;&#x000b1;&#x02009;10.75</td><td align="left">0.71&#x02009;&#x000b1;&#x02009;10.83</td><td align="left">5.6&#x02009;&#x000b1;&#x02009;16.92</td><td align="left">3.21&#x02009;&#x000b1;&#x02009;8.66</td></tr><tr><td align="left">SSIM</td><td align="left">0.71&#x02009;&#x000b1;&#x02009;0.03</td><td align="left">0.69&#x02009;&#x000b1;&#x02009;0.03</td><td align="left">0.71&#x02009;&#x000b1;&#x02009;0.03</td><td align="left">0.7&#x02009;&#x000b1;&#x02009;0.06</td><td align="left">0.69&#x02009;&#x000b1;&#x02009;0.03</td><td align="left">0.69&#x02009;&#x000b1;&#x02009;0.04</td></tr><tr><td align="left" rowspan="6">Centralized</td><td align="left">MAE</td><td align="left">0.09&#x02009;&#x000b1;&#x02009;0.02</td><td align="left">0.11&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">0.1&#x02009;&#x000b1;&#x02009;0.02</td><td align="left">0.12&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">0.12&#x02009;&#x000b1;&#x02009;0.05</td><td align="left">0.09&#x02009;&#x000b1;&#x02009;0.01</td></tr><tr><td align="left">ME</td><td align="left">0.01&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">&#x02009;&#x02212;&#x02009;0.01&#x02009;&#x000b1;&#x02009;0.08</td><td align="left">0.01&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">&#x02009;&#x02212;&#x02009;0.04&#x02009;&#x000b1;&#x02009;0.07</td><td align="left">&#x02009;&#x02212;&#x02009;0.01&#x02009;&#x000b1;&#x02009;0.09</td><td align="left">&#x02009;&#x02212;&#x02009;0.02&#x02009;&#x000b1;&#x02009;0.04</td></tr><tr><td align="left">PSNR</td><td align="left">35.75&#x02009;&#x000b1;&#x02009;1.59</td><td align="left">34.01&#x02009;&#x000b1;&#x02009;3.45</td><td align="left">35.53&#x02009;&#x000b1;&#x02009;1.99</td><td align="left">33.73&#x02009;&#x000b1;&#x02009;3.4</td><td align="left">34.06&#x02009;&#x000b1;&#x02009;2.66</td><td align="left">35.55&#x02009;&#x000b1;&#x02009;1.05</td></tr><tr><td align="left">ARE (%)</td><td align="left">10.37&#x02009;&#x000b1;&#x02009;2.62</td><td align="left">11.8&#x02009;&#x000b1;&#x02009;3.77</td><td align="left">11.63&#x02009;&#x000b1;&#x02009;3.29</td><td align="left">11.26&#x02009;&#x000b1;&#x02009;2.84</td><td align="left">12.48&#x02009;&#x000b1;&#x02009;4.6</td><td align="left">9.44&#x02009;&#x000b1;&#x02009;1.11</td></tr><tr><td align="left">RE (%)</td><td align="left">3.65&#x02009;&#x000b1;&#x02009;4.9</td><td align="left">1.53&#x02009;&#x000b1;&#x02009;7.45</td><td align="left">4.38&#x02009;&#x000b1;&#x02009;5.2</td><td align="left">&#x02009;&#x02212;&#x02009;0.59&#x02009;&#x000b1;&#x02009;5.84</td><td align="left">2.55&#x02009;&#x000b1;&#x02009;7.93</td><td align="left">0.77&#x02009;&#x000b1;&#x02009;4.34</td></tr><tr><td align="left">SSIM</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td></tr><tr><td align="left" rowspan="6">Federated SQ</td><td align="left">MAE</td><td align="left">0.14&#x02009;&#x000b1;&#x02009;0.05</td><td align="left">0.16&#x02009;&#x000b1;&#x02009;0.11</td><td align="left">0.13&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">0.15&#x02009;&#x000b1;&#x02009;0.06</td><td align="left">0.14&#x02009;&#x000b1;&#x02009;0.06</td><td align="left">0.12&#x02009;&#x000b1;&#x02009;0.04</td></tr><tr><td align="left">ME</td><td align="left">&#x02009;&#x02212;&#x02009;0.08&#x02009;&#x000b1;&#x02009;0.10</td><td align="left">&#x02009;&#x02212;&#x02009;0.14&#x02009;&#x000b1;&#x02009;0.13</td><td align="left">&#x02009;&#x02212;&#x02009;0.07&#x02009;&#x000b1;&#x02009;0.10</td><td align="left">&#x02009;&#x02212;&#x02009;0.12&#x02009;&#x000b1;&#x02009;0.09</td><td align="left">&#x02009;&#x02212;&#x02009;0.08&#x02009;&#x000b1;&#x02009;0.10</td><td align="left">&#x02009;&#x02212;&#x02009;0.09&#x02009;&#x000b1;&#x02009;0.06</td></tr><tr><td align="left">PSNR</td><td align="left">33&#x02009;&#x000b1;&#x02009;3.15</td><td align="left">32.01&#x02009;&#x000b1;&#x02009;5.39</td><td align="left">33.98&#x02009;&#x000b1;&#x02009;2.98</td><td align="left">32.88&#x02009;&#x000b1;&#x02009;3.4</td><td align="left">33.17&#x02009;&#x000b1;&#x02009;2.99</td><td align="left">33.97&#x02009;&#x000b1;&#x02009;3.24</td></tr><tr><td align="left">ARE (%)</td><td align="left">14.51&#x02009;&#x000b1;&#x02009;7.67</td><td align="left">14.1&#x02009;&#x000b1;&#x02009;6.29</td><td align="left">13.72&#x02009;&#x000b1;&#x02009;3.3</td><td align="left">13.23&#x02009;&#x000b1;&#x02009;4.46</td><td align="left">14.29&#x02009;&#x000b1;&#x02009;4.6</td><td align="left">11.21&#x02009;&#x000b1;&#x02009;2.8</td></tr><tr><td align="left">RE (%)</td><td align="left">&#x02009;&#x02212;&#x02009;4.81&#x02009;&#x000b1;&#x02009;12.9</td><td align="left">&#x02009;&#x02212;&#x02009;9.26&#x02009;&#x000b1;&#x02009;8.67</td><td align="left">&#x02009;&#x02212;&#x02009;3.36&#x02009;&#x000b1;&#x02009;10.2</td><td align="left">&#x02009;&#x02212;&#x02009;8.27&#x02009;&#x000b1;&#x02009;7.61</td><td align="left">&#x02009;&#x02212;&#x02009;3.82&#x02009;&#x000b1;&#x02009;9.7</td><td align="left">&#x02009;&#x02212;&#x02009;6.53&#x02009;&#x000b1;&#x02009;4.94</td></tr><tr><td align="left">SSIM</td><td align="left">0.91&#x02009;&#x000b1;&#x02009;0.07</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.92&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td></tr><tr><td align="left" rowspan="6">Federated PL</td><td align="left">MAE</td><td align="left">0.13&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">0.16&#x02009;&#x000b1;&#x02009;0.11</td><td align="left">0.13&#x02009;&#x000b1;&#x02009;0.04</td><td align="left">0.15&#x02009;&#x000b1;&#x02009;0.06</td><td align="left">0.14&#x02009;&#x000b1;&#x02009;0.06</td><td align="left">0.12&#x02009;&#x000b1;&#x02009;0.04</td></tr><tr><td align="left">ME</td><td align="left">&#x02009;&#x02212;&#x02009;0.09&#x02009;&#x000b1;&#x02009;0.08</td><td align="left">&#x02009;&#x02212;&#x02009;0.13&#x02009;&#x000b1;&#x02009;0.13</td><td align="left">&#x02009;&#x02212;&#x02009;0.07&#x02009;&#x000b1;&#x02009;0.09</td><td align="left">&#x02009;&#x02212;&#x02009;0.12&#x02009;&#x000b1;&#x02009;0.08</td><td align="left">&#x02009;&#x02212;&#x02009;0.08&#x02009;&#x000b1;&#x02009;0.10</td><td align="left">&#x02009;&#x02212;&#x02009;0.1&#x02009;&#x000b1;&#x02009;0.05</td></tr><tr><td align="left">PSNR</td><td align="left">33.43&#x02009;&#x000b1;&#x02009;2.89</td><td align="left">31.82&#x02009;&#x000b1;&#x02009;5.34</td><td align="left">33.9&#x02009;&#x000b1;&#x02009;2.97</td><td align="left">32.67&#x02009;&#x000b1;&#x02009;3.34</td><td align="left">33.1&#x02009;&#x000b1;&#x02009;3.01</td><td align="left">33.71&#x02009;&#x000b1;&#x02009;3.26</td></tr><tr><td align="left">ARE (%)</td><td align="left">12.22&#x02009;&#x000b1;&#x02009;3.16</td><td align="left">13.6&#x02009;&#x000b1;&#x02009;5.69</td><td align="left">13.35&#x02009;&#x000b1;&#x02009;2.96</td><td align="left">12.85&#x02009;&#x000b1;&#x02009;3.95</td><td align="left">13.95&#x02009;&#x000b1;&#x02009;4.49</td><td align="left">11.02&#x02009;&#x000b1;&#x02009;2.6</td></tr><tr><td align="left">RE (%)</td><td align="left">&#x02009;&#x02212;&#x02009;5.54&#x02009;&#x000b1;&#x02009;8.5</td><td align="left">&#x02009;&#x02212;&#x02009;8.49&#x02009;&#x000b1;&#x02009;8.2</td><td align="left">&#x02009;&#x02212;&#x02009;2.82&#x02009;&#x000b1;&#x02009;9.59</td><td align="left">&#x02009;&#x02212;&#x02009;7.65&#x02009;&#x000b1;&#x02009;7.25</td><td align="left">&#x02009;&#x02212;&#x02009;3.38&#x02009;&#x000b1;&#x02009;9.4</td><td align="left">&#x02009;&#x02212;&#x02009;5.94&#x02009;&#x000b1;&#x02009;4.89</td></tr><tr><td align="left">SSIM</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.92&#x02009;&#x000b1;&#x02009;0.01</td><td align="left">0.93&#x02009;&#x000b1;&#x02009;0.01</td></tr></tbody></table></table-wrap></p><p id="Par29">The quantitative performance of the different training strategies categorized by the clinical center is reported in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> (Supplemental Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> depicts similar information for each patient). The center-wise relative error for the CZ approach (ARE&#x02009;=&#x02009;11.16&#x02009;&#x000b1;&#x02009;3.24%) demonstrates slightly better performance compared to FL-SQ (ARE&#x02009;=&#x02009;13.51&#x02009;&#x000b1;&#x02009;5.04%) and FL-PL (ARE&#x02009;=&#x02009;12.83&#x02009;&#x000b1;&#x02009;3.91%) approaches. Conversely, ARE metric for the CB approach was larger (24.22&#x02009;&#x000b1;&#x02009;7.28%). The highest MAE was achieved by the CB method (0.21&#x02009;&#x000b1;&#x02009;0.07) compared to CZ, FL-SQ, and FL-PL which achieved values of 0.10&#x02009;&#x000b1;&#x02009;0.03, 0.14&#x02009;&#x000b1;&#x02009;0.07, and 0.14&#x02009;&#x000b1;&#x02009;0.06, respectively. For all approaches, SSIM and PSNR metrics demonstrated a consistent behavior over the different centers (0.93&#x02009;&#x000b1;&#x02009;0.02 and 34.0&#x02009;&#x000b1;&#x02009;3.23, respectively), except for CZ which achieved the poorest performance in terms of structural analysis, resulting in SSIM of 0.70&#x02009;&#x000b1;&#x02009;0.04 and PSNR of 28.66&#x02009;&#x000b1;&#x02009;2.70. Supplemental Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> depicts the quantitative performance of the different training strategies categorized by the different cases in the test dataset.<fig id="Fig4"><label>Fig. 4</label><caption><p>Quantitative performance of the different training strategies, including sequential federated learning (FL-SQ) and parallel federated learning (FL-PL), centralized (CZ), and center based (CB), categorized by clinical center</p></caption><graphic xlink:href="259_2022_6053_Fig4_HTML" id="MO4"/></fig></p><p id="Par30">Furthermore, the voxelwise joint histogram analysis depicting the correlation between the predicted and CT-ASC images serving as ground truth is illustrated in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>. The coefficient of determination (<italic>R</italic><sup>2</sup>) achieved by CB, CZ, FL-Sq, and FL-PL methods were 0.76, 0.94, 0.93, and 0.92, respectively.<fig id="Fig5"><label>Fig. 5</label><caption><p>Voxelwise joint histogram analysis depicting the correlation between the predicted images using the different training approaches and CT-ASC images serving as ground truth</p></caption><graphic xlink:href="259_2022_6053_Fig5_HTML" id="MO5"/></fig></p><p id="Par31">The results of the statistical analysis between the different learning strategies in the form of center-based categorization are summarized in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>. As illustrated, the CB approach is significantly different from the other algorithms as reflected by almost all quantitative parameters, except for RE and ME. The CZ model performance demonstrates consistent behavior against FL algorithms in almost all parameters in center-based categorization (<italic>p</italic>-value&#x02009;&#x0003e;&#x02009;0.05).<fig id="Fig6"><label>Fig. 6</label><caption><p>Statistical analysis between the different learning strategies in the form of CB as well as centralized CZ and FL approaches for the different quantitative metrics reflecting evaluation on the overall data as well as on data from each center. Blue and red colors indicate <italic>p</italic>-value&#x02009;&#x0003c;&#x02009;0.05 and <italic>p</italic>-value&#x02009;&#x0003e;&#x02009;0.05, respectively. Abbreviations: sequential (FL-SQ) and parallel federated learning (FL-PL), centralized (CZ), and center-based (CB) learning</p></caption><graphic xlink:href="259_2022_6053_Fig6_HTML" id="MO6"/></fig></p></sec><sec id="Sec9"><title>Discussion</title><p id="Par32">DL approaches are data-hungry algorithms that require large, reliable datasets to generate robust and generalizable models [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. However, the collection of large, centralized datasets for training DL models is challenging and not always feasible owing to the sensitivity of clinical datasets and specifically medical images [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. FL algorithms provide the opportunity to train a model using multicentric datasets without sharing data [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. In this work, we provide a framework for DL-based AC/SC model generation from PET images from different centers without the direct sharing of clinical datasets. Our FL-based DL models provided promising results which could improve model generalizability and robustness for AC/SC of PET images without sharing dataset in multicentric studies.</p><p id="Par33">The quantitative analysis performed on SUV PET images demonstrated highly reproducible performance against intra/inter-patient variability. In terms of SUV quantification bias, the ARE% metric demonstrated excellent agreement between FL-SQ (CI:12.21&#x02013;14.81%) and FL-PL (CI:11.82&#x02013;13.84%) models and conventional centralized training approach (CI:10.32&#x02013;12.00%), while FL-based algorithms improved model performance in terms of ARE by more than 11% compared to CB training strategy (CI: 22.34&#x02013;26.10%). The center-based voxel-wise quantitative analysis and structural indices (Figs. <xref rid="Fig3" ref-type="fig">3</xref> and <xref rid="Fig4" ref-type="fig">4</xref>) illustrated the superior performance of FL-based algorithms compared to the CB approach. Furthermore, although in the center-categorized mode, the Mann&#x02013;Whitney test between different strategies (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>) revealed consistency between CZ and FL-based algorithms (<italic>p</italic>-value&#x02009;&#x0003e;&#x02009;0.05) on the overall dataset, the statistical analysis demonstrated significant differences between the different training approaches (<italic>p</italic>-value&#x02009;&#x0003c;&#x02009;0.05). In addition, the joint histogram analysis (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>), depicting a voxel-wise comparison between reference CT-ASC and predicted images, exhibited close performance between CZ (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.94), FL-SQ (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.93), and FL-PL (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.92), while the CB model achieved a far lower coefficient of determination (<italic>R</italic><sup>2</sup>&#x02009;=&#x02009;0.74). Despite the strong correlation coefficient between CZ and FL-based methods compared to reference CT-ASC, a slight underestimation of the predicted tracer uptake was observed. Even though slightly inferior results were observed in some CB models, which could be attributed to the different number of slices used in the training of the models (as reflected in Table <xref rid="Tab1" ref-type="table">1</xref>). Overall, all CB models exhibited very similar values of quantitative errors.</p><p id="Par34">In a previous study in the context of conventional centralized learning [<xref ref-type="bibr" rid="CR20">20</xref>], direct AC/SC of PET images using a modified ResNet algorithm achieved good performance (MAE&#x02009;=&#x02009;0.22&#x02009;&#x000b1;&#x02009;0.09 and ARE&#x02009;=&#x02009;11.61&#x02009;&#x000b1;&#x02009;4.25%) using a large dataset (1150 patient images), as gathered from one center and single PET scanner [<xref ref-type="bibr" rid="CR20">20</xref>]. We then further improved the performance of our algorithm by developing a modified bilevel nested U-NET architecture inspired by U<sup>2</sup>-Net applied for object detection from natural images (centralized mode: MAE&#x02009;=&#x02009;0.10&#x02009;&#x000b1;&#x02009;0.03 and ARE&#x02009;=&#x02009;11.16&#x02009;&#x000b1;&#x02009;3.24%). In our previous study [<xref ref-type="bibr" rid="CR20">20</xref>], DL-based attenuation and scatter correction in the image domain were extensively evaluated on 150 clinical cases including quantitative analysis of radiotracer uptake in 170 lesions/abnormal high-uptake regions (colorectal, head and neck, lung, lymphoma, &#x02026;). A mean relative SUV error of less than 5% was observed for SUV<sub>max</sub> and SUV<sub>mean</sub> across all lesions/regions. Although the quantitative analysis was not performed on malignant lesions in the current study, the voxel-wise SUV error for CZ and FL algorithms was within the same range as our previous study [<xref ref-type="bibr" rid="CR20">20</xref>].</p><p id="Par35">In comparison to previous works, Yang et al. [<xref ref-type="bibr" rid="CR63">63</xref>] reported an average ARE of 16.55&#x02009;&#x000b1;&#x02009;4.43% for AC/SC of whole-body PET images using 3D generative adversarial networks. Dong et al. [<xref ref-type="bibr" rid="CR64">64</xref>] tested different network architectures (U-Net, GAN, and cycle-GAN) achieving good performance in terms of ME (0.62&#x02009;&#x000b1;&#x02009;1.26%) and normalized mean square error (0.72&#x02009;&#x000b1;&#x02009;0.34%), respectively. Van Hemmen et al. [<xref ref-type="bibr" rid="CR65">65</xref>] developed a modified U-Net architecture for AC/SC of whole body PET images only images resulting in an average ARE of 28.2% on a small-scale dataset. Hwang et al. [<xref ref-type="bibr" rid="CR66">66</xref>] compared different PET attenuation correction approaches using emission data, including DL-based &#x003bc;-map generation from non-attenuation-corrected (NAC) images, improved estimation of &#x003bc;-maps using maximum likelihood estimation of activity and attenuation (MLAA) and a combination of these two methods. They reported that the combination of the MLAA algorithm and DL approach outperformed &#x003bc;-maps estimated from NAC PET images, whereas no improvement was observed when combining these two approaches. Apart from direct AC/SC on PET images, a number of studies reported on MRI-guided AC/SC by generating pseudo-CT images from PET/MR images and attenuation maps based on tissue classification from PET-only images [<xref ref-type="bibr" rid="CR67">67</xref>&#x02013;<xref ref-type="bibr" rid="CR69">69</xref>]. Although the synthesized attenuation map-based approaches demonstrate promising results, they suffer from numerous challenges, including a mismatch between anatomical (MRI) and PET images and organ motion [<xref ref-type="bibr" rid="CR67">67</xref>&#x02013;<xref ref-type="bibr" rid="CR69">69</xref>]. However, the direct AC/SC approach is less sensitive to noise, metal artifacts, truncation, and local mismatch between anatomical and functional images [<xref ref-type="bibr" rid="CR70">70</xref>, <xref ref-type="bibr" rid="CR71">71</xref>]. Furthermore, this approach is potentially capable of correcting for organ motion and hollow artifacts provided that the model is trained on a clean and accurately corrected PET images [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR70">70</xref>].</p><p id="Par36">Although direct attenuation/scatter correction in the image domain has a number of advantages, the generation of pseudo &#x000b5;-maps (synthetic CT) from non-attenuation corrected images or MR images would provide an explainable AC map to verify/detect errors/drawbacks within PET attenuation and scatter correction procedures [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR66">66</xref>, <xref ref-type="bibr" rid="CR72">72</xref>, <xref ref-type="bibr" rid="CR73">73</xref>]. The suboptimal performance of direct AC approaches cannot be easily depicted from the resulting PET-AC images (local under/over estimation of radiotracer uptake). However, the suboptimal performance of DL-based synthetic CT generation approaches could be visually detected from the resulting synthetic &#x000b5;-maps. The resulting synthetic CT images could be visually checked to detect any possible anatomical defects and/or artifacts prior to PET attenuation correction. The other drawback of direct AC in the image domain is the sensitivity of the models to the quality of the query data wherein increased levels of noise, abnormalities, and minor image artifacts may result in erroneous signals in the resulting images. Moreover, the occurrence of outliers (cases with gross errors) in the outcome of these models should be carefully monitored (owing to black-box nature of DL models).</p><p id="Par37">Different studies have been recently performed to assess the performance of FL approaches in medical image analysis [<xref ref-type="bibr" rid="CR74">74</xref>]. In a study by Feki et al.[<xref ref-type="bibr" rid="CR36">36</xref>], COVID-19 detection from chest X-ray images using VGG16 and ResNet50 tested federated and centralized frameworks, reporting similar performance for both models. In a more recent study [<xref ref-type="bibr" rid="CR37">37</xref>], an FL-based model, referred to as EXAM, was developed based on vital signs laboratory exams and chest X-rays for future oxygen requirements of COVID-19 patients across twenty centers. They compared the FL-based model with the center-based model (where each center developed and evaluated the model separately) achieving 16% and 38% improvement in average AUC and generalizability, respectively. Gawali et al. [<xref ref-type="bibr" rid="CR75">75</xref>] compared different privacy-preserving DL methods for chest X-ray classification tasks. They reported an AUROC of 0.95/0.72 and an F1 score of 0.93/0.62 for a DenseNet model trained in a centralized way and their best-performing FL approach, respectively. In our study, we evaluated two FL-based models and achieved better and comparable results for CB and CZ models, respectively. Building a generalizable and robust model requires a large dataset, while privacy concerns could be addressed by FL approaches without sacrificing models&#x02019; performance. In a more recent study, Shiri et al. [<xref ref-type="bibr" rid="CR76">76</xref>] proposed a FL based multi-institutional PET image segmentation framework on head and neck studies. They enrolled 404 patients from eight different centers and reported that FL-based algorithms outperformed CB and achieved similar performance as the CZ approach.</p><p id="Par38">The FL paradigm enables the training of machine/DL models on multiple decentralized datasets without the need for exchanging data. This preserves data privacy, data security, and data access rights while allowing access to the large-scale heterogeneous database for model training [<xref ref-type="bibr" rid="CR77">77</xref>]. In the FL framework, the local data are not made available to other participants, or on the server. However, a curious server may infer sensitive data from the exchanged model parameters. In the literature, various attacks have been investigated against machine learning models [<xref ref-type="bibr" rid="CR78">78</xref>, <xref ref-type="bibr" rid="CR79">79</xref>]. For instance, Shokri et al. [<xref ref-type="bibr" rid="CR80">80</xref>] studied membership inference attacks, whereas Fredrikson et al. [<xref ref-type="bibr" rid="CR81">81</xref>] addressed model inversion attacks. Possible threats can be classified into three categories, depending on the stage of the process of an FL system. Malicious parties can perform data poisoning attacks at the &#x0201c;input&#x0201d; of the learning model [<xref ref-type="bibr" rid="CR82">82</xref>, <xref ref-type="bibr" rid="CR83">83</xref>]. For instance, they may modify the label of the data samples. Alternatively, they can perform model poisoning attacks during the learning process [<xref ref-type="bibr" rid="CR84">84</xref>, <xref ref-type="bibr" rid="CR85">85</xref>]. For example, they may upload random updates to the global model. Finally, a malicious party can perform inference attacks on the &#x0201c;released learnt model&#x0201d; [<xref ref-type="bibr" rid="CR77">77</xref>, <xref ref-type="bibr" rid="CR80">80</xref>, <xref ref-type="bibr" rid="CR81">81</xref>]. For instance, a curious server may infer sensitive information about the training data from the communicated model parameters.</p><p id="Par39">CB framework faces generalizability challenges even with large datasets owing to the large variability across different centers in terms of scanner brands, data acquisition and reconstruction protocols, and post-processing schemes. Moreover, due to the absence of infrastructures and expertise, it may not possible to build ML models at each center. The CZ training framework is the ideal option for ML model development. Yet, it suffers from limitations imposed by ethical and legal constraints. FL algorithms provide the opportunity to train a model using multicentric datasets without sharing data, and models trained with the FL framework can converge to the CZ performance in the ideal situation. Overall, the CZ model training would lead to the highest accuracy and generalizability. However, in cases where ethical and legal constraints do not allow data sharing or when a center does not have enough training samples FL approaches would be an attractive solution. Models trained with FL in the best-case scenario might approach the performance of the CZ models. On the other hand, CB models are observed to suffer from very poor generalizability.</p><p id="Par40">Data heterogeneity arising from the use of different scanners, image acquisition and reconstruction protocols, is the main source of error impairing building a generalizable model [<xref ref-type="bibr" rid="CR17">17</xref>]. The heterogeneity of data across the different centers prevents CB models from working properly on an unseen dataset from the other centers. To build a generalizable model, data from different centers should be included in the training dataset, which is possible in CZ and FL pipelines. In FL, a global model is built based on a portion of the data from the different centers in a federated approach and then for each center, the global model will be specialized for each center by applying a transfer learning technique using a transfer-FL framework [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. This approach could be employed to comply with the heterogeneous data collected from the different centers with various acquisition parameters.</p><p id="Par41">This study inherently bears a number of limitations. The implementation of all models was performed on a server using different GPUs where the different nodes were considered as centers similar to previous FL studies [<xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR36">36</xref>, <xref ref-type="bibr" rid="CR54">54</xref>&#x02013;<xref ref-type="bibr" rid="CR59">59</xref>, <xref ref-type="bibr" rid="CR74">74</xref>, <xref ref-type="bibr" rid="CR76">76</xref>]. The challenges of FL, such as local computer capacity, and communication bottleneck between centers and local server should be considered in the real clinical scenario. Further studies should be performed in real clinical situations using a larger size of the training dataset. In the current version, a proof-of-concept has been demonstrated and further investigation with larger cohorts is warranted. One limitation of FL is data preparation and preprocessing due to the nature of the decentralized process. However, for image preprocessing, including normalization, we used an easy method to ensure reproducibility.</p></sec><sec id="Sec10"><title>Conclusion</title><p id="Par42">AC/SC are key corrections required to enable quantitative PET imaging, which remains challenging on CT-less PET scanners (PET/MRI and standalone PET-only scanners). DL-based models provide very promising results and might outperform conventional algorithms in terms of attenuation and scatter corrections. At the same time, robust and generalizable DL models require heterogeneous, large, and reliable datasets from multiple centers. Yet, legal/ethical/privacy considerations prevent the collection of very large datasets. In this work, we developed an FL-based framework for anatomical knowledge free or CT-less AC/SC of PET images, which proved to outperform center-based models, demonstrating comparable performance with respect to centralized DL. FL-based DL provided promising results through improving model generalizability and robustness for AC/SC of PET images without direct sharing of datasets amongst centers.</p></sec><sec sec-type="supplementary-material"><title>Supplementary Information</title><sec id="Sec11"><p>Below is the link to the electronic supplementary material.<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="259_2022_6053_MOESM1_ESM.pdf"><caption><p>Supplementary file1 (PDF 513 kb)</p></caption></media></supplementary-material></p></sec></sec></body><back><fn-group><fn><p>This article is part of the Topical Collection on Advanced Image Analyses (Radiomics and Artificial Intelligence).</p></fn><fn><p><bold>Publisher's note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><notes notes-type="funding-information"><title>Funding</title><p>Open access funding provided by University of Geneva. This work was supported by the Swiss National Science Foundation under grant SNSF 320030_176052, the Eurostars program of the European Commission under grant E! 114021 ProVision, and the Private Foundation of Geneva University Hospitals under Grant RC-06&#x02013;01. The codes and models developed in this work are available upon request.</p></notes><notes notes-type="data-availability"><title>Data availability</title><p>Data are available from The Cancer Imaging Archive (TCIA) from refereces of [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR47">47</xref>].</p></notes><notes><title>Declarations</title><notes id="FPar1"><title>Ethics approval</title><p id="Par43">All procedures performed in studies involving human participants were in accordance with the ethical standards of the institutional and/or national research committee and with the 1964 Helsinki declaration and its later amendments or comparable ethical standards.</p></notes><notes id="FPar2"><title>Informed consent</title><p id="Par44">Informed consent was obtained from all individual participants included in the study.</p></notes><notes id="FPar3" notes-type="COI-statement"><title>Conflict of interest</title><p id="Par45">The authors declare no competing interests.</p></notes></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Blodgett</surname><given-names>TM</given-names></name><name><surname>Meltzer</surname><given-names>CC</given-names></name><name><surname>Townsend</surname><given-names>DW</given-names></name></person-group><article-title>PET/CT: Form and function</article-title><source>Radiology</source><year>2007</year><volume>242</volume><fpage>360</fpage><lpage>385</lpage><pub-id pub-id-type="doi">10.1148/radiol.2422051113</pub-id><?supplied-pmid 17255408?><pub-id pub-id-type="pmid">17255408</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zaidi</surname><given-names>H</given-names></name><name><surname>Montandon</surname><given-names>ML</given-names></name><name><surname>Meikle</surname><given-names>S</given-names></name></person-group><article-title>Strategies for attenuation compensation in neurological PET studies</article-title><source>Neuroimage</source><year>2007</year><volume>34</volume><fpage>518</fpage><lpage>541</lpage><pub-id pub-id-type="doi">10.1016/j.neuroimage.2006.10.002</pub-id><?supplied-pmid 17113312?><pub-id pub-id-type="pmid">17113312</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zaidi</surname><given-names>H</given-names></name><name><surname>Karakatsanis</surname><given-names>N</given-names></name></person-group><article-title>Towards enhanced PET quantification in clinical oncology</article-title><source>Br J Radiol</source><year>2018</year><volume>91</volume><fpage>20170508</fpage><pub-id pub-id-type="doi">10.1259/bjr.20170508</pub-id><?supplied-pmid 29164924?><pub-id pub-id-type="pmid">29164924</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zaidi</surname><given-names>H</given-names></name><name><surname>Hasegawa</surname><given-names>B</given-names></name></person-group><article-title>Determination of the attenuation map in emission tomography</article-title><source>J Nucl Med</source><year>2003</year><volume>44</volume><fpage>291</fpage><lpage>315</lpage><?supplied-pmid 12571222?><pub-id pub-id-type="pmid">12571222</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zaidi</surname><given-names>H</given-names></name><name><surname>Koral</surname><given-names>KF</given-names></name></person-group><article-title>Scatter modelling and compensation in emission tomography</article-title><source>Eur J Nucl Med Mol Imaging</source><year>2004</year><volume>31</volume><fpage>761</fpage><lpage>782</lpage><pub-id pub-id-type="doi">10.1007/s00259-004-1495-z</pub-id><?supplied-pmid 15057488?><pub-id pub-id-type="pmid">15057488</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zaidi</surname><given-names>H</given-names></name><name><surname>Montandon</surname><given-names>ML</given-names></name></person-group><article-title>Scatter compensation techniques in PET</article-title><source>PET Clin</source><year>2007</year><volume>2</volume><fpage>219</fpage><lpage>234</lpage><pub-id pub-id-type="doi">10.1016/j.cpet.2007.10.003</pub-id><?supplied-pmid 27157874?><pub-id pub-id-type="pmid">27157874</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Akbarzadeh</surname><given-names>A</given-names></name><name><surname>Ay</surname><given-names>MR</given-names></name><name><surname>Ahmadian</surname><given-names>A</given-names></name><name><surname>Alam</surname><given-names>NR</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>MRI-guided attenuation correction in whole-body PET/MR: assessment of the effect of bone attenuation</article-title><source>Ann Nucl Med</source><year>2013</year><volume>27</volume><fpage>152</fpage><lpage>162</lpage><pub-id pub-id-type="doi">10.1007/s12149-012-0667-3</pub-id><?supplied-pmid 23264064?><pub-id pub-id-type="pmid">23264064</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mehranian</surname><given-names>A</given-names></name><name><surname>Arabi</surname><given-names>H</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>Vision 20/20: Magnetic resonance imaging-guided attenuation correction in PET/MRI: challenges, solutions, and opportunities</article-title><source>Med Phys</source><year>2016</year><volume>43</volume><fpage>1130</fpage><lpage>1155</lpage><pub-id pub-id-type="doi">10.1118/1.4941014</pub-id><?supplied-pmid 26936700?><pub-id pub-id-type="pmid">26936700</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berker</surname><given-names>Y</given-names></name><name><surname>Li</surname><given-names>Y</given-names></name></person-group><article-title>Attenuation correction in emission tomography using the emission data&#x02013;a review</article-title><source>Med Phys</source><year>2016</year><volume>43</volume><fpage>807</fpage><lpage>832</lpage><pub-id pub-id-type="doi">10.1118/1.4938264</pub-id><?supplied-pmid 26843243?><pub-id pub-id-type="pmid">26843243</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Arabi</surname><given-names>H</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>Deep learning-guided estimation of attenuation correction factors from time-of-flight PET emission data</article-title><source>Med Image Anal</source><year>2020</year><volume>64</volume><fpage>101718</fpage><pub-id pub-id-type="doi">10.1016/j.media.2020.101718</pub-id><?supplied-pmid 32492585?><pub-id pub-id-type="pmid">32492585</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Arabi</surname><given-names>H</given-names></name><name><surname>Sanaat</surname><given-names>A</given-names></name><name><surname>Jenabi</surname><given-names>E</given-names></name><name><surname>Becker</surname><given-names>M</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>Fully automated gross tumour volume delineation from PET in head and neck cancer using deep learning algorithms</article-title><source>Clin Nucl Med</source><year>2021</year><volume>46</volume><fpage>872</fpage><lpage>883</lpage><pub-id pub-id-type="doi">10.1097/rlu.0000000000003789</pub-id><?supplied-pmid 34238799?><pub-id pub-id-type="pmid">34238799</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><mixed-citation publication-type="other">Yousefirizi F, Decasez P, Amyar A, Ruan S, Saboury B, Rahmim A. Artificial intelligence-based detection, classification and prediction/prognosis in PET imaging: towards radiophenomics. arXiv preprint arXiv:211010332. 2021.</mixed-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mohammadi</surname><given-names>R</given-names></name><name><surname>Shokatian</surname><given-names>I</given-names></name><name><surname>Salehi</surname><given-names>M</given-names></name><name><surname>Arabi</surname><given-names>H</given-names></name><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>Deep learning-based auto-segmentation of organs at risk in high-dose rate brachytherapy of cervical cancer</article-title><source>Radiother Oncol</source><year>2021</year><volume>159</volume><fpage>231</fpage><lpage>240</lpage><pub-id pub-id-type="doi">10.1016/j.radonc.2021.03.030</pub-id><?supplied-pmid 33831446?><pub-id pub-id-type="pmid">33831446</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Salimi</surname><given-names>Y</given-names></name><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Akhavanallaf</surname><given-names>A</given-names></name><name><surname>Mansouri</surname><given-names>Z</given-names></name><name><surname>Saberi Manesh</surname><given-names>A</given-names></name><name><surname>Sanaat</surname><given-names>A</given-names></name><etal/></person-group><article-title>Deep learning-based fully automated Z-axis coverage range definition from scout scans to eliminate overscanning in chest CT imaging</article-title><source>Insights Imaging</source><year>2021</year><volume>12</volume><fpage>162</fpage><pub-id pub-id-type="doi">10.1186/s13244-021-01105-3</pub-id><?supplied-pmid 34743251?><pub-id pub-id-type="pmid">34743251</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sanaat</surname><given-names>A</given-names></name><name><surname>Shooli</surname><given-names>H</given-names></name><name><surname>Ferdowsi</surname><given-names>S</given-names></name><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Arabi</surname><given-names>H</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>DeepTOFSino: a deep learning model for synthesizing full-dose time-of-flight bin sinograms from their corresponding low-dose sinograms</article-title><source>Neuroimage</source><year>2021</year><volume>245</volume><fpage>118697</fpage><pub-id pub-id-type="doi">10.1016/j.neuroimage.2021.118697</pub-id><?supplied-pmid 34742941?><pub-id pub-id-type="pmid">34742941</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sanaat</surname><given-names>A</given-names></name><name><surname>Akhavanalaf</surname><given-names>A</given-names></name><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Salimi</surname><given-names>Y</given-names></name><name><surname>Arabi</surname><given-names>H</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>Deep-TOF-PET: Deep learning-guided generation of time-of-flight from non-TOF brain PET images in the image and projection domains</article-title><source>Hum Brain Mapp</source><year>2022</year><pub-id pub-id-type="doi">10.1002/hbm.26068</pub-id><?supplied-pmid 36087092?><pub-id pub-id-type="pmid">36087092</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jabbarpour</surname><given-names>A</given-names></name><name><surname>Mahdavi</surname><given-names>SR</given-names></name><name><surname>Vafaei Sadr</surname><given-names>A</given-names></name><name><surname>Esmaili</surname><given-names>G</given-names></name><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Zaidi</surname><given-names>H</given-names></name></person-group><article-title>Unsupervised pseudo CT generation using heterogenous multicentric CT/MR images and CycleGAN: dosimetric assessment for 3D conformal radiotherapy</article-title><source>Comput Biol Med</source><year>2022</year><volume>143</volume><fpage>105277</fpage><pub-id pub-id-type="doi">10.1016/j.compbiomed.2022.105277</pub-id><?supplied-pmid 35123139?><pub-id pub-id-type="pmid">35123139</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Armanious</surname><given-names>K</given-names></name><name><surname>Hepp</surname><given-names>T</given-names></name><name><surname>K&#x000fc;stner</surname><given-names>T</given-names></name><name><surname>Dittmann</surname><given-names>H</given-names></name><name><surname>Nikolaou</surname><given-names>K</given-names></name><name><surname>La Foug&#x000e8;re</surname><given-names>C</given-names></name><etal/></person-group><article-title>Independent attenuation correction of whole body [(18)F]FDG-PET using a deep learning approach with Generative Adversarial Networks</article-title><source>EJNMMI Res</source><year>2020</year><volume>10</volume><fpage>53</fpage><pub-id pub-id-type="doi">10.1186/s13550-020-00644-y</pub-id><?supplied-pmid 32449036?><pub-id pub-id-type="pmid">32449036</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><mixed-citation publication-type="other">Qian H, Rui X, Ahn S. Deep learning models for PET scatter estimations. 2017 IEEE Nuclear Science Symposium and Medical Imaging Conference (NSS/MIC); 2017. p. 1&#x02013;5.</mixed-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Arabi</surname><given-names>H</given-names></name><name><surname>Geramifar</surname><given-names>P</given-names></name><name><surname>Hajianfar</surname><given-names>G</given-names></name><name><surname>Ghafarian</surname><given-names>P</given-names></name><name><surname>Rahmim</surname><given-names>A</given-names></name><etal/></person-group><article-title>Deep-JASC: joint attenuation and scatter correction in whole-body (18)F-FDG PET using a deep residual network</article-title><source>Eur J Nucl Med Mol Imaging</source><year>2020</year><volume>47</volume><fpage>2533</fpage><lpage>2548</lpage><pub-id pub-id-type="doi">10.1007/s00259-020-04852-5</pub-id><?supplied-pmid 32415552?><pub-id pub-id-type="pmid">32415552</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McMillan</surname><given-names>AB</given-names></name><name><surname>Bradshaw</surname><given-names>TJ</given-names></name></person-group><article-title>Artificial Intelligence-based data corrections for attenuation and scatter in position emission tomography and single-photon emission computed tomography</article-title><source>PET Clin</source><year>2021</year><volume>16</volume><fpage>543</fpage><lpage>552</lpage><pub-id pub-id-type="doi">10.1016/j.cpet.2021.06.010</pub-id><?supplied-pmid 34364816?><pub-id pub-id-type="pmid">34364816</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rieke</surname><given-names>N</given-names></name><name><surname>Hancox</surname><given-names>J</given-names></name><name><surname>Li</surname><given-names>W</given-names></name><name><surname>Milletar&#x000ec;</surname><given-names>F</given-names></name><name><surname>Roth</surname><given-names>HR</given-names></name><name><surname>Albarqouni</surname><given-names>S</given-names></name><etal/></person-group><article-title>The future of digital health with federated learning</article-title><source>NPJ Digit Med</source><year>2020</year><volume>3</volume><fpage>119</fpage><pub-id pub-id-type="doi">10.1038/s41746-020-00323-1</pub-id><?supplied-pmid 33015372?><pub-id pub-id-type="pmid">33015372</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kaissis</surname><given-names>GA</given-names></name><name><surname>Makowski</surname><given-names>MR</given-names></name><name><surname>R&#x000fc;ckert</surname><given-names>D</given-names></name><name><surname>Braren</surname><given-names>RF</given-names></name></person-group><article-title>Secure, privacy-preserving and federated machine learning in medical imaging</article-title><source>Nat Mach Intell</source><year>2020</year><volume>2</volume><fpage>305</fpage><lpage>311</lpage><pub-id pub-id-type="doi">10.1038/s42256-020-0186-1</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kirienko</surname><given-names>M</given-names></name><name><surname>Sollini</surname><given-names>M</given-names></name><name><surname>Ninatti</surname><given-names>G</given-names></name><name><surname>Loiacono</surname><given-names>D</given-names></name><name><surname>Giacomello</surname><given-names>E</given-names></name><name><surname>Gozzi</surname><given-names>N</given-names></name><etal/></person-group><article-title>Distributed learning: a reliable privacy-preserving strategy to change multicenter collaborations using AI</article-title><source>Eur J Nucl Med Mol Imaging</source><year>2021</year><volume>48</volume><fpage>3791</fpage><lpage>3804</lpage><pub-id pub-id-type="doi">10.1007/s00259-021-05339-7</pub-id><?supplied-pmid 33847779?><pub-id pub-id-type="pmid">33847779</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><mixed-citation publication-type="other">Navid Hasani MAM, Arman Rhamim, Ronald M. Summers, Elizabeth Jones, Eliot Siegel, Babak Saboury. Trustworthy artificial intelligence in medical imaging. PET Clin. 2021:17:1&#x02013;12.</mixed-citation></ref><ref id="CR26"><label>26.</label><mixed-citation publication-type="other">Li Q, Wen Z, Wu Z, Hu S, Wang N, Li Y, et al. A survey on federated learning systems: vision, hype and reality for data privacy and protection. arXiv preprint arXiv:190709693. 2019.</mixed-citation></ref><ref id="CR27"><label>27.</label><mixed-citation publication-type="other">Jorge VAM, Granada R, Maidana RG, Jurak DA, Heck G, Negreiros APF, et al. A survey on unmanned surface vehicles for disaster robotics: main challenges and directions. Sensors (Basel). 2019;19. 10.3390/s19030702.</mixed-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shyu</surname><given-names>C-R</given-names></name><name><surname>Putra</surname><given-names>KT</given-names></name><name><surname>Chen</surname><given-names>H-C</given-names></name><name><surname>Tsai</surname><given-names>Y-Y</given-names></name><name><surname>Hossain</surname><given-names>KT</given-names></name><name><surname>Jiang</surname><given-names>W</given-names></name><etal/></person-group><article-title>A systematic review of federated learning in the healthcare area: from the perspective of data properties and applications</article-title><source>Appl Sci</source><year>2021</year><volume>11</volume><fpage>11191</fpage><pub-id pub-id-type="doi">10.3390/app112311191</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><mixed-citation publication-type="other">Kone&#x0010d;n&#x000fd; J, McMahan HB, Yu FX, Richt&#x000e1;rik P, Suresh AT, Bacon D. Federated learning: strategies for improving communication efficiency. arXiv preprint arXiv:161005492. 2016.</mixed-citation></ref><ref id="CR30"><label>30.</label><mixed-citation publication-type="other">Singh A, Vepakomma P, Gupta O, Raskar R. Detailed comparison of communication efficiency of split learning and federated learning. arXiv preprint arXiv:190909145. 2019.</mixed-citation></ref><ref id="CR31"><label>31.</label><mixed-citation publication-type="other">Luping W, Wei W, Bo L. CMFL: Mitigating communication overhead for federated learning. 2019 IEEE 39th International Conference on Distributed Computing Systems (ICDCS): 2019. p. 954&#x02013;64.</mixed-citation></ref><ref id="CR32"><label>32.</label><mixed-citation publication-type="other">Amiri MM, Gunduz D, Kulkarni SR, Poor HV. Federated learning with quantized global model updates. arXiv preprint arXiv:200610672. 2020.</mixed-citation></ref><ref id="CR33"><label>33.</label><mixed-citation publication-type="other">Li W, Milletar&#x000ec; F, Xu D, Rieke N, Hancox J, Zhu W, et al. Privacy-preserving federated brain tumour segmentation. International workshop on machine learning in medical imaging: Springer; 2019. p. 133&#x02013;41.</mixed-citation></ref><ref id="CR34"><label>34.</label><mixed-citation publication-type="other">Xia Y, Yang D, Li W, Myronenko A, Xu D, Obinata H, et al. Auto-FedAvg: learnable federated averaging for multi-institutional medical image segmentation. arXiv preprint:210410195. 2021.</mixed-citation></ref><ref id="CR35"><label>35.</label><mixed-citation publication-type="other">Shiri I, Amini M, Salimi Y, Sanaat A, Saberi A, Razeghi B, et al. Multi-institutional PET/CT image segmentation using a decentralized federated deep transformer learning algorithm. J Nucl Med; 2022;63(Suppl2):3348.</mixed-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Feki</surname><given-names>I</given-names></name><name><surname>Ammar</surname><given-names>S</given-names></name><name><surname>Kessentini</surname><given-names>Y</given-names></name><name><surname>Muhammad</surname><given-names>K</given-names></name></person-group><article-title>Federated learning for COVID-19 screening from Chest X-ray images</article-title><source>Appl Soft Comput</source><year>2021</year><volume>106</volume><fpage>107330</fpage><pub-id pub-id-type="doi">10.1016/j.asoc.2021.107330</pub-id><?supplied-pmid 33776607?><pub-id pub-id-type="pmid">33776607</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dayan</surname><given-names>I</given-names></name><name><surname>Roth</surname><given-names>HR</given-names></name><name><surname>Zhong</surname><given-names>A</given-names></name><name><surname>Harouni</surname><given-names>A</given-names></name><name><surname>Gentili</surname><given-names>A</given-names></name><name><surname>Abidin</surname><given-names>AZ</given-names></name><etal/></person-group><article-title>Federated learning for predicting clinical outcomes in patients with COVID-19</article-title><source>Nat Med</source><year>2021</year><volume>27</volume><fpage>1135</fpage><lpage>1143</lpage><pub-id pub-id-type="doi">10.1038/s41591-021-01506-3</pub-id><pub-id pub-id-type="pmid">34183834</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><mixed-citation publication-type="other">Roth HR, Chang K, Singh P, Neumark N, Li W, Gupta V, et al. Federated learning for breast density classification: a real-world implementation. In: Domain Adaptation and Representation Transfer, and Distributed and Collaborative Learning: Lecture Notes in Computer Science, Vol. 12444. Springer, Cham. 2020. pp. 181&#x02013;91. 10.1007/978-3-030-60548-3_18</mixed-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Clark</surname><given-names>K</given-names></name><name><surname>Vendt</surname><given-names>B</given-names></name><name><surname>Smith</surname><given-names>K</given-names></name><name><surname>Freymann</surname><given-names>J</given-names></name><name><surname>Kirby</surname><given-names>J</given-names></name><name><surname>Koppel</surname><given-names>P</given-names></name><etal/></person-group><article-title>The Cancer Imaging Archive (TCIA): maintaining and operating a public information repository</article-title><source>J Digit Imaging</source><year>2013</year><volume>26</volume><fpage>1045</fpage><lpage>1057</lpage><pub-id pub-id-type="doi">10.1007/s10278-013-9622-7</pub-id><?supplied-pmid 23884657?><pub-id pub-id-type="pmid">23884657</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Machtay</surname><given-names>M</given-names></name><name><surname>Duan</surname><given-names>F</given-names></name><name><surname>Siegel</surname><given-names>BA</given-names></name><name><surname>Snyder</surname><given-names>BS</given-names></name><name><surname>Gorelick</surname><given-names>JJ</given-names></name><name><surname>Reddin</surname><given-names>JS</given-names></name><etal/></person-group><article-title>Prediction of survival by [18F]fluorodeoxyglucose positron emission tomography in patients with locally advanced non-small-cell lung cancer undergoing definitive chemoradiation therapy: results of the ACRIN 6668/RTOG 0235 trial</article-title><source>J Clin Oncol</source><year>2013</year><volume>31</volume><fpage>3823</fpage><lpage>3830</lpage><pub-id pub-id-type="doi">10.1200/jco.2012.47.5947</pub-id><?supplied-pmid 24043740?><pub-id pub-id-type="pmid">24043740</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><mixed-citation publication-type="other">Kinahan P, Muzi M, Bialecki B, Herman B, Coombs L. Data from the ACRIN 6668 Trial NSCLC-FDG-PET. Cancer Imaging Arch. 2019;10. 10.7937/tcia.2019.30ilqfcl</mixed-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bakr</surname><given-names>S</given-names></name><name><surname>Gevaert</surname><given-names>O</given-names></name><name><surname>Echegaray</surname><given-names>S</given-names></name><name><surname>Ayers</surname><given-names>K</given-names></name><name><surname>Zhou</surname><given-names>M</given-names></name><name><surname>Shafiq</surname><given-names>M</given-names></name><etal/></person-group><article-title>Data for NSCLC radiogenomics collection</article-title><source>The Cancer Imaging Archive</source><year>2017</year><pub-id pub-id-type="doi">10.7937/K9/TCIA.2017.7hs46erv</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bakr</surname><given-names>S</given-names></name><name><surname>Gevaert</surname><given-names>O</given-names></name><name><surname>Echegaray</surname><given-names>S</given-names></name><name><surname>Ayers</surname><given-names>K</given-names></name><name><surname>Zhou</surname><given-names>M</given-names></name><name><surname>Shafiq</surname><given-names>M</given-names></name><etal/></person-group><article-title>A radiogenomic dataset of non-small cell lung cancer</article-title><source>Sci Data</source><year>2018</year><volume>5</volume><fpage>180202</fpage><pub-id pub-id-type="doi">10.1038/sdata.2018.202</pub-id><?supplied-pmid 30325352?><pub-id pub-id-type="pmid">30325352</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gevaert</surname><given-names>O</given-names></name><name><surname>Xu</surname><given-names>J</given-names></name><name><surname>Hoang</surname><given-names>CD</given-names></name><name><surname>Leung</surname><given-names>AN</given-names></name><name><surname>Xu</surname><given-names>Y</given-names></name><name><surname>Quon</surname><given-names>A</given-names></name><etal/></person-group><article-title>Non-small cell lung cancer: identifying prognostic imaging biomarkers by leveraging public gene expression microarray data&#x02013;methods and preliminary results</article-title><source>Radiology</source><year>2012</year><volume>264</volume><fpage>387</fpage><lpage>396</lpage><pub-id pub-id-type="doi">10.1148/radiol.12111607</pub-id><?supplied-pmid 22723499?><pub-id pub-id-type="pmid">22723499</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><mixed-citation publication-type="other">Grossberg A, Elhalawani H, Mohamed A, Mulder S, Williams B, White A, et al. MD Anderson Cancer Center Head and Neck Quantitative Imaging Working Group.(2020) HNSCC . The Cancer Imaging Archive. doi:107937/k9/tcia. 2020:a8sh-7363.</mixed-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grossberg</surname><given-names>AJ</given-names></name><name><surname>Mohamed</surname><given-names>ASR</given-names></name><name><surname>Elhalawani</surname><given-names>H</given-names></name><name><surname>Bennett</surname><given-names>WC</given-names></name><name><surname>Smith</surname><given-names>KE</given-names></name><name><surname>Nolan</surname><given-names>TS</given-names></name><etal/></person-group><article-title>Imaging and clinical data archive for head and neck squamous cell carcinoma patients treated with radiotherapy</article-title><source>Sci Data</source><year>2018</year><volume>5</volume><fpage>180173</fpage><pub-id pub-id-type="doi">10.1038/sdata.2018.173</pub-id><?supplied-pmid 30179230?><pub-id pub-id-type="pmid">30179230</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><mixed-citation publication-type="other">Matched computed tomography segmentation and demographic data for oropharyngeal cancer radiomics challenges. Sci Data. 2017;4:170077. 10.1038/sdata.2017.77.</mixed-citation></ref><ref id="CR48"><label>48.</label><mixed-citation publication-type="other">Bonawitz K, Eichner H, Grieskamp W, Huba D, Ingerman A, Ivanov V, et al. Towards federated learning at scale: system design. arXiv preprint arXiv:190201046. 2019.</mixed-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>T</given-names></name><name><surname>Sahu</surname><given-names>AK</given-names></name><name><surname>Talwalkar</surname><given-names>A</given-names></name><name><surname>Smith</surname><given-names>V</given-names></name></person-group><article-title>Federated learning: Challenges, methods, and future directions</article-title><source>IEEE Signal Process Mag</source><year>2020</year><volume>37</volume><fpage>50</fpage><lpage>60</lpage></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Amiri</surname><given-names>MM</given-names></name><name><surname>G&#x000fc;nd&#x000fc;z</surname><given-names>D</given-names></name></person-group><article-title>Federated learning over wireless fading channels</article-title><source>IEEE Trans Wirel Commun</source><year>2020</year><volume>19</volume><fpage>3546</fpage><lpage>3557</lpage><pub-id pub-id-type="doi">10.1109/TWC.2020.2974748</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wei</surname><given-names>K</given-names></name><name><surname>Li</surname><given-names>J</given-names></name><name><surname>Ding</surname><given-names>M</given-names></name><name><surname>Ma</surname><given-names>C</given-names></name><name><surname>Yang</surname><given-names>HH</given-names></name><name><surname>Farokhi</surname><given-names>F</given-names></name><etal/></person-group><article-title>Federated learning with differential privacy: algorithms and performance analysis</article-title><source>IEEE Trans Inf Forensics Secur</source><year>2020</year><volume>15</volume><fpage>3454</fpage><lpage>3469</lpage><pub-id pub-id-type="doi">10.1109/TIFS.2020.2988575</pub-id></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mothukuri</surname><given-names>V</given-names></name><name><surname>Parizi</surname><given-names>RM</given-names></name><name><surname>Pouriyeh</surname><given-names>S</given-names></name><name><surname>Huang</surname><given-names>Y</given-names></name><name><surname>Dehghantanha</surname><given-names>A</given-names></name><name><surname>Srivastava</surname><given-names>G</given-names></name></person-group><article-title>A survey on security and privacy of federated learning</article-title><source>Future Gener Comput Syst</source><year>2021</year><volume>115</volume><fpage>619</fpage><lpage>640</lpage><pub-id pub-id-type="doi">10.1016/j.future.2020.10.007</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lu</surname><given-names>Y</given-names></name><name><surname>Huang</surname><given-names>X</given-names></name><name><surname>Dai</surname><given-names>Y</given-names></name><name><surname>Maharjan</surname><given-names>S</given-names></name><name><surname>Zhang</surname><given-names>Y</given-names></name></person-group><article-title>Blockchain and federated learning for privacy-preserved data sharing in industrial IoT</article-title><source>IEEE Trans Industr Inform</source><year>2019</year><volume>16</volume><fpage>4177</fpage><lpage>4186</lpage><pub-id pub-id-type="doi">10.1109/TII.2019.2942190</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><mixed-citation publication-type="other">Zhang M, Qu L, Singh P, Kalpathy-Cramer J, Rubin DL. SplitAVG: A heterogeneity-aware federated deep learning method for medical imaging. arXiv preprint arXiv:210702375. 2021.</mixed-citation></ref><ref id="CR55"><label>55.</label><mixed-citation publication-type="other">Stripelis D, Saleem H, Ghai T, Dhinagar N, Gupta U, Anastasiou C, et al. Secure neuroimaging analysis using federated learning with homomorphic encryption. 17th International Symposium on Medical Information Processing and Analysis: SPIE; 2021. p. 351&#x02013;359.</mixed-citation></ref><ref id="CR56"><label>56.</label><mixed-citation publication-type="other">Qu L, Zhou Y, Liang PP, Xia Y, Wang F, Fei-Fei L, et al. Rethinking architecture design for tackling data heterogeneity in federated learning. arXiv preprint arXiv:210606047. 2021.</mixed-citation></ref><ref id="CR57"><label>57.</label><mixed-citation publication-type="other">Liu Q, Yang H, Dou Q, Heng P-A. Federated semi-supervised medical image classification via inter-client relation matching. arXiv preprint arXiv:210608600. 2021.</mixed-citation></ref><ref id="CR58"><label>58.</label><mixed-citation publication-type="other">Chakravarty A, Kar A, Sethuraman R, Sheet D. Federated learning for site aware chest radiograph screening. 2021 IEEE 18th International Symposium on Biomedical Imaging (ISBI): IEEE; 2021. p. 1077&#x02013;81.</mixed-citation></ref><ref id="CR59"><label>59.</label><mixed-citation publication-type="other">Linardos A, Kushibar K, Walsh S, Gkontra P, Lekadir K. Federated learning for multi-center imaging diagnostics: a study in cardiovascular disease. arXiv preprint arXiv:210703901. 2021.</mixed-citation></ref><ref id="CR60"><label>60.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Qin</surname><given-names>X</given-names></name><name><surname>Zhang</surname><given-names>Z</given-names></name><name><surname>Huang</surname><given-names>C</given-names></name><name><surname>Dehghan</surname><given-names>M</given-names></name><name><surname>Zaiane</surname><given-names>OR</given-names></name><name><surname>Jagersand</surname><given-names>M</given-names></name></person-group><article-title>U2-Net: going deeper with nested U-structure for salient object detection</article-title><source>Pattern Recognit</source><year>2020</year><volume>106</volume><fpage>107404</fpage><pub-id pub-id-type="doi">10.1016/j.patcog.2020.107404</pub-id></element-citation></ref><ref id="CR61"><label>61.</label><mixed-citation publication-type="other">Ronneberger O, Fischer P, Brox T. U-net: convolutional networks for biomedical image segmentation. International Conference on Medical image computing and computer-assisted intervention: Springer; 2015. p. 234&#x02013;41.</mixed-citation></ref><ref id="CR62"><label>62.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Bovik</surname><given-names>AC</given-names></name><name><surname>Sheikh</surname><given-names>HR</given-names></name><name><surname>Simoncelli</surname><given-names>EP</given-names></name></person-group><article-title>Image quality assessment: from error visibility to structural similarity</article-title><source>IEEE Trans Image Process</source><year>2004</year><volume>13</volume><fpage>600</fpage><lpage>612</lpage><pub-id pub-id-type="doi">10.1109/TIP.2003.819861</pub-id><?supplied-pmid 15376593?><pub-id pub-id-type="pmid">15376593</pub-id></element-citation></ref><ref id="CR63"><label>63.</label><mixed-citation publication-type="other">Yang X, Lei Y, Dong X, Wang T, Higgins K, Liu T, et al. Attenuation and scatter correction for whole-body PET using 3D generative adversarial networks. J Nucl Med; 2019;60(Suppl 1):174.</mixed-citation></ref><ref id="CR64"><label>64.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dong</surname><given-names>X</given-names></name><name><surname>Lei</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>T</given-names></name><name><surname>Higgins</surname><given-names>K</given-names></name><name><surname>Liu</surname><given-names>T</given-names></name><name><surname>Curran</surname><given-names>WJ</given-names></name><etal/></person-group><article-title>Deep learning-based attenuation correction in the absence of structural information for whole-body positron emission tomography imaging</article-title><source>Phys Med Biol</source><year>2020</year><volume>65</volume><fpage>055011</fpage><pub-id pub-id-type="doi">10.1088/1361-6560/ab652c</pub-id><?supplied-pmid 31869826?><pub-id pub-id-type="pmid">31869826</pub-id></element-citation></ref><ref id="CR65"><label>65.</label><mixed-citation publication-type="other">Van Hemmen H, Massa H, Hurley S, Cho S, Bradshaw T, McMillan A. A deep learning-based approach for direct whole-body PET attenuation correction. J Nucl Med.;60,559, 2019.</mixed-citation></ref><ref id="CR66"><label>66.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hwang</surname><given-names>D</given-names></name><name><surname>Kang</surname><given-names>SK</given-names></name><name><surname>Kim</surname><given-names>KY</given-names></name><name><surname>Choi</surname><given-names>H</given-names></name><name><surname>Lee</surname><given-names>JS</given-names></name></person-group><article-title>Comparison of deep learning-based emission-only attenuation correction methods for positron emission tomography</article-title><source>Eur J Nucl Med Mol Imaging</source><year>2022</year><volume>49</volume><fpage>1833</fpage><lpage>1842</lpage><pub-id pub-id-type="doi">10.1007/s00259-021-05637-0</pub-id><?supplied-pmid 34882262?><pub-id pub-id-type="pmid">34882262</pub-id></element-citation></ref><ref id="CR67"><label>67.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>F</given-names></name><name><surname>Jang</surname><given-names>H</given-names></name><name><surname>Kijowski</surname><given-names>R</given-names></name><name><surname>Bradshaw</surname><given-names>T</given-names></name><name><surname>McMillan</surname><given-names>AB</given-names></name></person-group><article-title>Deep learning MR imaging-based attenuation correction for PET/MR imaging</article-title><source>Radiology</source><year>2018</year><volume>286</volume><fpage>676</fpage><lpage>684</lpage><pub-id pub-id-type="doi">10.1148/radiol.2017170700</pub-id><?supplied-pmid 28925823?><pub-id pub-id-type="pmid">28925823</pub-id></element-citation></ref><ref id="CR68"><label>68.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>F</given-names></name><name><surname>Jang</surname><given-names>H</given-names></name><name><surname>Kijowski</surname><given-names>R</given-names></name><name><surname>Zhao</surname><given-names>G</given-names></name><name><surname>Bradshaw</surname><given-names>T</given-names></name><name><surname>McMillan</surname><given-names>AB</given-names></name></person-group><article-title>A deep learning approach for (18)F-FDG PET attenuation correction</article-title><source>EJNMMI Phys</source><year>2018</year><volume>5</volume><fpage>24</fpage><pub-id pub-id-type="doi">10.1186/s40658-018-0225-8</pub-id><?supplied-pmid 30417316?><pub-id pub-id-type="pmid">30417316</pub-id></element-citation></ref><ref id="CR69"><label>69.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yang</surname><given-names>J</given-names></name><name><surname>Sohn</surname><given-names>JH</given-names></name><name><surname>Behr</surname><given-names>SC</given-names></name><name><surname>Gullberg</surname><given-names>GT</given-names></name><name><surname>Seo</surname><given-names>Y</given-names></name></person-group><article-title>CT-less direct correction of attenuation and scatter in the image space using deep learning for whole-body FDG PET: potential benefits and pitfalls</article-title><source>Radiol Artif Intell</source><year>2020</year><volume>3</volume><issue>2</issue><fpage>e200137</fpage><pub-id pub-id-type="doi">10.1148/ryai.**********</pub-id><?supplied-pmid 33937860?><pub-id pub-id-type="pmid">33937860</pub-id></element-citation></ref><ref id="CR70"><label>70.</label><mixed-citation publication-type="other">Shiri I, Sanaat A, Salimi Y, Akhavanallaf A, Arabi H, Rahmim A, et al. PET-QA-NET: Towards routine PET image artifact detection and correction using deep convolutional neural networks. 2021 IEEE Nuclear Science Symposium and Medical Imaging Conference (NSS/MIC); p. 1&#x02013;3. 10.1109/NSS/MIC44867.2021.9875610</mixed-citation></ref><ref id="CR71"><label>71.</label><mixed-citation publication-type="other">Izadi S, Shiri I, Uribe C, Geramifar P, Zaidi H, Rahmim A, et al. Enhanced direct joint attenuation and scatter correction of whole-body PET images via context-aware deep networks. medRxiv. 2022. 10.1101/2022.05.26.22275662</mixed-citation></ref><ref id="CR72"><label>72.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Zhou</surname><given-names>B</given-names></name><name><surname>Xie</surname><given-names>H</given-names></name><name><surname>Shi</surname><given-names>L</given-names></name><name><surname>Liu</surname><given-names>H</given-names></name><name><surname>Holler</surname><given-names>W</given-names></name><etal/></person-group><article-title>Direct and indirect strategies of deep-learning-based attenuation correction for general purpose and dedicated cardiac SPECT</article-title><source>Eur J Nucl Med Mol Imaging</source><year>2022</year><volume>49</volume><fpage>3046</fpage><lpage>3060</lpage><pub-id pub-id-type="doi">10.1007/s00259-022-05718-8</pub-id><?supplied-pmid 35169887?><pub-id pub-id-type="pmid">35169887</pub-id></element-citation></ref><ref id="CR73"><label>73.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Toyonaga</surname><given-names>T</given-names></name><name><surname>Shao</surname><given-names>D</given-names></name><name><surname>Shi</surname><given-names>L</given-names></name><name><surname>Zhang</surname><given-names>J</given-names></name><name><surname>Revilla</surname><given-names>EM</given-names></name><name><surname>Menard</surname><given-names>D</given-names></name><etal/></person-group><article-title>Deep learning-based attenuation correction for whole-body PET - a multi-tracer study with (18)F-FDG, (68) Ga-DOTATATE, and (18)F-Fluciclovine</article-title><source>Eur J Nucl Med Mol Imaging</source><year>2022</year><volume>49</volume><fpage>3086</fpage><lpage>3097</lpage><pub-id pub-id-type="doi">10.1007/s00259-022-05748-2</pub-id><?supplied-pmid 35277742?><pub-id pub-id-type="pmid">35277742</pub-id></element-citation></ref><ref id="CR74"><label>74.</label><mixed-citation publication-type="other">Shiri I, Sadr AV, Sanaat A, Ferdowsi S, Arabi H, Zaidi H. Federated learning-based deep learning model for PET attenuation and scatter correction: a multi-center study. 2021 IEEE Nuclear Science Symposium and Medical Imaging Conference (NSS/MIC). p. 1&#x02013;3.</mixed-citation></ref><ref id="CR75"><label>75.</label><mixed-citation publication-type="other">Gawali M, Arvind C, Suryavanshi S, Madaan H, Gaikwad A, Prakash KB, et al. Comparison of privacy-preserving distributed deep learning methods in healthcare. Annual Conference on Medical Image Understanding and Analysis: Springer; 2021. p. 457&#x02013;71.</mixed-citation></ref><ref id="CR76"><label>76.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shiri</surname><given-names>I</given-names></name><name><surname>Vafaei Sadr</surname><given-names>A</given-names></name><name><surname>Amini</surname><given-names>M</given-names></name><name><surname>Salimi</surname><given-names>Y</given-names></name><name><surname>Sanaat</surname><given-names>A</given-names></name><name><surname>Akhavanallaf</surname><given-names>A</given-names></name><etal/></person-group><article-title>Decentralized distributed multi-institutional pet image segmentation using a federated deep learning framework</article-title><source>Clin Nucl Med</source><year>2022</year><volume>47</volume><fpage>606</fpage><lpage>617</lpage><pub-id pub-id-type="doi">10.1097/rlu.0000000000004194</pub-id><?supplied-pmid 35442222?><pub-id pub-id-type="pmid">35442222</pub-id></element-citation></ref><ref id="CR77"><label>77.</label><mixed-citation publication-type="other">Melis L, Song C, De Cristofaro E, Shmatikov V. Exploiting unintended feature leakage in collaborative learning. 2019 IEEE Symposium on Security and Privacy (SP): IEEE; 2019. p. 691&#x02013;706.</mixed-citation></ref><ref id="CR78"><label>78.</label><mixed-citation publication-type="other">Carlini N, Liu C, Erlingsson &#x000da;, Kos J, Song D. The secret sharer: evaluating and testing unintended memorization in neural networks. 28th Security Symposium ( Security 19); 2019. p. 267&#x02013;84.</mixed-citation></ref><ref id="CR79"><label>79.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Duchi</surname><given-names>JC</given-names></name><name><surname>Jordan</surname><given-names>MI</given-names></name><name><surname>Wainwright</surname><given-names>MJ</given-names></name></person-group><article-title>Privacy aware learning</article-title><source>Journal of the ACM (JACM)</source><year>2014</year><volume>61</volume><fpage>1</fpage><lpage>57</lpage><pub-id pub-id-type="doi">10.1145/2666468</pub-id></element-citation></ref><ref id="CR80"><label>80.</label><mixed-citation publication-type="other">Shokri R, Stronati M, Song C, Shmatikov V. Membership inference attacks against machine learning models. 2017 IEEE Symposium on Security and Privacy (SP): IEEE; 2017. p. 3&#x02013;18.</mixed-citation></ref><ref id="CR81"><label>81.</label><mixed-citation publication-type="other">Fredrikson M, Jha S, Ristenpart T. Model inversion attacks that exploit confidence information and basic countermeasures. Proc of the 22nd ACM SIGSAC Conference on Computer and Communications Security; 2015. p. 1322&#x02013;33.</mixed-citation></ref><ref id="CR82"><label>82.</label><mixed-citation publication-type="other">Chen X, Liu C, Li B, Lu K, Song D. Targeted backdoor attacks on deep learning systems using data poisoning. arXiv preprint arXiv:171205526. 2017.</mixed-citation></ref><ref id="CR83"><label>83.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>B</given-names></name><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Singh</surname><given-names>A</given-names></name><name><surname>Vorobeychik</surname><given-names>Y</given-names></name></person-group><article-title>Data poisoning attacks on factorization-based collaborative filtering</article-title><source>Adv Neural Inf Process Syst</source><year>2016</year><volume>29</volume><fpage>1885</fpage><lpage>1893</lpage></element-citation></ref><ref id="CR84"><label>84.</label><mixed-citation publication-type="other">Bagdasaryan E, Veit A, Hua Y, Estrin D, Shmatikov V. How to backdoor federated learning. International Conference on Artificial Intelligence and Statistics: PMLR; 2020. p. 2938&#x02013;48.</mixed-citation></ref><ref id="CR85"><label>85.</label><mixed-citation publication-type="other">Xie C, Huang K, Chen P-Y, Li B. DBA: Distributed backdoor attacks against federated learning. International Conference on Learning Representations; 2020; p1&#x02013;15.</mixed-citation></ref></ref-list></back></article>