<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5336658</article-id><article-id pub-id-type="publisher-id">1563</article-id><article-id pub-id-type="doi">10.1186/s12859-017-1563-6</article-id><article-categories><subj-group subj-group-type="heading"><subject>Software</subject></subj-group></article-categories><title-group><article-title>ProteoModlR for functional proteomic analysis</article-title></title-group><contrib-group><contrib contrib-type="author" equal-contrib="yes"><name><surname>Cifani</surname><given-names>Paolo</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Shakiba</surname><given-names>Mojdeh</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Chhangawala</surname><given-names>Sagar</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-8063-9191</contrib-id><name><surname>Kentsis</surname><given-names>Alex</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2171 9952</institution-id><institution-id institution-id-type="GRID">grid.51462.34</institution-id><institution/><institution>Molecular Pharmacology Program, Sloan Kettering Institute, Memorial Sloan Kettering Cancer Center, </institution></institution-wrap>New York, NY USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">000000041936877X</institution-id><institution-id institution-id-type="GRID">grid.5386.8</institution-id><institution/><institution>Physiology, Biophysics and Systems Biology Program, Weill Cornell Graduate School of Medical Sciences, </institution></institution-wrap>New York, NY USA </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">000000041936877X</institution-id><institution-id institution-id-type="GRID">grid.5386.8</institution-id><institution>Department of Pediatrics and Memorial Sloan Kettering Cancer Center, </institution><institution>Weill Medical College of Cornell University, </institution></institution-wrap>New York, NY USA </aff></contrib-group><pub-date pub-type="epub"><day>4</day><month>3</month><year>2017</year></pub-date><pub-date pub-type="pmc-release"><day>4</day><month>3</month><year>2017</year></pub-date><pub-date pub-type="collection"><year>2017</year></pub-date><volume>18</volume><elocation-id>153</elocation-id><history><date date-type="received"><day>28</day><month>5</month><year>2016</year></date><date date-type="accepted"><day>24</day><month>2</month><year>2017</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2017</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>High-accuracy mass spectrometry enables near comprehensive quantification of the components of the cellular proteomes, increasingly including their chemically modified variants. Likewise, large-scale libraries of quantified synthetic peptides are becoming available, enabling absolute quantification of chemically modified proteoforms, and therefore systems-level analyses of changes of their absolute abundance and stoichiometry. Existing computational methods provide advanced tools for mass spectral analysis and statistical inference, but lack integrated functions for quantitative analysis of post-translationally modified proteins and their modification stoichiometry.</p></sec><sec><title>Results</title><p>Here, we develop ProteoModlR, a program for quantitative analysis of abundance and stoichiometry of post-translational chemical modifications across temporal and steady-state biological states. While ProteoModlR is intended for the analysis of experiments using isotopically labeled reference peptides for absolute quantitation, it also supports the analysis of labeled and label-free data, acquired in both data-dependent and data-independent modes for relative quantitation. Moreover, ProteoModlR enables functional analysis of sparsely sampled quantitative mass spectrometry experiments by inferring the missing values from the available measurements, without imputation. The implemented architecture includes parsing and normalization functions to control for common sources of technical variation. Finally, ProteoModlR&#x02019;s modular design and interchangeable format are optimally suited for integration with existing computational proteomics tools, thereby facilitating comprehensive quantitative analysis of cellular signaling.</p></sec><sec><title>Conclusions</title><p>ProteoModlR and its documentation are available for download at <ext-link ext-link-type="uri" xlink:href="http://github.com/kentsisresearchgroup/ProteoModlR">http://github.com/kentsisresearchgroup/ProteoModlR</ext-link> as a stand-alone R package.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s12859-017-1563-6) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Mass spectrometry</kwd><kwd>Quantitative proteomics</kwd><kwd>Post-translational modification stoichiometry</kwd><kwd>Functional analysis</kwd><kwd>R</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>R21 CA188881</award-id><award-id>P30 CA008748</award-id><principal-award-recipient><name><surname>Kentsis</surname><given-names>Alex</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100005410</institution-id><institution>American-Italian Cancer Foundation</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000861</institution-id><institution>Burroughs Wellcome Fund</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100009858</institution-id><institution>Gabrielle&#x02019;s Angel Foundation for Cancer Research</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100001445</institution-id><institution>Alex's Lemonade Stand Foundation for Childhood Cancer</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution>Josie Robertson Investigator Program</institution></funding-source></award-group><award-group><funding-source><institution>Damon Runyon-Lumsden Foundation Clinical Investigator</institution></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2017</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>Studies of cellular signaling have historically relied on immunoassays due to their ease of use and widespread accessibility. However, their variable specificity, semi-quantitative nature and availability only for selected proteins and post-translational modifications (PTMs) hinder their application to biological problems that require accurate, precise and multi-parametric measurements. High-resolution mass spectrometry (MS) satisfies these requirements, enabling quantitative measurements of post-translational modifications across thousands of proteins, with proteome coverage approaching genome-scale levels [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR3">3</xref>].</p><p>In bottom-up mass spectrometry, proteins are cleaved into peptides, whose identity is determined from the analysis of their fragmentation mass spectra. Likewise, peptide adducts from post-translational chemical modifications produce mass shifts in precursor and fragment ions that are used for PTM identification and localization [<xref ref-type="bibr" rid="CR4">4</xref>, <xref ref-type="bibr" rid="CR5">5</xref>]. Once identification of peptides and PTMs is accomplished, relative peptide abundance can be estimated from the intensity of the corresponding MS signal, i.e. from extracted ion current of either precursor or fragment ions. However, the chemical composition of each peptide determines its ionization properties and therefore its specific MS signal-response function. Absolute peptide quantitation, required for stoichiometry calculations, depends on the use of reference standards to control for variable chromatographic and ionization properties of peptides [<xref ref-type="bibr" rid="CR6">6</xref>, <xref ref-type="bibr" rid="CR7">7</xref>]. Furthermore, apparent differences in MS signals can be due to variations in both peptide abundance and/or its modification stoichiometry. Thus, functional proteomic analyses require deconvolution of these two distinct biological processes, in addition to control of sources of technical variation [<xref ref-type="bibr" rid="CR8">8</xref>].</p><p>Increasing complexity of biological mass spectrometric experiments has prompted the development of several computational programs for mass spectral analysis, ion current extraction, and statistical inference. For example, MaxQuant enables peptide identification, quantitation and PTM site localization [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>]. Skyline is designed for precise peptide quantification based on extracted ion chromatograms (XIC) [<xref ref-type="bibr" rid="CR11">11</xref>]. MSstats permits statistical comparisons of quantitative proteomics data, whereas NetworKIN and Scaffold enable functional annotation [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR13">13</xref>]. However, these programs lack integrated functions for quantitative analysis of post-translationally modified proteins or do not compute modification stoichiometry.</p><p>Here, we describe a generalized method for quantitative analysis of differential abundance and PTM stoichiometry from peptide-based proteomics data. We implemented this approach in an open-source R program [<xref ref-type="bibr" rid="CR14">14</xref>], named ProteoModlR, which also offers normalization functionalities to improve the analytical accuracy through control of common sources of technical variation. Due to its modular design and interchangeable format, ProteoModlR can process the output of a variety of current proteomic programs, such as MaxQuant and Skyline, and facilitates the use of quantitative proteomics for the analysis of cellular signaling.</p></sec><sec id="Sec2"><title>Implementation</title><p>ProteoModlR calculates differences in peptide abundance and PTM stoichiometry (i.e. the molar fraction of peptide bearing a given PTM, compared to the total amount of that peptide) across different experimental conditions and biologic states, based on intensity measurements obtained using any program for mass spectrometric analysis. The input dataset requires identifiers for proteins, peptides and modifications, along with the signal intensity measurements for each peptide, formatted as a comma-separated value (CSV) file, that can be generated using programs for quantitative mass spectral analysis such as MaxQuant or Skyline. In particular, Skyline&#x02019;s &#x02018;Export Report&#x02019; can be customized for direct import into ProteoModlR. ProteoModlR also accommodates additional protein and sample annotations, including PTM site information and functional ontology. The currently implemented workflow does not perform protein inference independently and relies instead on the explicitly provided peptide annotation.</p><p>ProteoModlR consists of 3 modules performing <italic>Quality Control</italic>, <italic>Normalization</italic>, and <italic>Analysis</italic> (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>, Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S1). For each protein in the input, the <italic>Quality Control</italic> module parses the available peptides based on their modification status. For a given peptide sequence, if none of the chemoforms bears the modification of interest, then the peptide is used for protein quantification (therefore labeled as &#x02018;Q&#x02019;). If, on the other hand, one or more chemoforms bears the modification of interest, then all peptides are labeled as &#x02018;modified&#x02019; (M) or &#x02018;not modified&#x02019; (NM), depending on their annotated PTM status (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref>). Furthermore, <italic>Quality Control</italic> checks the correct formatting of the input file, and removes proteins with no quantified peptides. The current implementation of <italic>Quality Control</italic> is based on exact amino acid sequences: if incomplete or nonspecific proteolysis results in peptides with different termini, each of them is considered separately. After annotation, the dataset is exported as a CSV file.<fig id="Fig1"><label>Fig. 1</label><caption><p>ProteoModlR&#x02019;s schema. ProteoModlR consists of 3 modules, accepts data from common programs for mass spectral analysis, and generates open-format results that integrate with existing R programs for network analysis and visualization</p></caption><graphic xlink:href="12859_2017_1563_Fig1_HTML" id="MO1"/></fig>
<fig id="Fig2"><label>Fig. 2</label><caption><p>ProteoModlR annotates peptides based on their PTM status and controls potential sources of technical variation. <bold>a</bold> Peptide annotation of peptides from a hypothetical protein depends on available quantified chemoforms. <bold>b</bold> Quantitation across three replicate measurements (shades of red) of five peptides from a protein. <bold>c</bold> ProteoModlR corrects errors introduced by differential ionization efficiency and technical variability, equalizing the signal recorded for equimolar reference isotopologues (<bold>d</bold>&#x02013;<bold>e</bold>)</p></caption><graphic xlink:href="12859_2017_1563_Fig2_HTML" id="MO2"/></fig>
</p><p>Variable ionization efficiency prevents direct conversion of MS signal intensities into absolute peptide abundance, which is required for calculating PTM stoichiometries. The relative abundance and stoichiometry calculations performed by ProteoModlR are therefore based on three assumptions. First, ProteoModlR assumes that the samples contain synthetic isotope-labeled peptides as reference standards, present at equimolar concentration. Second, it assumes that these peptides have linear signal-response functions with slopes equal or close to one. In practice, this requires confirmation, as is currently routine for absolute quantitation methods [<xref ref-type="bibr" rid="CR6">6</xref>, <xref ref-type="bibr" rid="CR15">15</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>]. Third, ProteoModlR assumes that all or most of the variants of each peptide produced by chemical modification of a given protein (hereafter named &#x02018;chemoforms&#x02019;) are quantified. This implies that the total molarity of all peptide chemoforms is equal to that of the unmodified peptide, and to that of the protein from which they originated.</p><p>For datasets that meet the aforementioned assumptions, the <italic>Normalization</italic> module normalizes the intensity values from the <italic>Quality Control</italic> output, equalizing the intensity of the equimolar reference peptides. This normalization (termed <italic>equimolar isotopologue</italic> normalization) can also correct for possible experimental alterations to chemoforms molarity, such as those produced by enrichment of chemically modified peptides [<xref ref-type="bibr" rid="CR18">18</xref>], or due to variable recovery from chromatographic separation. This module also reduces technical variability produced by other sources, such as uneven sample loading and variable efficiency of electro-spray ionization (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2b</xref>, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Figure S2).</p><p>For datasets that may not meet the aforementioned assumptions, ProteoModlR offers three additional normalization modes that can be leveraged for correction of potential technical artifacts. First, normalization by <italic>isotopologue</italic> can be performed if a non-equimolar set of labeled peptides is used, as could be the case for SILAC-labeled proteomes (Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Figure S3). Second, normalization by <italic>total ion current</italic> (TIC) can be used, if no isotopically encoded standard is available (Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S4). Finally, normalization using a set of <italic>internal reference</italic> peptides can be used to correct for variations in the total protein content per cell (Additional file <xref rid="MOESM5" ref-type="media">5</xref>: Figure S5). Recommended normalization strategies for common experimental designs are provided in the Software Documentation (Additional file <xref rid="MOESM6" ref-type="media">6</xref>). To permit modular analyses, the output of the <italic>Normalization</italic> module is also exported as a CSV file.</p><p>Finally, the <italic>Analysis</italic> module of ProteoModlR calculates differential abundance and PTM stoichiometry based on the annotation performed in <italic>Quality Control</italic>. Relative abundance is calculated using the intensities of &#x02018;Quantification&#x02019; peptides, and expressed as the ratio of the intensities in every sample to that of the reference sample. If positional information is provided, PTM stoichiometry is calculated for each site individually, as the ratio between the intensity of the modified peptide over the sum of the intensities of all chemoforms of that peptide. Assuming that the experimental dataset contains quantification annotation for all classes of peptides, as described above, these calculations are referred to as <italic>exact</italic> within ProteoModlR.</p><p>To enable approximate analysis of experimental data that are incompletely annotated, as in the case of proteins lacking quantified &#x02018;Q&#x02019; or &#x02018;NM&#x02019; peptides, ProteoModlR can be used to infer the missing values based on the intensities of the detected peptides (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>). As described above, ProteoModlR assumes that the total molarity of all chemoforms of a peptide is equal to that of the unmodified &#x02018;Q&#x02019; peptide. If &#x02018;Q&#x02019; peptides are not detected, then <italic>approximate</italic> abundance can be estimated using the sum of the intensity of all chemoforms of modified peptides (&#x02018;M&#x02019; and &#x02018;NM&#x02019; peptides). Likewise, <italic>approximate</italic> PTM stoichiometry can be estimated using the intensity of unrelated &#x02018;Q&#x02019; peptide as a proxy for total protein abundance, instead of the total intensity of all chemoforms for that peptide (Additional file <xref rid="MOESM7" ref-type="media">7</xref>: Figure S6). When no equimolar isotopologue standard is used, the results of these calculations approximate biologic differences. In the case of affinity enrichment of chemically modified peptides, such as in the case of conventional phosphoproteomic measurements, ProteoModlR requires appropriate internal standards to ensure accuracy of the results. The output of the <italic>Analysis</italic> module is exported as a CSV file, allowing for subsequent statistical and network analysis using existing programs, such as those implemented in Bioconductor. Further details of the operations performed by each module are provided in the Software Documentation (Additional file <xref rid="MOESM6" ref-type="media">6</xref> and available at <ext-link ext-link-type="uri" xlink:href="https://github.com/kentsisresearchgroup/ProteoModlR">https://github.com/kentsisresearchgroup/ProteoModlR</ext-link>).<fig id="Fig3"><label>Fig. 3</label><caption><p>ProteoModlR computes exact and approximate abundance and PTM stoichiometry based on the available set of peptides for a given protein. Exact and approximate calculations for abundance and stoichiometry were tested on simulated datasets modeling a hypothetical protein producing four peptides, two of which bear a PTM. If the input file contains all &#x02018;Q&#x02019;, &#x02018;M&#x02019; and &#x02018;NM&#x02019; peptides, then ProteoModlR computes &#x02018;exact&#x02019; relative peptide abundance, expressed as fold change compared to Sample #1 (<bold>a</bold>) and stoichiometry (<bold>c</bold>) If &#x02018;Quantification&#x02019; (&#x02018;Q&#x02019;) or non-modified (&#x02018;NM&#x02019;) peptides are not available, ProteoModlR can calculate &#x02018;approximate&#x02019; relative abundances (<bold>b</bold>) and 'approximate stoichiometry (<bold>d</bold>) resembling the exact values (Pearson product&#x02013;moment correlation coefficient = 0.98)</p></caption><graphic xlink:href="12859_2017_1563_Fig3_HTML" id="MO3"/></fig>
</p></sec><sec id="Sec3"><title>Results and discussion</title><p>To test the functionalities of ProteoModlR, first we used simulated datasets, modeling MS measurements of peptides generated from a hypothetical phosphorylated protein and from loading controls, across three biological samples (Additional file <xref rid="MOESM8" ref-type="media">8</xref>: Simulated Dataset 1 and Additional file <xref rid="MOESM9" ref-type="media">9</xref>: Simulated Dataset 7). The datasets used to test the performance of <italic>Normalization</italic> module simulated errors within identical samples, introduced by common sources of technical variation: i) deterioration of the efficiency of the LC-ESI-MS instrumentation; ii) variable sample loading from inaccurate estimation of protein content, and iii) variable sample loading from significant variation of specific proteins per cell. In each case, we were able to correct these errors by using ProteoModlR to apply normalization by isotopologue, equimolar isotopologue, total ion current, or internal reference peptides (Additional files <xref rid="MOESM2" ref-type="media">2</xref>, <xref rid="MOESM3" ref-type="media">3</xref>, <xref rid="MOESM4" ref-type="media">4</xref>, <xref rid="MOESM5" ref-type="media">5</xref>: Figures S2-S5, Additional files <xref rid="MOESM10" ref-type="media">10</xref>, <xref rid="MOESM11" ref-type="media">11</xref>, <xref rid="MOESM12" ref-type="media">12</xref>, <xref rid="MOESM13" ref-type="media">13</xref>, <xref rid="MOESM14" ref-type="media">14</xref>: Simulated Datasets 2&#x02013;6).</p><p>For data containing complete measurements of all peptides under all conditions, we used ProteoModlR to calculate exact peptide abundances and PTM stoichiometries (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>, <xref rid="Fig3" ref-type="fig">c</xref>). Simulating different types of incomplete datasets, we then used ProteoModlR to approximate peptide abundance and PTM stoichiometries (Figs.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>, <xref rid="Fig3" ref-type="fig">d</xref>, Additional file <xref rid="MOESM7" ref-type="media">7</xref>: Figure S6, Additional files <xref rid="MOESM9" ref-type="media">9</xref>, <xref rid="MOESM15" ref-type="media">15</xref>, <xref rid="MOESM16" ref-type="media">16</xref>, <xref rid="MOESM17" ref-type="media">17</xref>, <xref rid="MOESM18" ref-type="media">18</xref>: Simulated Dataset 7&#x02013;11). Similar results were obtained for the comparison between exact and approximate PTM stoichiometries. As such, approximate calculations generated results that highly correlated with the expected values (Pearson product&#x02013;moment correlation coefficient of 0.98 and 0.95 for abundance and stoichiometry, respectively). Thus, this approximation may suffice for semi-quantitative studies, when complete data are not available.</p><p>To test ProteoModlR&#x02019;s performance for the analysis of large-scale quantitative mass spectrometry data and compatibility with existing programs of mass spectral analysis, we analyzed changes in relative protein abundance and phosphorylation stoichiometries of CD8+ T cells upon interleukin-2 (IL-2) stimulation, as measured using SILAC and MaxQuant (PRIDE accession: PXD004645) [<xref ref-type="bibr" rid="CR19">19</xref>]. First, we used ProteoModlR&#x02019;s <italic>Quality Control</italic> and <italic>Normalization</italic> modules to correct for differences in loading between steady-state (light isotope) and stimulated (heavy isotope) conditions using the total ion current normalization routine. Since these experiments were performed using metal affinity chromatography, detection was biased against &#x02018;Q&#x02019; and &#x02018;NM&#x02019; peptides, and &#x02018;M&#x02019; phosphopeptides accounted for 84% of the detected chemoforms. We thus used the <italic>approximate</italic> abundance and stoichiometry calculation routines in the <italic>Analysis</italic> module to complement <italic>exact</italic> calculations.</p><p>Using this approach, we used ProteoModlR to calculate specific changes in both abundance and phosphorylation stoichiometry of 2794 proteins upon IL-2 stimulation (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>, Additional file <xref rid="MOESM19" ref-type="media">19</xref>: Table S1). For example, we found no changes in abundance or phosphorylation stoichiometry of LCK and ZAP70, in agreement with their known functions in T cell receptor (TCR) but not IL-2 receptor signaling [<xref ref-type="bibr" rid="CR19">19</xref>]. In contrast, we observed apparent increases in abundance and phosphorylation stoichiometry of STAT5A and NFIL3, in agreement with their expected involvement in JAK1/2-dependent signaling induced by IL-2 receptor stimulation [<xref ref-type="bibr" rid="CR19">19</xref>]. The apparent increase in STAT5A abundance without a change in phosphorylation stoichiometry may be due to an increase in STAT5A phosphorylation and/or increased protein abundance. Thus, ProteoModlR enabled both exact and approximate large-scale calculations of protein abundance and phosphorylation stoichiometry, depending on the presence of their chemoforms.<fig id="Fig4"><label>Fig. 4</label><caption><p>ProteoModlR enables large-scale analysis of experimentally measured protein abundance and PTM stoichiometry. Relative abundance of four representative proteins from 2784 proteins analyzed from SILAC-labeled CD8+ T cells stimulated with IL-2, as compared to unstimulated control, and analyzed using MaxQuant [<xref ref-type="bibr" rid="CR19">19</xref>]. T-cell receptor signaling-dependent LCK and ZAP70 proteins exhibit no changes upon IL-2 stimulation, whereas IL-2 receptor dependent STAT5A and NFIL3 exhibits increases in abundance or phosphorylation stoichiometry upon IL-2 stimulation</p></caption><graphic xlink:href="12859_2017_1563_Fig4_HTML" id="MO4"/></fig>
</p><p>Functional analysis of biological processes requires precise characterization of the activation status of the relevant effector proteins. In this context, cellular protein abundance and post-translational modification have important biological functions. Large libraries of synthetic peptides now enable near-comprehensive MS analysis of peptide chemoforms and deconvolution of their respective ionization efficiency. Consequently, it is now becoming feasible to calculate the modification stoichiometry of large sets of proteoforms, which is important for functional analysis of cellular signaling [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR21">21</xref>]. However, while existing mass spectrometry analysis programs allow extraction and visualization of protein expression levels from quantitative mass spectrometry experiments, calculations of PTM stoichiometry still require specialized approaches.</p><p>ProteoModlR facilitates the analysis of quantitative mass spectrometry experiments by calculating differential protein abundance and PTM stoichiometry across temporal and steady-state biological states. The software deconvolutes the contribution of chemical modifications of peptides to their mass spectrometric signal intensity, thereby calculating both PTM stoichiometry and relative protein abundance. To this end, ProteoModlR annotates the available quantified peptides according to their PTM status, determining for each modification the chemoforms relevant for stoichiometry and abundance calculations. The software integrates normalization functions to correct, based on the signal of synthetic reference standards, MS intensity distortion produced by variable peptide ionization efficiency, as well as other common sources of technical variability. Finally, ProteoModlR calculates relative differences in protein abundance and PTM stoichiometry, thus facilitating analyses of cellular protein function.</p><p>Missing values in sparsely annotated datasets are commonly either filtered out or imputed to enable subsequent statistical analysis and functional pathway modeling. ProteoModlR introduces an alternative strategy, based on inferring the quantity of non-detected peptides from the normalized measured intensities of other peptides derived from the same protein. It can thus complement and improve the comprehensiveness of currently available tools for functional analysis. In addition, ProteoModlR&#x02019;s modular design and flexible workflow allow for its integration with existing proteomics software such as MaxQuant and Skyline, as well as existing statistical and visualization tools available in Bioconductor. Thus, ProteoModlR&#x02019;s computational framework will prove useful for a wide variety of quantitative mass spectrometry studies, including the comprehensive investigation and quantitative modeling of cellular signaling and biochemical pathways.</p></sec><sec id="Sec4"><title>Conclusions</title><p>Here we introduce ProteoModlR for quantitative mass spectrometry analysis of post-translationally modified peptides and proteins for functional proteomics of cell signaling.</p></sec></body><back><app-group><app id="App1"><sec id="Sec5"><title>Additional files</title><p>
<media position="anchor" xlink:href="12859_2017_1563_MOESM1_ESM.tif" id="MOESM1"><label>Additional file 1: Figure S1.</label><caption><p>Conceptual overview of the operations performed by ProteoModlR. (A) A set of proteoforms is digested into peptides and (B) mixed with an equimolar set of synthetic reference peptides (in blue). (C) MS signal-response is affected by differential ionization efficiency. Furthermore, MS quantification may present missing values. (D) ProteoModlR first annotates the available set of peptides, then (E) corrects errors introduced by technical and biological variability. Finally, (F) exact or approximate calculations are deployed to obtain PTM stoichiometry and abundance. (TIF 919 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM2_ESM.tif" id="MOESM2"><label>Additional file 2: Figure S2.</label><caption><p>Equimolar Isotopologue normalization corrects for technical variability across measurements, as demonstrated on simulated data. A) Quantitation across three replicate measurements of five peptides from a protein of interest (shades of red) and four peptides from reference proteins (shades of blue). (B) ProteoModlR corrects errors introduced by technical and biological variability. (C) Quantitation of heavy labeled equimolar standard peptides is affected by differential ionization efficiency and technical variability. (D) ProteoModlR equalizes the intensities of the standard isotopologues for each peptide independently. (TIF 9310 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM3_ESM.tif" id="MOESM3"><label>Additional file 3: Figure S3.</label><caption><p>Isotopologue normalization corrects for technical variability across measurements, as demonstrated on simulated data. (A) Quantitation across three replicate measurements of five peptides from a protein of interest (shades of red) and four peptides from reference proteins (shades of blue). (B) ProteoModlR corrects errors introduced by technical and biological variability. (C) Quantitation of heavy labeled standard peptides is also affected by technical variability. (D) If isotopologue normalization is chosen, ProteoModlR equalizes the intensities of the standard isotopologues for each peptide independently. (TIF 9821 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM4_ESM.tif" id="MOESM4"><label>Additional file 4: Figure S4.</label><caption><p>Total ion current normalization corrects for technical variability across measurements in absence of isotopically encoded standards, as demonstrated on simulated data. (A) Quantitation across three replicate measurements of five peptides from a protein of interest (shades of red) and four peptides from reference proteins (shades of blue). (B) ProteoModlR corrects errors introduced by technical and biological variability. (C) Total ion current is also affected by technical variability. (D) If total ion current normalization is chosen, ProteoModlR equalizes the sum of the intensities of all peptides in each sample. (TIF 9017 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM5_ESM.tif" id="MOESM5"><label>Additional file 5: Figure S5.</label><caption><p>Reference peptide normalization corrects for variations in the total protein content per cell across measurements, as demonstrated on simulated data. (A) Quantitation across three replicate measurements of five peptides from a protein of interest (shades of red) and four peptides from reference proteins (shades of blue). (B) ProteoModlR corrects errors introduced by biological factors that vary the total amount of protein per cell, equalizing the intensities of one or more peptides chosen as internal reference. (TIF 4660 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM6_ESM.pdf" id="MOESM6"><label>Additional file 6: Software documentation.</label><caption><p>The document contains detailed description of ProteoModlR implementation as well as user instructions. (PDF 196 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM7_ESM.tif" id="MOESM7"><label>Additional file 7: Figure S6.</label><caption><p>Output of exact (A) and approximate (B-D) calculations from simulated datasets. The input contained quantitation across three replicate measurements of four peptides, two of which phosphorylated. (TIF 25386 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM8_ESM.txt" id="MOESM8"><label>Additional file 8: Simulated Dataset 1.</label><caption><p>Normalization simulated datasets &#x02013; description. Detailed description of the simulated datasets used to test ProteoModlR&#x02019;s normalization module. (TXT 1 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM9_ESM.txt" id="MOESM9"><label>Additional file 9: Simulated Dataset 7.</label><caption><p>Analysis simulated datasets &#x02013; description. Detailed description of the simulated datasets used to test the analysis module ProteoModlR .(TXT 783 bytes)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM10_ESM.csv" id="MOESM10"><label>Additional file 10: Simulated Dataset 2.</label><caption><p>Normalization_dataset_no_error. The file contains a simulated datasets with identical intensity values for all peptides in all conditions. This dataset is provided as a reference to evaluate the accuracy of normalization strategies. (CSV 4 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM11_ESM.csv" id="MOESM11"><label>Additional file 11: Simulated Dataset 3.</label><caption><p>Normalization simulated dataset 1a. The file contains the simulated datasets used to model differences in peptide intensity introduced by uneven performance of the LC/MS instrumentation or unequal sample amount. The output shows the result of equimolar isotopologue normalization, as displayed in Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Figure S2. (CSV 4 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM12_ESM.csv" id="MOESM12"><label>Additional file 12: Simulated Dataset 4.</label><caption><p>Normalization simulated dataset 1b. The file contains the simulated datasets used to model differences in peptide intensity introduced by uneven performance of the LC/MS instrumentation or unequal sample amount. The output shows the result of isotopologue normalization, as displayed in Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Figure S3. (CSV 4 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM13_ESM.csv" id="MOESM13"><label>Additional file 13: Simulated Dataset 5.</label><caption><p>Normalization simulated dataset 2. The file contains the simulated datasets used to model differences in peptide intensity introduced by uneven performance of the LC/MS instrumentation or unequal sample amount, with no isotopically labelled internal standard. The output shows the result of normalization by total ion current, as displayed in Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S4. (CSV 2 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM14_ESM.csv" id="MOESM14"><label>Additional file 14: Simulated Dataset 6.</label><caption><p>Normalization simulated dataset 3. The file contains the simulated datasets used to model differences in the peptide intensity introduced by biological factors that vary the protein amount per cell by affecting the expression of a limited subset of the proteome. The output shows the result of isotopologue normalization followed by normalization by reference peptide, as displayed in Additional file <xref rid="MOESM5" ref-type="media">5</xref>: Figure S5. (CSV 5 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM15_ESM.csv" id="MOESM15"><label>Additional file 15: Simulated Dataset 8.</label><caption><p>Analysis simulated dataset 1. This simulated dataset models an input with complete annotation of peptides required for exact calculations in the Analysis module of ProteoModlR. This file provides a reference for approximate calculations, and demonstrates the annotation performed by the quality control module of ProteModlR. (CSV 5 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM16_ESM.csv" id="MOESM16"><label>Additional file 16: Simulated Dataset 9.</label><caption><p>Analysis simulated dataset 2. This simulated dataset models an input with incomplete annotation of peptides required for exact calculation of abundance in the Analysis module of ProteoModlR. (CSV 5 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM17_ESM.csv" id="MOESM17"><label>Additional file 17: Simulated Dataset 10.</label><caption><p>Analysis simulated dataset 3. This simulated dataset models an input with incomplete annotation of chemically modified peptides intensity, required for exact calculation of stoichiometry in the Analysis module of ProteoModlR. (CSV 5 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM18_ESM.csv" id="MOESM18"><label>Additional file 18: Simulated Dataset 11.</label><caption><p>Analysis simulated dataset 4. This simulated dataset models an input with incomplete annotation of non-chemically modified peptides intensity, required for exact calculation of stoichiometry in the Analysis module of ProteoModlR. (CSV 5 kb)</p></caption></media>
<media position="anchor" xlink:href="12859_2017_1563_MOESM19_ESM.tif" id="MOESM19"><label>Additional file 19: Table S1.</label><caption><p>Chemoforms available for abundance and stoichiometry calculations from experimentally derived data. The table contains the peptides available in the experimentally derived dataset (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>, [<xref ref-type="bibr" rid="CR19">19</xref>]) for protein LCK, ZAP70, NFIL3 and STAT5A. For each chemoform, modification status and <italic>Quality Control</italic> annotation is reported. (TIF 4000 kb)</p></caption></media>
</p></sec></app></app-group><glossary><title>Abbreviations</title><def-list><def-item><term>DDA</term><def><p>Data-dependent acquisition</p></def></def-item><def-item><term>ESI</term><def><p>Electro-spray ionization</p></def></def-item><def-item><term>MS</term><def><p>Mass spectrometry</p></def></def-item><def-item><term>PTM</term><def><p>Post-translational modification</p></def></def-item><def-item><term>SILAC</term><def><p>Stable isotope labeling by amino acids in cell culture</p></def></def-item><def-item><term>XIC</term><def><p>Extracted ion chromatogram</p></def></def-item></def-list></glossary><ack><title>Acknowledgements</title><p>We thank manuscript reviewers for constructive criticisms and Asaf Poran for advice on R programming.</p><sec id="FPar1"><title>Funding</title><p>P.C. is a fellow of the American-Italian Cancer Foundation. This work was supported by NIH K08 CA160660, NIH R21 CA188881, NIH R01 CA204396, NIH P30 CA008748, Burroughs Wellcome Fund, Gabrielle&#x02019;s Angel Foundation for Cancer Research, and Alex&#x02019;s Lemonade Stand Foundation for Childhood Cancer, and the Josie Robertson Investigator Program. A.K. is the Damon Runyon-Lumsden Foundation Clinical Investigator.</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p>The R implementation of ProteoModlR and relevant datasets are available at <ext-link ext-link-type="uri" xlink:href="https://github.com/kentsisresearchgroup/ProteoModlR">https://github.com/kentsisresearchgroup/ProteoModlR</ext-link>.</p></sec><sec id="FPar3"><title>Authors&#x02019; contributions</title><p>PC, MS, and AK designed the software and analyzed results. MS and SC wrote the R program. All authors contributed to writing the manuscript. All authors read and approved the final manuscript.</p></sec><sec id="FPar4"><title>Competing interests</title><p>All authors declare that they have no competing interests.</p></sec><sec id="FPar5"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="FPar6"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Aebersold</surname><given-names>R</given-names></name><name><surname>Mann</surname><given-names>M</given-names></name></person-group><article-title>Mass spectrometry-based proteomics</article-title><source>Nature</source><year>2003</year><volume>422</volume><issue>6928</issue><fpage>198</fpage><lpage>207</lpage><pub-id pub-id-type="doi">10.1038/nature01511</pub-id><?supplied-pmid 12634793?><pub-id pub-id-type="pmid">12634793</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhou</surname><given-names>F</given-names></name><name><surname>Lu</surname><given-names>Y</given-names></name><name><surname>Ficarro</surname><given-names>SB</given-names></name><name><surname>Adelmant</surname><given-names>G</given-names></name><name><surname>Jiang</surname><given-names>W</given-names></name><name><surname>Luckey</surname><given-names>CJ</given-names></name><name><surname>Marto</surname><given-names>JA</given-names></name></person-group><article-title>Genome-scale proteome quantification by DEEP SEQ mass spectrometry</article-title><source>Nat Commun</source><year>2013</year><volume>4</volume><fpage>2171</fpage><?supplied-pmid 23863870?><pub-id pub-id-type="pmid">23863870</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hebert</surname><given-names>AS</given-names></name><name><surname>Richards</surname><given-names>AL</given-names></name><name><surname>Bailey</surname><given-names>DJ</given-names></name><name><surname>Ulbrich</surname><given-names>A</given-names></name><name><surname>Coughlin</surname><given-names>EE</given-names></name><name><surname>Westphall</surname><given-names>MS</given-names></name><name><surname>Coon</surname><given-names>JJ</given-names></name></person-group><article-title>The one hour yeast proteome</article-title><source>Mol Cell Proteomics</source><year>2014</year><volume>13</volume><issue>1</issue><fpage>339</fpage><lpage>347</lpage><pub-id pub-id-type="doi">10.1074/mcp.M113.034769</pub-id><?supplied-pmid 24143002?><pub-id pub-id-type="pmid">24143002</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eng</surname><given-names>JK</given-names></name><name><surname>McCormack</surname><given-names>AL</given-names></name><name><surname>Yates</surname><given-names>JR</given-names></name></person-group><article-title>An approach to correlate tandem mass spectral data of peptides with amino acid sequences in a protein database</article-title><source>J Am Soc Mass Spectrom</source><year>1994</year><volume>5</volume><issue>11</issue><fpage>976</fpage><lpage>989</lpage><pub-id pub-id-type="doi">10.1016/1044-0305(94)80016-2</pub-id><?supplied-pmid 24226387?><pub-id pub-id-type="pmid">24226387</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Steen</surname><given-names>H</given-names></name><name><surname>Mann</surname><given-names>M</given-names></name></person-group><article-title>The ABC&#x02018;s (and XYZ&#x02019;s) of peptide sequencing</article-title><source>Nat Rev Mol Cell Biol</source><year>2004</year><volume>5</volume><issue>9</issue><fpage>699</fpage><lpage>711</lpage><pub-id pub-id-type="doi">10.1038/nrm1468</pub-id><?supplied-pmid 15340378?><pub-id pub-id-type="pmid">15340378</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kirkpatrick</surname><given-names>DS</given-names></name><name><surname>Gerber</surname><given-names>SA</given-names></name><name><surname>Gygi</surname><given-names>SP</given-names></name></person-group><article-title>The absolute quantification strategy: a general procedure for the quantification of proteins and post-translational modifications</article-title><source>Methods</source><year>2005</year><volume>35</volume><issue>3</issue><fpage>265</fpage><lpage>273</lpage><pub-id pub-id-type="doi">10.1016/j.ymeth.2004.08.018</pub-id><?supplied-pmid 15722223?><pub-id pub-id-type="pmid">15722223</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Graumann</surname><given-names>J</given-names></name><name><surname>Hubner</surname><given-names>NC</given-names></name><name><surname>Kim</surname><given-names>JB</given-names></name><name><surname>Ko</surname><given-names>K</given-names></name><name><surname>Moser</surname><given-names>M</given-names></name><name><surname>Kumar</surname><given-names>C</given-names></name><name><surname>Cox</surname><given-names>J</given-names></name><name><surname>Scholer</surname><given-names>H</given-names></name><name><surname>Mann</surname><given-names>M</given-names></name></person-group><article-title>Stable isotope labeling by amino acids in cell culture (SILAC) and proteome quantitation of mouse embryonic stem cells to a depth of 5,111 proteins</article-title><source>Mol Cell Proteomics</source><year>2007</year><volume>7</volume><issue>4</issue><fpage>672</fpage><lpage>683</lpage><pub-id pub-id-type="doi">10.1074/mcp.M700460-MCP200</pub-id><?supplied-pmid 18045802?><pub-id pub-id-type="pmid">18045802</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>R</given-names></name><name><surname>Dephoure</surname><given-names>N</given-names></name><name><surname>Haas</surname><given-names>W</given-names></name><name><surname>Huttlin</surname><given-names>EL</given-names></name><name><surname>Zhai</surname><given-names>B</given-names></name><name><surname>Sowa</surname><given-names>ME</given-names></name><name><surname>Gygi</surname><given-names>SP</given-names></name></person-group><article-title>Correct interpretation of comprehensive phosphorylation dynamics requires normalization by protein expression changes</article-title><source>Mol Cell Proteomics</source><year>2011</year><volume>10</volume><issue>8</issue><fpage>M111.009654</fpage><pub-id pub-id-type="doi">10.1074/mcp.M111.009654</pub-id><?supplied-pmid 21551504?><pub-id pub-id-type="pmid">21551504</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cox</surname><given-names>J</given-names></name><name><surname>Mann</surname><given-names>M</given-names></name></person-group><article-title>MaxQuant enables high peptide identification rates, individualized p.p.b.-range mass accuracies and proteome-wide protein quantification</article-title><source>Nat Biotechnol</source><year>2008</year><volume>26</volume><issue>12</issue><fpage>1367</fpage><lpage>1372</lpage><pub-id pub-id-type="doi">10.1038/nbt.1511</pub-id><?supplied-pmid 19029910?><pub-id pub-id-type="pmid">19029910</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cox</surname><given-names>J</given-names></name><name><surname>Neuhauser</surname><given-names>N</given-names></name><name><surname>Michalski</surname><given-names>A</given-names></name><name><surname>Scheltema</surname><given-names>RA</given-names></name><name><surname>Olsen</surname><given-names>JV</given-names></name><name><surname>Mann</surname><given-names>M</given-names></name></person-group><article-title>Andromeda: a peptide search engine integrated into the MaxQuant environment</article-title><source>J Proteome Res</source><year>2011</year><volume>10</volume><issue>4</issue><fpage>1794</fpage><lpage>1805</lpage><pub-id pub-id-type="doi">10.1021/pr101065j</pub-id><?supplied-pmid 21254760?><pub-id pub-id-type="pmid">21254760</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>MacLean</surname><given-names>B</given-names></name><name><surname>Tomazela</surname><given-names>DM</given-names></name><name><surname>Shulman</surname><given-names>N</given-names></name><name><surname>Chambers</surname><given-names>M</given-names></name><name><surname>Finney</surname><given-names>GL</given-names></name><name><surname>Frewen</surname><given-names>B</given-names></name><name><surname>Kern</surname><given-names>R</given-names></name><name><surname>Tabb</surname><given-names>DL</given-names></name><name><surname>Liebler</surname><given-names>DC</given-names></name><name><surname>MacCoss</surname><given-names>MJ</given-names></name></person-group><article-title>Skyline: an open source document editor for creating and analyzing targeted proteomics experiments</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><issue>7</issue><fpage>966</fpage><lpage>968</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btq054</pub-id><?supplied-pmid 20147306?><pub-id pub-id-type="pmid">20147306</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Choi</surname><given-names>M</given-names></name><name><surname>Chang</surname><given-names>C-Y</given-names></name><name><surname>Clough</surname><given-names>T</given-names></name><name><surname>Broudy</surname><given-names>D</given-names></name><name><surname>Killeen</surname><given-names>T</given-names></name><name><surname>MacLean</surname><given-names>B</given-names></name><name><surname>Vitek</surname><given-names>O</given-names></name></person-group><article-title>MSstats: an R package for statistical analysis of quantitative mass spectrometry-based proteomic experiments</article-title><source>Bioinformatics</source><year>2014</year><volume>30</volume><issue>17</issue><fpage>2524</fpage><lpage>2526</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btu305</pub-id><?supplied-pmid 24794931?><pub-id pub-id-type="pmid">24794931</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Linding</surname><given-names>R</given-names></name><name><surname>Jensen</surname><given-names>LJ</given-names></name><name><surname>Pasculescu</surname><given-names>A</given-names></name><name><surname>Olhovsky</surname><given-names>M</given-names></name><name><surname>Colwill</surname><given-names>K</given-names></name><name><surname>Bork</surname><given-names>P</given-names></name><name><surname>Yaffe</surname><given-names>MB</given-names></name><name><surname>Pawson</surname><given-names>T</given-names></name></person-group><article-title>NetworKIN: a resource for exploring cellular phosphorylation networks</article-title><source>Nucleic Acids Res</source><year>2008</year><volume>36</volume><issue>Database issue</issue><fpage>D695</fpage><lpage>9</lpage><?supplied-pmid 17981841?><pub-id pub-id-type="pmid">17981841</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><mixed-citation publication-type="other">R Core Team. R: A language and environment for statistical computing. Vienna: R Foundation for Statistical Computing. 2014. <ext-link ext-link-type="uri" xlink:href="http://www.R-project.org/">http://www.R-project.org/</ext-link>.</mixed-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gerber</surname><given-names>SA</given-names></name><name><surname>Rush</surname><given-names>J</given-names></name><name><surname>Stemman</surname><given-names>O</given-names></name><name><surname>Kirschner</surname><given-names>MW</given-names></name><name><surname>Gygi</surname><given-names>SP</given-names></name></person-group><article-title>Absolute quantification of proteins and phosphoproteins from cell lysates by tandem MS</article-title><source>Proc Natl Acad Sci U S A</source><year>2003</year><volume>100</volume><issue>12</issue><fpage>6940</fpage><lpage>6945</lpage><pub-id pub-id-type="doi">10.1073/pnas.0832254100</pub-id><?supplied-pmid 12771378?><pub-id pub-id-type="pmid">12771378</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gallien</surname><given-names>S</given-names></name><name><surname>Duriez</surname><given-names>E</given-names></name><name><surname>Crone</surname><given-names>C</given-names></name><name><surname>Kellmann</surname><given-names>M</given-names></name><name><surname>Moehring</surname><given-names>T</given-names></name><name><surname>Domon</surname><given-names>B</given-names></name></person-group><article-title>Targeted proteomic quantification on quadrupole-orbitrap mass spectrometer</article-title><source>Mol Cell Proteomics</source><year>2012</year><volume>11</volume><issue>12</issue><fpage>1709</fpage><lpage>1723</lpage><pub-id pub-id-type="doi">10.1074/mcp.O112.019802</pub-id><?supplied-pmid 22962056?><pub-id pub-id-type="pmid">22962056</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lange</surname><given-names>V</given-names></name><name><surname>Picotti</surname><given-names>P</given-names></name><name><surname>Domon</surname><given-names>B</given-names></name><name><surname>Aebersold</surname><given-names>R</given-names></name></person-group><article-title>Selected reaction monitoring for quantitative proteomics: a tutorial</article-title><source>Mol Syst Biol</source><year>2008</year><volume>4</volume><fpage>222</fpage><pub-id pub-id-type="doi">10.1038/msb.2008.61</pub-id><?supplied-pmid 18854821?><pub-id pub-id-type="pmid">18854821</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ross</surname><given-names>AH</given-names></name><name><surname>Baltimore</surname><given-names>D</given-names></name><name><surname>Eisen</surname><given-names>HN</given-names></name></person-group><article-title>Phosphotyrosine-containing proteins isolated by affinity chromatography with antibodies to a synthetic hapten</article-title><source>Nature</source><year>1981</year><volume>294</volume><issue>5842</issue><fpage>654</fpage><lpage>656</lpage><pub-id pub-id-type="doi">10.1038/294654a0</pub-id><?supplied-pmid 6171737?><pub-id pub-id-type="pmid">6171737</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ross</surname><given-names>SH</given-names></name><name><surname>Rollings</surname><given-names>C</given-names></name><name><surname>Anderson</surname><given-names>KE</given-names></name><name><surname>Hawkins</surname><given-names>PT</given-names></name><name><surname>Stephens</surname><given-names>LR</given-names></name><name><surname>Cantrell</surname><given-names>DA</given-names></name></person-group><article-title>Phosphoproteomic analyses of interleukin 2 signaling reveal integrated JAK kinase-dependent and -independent networks in CD8(+) T cells</article-title><source>Immunity</source><year>2016</year><volume>45</volume><issue>3</issue><fpage>685</fpage><lpage>700</lpage><pub-id pub-id-type="doi">10.1016/j.immuni.2016.07.022</pub-id><?supplied-pmid 27566939?><pub-id pub-id-type="pmid">27566939</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shi</surname><given-names>T</given-names></name><name><surname>Gao</surname><given-names>Y</given-names></name><name><surname>Gaffrey</surname><given-names>MJ</given-names></name><name><surname>Nicora</surname><given-names>CD</given-names></name><name><surname>Fillmore</surname><given-names>TL</given-names></name><name><surname>Chrisler</surname><given-names>WB</given-names></name><name><surname>Gritsenko</surname><given-names>MA</given-names></name><name><surname>Wu</surname><given-names>C</given-names></name><name><surname>He</surname><given-names>J</given-names></name><name><surname>Bloodsworth</surname><given-names>KJ</given-names></name><name><surname>Zhao</surname><given-names>R</given-names></name><name><surname>Camp</surname><given-names>DG</given-names></name><name><surname>Liu</surname><given-names>T</given-names></name><name><surname>Rodland</surname><given-names>KD</given-names></name><name><surname>Smith</surname><given-names>RD</given-names></name><name><surname>Wiley</surname><given-names>HS</given-names></name><name><surname>Qian</surname><given-names>W-J</given-names></name></person-group><article-title>Sensitive targeted quantification of ERK phosphorylation dynamics and stoichiometry in human cells without affinity enrichment</article-title><source>Anal Chem</source><year>2015</year><volume>87</volume><issue>2</issue><fpage>1103</fpage><lpage>1110</lpage><pub-id pub-id-type="doi">10.1021/ac503797x</pub-id><?supplied-pmid 25517423?><pub-id pub-id-type="pmid">25517423</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tsai</surname><given-names>C-F</given-names></name><name><surname>Wang</surname><given-names>Y-T</given-names></name><name><surname>Yen</surname><given-names>H-Y</given-names></name><name><surname>Tsou</surname><given-names>C-C</given-names></name><name><surname>Ku</surname><given-names>W-C</given-names></name><name><surname>Lin</surname><given-names>P-Y</given-names></name><name><surname>Chen</surname><given-names>H-Y</given-names></name><name><surname>Nesvizhskii</surname><given-names>AI</given-names></name><name><surname>Ishihama</surname><given-names>Y</given-names></name><name><surname>Chen</surname><given-names>Y-J</given-names></name></person-group><article-title>Large-scale determination of absolute phosphorylation stoichiometries in human cells by motif-targeting quantitative proteomics</article-title><source>Nat Commun</source><year>2015</year><volume>6</volume><fpage>6622</fpage><pub-id pub-id-type="doi">10.1038/ncomms7622</pub-id><?supplied-pmid 25814448?><pub-id pub-id-type="pmid">25814448</pub-id></element-citation></ref></ref-list></back></article>