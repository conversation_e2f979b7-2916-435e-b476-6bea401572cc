<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 2?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5006569</article-id><article-id pub-id-type="publisher-id">1206</article-id><article-id pub-id-type="doi">10.1186/s12859-016-1206-3</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>A genetic algorithm-based weighted ensemble method for predicting transposon-derived piRNAs</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Li</surname><given-names>Dingfang</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Luo</surname><given-names>Longqiang</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Zhang</surname><given-names>Wen</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Liu</surname><given-names>Feng</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Luo</surname><given-names>Fei</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label>School of Mathematics and Statistics, Wuhan University, Wuhan, 430072 China </aff><aff id="Aff2"><label>2</label>State Key Lab of Software Engineering, Wuhan University, Wuhan, 430072 China </aff><aff id="Aff3"><label>3</label>School of Computer, Wuhan University, Wuhan, 430072 China </aff><aff id="Aff4"><label>4</label>International School of Software, Wuhan University, Wuhan, 430072 China </aff></contrib-group><pub-date pub-type="epub"><day>31</day><month>8</month><year>2016</year></pub-date><pub-date pub-type="pmc-release"><day>31</day><month>8</month><year>2016</year></pub-date><pub-date pub-type="collection"><year>2016</year></pub-date><volume>17</volume><issue>1</issue><elocation-id>329</elocation-id><history><date date-type="received"><day>18</day><month>3</month><year>2016</year></date><date date-type="accepted"><day>24</day><month>8</month><year>2016</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2016</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>Predicting piwi-interacting RNA (piRNA) is an important topic in the small non-coding RNAs, which provides clues for understanding the generation mechanism of gamete. To the best of our knowledge, several machine learning approaches have been proposed for the piRNA prediction, but there is still room for improvements.</p></sec><sec><title>Results</title><p>In this paper, we develop a genetic algorithm-based weighted ensemble method for predicting transposon-derived piRNAs. We construct datasets for three species: <italic>Human</italic>, <italic>Mouse</italic> and <italic>Drosophila</italic>. For each species, we compile the balanced dataset and imbalanced dataset, and thus obtain six datasets to build and evaluate prediction models. In the computational experiments, the genetic algorithm-based weighted ensemble method achieves 10-fold cross validation AUC of 0.932, 0.937 and 0.995 on the balanced <italic>Human</italic> dataset, <italic>Mouse</italic> dataset and <italic>Drosophila</italic> dataset, respectively, and achieves AUC of 0.935, 0.939 and 0.996 on the imbalanced datasets of three species. Further, we use the prediction models trained on the <italic>Mouse</italic> dataset to identify piRNAs of other species, and the models demonstrate the good performances in the cross-species prediction.</p></sec><sec><title>Conclusions</title><p>Compared with other state-of-the-art methods, our method can lead to better performances. In conclusion, the proposed method is promising for the transposon-derived piRNA prediction. The source codes and datasets are available in <ext-link ext-link-type="uri" xlink:href="https://github.com/zw9977129/piRNAPredictor">https://github.com/zw9977129/piRNAPredictor</ext-link>.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s12859-016-1206-3) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>piRNA</kwd><kwd>Feature</kwd><kwd>Genetic algorithm</kwd><kwd>Ensemble learning</kwd></kwd-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2016</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1" sec-type="introduction"><title>Background</title><p>Non-coding RNAs (ncRNAs) are defined as the important functional RNA molecules which are not translated into proteins [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. According to lengths, ncRNAs are classified into two types: long ncRNAs and short ncRNAs. Usually, long ncRNAs consists of more than 200 nucleotides [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR4">4</xref>]. Short ncRNAs having 20&#x02009;~&#x02009;32 nt are defined as small ncRNAs, such as small interfering RNA (siRNA), microRNA (miRNA) and piwi-interacting RNA (piRNA) [<xref ref-type="bibr" rid="CR5">5</xref>]. piRNA is a distinct class of small ncRNAs expressed in animal cells, especially in germline cells, and the length of piRNA sequences ranges from 26 to 32 in general [<xref ref-type="bibr" rid="CR6">6</xref>&#x02013;<xref ref-type="bibr" rid="CR8">8</xref>]. Compared with miRNA, piRNA lacks conserved secondary structure motifs, and the presence of a 5&#x02019; uridine is usually observed in both vertebrates and invertebrates [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>].</p><p>piRNAs play an important role in the transposon silencing [<xref ref-type="bibr" rid="CR11">11</xref>&#x02013;<xref ref-type="bibr" rid="CR15">15</xref>]. About nearly one-third of the fruit fly and one-half of human genomes are transposon elements. These transposons move within the genome and induce insertions, deletions, and mutations, which may cause the genome instability. piRNA pathway is an important genome defense mechanism to maintain genome integrity. Loaded into PIWI proteins, piRNAs serve as a guide to target the transposon transcripts by sequence complementarity with mismatches, and then the transposon transcripts will be cleaved and degraded, producing secondary piRNAs, which is called ping-pong cycle in fruit fly [<xref ref-type="bibr" rid="CR13">13</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>]. Therefore, predicting transposon-derived piRNAs provides biological significance and insights into the piRNA pathway.</p><p>The wet method utilizes immunoprecipitation and deep sequencing to identify piRNAs [<xref ref-type="bibr" rid="CR18">18</xref>]. Since piRNAs are diverse and non-conserved, wet methods are time-consuming and costly [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>]. Since the development of information science, the piRNA prediction based on the known data becomes an alternative. As far as we know, several computational methods have been proposed for piRNA prediction. Betel et al. developed the position-specific usage method to recognize piRNAs [<xref ref-type="bibr" rid="CR19">19</xref>]. Zhang et al. utilized a <italic>k</italic>-mer feature, and adopted support vector machine (SVM) to build the classifier (named piRNApredictor) for piRNA prediction [<xref ref-type="bibr" rid="CR20">20</xref>]. Wang et al. proposed a method named Piano to predict piRNAs, by using piRNA-transposon interaction information and SVM [<xref ref-type="bibr" rid="CR21">21</xref>]. These methods exploited different features of piRNAs, and build the prediction models by using machine learning methods.</p><p>Motivated by previous works, we attempt to differentiate transposon-derived piRNAs from non-piRNAs based on the sequential and physicochemical features. As far as we know, there are several critical issues for developing high-accuracy models. Firstly, the accuracy of models is highly dependent on the diversity of features. In order to achieve high-accuracy models, we should consider as many sequence-derived features as possible. Secondly, how to effectively combine various features for high-accuracy models is very challenging. In the previous work [<xref ref-type="bibr" rid="CR22">22</xref>], we adopted the exhaustive search strategy to combine five sequence-derived features to predict piRNAs, and used the AUC scores of individual feature-based models as weights in the ensemble learning. However, the method can&#x02019;t effectively integrate a great amount of features (NP-hard complexity: 2<sup><italic>N</italic></sup>-1 combinations of features, <italic>N</italic> is the number of features), and the determination of weights is arbitrary.</p><p>In this paper, we develop a genetic algorithm-based weighted ensemble method (GA-WE) to effectively integrate twenty-three discriminative features for the piRNA prediction. Specifically, individual features-based models are constructed as base learners, and the weighted average of their outputs is adopted as the final scores in the stage of prediction. Genetic algorithm (GA) is to search for the optimal weights for the base learners. Moreover, the proposed method can determine the weights for each base learner in a self-tune manner.</p><p>We construct datasets for three species: <italic>Human</italic>, <italic>Mouse</italic> and <italic>Drosophila</italic>. For each species, we compile the balanced dataset and imbalanced dataset, and thus obtain six datasets to build and evaluate prediction models. In the 10-fold cross validation experiments, the GA-WE method achieves AUC of 0.932, 0.937 and 0.995 on the balanced <italic>Human</italic> dataset, <italic>Mouse</italic> dataset and <italic>Drosophila</italic> dataset, respectively, and achieves AUC of 0.935, 0.939 and 0.996 on the imbalanced datasets of three species. Further, we use the prediction models trained on the <italic>Mouse</italic> dataset to identify piRNAs of other species. The results demonstrate that the models can produce good performances in the cross-species prediction. Compared with other state-of-the-art methods, our method produces better performances as well as good robustness. Therefore, the proposed method is promising for the transposon-derived piRNA prediction.</p></sec><sec id="Sec2" sec-type="materials|methods"><title>Methods</title><sec id="Sec3"><title>Datasets</title><p>In this paper, we construct datasets for three species: <italic>Human</italic>, <italic>Mouse</italic> and <italic>Drosophila</italic>, and use them to build prediction models and make evaluations.</p><p>As shown in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>, raw real piRNAs, raw non-piRNA ncRNAs and transposons are downloaded from NONCODE version 3.0 [<xref ref-type="bibr" rid="CR23">23</xref>], UCSC Genome Browser [<xref ref-type="bibr" rid="CR24">24</xref>] and NCBI Gene Expression Omnibus [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR25">25</xref>]. NONCODE is an integrated knowledge database about non-coding RNAs [<xref ref-type="bibr" rid="CR23">23</xref>]. The UCSC Genome Browser is an interactive website offering access to genome sequence data from a variety of vertebrate and invertebrate species, integrated with a large collection of aligned annotations [<xref ref-type="bibr" rid="CR24">24</xref>]. The NCBI Gene Expression Omnibus is the largest fully public repository for high-throughput molecular abundance data, primarily gene expression data [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR25">25</xref>].<table-wrap id="Tab1"><label>Table 1</label><caption><p>Raw data about three species</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Species</th><th>Raw real piRNAs</th><th>Raw non-piRNA ncRNAs</th><th>Transposons</th></tr></thead><tbody><tr><td>
<italic>Human</italic>
</td><td>32,152 (NONCODE v3.0)</td><td>59,003 (NONCODE v3.0)</td><td>4,679,772 (UCSC, hg38)</td></tr><tr><td>
<italic>Mouse</italic>
</td><td>75,814 (NONCODE v3.0)</td><td>43,855 (NONCODE v3.0)</td><td>3,660,356 (UCSC, mm10)</td></tr><tr><td>
<italic>Drosophila</italic>
</td><td>12,903 (NCBI, GSE9138)</td><td>102,655 (NONCODE v3.0)</td><td>37,326 (UCSC, dm6)</td></tr></tbody></table></table-wrap></p><p>The datasets are compiled from the raw data (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). By aligning raw real piRNAs to transposons with SeqMap (three mismatches at most) [<xref ref-type="bibr" rid="CR26">26</xref>], the aligned real piRNAs are transposon-matched piRNAs, and they are adopted as the set of real piRNAs. The length of real piRNAs ranges from 16 to 35. To meet the length range of real piRNAs, we remove non-piRNA ncRNAs shorter than 16, and cut non-piRNA ncRNAs longer than 35 by simulating length distribution of real piRNAs. The cut sequences are then aligned to transposons, and the aligned ones are used as the set of pseudo piRNAs. The real piRNAs and the pseudo piRNAs for three species are shown in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>. In order to the build prediction models, we build the datasets based on real piRNAs and pseudo piRNAs. To avoid the data bias caused by different size of positive instances and negative instances, we construct both balanced datasets and imbalanced datasets for three species. For balanced datasets, all real piRNAs are adopted as positive instances, and we sample the same number of pseudo piRNAs as negative instances. For imbalanced datasets, we use all real piRNAs and pseudo piRNAs as positive instances and negative instances.<table-wrap id="Tab2"><label>Table 2</label><caption><p>Number of real piRNAs and pseudo piRNA</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Species</th><th>Real piRNAs</th><th>Pseudo piRNA</th></tr></thead><tbody><tr><td>
<italic>Human</italic>
</td><td>7,405</td><td>21,846</td></tr><tr><td>
<italic>Mouse</italic>
</td><td>13,998</td><td>40,712</td></tr><tr><td>
<italic>Drosophila</italic>
</td><td>9,214</td><td>22,855</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec4"><title>Features of piRNAs</title><p>For prediction, we should explore informative features that can characterize piRNAs and convert variable-length piRNA sequences into fixed-length feature vectors. Here, we consider various potential features that are widely used in biological sequence prediction. Among these features, six features have been utilized for the piRNA prediction, while the rest are taken into account for the first time. These sequence-derived features are briefly introduced as follows.</p><p>Spectrum profile: <italic>k</italic>-spectrum profile, also named <italic>k</italic>-mer feature, is to count the occurrences of <italic>k</italic>-mers (<italic>k</italic>-length contiguous strings) in sequences (<italic>k</italic>&#x02009;&#x02265;&#x02009;1), and its success has been proved by numerous bioinformatics applications [<xref ref-type="bibr" rid="CR27">27</xref>&#x02013;<xref ref-type="bibr" rid="CR30">30</xref>].</p><p>Mismatch profile: (<italic>k</italic>, <italic>m</italic>)-mismatch profile also counts the occurrences of <italic>k</italic>-mers, but allows max <italic>m</italic>&#x000a0;(<italic>m</italic>&#x02009;&#x0003c;&#x02009;<italic>k</italic>) inexact matching, which is the penalization of spectrum profile [<xref ref-type="bibr" rid="CR30">30</xref>, <xref ref-type="bibr" rid="CR31">31</xref>].</p><p>Subsequence profile: (<italic>k</italic>, <italic>w</italic>)-subsequence profile considers not only the contiguous <italic>k</italic>-mers but also the non- contiguous <italic>k</italic>-mers, and the penalty factor <italic>w</italic>&#x000a0;(0&#x02009;&#x02264;&#x02009;<italic>w</italic>&#x02009;&#x02264;&#x02009;1) is used to penalize the gap of non-contiguous <italic>k</italic>-mers [<xref ref-type="bibr" rid="CR30">30</xref>, <xref ref-type="bibr" rid="CR32">32</xref>].</p><p>Reverse compliment <italic>k</italic>-mer (<italic>k</italic>-RevcKmer): <italic>k</italic>-RevcKmer is a variant of the basic <italic>k-</italic>mer, in which the <italic>k</italic>-mers are not expected to be strand-specific [<xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR33">33</xref>, <xref ref-type="bibr" rid="CR34">34</xref>].</p><p>Parallel correlation pseudo dinucleotide composition (PCPseDNC): PCPseDNC is proposed to avoid losing the physicochemical properties of dinucleotides. PCPseDNC of a sequence consists of two components, the first component represents the occurrences of different dinucleotides, while the other component reflects the physicochemical properties of dinucleotides [<xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR35">35</xref>].</p><p>Three features: parallel correlation pseudo trinucleotide composition (PCPseTNC), series correlation pseudo dinucleotide composition (SCPseDNC) and series correlation pseudo trinucleotide composition (SCPseTNC) are similar to the PCPseDNC. PCPseTNC considers the occurrences of trinucleotides and their physicochemical properties, and SCPseDNC and SCPseTNC consider series correlations of physicochemical properties of dinucleotides or trinucleotides [<xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR36">36</xref>].</p><p>Sparse profile [<xref ref-type="bibr" rid="CR37">37</xref>] and position-specific scoring matrix (PSSM) [<xref ref-type="bibr" rid="CR38">38</xref>&#x02013;<xref ref-type="bibr" rid="CR40">40</xref>] are usually generated from the fixed-length sequences. Here, we use a simple strategy to process the variable-length sequences, and obtain the features. We truncate the first <italic>d</italic> nucleotides of long sequences which lengths are more than <italic>d</italic>, and extend short sequences which lengths are less than <italic>d</italic> by adding the null character. Here, &#x02018;<italic>E</italic>&#x02019; represent the null character, which are added to the short sequences to meet the length <italic>d.</italic> In this way, all variable-length sequences are converted into fixed-length sequences, and the fixed-length sequences consist of five letters {<italic>A</italic>,&#x02009;<italic>C</italic>,&#x02009;<italic>G</italic>,&#x02009;<italic>T</italic>,&#x02009;<italic>E</italic>}. For the sparse profile, by encoding each letter of sequence as a 5<italic>-</italic>bit vector with 4 bits set to zero and 1 bit set to one, the sparse profile of a sequence is obtained by merging the bit vector for its letters. For the PSSM feature, PSSM can be calculated on the fixed-length sequences consisted of five letters {<italic>A</italic>,&#x02009;<italic>C</italic>,&#x02009;<italic>G</italic>,&#x02009;<italic>T</italic>,&#x02009;<italic>E</italic>} [<xref ref-type="bibr" rid="CR38">38</xref>&#x02013;<xref ref-type="bibr" rid="CR40">40</xref>]. Given a new sequence, it is truncated or extended, and then is encoded by PSSM as the feature vector. The PSSM representation of sequence <italic>x</italic>&#x02009;=&#x02009;<italic>R</italic><sub>1</sub><italic>R</italic><sub>2</sub>&#x02009;&#x02026;&#x02009;<italic>R</italic><sub><italic>d</italic></sub> is defined as:<disp-formula id="Equa"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {f_d}^{PSSM}(x)=\left( score\;\left({R}_1\right),\; score\left({R}_2\right),\dots,\;score\left({R}_d\right)\right) $$\end{document}</tex-math><mml:math id="M2"><mml:msup><mml:msub><mml:mi>f</mml:mi><mml:mi>d</mml:mi></mml:msub><mml:mi mathvariant="italic">PSSM</mml:mi></mml:msup><mml:mfenced close=")" open="("><mml:mi>x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="italic">score</mml:mi><mml:mspace width="0.12em"/><mml:mfenced close=")" open="("><mml:msub><mml:mi>R</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:mi mathvariant="italic">score</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>R</mml:mi><mml:mn>2</mml:mn></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:mspace width="0.12em"/><mml:mi mathvariant="italic">score</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>R</mml:mi><mml:mi>d</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12859_2016_1206_Article_Equa.gif" position="anchor"/></alternatives></disp-formula>where<disp-formula id="Equb"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ score\left({R}_k\right)=\left\{\begin{array}{l}m\left({R}_k\right),\kern0.75em {R}_k\in \left\{A,C,G,T\right\}\\ {}0,\kern2.75em {R}_k=E\end{array}\right.,k=1,2,\dots, d $$\end{document}</tex-math><mml:math id="M4"><mml:mi mathvariant="italic">score</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>R</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable columnalign="left"><mml:mtr><mml:mtd><mml:mi>m</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>R</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="0.75em"/><mml:msub><mml:mi>R</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mfenced close="}" open="{" separators=",,,"><mml:mi>A</mml:mi><mml:mi>C</mml:mi><mml:mi>G</mml:mi><mml:mi>T</mml:mi></mml:mfenced></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mspace width="2.75em"/><mml:msub><mml:mi>R</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mi>E</mml:mi></mml:mtd></mml:mtr></mml:mtable></mml:mfenced><mml:mo>,</mml:mo><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:mi>d</mml:mi></mml:math><graphic xlink:href="12859_2016_1206_Article_Equb.gif" position="anchor"/></alternatives></disp-formula>and <italic>m</italic>(<italic>R</italic><sub><italic>k</italic></sub>) represents the score of <italic>R</italic><sub><italic>k</italic></sub> in the <italic>k</italic>-th column of PSSM, if <italic>R</italic><sub><italic>k</italic></sub>&#x02009;&#x02208;&#x02009;{<italic>A</italic>,&#x02009;<italic>C</italic>,&#x02009;<italic>G</italic>,&#x02009;<italic>T</italic>},&#x02009;<italic>k</italic>&#x02009;=&#x02009;1,&#x02009;2,&#x02009;&#x02026;,&#x02009;<italic>d</italic>.</p><p>Local structure-sequence triplet elements (LSSTE): LSSTE adopts the piRNA-transposon interaction information to extract 32 different triplet elements, which contain the structural information of transposon-piRNA alignment as well as the piRNA sequence information [<xref ref-type="bibr" rid="CR21">21</xref>, <xref ref-type="bibr" rid="CR41">41</xref>, <xref ref-type="bibr" rid="CR42">42</xref>].</p><p>A total of twenty-three feature vectors are finally obtained, and they are summarized in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>.<table-wrap id="Tab3"><label>Table 3</label><caption><p>Twenty-three sequence-derived features</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Index</th><th>Feature</th><th>Dimension</th><th>Parameter</th><th>Annotation</th></tr></thead><tbody><tr><td>F1</td><td>1-Spectrum Profile</td><td>4</td><td>No Parameters</td><td>Used in [<xref ref-type="bibr" rid="CR20">20</xref>]</td></tr><tr><td>F2</td><td>2-Spectrum Profile</td><td>16</td><td>No Parameters</td><td>Used in [<xref ref-type="bibr" rid="CR20">20</xref>]</td></tr><tr><td>F3</td><td>3-Spectrum Profile</td><td>64</td><td>No Parameters</td><td>Used in [<xref ref-type="bibr" rid="CR20">20</xref>]</td></tr><tr><td>F4</td><td>4-Spectrum Profile</td><td>256</td><td>No Parameters</td><td>Used in [<xref ref-type="bibr" rid="CR20">20</xref>]</td></tr><tr><td>F5</td><td>5-Spectrum Profile</td><td>1024</td><td>No Parameters</td><td>Used in [<xref ref-type="bibr" rid="CR20">20</xref>]</td></tr><tr><td>F6</td><td>(3, <italic>m</italic>)-mismatch profile</td><td>64</td><td>
<italic>m</italic>: the max mismatches</td><td>New features</td></tr><tr><td>F7</td><td>(4, <italic>m</italic>)-mismatch profile</td><td>256</td><td>
<italic>m</italic>: the max mismatches</td><td>New features</td></tr><tr><td>F8</td><td>(5, <italic>m</italic>)-mismatch profile</td><td>1024</td><td>
<italic>m</italic>: the max mismatches</td><td>New features</td></tr><tr><td>F9</td><td>(3, <italic>w</italic>)-subsequence profile</td><td>64</td><td>
<italic>w</italic>: penalty for the non-contiguous matching</td><td>New features</td></tr><tr><td>F10</td><td>(4, <italic>w</italic>)-subsequence profile</td><td>256</td><td>
<italic>w</italic>: penalty for the non-contiguous matching</td><td>New features</td></tr><tr><td>F11</td><td>(5, <italic>w</italic>)-subsequence profile</td><td>1024</td><td>
<italic>w</italic>: penalty for the non-contiguous matching</td><td>New features</td></tr><tr><td>F12</td><td>1-RevcKmer</td><td>2</td><td>No Parameters</td><td>New features</td></tr><tr><td>F13</td><td>2-RevcKmer</td><td>10</td><td>No Parameters</td><td>New features</td></tr><tr><td>F14</td><td>3-RevcKmer</td><td>32</td><td>No Parameters</td><td>New features</td></tr><tr><td>F15</td><td>4-RevcKmer</td><td>136</td><td>No Parameters</td><td>New features</td></tr><tr><td>F16</td><td>5-RevcKmer</td><td>528</td><td>No Parameters</td><td>New features</td></tr><tr><td>F17</td><td>PCPseDNC</td><td>16&#x02009;+&#x02009;<italic>&#x003bb;</italic>
</td><td>
<italic>&#x003bb;</italic>: the highest counted rank of the correlation</td><td>New features</td></tr><tr><td>F18</td><td>PCPseTNC</td><td>64&#x02009;+&#x02009;<italic>&#x003bb;</italic>
</td><td>
<italic>&#x003bb;</italic>: the highest counted rank of the correlation</td><td>New features</td></tr><tr><td>F19</td><td>SCPseDNC</td><td>16&#x02009;+&#x02009;6&#x02009;&#x000d7;&#x02009;<italic>&#x003bb;</italic>
</td><td>
<italic>&#x003bb;</italic>: the highest counted rank of the correlation</td><td>New features</td></tr><tr><td>F20</td><td>SCPseTNC</td><td>64&#x02009;+&#x02009;12&#x02009;&#x000d7;&#x02009;<italic>&#x003bb;</italic>
</td><td>
<italic>&#x003bb;</italic>: the highest counted rank of the correlation</td><td>New features</td></tr><tr><td>F21</td><td>Sparse Profile</td><td>5&#x02009;&#x000d7;&#x02009;<italic>d</italic>
</td><td>
<italic>d</italic>: the fixed length of sequences</td><td>New features</td></tr><tr><td>F22</td><td>PSSM</td><td>
<italic>d</italic>
</td><td>
<italic>d</italic>: the fixed length of sequences</td><td>New features</td></tr><tr><td>F23</td><td>LSSTE</td><td>32</td><td>No parameters</td><td>Used in [<xref ref-type="bibr" rid="CR21">21</xref>]</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec5"><title>The GA-based weighted ensemble method</title><p>In the view of information science, a variety of features can bring diverse information, and the combination of various features can lead to better performance than individual features [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR43">43</xref>&#x02013;<xref ref-type="bibr" rid="CR46">46</xref>]. Ensemble learning is a sophisticated feature combination technique widely used in bioinformatics. Its success has been proved by numerous bioinformatics applications, such as the prediction of B-cell epitopes [<xref ref-type="bibr" rid="CR44">44</xref>] and the prediction of immunogenic T-cell epitopes [<xref ref-type="bibr" rid="CR45">45</xref>].</p><p>To the best of our knowledge, there are two crucial issues for designing good ensemble systems, i.e. base learners and combination rules. First, the training sequences are encoded into different feature vectors, respectively, and multiple base learners are constructed on these feature vectors by using classification engines. We compare two most popular classification methods, random forest (RF) [<xref ref-type="bibr" rid="CR47">47</xref>] and support vector machine (SVM) [<xref ref-type="bibr" rid="CR48">48</xref>] (results are given in the section &#x02018;<xref rid="Sec6" ref-type="sec">Results and Discussion</xref>&#x02019;), and finally adopt RF as the basic classification engine because of its high efficiency and high accuracy. Then, how to combine the outputs of base learners is crucial for the success of our ensemble system. Our ensemble learning adopts the weighted average of outputs from base learners as the final score. However, the determination of weights is difficult. In this paper, we develop a genetic algorithm (GA)-based weighted ensemble method, which can automatically determine the optimal weights and construct high-accuracy prediction models.</p><p>Given <italic>N</italic> features, we can construct <italic>N</italic> base learners: <italic>f</italic><sub>1</sub>,&#x02009;<italic>f</italic><sub>2</sub>,&#x02009;&#x02026;,&#x02009;<italic>f</italic><sub><italic>N</italic></sub> on training set. <italic>w</italic><sub>1</sub>,&#x02009;<italic>w</italic><sub>2</sub>,&#x02009;&#x02026;,&#x02009;<italic>w</italic><sub><italic>N</italic></sub> (&#x02211;<sub arrange="stack"><italic>i</italic>&#x02009;=&#x02009;1</sub><sup arrange="stack"><italic>N</italic></sup><italic>w</italic><sub><italic>i</italic></sub>,&#x000a0;0&#x02009;&#x02264;&#x02009;<italic>w</italic><sub><italic>i</italic></sub>&#x02009;&#x02264;&#x02009;1, <italic>i</italic>&#x02009;=&#x02009;1,&#x02009;2,&#x02009;&#x02026;,&#x02009;<italic>N</italic>) represent the corresponding weights. For a testing sequence <italic>x</italic>, <italic>f</italic><sub><italic>i</italic></sub>(<italic>x</italic>)&#x02009;&#x02208;&#x02009;[0,&#x02009;1] represents the probability of predicting <italic>x</italic> as real piRNA, <italic>i</italic>&#x02009;=&#x02009;1,&#x02009;2,&#x02009;&#x02026;,&#x02009;<italic>N</italic>, and the final predicted results of the weighted ensemble model is given as:<disp-formula id="Equc"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ F(x)={\displaystyle {\sum}_{i=1}^N{w}_i{f}_i(x)} $$\end{document}</tex-math><mml:math id="M6"><mml:mi>F</mml:mi><mml:mfenced close=")" open="("><mml:mi>x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mstyle displaystyle="true"><mml:msubsup><mml:mo stretchy="true">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>N</mml:mi></mml:msubsup><mml:mrow><mml:msub><mml:mi>w</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>f</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi>x</mml:mi></mml:mfenced></mml:mrow></mml:mstyle></mml:math><graphic xlink:href="12859_2016_1206_Article_Equc.gif" position="anchor"/></alternatives></disp-formula></p><p>As discussed above, the optimal weights are very important for the weighted ensemble model. We consider the determination of weights as an optimization problem and adopt the genetic algorithm (GA) to search the optimal weights. GA is a search approach that simulates the process of natural selection. It can effectively search the interesting space and easily solve complex problems without requiring the prior knowledge about the space. Here, we use the adaptive genetic algorithm [<xref ref-type="bibr" rid="CR49">49</xref>]. In the adaptive genetic algorithm, crossover probability and mutation probability are dynamically adjusted according to the fitness scores of chromosomes. The size of an initial population is 1000 chromosomes, and the iteration number is 500.</p><p>The flowchart of the GA-WE method is shown in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>. In each training-testing process, the dataset is split into the training set, the validation set and the testing set. In the GA optimization, a chromosome represents weights. For each chromosome (weights), the weighted ensemble model is constructed on the training set, and makes predictions for the validation set. The AUC score of the weighted ensemble model on the validation set is taken as the fitness of the chromosome. After randomly generating an initial population, the population is updated by three operators: selection, crossover and mutation, and the best individual with a chromosome will be obtained. Finally, the weighted ensemble model with the optimal weights is used to make predictions for the testing set.<fig id="Fig1"><label>Fig. 1</label><caption><p>Flowchart of the GA-based weighted ensemble method</p></caption><graphic xlink:href="12859_2016_1206_Fig1_HTML" id="MO1"/></fig></p></sec></sec><sec id="Sec6" sec-type="results"><title>Results and discussion</title><sec id="Sec7"><title>Performance evaluation metrics</title><p>The proposed methods are evaluated by the 10-fold cross validation (10-CV). In the 10-CV, a dataset is randomly split into 10 subsets with equal size. For each round of 10-CV, 8 subsets are used as the training set, 1 subset is used as the validation set and the rest one is considered as the testing set. Prediction models are constructed on the training set, and the parameters or optimal weights of models are determined on the validation set. Finally, optimized prediction models are adopted to predict the testing set. This processing is repeated until all subsets are ever used for testing.</p><p>Here, we adopt several metrics to assess the performances of prediction models, including the accuracy (ACC), sensitivity (SN), specificity (SP) and the AUC score (the area under the ROC curve). These metrics are defined as:<disp-formula id="Equd"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ SN=\frac{TP}{TP+FN} $$\end{document}</tex-math><mml:math id="M8"><mml:mi>S</mml:mi><mml:mi>N</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>T</mml:mi><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>T</mml:mi><mml:mi>P</mml:mi><mml:mo>+</mml:mo><mml:mi>F</mml:mi><mml:mi>N</mml:mi></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2016_1206_Article_Equd.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Eque"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ SP=\frac{TN}{TN+FP} $$\end{document}</tex-math><mml:math id="M10"><mml:mi>S</mml:mi><mml:mi>P</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>T</mml:mi><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mi>T</mml:mi><mml:mi>N</mml:mi><mml:mo>+</mml:mo><mml:mi>F</mml:mi><mml:mi>P</mml:mi></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2016_1206_Article_Eque.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equf"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ ACC=\frac{TP+TN}{TP+TN+FP+FN} $$\end{document}</tex-math><mml:math id="M12"><mml:mi>A</mml:mi><mml:mi>C</mml:mi><mml:mi>C</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>T</mml:mi><mml:mi>P</mml:mi><mml:mo>+</mml:mo><mml:mi>T</mml:mi><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mi>T</mml:mi><mml:mi>P</mml:mi><mml:mo>+</mml:mo><mml:mi>T</mml:mi><mml:mi>N</mml:mi><mml:mo>+</mml:mo><mml:mi>F</mml:mi><mml:mi>P</mml:mi><mml:mo>+</mml:mo><mml:mi>F</mml:mi><mml:mi>N</mml:mi></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2016_1206_Article_Equf.gif" position="anchor"/></alternatives></disp-formula></p><p>Where TP, FP, TN and FN are the numbers of true positives, false positives, true negatives and false negatives, respectively. The ROC curve is plotted by using the false positive rate (1-specificity) against the true positive rate (sensitivity) for different cutoff thresholds. Here, we consider the AUC as the primary metric, for it assesses the performance regardless of any threshold.</p></sec><sec id="Sec8"><title>Parameters of various features</title><p>As shown in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>, we consider twenty-three sequence-derived features to develop prediction models. Since subsequence profile, PCPseDNC, PCPseTNC, SCPseDNC, SCPseTNC, sparse profile and PSSM have parameters, we discuss how to determine the parameters based on the balanced <italic>Human</italic> dataset, and use them in the following studies. Considering the parameter <italic>&#x003bb;</italic> and <italic>d</italic> refer to the length of piRNAs, we analyze the length distribution of piRNAs in three species (<italic>Human</italic>, <italic>Mouse</italic> and <italic>Drosophila</italic>). As shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, the length of piRNAs ranges from 16 to 35, and reaches the peak at 30 for <italic>Human</italic> and <italic>Mouse</italic>, and 25 for <italic>Drosophila</italic>. Here, the impacts of parameters are evaluated according to the 10-CV performances of corresponding models.<fig id="Fig2"><label>Fig. 2</label><caption><p>The length distribution of piRNAs in three species (<italic>Human</italic>, <italic>Mouse</italic> and <italic>Drosophila</italic>)</p></caption><graphic xlink:href="12859_2016_1206_Fig2_HTML" id="MO2"/></fig></p><p>In the mismatch profile, the parameter <italic>m</italic> represents the max mismatches. Here, we assume that <italic>m</italic> does not exceed one third of length of <italic>k</italic>-mers. Therefore, (3, 1)-mismatch profile, (4, 1)-mismatch profile and (5, 1)-mismatch profile are used.</p><p>In the subsequence profile, the parameter <italic>w</italic> represents the gap penalty of non-contiguous <italic>k</italic>-mers. As shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> (a), <italic>w</italic>&#x02009;=&#x02009;1 produces the best AUC scores for (3, <italic>w</italic>)-subsequence profile, (4, <italic>w</italic>)- subsequence profile and (5, <italic>w</italic>)-subsequence profile. Therefore, (3, 1)-subsequence profile, (4, 1)-subsequence profile and (5, 1)-subsequence profile are finally adopted in the following study.<fig id="Fig3"><label>Fig. 3</label><caption><p>
<bold>a</bold> AUC scores of the (<italic>k</italic>, <italic>w</italic>)-subsequence profile-based models with the variation of parameter <italic>w</italic> on balanced <italic>Human</italic> dataset; <bold>b</bold> AUC scores of the PCPseDNC, PCPseTNC, SCPseDNC and SCPseTNC-based models with the variation of the parameter <italic>&#x003bb;</italic> on balanced <italic>Human</italic> dataset; <bold>c</bold> AUC scores of the sparse profile and PSSM-based models with the variation of the parameter <italic>d</italic> on balanced <italic>Human</italic> dataset</p></caption><graphic xlink:href="12859_2016_1206_Fig3_HTML" id="MO3"/></fig></p><p>In the PCPseDNC, PCPseTNC, SCPseDNC and SCPseTNC, the parameter <italic>&#x003bb;</italic> represents the highest counted rank of the correlation, 1&#x02009;&#x02264;&#x02009;<italic>&#x003bb;</italic>&#x02009;&#x02264;&#x02009;<italic>L</italic>&#x02009;&#x02212;&#x02009;2 (for the PCPseDNC and SCPseDNC); 1&#x02009;&#x02264;&#x02009;<italic>&#x003bb;</italic>&#x02009;&#x02264;&#x02009;<italic>L</italic>&#x02009;&#x02212;&#x02009;3 (for the PCPseTNC and SCPseTNC) [<xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR36">36</xref>]. <italic>L</italic> is the length of shortest piRNA sequences, and is 16 according to Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>. To test the impact of the parameter <italic>&#x003bb;</italic> on the four features, we construct the prediction models by using different values. As shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> (b). <italic>&#x003bb;</italic>&#x02009;=&#x02009;1 leads to the best AUC scores for PCPseDNC, PCPseTNC, SCPseDNC and SCPseTNC. Therefore, the best parameters are adopted for the final prediction models.</p><p>In the sparse profile and PSSM, the parameter <italic>d</italic> represents the fixed length of sequences. As show in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, the lengths of piRNAs range from 16 to 35. Therefore, the prediction models are constructed based on different length<italic>s</italic>. As shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> (<xref rid="Fig3" ref-type="fig">c</xref>), <italic>d</italic>&#x02009;=&#x02009;35 produces the best AUC scores for the sparse profile and PSSM feature. Therefore, we set the parameter <italic>d</italic> as 35 for the sparse profile feature and the PSSM feature.</p></sec><sec id="Sec9"><title>Evaluation of various features</title><p>After discussing feature parameters, we compare the capabilities of various features for the piRNA prediction. Here, individual feature-based models are constructed on balanced <italic>Human</italic> dataset and imbalanced <italic>Human</italic> dataset by using classification engines, and the performances of these models are evaluated by 10-CV.</p><p>To test different classifiers, we respectively adopt the random forest (RF) and support vector machine (SVM) to build the individual feature-based prediction models. Here, we use the python package &#x0201c;scikit-learn&#x0201d; to implement RF and SVM, and default values are adopted for parameters. The results demonstrate that RF can produce better performances in most cases (13 out of the 23 individual feature-based models). Moreover, RF runs much faster than SVM, and it is very important for implementing the following experiments. Results of RF models and SVM models are provided in the Additional files <xref rid="MOESM1" ref-type="media">1</xref> and <xref rid="MOESM2" ref-type="media">2</xref>. For these reasons, RF is adopted in the following study.</p><p>To test the impacts of the ratio of positive instances versus negative instances, we build the individual feature-based prediction models based on the balanced human datasets and the imbalanced human dataset. As shown in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref> and Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref>, the prediction models produce similar results on the balanced dataset and imbalanced dataset, indicating that they are robust to the different datasets. The performances of individual feature-based models help to rank the importance of features. According to Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref> and Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref>, the sparse profile yields the best results among these features, and the performance of LSSTE is much poorer than that of other features. Therefore, we adopt features indexed from F1 to F22 (&#x0201c;F1&#x02009;~&#x02009;F22&#x0201d;) for the final ensemble models.<table-wrap id="Tab4"><label>Table 4</label><caption><p>The performances of individual feature-based models on balanced <italic>Human</italic> dataset</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Index</th><th>Feature</th><th>AUC</th><th>ACC</th><th>SN</th><th>SP</th></tr></thead><tbody><tr><td>F1</td><td>1-Spectrum Profile</td><td char="." align="char">0.754</td><td char="." align="char">0.690</td><td char="." align="char">0.731</td><td char="." align="char">0.649</td></tr><tr><td>F2</td><td>2-Spectrum Profile</td><td char="." align="char">0.841</td><td char="." align="char">0.756</td><td char="." align="char">0.780</td><td char="." align="char">0.732</td></tr><tr><td>F3</td><td>3-Spectrum Profile</td><td char="." align="char">0.839</td><td char="." align="char">0.750</td><td char="." align="char">0.747</td><td char="." align="char">0.754</td></tr><tr><td>F4</td><td>4-Spectrum Profile</td><td char="." align="char">0.829</td><td char="." align="char">0.740</td><td char="." align="char">0.732</td><td char="." align="char">0.748</td></tr><tr><td>F5</td><td>5-Spectrum Profile</td><td char="." align="char">0.802</td><td char="." align="char">0.718</td><td char="." align="char">0.681</td><td char="." align="char">0.755</td></tr><tr><td>F6</td><td>(3,1)-Mismatch Profile</td><td char="." align="char">0.862</td><td char="." align="char">0.772</td><td char="." align="char">0.819</td><td char="." align="char">0.725</td></tr><tr><td>F7</td><td>(4,1)-Mismatch Profile</td><td char="." align="char">0.854</td><td char="." align="char">0.761</td><td char="." align="char">0.788</td><td char="." align="char">0.734</td></tr><tr><td>F8</td><td>(5,1)-Mismatch Profile</td><td char="." align="char">0.842</td><td char="." align="char">0.750</td><td char="." align="char">0.754</td><td char="." align="char">0.747</td></tr><tr><td>F9</td><td>(3,1)-Subsequence Profile</td><td char="." align="char">0.850</td><td char="." align="char">0.767</td><td char="." align="char">0.809</td><td char="." align="char">0.725</td></tr><tr><td>F10</td><td>(4,1)-Subsequence Profile</td><td char="." align="char">0.866</td><td char="." align="char">0.782</td><td char="." align="char">0.821</td><td char="." align="char">0.743</td></tr><tr><td>F11</td><td>(5,1)-Subsequence Profile</td><td char="." align="char">0.875</td><td char="." align="char">0.791</td><td char="." align="char">0.829</td><td char="." align="char">0.754</td></tr><tr><td>F12</td><td>1-RevcKmer</td><td char="." align="char">0.746</td><td char="." align="char">0.699</td><td char="." align="char">0.889</td><td char="." align="char">0.509</td></tr><tr><td>F13</td><td>2-RevcKmer</td><td char="." align="char">0.803</td><td char="." align="char">0.724</td><td char="." align="char">0.774</td><td char="." align="char">0.673</td></tr><tr><td>F14</td><td>3-RevcKmer</td><td char="." align="char">0.818</td><td char="." align="char">0.732</td><td char="." align="char">0.765</td><td char="." align="char">0.698</td></tr><tr><td>F15</td><td>4-RevcKmer</td><td char="." align="char">0.808</td><td char="." align="char">0.718</td><td char="." align="char">0.717</td><td char="." align="char">0.718</td></tr><tr><td>F16</td><td>5-RevcKmer</td><td char="." align="char">0.791</td><td char="." align="char">0.702</td><td char="." align="char">0.658</td><td char="." align="char">0.746</td></tr><tr><td>F17</td><td>PCPseDNC</td><td char="." align="char">0.836</td><td char="." align="char">0.757</td><td char="." align="char">0.776</td><td char="." align="char">0.738</td></tr><tr><td>F18</td><td>PCPseTNC</td><td char="." align="char">0.849</td><td char="." align="char">0.765</td><td char="." align="char">0.787</td><td char="." align="char">0.742</td></tr><tr><td>F19</td><td>SCPseDNC</td><td char="." align="char">0.833</td><td char="." align="char">0.754</td><td char="." align="char">0.770</td><td char="." align="char">0.739</td></tr><tr><td>F20</td><td>SCPseTNC</td><td char="." align="char">0.832</td><td char="." align="char">0.751</td><td char="." align="char">0.777</td><td char="." align="char">0.725</td></tr><tr><td>F21</td><td>Sparse Profile</td><td char="." align="char">0.904</td><td char="." align="char">0.819</td><td char="." align="char">0.815</td><td char="." align="char">0.824</td></tr><tr><td>F22</td><td>PSSM</td><td char="." align="char">0.880</td><td char="." align="char">0.807</td><td char="." align="char">0.815</td><td char="." align="char">0.799</td></tr><tr><td>F23</td><td>LSSTE</td><td char="." align="char">0.688</td><td char="." align="char">0.631</td><td char="." align="char">0.664</td><td char="." align="char">0.598</td></tr></tbody></table></table-wrap><table-wrap id="Tab5"><label>Table 5</label><caption><p>The performances of individual feature-based models on imbalanced <italic>Human</italic> dataset</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Index</th><th>Feature</th><th>AUC</th><th>ACC</th><th>SN</th><th>SP</th></tr></thead><tbody><tr><td>F1</td><td>1-Spectrum Profile</td><td char="." align="char">0.748</td><td char="." align="char">0.739</td><td char="." align="char">0.398</td><td char="." align="char">0.854</td></tr><tr><td>F2</td><td>2-Spectrum Profile</td><td char="." align="char">0.841</td><td char="." align="char">0.808</td><td char="." align="char">0.416</td><td char="." align="char">0.940</td></tr><tr><td>F3</td><td>3-Spectrum Profile</td><td char="." align="char">0.850</td><td char="." align="char">0.814</td><td char="." align="char">0.321</td><td char="." align="char">0.982</td></tr><tr><td>F4</td><td>4-Spectrum Profile</td><td char="." align="char">0.844</td><td char="." align="char">0.811</td><td char="." align="char">0.284</td><td char="." align="char">0.989</td></tr><tr><td>F5</td><td>5-Spectrum Profile</td><td char="." align="char">0.836</td><td char="." align="char">0.813</td><td char="." align="char">0.305</td><td char="." align="char">0.986</td></tr><tr><td>F6</td><td>(3,1)-Mismatch Profile</td><td char="." align="char">0.867</td><td char="." align="char">0.824</td><td char="." align="char">0.427</td><td char="." align="char">0.959</td></tr><tr><td>F7</td><td>(4,1)-Mismatch Profile</td><td char="." align="char">0.856</td><td char="." align="char">0.814</td><td char="." align="char">0.328</td><td char="." align="char">0.979</td></tr><tr><td>F8</td><td>(5,1)-Mismatch Profile</td><td char="." align="char">0.851</td><td char="." align="char">0.810</td><td char="." align="char">0.277</td><td char="." align="char">0.991</td></tr><tr><td>F9</td><td>(3,1)-Subsequence Profile</td><td char="." align="char">0.850</td><td char="." align="char">0.808</td><td char="." align="char">0.443</td><td char="." align="char">0.932</td></tr><tr><td>F10</td><td>(4,1)-Subsequence Profile</td><td char="." align="char">0.864</td><td char="." align="char">0.822</td><td char="." align="char">0.473</td><td char="." align="char">0.940</td></tr><tr><td>F11</td><td>(5,1)-Subsequence Profile</td><td char="." align="char">0.871</td><td char="." align="char">0.829</td><td char="." align="char">0.492</td><td char="." align="char">0.944</td></tr><tr><td>F12</td><td>1-RevcKmer</td><td char="." align="char">0.745</td><td char="." align="char">0.746</td><td char="." align="char">0.005</td><td char="." align="char">0.997</td></tr><tr><td>F13</td><td>2-RevcKmer</td><td char="." align="char">0.803</td><td char="." align="char">0.778</td><td char="." align="char">0.411</td><td char="." align="char">0.902</td></tr><tr><td>F14</td><td>3-RevcKmer</td><td char="." align="char">0.823</td><td char="." align="char">0.800</td><td char="." align="char">0.265</td><td char="." align="char">0.981</td></tr><tr><td>F15</td><td>4-RevcKmer</td><td char="." align="char">0.823</td><td char="." align="char">0.803</td><td char="." align="char">0.241</td><td char="." align="char">0.993</td></tr><tr><td>F16</td><td>5-RevcKmer</td><td char="." align="char">0.818</td><td char="." align="char">0.806</td><td char="." align="char">0.255</td><td char="." align="char">0.992</td></tr><tr><td>F17</td><td>PCPseDNC</td><td char="." align="char">0.841</td><td char="." align="char">0.806</td><td char="." align="char">0.374</td><td char="." align="char">0.952</td></tr><tr><td>F18</td><td>PCPseTNC</td><td char="." align="char">0.857</td><td char="." align="char">0.813</td><td char="." align="char">0.337</td><td char="." align="char">0.975</td></tr><tr><td>F19</td><td>SCPseDNC</td><td char="." align="char">0.836</td><td char="." align="char">0.803</td><td char="." align="char">0.346</td><td char="." align="char">0.958</td></tr><tr><td>F20</td><td>SCPseTNC</td><td char="." align="char">0.842</td><td char="." align="char">0.808</td><td char="." align="char">0.312</td><td char="." align="char">0.977</td></tr><tr><td>F21</td><td>Sparse Profile</td><td char="." align="char">0.905</td><td char="." align="char">0.856</td><td char="." align="char">0.634</td><td char="." align="char">0.932</td></tr><tr><td>F22</td><td>PSSM</td><td char="." align="char">0.882</td><td char="." align="char">0.832</td><td char="." align="char">0.584</td><td char="." align="char">0.916</td></tr><tr><td>F23</td><td>LSSTE</td><td char="." align="char">0.688</td><td char="." align="char">0.766</td><td char="." align="char">0.175</td><td char="." align="char">0.966</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec10"><title>Performances of GA-based weighted ensemble method</title><p>The GA-based weighted ensemble (GA-WE) method integrates sequence-derived features and constructs high-accuracy prediction models. We evaluate the performances of the GA-WE model on the datasets of three species. Moreover, we carry out the cross-species prediction, in which we build prediction models on <italic>Mouse</italic> species, and make prediction for other species.</p><sec id="Sec11"><title>Results of GA-WE models on three species</title><p>As show in Table&#x000a0;<xref rid="Tab6" ref-type="table">6</xref>, the GA-WE models achieve AUC of 0.932, accuracy of 0.839, sensitivity of 0.858 and specificity of 0.820 on the balanced <italic>Human</italic> dataset. Compared with the best individual features-based model (the sparse profile-based model), the GA-WE model improves AUC of &#x0003e;3%, indicating the GA-WE model can effectively combine various features to enhance performances. The proposed method also performs accurate prediction on balanced <italic>Mouse</italic> dataset, achieving AUC of 0.937. Compared with the piRNA prediction on mammalian: <italic>Human</italic> and <italic>Mouse</italic>, the prediction on <italic>Drosophila</italic> is much better, achieving AUC of 0.995. Similarly, the GA-WE model performs high-accuracy prediction on the imbalanced datasets of the three species, achieves AUC of 0.935, 0.939 and 0.996, respectively, which demonstrates that the GA-WE model has not only high accuracy but also good robustness.<table-wrap id="Tab6"><label>Table 6</label><caption><p>The performances of the GA-WE model on three species (<italic>Human</italic>, <italic>Mouse</italic> and <italic>Drosophila</italic>)</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Dataset</th><th>Species</th><th>AUC</th><th>ACC</th><th>SN</th><th>SP</th></tr></thead><tbody><tr><td rowspan="3">Balanced</td><td>
<italic>Human</italic>
</td><td char="." align="char">0.932</td><td char="." align="char">0.839</td><td char="." align="char">0.858</td><td char="." align="char">0.820</td></tr><tr><td>
<italic>Mouse</italic>
</td><td char="." align="char">0.937</td><td char="." align="char">0.838</td><td char="." align="char">0.824</td><td char="." align="char">0.852</td></tr><tr><td>
<italic>Drosophila</italic>
</td><td char="." align="char">0.995</td><td char="." align="char">0.959</td><td char="." align="char">0.951</td><td char="." align="char">0.966</td></tr><tr><td rowspan="3">Imbalanced</td><td>
<italic>Human</italic>
</td><td char="." align="char">0.935</td><td char="." align="char">0.869</td><td char="." align="char">0.687</td><td char="." align="char">0.931</td></tr><tr><td>
<italic>Mouse</italic>
</td><td char="." align="char">0.939</td><td char="." align="char">0.889</td><td char="." align="char">0.745</td><td char="." align="char">0.939</td></tr><tr><td>
<italic>Drosophila</italic>
</td><td char="." align="char">0.996</td><td char="." align="char">0.958</td><td char="." align="char">0.897</td><td char="." align="char">0.983</td></tr></tbody></table></table-wrap></p><p>Further, we investigate the optimal weights for the GA-WE model in each fold of 10-CV. Taking <italic>Human</italic> dataset as an example, the optimal weights of &#x0201c;F1&#x02009;~&#x02009;F22&#x0201d; for the GA-WE model are visualized by the heat map (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). We can draw several conclusions from the results. Firstly, different features have different weights in each fold of 10-CV, and the optimal weights can lead to the best ensemble model. Secondly, optimal weights reflect the contributions of the corresponding features for the ensemble model, and the feature having the best performances for piRNA prediction always makes the greatest contribution to the ensemble model. For example, the sparse profile (F21) performs the highest contribution to the ensemble model in each fold of 10-CV, for the sparse profile has the best predictive ability among all features. Thirdly, the optimal weights for the ensemble model depend on the training set, and determining the optimal weights is necessary for building high-accuracy models.<fig id="Fig4"><label>Fig. 4</label><caption><p>Optimal weights for the GA-WE model in each fold of 10-CV</p></caption><graphic xlink:href="12859_2016_1206_Fig4_HTML" id="MO4"/></fig></p></sec><sec id="Sec12"><title>Results of cross-species prediction</title><p>Considering that <italic>Mouse</italic> instances are more than <italic>Human</italic> instances and <italic>Drosophila</italic> instances, we construct the GA-WE model on <italic>Mouse</italic> dataset, and make predictions for <italic>Human</italic> dataset and <italic>Drosophila</italic> dataset.</p><p>As shown in Table&#x000a0;<xref rid="Tab7" ref-type="table">7</xref>, the GA-WE model trained with <italic>Mouse</italic> dataset achieves AUC of 0.863 and 0.687 on the balanced <italic>Human</italic> and <italic>Drosophila</italic> datasets, and achieves AUC of 0.868 and 0.746 on the imbalanced datasets of the two species. Compared with the experiments on a same species, the cross-species experiments produce lower scores, indicating that piRNAs derived from different species may have different patterns. Moreover, the results on <italic>Human</italic> dataset are better than the results on <italic>Drosophila</italic> dataset, and the possible reason is that the length distribution of <italic>Mouse</italic> piRNAs is similar to that of <italic>Human</italic> piRNAs, and is different from that of <italic>Drosophila</italic> piRNAs (shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). Therefore, we&#x02019;d better train models and make predictions based on a same species.<table-wrap id="Tab7"><label>Table 7</label><caption><p>The performances of cross-species prediction</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Dataset</th><th>Species</th><th>AUC</th><th>ACC</th><th>SN</th><th>SP</th></tr></thead><tbody><tr><td rowspan="2">Balanced</td><td>
<italic>Human</italic>
</td><td char="." align="char">0.863</td><td char="." align="char">0.788</td><td char="." align="char">0.796</td><td char="." align="char">0.781</td></tr><tr><td>
<italic>Drosophila</italic>
</td><td char="." align="char">0.687</td><td char="." align="char">0.668</td><td char="." align="char">0.639</td><td char="." align="char">0.698</td></tr><tr><td rowspan="2">Imbalanced</td><td>
<italic>Human</italic>
</td><td char="." align="char">0.868</td><td char="." align="char">0.811</td><td char="." align="char">0.425</td><td char="." align="char">0.942</td></tr><tr><td>
<italic>Drosophila</italic>
</td><td char="." align="char">0.746</td><td char="." align="char">0.774</td><td char="." align="char">0.370</td><td char="." align="char">0.936</td></tr></tbody></table></table-wrap></p></sec></sec><sec id="Sec13"><title>Comparison with other state-of-the-art methods</title><p>Here, three latest methods: piRNApredictor [<xref ref-type="bibr" rid="CR20">20</xref>], Piano [<xref ref-type="bibr" rid="CR21">21</xref>] and our previous work [<xref ref-type="bibr" rid="CR22">22</xref>] are adopted as the benchmark methods, for they build prediction models based on machine learning methods. piRNApredictor used <italic>k</italic>-mer feature (i.e, spectrum profile), <italic>k</italic>&#x02009;=&#x02009;1,&#x02009;2,&#x02009;3,&#x02009;4,&#x02009;5, and Piano used the LSSTE feature. piRNApredictor and Piano adopted the support vector machine (SVM) to construct prediction models. Our previous work adopted the exhaustive search strategy to combine five sequence-derived features to predict piRNAs. We implement piRNApredictor obtain the results. Since the source codes of Piano are available at <ext-link ext-link-type="uri" xlink:href="http://ento.njau.edu.cn/Piano.html">http://ento.njau.edu.cn/Piano.html</ext-link>, we can run the program on the benchmark datasets. The proposed methods and three benchmark methods are evaluated on six benchmark datasets by using 10-CV.</p><p>As shown in Table&#x000a0;<xref rid="Tab8" ref-type="table">8</xref>, our previous work, piRNApredictor and Piano achieve AUC of 0.920, 0.894 and 0.592 on the balanced <italic>Human</italic> dataset, respectively. Our GA-WE model produces AUC of 0.932 on the dataset. The proposed method also yields much better performances than piRNApredictor and Piano on the balanced <italic>Mouse</italic> dataset and balanced <italic>Drosophila</italic> dataset. There are several reasons for the superior performances of our method. Firstly, various useful features can guarantee the diversity for the GA-WE model. Secondly, the GA-WE model automatically determines the optimal weights on validation set.<table-wrap id="Tab8"><label>Table 8</label><caption><p>Performances of GA-WE and the state-of-the-art methods on three species</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Dataset</th><th>Species</th><th>Method</th><th>AUC</th><th>ACC</th><th>SN</th><th>SP</th></tr></thead><tbody><tr><td rowspan="12">Balanced</td><td rowspan="4">
<italic>Human</italic>
</td><td>Piano</td><td char="." align="char">0.592</td><td char="." align="char">0.560</td><td char="." align="char">0.855</td><td char="." align="char">0.265</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.894</td><td char="." align="char">0.812</td><td char="." align="char">0.859</td><td char="." align="char">0.764</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.920</td><td char="." align="char">0.807</td><td char="." align="char">0.815</td><td char="." align="char">0.800</td></tr><tr><td>GA-WE</td><td char="." align="char">0.932</td><td char="." align="char">0.839</td><td char="." align="char">0.858</td><td char="." align="char">0.820</td></tr><tr><td rowspan="4">
<italic>Mouse</italic>
</td><td>Piano</td><td char="." align="char">0.445</td><td char="." align="char">0.5365</td><td char="." align="char">0.837</td><td char="." align="char">0.236</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.892</td><td char="." align="char">0.819</td><td char="." align="char">0.862</td><td char="." align="char">0.776</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.924</td><td char="." align="char">0.810</td><td char="." align="char">0.863</td><td char="." align="char">0.756</td></tr><tr><td>GA-WE</td><td char="." align="char">0.937</td><td char="." align="char">0.838</td><td char="." align="char">0.826</td><td char="." align="char">0.850</td></tr><tr><td rowspan="4">
<italic>Drosophila</italic>
</td><td>Piano</td><td char="." align="char">0.741</td><td char="." align="char">0.692</td><td char="." align="char">0.836</td><td char="." align="char">0.547</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.983</td><td char="." align="char">0.952</td><td char="." align="char">0.927</td><td char="." align="char">0.977</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.994</td><td char="." align="char">0.958</td><td char="." align="char">0.952</td><td char="." align="char">0.965</td></tr><tr><td>GA-WE</td><td char="." align="char">0.995</td><td char="." align="char">0.959</td><td char="." align="char">0.949</td><td char="." align="char">0.966</td></tr><tr><td rowspan="12">Imbalanced</td><td rowspan="4">
<italic>Human</italic>
</td><td>Piano</td><td char="." align="char">0.449</td><td char="." align="char">0.747</td><td char="." align="char">0.000</td><td char="." align="char">1.000</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.905</td><td char="." align="char">0.847</td><td char="." align="char">0.548</td><td char="." align="char">0.949</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.922</td><td char="." align="char">0.836</td><td char="." align="char">0.589</td><td char="." align="char">0.919</td></tr><tr><td>GA-WE</td><td char="." align="char">0.935</td><td char="." align="char">0.869</td><td char="." align="char">0.687</td><td char="." align="char">0.931</td></tr><tr><td rowspan="4">
<italic>Mouse</italic>
</td><td>Piano</td><td char="." align="char">0.441</td><td char="." align="char">0.744</td><td char="." align="char">0.000</td><td char="." align="char">1.000</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.892</td><td char="." align="char">0.848</td><td char="." align="char">0.568</td><td char="." align="char">0.944</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.928</td><td char="." align="char">0.849</td><td char="." align="char">0.586</td><td char="." align="char">0.940</td></tr><tr><td>GA-WE</td><td char="." align="char">0.939</td><td char="." align="char">0.889</td><td char="." align="char">0.745</td><td char="." align="char">0.939</td></tr><tr><td rowspan="4">
<italic>Drosophila</italic>
</td><td>Piano</td><td char="." align="char">0.804</td><td char="." align="char">0.712</td><td char="." align="char">0.000</td><td char="." align="char">1.000</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.982</td><td char="." align="char">0.961</td><td char="." align="char">0.902</td><td char="." align="char">0.985</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.995</td><td char="." align="char">0.965</td><td char="." align="char">0.920</td><td char="." align="char">0.984</td></tr><tr><td>GA-WE</td><td char="." align="char">0.996</td><td char="." align="char">0.964</td><td char="." align="char">0.940</td><td char="." align="char">0.973</td></tr></tbody></table></table-wrap></p><p>Further, we compare the capabilities of the GA-WE method with the state-of-the-art methods in the cross-species prediction. All models are constructed on <italic>Mouse</italic> dataset, and make predictions for <italic>Human</italic> and <italic>Drosophila</italic> dataset. As shown in Table&#x000a0;<xref rid="Tab9" ref-type="table">9</xref>, our GA-WE model trained with <italic>Mouse</italic> dataset performs better than the state-of-the-art methods on the <italic>Human</italic> datasets, but performs worse than piRNApredictor on the <italic>Drosophila</italic> dataset. Moreover, the performances on <italic>Human</italic> dataset are always better than that on <italic>Drosophila</italic> dataset regardless of any method, and the possible reason is that the length distribution of <italic>Mouse</italic> piRNAs is similar to that of <italic>Human</italic> piRNAs, and is different from that of <italic>Drosophila</italic> piRNAs (shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). In general, our method can produce satisfying results in the cross-species prediction.<table-wrap id="Tab9"><label>Table 9</label><caption><p>Performances of GA-WE and the state-of-the-art methods in the cross-species prediction</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Dataset</th><th>Species</th><th>Method</th><th>AUC</th><th>ACC</th><th>SN</th><th>SP</th></tr></thead><tbody><tr><td rowspan="8">Balanced</td><td rowspan="4">
<italic>Human</italic>
</td><td>Piano</td><td char="." align="char">0.431</td><td char="." align="char">0.558</td><td char="." align="char">0.878</td><td char="." align="char">0.238</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.850</td><td char="." align="char">0.783</td><td char="." align="char">0.781</td><td char="." align="char">0.784</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.845</td><td char="." align="char">0.774</td><td char="." align="char">0.764</td><td char="." align="char">0.784</td></tr><tr><td>GA-WE</td><td char="." align="char">0.863</td><td char="." align="char">0.788</td><td char="." align="char">0.796</td><td char="." align="char">0.781</td></tr><tr><td rowspan="4">
<italic>Drosophila</italic>
</td><td>Piano</td><td char="." align="char">0.367</td><td char="." align="char">0.587</td><td char="." align="char">0.905</td><td char="." align="char">0.270</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.728</td><td char="." align="char">0.650</td><td char="." align="char">0.630</td><td char="." align="char">0.669</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.682</td><td char="." align="char">0.628</td><td char="." align="char">0.512</td><td char="." align="char">0.745</td></tr><tr><td>GA-WE</td><td char="." align="char">0.687</td><td char="." align="char">0.668</td><td char="." align="char">0.639</td><td char="." align="char">0.698</td></tr><tr><td rowspan="8">Imbalanced</td><td rowspan="4">
<italic>Human</italic>
</td><td>Piano</td><td char="." align="char">0.426</td><td char="." align="char">0.747</td><td char="." align="char">0.000</td><td char="." align="char">1.000</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.856</td><td char="." align="char">0.823</td><td char="." align="char">0.507</td><td char="." align="char">0.931</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.856</td><td char="." align="char">0.783</td><td char="." align="char">0.300</td><td char="." align="char">0.946</td></tr><tr><td>GA-WE</td><td char="." align="char">0.868</td><td char="." align="char">0.811</td><td char="." align="char">0.425</td><td char="." align="char">0.942</td></tr><tr><td rowspan="4">
<italic>Drosophila</italic>
</td><td>Piano</td><td char="." align="char">0.369</td><td char="." align="char">0.713</td><td char="." align="char">0.000</td><td char="." align="char">1.000</td></tr><tr><td>piRNApredictor</td><td char="." align="char">0.783</td><td char="." align="char">0.773</td><td char="." align="char">0.422</td><td char="." align="char">0.915</td></tr><tr><td>Ensemble Learning</td><td char="." align="char">0.750</td><td char="." align="char">0.736</td><td char="." align="char">0.275</td><td char="." align="char">0.921</td></tr><tr><td>GA-WE</td><td char="." align="char">0.746</td><td char="." align="char">0.774</td><td char="." align="char">0.370</td><td char="." align="char">0.936</td></tr></tbody></table></table-wrap></p></sec></sec><sec id="Sec14" sec-type="conclusion"><title>Conclusions</title><p>In this paper, we develop the GA-based weighted ensemble method, which can automatically determine the importance of different information resources and produce high-accuracy performances. We compile the <italic>Human</italic>, <italic>Mouse</italic> and <italic>Drosophila</italic> datasets from NONCODE version 3.0, UCSC Genome Browser and NCBI Gene Expression Omnibus. In the computational experiments, the GA-based weighted ensemble method achieves AUC of &#x0003e;93% by 10-CV. Compared with other state-of-the-art methods, our method produces better performances as well as good robustness. In conclusion, the proposed method is promising for transposon-derived piRNA prediction. The source codes and datasets are available in <ext-link ext-link-type="uri" xlink:href="https://github.com/zw9977129/piRNAPredictor">https://github.com/zw9977129/piRNAPredictor</ext-link>.</p></sec></body><back><app-group><app id="App1"><sec id="Sec15"><title>Additional file</title><p><media position="anchor" xlink:href="12859_2016_1206_MOESM1_ESM.xlsx" id="MOESM1"><label>Additional file 1: Table S1.</label><caption><p>The performances of RF models and SVM models on the balanced Human dataset. (XLSX 13 kb)</p></caption></media><media position="anchor" xlink:href="12859_2016_1206_MOESM2_ESM.xlsx" id="MOESM2"><label>Additional file 2: Table S2.</label><caption><p>The performances of RF models and SVM models on the imbalanced Human dataset. (XLSX 13 kb)</p></caption></media></p></sec></app></app-group><glossary><title>Abbreviations</title><def-list><def-item><term>&#x0201c;F1~F22&#x0201d;</term><def><p>The features indexed from F1 to F22</p></def></def-item><def-item><term>10-CV</term><def><p>10-fold cross validation</p></def></def-item><def-item><term>ACC</term><def><p>Accuracy</p></def></def-item><def-item><term>AUC</term><def><p>Area under ROC curve</p></def></def-item><def-item><term>GA</term><def><p>Genetic algorithm</p></def></def-item><def-item><term>GA-WE</term><def><p>Genetic algorithm-based weighted ensemble</p></def></def-item><def-item><term>LSSTE</term><def><p>Local structure-sequence triplet elements</p></def></def-item><def-item><term>PCPseDNC</term><def><p>Parallel correlation pseudo dinucleotide composition</p></def></def-item><def-item><term>PCPseTNC</term><def><p>Parallel correlation pseudo trinucleotide composition</p></def></def-item><def-item><term>PSSM</term><def><p>Position-specific scoring matrix</p></def></def-item><def-item><term>RF</term><def><p>Random forest</p></def></def-item><def-item><term>SCPseDNC</term><def><p>Series correlation pseudo dinucleotide composition</p></def></def-item><def-item><term>SCPseTNC</term><def><p>Series correlation pseudo trinucleotide composition</p></def></def-item><def-item><term>SN</term><def><p>Sensitivity</p></def></def-item><def-item><term>SP</term><def><p>Specificity</p></def></def-item><def-item><term>SVM</term><def><p>Support vector machine</p></def></def-item></def-list></glossary><ack><title>Acknowledgements</title><p>The authors thank Dr. Fei Li and Dr. Kai Wang for the codes of Piano.</p><sec id="FPar1"><title>Funding</title><p>The National Natural Science Foundation of China (61103126, 61271337, 61402340 and 61572368), Shenzhen Development Foundation (JCYJ20130401160028781) and Natural Science Foundation of Hubei Province of China (2014CFB194) support this work. These funding has no role in the design of the study and collection, analysis, and interpretation of data and in writing the manuscript.</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p>The datasets and source codes are available in <ext-link ext-link-type="uri" xlink:href="https://github.com/zw9977129/piRNAPredictor">https://github.com/zw9977129/piRNAPredictor</ext-link>.</p></sec><sec id="FPar3"><title>Authors&#x02019; contribution</title><p>WZ, DL and LL designed the study. LL implemented the algorithm. LL and WZ drafted the manuscript. FL (Fei) and FL (Feng) helped to prepare the data and draft the manuscript. All authors have read and approved the final version of the manuscript.</p></sec><sec id="FPar4"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="FPar5"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="FPar6"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jean-Michel</surname><given-names>C</given-names></name></person-group><article-title>Fewer genes, more noncoding RNA</article-title><source>Science</source><year>2005</year><volume>309</volume><issue>5740</issue><fpage>1529</fpage><lpage>30</lpage><pub-id pub-id-type="doi">10.1126/science.1116800</pub-id><pub-id pub-id-type="pmid">16141064</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mattick</surname><given-names>JS</given-names></name></person-group><article-title>The functional genomics of noncoding RNA</article-title><source>Science</source><year>2005</year><volume>309</volume><issue>5740</issue><fpage>1527</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1126/science.1117806</pub-id><?supplied-pmid 16141063?><pub-id pub-id-type="pmid">16141063</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chaoyong</surname><given-names>X</given-names></name><name><surname>Jiao</surname><given-names>Y</given-names></name><name><surname>Hui</surname><given-names>L</given-names></name><name><surname>Ming</surname><given-names>L</given-names></name><name><surname>Guoguang</surname><given-names>Z</given-names></name><name><surname>Dechao</surname><given-names>B</given-names></name><name><surname>Weimin</surname><given-names>Z</given-names></name><name><surname>Wei</surname><given-names>W</given-names></name><name><surname>Runsheng</surname><given-names>C</given-names></name><name><surname>Yi</surname><given-names>Z</given-names></name></person-group><article-title>NONCODEv4: exploring the world of long non-coding RNA genes</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><issue>D1</issue><fpage>D98</fpage><lpage>103</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt1222</pub-id><pub-id pub-id-type="pmid">24285305</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>Y</given-names></name><name><surname>Liu</surname><given-names>N</given-names></name><name><surname>Wang</surname><given-names>JP</given-names></name><name><surname>Wang</surname><given-names>YQ</given-names></name><name><surname>Yu</surname><given-names>XL</given-names></name><name><surname>Wang</surname><given-names>ZB</given-names></name><name><surname>Cheng</surname><given-names>XC</given-names></name><name><surname>Zou</surname><given-names>Q</given-names></name></person-group><article-title>Regulatory long non-coding RNA and its functions</article-title><source>J Physiol Biochem.</source><year>2012</year><volume>68</volume><issue>4</issue><fpage>611</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1007/s13105-012-0166-y</pub-id><?supplied-pmid 22535282?><pub-id pub-id-type="pmid">22535282</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Meenakshisundaram</surname><given-names>K</given-names></name><name><surname>Carmen</surname><given-names>L</given-names></name><name><surname>Michela</surname><given-names>B</given-names></name><name><surname>Diego</surname><given-names>DB</given-names></name><name><surname>Gabriella</surname><given-names>M</given-names></name><name><surname>Rosaria</surname><given-names>V</given-names></name></person-group><article-title>Existence of snoRNA, microRNA, piRNA characteristics in a novel non-coding RNA: x-ncRNA and its biological implication in Homo sapiens</article-title><source>J Bioinformatics Seq Anal</source><year>2009</year><volume>1</volume><issue>2</issue><fpage>31</fpage><lpage>40</lpage></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alexei</surname><given-names>A</given-names></name><name><surname>Dimos</surname><given-names>G</given-names></name><name><surname>S&#x000e9;bastien</surname><given-names>P</given-names></name><name><surname>Mariana</surname><given-names>LQ</given-names></name><name><surname>Pablo</surname><given-names>L</given-names></name><name><surname>Nicola</surname><given-names>I</given-names></name><name><surname>Patricia</surname><given-names>M</given-names></name><name><surname>Brownstein</surname><given-names>MJ</given-names></name><name><surname>Satomi</surname><given-names>KM</given-names></name><name><surname>Toru</surname><given-names>N</given-names></name></person-group><article-title>A novel class of small RNAs bind to MILI protein in mouse testes</article-title><source>Nature</source><year>2006</year><volume>442</volume><issue>7099</issue><fpage>203</fpage><lpage>7</lpage><pub-id pub-id-type="pmid">16751777</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lau</surname><given-names>NC</given-names></name><name><surname>Seto</surname><given-names>AG</given-names></name><name><surname>Jinkuk</surname><given-names>K</given-names></name><name><surname>Satomi</surname><given-names>KM</given-names></name><name><surname>Toru</surname><given-names>N</given-names></name><name><surname>Bartel</surname><given-names>DP</given-names></name><name><surname>Kingston</surname><given-names>RE</given-names></name></person-group><article-title>Characterization of the piRNA Complex from rat testes</article-title><source>Science</source><year>2006</year><volume>313</volume><issue>5785</issue><fpage>363</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1126/science.1130164</pub-id><?supplied-pmid 16778019?><pub-id pub-id-type="pmid">16778019</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grivna</surname><given-names>ST</given-names></name><name><surname>Ergin</surname><given-names>B</given-names></name><name><surname>Zhong</surname><given-names>W</given-names></name><name><surname>Haifan</surname><given-names>L</given-names></name></person-group><article-title>A novel class of small RNAs in mouse spermatogenic cells</article-title><source>Genes Dev</source><year>2006</year><volume>20</volume><issue>13</issue><fpage>1709</fpage><lpage>14</lpage><pub-id pub-id-type="doi">10.1101/gad.1434406</pub-id><?supplied-pmid 16766680?><pub-id pub-id-type="pmid">16766680</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Seto</surname><given-names>AG</given-names></name><name><surname>Kingston</surname><given-names>RE</given-names></name><name><surname>Lau</surname><given-names>NC</given-names></name></person-group><article-title>The coming of age for Piwi proteins</article-title><source>Mol Cell</source><year>2007</year><volume>26</volume><issue>5</issue><fpage>603</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2007.05.021</pub-id><?supplied-pmid 17560367?><pub-id pub-id-type="pmid">17560367</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ruby</surname><given-names>JG</given-names></name><name><surname>Jan</surname><given-names>C</given-names></name><name><surname>Player</surname><given-names>C</given-names></name><name><surname>Axtell</surname><given-names>MJ</given-names></name><name><surname>Lee</surname><given-names>W</given-names></name><name><surname>Nusbaum</surname><given-names>C</given-names></name><name><surname>Ge</surname><given-names>H</given-names></name><name><surname>Bartel</surname><given-names>DP</given-names></name></person-group><article-title>Large-scale sequencing reveals 21U-RNAs and additional Micro-RNAs and endogenous siRNAs in C. elegans</article-title><source>Cell</source><year>2007</year><volume>127</volume><issue>6</issue><fpage>1193</fpage><lpage>207</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2006.10.040</pub-id><pub-id pub-id-type="pmid">17174894</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cox</surname><given-names>DN</given-names></name><name><surname>Chao</surname><given-names>A</given-names></name><name><surname>Baker</surname><given-names>J</given-names></name><name><surname>Chang</surname><given-names>L</given-names></name><name><surname>Qiao</surname><given-names>D</given-names></name><name><surname>Lin</surname><given-names>H</given-names></name></person-group><article-title>A novel class of evolutionarily conserved genes defined by piwi are essential for stem cell self-renewal</article-title><source>Genes Dev</source><year>1998</year><volume>12</volume><issue>23</issue><fpage>3715</fpage><lpage>27</lpage><pub-id pub-id-type="doi">10.1101/gad.12.23.3715</pub-id><?supplied-pmid 9851978?><pub-id pub-id-type="pmid">9851978</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Klattenhoff</surname><given-names>C</given-names></name><name><surname>Theurkauf</surname><given-names>W</given-names></name></person-group><article-title>Biogenesis and germline functions of piRNAs</article-title><source>Development</source><year>2008</year><volume>135</volume><issue>1</issue><fpage>3</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1242/dev.006486</pub-id><?supplied-pmid 18032451?><pub-id pub-id-type="pmid">18032451</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brennecke</surname><given-names>BJ</given-names></name><name><surname>Aravin</surname><given-names>A</given-names></name><name><surname>Stark</surname><given-names>A</given-names></name><name><surname>Dus</surname><given-names>M</given-names></name><name><surname>Kellis</surname><given-names>M</given-names></name><name><surname>Sachidanandam</surname><given-names>R</given-names></name><name><surname>Hannon</surname><given-names>G</given-names></name></person-group><article-title>Discrete small RNA-Generating Loci as master regulators of transposon activity in drosophila</article-title><source>Cell</source><year>2007</year><volume>128</volume><issue>6</issue><fpage>1089</fpage><lpage>103</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2007.01.043</pub-id><?supplied-pmid 17346786?><pub-id pub-id-type="pmid">17346786</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thomson</surname><given-names>T</given-names></name><name><surname>Lin</surname><given-names>H</given-names></name></person-group><article-title>The biogenesis and function of PIWI proteins and piRNAs: progress and prospect</article-title><source>Annu Rev Cell Dev Biol</source><year>2009</year><volume>25</volume><issue>1</issue><fpage>355</fpage><lpage>76</lpage><pub-id pub-id-type="doi">10.1146/annurev.cellbio.24.110707.175327</pub-id><?supplied-pmid 19575643?><pub-id pub-id-type="pmid">19575643</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Houwing</surname><given-names>S</given-names></name><name><surname>Kamminga</surname><given-names>LM</given-names></name><name><surname>Berezikov</surname><given-names>E</given-names></name><name><surname>Cronembold</surname><given-names>D</given-names></name><name><surname>Girard</surname><given-names>A</given-names></name><name><surname>Elst</surname><given-names>HVD</given-names></name><name><surname>Filippov</surname><given-names>DV</given-names></name><name><surname>Blaser</surname><given-names>H</given-names></name><name><surname>Raz</surname><given-names>E</given-names></name><name><surname>Moens</surname><given-names>CB</given-names></name></person-group><article-title>A role for Piwi and piRNAs in germ cell maintenance and transposon silencing in Zebrafish</article-title><source>Cell</source><year>2007</year><volume>129</volume><issue>1</issue><fpage>69</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2007.03.026</pub-id><?supplied-pmid 17418787?><pub-id pub-id-type="pmid">17418787</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Das</surname><given-names>PP</given-names></name><name><surname>Bagijn</surname><given-names>MP</given-names></name><name><surname>Goldstein</surname><given-names>LD</given-names></name><name><surname>Woolford</surname><given-names>JR</given-names></name><name><surname>Lehrbach</surname><given-names>NJ</given-names></name><name><surname>Sapetschnig</surname><given-names>A</given-names></name><name><surname>Buhecha</surname><given-names>HR</given-names></name><name><surname>Gilchrist</surname><given-names>MJ</given-names></name><name><surname>Howe</surname><given-names>KL</given-names></name><name><surname>Stark</surname><given-names>R</given-names></name></person-group><article-title>Piwi and piRNAs act upstream of an endogenous siRNA pathway to suppress Tc3 transposon mobility in the caenorhabditis elegans germline</article-title><source>Mol Cell</source><year>2008</year><volume>31</volume><issue>1</issue><fpage>79</fpage><lpage>90</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2008.06.003</pub-id><?supplied-pmid 18571451?><pub-id pub-id-type="pmid">18571451</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nicolas</surname><given-names>R</given-names></name><name><surname>Lau</surname><given-names>NC</given-names></name><name><surname>Sudha</surname><given-names>B</given-names></name><name><surname>Zhigang</surname><given-names>J</given-names></name><name><surname>Katsutomo</surname><given-names>O</given-names></name><name><surname>Satomi</surname><given-names>KM</given-names></name><name><surname>Blower</surname><given-names>MD</given-names></name><name><surname>Lai</surname><given-names>EC</given-names></name></person-group><article-title>A broadly conserved pathway generates 3&#x02032;UTR-directed primary piRNAs</article-title><source>Curr Biol</source><year>2009</year><volume>19</volume><issue>24</issue><fpage>2066</fpage><lpage>76</lpage><pub-id pub-id-type="doi">10.1016/j.cub.2009.11.064</pub-id><pub-id pub-id-type="pmid">20022248</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hang</surname><given-names>Y</given-names></name><name><surname>Haifan</surname><given-names>L</given-names></name></person-group><article-title>An epigenetic activation role of Piwi and a Piwi-associated piRNA in Drosophila melanogaster</article-title><source>Nature</source><year>2007</year><volume>450</volume><issue>7167</issue><fpage>304</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1038/nature06263</pub-id><pub-id pub-id-type="pmid">17952056</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Betel</surname><given-names>D</given-names></name><name><surname>Sheridan</surname><given-names>R</given-names></name><name><surname>Marks</surname><given-names>DS</given-names></name><name><surname>Sander</surname><given-names>C</given-names></name></person-group><article-title>Computational analysis of mouse piRNA sequence and biogenesis</article-title><source>Plos Computational Biology</source><year>2007</year><volume>3</volume><issue>11</issue><fpage>e222</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.0030222</pub-id><?supplied-pmid 17997596?><pub-id pub-id-type="pmid">17997596</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>X</given-names></name><name><surname>Kang</surname><given-names>L</given-names></name></person-group><article-title>A k-mer scheme to predict piRNAs and characterize locust piRNAs</article-title><source>Bioinformatics</source><year>2011</year><volume>27</volume><issue>6</issue><fpage>771</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btr016</pub-id><?supplied-pmid 21224287?><pub-id pub-id-type="pmid">21224287</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>K</given-names></name><name><surname>Liang</surname><given-names>C</given-names></name><name><surname>Liu</surname><given-names>J</given-names></name><name><surname>Xiao</surname><given-names>H</given-names></name><name><surname>Huang</surname><given-names>S</given-names></name><name><surname>Xu</surname><given-names>J</given-names></name><name><surname>Li</surname><given-names>F</given-names></name></person-group><article-title>Prediction of piRNAs using transposon interaction and a support vector machine</article-title><source>BMC Bioinformatics</source><year>2014</year><volume>15</volume><issue>1</issue><fpage>1</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1186/1471-2105-15-S12-S1</pub-id><pub-id pub-id-type="pmid">24383880</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Luo</surname><given-names>L</given-names></name><name><surname>Li</surname><given-names>D</given-names></name><name><surname>Zhang</surname><given-names>W</given-names></name><name><surname>Tu</surname><given-names>S</given-names></name><name><surname>Zhu</surname><given-names>X</given-names></name><name><surname>Tian</surname><given-names>G</given-names></name></person-group><article-title>Accurate prediction of transposon-derived piRNAs by integrating various sequential and physicochemical features</article-title><source>PLoS One</source><year>2016</year><volume>11</volume><issue>4</issue><fpage>e0153268</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0153268</pub-id><?supplied-pmid 27074043?><pub-id pub-id-type="pmid">27074043</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bu</surname><given-names>D</given-names></name><name><surname>Yu</surname><given-names>K</given-names></name><name><surname>Sun</surname><given-names>S</given-names></name><name><surname>Xie</surname><given-names>C</given-names></name><name><surname>Skogerb&#x000f8;</surname><given-names>G</given-names></name><name><surname>Miao</surname><given-names>R</given-names></name><name><surname>Hui</surname><given-names>X</given-names></name><name><surname>Qi</surname><given-names>L</given-names></name><name><surname>Luo</surname><given-names>H</given-names></name><name><surname>Zhao</surname><given-names>G</given-names></name></person-group><article-title>NONCODE v3.0: integrative annotation of long noncoding RNAs</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><issue>D1</issue><fpage>D210</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1093/nar/gkr1175</pub-id><?supplied-pmid 22135294?><pub-id pub-id-type="pmid">22135294</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Karolchik</surname><given-names>D</given-names></name><name><surname>Barber</surname><given-names>G</given-names></name><name><surname>Casper</surname><given-names>J</given-names></name><etal/></person-group><article-title>The UCSC genome browser database: 2014 update</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><issue>suppl 1</issue><fpage>D590</fpage><lpage>8</lpage></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barrett</surname><given-names>T</given-names></name><name><surname>Suzek</surname><given-names>TO</given-names></name><name><surname>Troup</surname><given-names>DB</given-names></name><etal/></person-group><article-title>NCBI GEO: mining millions of expression profiles&#x02014;database and tools</article-title><source>Nucleic Acids Res</source><year>2005</year><volume>33</volume><issue>D1</issue><fpage>D562</fpage><lpage>6</lpage><?supplied-pmid 15608262?><pub-id pub-id-type="pmid">15608262</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jiang</surname><given-names>H</given-names></name><name><surname>Wong</surname><given-names>WH</given-names></name></person-group><article-title>SeqMap: mapping massive amount of oligonucleotides to the genome</article-title><source>Bioinformatics</source><year>2008</year><volume>24</volume><issue>20</issue><fpage>2395</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btn429</pub-id><?supplied-pmid 18697769?><pub-id pub-id-type="pmid">18697769</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leslie</surname><given-names>C</given-names></name><name><surname>Eskin</surname><given-names>E</given-names></name><name><surname>Noble</surname><given-names>WS</given-names></name></person-group><article-title>The spectrum kernel: a string kernel for SVM protein classification</article-title><source>Biocomputing.</source><year>2002</year><volume>7</volume><fpage>564</fpage><lpage>75</lpage><pub-id pub-id-type="pmid">11928508</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>B</given-names></name><name><surname>Liu</surname><given-names>FL</given-names></name><name><surname>Wang</surname><given-names>XL</given-names></name><name><surname>Chen</surname><given-names>JJ</given-names></name><name><surname>Fang</surname><given-names>LY</given-names></name><name><surname>Chou</surname><given-names>KC</given-names></name></person-group><article-title>Pse-in-One: A web server for generating various modes of pseudo components of DNA, RNA, and protein sequences</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><issue>W1</issue><fpage>W65</fpage><lpage>71</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv458</pub-id><?supplied-pmid 25958395?><pub-id pub-id-type="pmid">25958395</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>B</given-names></name><name><surname>Liu</surname><given-names>FL</given-names></name><name><surname>Fang</surname><given-names>LY</given-names></name><name><surname>Wang</surname><given-names>XL</given-names></name><name><surname>Chou</surname><given-names>KC</given-names></name></person-group><article-title>repDNA: a Python package to generate various modes of feature vectors for DNA sequences by incorporating user-defined physicochemical properties and sequence-order effects</article-title><source>Bioinformatics</source><year>2015</year><volume>31</volume><issue>8</issue><fpage>1307</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btu820</pub-id><?supplied-pmid 25504848?><pub-id pub-id-type="pmid">25504848</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>El-Manzalawy</surname><given-names>Y</given-names></name><name><surname>Dobbs</surname><given-names>D</given-names></name><name><surname>Honavar</surname><given-names>V</given-names></name></person-group><article-title>Predicting flexible length linear B-cell epitopes</article-title><source>Computational Syst Bioinformatics.</source><year>2008</year><volume>7</volume><fpage>121</fpage><lpage>32</lpage><pub-id pub-id-type="doi">10.1142/9781848162648_0011</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leslie</surname><given-names>CS</given-names></name><name><surname>Eskin</surname><given-names>E</given-names></name><name><surname>Cohen</surname><given-names>A</given-names></name><name><surname>Weston</surname><given-names>J</given-names></name><name><surname>Noble</surname><given-names>WS</given-names></name></person-group><article-title>Mismatch string kernels for discriminative protein classification</article-title><source>Bioinformatics</source><year>2004</year><volume>20</volume><issue>4</issue><fpage>467</fpage><lpage>76</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btg431</pub-id><?supplied-pmid 14990442?><pub-id pub-id-type="pmid">14990442</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lodhi</surname><given-names>H</given-names></name><name><surname>Saunders</surname><given-names>C</given-names></name><name><surname>Shawe-Taylor</surname><given-names>J</given-names></name><name><surname>Cristianini</surname><given-names>N</given-names></name><name><surname>Watkins</surname><given-names>C</given-names></name></person-group><article-title>Text classification using string kernels</article-title><source>J Mach Learn Res</source><year>2002</year><volume>2</volume><issue>3</issue><fpage>563</fpage><lpage>9</lpage></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Noble</surname><given-names>WS</given-names></name><name><surname>Kuehn</surname><given-names>S</given-names></name><name><surname>Thurman</surname><given-names>R</given-names></name><name><surname>Yu</surname><given-names>M</given-names></name><name><surname>Stamatoyannopoulos</surname><given-names>J</given-names></name></person-group><article-title>Predicting the in vivo signature of human gene regulatory sequences</article-title><source>Bioinformatics</source><year>2005</year><volume>21</volume><issue>suppl 1</issue><fpage>i338</fpage><lpage>43</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bti1047</pub-id><?supplied-pmid 15961476?><pub-id pub-id-type="pmid">15961476</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gupta</surname><given-names>S</given-names></name><name><surname>Dennis</surname><given-names>J</given-names></name><name><surname>Thurman</surname><given-names>RE</given-names></name><name><surname>Kingston</surname><given-names>R</given-names></name><name><surname>Stamatoyannopoulos</surname><given-names>JA</given-names></name><name><surname>Noble</surname><given-names>WS</given-names></name></person-group><article-title>Predicting human nucleosome occupancy from primary sequence</article-title><source>Plos Computational Biology</source><year>2008</year><volume>4</volume><issue>8</issue><fpage>e1000134</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1000134</pub-id><?supplied-pmid 18725940?><pub-id pub-id-type="pmid">18725940</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>W</given-names></name><name><surname>Lei</surname><given-names>T</given-names></name><name><surname>Jin</surname><given-names>D</given-names></name><etal/></person-group><article-title>PseKNC: A flexible web server for generating pseudo K-tuple nucleotide composition</article-title><source>Anal Biochem</source><year>2014</year><volume>456</volume><issue>1</issue><fpage>53</fpage><lpage>60</lpage><pub-id pub-id-type="doi">10.1016/j.ab.2014.04.001</pub-id><?supplied-pmid 24732113?><pub-id pub-id-type="pmid">24732113</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Qiu</surname><given-names>WR</given-names></name><name><surname>Xiao</surname><given-names>X</given-names></name><name><surname>Chou</surname><given-names>KC</given-names></name></person-group><article-title>iRSpot-TNCPseAAC: identify recombination spots with trinucleotide composition and pseudo amino acid components</article-title><source>Int J Mol Sci</source><year>2014</year><volume>15</volume><issue>2</issue><fpage>1746</fpage><lpage>66</lpage><pub-id pub-id-type="doi">10.3390/ijms15021746</pub-id><?supplied-pmid 24469313?><pub-id pub-id-type="pmid">24469313</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>W</given-names></name><name><surname>Xiong</surname><given-names>Y</given-names></name><name><surname>Zhao</surname><given-names>M</given-names></name><etal/></person-group><article-title>Prediction of conformational B-cell epitopes from 3D structures by random forests with a distance-based feature</article-title><source>BMC Bioinformatics</source><year>2011</year><volume>12</volume><issue>2</issue><fpage>341</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-12-341</pub-id><?supplied-pmid 21846404?><pub-id pub-id-type="pmid">21846404</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stormo</surname><given-names>GD</given-names></name></person-group><article-title>DNA binding sites: representation and discovery</article-title><source>Bioinformatics</source><year>2000</year><volume>16</volume><issue>1</issue><fpage>16</fpage><lpage>23</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/16.1.16</pub-id><?supplied-pmid 10812473?><pub-id pub-id-type="pmid">10812473</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sinha</surname><given-names>S</given-names></name></person-group><article-title>On counting position weight matrix matches in a sequence, with application to discriminative motif finding</article-title><source>Bioinformatics</source><year>2006</year><volume>22</volume><issue>14</issue><fpage>e454</fpage><lpage>63</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btl227</pub-id><?supplied-pmid 16873507?><pub-id pub-id-type="pmid">16873507</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><mixed-citation publication-type="other">Xia X. Position weight matrix, Gibbs sampler, and the associated significance tests in Motif characterization and prediction. Scientifica. 2012;917540&#x02013;917555.</mixed-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xue</surname><given-names>C</given-names></name><name><surname>Fei</surname><given-names>L</given-names></name><name><surname>Tao</surname><given-names>H</given-names></name><name><surname>Liu</surname><given-names>GP</given-names></name><name><surname>Li</surname><given-names>Y</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name></person-group><article-title>Classification of real and pseudo microRNA precursors using local structure-sequence features and support vector machine</article-title><source>BMC Bioinformatics</source><year>2005</year><volume>6</volume><issue>2</issue><fpage>1</fpage><lpage>7</lpage><pub-id pub-id-type="pmid">15631638</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tafer</surname><given-names>H</given-names></name><name><surname>Hofacker</surname><given-names>IL</given-names></name></person-group><article-title>RNAplex: a fast tool for RNA-RNA interaction search</article-title><source>Bioinformatics</source><year>2008</year><volume>24</volume><issue>22</issue><fpage>2657</fpage><lpage>63</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btn193</pub-id><?supplied-pmid 18434344?><pub-id pub-id-type="pmid">18434344</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hu</surname><given-names>X</given-names></name><name><surname>Mamitsuka</surname><given-names>H</given-names></name><name><surname>Zhu</surname><given-names>S</given-names></name></person-group><article-title>Ensemble approaches for improving HLA class I-peptide binding prediction</article-title><source>J Immunol Methods</source><year>2011</year><volume>374</volume><issue>1-2</issue><fpage>47</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1016/j.jim.2010.09.007</pub-id><?supplied-pmid 20849860?><pub-id pub-id-type="pmid">20849860</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>W</given-names></name><name><surname>Niu</surname><given-names>Y</given-names></name><name><surname>Xiong</surname><given-names>Y</given-names></name><name><surname>Zhao</surname><given-names>M</given-names></name><name><surname>Yu</surname><given-names>R</given-names></name><name><surname>Liu</surname><given-names>J</given-names></name></person-group><article-title>Computational prediction of conformational B-Cell Epitopes from antigen primary structures by ensemble learning</article-title><source>PLoS One</source><year>2012</year><volume>7</volume><issue>8</issue><fpage>e43575</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0043575</pub-id><?supplied-pmid 22927994?><pub-id pub-id-type="pmid">22927994</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>W</given-names></name><name><surname>Niu</surname><given-names>Y</given-names></name><name><surname>Zou</surname><given-names>H</given-names></name><name><surname>Luo</surname><given-names>L</given-names></name><name><surname>Liu</surname><given-names>Q</given-names></name><name><surname>Wu</surname><given-names>W</given-names></name></person-group><article-title>Accurate prediction of immunogenic T-Cell epitopes from epitope sequences using the genetic algorithm-based ensemble learning</article-title><source>PLoS One</source><year>2015</year><volume>10</volume><issue>5</issue><fpage>e0128194</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0128194</pub-id><?supplied-pmid 26020952?><pub-id pub-id-type="pmid">26020952</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><mixed-citation publication-type="other">Zhang W, Liu J, Xiong Y, Ke M, Zhang K. Predicting immunogenic T-cell epitopes by combining various sequence-derived features. In IEEE International Conference on Bioinformatics and Biomedicine. Shanghai: IEEE Computer Society; 2013. p. 4&#x02013;9.</mixed-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Breiman</surname><given-names>L</given-names></name></person-group><article-title>Random forests</article-title><source>Machine Learning.</source><year>2001</year><volume>45</volume><fpage>5</fpage><lpage>32</lpage><pub-id pub-id-type="doi">10.1023/A:1010933404324</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cortes</surname><given-names>C</given-names></name><name><surname>Vapnik</surname><given-names>V</given-names></name></person-group><article-title>Support-vector networks</article-title><source>Mach Learn</source><year>1995</year><volume>20</volume><issue>3</issue><fpage>273</fpage><lpage>97</lpage></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Srinivas</surname><given-names>M</given-names></name><name><surname>Patnaik</surname><given-names>LM</given-names></name></person-group><article-title>Adaptive probabilities of crossover and mutation in genetic algorithms</article-title><source>IEEE Trans Syst Man Cybern</source><year>1994</year><volume>24</volume><issue>4</issue><fpage>656</fpage><lpage>67</lpage><pub-id pub-id-type="doi">10.1109/21.286385</pub-id></element-citation></ref></ref-list></back></article>