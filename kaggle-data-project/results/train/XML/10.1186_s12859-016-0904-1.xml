<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 2?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">4741060</article-id><article-id pub-id-type="publisher-id">904</article-id><article-id pub-id-type="doi">10.1186/s12859-016-0904-1</article-id><article-categories><subj-group subj-group-type="heading"><subject>Software</subject></subj-group></article-categories><title-group><article-title>GenAp: a distributed SQL interface for genomic data</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes"><name><surname>Kozanitis</surname><given-names>Christos</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Patterson</surname><given-names>David A.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1">Department of Computer Science, University of California Berkeley, Soda Hall, Berkeley, 94720 California USA </aff></contrib-group><pub-date pub-type="epub"><day>4</day><month>2</month><year>2016</year></pub-date><pub-date pub-type="pmc-release"><day>4</day><month>2</month><year>2016</year></pub-date><pub-date pub-type="collection"><year>2016</year></pub-date><volume>17</volume><elocation-id>63</elocation-id><history><date date-type="received"><day>16</day><month>8</month><year>2015</year></date><date date-type="accepted"><day>7</day><month>1</month><year>2016</year></date></history><permissions><copyright-statement>&#x000a9; Kozanitis and Patterson. 2016</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold> This article is distributed under the terms of the Creative Commons Attribution 4.0 International License(<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver(<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>The impressively low cost and improved quality of genome sequencing provides to researchers of genetic diseases, such as cancer, a powerful tool to better understand the underlying genetic mechanisms of those diseases and treat them with effective targeted therapies. Thus, a number of projects today sequence the DNA of large patient populations each of which produces at least hundreds of terra-bytes of data. Now the challenge is to provide the produced data on demand to interested parties.</p></sec><sec><title>Results</title><p>In this paper, we show that the response to this challenge is a modified version of Spark SQL, a distributed SQL execution engine, that handles efficiently joins that use genomic intervals as keys. With this modification, Spark SQL serves such joins more than 50&#x000d7; faster than its existing brute force approach and 8&#x000d7; faster than similar distributed implementations. Thus, Spark SQL can replace existing practices to retrieve genomic data and, as we show, allow users to reduce the number of lines of software code that needs to be developed to query such data by an order of magnitude.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s12859-016-0904-1) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>NGS</kwd><kwd>Genomics</kwd><kwd>Big data</kwd><kwd>SQL</kwd><kwd>Interactive</kwd><kwd>Apache spark</kwd><kwd>Distributed</kwd><kwd>Kozanitis</kwd><kwd>Patterson</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000098</institution-id><institution>NIH Clinical Center (US)</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health (US)</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000001</institution-id><institution>National Science Foundation (US)</institution></institution-wrap></funding-source></award-group><award-group><funding-source><institution>LBNL</institution></funding-source></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000185</institution-id><institution>Defense Advanced Research Projects Agency (US)</institution></institution-wrap></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2016</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>In this paper, we present an enhancement to Apache Spark, which is a distributed computing framework, to accommodate efficiently SQL queries on genomic datasets. Although one can use existing technologies to import genomic datasets as SQL tables, the poor performance of those technologies to common genomic queries has made them an unattractive choice among users of Next Generation Sequencing (NGS) data.</p><p>As sequencing costs drop, more and more research centers invest in massive sequencing projects that aim to build huge databases of thousands of genomes and their associated phenotypic traits. For example the Oregon Health and Sciences University (OHSU) and the Multiple Myeloma Foundation (MMRF) are sequencing 1000 patients with Acute Myeloid Leukemia (AML) and Multiple Myeloma respectively [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. In another example, the International Cancer Genome Consortium (ICGC) [<xref ref-type="bibr" rid="CR3">3</xref>] is in the process of sequencing over 25,000 pairs of tumor and normal samples in order to catalogue the genetic abnormalities of 50 different cancer types. Each whole genome sequencing run with Illumina&#x02019;s technologies produces more than 200 GB of data.</p><p>Access to those data, though crucial for the advancement of cancer treatment, remains a challenge for researchers and data scientists.</p><p>Today there are two tiers of data access: a top and bottom tier. The top tier involves the downloading of FASTQ, BAM, or VCF files from an archive such as the SRA [<xref ref-type="bibr" rid="CR4">4</xref>] or CGHUB [<xref ref-type="bibr" rid="CR5">5</xref>] that contain reads or variants from the sequencing either of a person or a population. Although those archives use the state of the art of file sharing technology to reduce file transfer latencies over the Internet - as is the case of CGHUB that uses GeneTorrent [<xref ref-type="bibr" rid="CR6">6</xref>] - the size of the files that need to be transfered makes downloading slow. For example, the downloading of a 250 GB BAM with a 60x coverage of Whole Genome Sequencing (WGS) over a 100 Mbps Internet link takes 8 h. On the other hand, the bottom tier involves extractions of subsets of the downloaded data. A user either develops software from scratch to navigate through the data, or they use a combination of shell scripts in combination with commands of either of vcftools, samtools, and BEDtools. This practice adds a layer of complexity between data and the user for three reasons: 
<list list-type="order"><list-item><p>Scripts must be created to analyze these experiments</p></list-item><list-item><p>It requires users to manually parallelize the execution of those tools in distributed environments which get adopted to serve the increasing amounts of generated data.</p></list-item><list-item><p>It creates storage overheads with the possible creation of intermediate files that are used to transform files.</p></list-item></list></p><p>Assuming that genomic data in the order of Terabytes and Petabytes reside in distributed environments as in [<xref ref-type="bibr" rid="CR7">7</xref>], we propose that a more efficient alternative to both tiers of data access is a distributed data retrieval engine. Such an interface on the top access tier can provide data on demand by eliminating the network traffic and the need for secondary level processing at the user end. Even if owners of genomic data repositories are reluctant to provide such a feature, a data retrieval interface is still useful for the productivity of an end user at the bottom layer. With such an interface end users will not have to worry about scripting their way to retrieve and compare data from datasets of different origin, such as raw reads, variants, and annotation data.</p><p>In this work we use Spark SQL which is the distributed SQL execution engine of the Apache Spark [<xref ref-type="bibr" rid="CR8">8</xref>] framework. Spark is a user friendly high performance framework; it abstracts distributed collections of objects and it provides around 80 operators that either transform those objects through opeators such as map, filter, and groupBy, or perform actions on them through operators such as reduce, count, and foreach. Spark organizes a cluster in a Master-Slave architecture, where the driver (i.e., the master) executes a main program and transfers code to workers (i.e. slaves) to execute on those parts of the distributed objects that they contain.</p><sec id="Sec2"><title>Data model</title><p>In this work we assume that all genomic data are in ADAM format. ADAM [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>] is an open source software that separates genomic information from its underlying representation and is currently used as the main computational platform in the NIH BD2K center for big data in translational genomics [<xref ref-type="bibr" rid="CR7">7</xref>]. Such a separation removes from data users the burden of how data is represented. Thus, an ADAM user can operate on distributed storage without having to parse complicated files, given that ADAM supports and replaces all levels of genomic data that are currently represented by the legacy FASTQ, BAM, and VCF formats.</p><p>ADAM records consist of serializable objects that are stored in a distributed friendly column based format. It uses Apache AVRO [<xref ref-type="bibr" rid="CR11">11</xref>] as a data serialization system, which relies on schemas and stores them together with the data. The serialized data are stored with the use of Apache Parquet [<xref ref-type="bibr" rid="CR12">12</xref>] system, which is a columnar storage system based on Google Dremel [<xref ref-type="bibr" rid="CR13">13</xref>]. Parquet creates storage blocks by grouping sequences of records and it stores sequentially all columns of each block. Finally, given that Parquet provides built in support for writes and reads from the Hadoop File System (HDFS), ADAM transparently supports distributed environments that are built over HDFS.</p><p>Spark SQL fully recognizes Parquet files and consequently ADAM files as relational tables and it also infers their schemas. This allows users to natively query ADAM files from SPARK SQL.</p></sec><sec id="Sec3"><title>The problem</title><p>Although Spark SQL provides impressive expressive power and thus it can execute any genomic query, the main obstacle for its adoption to query genomic data has been its slow performance on two of the most frequently encountered queries: 1) random range selection, and 2) joins with interval keys. Random range selection in a collection of aligned reads took several minutes to run in a small cluster, which is embarrassingly slow given that samtools need only a few seconds. Fortunately, the rapid evolution of the open source libraries that we use (in particular Parquet on which ADAM files depend and whose API Spark SQL uses for their filtering) improved the execution of those queries by an order of magnitude as we show in the results section. Regarding the execution of interval joins between two tables, Spark SQL uses the obvious execution of filtering on their cross product. However, given the sizes that are involved in genomic data such an approach is unrealistic. If we consider for example an interval join between 1 billion aligned reads with 1 million variants, the cross product between them is 10<sup>15</sup> records and it is prohibitively slow to compute.</p><p>The contribution of this paper addresses the second performance bottleneck: joins on interval keys. We present a modification to Spark SQL that enhances the efficiency of interval joins and it thus makes it suitable to query genomic data. For this reason we use interval trees to interval join two tables in a distributed setting.</p></sec><sec id="Sec4"><title>Related work</title><p>The first generation of tools that access genomic data involves packages such as Samtools [<xref ref-type="bibr" rid="CR14">14</xref>], vcftools [<xref ref-type="bibr" rid="CR15">15</xref>], BAMtools [<xref ref-type="bibr" rid="CR16">16</xref>] and BEDtools [<xref ref-type="bibr" rid="CR17">17</xref>]. While powerful, these tools require extensive programming expertise to open and parse files of different formats, assign buffers, and manipulate various fields. In addition, given that these tools are optimised for single node computational performance, a user needs to manually parallelize them in a distributed environment.</p><p>The second generation of relevant software involves the Genome Query Language (GQL) [<xref ref-type="bibr" rid="CR18">18</xref>], which provides a clean abstraction of genomic data collection through a SQL-like interface. However, the support of GQL is only limited to queries on a small subset of fields of the entire SAM specification and it also requires extra manual effort to support distributed environments.</p><p>The third generation leverages the Hadoop ecosystem to easily provide data on demand on a distributed setting. For example, the GenomeMetric Query Language (GMQL) [<xref ref-type="bibr" rid="CR19">19</xref>] uses Apache Pig, which is a high level language that abstracts map-reduce operations, to support metadata management and queries between variants and annotation data. In another example, NEXTBIO [<xref ref-type="bibr" rid="CR20">20</xref>] uses HBase, which is Hadoop&#x02019;s NoSQL key-value store, to support data of similar nature. The scope of these tools however excludes raw data either in the FASTQ or in the BAM format.</p></sec></sec><sec id="Sec5"><title>Implementation</title><p>This section describes how we modified Spark SQL to add support for range based joins. The first step of the modification involves the grammar of Spark SQL, which we extended to simplify the syntax of those queries. Next, before describing our modification to the execution engine of Spark SQL, we provide a brief description of the interval tree and interval forest data structures which this modification utilizes.</p><sec id="Sec6"><title>Syntax</title><p>Although the existing syntax of Spark SQL suffices for a user to describe a join between two tables on interval overlap condition, it looks complex and counter intuitive for a user of genomic collections that routinely uses this operation. If we consider for example tables A (aStart: long, aEnd: long, aChr: String) and B (bStart: long, bEnd: long, bChr: String), then according to the current syntax of Spark SQL, an interval join looks like the following:</p><p><graphic xlink:href="12859_2016_904_Figa_HTML.gif" id="MO1"/></p><p>To eliminate the complexity of such a frequent operation, we enhanced the vocabulary of Spark SQL with two extra keywords, namely <italic>RANGEJOIN</italic> and <italic>GENOMEOVERLAP</italic>. In case of an interval based join, the former keyword replaces <italic>JOIN</italic> and the latter, which is followed by two tuples as arguments, specifies the overlap condition and is the only argument of the <italic>ON</italic> condition.</p><p>Using those new keywords, one can type the query of the previous example as follows:</p><p><graphic xlink:href="12859_2016_904_Figb_HTML.gif" id="MO2"/></p></sec><sec id="Sec7"><title>Interval trees</title><p>The most expensive part of a Join evaluation involves a search for overlaps between two arrays of intervals. Our implementation utilizes the interval tree data structure, which is a binary tree that is built from a collection of <italic>n</italic> intervals in O (<inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$n \log n$\end{document}</tex-math><mml:math id="M2"><mml:mi>n</mml:mi><mml:mo>log</mml:mo><mml:mi>n</mml:mi></mml:math><inline-graphic xlink:href="12859_2016_904_Article_IEq1.gif"/></alternatives></inline-formula>) time and it takes O (<inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\log n$\end{document}</tex-math><mml:math id="M4"><mml:mo>log</mml:mo><mml:mi>n</mml:mi></mml:math><inline-graphic xlink:href="12859_2016_904_Article_IEq2.gif"/></alternatives></inline-formula>) time to find which of the intervals of its collection overlap with a given query interval. Note that a brute force execution, which is what Spark SQL uses up to date, of the same operation takes quadratic time.</p><p>At this point we remind interested readers how an interval tree is constructed and searched.</p><p>Each node of the tree contains two objects. The first is the <italic>key</italic> that is the mid point of the union of the collection of intervals that are stored in the subtree which is rooted at a node. The second object is an overlapping list that contains those intervals that overlap with the <italic>key</italic>. Consider for example the interval tree of Fig. <xref rid="Fig1" ref-type="fig">1</xref> which stores intervals [1,5], [7,15], [16,19], [20,25] and [22,28]. The <italic>key</italic> of the root is 13 because that is the mid point of the union of all intervals which is [1,28]. This key overlaps only with interval [7,15], which is the only content of the overlapping list of the root.
<fig id="Fig1"><label>Fig. 1</label><caption><p>An example interval tree. This is the interval tree that stores intervals [1,5], [7,15], [16,19], [20,25], and [22,28]. Each node consists of two parts: The top part is the key of the node, which is the midpoint of the concatenation of all intervals of the subtree and the bottom part is a list of intervals that overlap with the key. So, in this example, the concatenation of all intervals is [1,28] and since the midpoint of that is 13, the root of the tree is keyed by 13 it stores interval [7,15] as it is the only interval of the collection that overlaps with 13. The intervals that do not overlap with the midpoint are used to build the left and right subtrees recursively such that all intervals of the left subtree end before and all intervals of the right subtree start after the midpoint</p></caption><graphic xlink:href="12859_2016_904_Fig1_HTML" id="MO3"/></fig></p><p>The subtrees of a node store those intervals that do not overlap with its <italic>key</italic>. The left subtree contains all intervals the end points of which are less than the <italic>key</italic>; symmetrically, the right subtree contains all intervals with start points greater than the <italic>key</italic>.</p><p>To search whether a particular interval overlaps with any of the intervals of an interval tree, one scans linearly the overlapping list of the root to search for intervals that might overlap with the query and continues traversing the tree according to the relevant position of the query interval and the <italic>keys</italic> of the encountered nodes. When the input interval ends before the <italic>key</italic> of a node the search continues only to the left subtree. Respectively, when the start of the query interval is greater than the <italic>key</italic> the search continues only to the right. In case of overlapping between an input interval and a <italic>key</italic>, the search continues to both subtrees. Assume for example a search for all overlapping intervals with [17,23] with the interval tree of Fig. <xref rid="Fig1" ref-type="fig">1</xref>. Starting from the root, after a quick scan of the overlapping list of the root returns the empty set, a comparison between the <italic>key</italic> of the root and the interval indicates that the search should continue towards the right subtree, which is rooted at the node with 22 as <italic>key</italic>. A quick scan of the overlapping list of the node detects that [20,25] is part of the solution and since the query interval overlaps with the <italic>key</italic> of the node, the search continues towards both left and right subtrees. Proceeding in the same way, the query returns as a result the set of intervals [20,25], [16,19] and [22,28].</p></sec><sec id="Sec8"><title>Interval forests</title><p>The interval tree structure that we described in the previous session is not useful as is to query genomic intervals because it does not differentiate intervals of different chromosomes. Thus, in this work we use a forest of interval trees, one per chromosome. All chromosome names are stored in a hash table and they point to the respective interval tree. Thus, a genomic interval lookup now consists of a hash table lookup to obtain the proper interval tree for the chromosome of interest before querying the tree as the previous subsection describes.</p></sec><sec id="Sec9"><title>Interval keyed joins on Spark SQL</title><p>Figure <xref rid="Fig2" ref-type="fig">2</xref> shows the implementation of the join between distributed tables A and B with interval keys.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Our distributed range join architecture. In this picture, distributed table A joins with distributed table B on genomic interval overlapping. Table A goes through a number of transformations to enable the Spark driver to create an interval forest which stores index pointers to the original data. Next it propagates the interval forest to workers which transform table B by performing interval lookups on the forest. The result of this operation is table T1, which contains tuples of data from table B and pointers to data of table A. To materialize this we join it with table A1 on those pointers and we obtain table T, which is the final result of the operation. The text under each table shows the data type of the contents of each table</p></caption><graphic xlink:href="12859_2016_904_Fig2_HTML" id="MO4"/></fig></p><p>In the first stage of the join between distributed tables A and B on interval keys and assuming A is the table with the fewest records, Spark SQL first creates distributed table A1 which is a transformation of table A into a key-value pair whose keys are the indices of each record. Note those indices are easy to obtain in Spark using the built-in function <italic>zipKeysWithIndex</italic>. Then it creates table A2 which is a key-value pair where the keys are the intervals of interest and the values are the keys of table A1. For example, consider table A whose schema is &#x02329;<italic>i</italic><italic>d</italic>(<italic>s</italic><italic>t</italic><italic>r</italic><italic>i</italic><italic>n</italic><italic>g</italic>):<italic>c</italic><italic>h</italic><italic>r</italic><italic>o</italic><italic>m</italic><italic>o</italic><italic>s</italic><italic>o</italic><italic>m</italic><italic>e</italic>(<italic>s</italic><italic>t</italic><italic>r</italic><italic>i</italic><italic>n</italic><italic>g</italic>):<italic>t</italic><italic>x</italic><italic>S</italic><italic>t</italic><italic>a</italic><italic>r</italic><italic>t</italic>(<italic>l</italic><italic>o</italic><italic>n</italic><italic>g</italic>):<italic>t</italic><italic>x</italic><italic>E</italic><italic>n</italic><italic>d</italic>(<italic>l</italic><italic>o</italic><italic>n</italic><italic>g</italic>):<italic>f</italic><italic>u</italic><italic>n</italic><italic>c</italic><italic>t</italic><italic>i</italic><italic>o</italic><italic>n</italic>(<italic>s</italic><italic>t</italic><italic>r</italic><italic>i</italic><italic>n</italic><italic>g</italic>)&#x0232a;. If table A contains entries &#x02329; &#x0201c; <italic>g</italic><italic>e</italic><italic>n</italic><italic>e</italic>1" :<italic>c</italic><italic>h</italic><italic>r</italic>3:100:1000: &#x0201c; <italic>f</italic><italic>u</italic><italic>n</italic><italic>c</italic><italic>t</italic><italic>i</italic><italic>o</italic><italic>n</italic>1" &#x0232a; and &#x02329; &#x0201c; <italic>g</italic><italic>e</italic><italic>n</italic><italic>e</italic>2" :<italic>c</italic><italic>h</italic><italic>r</italic>3:4000:5000: &#x0201c; <italic>f</italic><italic>u</italic><italic>n</italic><italic>c</italic><italic>t</italic><italic>i</italic><italic>o</italic><italic>n</italic>2" &#x0232a; then table A1 is 
<disp-formula id="Equa"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{*{20}l} \langle 0 &#x00026;: ({}^{\prime\prime}gene1^{\prime\prime}, chr3, 100, 1000, {}^{\prime\prime}function1^{\prime\prime}) \rangle \\ \langle 1 &#x00026;: ({}^{\prime\prime}gene2^{\prime\prime}, chr3, 4000, 5000, {}^{\prime\prime}function2^{\prime\prime}) \rangle \end{array} $$ \end{document}</tex-math><mml:math id="M6"><mml:mtable class="align" columnalign="left"><mml:mtr><mml:mtd class="align-1"><mml:mo>&#x02329;</mml:mo><mml:mn>0</mml:mn></mml:mtd><mml:mtd class="align-2"><mml:mo>:</mml:mo><mml:msup><mml:mrow><mml:mo>(</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">gene</mml:mtext><mml:msup><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>,</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>,</mml:mo><mml:mn>100</mml:mn><mml:mo>,</mml:mo><mml:mn>1000</mml:mn><mml:msup><mml:mrow><mml:mo>,</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">function</mml:mtext><mml:msup><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>)</mml:mo><mml:mo>&#x0232a;</mml:mo><mml:mspace width="2em"/></mml:mtd><mml:mtd><mml:mspace width="2em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd class="align-1"><mml:mo>&#x02329;</mml:mo><mml:mn>1</mml:mn></mml:mtd><mml:mtd class="align-2"><mml:mo>:</mml:mo><mml:msup><mml:mrow><mml:mo>(</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">gene</mml:mtext><mml:msup><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>,</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>,</mml:mo><mml:mn>4000</mml:mn><mml:mo>,</mml:mo><mml:mn>5000</mml:mn><mml:msup><mml:mrow><mml:mo>,</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">function</mml:mtext><mml:msup><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>)</mml:mo><mml:mo>&#x0232a;</mml:mo><mml:mspace width="2em"/></mml:mtd><mml:mtd><mml:mspace width="2em"/></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2016_904_Article_Equa.gif" position="anchor"/></alternatives></disp-formula></p><p>and the contents of table A2 are 
<disp-formula id="Equb"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{*{20}l} \langle (chr3, 100, 1000):0 \rangle \\ \langle(chr3, 4000, 5000): 1\rangle \end{array} $$ \end{document}</tex-math><mml:math id="M8"><mml:mtable class="align" columnalign="left"><mml:mtr><mml:mtd class="align-1"><mml:mo>&#x02329;</mml:mo><mml:mo>(</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>,</mml:mo><mml:mn>100</mml:mn><mml:mo>,</mml:mo><mml:mn>1000</mml:mn><mml:mo>)</mml:mo><mml:mo>:</mml:mo><mml:mn>0</mml:mn><mml:mo>&#x0232a;</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd class="align-1"><mml:mo>&#x02329;</mml:mo><mml:mo>(</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>,</mml:mo><mml:mn>4000</mml:mn><mml:mo>,</mml:mo><mml:mn>5000</mml:mn><mml:mo>)</mml:mo><mml:mo>:</mml:mo><mml:mn>1</mml:mn><mml:mo>&#x0232a;</mml:mo></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2016_904_Article_Equb.gif" position="anchor"/></alternatives></disp-formula></p><p>In the second stage of the implementation, the driver of the Spark cluster collects A2 into its local memory and it uses it to create an interval forest according to the description of the previous subsections. Then the driver broadcasts the resulted interval forest to the worker nodes.</p><p>In the third stage, Spark SQL creates table T1 with records that consist of a tuple whose first element involves a record from table B and its second element is one of the <italic>index</italic> values of table A2. In order to create table T1, Spark SQL transforms table B by using its interval keys to look up the interval forest that all worker nodes obtained from the previous step. For example consider table B with schema &#x02329;<italic>i</italic><italic>d</italic>(<italic>s</italic><italic>t</italic><italic>r</italic><italic>i</italic><italic>n</italic><italic>g</italic>):<italic>c</italic><italic>h</italic><italic>r</italic><italic>o</italic><italic>m</italic><italic>o</italic><italic>s</italic><italic>o</italic><italic>m</italic><italic>e</italic>(<italic>s</italic><italic>t</italic><italic>r</italic><italic>i</italic><italic>n</italic><italic>g</italic>):<italic>r</italic><italic>e</italic><italic>a</italic><italic>d</italic><italic>S</italic><italic>t</italic><italic>a</italic><italic>r</italic><italic>t</italic>(<italic>l</italic><italic>o</italic><italic>n</italic><italic>g</italic>):<italic>r</italic><italic>e</italic><italic>a</italic><italic>d</italic><italic>E</italic><italic>n</italic><italic>d</italic>(<italic>l</italic><italic>o</italic><italic>n</italic><italic>g</italic>)&#x0232a;. If table B contains entries 
<disp-formula id="Equc"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{*{20}l} \langle {}^{\prime\prime}read1^{\prime\prime} &#x00026;: chr3 : 150 : 250 \rangle \\ \langle {}^{\prime\prime}read2^{\prime\prime} &#x00026;: chr4: 3000: 3100 \rangle \\ \langle {}^{\prime\prime}read3^{\prime\prime} &#x00026;: chr1 : 1000 : 1100\rangle \end{array} $$ \end{document}</tex-math><mml:math id="M10"><mml:mtable class="align" columnalign="left"><mml:mtr><mml:mtd class="align-1"><mml:msup><mml:mrow><mml:mo>&#x02329;</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">read</mml:mtext><mml:msup><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup></mml:mtd><mml:mtd class="align-2"><mml:mo>:</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>:</mml:mo><mml:mn>150</mml:mn><mml:mo>:</mml:mo><mml:mn>250</mml:mn><mml:mo>&#x0232a;</mml:mo><mml:mspace width="2em"/></mml:mtd><mml:mtd><mml:mspace width="2em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd class="align-1"><mml:msup><mml:mrow><mml:mo>&#x02329;</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">read</mml:mtext><mml:msup><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup></mml:mtd><mml:mtd class="align-2"><mml:mo>:</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>4</mml:mn><mml:mo>:</mml:mo><mml:mn>3000</mml:mn><mml:mo>:</mml:mo><mml:mn>3100</mml:mn><mml:mo>&#x0232a;</mml:mo><mml:mspace width="2em"/></mml:mtd><mml:mtd><mml:mspace width="2em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd class="align-1"><mml:msup><mml:mrow><mml:mo>&#x02329;</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">read</mml:mtext><mml:msup><mml:mrow><mml:mn>3</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup></mml:mtd><mml:mtd class="align-2"><mml:mo>:</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>1</mml:mn><mml:mo>:</mml:mo><mml:mn>1000</mml:mn><mml:mo>:</mml:mo><mml:mn>1100</mml:mn><mml:mo>&#x0232a;</mml:mo><mml:mspace width="2em"/></mml:mtd><mml:mtd><mml:mspace width="2em"/></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2016_904_Article_Equc.gif" position="anchor"/></alternatives></disp-formula></p><p>and A2 is the table that we created in the previous paragraph whose interval keys created the interval forest, then only the first tuple of table B will query successfully the interval forest since its interval overlaps with the first interval of table A2. Thus the contents of table T1 are <inline-formula id="IEq3"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\left \{ \langle ({}^{\prime \prime }read1^{\prime \prime }, chr3, 150, 250) : 0 \rangle \right \}$\end{document}</tex-math><mml:math id="M12"><mml:mfenced close="}" open="{" separators=""><mml:mrow><mml:mo>&#x02329;</mml:mo><mml:msup><mml:mrow><mml:mo>(</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">read</mml:mtext><mml:msup><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>,</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>,</mml:mo><mml:mn>150</mml:mn><mml:mo>,</mml:mo><mml:mn>250</mml:mn><mml:mo>)</mml:mo><mml:mo>:</mml:mo><mml:mn>0</mml:mn><mml:mo>&#x0232a;</mml:mo></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="12859_2016_904_Article_IEq3.gif"/></alternatives></inline-formula>.</p><p>The last stage of the operation involves the generation of the final distributed table, which is the result of the range join and it consists of a tuple where the first element involves a record from table A and the second element involves a record from table B. In order to construct the output, Spark SQL joins table T1 with table A1 on the integer keys of A1 which have to match with the second element of the tuples of T1. So, in our example the transformation of T1 gives table T with contents <inline-formula id="IEq4"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\left \{\langle ({}^{\prime \prime }read1^{\prime \prime }, chr3, 150, 250):({}^{\prime \prime }gene1^{\prime \prime }, chr3, 100\right.$\end{document}</tex-math><mml:math id="M14"><mml:mfenced close="" open="{" separators=""><mml:mrow><mml:mo>&#x02329;</mml:mo><mml:msup><mml:mrow><mml:mo>(</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">read</mml:mtext><mml:msup><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>,</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>,</mml:mo><mml:mn>150</mml:mn><mml:mo>,</mml:mo><mml:mn>250</mml:mn><mml:mo>)</mml:mo><mml:mo>:</mml:mo><mml:msup><mml:mrow><mml:mo>(</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">gene</mml:mtext><mml:msup><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>,</mml:mo><mml:mtext mathvariant="italic">chr</mml:mtext><mml:mn>3</mml:mn><mml:mo>,</mml:mo><mml:mn>100</mml:mn></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="12859_2016_904_Article_IEq4.gif"/></alternatives></inline-formula>, <inline-formula id="IEq5"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\left. 1000, {}^{\prime \prime }function1^{\prime \prime })\rangle \right \}$\end{document}</tex-math><mml:math id="M16"><mml:mfenced close="}" open="" separators=""><mml:mrow><mml:mn>1000</mml:mn><mml:msup><mml:mrow><mml:mo>,</mml:mo></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mtext mathvariant="italic">function</mml:mtext><mml:msup><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>&#x02032;&#x02032;</mml:mi></mml:mrow></mml:msup><mml:mo>)</mml:mo><mml:mo>&#x0232a;</mml:mo></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="12859_2016_904_Article_IEq5.gif"/></alternatives></inline-formula></p></sec></sec><sec id="Sec10" sec-type="results"><title>Results</title><p>We demonstrate the suitability of Spark SQL on querying genomic datasets through a series of queries that involve random access of ranges and range joins. In our examples we used the ADAM representation of the raw data (BAM file of size 250 GB) of the platinum genome NA12878. Our hardware consisted of a Spark cluster with 5 workers all of which were m2.4xlarge instances on the Amazon EC2cloud.</p><p>We start by demonstrating the maturation of the technologies that we use through a random access of a simple range of gene NPM1. The query that we type is the following:</p><p><graphic xlink:href="12859_2016_904_Figc_HTML.gif" id="MO5"/></p><p>We ran the query against two different versions of the Parquet library that comprises the storage model of ADAM and whose API Spark SQL uses to evaluate filter predicates on Parquet encoded files. The earlier version of this library took 10 min to run while the latest version took 75 s. The difference was due to the fact that although the earlier versions of Parquet were evaluating a predicate on every single record of a file, recent ones utilize metadata to ignore retrieving unnecessary blocks of data. Of course, a minute long latency for such a query still seems incompetent compared to the second long latency that samtools obtain in single node through secondary indexes, but the observed trends make us believe that future improvements on the used libraries are going to bridge that gap.</p><p>In the second query, we demonstrate the efficiency of our implementation of interval joins. Note that this query joins all mapped reads of NA12878 which is in the order of 10<sup>9</sup> records with all 94 K records of the clinvar vcf that is provided at the dbsnp archive for build 142. Of course, the brute force solution which is a filtering operation on the cross product of the two tables is unrealistic since the cross product consists of a number of records in the order of 10<sup>14</sup>. The query that we used is:</p><p><graphic xlink:href="12859_2016_904_Figd_HTML.gif" id="MO6"/></p><p>Table <xref rid="Tab1" ref-type="table">1</xref> compares the performance of our implementation with the brute force original implementation of Spark SQL and the shuffle join approach that is introduced in [<xref ref-type="bibr" rid="CR10">10</xref>] as an alternative range join operation over genomic sequences. According to this table, the interval tree based method that we use in this paper is 9 times faster than shuffle join while the brute force approach could not complete the join by the time we interrupted its execution.
<table-wrap id="Tab1"><label>Table 1</label><caption><p>Performance of different implementations of interval keyed joins</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Method</th><th align="left">Runtime (hr)</th></tr></thead><tbody><tr><td align="left">Interval Tree (this paper)</td><td align="left">0.28</td></tr><tr><td align="left">Shuffle Join (ADAM)</td><td align="left">2.5</td></tr><tr><td align="left">Brute force (default Spark SQL)</td><td align="left">&#x0003e;14</td></tr></tbody></table></table-wrap></p><p>The next experiment demonstrates the scalability of our range join implementation by modifying the number of Spark workers that are used for the execution of the previous query. Figure <xref rid="Fig3" ref-type="fig">3</xref> shows that the latency of the interval tree based join drops linearly as the size of the cluster increases.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Scalability of range join. The execution time drops linearly as the cluster size increases</p></caption><graphic xlink:href="12859_2016_904_Fig3_HTML" id="MO7"/></fig></p><p>To examine the small slope of the curve of Fig. <xref rid="Fig3" ref-type="fig">3</xref>, which indicates small performance gains due to the cluster size increase, we monitored closely the utilization of the clusters. We focused mostly on the drop of the CPU utilization at points 3:26 and 3:30 of Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Figure S1 A. In the absence of any memory bottlenecks or intense network activity (Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Figure S1 B and C respectively), such a drop in CPU utilization indicates the existence of stranglers, i.e. a small subset of workers that still execute their part of a task while most nodes wait for them to finish in order to get assigned a new task. Although a complete understanding of strangler behavior in distributed systems is still an open research problem [<xref ref-type="bibr" rid="CR29">29</xref>], the nature of the dataset, which is sorted per chromosome location, makes us estimate that most probably the existence of stranglers occurs due to undesired, in this case, data locality. Given that the Hadoop File System partitions data blindly and our original input was initially sorted, it is very likely that overlapping data are grouped in the same partition and thus they are assigned to the same worker. So, when Spark SQL attempts to perform r ange join, it is likely that not all workers contribute equally to the preparation of the output; those whose most of the data do not participate in the join are going to complete their tasks earlier than workers that produce most of the output. We leave as a future work research on the best possible partitioning of genomic datasets in order to get queried as efficiently as possible.</p><p>Our final experiment stresses the tremendous savings in programmer productivity that the use of Spark SQL yields compared to the alternatives that researchers use today. The query that we used in this example was: &#x0201c;Are there any reads of the NA12878 dataset that align with the reference genome only with matches or mismatches (M CIGAR operations only), overlap with any variant location of the clinvar dataset and contain the respective alternate allele?&#x0201d;. Using existing genomic processing software, we implemented the same query in two steps: 
<list list-type="order"><list-item><p>We used the <italic>intersect</italic> feature of BEDtools to retrieve all those alignments of NA12878 that overlap with any locus of the clinvar dataset.</p></list-item><list-item><p>We wrote custom software using the samtools API to further process the retrieved alignments and discard those whose reads contain the reference allele.</p></list-item></list></p><p>The custom software that we wrote and we also include as an additional file (see: Additional file <xref rid="MOESM7" ref-type="media">7</xref>) consists of 130 lines of C++ code. The execution of both steps took 27 min to run; BEDtools took 26 min to filter the dataset and the resulted file was processed by our custom software in less than a minute. On the other hand, the Spark SQL implementation of this query requires an interval based join between the contents of NA12878 and clinvar tables followed by a <italic>WHERE</italic> statement that filters out rows with either complex CIGAR strings or with sequences that do not contain the alternate allele in the respective positions; the resulted Spark SQL script, which we also include as an additional file (see Additional file 6; interested readers can experiment with the code of this file against small datasets that we provide in Additional files <xref rid="MOESM3" ref-type="media">3</xref>, <xref rid="MOESM4" ref-type="media">4</xref> and <xref rid="MOESM5" ref-type="media">5</xref>), contains 11 lines of code that took 16 min to run on the same hardware.</p><p>Table <xref rid="Tab2" ref-type="table">2</xref> summarizes the results of this experiment. They show a decrease by an order of magnitude on the number of lines of code that needs to be developed with Spark SQL to implement complex queries on genomic data. In addition, the execution of the Spark SQL query was faster than the execution of BEDtools. The reason was that the column based storage and predicate push-down capabilities of the ADAM format that we used to represent alignments allowed Spark SQL to retrieve from the disk only the absolutely necessary fields of the dataset.
<table-wrap id="Tab2"><label>Table 2</label><caption><p>Comparison between Spark SQL and existing software methods that are used to retrieve genomic data. Complex queries that today need more than a hundred lines of code to be implemented, take an order of magnitude fewer lines of code on Spark SQL without performance sacrifices</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Software tool</th><th align="left">Lines of code</th><th align="left">Runtime (min)</th></tr></thead><tbody><tr><td align="left">Spark SQL</td><td align="left">11</td><td align="left">16</td></tr><tr><td align="left">State of the art software</td><td align="left">BEDtools: 1</td><td align="left">26</td></tr><tr><td align="left"/><td align="left">samtols API based code: 130</td><td align="left">1</td></tr><tr><td align="left"/><td align="left">total: 131</td><td align="left">27</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec11" sec-type="discussion"><title>Discussion</title><p>In our work we utilize the latest technologies in distributed computing, such as Spark SQL through Apache Spark and Parquet through ADAM in order to enable the research community to easily query heterogeneous datasets of genomic data either against each other (for example mixed queries between variant and raw data) or with annotated files.</p><p>Out of the two categories of software that query large amounts of data, NoSQL systems and distributed SQL execution engines, in this work we use a distributed SQL execution engine to address these needs because of its expressive power, and its scalability. As traditional relational databases are hard to scale, large amounts of data can be accessed via NoSQL systems such as MongoDB [<xref ref-type="bibr" rid="CR21">21</xref>], HBase [<xref ref-type="bibr" rid="CR22">22</xref>], and Cassandra [<xref ref-type="bibr" rid="CR23">23</xref>]. However, in the light of [<xref ref-type="bibr" rid="CR24">24</xref>] which advocates the use of relational algebra to retrieve genomic data, access patterns to sequencing data typically involve a combination of operators such as projections, selections, interval joins, and aggregations; NoSQL systems do not support all these operations. On the other hand, distributed SQL execution engines, such as as Hive [<xref ref-type="bibr" rid="CR25">25</xref>], Impala nature [<xref ref-type="bibr" rid="CR26">26</xref>], and Spark SQL [<xref ref-type="bibr" rid="CR27">27</xref>] provide scalable access to the data through relational algebra based interfaces.</p><p>Of course, Spark SQL is not the only distributed SQL execution engine, but we chose it in this work because of its sophisticated and extensible query planner. Big Data users have been using distributed SQL execution engines as an abstraction of distributed operations, such as map and reduce, through relational algebra operations. Earlier engines, such as Shark [<xref ref-type="bibr" rid="CR28">28</xref>], Hive [<xref ref-type="bibr" rid="CR25">25</xref>], and Impala [<xref ref-type="bibr" rid="CR26">26</xref>] rely on a step-by-step execution of the SQL statements. Spark SQL, on the other hand, provides a modular query planner and an optimizer to increase the performance of the execution. In this work, we use the modularity of the query planner to enhance the execution of some common queries.</p></sec><sec id="Sec12" sec-type="conclusion"><title>Conclusions</title><p>The central message of this paper is that a state of the art distributed SQL execution engine, such as Spark SQL, can be modified to provide an interactive SQL interface on all kinds of genomic data. Of course, other tools, such as samtools and bedtools can provide similar functionality, but Spark SQL provides a simple and expressive mechanism of querying heterogeneous genomic data over distributed hardware setups. And we expect that as big data technologies mature, the performance of Spark SQL is going to match the performance of today&#x02019;s popular tools.</p></sec><sec id="Sec13"><title>Availability and requirements</title><p><list list-type="bullet"><list-item><p><bold>Project name:</bold> GenAp</p></list-item><list-item><p><bold>Project homepage:</bold> branch rangeJoinsExtraPredicates-1.3 of <ext-link ext-link-type="uri" xlink:href="https://github.com/kozanitis/spark">https://github.com/kozanitis/spark</ext-link>. see Additional file 1 for instructions on how to clone and build the project.</p></list-item><list-item><p><bold>Operating systems:</bold> Linux</p></list-item><list-item><p><bold>Programming Language:</bold> Scala</p></list-item><list-item><p><bold>Other requirements:</bold> Maven, Java. We verified that the project builds with Maven 10.10.4 and runs with Java version 1.7.0_51</p></list-item><list-item><p><bold>Licence:</bold> Apache 2</p></list-item></list></p></sec></body><back><app-group><app id="App1"><sec id="Sec14"><title>Additional files</title><p><media position="anchor" xlink:href="12859_2016_904_MOESM1_ESM.txt" id="MOESM1"><label>Additional file 1</label><caption><p>
<bold>This file contains instructions on how to clone and build our modification of Apache spark and run it in local mode on a user&#x02019;s computer.</bold> (TXT 1 kb)</p></caption></media></p><p><media position="anchor" xlink:href="12859_2016_904_MOESM2_ESM.pdf" id="MOESM2"><label>Additional file 2</label><caption><p>
<bold>This is a file that contains the supplementary figures that we refer to in the manuscript.</bold> (PDF 615 kb)</p></caption></media></p><p><media position="anchor" xlink:href="12859_2016_904_MOESM3_ESM.tar" id="MOESM3"><label>Additional file 3</label><caption><p>
<bold>This tarball contains a sample file of aligned reads in ADAM format.</bold> Note that this is a binary format and we provide a user the oportunity to explore this file by attaching the equivalent SAM file of the same alignments. (TAR 21 kb)</p></caption></media></p><p><media position="anchor" xlink:href="12859_2016_904_MOESM4_ESM.sam" id="MOESM4"><label>Additional file 4</label><caption><p>
<bold>The SAM equivalent of the Additional File</bold>
<xref rid="MOESM3" ref-type="media">3</xref>. (SAM 3 kb)</p></caption></media></p><p><media position="anchor" xlink:href="12859_2016_904_MOESM5_ESM.vcf" id="MOESM5"><label>Additional file 5</label><caption><p>
<bold>This is a sample vcf file similar to the one we used in the results section.</bold> (VCF 9 kb)</p></caption></media></p><p><media position="anchor" xlink:href="12859_2016_904_MOESM6_ESM.scala" id="MOESM6"><label>Additional file 6</label><caption><p>
<bold>This is sample code that uses Spark SQL to perform an interval based join to the contents of sampleReads.adam with the contents of Additional File</bold>
<xref rid="MOESM5" ref-type="media">5</xref>. (SCALA 1 kb)</p></caption></media></p><p><media position="anchor" xlink:href="12859_2016_904_MOESM7_ESM.tar" id="MOESM7"><label>Additional file 7</label><caption><p>
<bold>This tarball contains the custom C++ software that we wrote to implement the last query of the results section.</bold> For reference, it also contains the respective Spark SQL implementation. (TAR 9 kb)</p></caption></media></p></sec></app></app-group><glossary><title>Abbreviations</title><def-list><def-item><term>NGS</term><def><p>next generation sequencing</p></def></def-item><def-item><term>OHSU</term><def><p>oregon health and sciences university</p></def></def-item><def-item><term>MMRF</term><def><p>multiple myeloma foundation</p></def></def-item><def-item><term>AML</term><def><p>acute myeloid leukemia</p></def></def-item><def-item><term>ICGC</term><def><p>international cancer genome symposium</p></def></def-item><def-item><term>WGS</term><def><p>whole genome sequencing</p></def></def-item><def-item><term>HDFS</term><def><p>hadoop file system</p></def></def-item><def-item><term>GQL</term><def><p>genome query language</p></def></def-item><def-item><term>GMQL</term><def><p>genomeMetric query language</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Competing interests</bold></p><p>The authors declare that they have no competing interests.</p></fn><fn><p><bold>Authors&#x02019; contributions</bold></p><p>CK modified Spark SQL, performed the experiments and wrote the paper. DP guided the project, gave advice on performance related issues of the implementation and edited the paper.</p></fn></fn-group><ack><title>Acknowledgements</title><p>The authors thank Frank Austin Nothaft for providing feedback. This research is supported in part by NIH BD2K Award 1-U54HG007990-01 and NIH Cancer Cloud Pilot Award HHSN261201400006C, NSF CISE Expeditions Award CCF-1139158, LBNL Award 7076018, and DARPA XData Award FA8750-12-2-0331, and gifts from Amazon Web Services, Google, SAP, The Thomas and Stacey Siebel Foundation, Adatao, Adobe, Apple, Inc., Blue Goji, Bosch, C3Energy, Cisco, Cray, Cloudera, EMC2, Ericsson, Facebook, Guavus, Huawei, Informatica, Intel, Microsoft, NetApp, Pivotal, Samsung, Schlumberger, Splunk, Virdata and VMware.</p></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1</label><mixed-citation publication-type="other">BeatAML Project. <ext-link ext-link-type="uri" xlink:href="http://www.ohsu.edu/xd/health/services/cancer/about-us/druker/upload/beat-aml-flyer-v5.pdf">http://www.ohsu.edu/xd/health/services/cancer/about-us/druker/upload/beat-aml-flyer-v5.pdf</ext-link>.</mixed-citation></ref><ref id="CR2"><label>2</label><mixed-citation publication-type="other">MMRF CoMMpass Project. <ext-link ext-link-type="uri" xlink:href="https://research.themmrf.org/">https://research.themmrf.org/</ext-link>.</mixed-citation></ref><ref id="CR3"><label>3</label><mixed-citation publication-type="other">ICGC Cancer Genome Projects. <ext-link ext-link-type="uri" xlink:href="https://icgc.org/">https://icgc.org/</ext-link>.</mixed-citation></ref><ref id="CR4"><label>4</label><mixed-citation publication-type="other">Sequence Read Archive (SRA). <ext-link ext-link-type="uri" xlink:href="http://www.ncbi.nlm.nih.gov/sra/">http://www.ncbi.nlm.nih.gov/sra/</ext-link>.</mixed-citation></ref><ref id="CR5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wilks</surname><given-names>C</given-names></name><name><surname>Cline</surname><given-names>MS</given-names></name><name><surname>Weiler</surname><given-names>E</given-names></name><name><surname>Diehkans</surname><given-names>M</given-names></name><name><surname>Craft</surname><given-names>B</given-names></name><name><surname>Martin</surname><given-names>C</given-names></name><etal/></person-group><article-title>The cancer genomics hub (cghub): overcoming cancer through the power of torrential data</article-title><source>Database</source><year>2014</year><volume>2014</volume><fpage>093</fpage><pub-id pub-id-type="doi">10.1093/database/bau093</pub-id></element-citation></ref><ref id="CR6"><label>6</label><mixed-citation publication-type="other">Annai&#x02019;s Gene Torrent. A High Speed File Transfer Protocol. <ext-link ext-link-type="uri" xlink:href="https://annaisystems.zendesk.com/hc/en-us/articles/204184548-What-is-GNOS-">https://annaisystems.zendesk.com/hc/en-us/articles/204184548-What-is-GNOS-</ext-link>.</mixed-citation></ref><ref id="CR7"><label>7</label><mixed-citation publication-type="other">Paten B, Diekhans M, Druker BJ, Friend S, Guinney J, Gassner N, et al.The nih bd2k center for big data in translational genomics. J Am Med Inf Assoc. 2015; 047.</mixed-citation></ref><ref id="CR8"><label>8</label><mixed-citation publication-type="other">Zaharia M, Chowdhury M, Das T, Dave A, Ma J, McCauley M, et al.Resilient distributed datasets: A fault-tolerant abstraction for in-memory cluster computing. In: Proceedings of the 9th USENIX Conference on Networked Systems Design and Implementation. USENIX Association: 2012. p. 2&#x02013;2.</mixed-citation></ref><ref id="CR9"><label>9</label><mixed-citation publication-type="other">Massie M, Nothaft F, Hartl C, Kozanitis C, Schumacher A, Joseph AD, et al.Adam: Genomics formats and processing patterns for cloud scale computing. 2013. EECS Department, University of California, Berkeley, Tech. Rep. UCB/EECS-2013-207.</mixed-citation></ref><ref id="CR10"><label>10</label><mixed-citation publication-type="other">Nothaft FA, Massie M, Danford T, Zhang Z, Laserson U, Yeksigian C, et al.Rethinking data-intensive science using scalable analytics systems. In: Proceedings of the 2015 ACM SIGMOD International Conference on Management of Data. ACM: 2015. p. 631&#x02013;46.</mixed-citation></ref><ref id="CR11"><label>11</label><mixed-citation publication-type="other">Apache Avro. <ext-link ext-link-type="uri" xlink:href="https://avro.apache.org/">https://avro.apache.org/</ext-link>.</mixed-citation></ref><ref id="CR12"><label>12</label><mixed-citation publication-type="other">Apache Parquet. <ext-link ext-link-type="uri" xlink:href="https://parquet.apache.org/">https://parquet.apache.org/</ext-link>.</mixed-citation></ref><ref id="CR13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Melnik</surname><given-names>S</given-names></name><name><surname>Gubarev</surname><given-names>A</given-names></name><name><surname>Long</surname><given-names>JJ</given-names></name><name><surname>Romer</surname><given-names>G</given-names></name><name><surname>Shivakumar</surname><given-names>S</given-names></name><name><surname>Tolton</surname><given-names>M</given-names></name><etal/></person-group><article-title>Dremel: interactive analysis of web-scale datasets</article-title><source>Proc VLDB Endowment</source><year>2010</year><volume>3</volume><issue>1&#x02013;2</issue><fpage>330</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.14778/1920841.1920886</pub-id></element-citation></ref><ref id="CR14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Handsaker</surname><given-names>B</given-names></name><name><surname>Wysoker</surname><given-names>A</given-names></name><name><surname>Fennell</surname><given-names>T</given-names></name><name><surname>Ruan</surname><given-names>J</given-names></name><name><surname>Homer</surname><given-names>N</given-names></name><etal/></person-group><article-title>The sequence alignment/map format and samtools</article-title><source>Bioinforma</source><year>2009</year><volume>25</volume><issue>16</issue><fpage>2078</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btp352</pub-id></element-citation></ref><ref id="CR15"><label>15</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Danecek</surname><given-names>P</given-names></name><name><surname>Auton</surname><given-names>A</given-names></name><name><surname>Abecasis</surname><given-names>G</given-names></name><name><surname>Albers</surname><given-names>CA</given-names></name><name><surname>Banks</surname><given-names>E</given-names></name><name><surname>DePristo</surname><given-names>MA</given-names></name><etal/></person-group><article-title>The variant call format and vcftools</article-title><source>Bioinforma</source><year>2011</year><volume>27</volume><issue>15</issue><fpage>2156</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btr330</pub-id></element-citation></ref><ref id="CR16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barnett</surname><given-names>DW</given-names></name><name><surname>Garrison</surname><given-names>EK</given-names></name><name><surname>Quinlan</surname><given-names>AR</given-names></name><name><surname>Stromberg</surname><given-names>MP</given-names></name><name><surname>Marth</surname><given-names>GT</given-names></name></person-group><article-title>BamTools: a C++ API and toolkit for analyzing and managing BAM files</article-title><source>Bioinformatics</source><year>2011</year><volume>27</volume><issue>12</issue><fpage>1691</fpage><lpage>92</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btr174</pub-id><?supplied-pmid 21493652?><pub-id pub-id-type="pmid">21493652</pub-id></element-citation></ref><ref id="CR17"><label>17</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Quinlan</surname><given-names>AR</given-names></name><name><surname>Hall</surname><given-names>IM</given-names></name></person-group><article-title>BEDTools: a flexible suite of utilities for comparing genomic features</article-title><source>Bioinforma</source><year>2010</year><volume>26</volume><issue>6</issue><fpage>841</fpage><lpage>2</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btq033</pub-id></element-citation></ref><ref id="CR18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kozanitis</surname><given-names>C</given-names></name><name><surname>Heiberg</surname><given-names>A</given-names></name><name><surname>Varghese</surname><given-names>G</given-names></name><name><surname>Bafna</surname><given-names>V</given-names></name></person-group><article-title>Using genome query language to uncover genetic variation</article-title><source>Bioinforma</source><year>2014</year><volume>30</volume><issue>1</issue><fpage>1</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btt250</pub-id></element-citation></ref><ref id="CR19"><label>19</label><mixed-citation publication-type="other">Masseroli M, Pinoli P, Venco F, Kaitoua A, Jalili V, Palluzzi F, Muller H, et al. Genometric query language: A novel approach to large-scale genomic data management. Bioinforma. 2015; 048.</mixed-citation></ref><ref id="CR20"><label>20</label><mixed-citation publication-type="other">Nextbio&#x02019;s Scalable SAAS Platform for Big Data. <ext-link ext-link-type="uri" xlink:href="http://www.nextbio.com/b/corp/products.nb">http://www.nextbio.com/b/corp/products.nb</ext-link>.</mixed-citation></ref><ref id="CR21"><label>21</label><mixed-citation publication-type="other">mongoDB. <ext-link ext-link-type="uri" xlink:href="https://www.mongodb.org/">https://www.mongodb.org/</ext-link>.</mixed-citation></ref><ref id="CR22"><label>22</label><mixed-citation publication-type="other">Apache HBase. <ext-link ext-link-type="uri" xlink:href="http://hbase.apache.org/">http://hbase.apache.org/</ext-link>.</mixed-citation></ref><ref id="CR23"><label>23</label><mixed-citation publication-type="other">Apache Cassandra. <ext-link ext-link-type="uri" xlink:href="http://cassandra.apache.org/">http://cassandra.apache.org/</ext-link>.</mixed-citation></ref><ref id="CR24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bafna</surname><given-names>V</given-names></name><name><surname>Deutsch</surname><given-names>A</given-names></name><name><surname>Heiberg</surname><given-names>A</given-names></name><name><surname>Kozanitis</surname><given-names>C</given-names></name><name><surname>Ohno-Machado</surname><given-names>L</given-names></name><name><surname>Varghese</surname><given-names>G</given-names></name><etal/></person-group><article-title>Abstractions for genomics</article-title><source>Communications of the ACM</source><year>2013</year><volume>56</volume><issue>1</issue><fpage>83</fpage><lpage>93</lpage><pub-id pub-id-type="doi">10.1145/2398356.2398376</pub-id><?supplied-pmid 25284821?><pub-id pub-id-type="pmid">25284821</pub-id></element-citation></ref><ref id="CR25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thusoo</surname><given-names>A</given-names></name><name><surname>Sarma</surname><given-names>JS</given-names></name><name><surname>Jain</surname><given-names>N</given-names></name><name><surname>Shao</surname><given-names>Z</given-names></name><name><surname>Chakka</surname><given-names>P</given-names></name><name><surname>Anthony</surname><given-names>S</given-names></name><etal/></person-group><article-title>Hive: a warehousing solution over a map-reduce framework</article-title><source>Proc VLDB Endowment</source><year>2009</year><volume>2</volume><issue>2</issue><fpage>1626</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.14778/1687553.1687609</pub-id></element-citation></ref><ref id="CR26"><label>26</label><mixed-citation publication-type="other">Cloudera Impala. <ext-link ext-link-type="uri" xlink:href="http://impala.io/">http://impala.io/</ext-link>.</mixed-citation></ref><ref id="CR27"><label>27</label><mixed-citation publication-type="other">Armbrust M, Xin RS, Lian C, Huai Y, Liu D, Bradley JK, et al.Spark sql: Relational data processing in spark. In: Proceedings of the 2015 ACM SIGMOD International Conference on Management of Data. ACM: 2015. p. 1383&#x02013;94.</mixed-citation></ref><ref id="CR28"><label>28</label><mixed-citation publication-type="other">Xin RS, Rosen J, Zaharia M, Franklin MJ, Shenker S, Stoica I. Shark: Sql and rich analytics at scale. In: Proceedings of the 2013 ACM SIGMOD International Conference on Management of Data. ACM: 2013. p. 13&#x02013;24.</mixed-citation></ref><ref id="CR29"><label>29</label><mixed-citation publication-type="other">Yadwadkar NJ, Ananthanarayanan G, Katz R. Wrangler: Predictable and faster jobs using fewer resources. In: Proceedings of the ACM Symposium on Cloud Computing. ACM: 2014. p. 1&#x02013;14.</mixed-citation></ref></ref-list></back></article>