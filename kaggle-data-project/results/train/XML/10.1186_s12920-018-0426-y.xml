<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Med Genomics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Med Genomics</journal-id><journal-title-group><journal-title>BMC Medical Genomics</journal-title></journal-title-group><issn pub-type="epub">1755-8794</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6245874</article-id><article-id pub-id-type="publisher-id">426</article-id><article-id pub-id-type="doi">10.1186/s12920-018-0426-y</article-id><article-categories><subj-group subj-group-type="heading"><subject>Technical Advance</subject></subj-group></article-categories><title-group><article-title>integRATE: a desirability-based data integration framework for the prioritization of candidate genes across heterogeneous omics and its application to preterm birth</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Eidem</surname><given-names>Haley R.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Steenwyk</surname><given-names>Jacob L.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Wisecaver</surname><given-names>Jennifer H.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Capra</surname><given-names>John A.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Abbot</surname><given-names>Patrick</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-7248-6551</contrib-id><name><surname>Rokas</surname><given-names>Antonis</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2264 7217</institution-id><institution-id institution-id-type="GRID">grid.152326.1</institution-id><institution>Department of Biological Sciences, </institution><institution>Vanderbilt University, </institution></institution-wrap>Nashville, TN USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1937 2197</institution-id><institution-id institution-id-type="GRID">grid.169077.e</institution-id><institution>Department of Biochemistry, </institution><institution>Purdue University, </institution></institution-wrap>West Lafayette, IN USA </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2264 7217</institution-id><institution-id institution-id-type="GRID">grid.152326.1</institution-id><institution>Department of Biomedical Informatics, </institution><institution>Vanderbilt University, </institution></institution-wrap>Nashville, TN USA </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2264 7217</institution-id><institution-id institution-id-type="GRID">grid.152326.1</institution-id><institution>Vanderbilt Genetics Institute, </institution><institution>Vanderbilt University, </institution></institution-wrap>Nashville, TN USA </aff></contrib-group><pub-date pub-type="epub"><day>19</day><month>11</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>19</day><month>11</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>11</volume><elocation-id>107</elocation-id><history><date date-type="received"><day>16</day><month>4</month><year>2018</year></date><date date-type="accepted"><day>7</day><month>11</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">The integration of high-quality, genome-wide analyses offers a robust approach to elucidating genetic factors involved in complex human diseases. Even though several methods exist to integrate heterogeneous omics data, most biologists still manually select candidate genes by examining the intersection of lists of candidates stemming from analyses of different types of omics data that have been generated by imposing hard (strict) thresholds on quantitative variables, such as <italic>P</italic>-values and fold changes, increasing the chance of missing potentially important candidates.</p></sec><sec><title>Methods</title><p id="Par2">To better facilitate the unbiased integration of heterogeneous omics data collected from diverse platforms and samples, we propose a desirability function framework for identifying candidate genes with strong evidence across data types as targets for follow-up functional analysis. Our approach is targeted towards disease systems with sparse, heterogeneous omics data, so we tested it on one such pathology: spontaneous preterm birth (sPTB).</p></sec><sec><title>Results</title><p id="Par3">We developed the software integRATE, which uses desirability functions to rank genes both within and across studies, identifying well-supported candidate genes according to the cumulative weight of biological evidence rather than based on imposition of hard thresholds of key variables. Integrating 10 sPTB omics studies identified both genes in pathways previously suspected to be involved in sPTB as well as novel genes never before linked to this syndrome. integRATE is available as an R package on GitHub (<ext-link ext-link-type="uri" xlink:href="https://github.com/haleyeidem/integRATE">https://github.com/haleyeidem/integRATE</ext-link>).</p></sec><sec><title>Conclusions</title><p id="Par4">Desirability-based data integration is a solution most applicable in biological research areas where omics data is especially heterogeneous and sparse, allowing for the prioritization of candidate genes that can be used to inform more targeted downstream functional analyses.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12920-018-0426-y) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Prematurity</kwd><kwd>Integrative genomics</kwd><kwd>Complex disease</kwd><kwd>Candidate gene ranking</kwd><kwd>Venn diagram</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100011636</institution-id><institution>March of Dimes Prematurity Research Center Ohio Collaborative</institution></institution-wrap></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000861</institution-id><institution>Burroughs Wellcome Fund</institution></institution-wrap></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par12">Biological processes underlying disease pathogenesis typically involve a complex, dynamic, and interconnected system of molecular and environmental factors [<xref ref-type="bibr" rid="CR1">1</xref>]. Advances in high-throughput omics technologies have allowed for the collection of data corresponding to the genomic, transcriptomic, epigenomic, proteomic, and metabolomic elements that contribute to variation in these biological processes [<xref ref-type="bibr" rid="CR2">2</xref>]. However, each of these omics approaches, when employed in isolation, can only capture variation within a single layer of a much more complicated biological system [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR4">4</xref>]. For example, even though the thousands of single nucleotide polymorphisms (SNPs) that have been linked to complex diseases or traits via genome-wide association studies (GWAS) have greatly contributed to our understanding of complex disease, these SNPs may only be tagging the causal genetic element(s), and we still lack in depth knowledge of the molecular mechanisms underlying the vast majority of these associations [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>]. Similarly, transcriptomics studies routinely identify hundreds to thousands of differentially expressed genes between diseased and healthy tissue samples, but disentangling the disease-causing changes in gene expression from its byproducts can be far more challenging [<xref ref-type="bibr" rid="CR7">7</xref>]. Given the limitations of each omics approach and their focuses on different layers of the biological system, integration of different types of omics data to identify the key biological pathways involved in disease has emerged as a promising avenue for research [<xref ref-type="bibr" rid="CR4">4</xref>].</p><p id="Par13">One integrative study design is to obtain diverse types of omics data from the same tissue samples or patient cohorts. The resulting data can then be vertically integrated (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, top left) to identify candidate genes and pathways involved in complex disease. Alternatively, a single type of omics data can be collected from a variety of tissue samples or patient cohorts, facilitating their horizontal integration across many samples, which can substantially increase the experiment&#x02019;s power (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, top right). In both vertical and horizontal integration study designs, the availability of diverse types of omics data from the same samples enables the use of a variety of statistical integration approaches (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, bottom) [<xref ref-type="bibr" rid="CR8">8</xref>]. For example, multi-staged integration uses multiple steps to first identify associations between different data types and then identify associations between data types and the phenotype of interest [<xref ref-type="bibr" rid="CR9">9</xref>], whereas meta-dimensional integration combines data simultaneously based on concatenation, transformation, or model building [<xref ref-type="bibr" rid="CR10">10</xref>].<fig id="Fig1"><label>Fig. 1</label><caption><p>Selecting a data integration strategy depends on the structure of accessible multi-omics data. (<bold>a</bold>, left) If multiple types of omics data (&#x02018;multi-omics&#x02019;) are available for the same cohort of patients, vertical integrative analysis can be performed to combine information across data types. This integration can be achieved using a variety of multi-staged and meta-dimensional statistical approaches that identify disease subtypes, regulatory networks, and driver genes. (<bold>a</bold>, right) If the opposite is true and a specific type of omics data is available across a number of different patient cohorts, horizontal meta-analysis can be performed to increase statistical power and identify disease-associated perturbations. <bold>b</bold> In some cases, however, experimental data are only available for different omics data types from different cohorts of patients and neither vertical nor horizontal data integration can be performed. In these situations, integration relies on mapping data to common units (e.g., genes or pathways) and then either integrating transformed data or simply overlapping candidate sets. The software approach presented here (integRATE) utilizes desirability functions to transform and integrate heterogeneous data allowing for the prioritization of candidate genes for functional analysis</p></caption><graphic xlink:href="12920_2018_426_Fig1_HTML" id="MO1"/></fig></p><p id="Par14">Although multi-omics data sets generated using vertical and horizontal study designs are becoming increasingly common, such data sets are lacking for many complex diseases [<xref ref-type="bibr" rid="CR11">11</xref>&#x02013;<xref ref-type="bibr" rid="CR15">15</xref>]. Often, heterogeneous omics data are collected study by study, for a limited set of tissue samples and across only one or two omics data types at a time (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b, top). For each study, a long list of genes or genomic regions with associated data is produced and sorted based on effect size (e.g., fold change), significance (e.g., <italic>P</italic>-value), or some other criterion. Hard thresholds can then be imposed on <italic>P</italic>-values, for example, to bin the genes or genomic regions and identify significant candidates for further analysis; this type of approach can then be applied across multiple, heterogeneous omics studies.</p><p id="Par15">Several problems exist with the imposition of hard thresholds, however. Including (or excluding) genes or genomic regions as candidates based on <italic>P</italic>-value, fold change, expression level, and/or odds ratio cutoffs introduces biases and removes information, especially when combining multiple cutoffs from several criteria [<xref ref-type="bibr" rid="CR16">16</xref>&#x02013;<xref ref-type="bibr" rid="CR18">18</xref>]. These cutoffs can sometimes even be arbitrary, like selecting the top n or n% from each data set. Additionally, statistical significance is not always equivalent to biological significance, meaning that non-statistically significant genes may still be involved in disease pathogenesis, or vice versa. Moreover, while selecting the top n genes might limit the scope of further functional analysis, the alternative approach of selecting all significant hits could mean that thousands of genes are identified as candidates. A final consideration in analyzing heterogeneous omics data is that we sometimes do not know any genes, pathways, or networks that have already been shown to be involved in complex disease. Some integration methods, especially those based on prediction (e.g., machine learning, network analysis), depend on the availability of such knowledge for algorithm training and cannot be performed in their absence [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR19">19</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>].</p><p id="Par16">Desirability functions provide a way to integrate heterogeneous omics data in systems where gold standards (i.e., genes known to be involved in the complex disease under investigation) are not yet known (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b, bottom). Originally developed for industrial quality control, desirability functions have been successfully used in chemoinformatics to rank compounds for drug discovery and have been proposed as a way to integrate multiple selection criteria in functional genomics experiments [<xref ref-type="bibr" rid="CR23">23</xref>&#x02013;<xref ref-type="bibr" rid="CR27">27</xref>]. In the context of integrating diverse but heterogeneous omics data, desirability functions allow for the ranking and prioritizing of candidate genes based on cumulative evidence across data types and their variables, rather than within-study separation of significant and non-significant genes based on single variables in single studies. For example, a 2015 study initially proposed the use of desirability functions to integrate multiple selection criteria for ranking, selecting, and prioritizing genes across heterogeneous biological analyses and demonstrated its use by analyzing a set of microarray-generated gene expression data [<xref ref-type="bibr" rid="CR23">23</xref>].</p><p id="Par17">To facilitate data integration in the presence of heterogeneous multi-omics data and when prior biological knowledge is limited, we propose a desirability-based framework to prioritize candidate genes for functional analysis. To facilitate application of our framework, we built a user-friendly software package called integRATE, which takes as input data sets from any omics experiment and generates a single desirability score based on all available information. This approach is targeted towards biological processes or diseases with particularly sparse or heterogeneous data, so we test integRATE on a set of 10 omics data sets related to spontaneous preterm birth (sPTB), a complex disease where heterogeneous multi-omics data are the only omics data currently available.</p></sec><sec id="Sec2"><title>Design</title><sec id="Sec3"><title>Variable transformation</title><p id="Par18">First, relevant studies need to be identified for integration; this selection can be based on any number of characteristics including tissue(s) sampled, disease subtype, or experimental designs (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, step 1). The data in each of these studies (e.g., gene expression data, proteomic data, GWAS data, etc.) are typically specific to or can be mapped to individual genetic elements (e.g., genes) in the genome. Furthermore, each study&#x02019;s data contain genetic element-specific values for many different variables (e.g., <italic>P</italic>-value, odds ratio, fold change, etc.). Then desirability functions are fit to the observations for each variable within a study (e.g., <italic>P</italic>-value, odds ratio, fold change, etc.) according to whether low values are most desirable (<italic>d</italic><sub><italic>low</italic></sub>, e.g., <italic>P</italic>-value), high values are most desirable (<italic>d</italic><sub><italic>high</italic></sub>, e.g., odds ratio), or extreme values are most desirable (<italic>d</italic><sub><italic>extreme</italic></sub>, e.g., fold change) (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, step 2). The desirability score for each genetic element can be calculated by applying one of the following equations to a given variable:<fig id="Fig2"><label>Fig. 2</label><caption><p>integRATE relies on three main steps to identify studies, integrate data, and rank candidate genes. (1) Relevant studies must first be identified for integration based on any number of features including, but not limited to: phenotype, experimental design, and data availability. (2) Data corresponding to all variables in each study are then transformed according to the appropriate desirability function. In this step, the user assigns a function based on whether low values are most desirable (<italic>d</italic><sub><italic>low</italic></sub>), high values are most desirable (<italic>d</italic><sub><italic>high</italic></sub>), or extreme values are most desirable (<italic>d</italic><sub><italic>extreme</italic></sub>) and can customize the shape of the function with other variables like cut points (<italic>A</italic>, <italic>B</italic>, <italic>C</italic>), scales (<italic>s</italic>), and weights (<italic>w</italic>) to better reflect the data distributions or to align with user opinion regarding data quality and relevance. (3) These variable-based scores are integrated (<italic>d</italic><sub><italic>study</italic></sub>) with a straightforward arithmetic mean (where weights can also be applied) to produce a single desirability score for each gene in each study containing information from all variables simultaneously. (4) Finally, study-based desirability scores are integrated to produce a single desirability score for each gene (<italic>d</italic><sub><italic>overall</italic></sub>) that includes information from all variables in all studies and reflects its cumulative weight of evidence from each data set identified in step 1. These scores are normalized by the number of studies containing data for each gene and can be used to rank and prioritize candidate genes for follow up computational and, most importantly, functional analyses</p></caption><graphic xlink:href="12920_2018_426_Fig2_HTML" id="MO2"/></fig></p><p id="Par19">
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{low}=\left\{\begin{array}{c}0\\ {}{\left[\frac{Y-B}{A-B}\right]}^s\\ {}1\end{array}\right.\left|\begin{array}{c}Y&#x0003e;B\\ {}A\le Y\le B\\ {}Y&#x0003c;A\end{array}\right. $$\end{document}</tex-math><mml:math id="M2" display="block"><mml:maligngroup/><mml:msub><mml:mi>d</mml:mi><mml:mi mathvariant="italic">low</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>0</mml:mn></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:msup><mml:mfenced close="]" open="["><mml:mfrac><mml:mrow><mml:mi>Y</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>B</mml:mi></mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>B</mml:mi></mml:mrow></mml:mfrac></mml:mfenced><mml:mi>s</mml:mi></mml:msup></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>1</mml:mn></mml:mtd></mml:mtr></mml:mtable></mml:mfenced><mml:mfenced close="" open="|"><mml:mtable><mml:mtr><mml:mtd><mml:mi>Y</mml:mi><mml:mo>&#x0003e;</mml:mo><mml:mi>B</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mi>A</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>Y</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>B</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mi>Y</mml:mi><mml:mo>&#x0003c;</mml:mo><mml:mi>A</mml:mi></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12920_2018_426_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula>
<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{high}=\left\{\begin{array}{c}0\\ {}{\left[\frac{Y-A}{B-A}\right]}^s\\ {}1\end{array}\right.\left|\begin{array}{c}Y&#x0003c;A\\ {}A\le Y\le B\\ {}Y&#x0003e;B\end{array}\right. $$\end{document}</tex-math><mml:math id="M4" display="block"><mml:mtable displaystyle="true"><mml:mtr><mml:mtd><mml:maligngroup/><mml:msub><mml:mi>d</mml:mi><mml:mtext mathvariant="italic">high</mml:mtext></mml:msub><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>0</mml:mn></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:msup><mml:mfenced close="]" open="["><mml:mfrac><mml:mrow><mml:mi>Y</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>A</mml:mi></mml:mrow><mml:mrow><mml:mi>B</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mfrac></mml:mfenced><mml:mi>s</mml:mi></mml:msup></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>1</mml:mn></mml:mtd></mml:mtr></mml:mtable></mml:mfenced><mml:mfenced close="" open="|"><mml:mtable><mml:mtr><mml:mtd><mml:mi>Y</mml:mi><mml:mo>&#x0003c;</mml:mo><mml:mi>A</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mi>A</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>Y</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>B</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mi>Y</mml:mi><mml:mo>&#x0003e;</mml:mo><mml:mi>B</mml:mi></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12920_2018_426_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula>
<disp-formula id="Equ3"><label>3</label><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{extreme}=\left\{\begin{array}{c}{\left[\frac{Y-A}{C-A}\right]}^s\\ {}{\left[\frac{Y-B}{C-B}\right]}^s\\ {}0\end{array}\right.\left|\begin{array}{c}A\le Y\le C\\ {}C\le Y\le B\\ {} else\end{array}\right. $$\end{document}</tex-math><mml:math id="M6" display="block"><mml:mtable displaystyle="true"><mml:mtr><mml:mtd><mml:maligngroup/><mml:msub><mml:mi>d</mml:mi><mml:mtext mathvariant="italic">extreme</mml:mtext></mml:msub><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:msup><mml:mfenced close="]" open="["><mml:mfrac><mml:mrow><mml:mi>Y</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>A</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mfrac></mml:mfenced><mml:mi>s</mml:mi></mml:msup></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:msup><mml:mfenced close="]" open="["><mml:mfrac><mml:mrow><mml:mi>Y</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>B</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>B</mml:mi></mml:mrow></mml:mfrac></mml:mfenced><mml:mi>s</mml:mi></mml:msup></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>0</mml:mn></mml:mtd></mml:mtr></mml:mtable></mml:mfenced><mml:mfenced close="" open="|"><mml:mtable><mml:mtr><mml:mtd><mml:mi>A</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>Y</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>C</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mi>C</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>Y</mml:mi><mml:mo>&#x02264;</mml:mo><mml:mi>B</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mtext mathvariant="italic">else</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12920_2018_426_Article_Equ3.gif" position="anchor"/></alternatives></disp-formula>
</p><p id="Par20">In these equations, <italic>Y</italic> is the variable value and <italic>s</italic> is the scale coefficient affecting the function&#x02019;s rate of change that can be customized according to user preference. Alternatively, the equations could be used without any scaling by setting the scale coefficient to 1. For <italic>d</italic><sub><italic>low</italic></sub> and <italic>d</italic><sub><italic>high</italic></sub>, <italic>A</italic> is the low cut point and <italic>B</italic> is the high cut point where the function changes. For <italic>d</italic><sub><italic>extreme</italic></sub>, <italic>A</italic> is the low cut point, <italic>C</italic> is the intermediate cut point, and <italic>B</italic> is the high cut point where the function changes. The user can customize these cut points based on numerical values (e.g., <italic>P</italic>-value &#x0003c;&#x02009;0.05) or percentile values (e.g., top 10%). The resulting values, ranging from 0 to 1 (or the minimum and maximum values specified) are transformed desirability scores based on information from each variable.</p></sec><sec id="Sec4"><title>Variable integration</title><p id="Par21">Next, desirability scores for each of the <italic>N</italic> variables within a study (e.g., <italic>P</italic>-value, odds ratio, fold change, etc.) are combined using an arithmetic mean so that genetic elements (e.g., genes) with desirability scores of zero for any given variable remain in the analysis (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, step 3). Desirability for genetic elements within a study can be calculated by:</p><p id="Par22">
<disp-formula id="Equ4"><label>4</label><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{study}=\sum \limits_{i=1}^N\frac{w_i{d}_i}{N} $$\end{document}</tex-math><mml:math id="M8" display="block"><mml:mtable displaystyle="true"><mml:mtr><mml:mtd><mml:maligngroup/><mml:msub><mml:mi>d</mml:mi><mml:mtext mathvariant="italic">study</mml:mtext></mml:msub><mml:mo>=</mml:mo><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>N</mml:mi></mml:munderover><mml:mfrac><mml:mrow><mml:msub><mml:mi>w</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mi>N</mml:mi></mml:mfrac></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12920_2018_426_Article_Equ4.gif" position="anchor"/></alternatives></disp-formula>
</p><p id="Par23">In this equation, <italic>w</italic><sub><italic>i</italic></sub> is the weight parameter (assigned to each variable), <italic>d</italic><sub><italic>i</italic></sub> is desirability score for each genetic element based on the values of each variable derived from Eqs. (<xref rid="Equ1" ref-type="">1</xref>), (<xref rid="Equ2" ref-type="">2</xref>) or (<xref rid="Equ3" ref-type="">3</xref>), and <italic>N</italic> is the total number of transformed variables. This step produces a single desirability score (<italic>d</italic><sub><italic>study</italic></sub>) for each genetic element in the study containing information from all transformed variables. Here, the user is also able to include variable weights (<italic>w</italic><sub><italic>i</italic></sub>) when integrating their desirability scores, which can be useful in cases where certain variables are considered more informative or accurate than others.</p></sec><sec id="Sec5"><title>Study integration</title><p id="Par24">Finally, the <italic>d</italic><sub><italic>study</italic></sub> values can be integrated using the arithmetic mean to produce a single desirability score (<italic>d</italic><sub><italic>overall</italic></sub>) for each genetic element, representing its desirability as a candidate according to the weight of evidence from all variables in all <italic>K</italic> studies that were integrated (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, step 4). The overall score used to prioritize candidates can be calculated by:</p><p id="Par25">
<disp-formula id="Equ5"><label>5</label><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{overall}=\sum \limits_{j=1}^K\frac{w_j{d}_{study\ j}}{K\left( no. studies\ missing\ data+1\right)} $$\end{document}</tex-math><mml:math id="M10" display="block"><mml:mtable displaystyle="true"><mml:mtr><mml:mtd><mml:maligngroup/><mml:msub><mml:mi>d</mml:mi><mml:mtext mathvariant="italic">overall</mml:mtext></mml:msub><mml:mo>=</mml:mo><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>K</mml:mi></mml:munderover><mml:mfrac><mml:mrow><mml:msub><mml:mi>w</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mtext mathvariant="italic">study</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mi>K</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="italic">no</mml:mi><mml:mo>.</mml:mo><mml:mtext mathvariant="italic">studies missing data</mml:mtext><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12920_2018_426_Article_Equ5.gif" position="anchor"/></alternatives></disp-formula>
</p><p id="Par26">In this equation, <italic>w</italic><sub><italic>j</italic></sub> is the weight parameter (assigned to each study), <italic>d</italic><sub><italic>study j</italic></sub> is the desirability score for each study, and <italic>K</italic> is the total number of studies integrated. Importantly, the overall desirability score <italic>d</italic><sub><italic>overall</italic></sub> is normalized by the number of studies missing data for each genetic element to account for the number of values contributing to each overall desirability score. This normalization factor can be used to calculate a soft cutoff for the most desirable candidates that is equivalent or higher than the desirability score that would be achieved by a genetic element with a perfect desirability score of 1 in a single study but missing from all other studies. We call genetic elements achieving desirability scores equal to or above this cutoff &#x02018;desirable.&#x02019;</p></sec><sec id="Sec6"><title>Software</title><p id="Par27">The methodology described above is implemented in our software, integRATE, available on GitHub as an R package (<ext-link ext-link-type="uri" xlink:href="https://github.com/haleyeidem/integRATE">https://github.com/haleyeidem/integRATE</ext-link>). Although we focus on using desirability functions to integrate heterogeneous omics data corresponding to complex human diseases, integRATE can be applied to data sets from any phenotype, species, and data type (provided that the units can all be mapped to a common set of elements, such as genes). Functionality is provided for the application of customizable desirability functions as well as data visualization.</p></sec><sec id="Sec7"><title>Implementation</title><p id="Par28">One human complex genetic disease where the omics data available are heterogeneous is preterm birth (PTB). Defined as birth before 37&#x02009;weeks of completed gestation, PTB is the leading cause of newborn death worldwide [<xref ref-type="bibr" rid="CR28">28</xref>]. Although 30% of preterm births are medically indicated due to complications including preeclampsia (PE) or intrauterine growth restriction (IUGR), the remaining 70% occur spontaneously either due to the preterm premature rupture of membranes (PPROM) or idiopathically (sPTB). Further complicating factors are that multiple maternal and fetal tissues are involved (e.g., placenta, fetal membranes, umbilical cord, myometrium, decidua, etc.) as well as multiple genomes (maternal, paternal, and fetal) [<xref ref-type="bibr" rid="CR29">29</xref>]. Evidence from family, twin, and case-control studies suggests that genetics plays a role in determining birth timing and a recent GWAS identified a handful of genes linked to prematurity [<xref ref-type="bibr" rid="CR30">30</xref>]. Nevertheless, the pathogenesis of PTB and its many subtypes remains poorly understood [<xref ref-type="bibr" rid="CR31">31</xref>&#x02013;<xref ref-type="bibr" rid="CR33">33</xref>].</p><p id="Par29">The publicly available data for sPTB consist of several different independently conducted omics analyses that would be challenging to analyze with statistical approaches developed for vertical and horizontal integration [<xref ref-type="bibr" rid="CR30">30</xref>, <xref ref-type="bibr" rid="CR34">34</xref>, <xref ref-type="bibr" rid="CR35">35</xref>]. Although these omics data have been analyzed in isolation, integration of their information using the desirability-based platform implemented in integRATE may provide unique insights into the complex mechanisms involved in regulating birth timing and, thus, allow for the identification and prioritization of novel candidate genes for further functional and targeted analyses.</p></sec><sec id="Sec8"><title>Study identification</title><p id="Par30">Studies were initially identified based on the PubMed searches (up to 10/19/2017) using combinations of terms, including &#x0201c;Pregnancy&#x0201d;, &#x0201c;Humans&#x0201d;, &#x0201c;Preterm birth&#x0201d;, &#x0201c;Placenta&#x0201d;, &#x0201c;Decidua&#x0201d;, &#x0201c;Myometrium&#x0201d;, &#x0201c;Cervix Uteri&#x0201d;, &#x0201c;Extraembryonic Membranes&#x0201d;, &#x0201c;Blood&#x0201d;, &#x0201c;Plasma&#x0201d; and &#x0201c;Umbilical Cord&#x0201d;. Studies that reported conducting a genome-wide omics analysis of sPTB from a preliminary scan of the abstract were downloaded for full-text assessment. Furthermore, a thorough investigation was conducted of their associated reference lists to identify studies not captured via PubMed. Additionally, each study had to meet the following inclusion criteria:<list list-type="order"><list-item><p id="Par31">Experimental group consisted of sPTB cases only and was not confounded by other pregnancy phenotypes (e.g., preeclampsia),</p></list-item><list-item><p id="Par32">Analysis was genome-wide and not targeted to any specific subset of genes or pathways, and</p></list-item><list-item><p id="Par33">Full data set was publicly available (not just top n%).</p></list-item></list></p><p id="Par34">We identified 54 studies through the first phase of our literature search, but only 10 data sets that met all inclusion criteria. All excluded studies are listed in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref> with reasons for exclusion and the 10 data sets used in our pilot analysis are outlined in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> [<xref ref-type="bibr" rid="CR30">30</xref>, <xref ref-type="bibr" rid="CR34">34</xref>&#x02013;<xref ref-type="bibr" rid="CR46">46</xref>].<table-wrap id="Tab1"><label>Table 1</label><caption><p>The 10 sPTB omics data sets identified for desirability-based integration</p></caption><table frame="hsides" rules="groups"><thead><tr><th>First author</th><th>Year</th><th>Experiment</th><th>Control</th><th>Tissue</th><th>omics Type</th></tr></thead><tbody><tr><td>Zhang</td><td>2017</td><td>sPTB</td><td>term</td><td>maternal blood</td><td>genomics (GWAS)</td></tr><tr><td>Ackerman</td><td>2015</td><td>sPTB</td><td>term</td><td>placenta</td><td>transcriptomics (RNA-seq)</td></tr><tr><td>Heng</td><td>2014</td><td>sPTB</td><td>term</td><td>maternal blood</td><td>transcriptomics (microarray)</td></tr><tr><td>Chim</td><td>2012</td><td>sPTB</td><td>term</td><td>maternal blood</td><td>transcriptomics (microarray)</td></tr><tr><td>Mayor-Lynn</td><td>2011</td><td>sPTB</td><td>term</td><td>placenta</td><td>transcriptomics (microarray)</td></tr><tr><td>de Goede</td><td>2017</td><td>sPTB</td><td>term</td><td>cord blood</td><td>epigenomics (microarray)</td></tr><tr><td>Fernando</td><td>2015</td><td>sPTB</td><td>term</td><td>cord blood</td><td>epigenomics (microarray)</td></tr><tr><td>Parets</td><td>2015</td><td>sPTB</td><td>term</td><td>maternal blood</td><td>epigenomics (microarray)</td></tr><tr><td>Cruickshank</td><td>2013</td><td>sPTB</td><td>term</td><td>fetal blood</td><td>epigenomics (microarray)</td></tr><tr><td>Heng</td><td>2015</td><td>sPTB</td><td>term</td><td>maternal blood</td><td>proteomics (mass spec)</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec9"><title>Data transformation</title><p id="Par35">Each of the 10 data sets was mapped to a gene-based format. This step was necessary because integRATE applies desirability functions both within and across studies and, in order for that integration to be possible, the genetic elements of each study have to match.</p><sec id="Sec10"><title>Genomics</title><p id="Par36">SNP-based data containing <italic>P</italic>-values and effect sizes were mapped to genes with MAGMA, as outlined in the Zhang et al. supplementary methods (<ext-link ext-link-type="uri" xlink:href="http://ctg.cncr.nl/software/magma)">http://ctg.cncr.nl/software/magma</ext-link>) [<xref ref-type="bibr" rid="CR39">39</xref>, <xref ref-type="bibr" rid="CR47">47</xref>, <xref ref-type="bibr" rid="CR48">48</xref>].</p></sec><sec id="Sec11"><title>Transcriptomics</title><p id="Par37">Gene expression data from microarray experiments were accessed via GEO (<ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/geo/)">https://www.ncbi.nlm.nih.gov/geo/</ext-link>) and re-analyzed using the GEO2R plugin (<ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/geo/info/geo2r.html)">https://www.ncbi.nlm.nih.gov/geo/info/geo2r.html</ext-link>) [<xref ref-type="bibr" rid="CR40">40</xref>&#x02013;<xref ref-type="bibr" rid="CR43">43</xref>]. Raw RNA-seq data from Ackerman et al. were analyzed in-house with custom scripts [<xref ref-type="bibr" rid="CR34">34</xref>].</p></sec><sec id="Sec12"><title>Epigenomics</title><p id="Par38">Methylation data were accessed via GEO (<ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/geo/)">https://www.ncbi.nlm.nih.gov/geo/</ext-link>) and re-analyzed using the GEO2R plugin (<ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/geo/info/geo2r.html)">https://www.ncbi.nlm.nih.gov/geo/info/geo2r.html</ext-link>) [<xref ref-type="bibr" rid="CR36">36</xref>, <xref ref-type="bibr" rid="CR37">37</xref>, <xref ref-type="bibr" rid="CR44">44</xref>&#x02013;<xref ref-type="bibr" rid="CR46">46</xref>].</p></sec><sec id="Sec13"><title>Proteomics</title><p id="Par39">Protein expression data were downloaded from supplementary files associated with each publication and the protein IDs were mapped to genes using Ensemble&#x02019;s BioMart tool (<ext-link ext-link-type="uri" xlink:href="https://www.ensembl.org/info/data/biomart/index.html)">https://www.ensembl.org/info/data/biomart/index.html</ext-link>) [<xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR38">38</xref>].</p></sec></sec><sec id="Sec14"><title>Application of integRATE</title><p id="Par40">After mapping results from all 10 omics studies to genes, we used integRATE to calculate desirabilities for all genes across all variables within studies. We ran four different sPTB analyses:<list list-type="order"><list-item><p id="Par41">In the first analysis (<bold>iR-none</bold>), we ran integRATE with no added customizations (e.g., no cut points, no scales (i.e., scale coefficient&#x02009;=&#x02009;1), no minimum or maximum desirabilities, etc.) (Figs.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>, <xref rid="Fig4" ref-type="fig">4</xref> and <xref rid="Fig5" ref-type="fig">5</xref>, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>).</p></list-item><list-item><p id="Par42">In the second analysis (<bold>iR-num</bold>), we ran integRATE using numerical cut points (<italic>P</italic>&#x02009;=&#x02009;0.0001, 0.1 and fold change&#x02009;=&#x02009;1.5, 0.5, &#x02212;&#x02009;0.5, &#x02212;&#x02009;1.5) and no scales (Additional&#x000a0;files&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>, <xref rid="MOESM4" ref-type="media">4</xref>, <xref rid="MOESM5" ref-type="media">5</xref>, <xref rid="MOESM6" ref-type="media">6</xref>).</p></list-item><list-item><p id="Par43">In the third analysis (<bold>iR-per</bold>), we ran integRATE using percentile cut points (<italic>P</italic>&#x02009;=&#x02009;5, 95%, and fold change&#x02009;=&#x02009;5, 50, 95%) and no scales (Additional&#x000a0;files&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>, <xref rid="MOESM8" ref-type="media">8</xref>, <xref rid="MOESM9" ref-type="media">9</xref>, <xref rid="MOESM10" ref-type="media">10</xref>).</p></list-item><list-item><p id="Par44">In the fourth analysis (<bold>HardThresh</bold>), we considered statistically significant genes from each study to represent the results that would have been obtained if the typical approach based on hard thresholds and intersection of significant genes across studies outlined earlier was applied (Additional&#x000a0;files&#x000a0;<xref rid="MOESM11" ref-type="media">11</xref>, <xref rid="MOESM12" ref-type="media">12</xref>). All genes with adjusted <italic>P</italic>-values &#x0003c;&#x02009;0.1 or unadjusted <italic>P</italic>-values &#x0003c;&#x02009;0.05 were deemed significant in each study and intersected to compare with the results from integRATE [<xref ref-type="bibr" rid="CR49">49</xref>].</p></list-item></list><fig id="Fig3"><label>Fig. 3</label><caption><p>After integration, 7977/26,868 genes were identified as highly desirable. All genes in the iR-none analysis were sorted from most desirable (rank&#x02009;=&#x02009;1) to least desirable (rank&#x02009;=&#x02009;26,868) and plotted according to their overall desirability scores, ranging from 8.04E-16 to 0.46. Because this analysis included 10 omics studies, the normalized lower bound for our set of &#x02018;desirable&#x02019; candidate genes is 0.1 (blue line) and 7977 genes achieved scores greater than or equal to that value. Furthermore, the results of our permutation test are plotted in pink, with a mean of 0.059 (95% CI [0.058, 0.061]). All desirability scores for the entire data set are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref> (and in Additional&#x000a0;files&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref> and <xref rid="MOESM7" ref-type="media">7</xref> for iR-num and iR-per, respectively)</p></caption><graphic xlink:href="12920_2018_426_Fig3_HTML" id="MO3"/></fig><fig id="Fig4"><label>Fig. 4</label><caption><p>The top 10 most desirable genes have a wide range of desirabilities across data types. The top 10 genes from our iR-none analysis have overall desirabilities ranging from 0.38 (<italic>ACTN1</italic>) to 0.46 (<italic>CAPZB</italic>), but the <italic>d</italic><sub><italic>study</italic></sub> values range, even when organized by data type. Some genes, like <italic>STOM</italic>, appear to be highly ranked not because of any extremely high <italic>d</italic><sub><italic>study</italic></sub> value, but rather due to a lack of low <italic>d</italic><sub><italic>study</italic></sub> values in any data type. In other words, this gene is likely not identified as particularly important in any individual study but shows a consensus of relatively strong evidence across all 10 studies. Contrastingly, other genes, like <italic>CAPZB</italic>, appear to be highly ranked due to one very high desirability score in a single data type (GWAS) that overpowers underwhelming evidence in other studies</p></caption><graphic xlink:href="12920_2018_426_Fig4_HTML" id="MO4"/></fig><fig id="Fig5"><label>Fig. 5</label><caption><p>The top 10 most desirable genes show a large discrepancy in their percentile ranks across studies. After ranking the genes in each study by desirability (using the iR-none analysis) and calculating their percentiles based on the number of unique ranks, the top 10 most desirable genes appear to show even greater variability in relative ranking across not just data type, but individual studies. All 10 genes are in the top 25% of the (smaller) proteomics study, but their relative rankings vary significantly in all other studies. Furthermore, while none of the genes are in the top 25% of the GWAS study [<xref ref-type="bibr" rid="CR30">30</xref>], other studies, like one of the transcriptomics analyses [<xref ref-type="bibr" rid="CR43">43</xref>], show a large range in relative rankings, with certain highly desirable genes ranked very high and others ranked very low</p></caption><graphic xlink:href="12920_2018_426_Fig5_HTML" id="MO5"/></fig></p><p id="Par45">To test whether the analyses described above produced results different from what might occur at random, we performed a permutation test shuffling desirabilities for all 26,868 genes 1000 times.</p></sec></sec><sec id="Sec15"><title>Results</title><p id="Par46">In total, our sPTB analyses integrated gene-based results from 10 omics studies (1 genomics, 4 transcriptomics, 4 epigenomics, and 1 proteomics; Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>) and included data sets ranging from 422 genes [<xref ref-type="bibr" rid="CR35">35</xref>] to 20,841 genes [<xref ref-type="bibr" rid="CR42">42</xref>]. The null distribution generated by our random permutation test had mean desirability range from 0.056 to 0.062, with an average of 0.059 (95% CI [0.058, 0.061]) (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>).</p><sec id="Sec16"><title>iR-none</title><p id="Par47">First, the software was run without any added cuts, weights, or scales, resulting in a list of 26,868 genes with data from one or more of the 10 omics studies (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). Normalized desirabilities for these 26,868 genes ranged from 8.04E-16 to 0.46 (mean&#x02009;=&#x02009;0.08&#x02009;&#x000b1;&#x02009;0.05) (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>). Furthermore, 7977 genes (29.7%) had desirabilities &#x02265;0.1 corresponding to values equal to or higher than what would be achieved if a given gene achieved maximal desirability in one study but was absent from all others. These top 7977 genes were enriched for 70 unique GO-Slim Biological Process categories, including pathways involved in metabolic processes, immunity, and signal transduction (Additional&#x000a0;file&#x000a0;<xref rid="MOESM13" ref-type="media">13</xref>) [<xref ref-type="bibr" rid="CR50">50</xref>]. Additionally, 15,285/26,868 (56.9%) genes achieved desirabilities greater than the permutation mean of 0.059. The top 10 genes (Figs.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> and <xref rid="Fig5" ref-type="fig">5</xref>) had desirabilities ranging from 0.46 (<italic>CAPZB</italic>) to 0.38 (<italic>ACTN1</italic>) and were all represented in each of the 10 omics data sets analyzed. This analysis applied integRATE without cut points, allowing for a straightforward, linear transformation of data across all variables and studies.</p></sec><sec id="Sec17"><title>iR-num</title><p id="Par48">We next applied cut points based on numerical values (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>). <italic>P</italic>-values such that values smaller than 0.0001 received the maximum desirability score of 1 and values larger than 0.1 received the minimum desirability score of 0. All <italic>P</italic>-values between 0.0001 and 0.1 were transformed according to the <italic>d</italic><sub><italic>low</italic></sub> function. For <italic>d</italic><sub><italic>extreme</italic></sub> functions, 4 cut points were assigned and we chose commonly used values of 0.5 and 1.5 (or their equivalents if the values were log transformed). Therefore, fold changes below &#x02212;&#x02009;1.5 or above &#x02212;&#x02009;1.5 (or below log2(1/3) or above log2(3)) received the maximum desirability score of 1 and fold changes between &#x02212;&#x02009;0.5 and 0.5 (or between log2(1/1.5) and log2(1.5)) received the minimum desirability score of 0. Intermediate values were transformed according to the <italic>d</italic><sub><italic>extreme</italic></sub> function. This approach mirrors what was applied in a previous implementation of the desirability framework for omics data, and takes into account prior knowledge of typical <italic>P</italic>-value and fold change distributions [<xref ref-type="bibr" rid="CR23">23</xref>]. While the top most desirable genes in iR-num appeared to be better candidates in each individual study (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>), using these cut points corresponding to standard significant <italic>P</italic>-value and fold change cut offs greatly reduced the number of desirable genes identified (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>). Specifically, only 1386/26,868 (5.1%) genes achieved desirabilities greater than the permutation mean of 0.059 and the top 10 most desirable genes were analyzed by only 4 or 5 studies instead of all 10 (Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>).</p></sec><sec id="Sec18"><title>iR-per</title><p id="Par49">Finally, we applied cut points based on percentiles (Additional&#x000a0;file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>). <italic>P</italic>-values were cut such that those in the top 5% received the maximum desirability score of 1 and those in the bottom 5% received the minimum desirability score of 0, with all values in between transformed according to the <italic>d</italic><sub><italic>low</italic></sub> function. Fold changes were cut such that those in the top 5% and bottom 5% received the maximum desirability score of 1 and those in the middle 50% received the minimum desirability score of 0, with all other values transformed according to the <italic>d</italic><sub><italic>extreme</italic></sub> function. In this analysis, 16,604/26,868 (61.8%) genes achieved desirabilities greater than the permutation mean of 0.059.</p></sec><sec id="Sec19"><title>HardThresh</title><p id="Par50">For comparison, we also manually selected candidate genes by imposing a hard threshold on <italic>P</italic>-value (<italic>P</italic>-value &#x0003c;&#x02009;0.05 if unadjusted and <italic>P</italic>-value &#x0003c;&#x02009;0.1 if adjusted) (Additional&#x000a0;file&#x000a0;<xref rid="MOESM11" ref-type="media">11</xref>). After binning data into &#x02018;significant&#x02019; gene lists, we intersected these lists to pull out genes that would have been identified simply by selecting the intersection of all significant genes. Although 18,727 genes were considered &#x02018;significant&#x02019; in at least one study, no genes were identified as significant in all 10 studies. The top candidate gene (<italic>KIAA0040</italic>) was significant in 6/10 studies and 15 other genes were identified in 5/10 studies (Additional&#x000a0;file&#x000a0;<xref rid="MOESM12" ref-type="media">12</xref>). Interestingly, none of these 16 genes appear in the top 10 of our most desirable candidates after integration and, even more generally, none are specifically discussed in any of the studies, either.</p></sec><sec id="Sec20"><title>Using integRATE to identify the most desirable sPTB genes</title><p id="Par51">In our sPTB pilot analyses, members of the annexin family (<italic>ANXA3</italic>, <italic>ANXA4</italic> and <italic>ANXA9</italic>) appear in the top 10 most desirable candidate gene sets regardless of analysis approach (e.g., without cut points as well as with numerical <italic>and</italic> percentile cut points). This family is involved in calcium-dependent phospholipid binding and membrane-related exocytotic and endocytotic events, including endosome aggregation mediation (<italic>ANXA6</italic>). In a previous proteomic analysis, <italic>ANXA3</italic> was found to be differentially expressed in cervicovaginal fluid 26&#x02013;30&#x02009;days before the eventual onset of sPTB as compared to before healthy, term deliveries [<xref ref-type="bibr" rid="CR51">51</xref>]. Furthermore, members of the annexin family are known to be involved in coagulation (<italic>ANXA3</italic>, <italic>ANXA4</italic>). Coagulation has been previously suggested to be involved in PTB and, even though the mechanism of such involvement is still a mystery, it is interesting that several genes involved in coagulation or blood disorders appear in our top candidate lists [<xref ref-type="bibr" rid="CR52">52</xref>]. In addition to <italic>ANXA3</italic> and <italic>ANXA4</italic>, <italic>VWF</italic> (or Von Willebrand Factor) is a gene encoding a glycoprotein involved in coagulation that has been found to be expressed significantly more in preterm infant serum as compared to term [<xref ref-type="bibr" rid="CR53">53</xref>, <xref ref-type="bibr" rid="CR54">54</xref>]. Finally, another highly desirable candidate, <italic>STOM</italic>, encodes an integral membrane protein that localizes to red blood cells, the loss of which has been linked to anemia [<xref ref-type="bibr" rid="CR55">55</xref>].</p><p id="Par52">In addition to coagulation, another biological process represented across our results is actin regulation and muscle activity. The most notable gene associated with this biological process is <italic>CAPZB</italic>, which encodes part of an actin binding protein that regulates actin filament dynamics and stabilization and is present in the top 10 most desirable candidate gene list in all three analyses. Although <italic>CAPZB</italic> has never been linked to sPTB or other pregnancy pathologies, its role in muscle function could be linked to myometrial and uterine contractions that, when they occur prematurely, might be directly involved in the development of sPTB [<xref ref-type="bibr" rid="CR56">56</xref>, <xref ref-type="bibr" rid="CR57">57</xref>]. Another one of our top candidates, <italic>ACTN1</italic>, is also involved in actin regulation and, even more interestingly, has also been linked to blood and bleeding disorders [<xref ref-type="bibr" rid="CR58">58</xref>, <xref ref-type="bibr" rid="CR59">59</xref>]. Finally, several other highly desirable genes identified in one or more of our integrative analyses include <italic>GPSM3</italic>, <italic>WDR1</italic>, and <italic>DYSF</italic>, are all involved in the development and regulation of muscle or in the pathogenesis of muscle-related diseases [<xref ref-type="bibr" rid="CR60">60</xref>&#x02013;<xref ref-type="bibr" rid="CR62">62</xref>].</p><p id="Par53">Even outside the top 10 most desirable genes across our integrative analyses, we found genes both previously identified as being involved in pregnancy or sPTB pathology as well as involved in pathways potentially relevant to sPTB (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). For example, one gene falling just outside the top 10 most desirable candidates in all analyses is <italic>MMP9</italic>, a matrix metalloproteinase. Interestingly, <italic>MMP9</italic> has been linked not only to sPTB, but also to PPROM and PE across a number of fetal and maternal tissues and at a variety of time points during pregnancy [<xref ref-type="bibr" rid="CR63">63</xref>&#x02013;<xref ref-type="bibr" rid="CR67">67</xref>]. <italic>MMP9</italic> gene expression has been observed as significantly higher during preterm labor than during term labor in maternal serum, placenta, and fetal membranes [<xref ref-type="bibr" rid="CR68">68</xref>&#x02013;<xref ref-type="bibr" rid="CR70">70</xref>]. Even in the first trimester, levels of <italic>MMP9</italic> in maternal serum were higher in PE cases than in healthy controls, suggesting that increased <italic>MMP9</italic> protein expression is linked to the underlying inflammatory processes governing PE pathogenesis [<xref ref-type="bibr" rid="CR66">66</xref>]. Finally, fetal plasma <italic>MMP9</italic> concentration has been found to be significantly higher in fetuses with PPROM than in early and term deliveries with intact membranes, implicating <italic>MMP9</italic> in the membrane rupture mechanism controlling early delivery due to membrane rupture [<xref ref-type="bibr" rid="CR67">67</xref>]. We see similar evidence of <italic>MMP9</italic> as a desirable sPTB candidate maintained across omics and tissue types in our integRATE analyses, raising the hypothesis that its role in inflammation and extracellular matrix organization relates to sPTB even in the absence of PPROM or PE.</p></sec></sec><sec id="Sec21"><title>Discussion</title><p id="Par54">By using desirability functions to rank genes within studies and combine results across studies, integRATE allows for the identification of candidate genes supported across experimental conditions and omics data types. This is especially important when heterogeneous sets of omics data, like those available for sPTB, where the statistical approaches developed for vertical or horizontal integration are challenging to apply. We have shown that integRATE can map any omics data to a common [0, 1] scale for linear integration and produce a list of the most desirable candidates according to their weight of evidence across available studies. These candidates then become promising targets for follow-up functional testing depending on where in the data their desirability signals come from. Analysis of 10 heterogeneous omics data sets on sPTB showed that the gene candidates identified using desirability functions appear to be much more broadly supported than those identified by the intersection of all significant genes across all studies and contain both genes that have been previously associated with sPTB as well as novel ones (Figs.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> and <xref rid="Fig5" ref-type="fig">5</xref>, Additional&#x000a0;file&#x000a0;<xref rid="MOESM12" ref-type="media">12</xref>).</p><p id="Par55">integRATE identifies both known and novel candidate genes associated with a complex disease, including ones that are not among the top candidates in any single omics study but are consistently (i.e., across studies) recovered as significantly (or nearly significantly) associated. For example, genes that are significantly differentially expressed at an intermediate to high level across <italic>many</italic> studies will have high desirability scores. Furthermore, integRATE can identify such genes across omics types, tissues, patient groups, and any other variable condition. Although integRATE allows for this kind of synergistic, desirability-based analysis, it is important to note that integRATE is not a statistical tool nor is it intended to be the end point of any analysis. Rather, it is a straightforward framework for the identification of well-supported candidate genes in any phenotype where true multi-omics data are unavailable and can also serve as a springboard for future functional analysis, an essential next-step in testing whether the candidates are actually involved in the biology of the disease or phenotype at hand.</p><p id="Par56">In our analyses, the genomics data set was typically the one with the highest desirability scores for each of the top 10 genes (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>) and the proteomics data set was typically the one in which the relative rank of the top 10 genes was the highest (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>). Both of these trends may appear surprising considering that our analyses contained just one genomics and one proteomics data set compared to four transcriptomics and four epigenomics ones (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). There are three reasons for these two trends. First, there is substantial heterogeneity among the top genes identified by the four transcriptomic studies (see also [<xref ref-type="bibr" rid="CR71">71</xref>]), as well as among the top genes identified by the four epigenomic studies. As a consequence, there is no common signature of the four transcriptomic studies or the four epigenomic studies (see Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). Second, there are many more genes with high desirability scores in the genomics data set than in the other nine data sets (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). However, we note that the ranking of the top 10 genes is not driven by the genomics data set; as we discuss below (see last paragraph of the discussion section), only one of the top 10 genes (<italic>EBF1</italic>) is among the candidate genes identified to be significantly associated with preterm birth and gestation length in the genomics data set [<xref ref-type="bibr" rid="CR47">47</xref>]. Third, the number of differentially expressed proteins (mapped to genes) in the proteomics data set, and as a consequence the number of genes with desirability scores in this data set, was substantially lower than that of all other studies (and included hundreds of genes vs tens of thousands of genes). As a result, the percentile rank of the top 10 genes for the proteomics data set (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>) was much higher than their percentile rank in other data sets. However, as shown in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>, the desirability scores of the top 10 genes in the proteomics data set were typically neither very high nor very low, and did not appear to exert a disproportionate influence on the ranking of our top 10 genes.</p><p id="Par57">Importantly, there is no single principled strategy for the selection of cut points. In our sPTB analyses (iR-none, iR-num, and iR-per), we observed that the imposition of cut points corresponding to generally agreed upon values (e.g., <italic>P</italic>-value &#x0003c;&#x02009;0.0001) has the potential to greatly affect the resulting gene prioritization. On this basis, we propose that desirability functions are best used to integrate highly heterogeneous omics data without imposed numerical cut points for <italic>P</italic>-values, fold changes, and other variables. Implemented this way, one can maximize the information from the analysis of each omics data set used in prioritizing candidate genes. But users may also have reasons to want to put more weight on data sets that are of higher quality or on data types that may be more informative. In such instances, the weight parameter can be used to reflect study quality instead of imposing cut points (e.g., studies that fail to achieve <italic>P</italic>-values as low as others in the integrative analysis can be weighted less to reflect potentially lower experimental quality).</p><p id="Par58">A recent GWAS analysis, the largest of its kind across pregnancy research, identified several candidate genes with SNPs linked to PTB [<xref ref-type="bibr" rid="CR47">47</xref>]. This study linked <italic>EBF1</italic>, <italic>EEFSEC</italic>, and <italic>AGTR2</italic> to preterm birth and <italic>EBF1</italic>, <italic>EEFSEC</italic>, <italic>AGTR2</italic>, and <italic>WNT4</italic> to gestational duration (with <italic>ADCY5</italic> and <italic>RAP2C</italic> linked suggestively). By analyzing 43,568 women of European ancestry, this large study is the first to identify variants and genes that are statistically associated with sPTB. Interestingly, our integrative analysis identified <italic>EBF1</italic> as a desirable candidate (<italic>d</italic><sub><italic>overall</italic></sub>&#x02009;=&#x02009;0.15 [top 3%] in iR-none and <italic>d</italic><sub><italic>overall</italic></sub>&#x02009;=&#x02009;0.23 [top 1%] in iR-per), suggesting that this gene, in addition to GWAS, might also be functionally linked to sPTB pathogenesis across transcriptomics, epigenomics, and proteomics studies. Even when analyzing the 9 other omics studies without this GWAS data set, <italic>EBF1</italic> still achieved a <italic>d</italic><sub><italic>overall</italic></sub> score of 0.17, placing it in the top 2% of all genes (Additional&#x000a0;file&#x000a0;<xref rid="MOESM14" ref-type="media">14</xref>). While our integrative analysis supports the identification of <italic>EBF1</italic> as an interesting candidate gene for follow up, the lack of signal for any of the other GWAS-identified hits also reinforces the need to approach complex phenotypes like sPTB from a variety of omics perspectives, since sequenced-based changes may impact the phenotype in indirect and complicated functional ways.</p></sec><sec id="Sec22"><title>Conclusions</title><p id="Par59">Desirability-based data integration (and our integRATE software) is a solution most applicable in biological research areas where omics data is especially heterogeneous and sparse. Our approach combines information from all variables across all related studies to calculate the total weight of evidence for any given gene as a candidate involved in disease pathogenesis, for example. Although not a statistical approach, this method of data integration allows for the prioritization of candidate genes based on information from heterogeneous omics data even without known &#x02018;gold standard&#x02019; genes to test against and can be used to inform more targeted downstream functional analyses.</p></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec23"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12920_2018_426_MOESM1_ESM.xlsx"><label>Additional file 1:</label><caption><p>Results of meta-analysis to identify studies for integration. We outline the 10 studies meeting all inclusion criteria for integrative analysis. Furthermore, we list the other 44 studies that we identified through our literature search but we excluded from the data analysis as well as reasons for their exclusion. (XLSX 42 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12920_2018_426_MOESM2_ESM.xlsx"><label>Additional file 2:</label><caption><p>All results from iR-none. All desirability scores across all variables in all studies as well as overall desirabilities and normalized overall desirabilities are presented. (XLSX 3762 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12920_2018_426_MOESM3_ESM.xlsx"><label>Additional file 3:</label><caption><p>All results from iR-num. All desirability scores across all variables in all studies as well as overall desirabilities and normalized overall desirabilities are presented. (XLSX 1988 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12920_2018_426_MOESM4_ESM.eps"><label>Additional file 4:</label><caption><p>Results from iR-num. All genes in the analysis including numerical cut points were sorted from most desirable (rank&#x02009;=&#x02009;1) to least desirable (rank&#x02009;=&#x02009;26,869) and plotted according to their overall desirability scores. (EPS 611 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12920_2018_426_MOESM5_ESM.eps"><label>Additional file 5:</label><caption><p>Top 10 genes from iR-num by data type. Desirability scores for the top 10 most desirable genes are plotted according to the type of omics analysis. (EPS 761 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="12920_2018_426_MOESM6_ESM.eps"><label>Additional file 6:</label><caption><p>Top 10 genes from iR-num by study. Desirability scores for the top 10 most desirable genes are plotted according to individual study. (EPS 784 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="12920_2018_426_MOESM7_ESM.xlsx"><label>Additional file 7:</label><caption><p>All results from iR-per. All desirability scores across all variables in all studies as well as overall desirabilities and normalized overall desirabilities are presented. (XLSX 3512 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="12920_2018_426_MOESM8_ESM.eps"><label>Additional file 8:</label><caption><p>Results from iR-per. All genes in the iR-per analysis were sorted from most desirable (rank&#x02009;=&#x02009;1) to least desirable (rank&#x02009;=&#x02009;26,869) and plotted according to their overall desirability scores. (EPS 618 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="12920_2018_426_MOESM9_ESM.eps"><label>Additional file 9:</label><caption><p>Top 10 genes from iR-per by data type. Desirability scores for the top 10 most desirable genes are plotted according to the type of omics analysis. (EPS 764 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM10"><media xlink:href="12920_2018_426_MOESM10_ESM.eps"><label>Additional file 10:</label><caption><p>Top 10 genes from iR-per by study. Desirability scores for the top 10 most desirable genes are plotted according to individual study. (EPS 789 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM11"><media xlink:href="12920_2018_426_MOESM11_ESM.xlsx"><label>Additional file 11:</label><caption><p>Raw data for manual overlap based on significance dichotomization. All 18,727 genes identified as significant in at least 1 study and overlap across the entire data set. (XLSX 769 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM12"><media xlink:href="12920_2018_426_MOESM12_ESM.eps"><label>Additional file 12:</label><caption><p>Genes binned as significant in 4 or more omics studies. Upset plot showing intersections of significant genes across all 10 omics studies. (EPS 12 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM13"><media xlink:href="12920_2018_426_MOESM13_ESM.xlsx"><label>Additional file 13:</label><caption><p>GO-Slim gene set enrichment results. The PANTHER output for gene set functional enrichment is provided, including 37 statistically enriched biological pathways. (XLSX 13 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM14"><media xlink:href="12920_2018_426_MOESM14_ESM.xlsx"><label>Additional file 14:</label><caption><p>All results <italic>without</italic> including the&#x000a0;Zhang 2017&#x000a0;data set [<xref ref-type="bibr" rid="CR30">30</xref>]. All desirability scores across all variables in all studies as well as overall desirabilities and normalized overall desirabilities are presented. (XLSX 2908 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>GWAS</term><def><p id="Par5">Genome-wide association study</p></def></def-item><def-item><term>IUGR</term><def><p id="Par6">Intrauterine growth restriction</p></def></def-item><def-item><term>PE</term><def><p id="Par7">Preeclampsia</p></def></def-item><def-item><term>PPROM</term><def><p id="Par8">Premature rupture of membranes</p></def></def-item><def-item><term>PTB</term><def><p id="Par9">Preterm birth</p></def></def-item><def-item><term>SNP</term><def><p id="Par10">Single nucleotide polymorphism</p></def></def-item><def-item><term>sPTB</term><def><p id="Par11">Spontaneous preterm birth</p></def></def-item></def-list></glossary><ack><title>Acknowledgements</title><p>We thank Dr. Lou Muglia for invaluable discussion and support in designing and applying this approach to data integration and Dr. Ge Zhang for providing access to preprocessed GWAS data.</p><sec id="FPar1"><title>Funding</title><p id="Par60">HRE was supported by a Transdisciplinary Scholar Award from the March of Dimes Prematurity Research Center Ohio Collaborative. This research was supported by the March of Dimes through the March of Dimes Prematurity Research Center Ohio Collaborative and the Burroughs Wellcome Fund. The funders had no role in study design, data collection and analysis, decision to publish, or preparation of the manuscript.</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p id="Par61">All the data associated with and supporting the findings of this study are included in the manuscript and its supplementary files.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>Conceived and designed experiments: HRE, AR. Performed experiments: HRE. Developed scripts: HRE, JS. Analyzed data: HRE. Wrote paper: HRE. Assisted with project development: AR, JHW, PA, JAC. Provided feedback: JHW, PA, JAC, AR. All authors have read and approved the manuscript.</p></notes><notes notes-type="COI-statement"><sec id="FPar3"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec id="FPar4"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="FPar5"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="FPar6"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gohlke</surname><given-names>JM</given-names></name><name><surname>Thomas</surname><given-names>R</given-names></name><name><surname>Zhang</surname><given-names>Y</given-names></name><name><surname>Rosenstein</surname><given-names>MC</given-names></name><name><surname>Davis</surname><given-names>AP</given-names></name><name><surname>Murphy</surname><given-names>C</given-names></name><etal/></person-group><article-title>Genetic and environmental pathways to complex diseases</article-title><source>BMC Syst Biol</source><year>2009</year><volume>3</volume><fpage>46</fpage><pub-id pub-id-type="doi">10.1186/1752-0509-3-46</pub-id><pub-id pub-id-type="pmid">19416532</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hasin</surname><given-names>Y</given-names></name><name><surname>Seldin</surname><given-names>M</given-names></name><name><surname>Lusis</surname><given-names>A</given-names></name></person-group><article-title>Multi-omics approaches to disease</article-title><source>Genome Biol</source><year>2017</year><volume>18</volume><fpage>83</fpage><pub-id pub-id-type="doi">10.1186/s13059-017-1215-1</pub-id><pub-id pub-id-type="pmid">28476144</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>R</given-names></name><name><surname>Mias</surname><given-names>GI</given-names></name><name><surname>Li-Pook-Than</surname><given-names>J</given-names></name><name><surname>Jiang</surname><given-names>L</given-names></name><name><surname>Lam</surname><given-names>HYK</given-names></name><name><surname>Chen</surname><given-names>R</given-names></name><etal/></person-group><article-title>Personal omics profiling reveals dynamic molecular and medical phenotypes</article-title><source>Cell</source><year>2012</year><volume>148</volume><fpage>1293</fpage><lpage>1307</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2012.02.009</pub-id><?supplied-pmid 22424236?><pub-id pub-id-type="pmid">22424236</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Karczewski</surname><given-names>KJ</given-names></name><name><surname>Snyder</surname><given-names>MP</given-names></name></person-group><article-title>Integrative omics for health and disease</article-title><source>Nat Rev Genet</source><year>2018</year><volume>19</volume><fpage>299</fpage><lpage>310</lpage><pub-id pub-id-type="doi">10.1038/nrg.2018.4</pub-id><?supplied-pmid 29479082?><pub-id pub-id-type="pmid">29479082</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edwards</surname><given-names>SL</given-names></name><name><surname>Beesley</surname><given-names>J</given-names></name><name><surname>French</surname><given-names>JD</given-names></name><name><surname>Dunning</surname><given-names>AM</given-names></name></person-group><article-title>Beyond GWASs: illuminating the dark road from association to function</article-title><source>Am J Hum Genet</source><year>2013</year><volume>93</volume><fpage>779</fpage><lpage>797</lpage><pub-id pub-id-type="doi">10.1016/j.ajhg.2013.10.012</pub-id><pub-id pub-id-type="pmid">24210251</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Visscher</surname><given-names>PM</given-names></name><name><surname>Wray</surname><given-names>NR</given-names></name><name><surname>Zhang</surname><given-names>Q</given-names></name><name><surname>Sklar</surname><given-names>P</given-names></name><name><surname>McCarthy</surname><given-names>MI</given-names></name><name><surname>Brown</surname><given-names>MA</given-names></name><etal/></person-group><article-title>10 years of GWAS discovery: biology, function, and translation</article-title><source>Am J Hum Genet</source><year>2017</year><volume>101</volume><fpage>5</fpage><lpage>22</lpage><pub-id pub-id-type="doi">10.1016/j.ajhg.2017.06.005</pub-id><pub-id pub-id-type="pmid">28686856</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Casamassimi</surname><given-names>A</given-names></name><name><surname>Federico</surname><given-names>A</given-names></name><name><surname>Rienzo</surname><given-names>M</given-names></name><name><surname>Esposito</surname><given-names>S</given-names></name><name><surname>Ciccodicola</surname><given-names>A</given-names></name></person-group><article-title>Transcriptome profiling in human diseases: new advances and perspectives</article-title><source>Int J Mol Sci</source><year>2017</year><volume>18</volume><fpage>1652</fpage><pub-id pub-id-type="doi">10.3390/ijms18081652</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ritchie</surname><given-names>MD</given-names></name><name><surname>Holzinger</surname><given-names>ER</given-names></name><name><surname>Li</surname><given-names>R</given-names></name><name><surname>Pendergrass</surname><given-names>SA</given-names></name><name><surname>Kim</surname><given-names>D</given-names></name></person-group><article-title>Methods of integrating data to uncover genotype&#x02013;phenotype interactions</article-title><source>Nat Rev Genet</source><year>2015</year><volume>16</volume><fpage>85</fpage><lpage>97</lpage><pub-id pub-id-type="doi">10.1038/nrg3868</pub-id><?supplied-pmid 25582081?><pub-id pub-id-type="pmid">25582081</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Holzinger</surname><given-names>ER</given-names></name><name><surname>Ritchie</surname><given-names>MD</given-names></name></person-group><article-title>Integrating heterogeneous high-throughput data for meta-dimensional pharmacogenomics and disease-related studies</article-title><source>Pharmacogenomics</source><year>2012</year><volume>13</volume><fpage>213</fpage><lpage>222</lpage><pub-id pub-id-type="doi">10.2217/pgs.11.145</pub-id><?supplied-pmid 22256870?><pub-id pub-id-type="pmid">22256870</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>D</given-names></name><name><surname>Shin</surname><given-names>H</given-names></name><name><surname>Song</surname><given-names>YS</given-names></name><name><surname>Kim</surname><given-names>JH</given-names></name></person-group><article-title>Synergistic effect of different levels of genomic data for cancer clinical outcome prediction</article-title><source>J Biomed Inform</source><year>2012</year><volume>45</volume><fpage>1191</fpage><lpage>1198</lpage><pub-id pub-id-type="doi">10.1016/j.jbi.2012.07.008</pub-id><?supplied-pmid 22910106?><pub-id pub-id-type="pmid">22910106</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Peng</surname><given-names>C</given-names></name><name><surname>Li</surname><given-names>A</given-names></name><name><surname>Wang</surname><given-names>M</given-names></name></person-group><article-title>Discovery of bladder cancer-related genes using integrative heterogeneous network modeling of multi-omics data</article-title><source>Sci Rep</source><year>2017</year><volume>7</volume><fpage>15639</fpage><pub-id pub-id-type="doi">10.1038/s41598-017-15890-9</pub-id><?supplied-pmid 29142286?><pub-id pub-id-type="pmid">29142286</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pavel</surname><given-names>AB</given-names></name><name><surname>Sonkin</surname><given-names>D</given-names></name><name><surname>Reddy</surname><given-names>A</given-names></name></person-group><article-title>Integrative modeling of multi-omics data to identify cancer drivers and infer patient-specific gene activity</article-title><source>BMC Syst Biol</source><year>2016</year><volume>10</volume><fpage>16</fpage><pub-id pub-id-type="doi">10.1186/s12918-016-0260-9</pub-id><?supplied-pmid 26864072?><pub-id pub-id-type="pmid">26864072</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>J</given-names></name><name><surname>Shi</surname><given-names>Z</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Zhang</surname><given-names>B</given-names></name></person-group><article-title>Empowering biologists with multi-omics data: colorectal cancer as a paradigm</article-title><source>Bioinformatics</source><year>2015</year><volume>31</volume><fpage>1436</fpage><lpage>1443</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btu834</pub-id><?supplied-pmid 25527095?><pub-id pub-id-type="pmid">25527095</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McLendon</surname><given-names>R</given-names></name><name><surname>Friedman</surname><given-names>A</given-names></name><name><surname>Bigner</surname><given-names>D</given-names></name><name><surname>Van Meir</surname><given-names>EG</given-names></name><name><surname>Brat</surname><given-names>DJ</given-names></name><name><surname>Mastrogianakis</surname><given-names>GM</given-names></name><etal/></person-group><article-title>Comprehensive genomic characterization defines human glioblastoma genes and core pathways</article-title><source>Nature</source><year>2008</year><volume>455</volume><fpage>1061</fpage><lpage>1068</lpage><pub-id pub-id-type="doi">10.1038/nature07385.</pub-id><pub-id pub-id-type="pmid">18772890</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Verhaak</surname><given-names>RGW</given-names></name><name><surname>Hoadley</surname><given-names>KA</given-names></name><name><surname>Purdom</surname><given-names>E</given-names></name><name><surname>Wang</surname><given-names>V</given-names></name><name><surname>Qi</surname><given-names>Y</given-names></name><name><surname>Wilkerson</surname><given-names>MD</given-names></name><etal/></person-group><article-title>Integrated genomic analysis identifies clinically relevant subtypes of glioblastoma characterized by abnormalities in PDGFRA, IDH1, EGFR, and NF1</article-title><source>Cancer Cell</source><year>2010</year><volume>17</volume><fpage>98</fpage><lpage>110</lpage><pub-id pub-id-type="doi">10.1016/j.ccr.2009.12.020</pub-id><?supplied-pmid 2818769?><pub-id pub-id-type="pmid">20129251</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cohen</surname><given-names>J</given-names></name></person-group><article-title>The cost of dichotomization</article-title><source>Appl Psychol Meas</source><year>1983</year><volume>7</volume><fpage>249</fpage><lpage>253</lpage><pub-id pub-id-type="doi">10.1177/014662168300700301</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Streiner</surname><given-names>DL</given-names></name></person-group><article-title>Breaking up is hard to do: the heartbreak of dichotomizing continuous data</article-title><source>Can J Psychiatr</source><year>2002</year><volume>47</volume><fpage>262</fpage><lpage>266</lpage><pub-id pub-id-type="doi">10.1177/070674370204700307</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barnwell-M&#x000e9;nard</surname><given-names>J-L</given-names></name><name><surname>Li</surname><given-names>Q</given-names></name><name><surname>Cohen</surname><given-names>AA</given-names></name></person-group><article-title>Effects of categorization method, regression type, and variable distribution on the inflation of type-I error rate when categorizing a confounding variable</article-title><source>Stat Med</source><year>2015</year><volume>34</volume><fpage>936</fpage><lpage>949</lpage><pub-id pub-id-type="doi">10.1002/sim.6387</pub-id><?supplied-pmid 25504513?><pub-id pub-id-type="pmid">25504513</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Reif</surname><given-names>DM</given-names></name><name><surname>White</surname><given-names>BC</given-names></name><name><surname>Moore</surname><given-names>JH</given-names></name></person-group><article-title>Integrated analysis of genetic, genomic and proteomic data</article-title><source>Expert Rev Proteomics</source><year>2004</year><volume>1</volume><fpage>67</fpage><lpage>75</lpage><pub-id pub-id-type="doi">10.1586/14789450.1.1.67</pub-id><?supplied-pmid 15966800?><pub-id pub-id-type="pmid">15966800</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hamid</surname><given-names>JS</given-names></name><name><surname>Hu</surname><given-names>P</given-names></name><name><surname>Roslin</surname><given-names>NM</given-names></name><name><surname>Ling</surname><given-names>V</given-names></name><name><surname>Greenwood</surname><given-names>CMT</given-names></name><name><surname>Beyene</surname><given-names>J</given-names></name></person-group><article-title>Data integration in genetics and genomics: methods and challenges</article-title><source>Hum Genomics Proteomics</source><year>2009</year><volume>2009</volume><fpage>1</fpage><lpage>13</lpage><pub-id pub-id-type="doi">10.4061/2009/869093.</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sieberts</surname><given-names>SK</given-names></name><name><surname>Schadt</surname><given-names>EE</given-names></name></person-group><article-title>Moving toward a system genetics view of disease</article-title><source>Mamm Genome</source><year>2007</year><volume>18</volume><fpage>389</fpage><lpage>401</lpage><pub-id pub-id-type="doi">10.1007/s00335-007-9040-6</pub-id><?supplied-pmid 17653589?><pub-id pub-id-type="pmid">17653589</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hawkins</surname><given-names>RD</given-names></name><name><surname>Hon</surname><given-names>GC</given-names></name><name><surname>Ren</surname><given-names>B</given-names></name></person-group><article-title>Next-generation genomics: an integrative approach</article-title><source>Nat Rev Genet</source><year>2010</year><volume>11</volume><fpage>476</fpage><lpage>486</lpage><pub-id pub-id-type="doi">10.1038/nrg2795</pub-id><?supplied-pmid 20531367?><pub-id pub-id-type="pmid">20531367</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lazic</surname><given-names>SE</given-names></name></person-group><article-title>Ranking, selecting, and prioritising genes with desirability functions</article-title><source>PeerJ</source><year>2015</year><volume>3</volume><fpage>e1444</fpage><pub-id pub-id-type="doi">10.7717/peerj.1444</pub-id><?supplied-pmid 26644980?><pub-id pub-id-type="pmid">26644980</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bickerton</surname><given-names>GR</given-names></name><name><surname>Paolini</surname><given-names>GV</given-names></name><name><surname>Besnard</surname><given-names>J</given-names></name><name><surname>Muresan</surname><given-names>S</given-names></name><name><surname>Hopkins</surname><given-names>AL</given-names></name></person-group><article-title>Quantifying the chemical beauty of drugs</article-title><source>Nat Chem</source><year>2012</year><volume>4</volume><fpage>90</fpage><lpage>98</lpage><pub-id pub-id-type="doi">10.1038/nchem.1243</pub-id><?supplied-pmid 22270643?><pub-id pub-id-type="pmid">22270643</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Harrington</surname><given-names>E</given-names></name></person-group><article-title>The desirability function</article-title><source>Ind Qual Control</source><year>1965</year><volume>21</volume><fpage>494</fpage><lpage>498</lpage></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Derringer</surname><given-names>G</given-names></name><name><surname>Suich</surname><given-names>R</given-names></name></person-group><article-title>Simultaneous optimization of several response variables</article-title><source>J Qual Technol</source><year>1980</year><volume>12</volume><fpage>214</fpage><lpage>219</lpage><pub-id pub-id-type="doi">10.1080/00224065.1980.11980968</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Derringer</surname><given-names>G</given-names></name></person-group><article-title>A balancing act: optimizing a products properties</article-title><source>Qual Prog</source><year>1994</year><volume>27</volume><fpage>51</fpage></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Romero</surname><given-names>R</given-names></name><name><surname>Dey</surname><given-names>SK</given-names></name><name><surname>Fisher</surname><given-names>SJ</given-names></name></person-group><article-title>Preterm labor: one syndrome, many causes</article-title><source>Science</source><year>2014</year><volume>345</volume><fpage>760</fpage><lpage>765</lpage><pub-id pub-id-type="doi">10.1126/science.1251816</pub-id><?supplied-pmid 25124429?><pub-id pub-id-type="pmid">25124429</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eidem</surname><given-names>HR</given-names></name><name><surname>McGary</surname><given-names>KL</given-names></name><name><surname>Capra</surname><given-names>JA</given-names></name><name><surname>Abbot</surname><given-names>P</given-names></name><name><surname>Rokas</surname><given-names>A</given-names></name></person-group><article-title>The transformative potential of an integrative approach to pregnancy</article-title><source>Placenta</source><year>2017</year><volume>57</volume><fpage>204</fpage><lpage>215</lpage><pub-id pub-id-type="doi">10.1016/j.placenta.2017.07.010</pub-id><pub-id pub-id-type="pmid">28864013</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>G</given-names></name><name><surname>Jacobsson</surname><given-names>B</given-names></name><name><surname>Muglia</surname><given-names>LJ</given-names></name></person-group><article-title>Genetic associations with spontaneous preterm birth</article-title><source>N Engl J Med</source><year>2017</year><volume>377</volume><fpage>2401</fpage><lpage>2402</lpage><pub-id pub-id-type="doi">10.1056/NEJMc1713902</pub-id><?supplied-pmid 29236637?><pub-id pub-id-type="pmid">29236637</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Plunkett</surname><given-names>J</given-names></name><name><surname>Muglia</surname><given-names>LJ</given-names></name></person-group><article-title>Genetic contributions to preterm birth: implications from epidemiological and genetic association studies</article-title><source>Ann Med</source><year>2008</year><volume>40</volume><fpage>167</fpage><lpage>179</lpage><pub-id pub-id-type="doi">10.1080/07853890701806181</pub-id><?supplied-pmid 18382883?><pub-id pub-id-type="pmid">18382883</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Muglia</surname><given-names>LJ</given-names></name><name><surname>Katz</surname><given-names>M</given-names></name></person-group><article-title>The enigma of spontaneous preterm birth</article-title><source>N Engl J Med</source><year>2010</year><volume>362</volume><fpage>529</fpage><lpage>535</lpage><pub-id pub-id-type="doi">10.1056/NEJMra0904308</pub-id><?supplied-pmid 20147718?><pub-id pub-id-type="pmid">20147718</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Lengyel</surname><given-names>C</given-names></name><name><surname>Muglia</surname><given-names>LJ</given-names></name><name><surname>Pavli&#x0010d;ev</surname><given-names>M</given-names></name></person-group><article-title>Genetics of Preterm Birth</article-title><source>eLS</source><year>2014</year><publisher-loc>Chichester</publisher-loc><publisher-name>Wiley</publisher-name></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ackerman</surname><given-names>WE</given-names></name><name><surname>Buhimschi</surname><given-names>IA</given-names></name><name><surname>Eidem</surname><given-names>HR</given-names></name><name><surname>Rinker</surname><given-names>DC</given-names></name><name><surname>Rokas</surname><given-names>A</given-names></name><name><surname>Rood</surname><given-names>K</given-names></name><etal/></person-group><article-title>Comprehensive RNA profiling of villous trophoblast and decidua basalis in pregnancies complicated by preterm birth following intra-amniotic infection</article-title><source>Placenta</source><year>2016</year><volume>44</volume><fpage>23</fpage><lpage>33</lpage><pub-id pub-id-type="doi">10.1016/j.placenta.2016.05.010</pub-id><?supplied-pmid 27452435?><pub-id pub-id-type="pmid">27452435</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Heng</surname><given-names>YJ</given-names></name><name><surname>Taylor</surname><given-names>L</given-names></name><name><surname>Larsen</surname><given-names>BG</given-names></name><name><surname>Chua</surname><given-names>HN</given-names></name><name><surname>Pung</surname><given-names>SM</given-names></name><name><surname>Lee</surname><given-names>MWF</given-names></name><etal/></person-group><article-title>Albumin decrease is associated with spontaneous preterm delivery within 48 h in women with threatened preterm labor</article-title><source>J Proteome Res</source><year>2015</year><volume>14</volume><fpage>457</fpage><lpage>466</lpage><pub-id pub-id-type="doi">10.1021/pr500852p</pub-id><?supplied-pmid 25299736?><pub-id pub-id-type="pmid">25299736</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Parets</surname><given-names>SE</given-names></name><name><surname>Conneely</surname><given-names>KN</given-names></name><name><surname>Kilaru</surname><given-names>V</given-names></name><name><surname>Fortunato</surname><given-names>SJ</given-names></name><name><surname>Syed</surname><given-names>TA</given-names></name><name><surname>Saade</surname><given-names>G</given-names></name><etal/></person-group><article-title>Fetal DNA methylation associates with early spontaneous preterm birth and gestational age</article-title><source>PLoS One</source><year>2013</year><volume>8</volume><fpage>e67489</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0067489</pub-id><?supplied-pmid 23826308?><pub-id pub-id-type="pmid">23826308</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cruickshank</surname><given-names>MN</given-names></name><name><surname>Oshlack</surname><given-names>A</given-names></name><name><surname>Theda</surname><given-names>C</given-names></name><name><surname>Davis</surname><given-names>PG</given-names></name><name><surname>Martino</surname><given-names>D</given-names></name><name><surname>Sheehan</surname><given-names>P</given-names></name><etal/></person-group><article-title>Analysis of epigenetic changes in survivors of preterm birth reveals the effect of gestational age and evidence for a long term legacy</article-title><source>Genome Med</source><year>2013</year><volume>5</volume><fpage>96</fpage><pub-id pub-id-type="doi">10.1186/gm500</pub-id><?supplied-pmid 24134860?><pub-id pub-id-type="pmid">24134860</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Saade</surname><given-names>GR</given-names></name><name><surname>Boggess</surname><given-names>KA</given-names></name><name><surname>Sullivan</surname><given-names>SA</given-names></name><name><surname>Markenson</surname><given-names>GR</given-names></name><name><surname>Iams</surname><given-names>JD</given-names></name><name><surname>Coonrod</surname><given-names>DV</given-names></name><etal/></person-group><article-title>Development and validation of a spontaneous preterm delivery predictor in asymptomatic women</article-title><source>Am J Obstet Gynecol</source><year>2016</year><volume>214</volume><fpage>633.e1</fpage><lpage>633.e24</lpage><pub-id pub-id-type="doi">10.1016/j.ajog.2016.02.001</pub-id><pub-id pub-id-type="pmid">26874297</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>G</given-names></name><name><surname>Bacelis</surname><given-names>J</given-names></name><name><surname>Lengyel</surname><given-names>C</given-names></name><name><surname>Teramo</surname><given-names>K</given-names></name><name><surname>Hallman</surname><given-names>M</given-names></name><name><surname>Helgeland</surname><given-names>&#x000d8;</given-names></name><etal/></person-group><article-title>Assessing the causal relationship of maternal height on birth size and gestational age at birth: a Mendelian randomization analysis</article-title><source>PLoS Med</source><year>2015</year><volume>12</volume><fpage>e1001865</fpage><pub-id pub-id-type="doi">10.1371/journal.pmed.1001865</pub-id><?supplied-pmid 26284790?><pub-id pub-id-type="pmid">26284790</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Makieva</surname><given-names>S</given-names></name><name><surname>Dubicke</surname><given-names>A</given-names></name><name><surname>Rinaldi</surname><given-names>SF</given-names></name><name><surname>Fransson</surname><given-names>E</given-names></name><name><surname>Ekman-Ordeberg</surname><given-names>G</given-names></name><name><surname>Norman</surname><given-names>JE</given-names></name></person-group><article-title>The preterm cervix reveals a transcriptomic signature in the presence of premature prelabor rupture of membranes</article-title><source>Am J Obstet Gynecol</source><year>2017</year><volume>216</volume><fpage>602.e1</fpage><lpage>602.e21</lpage><pub-id pub-id-type="doi">10.1016/j.ajog.2017.02.009</pub-id><pub-id pub-id-type="pmid">28209491</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Heng</surname><given-names>YJ</given-names></name><name><surname>Pennell</surname><given-names>CE</given-names></name><name><surname>Chua</surname><given-names>HN</given-names></name><name><surname>Perkins</surname><given-names>JE</given-names></name><name><surname>Lye</surname><given-names>SJ</given-names></name></person-group><article-title>Whole blood gene expression profile associated with spontaneous preterm birth in women with threatened preterm labor</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><fpage>e96901</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0096901</pub-id><?supplied-pmid 24828675?><pub-id pub-id-type="pmid">24828675</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chim</surname><given-names>SSC</given-names></name><name><surname>Lee</surname><given-names>WS</given-names></name><name><surname>Ting</surname><given-names>YH</given-names></name><name><surname>Chan</surname><given-names>OK</given-names></name><name><surname>Lee</surname><given-names>SWY</given-names></name><name><surname>Leung</surname><given-names>TY</given-names></name></person-group><article-title>Systematic identification of spontaneous preterm birth-associated RNA transcripts in maternal plasma</article-title><source>PLoS One</source><year>2012</year><volume>7</volume><fpage>e34328</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0034328</pub-id><?supplied-pmid 22496790?><pub-id pub-id-type="pmid">22496790</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mayor-Lynn</surname><given-names>K</given-names></name><name><surname>Toloubeydokhti</surname><given-names>T</given-names></name><name><surname>Cruz</surname><given-names>AC</given-names></name><name><surname>Chegini</surname><given-names>N</given-names></name></person-group><article-title>Expression profile of MicroRNAs and mRNAs in human placentas from pregnancies complicated by preeclampsia and preterm labor</article-title><source>Reprod Sci</source><year>2011</year><volume>18</volume><fpage>46</fpage><lpage>56</lpage><pub-id pub-id-type="doi">10.1177/1933719110374115</pub-id><?supplied-pmid 21079238?><pub-id pub-id-type="pmid">21079238</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>de Goede</surname><given-names>OM</given-names></name><name><surname>Lavoie</surname><given-names>PM</given-names></name><name><surname>Robinson</surname><given-names>WP</given-names></name></person-group><article-title>Cord blood hematopoietic cells from preterm infants display altered DNA methylation patterns</article-title><source>Clin Epigenetics</source><year>2017</year><volume>9</volume><fpage>39</fpage><pub-id pub-id-type="doi">10.1186/s13148-017-0339-1</pub-id><?supplied-pmid 28428831?><pub-id pub-id-type="pmid">28428831</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hong</surname><given-names>X</given-names></name><name><surname>Sherwood</surname><given-names>B</given-names></name><name><surname>Ladd-Acosta</surname><given-names>C</given-names></name><name><surname>Peng</surname><given-names>S</given-names></name><name><surname>Ji</surname><given-names>H</given-names></name><name><surname>Hao</surname><given-names>K</given-names></name><etal/></person-group><article-title>Genome-wide DNA methylation associations with spontaneous preterm birth in US blacks: findings in maternal and cord blood samples</article-title><source>Epigenetics</source><year>2018</year><volume>13</volume><fpage>163</fpage><lpage>172</lpage><pub-id pub-id-type="doi">10.1080/15592294.2017.1287654</pub-id><?supplied-pmid 28165855?><pub-id pub-id-type="pmid">28165855</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fernando</surname><given-names>F</given-names></name><name><surname>Keijser</surname><given-names>R</given-names></name><name><surname>Henneman</surname><given-names>P</given-names></name><name><surname>van der Kevie-Kersemaekers</surname><given-names>A-MF</given-names></name><name><surname>Mannens</surname><given-names>MM</given-names></name><name><surname>van der Post</surname><given-names>JA</given-names></name><etal/></person-group><article-title>The idiopathic preterm delivery methylation profile in umbilical cord blood DNA</article-title><source>BMC Genomics</source><year>2015</year><volume>16</volume><fpage>736</fpage><pub-id pub-id-type="doi">10.1186/s12864-015-1915-4.</pub-id><?supplied-pmid 26419829?><pub-id pub-id-type="pmid">26419829</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>G</given-names></name><name><surname>Feenstra</surname><given-names>B</given-names></name><name><surname>Bacelis</surname><given-names>J</given-names></name><name><surname>Liu</surname><given-names>X</given-names></name><name><surname>Muglia</surname><given-names>LM</given-names></name><name><surname>Juodakis</surname><given-names>J</given-names></name><etal/></person-group><article-title>Genetic associations with gestational duration and spontaneous preterm birth</article-title><source>Obstet Gynecol Surv</source><year>2017</year><volume>73</volume><fpage>83</fpage><lpage>85</lpage><pub-id pub-id-type="doi">10.1097/01.ogx.0000530434.15441.45.</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>de Leeuw</surname><given-names>CA</given-names></name><name><surname>Mooij</surname><given-names>JM</given-names></name><name><surname>Heskes</surname><given-names>T</given-names></name><name><surname>Posthuma</surname><given-names>D</given-names></name></person-group><article-title>MAGMA: generalized gene-set analysis of GWAS data</article-title><source>PLoS Comput Biol</source><year>2015</year><volume>11</volume><fpage>e1004219</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004219</pub-id><?supplied-pmid 25885710?><pub-id pub-id-type="pmid">25885710</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Conway</surname><given-names>JR</given-names></name><name><surname>Lex</surname><given-names>A</given-names></name><name><surname>Gehlenborg</surname><given-names>N</given-names></name></person-group><article-title>UpSetR: an R package for the visualization of intersecting sets and their properties</article-title><source>Bioinformatics</source><year>2017</year><volume>33</volume><fpage>2938</fpage><lpage>2940</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btx364</pub-id><?supplied-pmid 5870712?><pub-id pub-id-type="pmid">28645171</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mi</surname><given-names>H</given-names></name><name><surname>Huang</surname><given-names>X</given-names></name><name><surname>Muruganujan</surname><given-names>A</given-names></name><name><surname>Tang</surname><given-names>H</given-names></name><name><surname>Mills</surname><given-names>C</given-names></name><name><surname>Kang</surname><given-names>D</given-names></name><etal/></person-group><article-title>PANTHER version 11: expanded annotation data from gene ontology and reactome pathways, and data analysis tool enhancements</article-title><source>Nucleic Acids Res</source><year>2017</year><volume>45</volume><fpage>D183</fpage><lpage>D189</lpage><pub-id pub-id-type="doi">10.1093/nar/gkw1138.</pub-id><?supplied-pmid 27899595?><pub-id pub-id-type="pmid">27899595</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><mixed-citation publication-type="other">Heng YJ, Liong S, Permezel M, Rice GE, Di Quinzio MKW, Georgiou HM. Human cervicovaginal fluid biomarkers to predict term and preterm labor. Front Physiol. 2015;6. 10.3389/fphys.2015.00151.</mixed-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Velez</surname><given-names>DR</given-names></name><name><surname>Fortunato</surname><given-names>SJ</given-names></name><name><surname>Thorsen</surname><given-names>P</given-names></name><name><surname>Lombardi</surname><given-names>SJ</given-names></name><name><surname>Williams</surname><given-names>SM</given-names></name><name><surname>Menon</surname><given-names>R</given-names></name></person-group><article-title>Preterm birth in Caucasians is associated with coagulation and inflammation pathway gene variants</article-title><source>PLoS One</source><year>2008</year><volume>3</volume><fpage>e3283</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0003283</pub-id><?supplied-pmid 18818748?><pub-id pub-id-type="pmid">18818748</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cowman</surname><given-names>J</given-names></name><name><surname>Quinn</surname><given-names>N</given-names></name><name><surname>Geoghegan</surname><given-names>S</given-names></name><name><surname>M&#x000fc;llers</surname><given-names>S</given-names></name><name><surname>Oglesby</surname><given-names>I</given-names></name><name><surname>Byrne</surname><given-names>B</given-names></name><etal/></person-group><article-title>Dynamic platelet function on von Willebrand factor is different in preterm neonates and full-term neonates: changes in neonatal platelet function</article-title><source>J Thromb Haemost</source><year>2016</year><volume>14</volume><fpage>2027</fpage><lpage>2035</lpage><pub-id pub-id-type="doi">10.1111/jth.13414</pub-id><?supplied-pmid 27416003?><pub-id pub-id-type="pmid">27416003</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Strauss</surname><given-names>T</given-names></name><name><surname>Elisha</surname><given-names>N</given-names></name><name><surname>Ravid</surname><given-names>B</given-names></name><name><surname>Rosenberg</surname><given-names>N</given-names></name><name><surname>Lubetsky</surname><given-names>A</given-names></name><name><surname>Levy-Mendelovich</surname><given-names>S</given-names></name><etal/></person-group><article-title>Activity of Von Willebrand factor and levels of VWF-cleaving protease (ADAMTS13) in preterm and full term neonates</article-title><source>Blood Cells Mol Dis</source><year>2017</year><volume>67</volume><fpage>14</fpage><lpage>17</lpage><pub-id pub-id-type="doi">10.1016/j.bcmd.2016.12.013</pub-id><?supplied-pmid 28087247?><pub-id pub-id-type="pmid">28087247</pub-id></element-citation></ref><ref id="CR55"><label>55.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>Y</given-names></name><name><surname>Paszty</surname><given-names>C</given-names></name><name><surname>Turetsky</surname><given-names>T</given-names></name><name><surname>Tsai</surname><given-names>S</given-names></name><name><surname>Kuypers</surname><given-names>FA</given-names></name><name><surname>Lee</surname><given-names>G</given-names></name><etal/></person-group><article-title>Stomatocytosis is absent in &#x0201c;stomatin&#x0201d;-deficient murine red blood cells</article-title><source>Blood</source><year>1999</year><volume>93</volume><fpage>2404</fpage><lpage>2410</lpage><?supplied-pmid 10090952?><pub-id pub-id-type="pmid">10090952</pub-id></element-citation></ref><ref id="CR56"><label>56.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Littlefield</surname><given-names>R</given-names></name><name><surname>Almenar-Queralt</surname><given-names>A</given-names></name><name><surname>Fowler</surname><given-names>VM</given-names></name></person-group><article-title>Actin dynamics at pointed ends regulates thin filament length in striated muscle</article-title><source>Nat Cell Biol</source><year>2001</year><volume>3</volume><fpage>544</fpage><lpage>551</lpage><pub-id pub-id-type="doi">10.1038/35078517</pub-id><?supplied-pmid 11389438?><pub-id pub-id-type="pmid">11389438</pub-id></element-citation></ref><ref id="CR57"><label>57.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Caldwell</surname><given-names>JE</given-names></name><name><surname>Heiss</surname><given-names>SG</given-names></name><name><surname>Mermall</surname><given-names>V</given-names></name><name><surname>Cooper</surname><given-names>JA</given-names></name></person-group><article-title>Effects of CapZ, an actin-capping protein of muscle, on the polymerization of actin</article-title><source>Biochemistry</source><year>1989</year><volume>28</volume><fpage>8506</fpage><lpage>8514</lpage><pub-id pub-id-type="doi">10.1021/bi00447a036</pub-id><?supplied-pmid 2557904?><pub-id pub-id-type="pmid">2557904</pub-id></element-citation></ref><ref id="CR58"><label>58.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bottega</surname><given-names>R</given-names></name><name><surname>Marconi</surname><given-names>C</given-names></name><name><surname>Faleschini</surname><given-names>M</given-names></name><name><surname>Baj</surname><given-names>G</given-names></name><name><surname>Cagioni</surname><given-names>C</given-names></name><name><surname>Pecci</surname><given-names>A</given-names></name><etal/></person-group><article-title>ACTN1-related thrombocytopenia: identification of novel families for phenotypic characterization</article-title><source>Blood</source><year>2015</year><volume>125</volume><fpage>869</fpage><lpage>872</lpage><pub-id pub-id-type="doi">10.1182/blood-2014-08-594531</pub-id><?supplied-pmid 25361813?><pub-id pub-id-type="pmid">25361813</pub-id></element-citation></ref><ref id="CR59"><label>59.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kunishima</surname><given-names>S</given-names></name><name><surname>Okuno</surname><given-names>Y</given-names></name><name><surname>Yoshida</surname><given-names>K</given-names></name><name><surname>Shiraishi</surname><given-names>Y</given-names></name><name><surname>Sanada</surname><given-names>M</given-names></name><name><surname>Muramatsu</surname><given-names>H</given-names></name><etal/></person-group><article-title>ACTN1 mutations cause congenital macrothrombocytopenia</article-title><source>Am J Hum Genet</source><year>2013</year><volume>92</volume><fpage>431</fpage><lpage>438</lpage><pub-id pub-id-type="doi">10.1016/j.ajhg.2013.01.015</pub-id><?supplied-pmid 23434115?><pub-id pub-id-type="pmid">23434115</pub-id></element-citation></ref><ref id="CR60"><label>60.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhao</surname><given-names>P</given-names></name><name><surname>Chidiac</surname><given-names>P</given-names></name></person-group><article-title>Regulation of RGS5 GAP activity by GPSM3</article-title><source>Mol Cell Biochem</source><year>2015</year><volume>405</volume><fpage>33</fpage><lpage>40</lpage><pub-id pub-id-type="doi">10.1007/s11010-015-2393-3</pub-id><?supplied-pmid 25842189?><pub-id pub-id-type="pmid">25842189</pub-id></element-citation></ref><ref id="CR61"><label>61.</label><mixed-citation publication-type="other">Ono S. Functions of actin-interacting protein 1 (AIP1)/WD repeat protein 1 (WDR1) in actin filament dynamics and cytoskeletal regulation. Biochem Biophys Res Commun. 2017. 10.1016/j.bbrc.2017.10.096.</mixed-citation></ref><ref id="CR62"><label>62.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>J</given-names></name><name><surname>Aoki</surname><given-names>M</given-names></name><name><surname>Illa</surname><given-names>I</given-names></name><name><surname>Wu</surname><given-names>C</given-names></name><name><surname>Fardeau</surname><given-names>M</given-names></name><name><surname>Angelini</surname><given-names>C</given-names></name><etal/></person-group><article-title>Dysferlin, a novel skeletal muscle gene, is mutated in Miyoshi myopathy and limb girdle muscular dystrophy</article-title><source>Nat Genet</source><year>1998</year><volume>20</volume><fpage>31</fpage><lpage>36</lpage><pub-id pub-id-type="doi">10.1038/1682</pub-id><?supplied-pmid 9731526?><pub-id pub-id-type="pmid">9731526</pub-id></element-citation></ref><ref id="CR63"><label>63.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Athayde</surname><given-names>N</given-names></name><name><surname>Romero</surname><given-names>R</given-names></name><name><surname>Gomez</surname><given-names>R</given-names></name><name><surname>Maymon</surname><given-names>E</given-names></name><name><surname>Pacora</surname><given-names>P</given-names></name><name><surname>Mazor</surname><given-names>M</given-names></name><etal/></person-group><article-title>Matrix metalloproteinases-9 in preterm and term human parturition</article-title><source>J Matern Neonatal Med</source><year>1999</year><volume>8</volume><fpage>213</fpage><lpage>219</lpage><pub-id pub-id-type="doi">10.3109/14767059909052049</pub-id></element-citation></ref><ref id="CR64"><label>64.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>J</given-names></name><name><surname>Khalil</surname><given-names>RA</given-names></name></person-group><article-title>Matrix metalloproteinases in normal pregnancy and preeclampsia</article-title><source>Progress in molecular biology and translational science</source><year>2017</year><fpage>87</fpage><lpage>165</lpage></element-citation></ref><ref id="CR65"><label>65.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>P</given-names></name><name><surname>Alfaidy</surname><given-names>N</given-names></name><name><surname>Challis</surname><given-names>JRG</given-names></name></person-group><article-title>Expression of matrix metalloproteinase (MMP)-2 and MMP-9 in human placenta and fetal membranes in relation to preterm and term labor</article-title><source>J Clin Endocrinol Metab</source><year>2002</year><volume>87</volume><fpage>1353</fpage><lpage>1361</lpage><pub-id pub-id-type="doi">10.1210/jcem.87.3.8320</pub-id><?supplied-pmid 11889208?><pub-id pub-id-type="pmid">11889208</pub-id></element-citation></ref><ref id="CR66"><label>66.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Poon</surname><given-names>LCY</given-names></name><name><surname>Nekrasova</surname><given-names>E</given-names></name><name><surname>Anastassopoulos</surname><given-names>P</given-names></name><name><surname>Livanos</surname><given-names>P</given-names></name><name><surname>Nicolaides</surname><given-names>KH</given-names></name></person-group><article-title>First-trimester maternal serum matrix metalloproteinase-9 (MMP-9) and adverse pregnancy outcome</article-title><source>Prenat Diagn</source><year>2009</year><volume>29</volume><fpage>553</fpage><lpage>559</lpage><pub-id pub-id-type="doi">10.1002/pd.2234</pub-id><?supplied-pmid 19242924?><pub-id pub-id-type="pmid">19242924</pub-id></element-citation></ref><ref id="CR67"><label>67.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Romero</surname><given-names>R</given-names></name><name><surname>Chaiworapongsa</surname><given-names>T</given-names></name><name><surname>Espinoza</surname><given-names>J</given-names></name><name><surname>Gomez</surname><given-names>R</given-names></name><name><surname>Yoon</surname><given-names>BH</given-names></name><name><surname>Edwin</surname><given-names>S</given-names></name><etal/></person-group><article-title>Fetal plasma MMP-9 concentrations are elevated in preterm premature rupture of the membranes</article-title><source>Am J Obstet Gynecol</source><year>2002</year><volume>187</volume><fpage>1125</fpage><lpage>1130</lpage><pub-id pub-id-type="doi">10.1067/mob.2002.127312</pub-id><?supplied-pmid 12439489?><pub-id pub-id-type="pmid">12439489</pub-id></element-citation></ref><ref id="CR68"><label>68.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tency</surname><given-names>I</given-names></name><name><surname>Verstraelen</surname><given-names>H</given-names></name><name><surname>Kroes</surname><given-names>I</given-names></name><name><surname>Holtappels</surname><given-names>G</given-names></name><name><surname>Verhasselt</surname><given-names>B</given-names></name><name><surname>Vaneechoutte</surname><given-names>M</given-names></name><etal/></person-group><article-title>Imbalances between matrix metalloproteinases (MMPs) and tissue inhibitor of metalloproteinases (TIMPs) in maternal serum during preterm labor</article-title><source>PLoS One</source><year>2012</year><volume>7</volume><fpage>e49042</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0049042</pub-id><?supplied-pmid 23145060?><pub-id pub-id-type="pmid">23145060</pub-id></element-citation></ref><ref id="CR69"><label>69.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sundrani</surname><given-names>DP</given-names></name><name><surname>Chavan-Gautam</surname><given-names>PM</given-names></name><name><surname>Pisal</surname><given-names>HR</given-names></name><name><surname>Mehendale</surname><given-names>SS</given-names></name><name><surname>Joshi</surname><given-names>SR</given-names></name></person-group><article-title>Matrix metalloproteinase-1 and -9 in human placenta during spontaneous vaginal delivery and caesarean sectioning in preterm pregnancy</article-title><source>PLoS One</source><year>2012</year><volume>7</volume><fpage>e29855</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0029855</pub-id><pub-id pub-id-type="pmid">22253805</pub-id></element-citation></ref><ref id="CR70"><label>70.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yonemoto</surname><given-names>H</given-names></name><name><surname>Young</surname><given-names>CB</given-names></name><name><surname>Ross</surname><given-names>JT</given-names></name><name><surname>Guilbert</surname><given-names>LL</given-names></name><name><surname>Fairclough</surname><given-names>RJ</given-names></name><name><surname>Olson</surname><given-names>DM</given-names></name></person-group><article-title>Changes in matrix metalloproteinase (MMP)-2 and MMP-9 in the fetal amnion and chorion during gestation and at term and preterm labor</article-title><source>Placenta</source><year>2006</year><volume>27</volume><fpage>669</fpage><lpage>677</lpage><pub-id pub-id-type="doi">10.1016/j.placenta.2005.05.014</pub-id><pub-id pub-id-type="pmid">16061282</pub-id></element-citation></ref><ref id="CR71"><label>71.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eidem</surname><given-names>HR</given-names></name><name><surname>Ackerman</surname><given-names>WE</given-names></name><name><surname>McGary</surname><given-names>KL</given-names></name><name><surname>Abbot</surname><given-names>P</given-names></name><name><surname>Rokas</surname><given-names>A</given-names></name></person-group><article-title>Gestational tissue transcriptomics in term and preterm human pregnancies: a systematic review and meta-analysis</article-title><source>BMC Med Genet</source><year>2015</year><volume>8</volume><fpage>27</fpage></element-citation></ref></ref-list></back></article>