<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Microbiome</journal-id><journal-id journal-id-type="iso-abbrev">Microbiome</journal-id><journal-title-group><journal-title>Microbiome</journal-title></journal-title-group><issn pub-type="epub">2049-2618</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6162917</article-id><article-id pub-id-type="publisher-id">550</article-id><article-id pub-id-type="doi">10.1186/s40168-018-0550-0</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>Genomes from uncultivated prokaryotes: a comparison of metagenome-assembled and single-amplified genomes</article-title></title-group><contrib-group><contrib contrib-type="author" equal-contrib="yes"><name><surname>Alneberg</surname><given-names>Johannes</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Karlsson</surname><given-names>Christofer M. G.</given-names></name><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Divne</surname><given-names>Anna-Maria</given-names></name><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Bergin</surname><given-names>Claudia</given-names></name><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Homa</surname><given-names>Felix</given-names></name><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Lindh</surname><given-names>Markus V.</given-names></name><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Hugerth</surname><given-names>Luisa W.</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Ettema</surname><given-names>Thijs J. G.</given-names></name><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Bertilsson</surname><given-names>Stefan</given-names></name><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Andersson</surname><given-names>Anders F.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-6405-1347</contrib-id><name><surname>Pinhassi</surname><given-names>Jarone</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000000121581746</institution-id><institution-id institution-id-type="GRID">grid.5037.1</institution-id><institution>School of Engineering Sciences in Chemistry, Biotechnology and Health, Department of Gene Technology, Science for Life Laboratory, </institution><institution>KTH Royal Institute of Technology, </institution></institution-wrap>Stockholm, Sweden </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2174 3522</institution-id><institution-id institution-id-type="GRID">grid.8148.5</institution-id><institution>Centre for Ecology and Evolution in Microbial Model Systems, EEMiS, </institution><institution>Linnaeus University, </institution></institution-wrap>Kalmar, Sweden </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 9457</institution-id><institution-id institution-id-type="GRID">grid.8993.b</institution-id><institution>Department of Cell and Molecular Biology, SciLifeLab, </institution><institution>Uppsala University, </institution></institution-wrap>Uppsala, Sweden </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="GRID">grid.465198.7</institution-id><institution>Present address: Science for Life Laboratory, Department of Molecular, Tumour and Cell Biology, Centre for Translational Microbiome Research, </institution><institution>Karolinska Institutet, </institution></institution-wrap>Solna, Sweden </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0930 2361</institution-id><institution-id institution-id-type="GRID">grid.4514.4</institution-id><institution>Present address: Department of Biology, </institution><institution>Lund University, </institution></institution-wrap>Lund, Sweden </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 9457</institution-id><institution-id institution-id-type="GRID">grid.8993.b</institution-id><institution>Department of Ecology and Genetics, Limnology, Science for Life Laboratory, </institution><institution>Uppsala University, </institution></institution-wrap>Uppsala, Sweden </aff></contrib-group><pub-date pub-type="epub"><day>28</day><month>9</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>28</day><month>9</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>6</volume><elocation-id>173</elocation-id><history><date date-type="received"><day>3</day><month>8</month><year>2017</year></date><date date-type="accepted"><day>5</day><month>9</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Prokaryotes dominate the biosphere and regulate biogeochemical processes essential to all life. Yet, our knowledge about their biology is for the most part limited to the minority that has been successfully cultured. Molecular techniques now allow for obtaining genome sequences of uncultivated prokaryotic taxa, facilitating in-depth analyses that may ultimately improve our understanding of these key organisms.</p></sec><sec><title>Results</title><p id="Par2">We compared results from two culture-independent strategies for recovering bacterial genomes: single-amplified genomes and metagenome-assembled genomes. Single-amplified genomes were obtained from samples collected at an offshore station in the Baltic Sea Proper and compared to previously obtained metagenome-assembled genomes from a time series at the same station. Among 16 single-amplified genomes analyzed, seven were found to match metagenome-assembled genomes, affiliated with a diverse set of taxa. Notably, genome pairs between the two approaches were nearly identical (average 99.51% sequence identity; range 98.77&#x02013;99.84%) across overlapping regions (30&#x02013;80% of each genome). Within matching pairs, the single-amplified genomes were consistently smaller and less complete, whereas the genetic functional profiles were maintained. For the metagenome-assembled genomes, only on average 3.6% of the bases were estimated to be missing from the genomes due to wrongly binned contigs.</p></sec><sec><title>Conclusions</title><p id="Par3">The strong agreement between the single-amplified and metagenome-assembled genomes emphasizes that both methods generate accurate genome information from uncultivated bacteria. Importantly, this implies that the research questions and the available resources are allowed to determine the selection of genomics approach for microbiome studies.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s40168-018-0550-0) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Single-amplified genomes</kwd><kwd>Metagenome-assembled genomes</kwd><kwd>Metagenomics</kwd><kwd>Binning</kwd><kwd>Single-cell genomics</kwd></kwd-group><funding-group><award-group><funding-source><institution>EU BONUS</institution></funding-source><award-id>BLUEPRINT</award-id><award-id>BLUEPRINT</award-id><principal-award-recipient><name><surname>Andersson</surname><given-names>Anders F.</given-names></name><name><surname>Pinhassi</surname><given-names>Jarone</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100004359</institution-id><institution>Vetenskapsr&#x000e5;det</institution></institution-wrap></funding-source><award-id>2011-4369</award-id><award-id>2015-04959</award-id><award-id>2011-5689</award-id><principal-award-recipient><name><surname>Andersson</surname><given-names>Anders F.</given-names></name><name><surname>Pinhassi</surname><given-names>Jarone</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001862</institution-id><institution>Svenska Forskningsr&#x000e5;det Formas</institution></institution-wrap></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000781</institution-id><institution>European Research Council</institution></institution-wrap></funding-source><award-id>310039-PUZZLE_CELL</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001729</institution-id><institution>Stiftelsen f&#x000f6;r&#x000a0;Strategisk Forskning</institution></institution-wrap></funding-source><award-id>SSF-FFL5</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par11">The genome is a fundamental resource for understanding the physiology, ecology, and evolution of an organism. With the availability of high-throughput sequencing technologies, we are witnessing a massive increase in the number of genomes in public repositories, with nearly a doubling per year in the Genomes OnLine Database (GOLD) [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. Reference genomes are important in both medical and environmental microbiology for capturing information on metabolic properties [<xref ref-type="bibr" rid="CR3">3</xref>], phylogeny [<xref ref-type="bibr" rid="CR4">4</xref>], evolution and diseases [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>], population genetics [<xref ref-type="bibr" rid="CR7">7</xref>], functionality and biogeochemical cycles [<xref ref-type="bibr" rid="CR8">8</xref>], and interactions [<xref ref-type="bibr" rid="CR9">9</xref>] and to establish links between genomes and functionality of cells in organisms [<xref ref-type="bibr" rid="CR10">10</xref>]. In fact, obtaining good and relevant reference genomes is crucial for current advances in many, if not all, branches of biological research [<xref ref-type="bibr" rid="CR11">11</xref>].</p><p id="Par12">Prokaryotes dominate the biosphere in the context of abundance and diversity [<xref ref-type="bibr" rid="CR12">12</xref>] and hold key roles in biogeochemical processes essential to all life [<xref ref-type="bibr" rid="CR13">13</xref>]. However, only a small fraction of the bacterial diversity (&#x0003c;&#x02009;1%) can be isolated and cultivated in a standardized fashion [<xref ref-type="bibr" rid="CR14">14</xref>]. Therefore, strategies for recovering genomes from samples without the need for cultivation have emerged as important complements to traditional microbiological techniques. In the single-amplified genome (SAG) strategy, genomes of individual cells are sequenced. The first step comprises partitioning of the cells [<xref ref-type="bibr" rid="CR15">15</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>] using techniques such as fluorescent-activated cell sorting (FACS) [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR19">19</xref>] or microfluidics [<xref ref-type="bibr" rid="CR20">20</xref>]. The next step involves cell lysis and whole-genome amplification (WGA) for which three methods are most commonly used: PCR-based (e.g., degenerate oligonucleotide-primed PCR (DOP-PCR)), isothermal (e.g., multiple displacement amplification (MDA)), or hybrid methods (e.g., multiple annealing and looping-based amplification cycles (MALBAC)) [<xref ref-type="bibr" rid="CR21">21</xref>] before applying shotgun sequencing and genome assembly [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR22">22</xref>].</p><p id="Par13">Genomes can also be recovered from metagenomes by assembling short shotgun reads into longer contigs which are then clustered into groups, or bins, of contigs derived from the same organism, through a process called binning. The resulting bins are quality filtered for contamination and completeness, and the approved bins are referred to as metagenome-assembled genomes (MAGs), a term proposed by Hugerth et al. [<xref ref-type="bibr" rid="CR23">23</xref>] and later accepted by the Genomic Standards Consortium (GSC) [<xref ref-type="bibr" rid="CR24">24</xref>]. Metagenomic binning has been used for some time [<xref ref-type="bibr" rid="CR25">25</xref>], but a fairly recent development is to perform the binning using a combination of sequence composition and differential abundance information [<xref ref-type="bibr" rid="CR26">26</xref>&#x02013;<xref ref-type="bibr" rid="CR29">29</xref>]. Whereas it is possible to use as few as two samples for utilizing differential abundance information, the quality of the binning results can be greatly improved by increasing the number of samples [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>].</p><p id="Par14">Although both the SAG and the MAG approaches have proven powerful and contributed greatly to our understanding of the physiology and evolution of organisms [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR30">30</xref>&#x02013;<xref ref-type="bibr" rid="CR35">35</xref>], a number of challenges are associated with each approach. SAG sequencing is demanding in terms of instrumentation and staff [<xref ref-type="bibr" rid="CR36">36</xref>]. Starting with only one genome copy makes DNA amplification necessary but difficult, which often results in highly uneven coverage depth and some regions being completely missing from the sequencing output [<xref ref-type="bibr" rid="CR21">21</xref>, <xref ref-type="bibr" rid="CR37">37</xref>]. The commonly used method for DNA amplification, multiple displacement amplification (MDA), has also been shown to cause formation of chimeric molecules, mainly through inversions [<xref ref-type="bibr" rid="CR38">38</xref>]. Contamination is a common problem with SAG sequencing, originating either from reagent kits [<xref ref-type="bibr" rid="CR39">39</xref>] or from free DNA in environmental samples [<xref ref-type="bibr" rid="CR20">20</xref>]. Furthermore, cell dispersion, which might be necessary when cells are attached to particles or have formed biofilms, can be problematic and hinder genome recovery from some single cells [<xref ref-type="bibr" rid="CR40">40</xref>]. Obtaining a large number of high-quality MAGs, on the other hand, requires extensive sequencing and ideally a large number of samples that to some degree share the same organisms in different abundances [<xref ref-type="bibr" rid="CR28">28</xref>]. The quality of the MAGs is also highly dependent on the quality of the metagenome assembly; short contigs are not considered by most binning algorithms since their coverage and composition information contain too much noise [<xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR41">41</xref>, <xref ref-type="bibr" rid="CR42">42</xref>]. Another limitation is the computational demands, which normally exceed those for SAG assembly [<xref ref-type="bibr" rid="CR41">41</xref>]. Also, due to intraspecies genetic variation in the community, genomes recovered from metagenomic data often represent a population of closely related organisms (i.e., strains) rather than an individual organism [<xref ref-type="bibr" rid="CR41">41</xref>].</p><p id="Par15">Studies have successfully combined the SAG and MAG approaches to reach conclusions about organisms and ecosystems [<xref ref-type="bibr" rid="CR43">43</xref>, <xref ref-type="bibr" rid="CR44">44</xref>]. The approaches have also been combined to methodologically improve either the quality of the single-cell assemblies [<xref ref-type="bibr" rid="CR45">45</xref>] or the metagenome binning performance [<xref ref-type="bibr" rid="CR46">46</xref>]. However, with the exception of a study that focused on a single phylum and that did not use abundance patterns over multiple samples for the MAG construction [<xref ref-type="bibr" rid="CR43">43</xref>], the performance of the two approaches have to our knowledge not been thoroughly compared. The aim of this study was to do a comprehensive comparison between the SAG and MAG approaches for recovering prokaryotic genomes. We investigated SAGs and MAGs from bacterioplankton collected in the Baltic Sea Proper, where recent analyses have provided a detailed picture of the spatio-temporal distribution of microbial populations [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR47">47</xref>&#x02013;<xref ref-type="bibr" rid="CR49">49</xref>] and metabolic processes [<xref ref-type="bibr" rid="CR50">50</xref>]. Thus, this ecosystem is well suited for comparing different methodologies for investigating the genomic content and functional potential of dominant bacterial populations.</p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>Overview of SAGs and MAGs</title><p id="Par16">In order to compare single-amplified genomes with metagenome-assembled genomes from the same environment, we generated SAGs from the Linnaeus Microbial Observatory (LMO), located 11&#x000a0;km off the coast of Sweden in the Baltic Sea, and compared them with MAGs generated earlier from the same station [<xref ref-type="bibr" rid="CR23">23</xref>]. We obtained 16 SAGs of a variety of taxa including <italic>Bacteroidetes</italic>, <italic>Cyanobacteria</italic>, <italic>Alphaproteobacteria</italic>, and <italic>Gammaproteobacteria</italic> (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S1). These were compared to 83 MAGs from 30 phylogenetically distinct Baltic Sea clusters (BACLs) [<xref ref-type="bibr" rid="CR23">23</xref>] (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1; Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S1). The SAGs ranged in size from 0.14 to 2.15&#x000a0;Mbp and MAGs from 0.59 to 2.98&#x000a0;Mbp (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S1). The number of contigs in SAGs ranged from 80 to 712 with a maximum length of 107,141&#x000a0;bp, while the number of contigs in MAGs ranged from 60 to 951 with the longest being 181,472&#x000a0;bp (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S1).</p><p id="Par17">Using Mash [<xref ref-type="bibr" rid="CR51">51</xref>] to cluster the 99 genomes from both approaches, seven of the 16 SAGs were placed together with 24 of the MAGs into six clusters (i.e., each of these SAGs matching 1&#x02013;14 MAGs and each of these MAGs matching 1&#x02013;2 SAGs; Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> and Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1). This was in agreement with the clustering of MAGs in the analysis of Hugerth et al. [<xref ref-type="bibr" rid="CR23">23</xref>]. These clusters belonged to a diverse set of bacterial taxa, representing the SAR86 and SAR92 clades (<italic>Gammaproteobacteria</italic>), <italic>Flavobacteriaceae</italic> (2 taxa) and <italic>Cryomorphaceae</italic> (<italic>Bacteroidetes</italic>) and <italic>Rhodobacteraceae</italic> (<italic>Alphaproteobacteria</italic>) (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). The following comparisons between SAGs and MAGs are based on the genomes in these clusters.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Overview of the matching SAGs and MAGs sorted by Baltic Sea cluster (BACL) number</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2"/><th rowspan="2">Nucleotide identity in % (standard deviation)</th><th rowspan="2">Size (in bp)</th><th rowspan="2">% completeness</th><th rowspan="2">% redundancy</th><th rowspan="2">% MAG aligned</th><th rowspan="2">% SAG aligned</th><th colspan="4">% SAG reads mapping</th></tr><tr><th>MAG contigs</th><th>&#x02265;&#x02009;1kb contigs outside MAG</th><th>&#x0003c;&#x02009;1&#x000a0;kb contigs</th><th>Not mapping to metagenome</th></tr></thead><tbody><tr><td colspan="11">BACL1: <italic>Gammaproteobacteria</italic>; SAR86</td></tr><tr><td>&#x02003;BS0038H10</td><td/><td>547073</td><td>30.22</td><td>0.72</td><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;&#x02003;120507-bin14</td><td>99.36 (1.71)</td><td>1482147</td><td>94.24</td><td>2.16</td><td>29.10</td><td>84.20</td><td>72.07</td><td>0.01</td><td>18</td><td>9.92</td></tr><tr><td>&#x02003;&#x02003;120619-bin26</td><td>99.62 (1.21)</td><td>1539140</td><td>92.81</td><td>0.72</td><td>28.06</td><td>82.49</td><td>73.96</td><td>0.25</td><td>17.57</td><td>8.22</td></tr><tr><td>&#x02003;&#x02003;120813-bin36</td><td>99.56 (0.81)</td><td>1264266</td><td>92.09</td><td>1.44</td><td>31.19</td><td>79.38</td><td>76.61</td><td>0.33</td><td>7.68</td><td>15.38</td></tr><tr><td>&#x02003;&#x02003;120820-bin45</td><td>99.48 (1.15)</td><td>1455539</td><td>92.81</td><td>0.72</td><td>29.10</td><td>82.30</td><td>74.03</td><td>0.01</td><td>15.3</td><td>10.66</td></tr><tr><td>&#x02003;&#x02003;120823-bin87</td><td>99.55 (1.04)</td><td>1451966</td><td>93.53</td><td>2.16</td><td>29.53</td><td>85.14</td><td>78.2</td><td>0.04</td><td>11.46</td><td>10.3</td></tr><tr><td>&#x02003;&#x02003;120828-bin5</td><td>99.58 (0.77)</td><td>1029940</td><td>85.61</td><td>0.72</td><td>32.47</td><td>68.11</td><td>71.16</td><td>5.53</td><td>9.48</td><td>13.83</td></tr><tr><td>&#x02003;&#x02003;120920-bin57</td><td>99.57 (1.26)</td><td>1450272</td><td>86.33</td><td>4.32</td><td>27.45</td><td>76.21</td><td>68.46</td><td>0.15</td><td>23.47</td><td>7.92</td></tr><tr><td>&#x02003;&#x02003;120924-bin88</td><td>99.61 (0.97)</td><td>1314100</td><td>91.37</td><td>0.72</td><td>30.30</td><td>79.59</td><td>72.71</td><td>0.01</td><td>15.61</td><td>11.67</td></tr><tr><td>&#x02003;&#x02003;121001-bin56</td><td>99.57 (1.19)</td><td>1509054</td><td>87.05</td><td>4.32</td><td>28.33</td><td>82.35</td><td>70.28</td><td>0.04</td><td>18.97</td><td>10.71</td></tr><tr><td>&#x02003;&#x02003;121004-bin11</td><td>99.68 (0.46)</td><td>1030921</td><td>78.42</td><td>0.00</td><td>32.44</td><td>68.15</td><td>66.78</td><td>10.84</td><td>11.58</td><td>10.8</td></tr><tr><td>&#x02003;&#x02003;121015-bin70</td><td>99.49 (1.44)</td><td>1495089</td><td>92.81</td><td>0.00</td><td>29.02</td><td>86.08</td><td>80.22</td><td>0.01</td><td>10</td><td>9.76</td></tr><tr><td>&#x02003;&#x02003;121022-bin58</td><td>99.58 (0.93)</td><td>1435343</td><td>93.53</td><td>0.72</td><td>29.37</td><td>84.33</td><td>78.28</td><td>0.01</td><td>10.77</td><td>10.94</td></tr><tr><td>&#x02003;&#x02003;121105-bin34</td><td>99.61 (0.73)</td><td>1306513</td><td>94.24</td><td>4.32</td><td>30.45</td><td>79.11</td><td>74.96</td><td>0.01</td><td>13.64</td><td>11.39</td></tr><tr><td>&#x02003;&#x02003;121128-bin56</td><td>99.54 (1.19)</td><td>1469346</td><td>94.24</td><td>4.32</td><td>29.37</td><td>85.59</td><td>76.36</td><td>0.01</td><td>13.33</td><td>10.3</td></tr><tr><td colspan="11">BACL7: <italic>Bacteroidetes</italic>; <italic>Cryomorphaceae</italic>; <italic>Owenweeksia</italic></td></tr><tr><td>&#x02003;A11</td><td/><td>1656754</td><td>68.35</td><td>7.91</td><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;&#x02003;120322-bin74</td><td>99.84 (0.21)</td><td>1743356</td><td>97.84</td><td>0.00</td><td>75.05</td><td>83.52</td><td>87.6</td><td>0.42</td><td>2.25</td><td>9.73</td></tr><tr><td>&#x02003;&#x02003;120910-bin2</td><td>99.82 (0.31)</td><td>1746953</td><td>97.12</td><td>0.72</td><td>75.05</td><td>83.53</td><td>87.4</td><td>1.07</td><td>1.7</td><td>9.83</td></tr><tr><td>&#x02003;&#x02003;121220-bin83</td><td>99.82 (0.24)</td><td>1723929</td><td>95.68</td><td>0.00</td><td>75.13</td><td>82.22</td><td>85.79</td><td>3.22</td><td>2.62</td><td>8.37</td></tr><tr><td colspan="11">BACL10: <italic>Alphaproteobacteria</italic>; <italic>Rhodobacter</italic></td></tr><tr><td>&#x02003;BS0038D5</td><td/><td>1732939</td><td>39.57</td><td>0.72</td><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;&#x02003;120419-bin15</td><td>99.18 (1.16)</td><td>2834045</td><td>96.40</td><td>2.88</td><td>42.11</td><td>68.10</td><td>53.62</td><td>2.53</td><td>22.67</td><td>21.18</td></tr><tr><td>&#x02003;&#x02003;120910-bin24</td><td>99.21 (1.02)</td><td>2763624</td><td>95.68</td><td>1.44</td><td>42.24</td><td>68.31</td><td>55.24</td><td>0.58</td><td>21.91</td><td>22.27</td></tr><tr><td>&#x02003;&#x02003;121220-bin24</td><td>99.07 (0.95)</td><td>2112289</td><td>84.89</td><td>1.44</td><td>46.50</td><td>58.12</td><td>45.37</td><td>1.79</td><td>24.68</td><td>28.16</td></tr><tr><td colspan="11">BACL16: <italic>Gammaproteobacteria</italic>; SAR92</td></tr><tr><td>&#x02003;BS0038E9</td><td/><td>1153566</td><td>41.73</td><td>1.44</td><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;&#x02003;120322-bin99</td><td>99.45 (0.74)</td><td>1997685</td><td>92.09</td><td>1.44</td><td>42.50</td><td>74.24</td><td>70.42</td><td>2.3</td><td>17.57</td><td>9.71</td></tr><tr><td>&#x02003;&#x02003;120619-bin48</td><td>99.20 (1.51)</td><td>2527476</td><td>99.28</td><td>0.72</td><td>40.23</td><td>89.12</td><td>86.03</td><td>2.77</td><td>1.81</td><td>9.39</td></tr><tr><td colspan="11">BACL21: <italic>Bacteroidetes</italic>; <italic>Flavobacteriaceae</italic></td></tr><tr><td>&#x02003;BS0038D11</td><td/><td>1637880</td><td>74.82</td><td>2.16</td><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;&#x02003;121220-bin10</td><td>99.75 (0.38)</td><td>1915951</td><td>97.84</td><td>0.72</td><td>75.00</td><td>88.18</td><td>84.21</td><td>2.15</td><td>6.66</td><td>6.98</td></tr><tr><td>&#x02003;BS0038D2</td><td/><td>1023978</td><td>37.41</td><td>2.16</td><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;&#x02003;121220-bin10</td><td>99.74 (0.46)</td><td>1915951</td><td>97.84</td><td>0.72</td><td>45.59</td><td>85.40</td><td>92.43</td><td>1.37</td><td>4.36</td><td>1.85</td></tr><tr><td colspan="11">BACL22: <italic>Bacteroidetes</italic>; <italic>Flavobacteriaceae</italic>; <italic>Polaribacter</italic></td></tr><tr><td>&#x02003;BS0038A11</td><td/><td>1334036</td><td>33.81</td><td>2.88</td><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;&#x02003;120619-bin32</td><td>98.77 (2.13)</td><td>2408986</td><td>97.12</td><td>3.60</td><td>39.15</td><td>72.59</td><td>66.22</td><td>0.17</td><td>7.98</td><td>25.63</td></tr><tr><td>Average</td><td>99.51 (0.96)</td><td>SAG:<break/>1298032</td><td>SAG: 46.56</td><td>SAG: 2.57</td><td>40.59</td><td>79.05</td><td>73.94</td><td>1.42</td><td>12.44</td><td>12.20</td></tr><tr><td/><td/><td>MAG:<break/>1716955</td><td>MAG:<break/>92.83</td><td>MAG:<break/>92.83</td><td/><td/><td/><td/><td/><td/></tr></tbody></table></table-wrap></p><p id="Par18">The seasonal dynamics of the clusters at the LMO station were determined in the original MAG study by metagenome samples covering a single year (2012) [<xref ref-type="bibr" rid="CR23">23</xref>]. By comparing the 16S rRNA gene sequences from the genome clusters to 16S rRNA gene data from an amplicon-based high-temporal-resolution study from the same station from the previous year (2011) [<xref ref-type="bibr" rid="CR49">49</xref>], we observed five matches with a sequence identity of 100%. In these cases, the seasonal dynamics of the genome clusters and OTUs was similar between the years, with representatives abundant in spring and late autumn (2012) (BACL21, <italic>Flavobacteriaceae,</italic> OTU:000004 and BACL7, <italic>Owenweeksia</italic>, OTU:000021); spring and early summer (BACL16, SAR92 clade, OTU:000043); spring, summer, and autumn (BACL10, <italic>Rhodobacteraceae</italic>, OTU:000011); and all year round (BACL1, SAR86 clade, OTU:000013) [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR49">49</xref>] (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S2). The contigs representing the genomes of BACL22 lacked the 16S rRNA gene sequence and were not included in the seasonality analysis.</p></sec><sec id="Sec4"><title>Alignment and gene content</title><p id="Par19">To verify the clustering and to achieve more detailed statistics, each SAG-MAG pair was aligned using MUMmer (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). Across the genome regions showing homology between SAGs and MAGs, the within-cluster nucleotide sequence identity averaged 99.51%, with the lowest sequence identity value recorded for BACL22 (98.77%; Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). A larger fraction of the SAGs&#x02019; bases (average 78.9%) aligned compared to the MAGs&#x02019; (average 40.5%), in agreement with these SAGs being consistently smaller than the corresponding MAGs, 0.5&#x02013;1.7&#x000a0;Mbp and 1.0&#x02013;2.8&#x000a0;Mbp, respectively (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>)[<xref ref-type="bibr" rid="CR23">23</xref>].</p><p id="Par20">To further compare the SAGs and MAGs, the Anvi&#x02019;o pangenomic workflow [<xref ref-type="bibr" rid="CR52">52</xref>] was run on each cluster (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>, Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>: Table S2). This analysis showed that the completeness of the SAG genomes (average 46.6%) was lower than that of the MAG genomes (average 92.6%) (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>), as estimated by Anvi&#x02019;o (by presence of 139 bacterial single-copy genes [SCGs]). Redundancy in gene content (measured as SCGs present more than once) showed no systematic difference between SAGs and MAGs (Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>: Table S2); it was highest in SAG A11 and in four MAGs of BACL1 (with 7.9% and 4.3%, respectively). For details on contamination of SAGs, see the Results section &#x0201c;<xref rid="Sec7" ref-type="sec">SAG quality evaluation</xref>&#x0201d; below.<fig id="Fig1"><label>Fig. 1</label><caption><p>Gene homolog presence per genome cluster. Presence of gene homologs for each genome cluster by graphs produced by Anvi&#x02019;o. Each horizontal bar represents one genome, where blue bars are single-amplified genomes and black and grey bars are metagenome-assembled genomes. Each vertical bar corresponds to one gene homolog where a dark vertical bar indicates presence of the gene homologs and a lighter vertical bar indicates absence. The gene homologs are aligned between genomes within each genome cluster. The numbers assigned to the genome clusters corresponds to the original MAG BACLs used in [<xref ref-type="bibr" rid="CR23">23</xref>]</p></caption><graphic xlink:href="40168_2018_550_Fig1_HTML" id="MO1"/></fig></p><p id="Par21">There was a substantial range in gene content overlaps in different clusters (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). For example, most MAGs in BACL1 contained a large set of genes (~&#x02009;35% of genomes) missing in the corresponding SAG (BS0038H10), whereas the SAG in this cluster contained few genes (~&#x02009;5% of genomes) not present in the MAGs. In contrast, in BACL7, similar portions of the genes (~&#x02009;20% of genomes) were unique to the SAG or the MAGs. The case of BACL21 is particularly interesting since it contained two SAGs (the only cluster with more than one SAG) that differed substantially in size (1.0&#x000a0;Mb and 1.6&#x000a0;Mb; Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). The two SAGs together covered nearly the entire gene content of the corresponding MAG (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>).</p><p id="Par22">For the genomes that were placed in the six clusters, 16S rRNA genes were found in four out of seven SAGs (57%) and 19 out of 24 MAGs (79%), where the latter proportion is notably high. In comparison, analysis of 16S rRNA genes in all genomes showed that 11 out of 16 SAGs (69%) and 38 out of 83 MAGs (46%) contained 16S rRNA gene sequences. It is worth noting that the higher proportion of SAGs containing a 16S rRNA gene sequence in the complete dataset could reflect that the initial selection of SAGs for sequencing was mainly based on them containing a PCR-amplifiable 16S rRNA gene sequence. A lower proportion for MAGs could also be due to known issues with metagenome assembly and binning of sequences from 16S rRNA genes [<xref ref-type="bibr" rid="CR53">53</xref>].</p></sec><sec id="Sec5"><title>Analysis of functional gene data</title><p id="Par23">Despite the differences in genome sizes, the distribution of broad functional gene categories, as defined by Clusters of Orthologous Groups (COGs), was largely consistent within SAG and MAG clusters (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref>). Statistical analysis of the COG category distributions showed that the genomes clustered according to BACL (ANOSIM <italic>R</italic>&#x02009;=&#x02009;0.96; <italic>P</italic>&#x02009;=&#x02009;0.0001) but not significantly so according to genome type (i.e., SAG vs. MAG; ANOSIM <italic>R</italic>&#x02009;=&#x02009;0.21; <italic>P</italic>&#x02009;=&#x02009;0.06; Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2b</xref>). The distribution of COG categories also appeared to differ taxonomically (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2b</xref>). For instance, the COG category &#x0201c;Amino acid metabolism and transport&#x0201d; was more abundant in the cluster BACL10 (<italic>Rhodobacter</italic>) compared to other clusters. The <italic>Flavobacteria</italic> (BACL7, 21, and 22) showed elevated proportions of the functions &#x0201c;Cell wall/membrane/envelope-biogenesis&#x0201d; and &#x0201c;Translation.&#x0201d; &#x0201c;Lipid metabolism&#x0201d; was more frequent in the <italic>Gammaproteobacteria</italic> clusters (BACL1 and 16) compared to other clusters (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref>).<fig id="Fig2"><label>Fig. 2</label><caption><p>Distribution of functional categories in SAGs and MAGs. <bold>a</bold> Distribution of broad COG categories in the different genome clusters for MAGs and SAGs. The <italic>X</italic>-axis shows genomes grouped and ordered according to genome clusters. The <italic>Y</italic>-axis shows the percentage of genes in COG categories in each genome. <bold>b</bold> Non-metric multidimensional scaling (NMDS) plot based on counts of COG categories in the SAGs and MAGs</p></caption><graphic xlink:href="40168_2018_550_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec6"><title>Quantification of metagenome binning and assembly errors</title><p id="Par24">Since the SAGs contained genome regions not present in the MAGs (on average 78.9% of SAG genomes aligned with the corresponding MAG genomes), we investigated potential reasons for these regions to be missing in the MAGs. Accordingly, we determined the distribution of SAG sequencing reads mapping to different categories of metagenome contigs. This quantification showed that a median of 74.0% of the SAG reads mapped to the contigs in their corresponding MAG (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>). Other metagenome contigs which were included in the binning due to their lengths (&#x0003e;&#x02009;1&#x000a0;kb), but that had hence ended up in other bins, recruited far fewer reads (median 0.33%) (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>). These contigs were likely misplaced in the binning procedure and can be used to calculate an estimate for the false negative error of the binning. This was calculated as the number of nucleotide bases in these potentially misplaced contigs covered by SAG reads divided by the number of nucleotide bases covered by SAG reads in all contigs that were subject to binning&#x02014;this value averaged 3.6% (Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>: Table S3). The remaining SAG reads were either mapping to small contigs (1&#x000a0;kb), not included in the binning because they were too short (&#x0003c;&#x02009;1&#x000a0;kb) (median 11.6% of reads), or not mapping to metagenome contigs at all (median 10.3% of reads) (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3c</xref>, <xref rid="Fig3" ref-type="fig">d</xref>) and were hence rather reflecting insufficient metagenomic assembly or contaminations in the SAGs.<fig id="Fig3"><label>Fig. 3</label><caption><p>Distribution of SAG reads mapped against metagenome assemblies. Boxplot of the distribution of SAG reads mapped against the corresponding metagenome assemblies where each individual data point is jittered on top of each box. All reads for each SAG was mapped against the assembly associated with each matching MAG and thus positioned in exactly one out of these four categories. Only contigs longer than 1&#x000a0;kb were included in the binning, which is the reason to use it as a divider here</p></caption><graphic xlink:href="40168_2018_550_Fig3_HTML" id="MO3"/></fig></p></sec><sec id="Sec7"><title>SAG quality evaluation</title><p id="Par25">A potential explanation for the SAG specific content could be contaminating DNA in the SAGs [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR39">39</xref>]. In order to address this, we analyzed patterns of nucleotide composition and metagenome coverage for SAG contigs. A clear difference in tetranucleotide pattern was observed between the SAG contigs that were aligning and those that were not aligning with MAG contigs. The set of SAG contigs which did not align (&#x0003c;&#x02009;5% of bases) contained many outliers in the tetranucleotide PCA (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Figure S3, Additional&#x000a0;file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>: Figure S4, Additional&#x000a0;file&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref>: Figure S5, Additional&#x000a0;file&#x000a0;<xref rid="MOESM9" ref-type="media">9</xref>: Figure S6, Additional&#x000a0;file&#x000a0;<xref rid="MOESM10" ref-type="media">10</xref>: Figure S7, Additional&#x000a0;file&#x000a0;<xref rid="MOESM11" ref-type="media">11</xref>: Figure S8 and Additional&#x000a0;file&#x000a0;<xref rid="MOESM12" ref-type="media">12</xref>: Figure S9). This could potentially be due to the use of tetranucleotide patterns in the construction of MAGs and that these regions are falsely missing in the MAGs due to their atypical sequence composition. However, investigating the mapping of metagenome reads against the SAG contigs showed that the SAG contigs that were not aligning to MAGs and that displayed atypical tetranucleotide patterns also as a rule had significantly lower coverage in the metagenome (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Figure S3, Additional&#x000a0;file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>: Figure S4, Additional&#x000a0;file&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref>: Figure S5, Additional&#x000a0;file&#x000a0;<xref rid="MOESM9" ref-type="media">9</xref>: Figure S6, Additional&#x000a0;file&#x000a0;<xref rid="MOESM10" ref-type="media">10</xref>: Figure S7, Additional&#x000a0;file&#x000a0;<xref rid="MOESM11" ref-type="media">11</xref>: Figure S8 and Additional&#x000a0;file&#x000a0;<xref rid="MOESM12" ref-type="media">12</xref>: Figure S9). This substantially strengthens the hypothesis that these SAG contigs are due to contamination.</p><p id="Par26">During MDA, chimeric sequences, in particular inversions, can be generated [<xref ref-type="bibr" rid="CR38">38</xref>]. Using the SAG reads mapping against the metagenome, such chimeric reads were identified. Using conservative criteria, on average, 1.72% of the reads were identified as chimeric (Additional&#x000a0;file&#x000a0;<xref rid="MOESM13" ref-type="media">13</xref>: Table S4). The chimeric nature of these reads could potentially affect the mapping to the metagenome (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>), particularly by inflating the number of reads which did not map to any metagenome contig. However, the distribution of these chimeric reads among the categories of Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> did not differ from all reads (Additional&#x000a0;file&#x000a0;<xref rid="MOESM13" ref-type="media">13</xref>: Table S4). Hence, chimeric SAG reads did not bias the distribution of SAG reads mapping against the metagenome.</p><p id="Par27">The chimeric reads could potentially negatively impact the SAG assembly, because the chimeras could be propagated to the contigs formed in the assembly. Since a large majority of the chimeric reads (on average 96.9%, compared to 8.6% of other reads; Additional&#x000a0;file&#x000a0;<xref rid="MOESM14" ref-type="media">14</xref>: Table S5) aligned with more than 20 bases soft-clipped against the SAG contigs, this does not seem to generally be the case. The chimeric reads could also result in a fragmented assembly by introducing alternative, erroneous, paths in the assembly graph, leading to truncated contigs. If this would be the case, one would expect chimeric reads to be overrepresented among reads overlapping the ends of contigs. While chimeric reads were more often aligned over an edge of a contig compared to other reads, these reads only corresponded to on average 6.3% of the chimeric reads. Thus, chimeric reads do not seem to have had a substantial impact on the SAG assembly.</p><p id="Par28">From visual inspection of genome alignments, we discovered some MAG contigs aligning to multiple SAG contigs. This was caused by erroneously duplicated contig sequences within the SAGs, where the highest amount was found within the A11 SAG assembly. However, this issue was resolved when using the most recent version of the assembly software (Spades version 3.10.1 instead of version 3.5), tested on the A11 SAG (data not shown).</p></sec></sec><sec id="Sec8"><title>Discussion</title><p id="Par29">In this study, we compared the genome output from two state-of-the-art approaches for obtaining prokaryotic genomes representing abundant populations in the natural environment without cultivation. From a collection of SAGs and MAGs, we found an overlap in six clusters, representing a broad taxonomic range including <italic>Gammaproteobacteria</italic>, <italic>Bacteroidetes</italic>, and <italic>Alphaproteobacteria</italic> that were nearly identical between the two groups (average 99.51% sequence identity), verifying previous results with high average nucleotide identity between SAGs and MAGs [<xref ref-type="bibr" rid="CR43">43</xref>]. It is interesting to note that average nucleotide identity (ANI) is an important measure of the genomic level of relatedness in the taxonomy of prokaryotes [<xref ref-type="bibr" rid="CR54">54</xref>]. Moreover, Konstantinidis and Rosell&#x000f3;-M&#x000f3;ra [<xref ref-type="bibr" rid="CR55">55</xref>] state that &#x0201c;In general, two organisms sharing ANI values above 94&#x02013;96% may be considered as members of the same genospecies&#x0201d; citing the articles [<xref ref-type="bibr" rid="CR56">56</xref>] and [<xref ref-type="bibr" rid="CR57">57</xref>], and Varghese et al. [<xref ref-type="bibr" rid="CR58">58</xref>] found intra-species identities to range between 96.5 and 100% ANI. Thus, it appears that the matching SAGs and MAGs in our study are highly likely to represent the same genomic populations&#x02014;yet, this remains to be explored in detail in future phylogenomic analyses. Due to seasonal recurrence of bacterial populations in the waters studied here [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR49">49</xref>], a very high nucleotide identity (&#x0003e;&#x02009;98.7% in overlapping regions) could be achieved despite samples used for SAG sequencing and MAG construction were collected 1&#x000a0;year apart. From the relative abundance of matching data on specific bacterial populations (OTUs), we conclude that both approaches provide genomic information on abundant taxa in the natural environment.</p><p id="Par30">There are, however, differences between the two methods. When conducting sequencing of single-amplified genomes, one of the benefits is that the cells can be screened and the researcher can select particular cells to sequence, perhaps targeting a specific taxon or function. Furthermore, if one has only very few samples, producing SAGs may be preferable since the efficiency of the MAG approach improves with the number of samples [<xref ref-type="bibr" rid="CR28">28</xref>]. Similarly, the MAG approach has critical difficulties assembling closely related strains [<xref ref-type="bibr" rid="CR59">59</xref>] and the presence of multiple strains also inhibits accurate binning [<xref ref-type="bibr" rid="CR28">28</xref>]. Moreover, closely related strains that display a wide variation in genetic content may obtain different abundance patterns, since temporal dynamics may differ between core- and strain-specific parts of the genomes. SAGs also supply superior information on which nucleotide variants that co-occur within a genome (haplotypes), whereas for metagenomics, this information is limited to the read length, although computational approaches for haplotype reconstruction are emerging [<xref ref-type="bibr" rid="CR60">60</xref>]. Nevertheless, metagenome-assembled genomes do recover a higher percentage of the genome compared to SAGs. Also, since reads from many individuals of each population are being sampled, population genomic analysis can be performed using the metagenome data [<xref ref-type="bibr" rid="CR61">61</xref>&#x02013;<xref ref-type="bibr" rid="CR63">63</xref>], and additional information about the whole microbial community is obtained from the metagenome dataset, which is achieved with a more standard set of equipment compared to that needed for single-cell sequencing. Multiple samples are often beneficial for ecological investigations, making such projects suitable for MAG construction. Nevertheless, the fact that the genomes matched abundant OTUs with representatives from different taxonomic groups shows that both the SAG and the MAG approaches have a broad generality when applied to environmental samples.</p><sec id="Sec9"><title>Size of SAGs compared to MAGs</title><p id="Par31">The SAGs in this study were consistently smaller than the corresponding MAGs. This could be caused by either incomplete SAG assemblies or by metagenome contigs erroneously placed in MAGs by the binning algorithm. Looking closer at the case where two SAGs aligned to the same single MAG (i.e., BACL21), there was evidence that the smaller of the two SAGs (BS0038D2) was incomplete, i.e., it lacked a large fraction of genes that were shared by the second SAG and the MAG (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). Our results therefore support the first explanation, which has been previously observed [<xref ref-type="bibr" rid="CR39">39</xref>, <xref ref-type="bibr" rid="CR64">64</xref>, <xref ref-type="bibr" rid="CR65">65</xref>]. Combining the sections included in the SAGs would also cover a higher proportion of the MAG than any of the two SAGs did individually (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). Furthermore, MAGs showed a low level of redundancy (i.e., measured as duplicated SCGs) which would likely have been higher if MAGs contained a high degree of erroneously binned contigs. Finally, matching SAGs are also less complete than MAGs as estimated by presence of SCGs.</p><p id="Par32">The cause for incomplete SAGs could be either uneven or incomplete amplification of parts of the, typically, single-genome copy [<xref ref-type="bibr" rid="CR21">21</xref>]. The average sequencing depth was, however, one order of magnitude higher for the SAGs than the MAGs (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S1), and in most cases, the sequencing reads used were longer. The formation of chimeric reads is an additional problem potentially affecting the assembly quality of SAGs. Our analysis shows, however, that while chimeric reads are present, they are in most cases not aligning over the edges of SAG contigs (Additional&#x000a0;file&#x000a0;<xref rid="MOESM14" ref-type="media">14</xref>: Table S5). It therefore seems likely that the major causes for incomplete SAGs are other problems related to whole-genome amplification. Attempts to improve this method are ongoing [<xref ref-type="bibr" rid="CR66">66</xref>, <xref ref-type="bibr" rid="CR67">67</xref>], but alternatively, multiple SAGs from the same population can be sequenced for better coverage [<xref ref-type="bibr" rid="CR31">31</xref>, <xref ref-type="bibr" rid="CR37">37</xref>]. Even though the SAGs were smaller than MAGs, the analysis of COG categories within each matching SAG and MAG demonstrated that the two approaches capture the broad functional categories in a similar manner (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). This essentially indicates that a majority of functional genes in different categories are fairly evenly distributed across the genomes.</p></sec><sec id="Sec10"><title>Unique SAG sequences&#x02014;metagenome assembly problem or contamination?</title><p id="Par33">With the caveats that the whole-genome amplification of single cells generates uneven depth of coverage for different parts of the genome [<xref ref-type="bibr" rid="CR21">21</xref>], mapping SAG reads against the metagenomes allowed us to investigate how well the MAGs and the remaining metagenomes accounted for all SAG sequences (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>). SAG reads mapping to contigs included in the corresponding MAG accounted for the largest fraction for all pairs of MAGs and SAGs, confirming the completeness of the MAGs (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>). In the MAG assembly, only contigs longer than 1&#x000a0;kb were used as input to the binning, because short contigs are difficult to cluster correctly [<xref ref-type="bibr" rid="CR28">28</xref>]. Therefore, reads mapping to contigs which were longer than 1&#x000a0;kb, and thus subject to binning, but not included in the corresponding MAG (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>), likely indicated wrongly binned contigs or possibly indicated sequence variation between strains of the same population. A high rate of false negative binning errors would necessitate a high percentage of reads in this category. However, this was not observed (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>). Instead, the estimated false negative rate of the binning was low&#x02014;on average only 3.6% measured as number of genomic bases.</p><p id="Par34">In contrast, a significant portion of the SAG reads were placed in either of the two remaining categories: reads mapping to metagenome contigs shorter than 1&#x000a0;kb (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3c</xref>) or reads not mapping to any metagenome contig (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3d</xref>). This could potentially be due to that metagenome assembly failed to assemble true MAG sequences past the 1-kb cutoff used for binning. Improvements of metagenome assembly strategies have recently been made [<xref ref-type="bibr" rid="CR68">68</xref>, <xref ref-type="bibr" rid="CR69">69</xref>], possibly reducing the influence of this issue. Alternatively, these sequences could correspond to SAG contamination. While not easily quantified, our analysis showed clear presence of contaminating sequences within the SAGs (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Figure S3, Additional&#x000a0;file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>: Figure S4, Additional&#x000a0;file&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref>: Figure S5, Additional&#x000a0;file&#x000a0;<xref rid="MOESM9" ref-type="media">9</xref>: Figure S6, Additional&#x000a0;file&#x000a0;<xref rid="MOESM10" ref-type="media">10</xref>: Figure S7, Additional&#x000a0;file&#x000a0;<xref rid="MOESM11" ref-type="media">11</xref>: Figure S8 and Additional&#x000a0;file&#x000a0;<xref rid="MOESM12" ref-type="media">12</xref>: Figure S9). Contaminating sequences could either be introduced during the handling of samples in the lab [<xref ref-type="bibr" rid="CR39">39</xref>] or be present in the environmental samples as free DNA [<xref ref-type="bibr" rid="CR20">20</xref>]. An additional possibility is that some regions here identified as contamination of SAGs are instead true SAG sequences which are unique to the SAG genome in comparison to the MAG. Genome regions recently acquired through horizontal gene transfer are likely to have a different sequence composition [<xref ref-type="bibr" rid="CR70">70</xref>] which is also one of the criteria to identify SAG contamination.</p></sec><sec id="Sec11"><title>No significant core genome enrichment in MAGs</title><p id="Par35">A potential problem with binning using coverage variations over multiple samples is that strain-specific genes can have different abundance profiles than the core genome if multiple strains of the same species are present in the samples [<xref ref-type="bibr" rid="CR28">28</xref>]. Therefore strain-specific and core genes are at risk of being placed into different bins, and the use of single-copy core genes as an estimate of completeness would result in an overly optimistic measure for the core genome bin. If any of the MAGs would be artificially core-genome-enriched in the binning procedure, we would expect a large fraction of the SAG reads, in particular those corresponding to the non-core genome, to map to the long contigs that were not in the MAGs. This was however not the case, as only a very small fraction was detected (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>). These findings indicate that core genome enrichment in the construction of MAGs is a smaller problem than previously thought. However, the severity of this problem is likely dependent on the structure of the pangenome of the organism.</p></sec></sec><sec id="Sec12"><title>Conclusion</title><p id="Par36">Individual MAGs in this study were found to be larger and more complete than corresponding SAGs, although there is reason to believe that analysis of multiple SAGs from the same group of organisms could result in equal or higher completeness if jointly assembled. The false negative rate in the binning process was generally low. Single-cell technology offers the possibility of genome recovery from a single sample whereas the reconstruction of MAGs often requires multiple samples. This, on the other hand, provides ecological information based on the MAG abundance variations across samples. The strong agreement between the SAG and MAG methodologies emphasizes that both are accurate and that the choice of approach should depend on the research questions and on available resources.</p></sec><sec id="Sec13"><title>Methods</title><sec id="Sec14"><title>Generation of MAGs</title><p id="Par37">The MAGs used in the current study were obtained as previously described in Hugerth et al. [<xref ref-type="bibr" rid="CR23">23</xref>]. Briefly, bacterial community DNA for MAG construction was obtained from surface water (2&#x000a0;m) collected in the Baltic Sea on 37 time points between March and December 2012 at the Linnaeus Microbial Observatory (LMO) located ~&#x02009;11&#x000a0;km offshore K&#x000e5;rehamn, Sweden (56&#x000b0;55&#x02032;.51.24&#x02033; N 17&#x000b0;3&#x02032;38.52&#x02033; E). Library preparation of the bacterial community DNA was performed with the Rubicon ThruPlex kit (Rubicon Genomics, Ann Arbor, MI, USA) according to the instructions of the manufacturer, and finished libraries were sequenced on a HiSeq 2500 (Illumina Inc., San Diego, CA, USA) with paired-end reads of 2&#x02009;&#x000d7;&#x02009;100&#x000a0;bp at SciLifeLab/NGI (Solna, Sweden). On average, 31.9 million paired-end reads per sample were generated.</p><p id="Par38">Quality-controlled reads were assembled separately for each sample using a combination of Ray 2.1 (Ray Meta) [<xref ref-type="bibr" rid="CR71">71</xref>] and 454 Life Science&#x02019;s software Newbler (v.29; Roche, Basel, Switzerland). Bowtie2 [<xref ref-type="bibr" rid="CR72">72</xref>] was used to map all quality-controlled reads for each sample against the contigs. Contigs from each sample were then binned using CONCOCT [<xref ref-type="bibr" rid="CR28">28</xref>], an algorithm that clusters contigs into genomes across multiple samples, dependent on sample coverage and sequence composition using Gaussian mixture models. Bins were evaluated with a set of 36 single-copy genes presented in [<xref ref-type="bibr" rid="CR28">28</xref>] and approved if they contained at least 30 unique SCGs with a maximum of 2 in more than a single copy. Bins meeting these criteria were considered MAGs. It should be noted that metagenome assembly and metagenome binning softwares continuously evolve, which could potentially influence MAG construction. However, the CONCOCT algorithm has not changed since we applied it on these data, and CONCOCT is regarded a highly successful software for metagenome binning [<xref ref-type="bibr" rid="CR59">59</xref>, <xref ref-type="bibr" rid="CR73">73</xref>]. Two MAGs from different samples could correspond to the same organism, and therefore, the 83 MAGs were clustered using MUMmer [<xref ref-type="bibr" rid="CR74">74</xref>] into 30 Baltic Sea clusters (BACL). Functional analysis of each BACL was made with the PROKKA pipeline (v.1.7) [<xref ref-type="bibr" rid="CR75">75</xref>] and extended with annotation for COG categories [<xref ref-type="bibr" rid="CR76">76</xref>]. Taxonomic assignment for each MAG was firstly done with Phylosift [<xref ref-type="bibr" rid="CR77">77</xref>] and then complemented with complete or partial 16S rRNA genes identified in the MAGs with webMGA [<xref ref-type="bibr" rid="CR78">78</xref>].</p></sec><sec id="Sec15"><title>SAG sampling and single-cell sorting</title><p id="Par39">Samples for SAGs from the Baltic Sea were collected on 13 May 2013 at the Linnaeus Microbial Observatory and cryopreserved in 1&#x000d7; TE, 5% glycerol (final concentration) before arriving to the Microbial Single Cell Genomics facility, SciLifeLab, Uppsala University. Prior to sorting, the cryopreserved samples were thawed and diluted, before being stained with 1&#x000d7; (final concentration) SYBR Green I (Life Technologies, CA, USA) for approximately 30&#x000a0;min. The sorting was performed with a MoFlo Astrios EQ (Beckman Coulter, USA) cell sorter using a 488-nm laser for excitation, 70-&#x003bc;m nozzle, sheath pressure of 60&#x000a0;psi, and 1.3% sterile filtered NaCl as sheath fluid. Individual cells were deposited into 96-well plates (Bio-Rad, CA, USA) containing 1&#x000a0;&#x003bc;L of 1&#x000d7; TE using a CyClone&#x02122; robotic arm and the most stringent single-cell sort settings (single mode, 0.5 drop envelope). The sorter was triggered on forward scatter at a threshold of 0.08%, and sort regions were set on SYBR Green I fluorescence detected at 513&#x000a0;nm using a 40-nm bandpass filter.</p></sec><sec id="Sec16"><title>Whole-genome amplification using MDA with phi29</title><p id="Par40">Deposited cells were lysed and neutralized followed by whole-genome amplification using Phi29 and MDA as described by [<xref ref-type="bibr" rid="CR18">18</xref>]. In short, the cells were incubated in an alkaline solution at RT for 5&#x000a0;min. Lysis reactions were neutralized by adding 1&#x000a0;&#x003bc;L neutralization buffer (Qiagen, Germany). MDA was performed using the RepliPHI&#x02122; Phi29 Reagent set (0.1&#x000a0;&#x003bc;g/&#x003bc;L, RH04210, Epicenter, WI, USA) at 30&#x000a0;&#x000b0;C for 16&#x000a0;h in 15&#x000a0;&#x003bc;L reaction volumes with a final concentration of 1&#x000d7; reaction buffer, 0.4&#x000a0;mM dNTPs, 10&#x000a0;&#x003bc;M DTT, 5% DMSO, 50&#x000a0;&#x003bc;M hexamers with 3&#x02032;-phosphorothioate modifications (IDT Integrated DNA Technologies, IA, USA), 40&#x000a0;U Phi 29 enzyme, 0.5&#x000a0;&#x003bc;M SYTO13&#x000ae; (Life Technologies, CA, USA), and water. All reagents except SYTO13 were UV decontaminated at 2&#x02009;&#x000d7;&#x02009;0.5&#x000a0;J in a Biolinker. The whole-genome amplification was monitored in real time by detection of SYTO13 fluorescence every 15&#x000a0;min for 16&#x000a0;h using a Chromo4 real-time PCR instrument (Bio-Rad, CA, USA). The single amplified genome DNA was stored at &#x02212;&#x02009;20&#x000a0;&#x000b0;C until further PCR screening, library preparation, and Illumina sequencing.</p></sec><sec id="Sec17"><title>Screening of SAGs</title><p id="Par41">Positive SAGs, defined by an early amplification curve well separated from negative controls as well as a positive PCR product targeting the 16S rRNA gene, were diluted 20-fold and screened using primer pair Bact_341 F: 5&#x02032;-CCTACGGGNGGCWGCAG-3&#x02032; and Bact_805 R: 5&#x02032;- GACTACHVGGGTATCTAATCC-3&#x02032; [<xref ref-type="bibr" rid="CR47">47</xref>]. The reactions were performed in 20&#x000a0;&#x003bc;L reaction volume with 2&#x000a0;U of Taq DNA Polymerase recombinant (Thermo Fisher Scientific, MA, USA), 1&#x000d7; reaction buffer, 0.2&#x000a0;mM dNTPs, 2&#x000a0;mM MgCl<sub>2</sub>, and 0.25&#x000a0;&#x003bc;M of each primer. Following a 3-min denaturation at 95&#x000a0;&#x000b0;C, targets were amplified for 35&#x000a0;cycles of 95&#x000a0;&#x000b0;C for 30&#x000a0;s, 50&#x000a0;&#x000b0;C for 30&#x000a0;s, 72&#x000a0;&#x000b0;C for 60&#x000a0;s, and a final 10-min extension at 72&#x000a0;&#x000b0;C. PCR products were detected by an approximate 450-bp fragment on a 1.5% agarose gel. The products were purified using the NucleoSpin Gel and PCR clean-up purification kit (Macherey-Nagel, Germany), quantified using the Quant-iT &#x02122; PicoGreen&#x000ae; dsDNA assay kit (Invitrogen, MA, USA) in a FLUOstar&#x000ae; Omega microplate reader (BMG Labtech, Germany) and submitted for identification by Sanger sequencing at the Uppsala Genome Center.</p></sec><sec id="Sec18"><title>Illumina MiSeq sequencing</title><p id="Par42">Altogether, 15 SAGs were selected for genome sequencing. Twelve of these generated a 16S rRNA sequence identified by Sanger sequencing and were selected to cover a broad range of phylogenetic groups. Three additional SAGs did not generate any 16S rRNA amplicons with the indicated primers but were nevertheless selected to include also lineages not targeted by bacterial primers.</p><p id="Par43">The DNA content of the SAGs was quantified with the Quant-iT &#x02122; PicoGreen&#x000ae; dsDNA assay kit and subsequently diluted to a concentration of 0.2&#x000a0;ng/&#x003bc;L as recommended for the Nextera XT Library Preparation kit (Illumina, CA, USA). Procedures were according to instructions from the manufacturer except that normalization was performed using the Kapa qPCR quantification method instead of bead normalization. In short, the Nextera XT uses an enzymatic step for fragmentation of DNA which enables small quantities of input DNA. The protocol involves a PCR amplification step where indexes and additional required nucleotide sequences are incorporated. After PCR cleanup, the library for each SAG was quantified and handed in for individual quality control at the SciLifeLab SNP&#x00026;SEQ facility. The quality of the libraries was evaluated using the TapeStation from Agilent Technologies with the D1000 ScreenTape. The sequencing libraries were quantified by qPCR using the library quantification kit for Illumina (KAPA Biosystems, MA, USA) on a StepOnePlus instrument (Applied Biosystems, CA, USA) and pooled in equal amounts prior to cluster generation and sequencing on a single MiSeq run with V3 chemistry and 2&#x02009;&#x000d7;&#x02009;300&#x000a0;bp mode.</p><p id="Par44">One additional SAG (A11) from the same sample but from another sorted plate was purified using the NucleoSpin Tissue purification kit (Macherey-Nagel, Germany) and handed in directly to the SNPseq sequencing facility for preparation using the TruSeq Nano DNA library kit (Illumina, CA, USA) and thereafter sequenced in another MiSeq V3 2&#x02009;&#x000d7;&#x02009;300&#x000a0;bp run.</p></sec><sec id="Sec19"><title>Data analysis of sequenced libraries</title><p id="Par45">The global quality of raw and trimmed reads was checked using Fastqc 0.11 [<xref ref-type="bibr" rid="CR79">79</xref>], and low-quality data was removed together with adapters using Cutadapt 1.7 [<xref ref-type="bibr" rid="CR80">80</xref>], requiring a minimal length of 75 nucleotides and using a quality of 30 as the threshold. The trimmed reads were assembled using the default values for single cell (<italic>--sc</italic>) with SPAdes 3.5 [<xref ref-type="bibr" rid="CR81">81</xref>] and the parameter <italic>careful</italic>, which, according to the documentation, reduces the number of mismatches and short indels in contigs. The quality of each of the assemblies was assessed using the software QUAST 2.3 [<xref ref-type="bibr" rid="CR82">82</xref>].</p></sec><sec id="Sec20"><title>Comparative genomics analyses</title><p id="Par46">Mash version 1.0.1 [<xref ref-type="bibr" rid="CR51">51</xref>] with 100,000 15-mers for each SAG and MAG was used to calculate pairwise distances between all genomes. Single-linkage clustering was then performed using Scipy [<xref ref-type="bibr" rid="CR83">83</xref>] and visualized using matplotlib [<xref ref-type="bibr" rid="CR84">84</xref>] (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1). Clustering cutoff for each BACL was set at 0.1 (90% estimated similarity), and in each cluster containing a combination of MAGs and SAGs, they were pairwise aligned using the dnadiff tool from the Mummer suite version 3.23 [<xref ref-type="bibr" rid="CR74">74</xref>]. Since Mash only gives an estimation of the nucleotide distance, we also subjected two additional clusters just over the 10% dissimilarity limit (BACL24 and BACL30) for alignment with MUMmer. Out of these, BACL30 resulted in the best alignment at 96.5% identity and alignment rate of the SAG at 53.7%. However, none of these two clusters were included in the comparison. The numbers assigned to the clusters correspond to the original MAG BACLs used in [<xref ref-type="bibr" rid="CR23">23</xref>]. None of the SAGs or MAGs was closely related to complete genomes available through the newly developed Genome Taxonomy Database (<ext-link ext-link-type="uri" xlink:href="http://gtdb.ecogenomic.org/">http://gtdb.ecogenomic.org/</ext-link>). We only found some matches to non-SAG/non-MAG genomes for BACL16. The matches of the BACL16 120322 MAG to the genomes of the two bacterial strains MOLA455 and HTCC2207 were less than 2% and 4% of the aligned bases, respectively (determined using MUMmer/dnadiff); the sequence identity was &#x0003c;&#x02009;83% across the aligned regions.</p><p id="Par47">Following the same procedure as [<xref ref-type="bibr" rid="CR23">23</xref>], the SAGs were gene annotated using the PROKKA pipeline [<xref ref-type="bibr" rid="CR75">75</xref>] and complemented with all significant (<italic>e</italic> value &#x0003c;&#x02009;0.00001) COG annotations using rpsblast from BLAST+ version 2.2.28+ [<xref ref-type="bibr" rid="CR85">85</xref>]. Non-metric multidimensional scaling (NMDS) and ANOSIM analysis was based on counts of COG categories in the genomes, running the ANOSIM with 99,999 permutations. The pairwise genome distances for these analyses were calculated using Poisson dissimilarity [<xref ref-type="bibr" rid="CR86">86</xref>] with the PoiClaClu package, and NMDS and ANOSIM were conducted with the Vegan package, in <italic>R</italic> (<ext-link ext-link-type="uri" xlink:href="https://www.r-project.org/">www.r-project.org</ext-link>). Using the Anvi&#x02019;o (Docker image with version 2.1.0) pangenomic workflow [<xref ref-type="bibr" rid="CR52">52</xref>, <xref ref-type="bibr" rid="CR87">87</xref>] separately for each genome cluster, gene homologs were identified and visualized and estimates of completeness and redundancy were obtained using the MCL algorithm [<xref ref-type="bibr" rid="CR88">88</xref>], prodigal [<xref ref-type="bibr" rid="CR89">89</xref>], hmmer [<xref ref-type="bibr" rid="CR90">90</xref>], and 139 bacterial single-copy genes (SCGs) defined by [<xref ref-type="bibr" rid="CR91">91</xref>]. The summary statistics produced by Anvi&#x02019;o are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>: Table S2.</p><p id="Par48">SAG reads corrected during the assembly process [<xref ref-type="bibr" rid="CR81">81</xref>] that mapped to the SAG genome itself (minimum 99.55%) were mapped using Bowtie2 (version 2.2.6 with the --local argument) [<xref ref-type="bibr" rid="CR72">72</xref>] against the assembled metagenome samples from which the MAGs were obtained. The resulting BAM-files were sorted using Samtools version 1.3 [<xref ref-type="bibr" rid="CR92">92</xref>], duplicates were removed with Picard version 1.118, and the number of mapped reads per contig was counted (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>). Metagenomic contigs were divided into three groups: contigs included in the correct MAG, long (&#x02265;&#x02009;1&#x000a0;kb) contigs included in the binning but not belonging to the correct MAG, and short (&#x0003c;&#x02009;1&#x000a0;kb) contigs not included in the binning. Additionally, there were those reads that did not map to the metagenome assembly at all. The counts were summarized and visualized using Pandas [<xref ref-type="bibr" rid="CR93">93</xref>] and Seaborn [<xref ref-type="bibr" rid="CR94">94</xref>].</p><p id="Par49">Duplicated elements in the genomes were identified with BLASTN version 2.2.28+ [<xref ref-type="bibr" rid="CR85">85</xref>] as alignments longer than 0.1&#x000a0;kb between contigs longer than 1&#x000a0;kb and with 100% nucleotide identity. Reassembly of A11 was done using the corrected reads from existing assembly as input to Spades version 3.10.1 run in single-cell mode.</p></sec><sec id="Sec21"><title>Prevalence of 16S rRNA gene sequences in SAGs and MAGs and seasonal occurrence</title><p id="Par50">Twelve out of the 16 single-amplified genomes had 16S rRNA genes identified through Sanger sequencing as described above. However, four SAGs (A11, BS0038A02, BS0038A08, and BS0038A11) seemed to lack 16S rRNA gene sequence data and were therefore investigated with Barrnap (version 0.8) [<xref ref-type="bibr" rid="CR95">95</xref>]. Barrnap identified the 16S rRNA gene in SAG A11 and this sequence was taxonomically investigated using the SINA/SILVA database [<xref ref-type="bibr" rid="CR96">96</xref>]. Barrnap was also applied to all SAGs and MAGs to compare the presence of 16S rRNA genes in the genomes.</p><p id="Par51">To obtain a taxonomic annotation for the three remaining SAGs without 16S rRNA genes (BS0038A02, BS0038A08, and BS0038A11), we investigated their good quality contigs with a minimum length of 1&#x000a0;kb and kmer coverage (provided by Spades) of at least 11. Prodigal 2.6.1 [<xref ref-type="bibr" rid="CR89">89</xref>] was then used to predict coding regions in the selected contigs and predicted proteins were aligned against NCBI nucleotide and NCBI non-redundant database using BLAST (standalone BLAST + package version 2.2.30) [<xref ref-type="bibr" rid="CR85">85</xref>].</p><p id="Par52">To investigate the presence in the Baltic Sea of the 13 SAGs having a 16S rRNA gene, we individually blasted the sequences to a 16S rRNA gene amplicon dataset from a field study at the LMO station [<xref ref-type="bibr" rid="CR49">49</xref>] using online BLASTN [<xref ref-type="bibr" rid="CR97">97</xref>]. The seasonal dynamics were then explored by comparing the matching SAG/MAG clusters from 2012 (i.e., BACLs from Hugerth et al. 2015 [<xref ref-type="bibr" rid="CR23">23</xref>]) to the corresponding OTU in 2011 (i.e., Lindh et al. 2015 [<xref ref-type="bibr" rid="CR49">49</xref>]).</p></sec><sec id="Sec22"><title>Analysis of contamination of SAGs and chimeric reads</title><p id="Par53">The presence of contamination within SAGs was visually estimated through a tetranucleotide nucleotide composition PCA. The PCA was performed on all contigs from each individual SAG, but for visualization, the contigs were separated into two sets. One set contained contigs which aligned with less than 5% of their lengths to their corresponding MAG and the other set contained all other contigs (which did align to the corresponding MAG). When more than one MAG were in the same cluster, the union of all aligning SAG bases was used. To make the contamination detection less dependent of the MAGs, the contigs were also colored according to the number of metagenome reads mapping to them. The average metagenome coverage was estimated by assuming a length of 100 bases for each metagenome read. For a clearer visualization, the high coverage values were adjusted so that the maximum value was only three times the median value. The density plots were constructed using Pandas [<xref ref-type="bibr" rid="CR93">93</xref>].</p><p id="Par54">To identify chimeric SAG reads that contain inversions, the SAG read mappings against the metagenomes were investigated. Reads were first flagged as potentially chimeric if they mapped with at least 20 soft-clipped bases (as marked with S in the SAM-file cigar string) against any of the metagenome samples where matching MAGs had been obtained. Furthermore, the remaining matching region of the read was required to correspond to at least half of the read length and contain no more than two mismatches. This rather strict requirement was enforced to minimize the risk that the mapping was not to the intended organism. Finally, the list of potentially chimeric reads from all matching metagenome samples was combined, deduplicated, and filtered to remove reads which mapped in a non-chimeric fashion in any sample. We defined a non-chimeric mapping to contain alignment of at least 95% of the read length and to contain less than five mismatches within this region.</p><p id="Par55">The effect of chimeric reads was evaluated on both SAG assembly and on statistics for mapping SAG reads against metagenome. For all mapping files, the distribution of the chimeric reads was evaluated based on whether they were soft clipped and whether their alignment was overlapping a contig edge. A soft-clipped alignment was defined as containing at least 20 clipped bases. Furthermore, for mapping files of SAG reads against the metagenome samples, categories were defined based on the metagenome contigs in analogy to Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>. A metagenomic contig was either shorter than 1&#x000a0;kb, longer than 1&#x000a0;kb but not contained within the focal MAG, or part of the MAG in question. These categories were used to investigate the distribution of mapping SAG reads based on the metagenome contig they mapped against.</p></sec></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec23"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="40168_2018_550_MOESM1_ESM.xlsx"><label>Additional file 1:</label><caption><p><bold>Table S1.</bold> Assembly statistics and taxonomy for all MAGs and SAGs. For MAGs, &#x0201c;Coverage within sample&#x0201d; indicates that coverage was calculated based on the sample from where it was assembled. (XLSX 14 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="40168_2018_550_MOESM2_ESM.pdf"><label>Additional file 2:</label><caption><p><bold>Figure S1.</bold> Hierarchical single-linkage clustering of SAGs and MAGs based on distances generated by MASH. Genome names starting with &#x0201c;BACL&#x0201d; indicate MAGs and the number following indicates the Baltic Sea cluster. Leaves joined by nodes within a distance of 0.10 are grouped by color of their leftmost branches. (PDF 71 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="40168_2018_550_MOESM3_ESM.pdf"><label>Additional file 3:</label><caption><p><bold>Figure S2.</bold> Abundances over the years 2011 and 2012 for OTUs matching clusters of SAGs and MAGs. Redrawn from references Hugerth et al. and Lindh et al. [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR49">49</xref>]. (PDF 188 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="40168_2018_550_MOESM4_ESM.xlsx"><label>Additional file 4:</label><caption><p><bold>Table S2.</bold> Summary statistics as given by Anvi&#x02019;o for all MAGs and SAGs found by both approaches. (XLSX 23 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="40168_2018_550_MOESM5_ESM.xlsx"><label>Additional file 5:</label><caption><p><bold>Table S3.</bold> Distribution of metagenome bases covered by SAG reads mapped against the corresponding metagenome assemblies. &#x0201c;Estimated False Negative Rate in Binning (%)&#x0201d; was calculated by dividing the number of &#x0201c;Bases covered within long (&#x02265;&#x02009;1 kb) non-MAG contigs&#x0201d; with the number of &#x0201c;Nucleotide bases covered within MAG contigs.&#x0201d; (XLSX 10 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="40168_2018_550_MOESM6_ESM.pdf"><label>Additional file 6:</label><caption><p><bold>Figure S3.</bold> Tetranucleotide frequency plots of SAG BS0038H10 in BACL1. Nucleotide composition PCAs (a,b) and metagenome coverage estimate density plots (c,d) for contigs separated on alignment rate (&#x0003c;&#x02009;5% of bases: a,c; &#x02265;5% of bases: b,d) against the corresponding MAG. The color of the circles in panels a and b corresponds to the average metagenome coverage and the size of the circles corresponds to the contig sizes. Metagenome average coverage depth was estimated by assuming all mapping reads were 100 bases long. Furthermore, for clarity, the maximum value for the average coverage depth has been set to three times the median. (PDF 144 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="40168_2018_550_MOESM7_ESM.pdf"><label>Additional file 7:</label><caption><p><bold>Figure S4.</bold> Tetranucleotide frequency plots of SAG A11 in BACL7. Other figure legend information same as in Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: <bold>Figure S3.</bold> (PDF 192 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="40168_2018_550_MOESM8_ESM.pdf"><label>Additional file 8:</label><caption><p><bold>Figure S5.</bold> Tetranucleotide frequency plots of SAG BS0038D5 in BACL10. Other figure legend information same as in Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: <bold>Figure S3.</bold> (PDF 236 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="40168_2018_550_MOESM9_ESM.pdf"><label>Additional file 9:</label><caption><p><bold>Figure S6.</bold> Tetranucleotide frequency plots of SAG BS0038E9 in BACL16. Other figure legend information same as in Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: <bold>Figure S3.</bold> (PDF 100 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM10"><media xlink:href="40168_2018_550_MOESM10_ESM.pdf"><label>Additional file 10:</label><caption><p><bold>Figure S7.</bold> Tetranucleotide frequency plots of SAG BS0038D2 in BACL21. Other figure legend information same as in Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: <bold>Figure S3.</bold> (PDF 158 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM11"><media xlink:href="40168_2018_550_MOESM11_ESM.pdf"><label>Additional file 11:</label><caption><p><bold>Figure S8.</bold> Tetranucleotide frequency plots of SAG BS0038D11 in BACL21. Other figure legend information same as in Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: <bold>Figure S3.</bold> (PDF 87 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM12"><media xlink:href="40168_2018_550_MOESM12_ESM.pdf"><label>Additional file 12:</label><caption><p><bold>Figure S9.</bold> Tetranucleotide frequency plots of SAG BS0038A11 in BACL22. Other figure legend information same as in Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: <bold>Figure S3.</bold> (PDF 141 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM13"><media xlink:href="40168_2018_550_MOESM13_ESM.xlsx"><label>Additional file 13:</label><caption><p><bold>Table S4.</bold> Statistics for chimeric SAG reads mapping against metagenome contigs. (XLSX 14 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM14"><media xlink:href="40168_2018_550_MOESM14_ESM.xlsx"><label>Additional file 14:</label><caption><p><bold>Table S5.</bold> Statistics for chimeric SAG reads and other SAG reads mapping against SAG contigs. (XLSX 11 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>BACL</term><def><p id="Par4">Baltic Sea cluster</p></def></def-item><def-item><term>COG</term><def><p id="Par5">Clusters of Orthologous Groups</p></def></def-item><def-item><term>LMO</term><def><p id="Par6">Linnaeus Microbial Observatory</p></def></def-item><def-item><term>MAG</term><def><p id="Par7">Metagenome-assembled genome</p></def></def-item><def-item><term>Mbp</term><def><p id="Par8">Million base pairs</p></def></def-item><def-item><term>OTU</term><def><p id="Par9">Operational taxonomic unit</p></def></def-item><def-item><term>SAG</term><def><p id="Par10">Single-amplified genome</p></def></def-item></def-list></glossary><ack><title>Acknowledgements</title><p>We thankfully acknowledge Anders M&#x000e5;nsson and Kristofer Bergstr&#x000f6;m for their sampling at sea and Sabina Arnautovic for the skillful support in the laboratory. We thank the National Genomics Infrastructure sequencing platforms at the Science for Life Laboratory at Uppsala University, a national infrastructure supported by the Swedish Research Council (VR-RFI) and the Knut and Alice Wallenberg Foundation. We would also like to thank the SciLifeLab Microbial Single Cell Genomics Facility at Uppsala University where the single cell genomics efforts were carried out. Computational resources, including support, were supplied through UPPMAX: Uppsala Multidisciplinary Center for Advanced Computational Science, for which we are very grateful.</p><sec id="FPar1"><title>Funding</title><p id="Par56">This work was supported by grants from the Swedish Research Council VR to J.P. (grant no. 2011-4369 and 2015-04254) and to A.F.A. (grant no. 2011-5689). This research was also funded by the BONUS BLUEPRINT project that was supported by BONUS (Art 185); funded jointly by the EU and the Swedish Research Council FORMAS (to J.P. and A.F.A.); funded by grants of the European Research Council (ERC Starting grant 310039-PUZZLE_CELL), the Swedish Foundation for Strategic Research (SSF-FFL5), and the Swedish Research Council VR (grant 2015-04959) to T.J.G.E.; and funded by Uppsala University SciLifeLab SFO funds.</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p id="Par57">The single-amplified genome sequence dataset generated during the current study is available in the EMBL-EBI European Nucleotide Archive repository, under the primary accession PRJEB21451. The metagenomic reads dataset analyzed in the current study are previously published [<xref ref-type="bibr" rid="CR23">23</xref>] and are available on the sequence read archive under the accession SRP058493, <ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/bioproject/PRJNA273799">https://www.ncbi.nlm.nih.gov/bioproject/PRJNA273799</ext-link>.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>AFA and JP conceived the study. JA, CMGK, AFA, and JP designed the research, analyzed data, and wrote the paper. JA, CMGK, A-MD, CB, FH, MVL, LWH, TJGE, and SB performed the research. All authors read and approved the final manuscript.</p></notes><notes notes-type="COI-statement"><sec id="FPar3"><title>Ethics approval and consent to participate</title><p>Not applicable</p></sec><sec id="FPar4"><title>Consent for publication</title><p>Not applicable</p></sec><sec id="FPar5"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="FPar6"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Reddy</surname><given-names>T</given-names></name><name><surname>Thomas</surname><given-names>AD</given-names></name><name><surname>Stamatis</surname><given-names>D</given-names></name></person-group><article-title>The Genomes OnLine Database (GOLD) v. 5: a metadata management system based on a four level (meta) genome project classification</article-title><source>Nucleic acids Res.</source><year>2014</year><volume>43</volume><issue>Database issue</issue><fpage>D1099</fpage><lpage>D1106</lpage><?supplied-pmid 25348402?><pub-id pub-id-type="pmid">25348402</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mukherjee</surname><given-names>S</given-names></name><name><surname>Stamatis</surname><given-names>D</given-names></name><name><surname>Bertsch</surname><given-names>J</given-names></name><name><surname>Ovchinnikova</surname><given-names>G</given-names></name><name><surname>Verezemska</surname><given-names>O</given-names></name><name><surname>Isbandi</surname><given-names>M</given-names></name><etal/></person-group><article-title>Genomes OnLine Database (GOLD) v.6: data updates and feature enhancements</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>45</volume><fpage>gkw992</fpage></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bult</surname><given-names>CJ</given-names></name><name><surname>White</surname><given-names>O</given-names></name><name><surname>Olsen</surname><given-names>GJ</given-names></name><name><surname>Zhou</surname><given-names>L</given-names></name><name><surname>Fleischmann</surname><given-names>RD</given-names></name><name><surname>Sutton</surname><given-names>GG</given-names></name><etal/></person-group><article-title>Complete genome sequence of the methanogenic archaeon, <italic>Methanococcus jannaschii</italic></article-title><source>Science.</source><year>1996</year><volume>273</volume><fpage>1058</fpage><lpage>1073</lpage><pub-id pub-id-type="doi">10.1126/science.273.5278.1058</pub-id><pub-id pub-id-type="pmid">8688087</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>D</given-names></name><name><surname>Hugenholtz</surname><given-names>P</given-names></name><name><surname>Mavromatis</surname><given-names>K</given-names></name><name><surname>Pukall</surname><given-names>R</given-names></name><name><surname>Dalin</surname><given-names>E</given-names></name><name><surname>Ivanova</surname><given-names>NN</given-names></name><etal/></person-group><article-title>A phylogeny-driven genomic encyclopaedia of bacteria and archaea</article-title><source>Nature.</source><year>2009</year><volume>462</volume><fpage>1056</fpage><lpage>1060</lpage><pub-id pub-id-type="doi">10.1038/nature08656</pub-id><pub-id pub-id-type="pmid">20033048</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Craig Venter</surname><given-names>J</given-names></name><name><surname>Adams</surname><given-names>MD</given-names></name><name><surname>Myers</surname><given-names>EW</given-names></name><name><surname>Li</surname><given-names>PW</given-names></name><name><surname>Mural</surname><given-names>RJ</given-names></name><name><surname>Sutton Granger</surname><given-names>G</given-names></name><etal/></person-group><article-title>The sequence of the human genome</article-title><source>Science.</source><year>2001</year><volume>291</volume><fpage>1304</fpage><lpage>1351</lpage><pub-id pub-id-type="doi">10.1126/science.1058040</pub-id><pub-id pub-id-type="pmid">11181995</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Turnbaugh</surname><given-names>PJ</given-names></name><name><surname>Ley</surname><given-names>RE</given-names></name><name><surname>Hamady</surname><given-names>M</given-names></name><name><surname>Fraser-Liggett</surname><given-names>CM</given-names></name><name><surname>Knight</surname><given-names>R</given-names></name><name><surname>Gordon</surname><given-names>JI</given-names></name></person-group><article-title>The human microbiome project</article-title><source>Nature.</source><year>2007</year><volume>449</volume><fpage>804</fpage><lpage>810</lpage><pub-id pub-id-type="doi">10.1038/nature06244</pub-id><pub-id pub-id-type="pmid">17943116</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nemergut</surname><given-names>DR</given-names></name><name><surname>Costello</surname><given-names>EK</given-names></name><name><surname>Hamady</surname><given-names>M</given-names></name><name><surname>Lozupone</surname><given-names>C</given-names></name><name><surname>Jiang</surname><given-names>L</given-names></name><name><surname>Schmidt</surname><given-names>SK</given-names></name><etal/></person-group><article-title>Global patterns in the biogeography of bacterial taxa</article-title><source>Environ Microbiol</source><year>2011</year><volume>13</volume><fpage>135</fpage><lpage>144</lpage><pub-id pub-id-type="doi">10.1111/j.1462-2920.2010.02315.x</pub-id><pub-id pub-id-type="pmid">21199253</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sunagawa</surname><given-names>S</given-names></name><name><surname>Coelho</surname><given-names>LP</given-names></name><name><surname>Chaffron</surname><given-names>S</given-names></name><name><surname>Kultima</surname><given-names>JR</given-names></name><name><surname>Labadie</surname><given-names>K</given-names></name><name><surname>Salazar</surname><given-names>G</given-names></name><etal/></person-group><article-title>Ocean plankton. Structure and function of the global ocean microbiome</article-title><source>Science</source><year>2015</year><volume>348</volume><fpage>1261359</fpage><pub-id pub-id-type="doi">10.1126/science.1261359</pub-id><pub-id pub-id-type="pmid">25999513</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Durham</surname><given-names>BP</given-names></name><name><surname>Sharma</surname><given-names>S</given-names></name><name><surname>Luo</surname><given-names>H</given-names></name><name><surname>Smith</surname><given-names>CB</given-names></name><name><surname>Amin</surname><given-names>SA</given-names></name><name><surname>Bender</surname><given-names>SJ</given-names></name><etal/></person-group><article-title>Cryptic carbon and sulfur cycling between surface ocean plankton</article-title><source>Proc Natl Acad Sci USA</source><year>2015</year><volume>112</volume><fpage>453</fpage><lpage>457</lpage><pub-id pub-id-type="doi">10.1073/pnas.1413137112</pub-id><pub-id pub-id-type="pmid">25548163</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kanehisa</surname><given-names>M</given-names></name><name><surname>Sato</surname><given-names>Y</given-names></name><name><surname>Kawashima</surname><given-names>M</given-names></name><name><surname>Furumichi</surname><given-names>M</given-names></name><name><surname>Tanabe</surname><given-names>M</given-names></name></person-group><article-title>KEGG as a reference resource for gene and protein annotation</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><fpage>D457</fpage><lpage>D462</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1070</pub-id><pub-id pub-id-type="pmid">26476454</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Choi</surname><given-names>J</given-names></name><name><surname>Yang</surname><given-names>F</given-names></name><name><surname>Stepanauskas</surname><given-names>R</given-names></name><name><surname>Cardenas</surname><given-names>E</given-names></name><name><surname>Garoutte</surname><given-names>A</given-names></name><name><surname>Williams</surname><given-names>R</given-names></name><etal/></person-group><article-title>Strategies to improve reference databases for soil microbiomes</article-title><source>ISME J</source><year>2016</year><volume>11</volume><fpage>829</fpage><lpage>834</lpage><pub-id pub-id-type="doi">10.1038/ismej.2016.168</pub-id><pub-id pub-id-type="pmid">27935589</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Whitman</surname><given-names>WB</given-names></name><name><surname>Coleman</surname><given-names>DC</given-names></name><name><surname>Wiebe</surname><given-names>WJ</given-names></name></person-group><article-title>Prokaryotes: the unseen majority</article-title><source>Proc Natl Acad Sci USA</source><year>1998</year><volume>95</volume><fpage>6578</fpage><lpage>6583</lpage><pub-id pub-id-type="doi">10.1073/pnas.95.12.6578</pub-id><pub-id pub-id-type="pmid">9618454</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Falkowski</surname><given-names>PG</given-names></name><name><surname>Fenchel</surname><given-names>T</given-names></name><name><surname>Delong</surname><given-names>EF</given-names></name></person-group><article-title>The microbial engines that drive Earth&#x02019;s biogeochemical cycles</article-title><source>Science.</source><year>2008</year><volume>320</volume><fpage>1034</fpage><lpage>1039</lpage><pub-id pub-id-type="doi">10.1126/science.1153213</pub-id><pub-id pub-id-type="pmid">18497287</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Amann</surname><given-names>RI</given-names></name><name><surname>Ludwig</surname><given-names>W</given-names></name><name><surname>Schleifer</surname><given-names>KH</given-names></name></person-group><article-title>Phylogenetic identification and in situ detection of individual microbial cells without cultivation</article-title><source>Microbiol Rev.</source><year>1995</year><volume>59</volume><fpage>143</fpage><lpage>169</lpage><?supplied-pmid 7535888?><pub-id pub-id-type="pmid">7535888</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>K</given-names></name><name><surname>Martiny</surname><given-names>AC</given-names></name><name><surname>Reppas</surname><given-names>NB</given-names></name><name><surname>Barry</surname><given-names>KW</given-names></name><name><surname>Malek</surname><given-names>J</given-names></name><name><surname>Chisholm</surname><given-names>SW</given-names></name><etal/></person-group><article-title>Sequencing genomes from single cells by polymerase cloning</article-title><source>Nat Biotechnol.</source><year>2006</year><volume>24</volume><fpage>680</fpage><lpage>686</lpage><pub-id pub-id-type="doi">10.1038/nbt1214</pub-id><pub-id pub-id-type="pmid">16732271</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Woyke</surname><given-names>T</given-names></name><name><surname>Tighe</surname><given-names>D</given-names></name><name><surname>Mavromatis</surname><given-names>K</given-names></name><name><surname>Clum</surname><given-names>A</given-names></name><name><surname>Copeland</surname><given-names>A</given-names></name><name><surname>Schackwitz</surname><given-names>W</given-names></name><etal/></person-group><article-title>One bacterial cell, one complete genome</article-title><source>PLoS One.</source><year>2010</year><volume>5</volume><fpage>e10314</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0010314</pub-id><pub-id pub-id-type="pmid">20428247</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Landry</surname><given-names>ZC</given-names></name><name><surname>Giovanonni</surname><given-names>SJ</given-names></name><name><surname>Quake</surname><given-names>SR</given-names></name><name><surname>Blainey</surname><given-names>PC</given-names></name></person-group><article-title>Optofluidic cell selection from complex microbial communities for single-genome analysis</article-title><source>Methods Enzymol.</source><year>2013</year><volume>531</volume><fpage>61</fpage><lpage>90</lpage><pub-id pub-id-type="doi">10.1016/B978-0-12-407863-5.00004-6</pub-id><pub-id pub-id-type="pmid">24060116</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rinke</surname><given-names>C</given-names></name><name><surname>Lee</surname><given-names>J</given-names></name><name><surname>Nath</surname><given-names>N</given-names></name><name><surname>Goudeau</surname><given-names>D</given-names></name><name><surname>Thompson</surname><given-names>B</given-names></name><name><surname>Poulton</surname><given-names>N</given-names></name><etal/></person-group><article-title>Obtaining genomes from uncultivated environmental microorganisms using FACS-based single-cell genomics</article-title><source>Nat Protoc.</source><year>2014</year><volume>9</volume><fpage>1038</fpage><lpage>1048</lpage><pub-id pub-id-type="doi">10.1038/nprot.2014.067</pub-id><pub-id pub-id-type="pmid">24722403</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Navin</surname><given-names>N</given-names></name><name><surname>Kendall</surname><given-names>J</given-names></name><name><surname>Troge</surname><given-names>J</given-names></name><name><surname>Andrews</surname><given-names>P</given-names></name><name><surname>Rodgers</surname><given-names>L</given-names></name><name><surname>McIndoo</surname><given-names>J</given-names></name><etal/></person-group><article-title>Tumour evolution inferred by single-cell sequencing</article-title><source>Nature.</source><year>2011</year><volume>472</volume><fpage>90</fpage><lpage>94</lpage><pub-id pub-id-type="doi">10.1038/nature09807</pub-id><pub-id pub-id-type="pmid">21399628</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Marcy</surname><given-names>Y</given-names></name><name><surname>Ouverney</surname><given-names>C</given-names></name><name><surname>Bik</surname><given-names>EM</given-names></name><name><surname>Losekann</surname><given-names>T</given-names></name><name><surname>Ivanova</surname><given-names>N</given-names></name><name><surname>Martin</surname><given-names>HG</given-names></name><etal/></person-group><article-title>Dissecting biological &#x0201c;dark matter&#x0201d; with single-cell genetic analysis of rare and uncultivated TM7 microbes from the human mouth</article-title><source>Proc Natl Acad Sci USA.</source><year>2007</year><volume>104</volume><fpage>11889</fpage><lpage>11894</lpage><pub-id pub-id-type="doi">10.1073/pnas.0704662104</pub-id><pub-id pub-id-type="pmid">17620602</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gawad</surname><given-names>C</given-names></name><name><surname>Koh</surname><given-names>W</given-names></name><name><surname>Quake</surname><given-names>SR</given-names></name></person-group><article-title>Single-cell genome sequencing: current state of the science</article-title><source>Nat Rev Genet.</source><year>2016</year><volume>17</volume><fpage>175</fpage><lpage>188</lpage><pub-id pub-id-type="doi">10.1038/nrg.2015.16</pub-id><pub-id pub-id-type="pmid">26806412</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kashtan</surname><given-names>N</given-names></name><name><surname>Roggensack</surname><given-names>SE</given-names></name><name><surname>Rodrigue</surname><given-names>S</given-names></name><name><surname>Thompson</surname><given-names>JW</given-names></name><name><surname>Biller</surname><given-names>SJ</given-names></name><name><surname>Coe</surname><given-names>A</given-names></name><etal/></person-group><article-title>Single-cell genomics reveals hundreds of coexisting subpopulations in wild Prochlorococcus</article-title><source>Science.</source><year>2014</year><volume>344</volume><fpage>416</fpage><lpage>420</lpage><pub-id pub-id-type="doi">10.1126/science.1248575</pub-id><pub-id pub-id-type="pmid">24763590</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hugerth</surname><given-names>LW</given-names></name><name><surname>Larsson</surname><given-names>J</given-names></name><name><surname>Alneberg</surname><given-names>J</given-names></name><name><surname>Lindh</surname><given-names>MV</given-names></name><name><surname>Legrand</surname><given-names>C</given-names></name><name><surname>Pinhassi</surname><given-names>J</given-names></name><etal/></person-group><article-title>Metagenome-assembled genomes uncover a global brackish microbiome</article-title><source>Genome Biol.</source><year>2015</year><volume>16</volume><fpage>279</fpage><pub-id pub-id-type="doi">10.1186/s13059-015-0834-7</pub-id><pub-id pub-id-type="pmid">26667648</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bowers</surname><given-names>RM</given-names></name><name><surname>Kyrpides</surname><given-names>NC</given-names></name><name><surname>Stepanauskas</surname><given-names>R</given-names></name><name><surname>Harmon-Smith</surname><given-names>M</given-names></name><name><surname>Doud</surname><given-names>D</given-names></name><name><surname>Reddy</surname><given-names>TBK</given-names></name><etal/></person-group><article-title>Minimum information about a single amplified genome (MISAG) and a metagenome-assembled genome (MIMAG) of bacteria and archaea</article-title><source>Nat Biotechnol.</source><year>2017</year><volume>35</volume><fpage>725</fpage><lpage>731</lpage><pub-id pub-id-type="doi">10.1038/nbt.3893</pub-id><pub-id pub-id-type="pmid">28787424</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tyson</surname><given-names>GW</given-names></name><name><surname>Chapman</surname><given-names>J</given-names></name><name><surname>Hugenholtz</surname><given-names>P</given-names></name><name><surname>Allen</surname><given-names>EE</given-names></name><name><surname>Ram</surname><given-names>RJ</given-names></name><name><surname>Richardson</surname><given-names>PM</given-names></name><etal/></person-group><article-title>Community structure and metabolism through reconstruction of microbial genomes from the environment</article-title><source>Nature.</source><year>2004</year><volume>428</volume><fpage>37</fpage><lpage>43</lpage><pub-id pub-id-type="doi">10.1038/nature02340</pub-id><pub-id pub-id-type="pmid">14961025</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sharon</surname><given-names>I</given-names></name><name><surname>Morowitz</surname><given-names>MJ</given-names></name><name><surname>Thomas</surname><given-names>BC</given-names></name><name><surname>Costello</surname><given-names>EK</given-names></name><name><surname>Relman</surname><given-names>DA</given-names></name><name><surname>Banfield</surname><given-names>JF</given-names></name></person-group><article-title>Time series community genomics analysis reveals rapid shifts in bacterial species, strains, and phage during infant gut colonization</article-title><source>Genome Res.</source><year>2013</year><volume>23</volume><fpage>111</fpage><lpage>120</lpage><pub-id pub-id-type="doi">10.1101/gr.142315.112</pub-id><pub-id pub-id-type="pmid">22936250</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Albertsen</surname><given-names>M</given-names></name><name><surname>Hugenholtz</surname><given-names>P</given-names></name><name><surname>Skarshewski</surname><given-names>A</given-names></name><name><surname>Nielsen</surname><given-names>KL</given-names></name><name><surname>Tyson</surname><given-names>GW</given-names></name><name><surname>Nielsen</surname><given-names>PH</given-names></name></person-group><article-title>Genome sequences of rare, uncultured bacteria obtained by differential coverage binning of multiple metagenomes</article-title><source>Nature Biotechnol.</source><year>2013</year><volume>31</volume><fpage>533</fpage><lpage>538</lpage><pub-id pub-id-type="doi">10.1038/nbt.2579</pub-id><pub-id pub-id-type="pmid">23707974</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alneberg</surname><given-names>J</given-names></name><name><surname>Bjarnason</surname><given-names>BS</given-names></name><name><surname>de Bruijn</surname><given-names>I</given-names></name><name><surname>Schirmer</surname><given-names>M</given-names></name><name><surname>Quick</surname><given-names>J</given-names></name><name><surname>Ijaz</surname><given-names>UZ</given-names></name><etal/></person-group><article-title>Binning metagenomic contigs by coverage and composition</article-title><source>Nat Methods.</source><year>2014</year><volume>11</volume><fpage>1144</fpage><lpage>1146</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3103</pub-id><pub-id pub-id-type="pmid">25218180</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Imelfort</surname><given-names>M</given-names></name><name><surname>Parks</surname><given-names>D</given-names></name><name><surname>Woodcroft</surname><given-names>BJ</given-names></name><name><surname>Dennis</surname><given-names>P</given-names></name><name><surname>Hugenholtz</surname><given-names>P</given-names></name><name><surname>Tyson</surname><given-names>GW</given-names></name></person-group><article-title>GroopM: an automated tool for the recovery of population genomes from related metagenomes</article-title><source>PeerJ.</source><year>2014</year><volume>2</volume><fpage>e603</fpage><pub-id pub-id-type="doi">10.7717/peerj.603</pub-id><pub-id pub-id-type="pmid">25289188</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Swan</surname><given-names>BK</given-names></name><name><surname>Martinez-Garcia</surname><given-names>M</given-names></name><name><surname>Preston</surname><given-names>CM</given-names></name><name><surname>Sczyrba</surname><given-names>A</given-names></name><name><surname>Woyke</surname><given-names>T</given-names></name><name><surname>Lamy</surname><given-names>D</given-names></name><etal/></person-group><article-title>Potential for chemolithoautotrophy among ubiquitous bacteria lineages in the dark ocean</article-title><source>Science.</source><year>2011</year><volume>333</volume><fpage>1296</fpage><lpage>1300</lpage><pub-id pub-id-type="doi">10.1126/science.1203690</pub-id><pub-id pub-id-type="pmid">21885783</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rinke</surname><given-names>C</given-names></name><name><surname>Schwientek</surname><given-names>P</given-names></name><name><surname>Sczyrba</surname><given-names>A</given-names></name><name><surname>Ivanova</surname><given-names>NN</given-names></name><name><surname>Anderson</surname><given-names>IJ</given-names></name><name><surname>Cheng</surname><given-names>J-F</given-names></name><etal/></person-group><article-title>Insights into the phylogeny and coding potential of microbial dark matter</article-title><source>Nature.</source><year>2013</year><volume>499</volume><fpage>431</fpage><lpage>437</lpage><pub-id pub-id-type="doi">10.1038/nature12352</pub-id><pub-id pub-id-type="pmid">23851394</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Spang</surname><given-names>A</given-names></name><name><surname>Saw</surname><given-names>JH</given-names></name><name><surname>J&#x000f8;rgensen</surname><given-names>SL</given-names></name><name><surname>Zaremba-Niedzwiedzka</surname><given-names>K</given-names></name><name><surname>Martijn</surname><given-names>J</given-names></name><name><surname>Lind</surname><given-names>AE</given-names></name><etal/></person-group><article-title>Complex archaea that bridge the gap between prokaryotes and eukaryotes</article-title><source>Nature.</source><year>2015</year><volume>521</volume><fpage>173</fpage><lpage>179</lpage><pub-id pub-id-type="doi">10.1038/nature14447</pub-id><pub-id pub-id-type="pmid">25945739</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zaremba-Niedzwiedzka</surname><given-names>K</given-names></name><name><surname>Caceres</surname><given-names>EF</given-names></name><name><surname>Saw</surname><given-names>JH</given-names></name><name><surname>Backstrom</surname><given-names>D</given-names></name><name><surname>Juzokaite</surname><given-names>L</given-names></name><name><surname>Vancaester</surname><given-names>E</given-names></name><etal/></person-group><article-title>Metagenomic exploration of Asgard archaea illuminates the origin of eukaryotic cellular complexity</article-title><source>Nature.</source><year>2017</year><volume>541</volume><fpage>353</fpage><lpage>358</lpage><pub-id pub-id-type="doi">10.1038/nature21031</pub-id><pub-id pub-id-type="pmid">28077874</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stewart</surname><given-names>RD</given-names></name><name><surname>Auffret</surname><given-names>MD</given-names></name><name><surname>Warr</surname><given-names>A</given-names></name><name><surname>Wiser</surname><given-names>AH</given-names></name><name><surname>Press</surname><given-names>MO</given-names></name><name><surname>Langford</surname><given-names>KW</given-names></name><etal/></person-group><article-title>Assembly of 913 microbial genomes from metagenomic sequencing of the cow rumen</article-title><source>Nat Commun.</source><year>2018</year><volume>9</volume><fpage>870</fpage><pub-id pub-id-type="doi">10.1038/s41467-018-03317-6</pub-id><pub-id pub-id-type="pmid">29491419</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Parks</surname><given-names>DH</given-names></name><name><surname>Rinke</surname><given-names>C</given-names></name><name><surname>Chuvochina</surname><given-names>M</given-names></name><name><surname>Chaumeil</surname><given-names>PA</given-names></name><name><surname>Woodcroft</surname><given-names>BJ</given-names></name><name><surname>Evans</surname><given-names>PN</given-names></name><name><surname>Hugenholtz</surname><given-names>P</given-names></name><name><surname>Tyson</surname><given-names>GW</given-names></name></person-group><article-title>Recovery of nearly 8,000 metagenome-assembled genomes substantially expands the tree of life</article-title><source>Nature Microbiol</source><year>2017</year><volume>2</volume><fpage>1533</fpage><lpage>1542</lpage><pub-id pub-id-type="doi">10.1038/s41564-017-0012-7</pub-id><pub-id pub-id-type="pmid">28894102</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stepanauskas</surname><given-names>R</given-names></name></person-group><article-title>Single cell genomics: an individual look at microbes</article-title><source>Curr Opin Microbiol.</source><year>2012</year><volume>15</volume><fpage>613</fpage><lpage>620</lpage><pub-id pub-id-type="doi">10.1016/j.mib.2012.09.001</pub-id><pub-id pub-id-type="pmid">23026140</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Troell</surname><given-names>K</given-names></name><name><surname>Hallstr&#x000f6;m</surname><given-names>B</given-names></name><name><surname>Divne</surname><given-names>A-M</given-names></name><name><surname>Alsmark</surname><given-names>C</given-names></name><name><surname>Arrighi</surname><given-names>R</given-names></name><name><surname>Huss</surname><given-names>M</given-names></name><etal/></person-group><article-title>Cryptosporidium as a testbed for single cell genome characterization of unicellular eukaryotes</article-title><source>BMC Genomics.</source><year>2016</year><volume>17</volume><fpage>471</fpage><pub-id pub-id-type="doi">10.1186/s12864-016-2815-y</pub-id><pub-id pub-id-type="pmid">27338614</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lasken</surname><given-names>RS</given-names></name><name><surname>Stockwell</surname><given-names>TB</given-names></name></person-group><article-title>Mechanism of chimera formation during the multiple displacement amplification reaction</article-title><source>BMC Biotechnol.</source><year>2007</year><volume>7</volume><fpage>19</fpage><pub-id pub-id-type="doi">10.1186/1472-6750-7-19</pub-id><pub-id pub-id-type="pmid">17430586</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Woyke</surname><given-names>T</given-names></name><name><surname>Sczyrba</surname><given-names>A</given-names></name><name><surname>Lee</surname><given-names>J</given-names></name><name><surname>Rinke</surname><given-names>C</given-names></name><name><surname>Tighe</surname><given-names>D</given-names></name><name><surname>Clingenpeel</surname><given-names>S</given-names></name><etal/></person-group><article-title>Decontamination of MDA reagents for single cell whole genome amplification</article-title><source>PLoS One.</source><year>2011</year><volume>6</volume><fpage>e26161</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0026161</pub-id><pub-id pub-id-type="pmid">22028825</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Clingenpeel</surname><given-names>S</given-names></name><name><surname>Clum</surname><given-names>A</given-names></name><name><surname>Schwientek</surname><given-names>P</given-names></name><name><surname>Rinke</surname><given-names>C</given-names></name><name><surname>Woyke</surname><given-names>T</given-names></name></person-group><article-title>Reconstructing each cell&#x02019;s genome within complex microbial communities-dream or reality?</article-title><source>Front Microbiol.</source><year>2015</year><volume>6</volume><fpage>1</fpage><lpage>6</lpage><pub-id pub-id-type="pmid">25653648</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sangwan</surname><given-names>N</given-names></name><name><surname>Xia</surname><given-names>F</given-names></name><name><surname>Gilbert</surname><given-names>JA</given-names></name></person-group><article-title>Recovering complete and draft population genomes from metagenome datasets</article-title><source>Microbiome.</source><year>2016</year><volume>4</volume><fpage>8</fpage><pub-id pub-id-type="doi">10.1186/s40168-016-0154-5</pub-id><pub-id pub-id-type="pmid">26951112</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kang</surname><given-names>DD</given-names></name><name><surname>Froula</surname><given-names>J</given-names></name><name><surname>Egan</surname><given-names>R</given-names></name><name><surname>Wang</surname><given-names>Z</given-names></name></person-group><article-title>MetaBAT, an efficient tool for accurately reconstructing single genomes from complex microbial communities</article-title><source>PeerJ.</source><year>2015</year><volume>3</volume><fpage>e1165</fpage><pub-id pub-id-type="doi">10.7717/peerj.1165</pub-id><pub-id pub-id-type="pmid">26336640</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nobu</surname><given-names>MK</given-names></name><name><surname>Dodsworth</surname><given-names>JA</given-names></name><name><surname>Murugapiran</surname><given-names>SK</given-names></name><name><surname>Rinke</surname><given-names>C</given-names></name><name><surname>Gies</surname><given-names>EA</given-names></name><name><surname>Webster</surname><given-names>G</given-names></name><etal/></person-group><article-title>Phylogeny and physiology of candidate phylum &#x0201c;Atribacteria&#x0201d; (OP9/JS1) inferred from cultivation-independent genomics</article-title><source>ISME J</source><year>2016</year><volume>10</volume><fpage>273</fpage><lpage>286</lpage><pub-id pub-id-type="doi">10.1038/ismej.2015.97</pub-id><pub-id pub-id-type="pmid">26090992</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mason</surname><given-names>OU</given-names></name><name><surname>Hazen</surname><given-names>TC</given-names></name><name><surname>Borglin</surname><given-names>S</given-names></name><name><surname>Chain</surname><given-names>PSG</given-names></name><name><surname>Dubinsky</surname><given-names>EA</given-names></name><name><surname>Fortney</surname><given-names>JL</given-names></name><etal/></person-group><article-title>Metagenome, metatranscriptome and single-cell sequencing reveal microbial response to Deepwater Horizon oil spill</article-title><source>ISME J.</source><year>2012</year><volume>6</volume><fpage>1715</fpage><lpage>1727</lpage><pub-id pub-id-type="doi">10.1038/ismej.2012.59</pub-id><pub-id pub-id-type="pmid">22717885</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mende</surname><given-names>DR</given-names></name><name><surname>Aylward</surname><given-names>FO</given-names></name><name><surname>Eppley</surname><given-names>JM</given-names></name><name><surname>Nielsen</surname><given-names>TN</given-names></name><name><surname>DeLong</surname><given-names>EF</given-names></name></person-group><article-title>Improved environmental genomes via integration of metagenomic and single-cell assemblies</article-title><source>Front Microbiol.</source><year>2016</year><volume>7</volume><fpage>1</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.3389/fmicb.2016.00143</pub-id><pub-id pub-id-type="pmid">26834723</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Becraft</surname><given-names>ED</given-names></name><name><surname>Dodsworth</surname><given-names>JA</given-names></name><name><surname>Murugapiran</surname><given-names>SK</given-names></name><name><surname>Ohlsson</surname><given-names>JI</given-names></name><name><surname>Briggs</surname><given-names>BR</given-names></name><name><surname>Kanbar</surname><given-names>J</given-names></name><etal/></person-group><article-title>Single-cell-genomics-facilitated read binning of candidate phylum EM19 genomes from geothermal spring metagenomes</article-title><source>Appl Environ Microbiol.</source><year>2015</year><volume>82</volume><fpage>992</fpage><lpage>1003</lpage><pub-id pub-id-type="doi">10.1128/AEM.03140-15</pub-id><pub-id pub-id-type="pmid">26637598</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Herlemann</surname><given-names>DP</given-names></name><name><surname>Labrenz</surname><given-names>M</given-names></name><name><surname>J&#x000fc;rgens</surname><given-names>K</given-names></name><name><surname>Bertilsson</surname><given-names>S</given-names></name><name><surname>Waniek</surname><given-names>JJ</given-names></name><name><surname>Andersson</surname><given-names>AF</given-names></name></person-group><article-title>Transitions in bacterial communities along the 2000 km salinity gradient of the Baltic Sea</article-title><source>ISME J.</source><year>2011</year><volume>5</volume><fpage>1571</fpage><lpage>1579</lpage><pub-id pub-id-type="doi">10.1038/ismej.2011.41</pub-id><pub-id pub-id-type="pmid">21472016</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Andersson</surname><given-names>AF</given-names></name><name><surname>Riemann</surname><given-names>L</given-names></name><name><surname>Bertilsson</surname><given-names>S</given-names></name></person-group><article-title>Pyrosequencing reveals contrasting seasonal dynamics of taxa within Baltic Sea bacterioplankton communities</article-title><source>ISME J.</source><year>2010</year><volume>4</volume><fpage>171</fpage><lpage>181</lpage><pub-id pub-id-type="doi">10.1038/ismej.2009.108</pub-id><pub-id pub-id-type="pmid">19829318</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lindh</surname><given-names>MV</given-names></name><name><surname>Sj&#x000f6;stedt</surname><given-names>J</given-names></name><name><surname>Andersson</surname><given-names>AF</given-names></name><name><surname>Baltar</surname><given-names>F</given-names></name><name><surname>Hugerth</surname><given-names>LW</given-names></name><name><surname>Lundin</surname><given-names>D</given-names></name><etal/></person-group><article-title>Disentangling seasonal bacterioplankton population dynamics by high-frequency sampling</article-title><source>Environ Microbiol.</source><year>2015</year><volume>17</volume><fpage>2459</fpage><lpage>2476</lpage><pub-id pub-id-type="doi">10.1111/1462-2920.12720</pub-id><pub-id pub-id-type="pmid">25403576</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dupont</surname><given-names>CL</given-names></name><name><surname>Larsson</surname><given-names>J</given-names></name><name><surname>Yooseph</surname><given-names>S</given-names></name><name><surname>Ininbergs</surname><given-names>K</given-names></name><name><surname>Goll</surname><given-names>J</given-names></name><name><surname>Asplund-Samuelsson</surname><given-names>J</given-names></name><etal/></person-group><article-title>Functional tradeoffs underpin salinity-driven divergence in microbial community composition</article-title><source>PLoS One.</source><year>2014</year><volume>9</volume><fpage>e89549</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0089549</pub-id><pub-id pub-id-type="pmid">24586863</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ondov</surname><given-names>BD</given-names></name><name><surname>Treangen</surname><given-names>TJ</given-names></name><name><surname>Melsted</surname><given-names>P</given-names></name><name><surname>Mallonee</surname><given-names>AB</given-names></name><name><surname>Bergman</surname><given-names>NH</given-names></name><name><surname>Koren</surname><given-names>S</given-names></name><etal/></person-group><article-title>Mash: fast genome and metagenome distance estimation using MinHash</article-title><source>Genome Biol.</source><year>2016</year><volume>17</volume><fpage>132</fpage><pub-id pub-id-type="doi">10.1186/s13059-016-0997-x</pub-id><pub-id pub-id-type="pmid">27323842</pub-id></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eren</surname><given-names>AM</given-names></name><name><surname>Esen</surname><given-names>&#x000d6;C</given-names></name><name><surname>Quince</surname><given-names>C</given-names></name><name><surname>Vineis</surname><given-names>JH</given-names></name><name><surname>Morrison</surname><given-names>HG</given-names></name><name><surname>Sogin</surname><given-names>ML</given-names></name><etal/></person-group><article-title>Anvi&#x02019;o: an advanced analysis and visualization platform for &#x02018;omics data</article-title><source>PeerJ.</source><year>2015</year><volume>3</volume><fpage>e1319</fpage><pub-id pub-id-type="doi">10.7717/peerj.1319</pub-id><pub-id pub-id-type="pmid">26500826</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dick</surname><given-names>GJ</given-names></name><name><surname>Andersson</surname><given-names>AF</given-names></name><name><surname>Baker</surname><given-names>BJ</given-names></name><name><surname>Simmons</surname><given-names>SL</given-names></name><name><surname>Thomas</surname><given-names>BC</given-names></name><name><surname>Yelton</surname><given-names>AP</given-names></name><etal/></person-group><article-title>Community-wide analysis of microbial genome sequence signatures</article-title><source>Genome Biol.</source><year>2009</year><volume>10</volume><fpage>R85</fpage><pub-id pub-id-type="doi">10.1186/gb-2009-10-8-r85</pub-id><pub-id pub-id-type="pmid">19698104</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Konstantinidis</surname><given-names>KT</given-names></name><name><surname>Tiedje</surname><given-names>JM</given-names></name></person-group><article-title>Genomic insights that advance the species definition for prokaryotes</article-title><source>Proc Natl Acad Sci USA.</source><year>2005</year><volume>102</volume><fpage>2567</fpage><lpage>2572</lpage><pub-id pub-id-type="doi">10.1073/pnas.0409727102</pub-id><pub-id pub-id-type="pmid">15701695</pub-id></element-citation></ref><ref id="CR55"><label>55.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Konstantinidis</surname><given-names>KT</given-names></name><name><surname>Rossell&#x000f3;-M&#x000f3;ra</surname><given-names>R</given-names></name></person-group><article-title>Classifying the uncultivated microbial majority: a place for metagenomic data in the candidatus proposal</article-title><source>Syst Appl Microbiol.</source><year>2015</year><volume>38</volume><fpage>223</fpage><lpage>230</lpage><pub-id pub-id-type="doi">10.1016/j.syapm.2015.01.001</pub-id><pub-id pub-id-type="pmid">25681255</pub-id></element-citation></ref><ref id="CR56"><label>56.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Goris</surname><given-names>J</given-names></name><name><surname>Konstantinidis</surname><given-names>KT</given-names></name><name><surname>Klappenbach</surname><given-names>JA</given-names></name><name><surname>Coenye</surname><given-names>T</given-names></name><name><surname>Vandamme</surname><given-names>P</given-names></name><name><surname>Tiedje</surname><given-names>JM</given-names></name></person-group><article-title>DNA&#x02013;DNA hybridization values and their relationship to whole-genome sequence similarities</article-title><source>Int J Syst Evol Microbiol.</source><year>2007</year><volume>57</volume><fpage>81</fpage><lpage>91</lpage><pub-id pub-id-type="doi">10.1099/ijs.0.64483-0</pub-id><pub-id pub-id-type="pmid">17220447</pub-id></element-citation></ref><ref id="CR57"><label>57.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Richter</surname><given-names>M</given-names></name><name><surname>Rossell&#x000f3;-M&#x000f3;ra</surname><given-names>R</given-names></name></person-group><article-title>Shifting the genomic gold standard for the prokaryotic species definition</article-title><source>Proc Natl Acad Sci USA.</source><year>2009</year><volume>106</volume><fpage>19126</fpage><lpage>19131</lpage><pub-id pub-id-type="doi">10.1073/pnas.0906412106</pub-id><pub-id pub-id-type="pmid">19855009</pub-id></element-citation></ref><ref id="CR58"><label>58.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Varghese</surname><given-names>NJ</given-names></name><name><surname>Mukherjee</surname><given-names>S</given-names></name><name><surname>Ivanova</surname><given-names>N</given-names></name><name><surname>Konstantinidis</surname><given-names>KT</given-names></name><name><surname>Mavrommatis</surname><given-names>K</given-names></name><name><surname>Kyrpides</surname><given-names>NC</given-names></name><etal/></person-group><article-title>Microbial species delineation using whole genome sequences</article-title><source>Nucleic Acids Res.</source><year>2015</year><volume>43</volume><fpage>6761</fpage><lpage>6771</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv657</pub-id><pub-id pub-id-type="pmid">26150420</pub-id></element-citation></ref><ref id="CR59"><label>59.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sczyrba</surname><given-names>A</given-names></name><name><surname>Hofmann</surname><given-names>P</given-names></name><name><surname>Belmann</surname><given-names>P</given-names></name><name><surname>Koslicki</surname><given-names>D</given-names></name><name><surname>Janssen</surname><given-names>S</given-names></name><name><surname>Dr&#x000f6;ge</surname><given-names>J</given-names></name><etal/></person-group><article-title>Critical assessment of metagenome interpretation - a benchmark of metagenomics software</article-title><source>Nat Methods.</source><year>2017</year><volume>14</volume><fpage>1063</fpage><lpage>1071</lpage><pub-id pub-id-type="doi">10.1038/nmeth.4458</pub-id><pub-id pub-id-type="pmid">28967888</pub-id></element-citation></ref><ref id="CR60"><label>60.</label><mixed-citation publication-type="other">Quince C, Delmont TO, Raguideau S, Alneberg J, Darling AE, Collins G, Eren AM. DESMAN: a new tool for de novo extraction of strains from metagenomes. Genome Biol. 2017;18:181.</mixed-citation></ref><ref id="CR61"><label>61.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schloissnig</surname><given-names>S</given-names></name><name><surname>Arumugam</surname><given-names>M</given-names></name><name><surname>Sunagawa</surname><given-names>S</given-names></name><name><surname>Mitreva</surname><given-names>M</given-names></name><name><surname>Tap</surname><given-names>J</given-names></name><name><surname>Zhu</surname><given-names>A</given-names></name><etal/></person-group><article-title>Genomic variation landscape of the human gut microbiome</article-title><source>Nature.</source><year>2012</year><volume>493</volume><fpage>45</fpage><lpage>50</lpage><pub-id pub-id-type="doi">10.1038/nature11711</pub-id><pub-id pub-id-type="pmid">23222524</pub-id></element-citation></ref><ref id="CR62"><label>62.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nayfach</surname><given-names>S</given-names></name><name><surname>Rodriguez-Mueller</surname><given-names>B</given-names></name><name><surname>Garud</surname><given-names>N</given-names></name><name><surname>Pollard</surname><given-names>KS</given-names></name></person-group><article-title>An integrated metagenomics pipeline for strain profiling reveals novel patterns of bacterial transmission and biogeography</article-title><source>Genome Res.</source><year>2016</year><volume>26</volume><fpage>1612</fpage><lpage>1625</lpage><pub-id pub-id-type="doi">10.1101/gr.201863.115</pub-id><pub-id pub-id-type="pmid">27803195</pub-id></element-citation></ref><ref id="CR63"><label>63.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Andersson</surname><given-names>AF</given-names></name><name><surname>Sj&#x000f6;qvist</surname><given-names>C</given-names></name><collab>POGENOM</collab></person-group><source>POGENOM: population genomics from metagenomes</source><year>2017</year></element-citation></ref><ref id="CR64"><label>64.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ghylin</surname><given-names>TW</given-names></name><name><surname>Garcia</surname><given-names>SL</given-names></name><name><surname>Moya</surname><given-names>F</given-names></name><name><surname>Oyserman</surname><given-names>BO</given-names></name><name><surname>Schwientek</surname><given-names>P</given-names></name><name><surname>Forest</surname><given-names>KT</given-names></name><etal/></person-group><article-title>Comparative single-cell genomics reveals potential ecological niches for the freshwater acI Actinobacteria lineage</article-title><source>ISME J.</source><year>2014</year><volume>8</volume><fpage>2503</fpage><lpage>2516</lpage><pub-id pub-id-type="doi">10.1038/ismej.2014.135</pub-id><pub-id pub-id-type="pmid">25093637</pub-id></element-citation></ref><ref id="CR65"><label>65.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eiler</surname><given-names>A</given-names></name><name><surname>Mondav</surname><given-names>R</given-names></name><name><surname>Sinclair</surname><given-names>L</given-names></name><name><surname>Fernandez-Vidal</surname><given-names>L</given-names></name><name><surname>Scofield</surname><given-names>DG</given-names></name><name><surname>Schwientek</surname><given-names>P</given-names></name><etal/></person-group><article-title>Tuning fresh: radiation through rewiring of central metabolism in streamlined bacteria</article-title><source>ISME J.</source><year>2016</year><volume>10</volume><fpage>1902</fpage><lpage>1914</lpage><pub-id pub-id-type="doi">10.1038/ismej.2015.260</pub-id><pub-id pub-id-type="pmid">26784354</pub-id></element-citation></ref><ref id="CR66"><label>66.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>C</given-names></name><name><surname>Xing</surname><given-names>D</given-names></name><name><surname>Tan</surname><given-names>L</given-names></name><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Zhou</surname><given-names>G</given-names></name><name><surname>Huang</surname><given-names>L</given-names></name><etal/></person-group><article-title>Single-cell whole-genome analyses by Linear Amplification via Transposon Insertion (LIANTI)</article-title><source>Science.</source><year>2017</year><volume>356</volume><fpage>189</fpage><lpage>194</lpage><pub-id pub-id-type="doi">10.1126/science.aak9787</pub-id><pub-id pub-id-type="pmid">28408603</pub-id></element-citation></ref><ref id="CR67"><label>67.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leung</surname><given-names>K</given-names></name><name><surname>Klaus</surname><given-names>A</given-names></name><name><surname>Lin</surname><given-names>BK</given-names></name><name><surname>Laks</surname><given-names>E</given-names></name><name><surname>Biele</surname><given-names>J</given-names></name><name><surname>Lai</surname><given-names>D</given-names></name><etal/></person-group><article-title>Robust high-performance nanoliter-volume single-cell multiple displacement amplification on planar substrates</article-title><source>Proc Natl Acad Sci USA.</source><year>2016</year><volume>113</volume><fpage>8484</fpage><lpage>8489</lpage><pub-id pub-id-type="doi">10.1073/pnas.1520964113</pub-id><pub-id pub-id-type="pmid">27412862</pub-id></element-citation></ref><ref id="CR68"><label>68.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>D</given-names></name><name><surname>Liu</surname><given-names>C-M</given-names></name><name><surname>Luo</surname><given-names>R</given-names></name><name><surname>Sadakane</surname><given-names>K</given-names></name><name><surname>Lam</surname><given-names>T-W</given-names></name></person-group><article-title>MEGAHIT: an ultra-fast single-node solution for large and complex metagenomics assembly via succinct de Bruijn graph</article-title><source>Bioinformatics.</source><year>2015</year><volume>31</volume><fpage>1674</fpage><lpage>1676</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btv033</pub-id><pub-id pub-id-type="pmid">25609793</pub-id></element-citation></ref><ref id="CR69"><label>69.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nurk</surname><given-names>S</given-names></name><name><surname>Meleshko</surname><given-names>D</given-names></name><name><surname>Korobeynikov</surname><given-names>A</given-names></name><name><surname>Pevzner</surname><given-names>PA</given-names></name></person-group><article-title>metaSPAdes: a new versatile metagenomic assembler</article-title><source>Genome Res</source><year>2017</year><volume>27</volume><fpage>824</fpage><lpage>834</lpage><pub-id pub-id-type="doi">10.1101/gr.213959.116</pub-id><pub-id pub-id-type="pmid">28298430</pub-id></element-citation></ref><ref id="CR70"><label>70.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sandberg</surname><given-names>R</given-names></name><name><surname>Winberg</surname><given-names>G</given-names></name><name><surname>Br&#x000e4;nden</surname><given-names>CI</given-names></name><name><surname>Kaske</surname><given-names>A</given-names></name><name><surname>Ernberg</surname><given-names>I</given-names></name><name><surname>C&#x000f6;ster</surname><given-names>J</given-names></name></person-group><article-title>Capturing whole-genome characteristics in short sequences using a na&#x000ef;ve Bayesian classifier</article-title><source>Genome Res.</source><year>2001</year><volume>11</volume><fpage>1404</fpage><lpage>1409</lpage><pub-id pub-id-type="doi">10.1101/gr.186401</pub-id><pub-id pub-id-type="pmid">11483581</pub-id></element-citation></ref><ref id="CR71"><label>71.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Boisvert</surname><given-names>S</given-names></name><name><surname>Raymond</surname><given-names>F</given-names></name><name><surname>Godzaridis</surname><given-names>E</given-names></name><name><surname>Laviolette</surname><given-names>F</given-names></name><name><surname>Corbeil</surname><given-names>J</given-names></name></person-group><article-title>Ray Meta: scalable de novo metagenome assembly and profiling</article-title><source>Genome Biol.</source><year>2012</year><volume>13</volume><fpage>R122</fpage><pub-id pub-id-type="doi">10.1186/gb-2012-13-12-r122</pub-id><pub-id pub-id-type="pmid">23259615</pub-id></element-citation></ref><ref id="CR72"><label>72.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Langmead</surname><given-names>B</given-names></name><name><surname>Salzberg</surname><given-names>SL</given-names></name></person-group><article-title>Fast gapped-read alignment with Bowtie 2</article-title><source>Nat Methods.</source><year>2012</year><volume>9</volume><fpage>357</fpage><lpage>359</lpage><pub-id pub-id-type="doi">10.1038/nmeth.1923</pub-id><pub-id pub-id-type="pmid">22388286</pub-id></element-citation></ref><ref id="CR73"><label>73.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Quince</surname><given-names>C</given-names></name><name><surname>Walker</surname><given-names>AW</given-names></name><name><surname>Simpson</surname><given-names>JT</given-names></name><name><surname>Loman</surname><given-names>NJ</given-names></name><name><surname>Segata</surname><given-names>N</given-names></name></person-group><article-title>Shotgun metagenomics, from sampling to analysis</article-title><source>Nat Biotechnol.</source><year>2017</year><volume>35</volume><fpage>833</fpage><lpage>844</lpage><pub-id pub-id-type="doi">10.1038/nbt.3935</pub-id><pub-id pub-id-type="pmid">28898207</pub-id></element-citation></ref><ref id="CR74"><label>74.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kurtz</surname><given-names>S</given-names></name><name><surname>Phillippy</surname><given-names>A</given-names></name><name><surname>Delcher</surname><given-names>AL</given-names></name><name><surname>Smoot</surname><given-names>M</given-names></name><name><surname>Shumway</surname><given-names>M</given-names></name><name><surname>Antonescu</surname><given-names>C</given-names></name><etal/></person-group><article-title>Versatile and open software for comparing large genomes</article-title><source>Genome Biol.</source><year>2004</year><volume>5</volume><fpage>R12</fpage><pub-id pub-id-type="doi">10.1186/gb-2004-5-2-r12</pub-id><pub-id pub-id-type="pmid">14759262</pub-id></element-citation></ref><ref id="CR75"><label>75.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Seemann</surname><given-names>T</given-names></name></person-group><article-title>Prokka: rapid prokaryotic genome annotation</article-title><source>Bioinformatics.</source><year>2014</year><volume>30</volume><fpage>2068</fpage><lpage>2069</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btu153</pub-id><pub-id pub-id-type="pmid">24642063</pub-id></element-citation></ref><ref id="CR76"><label>76.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tatusov</surname><given-names>RL</given-names></name><name><surname>Galperin</surname><given-names>MY</given-names></name><name><surname>Natale</surname><given-names>DA</given-names></name><name><surname>Koonin</surname><given-names>EV</given-names></name></person-group><article-title>The COG database: a tool for genome-scale analysis of protein functions and evolution</article-title><source>Nucleic Acids Res.</source><year>2000</year><volume>28</volume><fpage>33</fpage><lpage>36</lpage><pub-id pub-id-type="doi">10.1093/nar/28.1.33</pub-id><pub-id pub-id-type="pmid">10592175</pub-id></element-citation></ref><ref id="CR77"><label>77.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Darling</surname><given-names>AE</given-names></name><name><surname>Jospin</surname><given-names>G</given-names></name><name><surname>Lowe</surname><given-names>E</given-names></name><name><surname>Matsen</surname><given-names>FA</given-names><suffix>4th</suffix></name><name><surname>Bik</surname><given-names>HM</given-names></name><name><surname>Eisen</surname><given-names>JA</given-names></name></person-group><article-title>PhyloSift: phylogenetic analysis of genomes and metagenomes</article-title><source>PeerJ.</source><year>2014</year><volume>2</volume><fpage>e243</fpage><pub-id pub-id-type="doi">10.7717/peerj.243</pub-id><pub-id pub-id-type="pmid">24482762</pub-id></element-citation></ref><ref id="CR78"><label>78.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>S</given-names></name><name><surname>Zhu</surname><given-names>Z</given-names></name><name><surname>Fu</surname><given-names>L</given-names></name><name><surname>Niu</surname><given-names>B</given-names></name><name><surname>Li</surname><given-names>W</given-names></name></person-group><article-title>WebMGA: a customizable web server for fast metagenomic sequence analysis</article-title><source>BMC Genomics</source><year>2011</year><volume>12</volume><fpage>444</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-12-444</pub-id><pub-id pub-id-type="pmid">21899761</pub-id></element-citation></ref><ref id="CR79"><label>79.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Andrews</surname><given-names>S</given-names></name></person-group><source>FastQC: a quality control tool for high throughput sequence data</source><year>2010</year></element-citation></ref><ref id="CR80"><label>80.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Martin</surname><given-names>M</given-names></name></person-group><article-title>Cutadapt removes adapter sequences from high-throughput sequencing reads</article-title><source>EMBnet. J.</source><year>2011</year><volume>17</volume><fpage>10</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.14806/ej.17.1.200</pub-id></element-citation></ref><ref id="CR81"><label>81.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bankevich</surname><given-names>A</given-names></name><name><surname>Nurk</surname><given-names>S</given-names></name><name><surname>Antipov</surname><given-names>D</given-names></name><name><surname>Gurevich</surname><given-names>A</given-names></name><name><surname>Dvorkin</surname><given-names>M</given-names></name><name><surname>Kulikov</surname><given-names>AS</given-names></name><etal/></person-group><article-title>SPAdes: a new genome assembly algorithm and its applications to single-cell sequencing</article-title><source>J Comput Biol.</source><year>2012</year><volume>19</volume><fpage>455</fpage><lpage>477</lpage><pub-id pub-id-type="doi">10.1089/cmb.2012.0021</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR82"><label>82.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gurevich</surname><given-names>A</given-names></name><name><surname>Saveliev</surname><given-names>V</given-names></name><name><surname>Vyahhi</surname><given-names>N</given-names></name><name><surname>Tesler</surname><given-names>G</given-names></name></person-group><article-title>QUAST: quality assessment tool for genome assemblies</article-title><source>Bioinformatics.</source><year>2013</year><volume>29</volume><fpage>1072</fpage><lpage>1075</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btt086</pub-id><pub-id pub-id-type="pmid">23422339</pub-id></element-citation></ref><ref id="CR83"><label>83.</label><mixed-citation publication-type="other">van d WS, Colbert SC, Varoquaux G. The NumPy array: a structure for efficient numerical computation. Comput Sci Eng. 2011;13:22&#x02013;30.</mixed-citation></ref><ref id="CR84"><label>84.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hunter</surname><given-names>JD</given-names></name></person-group><article-title>Matplotlib: a 2D graphics environment</article-title><source>Comput Sci Eng.</source><year>2007</year><volume>9</volume><fpage>90</fpage><lpage>95</lpage><pub-id pub-id-type="doi">10.1109/MCSE.2007.55</pub-id></element-citation></ref><ref id="CR85"><label>85.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Altschul</surname><given-names>SF</given-names></name><name><surname>Gish</surname><given-names>W</given-names></name><name><surname>Miller</surname><given-names>W</given-names></name><name><surname>Myers</surname><given-names>EW</given-names></name><name><surname>Lipman</surname><given-names>DJ</given-names></name></person-group><article-title>Basic local alignment search tool</article-title><source>J Mol Biol.</source><year>1990</year><volume>215</volume><fpage>403</fpage><lpage>410</lpage><pub-id pub-id-type="doi">10.1016/S0022-2836(05)80360-2</pub-id><pub-id pub-id-type="pmid">2231712</pub-id></element-citation></ref><ref id="CR86"><label>86.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Witten</surname><given-names>DM</given-names></name></person-group><article-title>Classification and clustering of sequencing data using a Poisson model</article-title><source>Ann Appl Stat.</source><year>2011</year><volume>5</volume><fpage>2493</fpage><lpage>2518</lpage><pub-id pub-id-type="doi">10.1214/11-AOAS493</pub-id></element-citation></ref><ref id="CR87"><label>87.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Buchfink</surname><given-names>B</given-names></name><name><surname>Xie</surname><given-names>C</given-names></name><name><surname>Huson</surname><given-names>DH</given-names></name></person-group><article-title>Fast and sensitive protein alignment using DIAMOND</article-title><source>Nat Methods.</source><year>2015</year><volume>12</volume><fpage>59</fpage><lpage>60</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3176</pub-id><pub-id pub-id-type="pmid">25402007</pub-id></element-citation></ref><ref id="CR88"><label>88.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>van Dongen</surname><given-names>S</given-names></name><name><surname>Abreu-Goodger</surname><given-names>C</given-names></name></person-group><article-title>Using MCL to extract clusters from networks</article-title><source>Methods Mol Biol.</source><year>2012</year><volume>804</volume><fpage>281</fpage><lpage>295</lpage><pub-id pub-id-type="doi">10.1007/978-1-61779-361-5_15</pub-id><pub-id pub-id-type="pmid">22144159</pub-id></element-citation></ref><ref id="CR89"><label>89.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hyatt</surname><given-names>D</given-names></name><name><surname>Chen</surname><given-names>G-L</given-names></name><name><surname>Locascio</surname><given-names>PF</given-names></name><name><surname>Land</surname><given-names>ML</given-names></name><name><surname>Larimer</surname><given-names>FW</given-names></name><name><surname>Hauser</surname><given-names>LJ</given-names></name></person-group><article-title>Prodigal: prokaryotic gene recognition and translation initiation site identification</article-title><source>BMC Bioinformatics.</source><year>2010</year><volume>11</volume><fpage>119</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-11-119</pub-id><pub-id pub-id-type="pmid">20211023</pub-id></element-citation></ref><ref id="CR90"><label>90.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eddy</surname><given-names>SR</given-names></name></person-group><article-title>Accelerated profile HMM searches</article-title><source>PLoS Comput Biol.</source><year>2011</year><volume>7</volume><fpage>e1002195</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1002195</pub-id><pub-id pub-id-type="pmid">22039361</pub-id></element-citation></ref><ref id="CR91"><label>91.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Campbell</surname><given-names>JH</given-names></name><name><surname>O&#x02019;Donoghue</surname><given-names>P</given-names></name><name><surname>Campbell</surname><given-names>AG</given-names></name><name><surname>Schwientek</surname><given-names>P</given-names></name><name><surname>Sczyrba</surname><given-names>A</given-names></name><name><surname>Woyke</surname><given-names>T</given-names></name><etal/></person-group><article-title>UGA is an additional glycine codon in uncultured SR1 bacteria from the human microbiota</article-title><source>Proc Natl Acad Sci USA.</source><year>2013</year><volume>110</volume><fpage>5540</fpage><lpage>5545</lpage><pub-id pub-id-type="doi">10.1073/pnas.1303090110</pub-id><pub-id pub-id-type="pmid">23509275</pub-id></element-citation></ref><ref id="CR92"><label>92.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Handsaker</surname><given-names>B</given-names></name><name><surname>Wysoker</surname><given-names>A</given-names></name><name><surname>Fennell</surname><given-names>T</given-names></name><name><surname>Ruan</surname><given-names>J</given-names></name><name><surname>Homer</surname><given-names>N</given-names></name><etal/></person-group><article-title>The sequence alignment/map format and SAMtools</article-title><source>Bioinformatics.</source><year>2009</year><volume>25</volume><fpage>2078</fpage><lpage>2079</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btp352</pub-id><pub-id pub-id-type="pmid">19505943</pub-id></element-citation></ref><ref id="CR93"><label>93.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>McKinney</surname><given-names>W</given-names></name><etal/></person-group><person-group person-group-type="editor"><name><surname>van der Voort</surname><given-names>S</given-names></name><name><surname>Millman</surname><given-names>J</given-names></name><etal/></person-group><article-title>Data structures for statistical computing in python</article-title><source>Proceedings of the 9th Python in Science Conference</source><year>2010</year><fpage>51</fpage><lpage>56</lpage></element-citation></ref><ref id="CR94"><label>94.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Waskom</surname><given-names>M</given-names></name><name><surname>Botvinnik</surname><given-names>O</given-names></name><name><surname>Hobson</surname><given-names>P</given-names></name><name><surname>Warmenhoven</surname><given-names>J</given-names></name><name><surname>Cole</surname><given-names>JB</given-names></name><name><surname>Halchenko</surname><given-names>Y</given-names></name><etal/></person-group><source>Seaborn: statistical data visualization</source><year>2014</year></element-citation></ref><ref id="CR95"><label>95.</label><mixed-citation publication-type="other">Seemann T. Barrnap: rapid ribosomal RNA prediction. 2015 [cited 2016 Jul 21]. Available from: <ext-link ext-link-type="uri" xlink:href="https://github.com/tseemann/barrnap">https://github.com/tseemann/barrnap</ext-link></mixed-citation></ref><ref id="CR96"><label>96.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pruesse</surname><given-names>E</given-names></name><name><surname>Peplies</surname><given-names>J</given-names></name><name><surname>Gl&#x000f6;ckner</surname><given-names>FO</given-names></name></person-group><article-title>SINA: accurate high-throughput multiple sequence alignment of ribosomal RNA genes</article-title><source>Bioinformatics</source><year>2012</year><volume>28</volume><fpage>1823</fpage><lpage>1829</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bts252</pub-id><pub-id pub-id-type="pmid">22556368</pub-id></element-citation></ref><ref id="CR97"><label>97.</label><mixed-citation publication-type="other">BLASTN: Standard Nucleotide BLAST. [cited 2017 Apr 21]. Available from: <ext-link ext-link-type="uri" xlink:href="https://blast.ncbi.nlm.nih.gov/Blast.cgi?PAGE_TYPE=BlastSearch">https://blast.ncbi.nlm.nih.gov/Blast.cgi?PAGE_TYPE=BlastSearch</ext-link></mixed-citation></ref></ref-list></back></article>