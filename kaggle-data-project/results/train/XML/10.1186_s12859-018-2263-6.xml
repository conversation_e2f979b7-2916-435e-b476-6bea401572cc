<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6044013</article-id><article-id pub-id-type="publisher-id">2263</article-id><article-id pub-id-type="doi">10.1186/s12859-018-2263-6</article-id><article-categories><subj-group subj-group-type="heading"><subject>Methodology Article</subject></subj-group></article-categories><title-group><article-title>Alternative empirical Bayes models for adjusting for batch effects in genomic studies</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Zhang</surname><given-names>Yuqing</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Jenkins</surname><given-names>David F.</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Manimaran</surname><given-names>Solaiappan</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-6247-6595</contrib-id><name><surname>Johnson</surname><given-names>W. Evan</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0367 5222</institution-id><institution-id institution-id-type="GRID">grid.475010.7</institution-id><institution>Division of Computational Biomedicine, Boston University School of Medicine, </institution></institution-wrap>72 East Concord Street, Boston, 02118 MA USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 7558</institution-id><institution-id institution-id-type="GRID">grid.189504.1</institution-id><institution>Graduate Program in Bioinformatics, Boston University, </institution></institution-wrap>24 Cummington Mall, Boston, 02215 MA USA </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 7558</institution-id><institution-id institution-id-type="GRID">grid.189504.1</institution-id><institution>Department of Biostatistics, Boston University School of Public Health, </institution></institution-wrap>715 Albany Street, Boston, 02118 MA USA </aff></contrib-group><pub-date pub-type="epub"><day>13</day><month>7</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>13</day><month>7</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>19</volume><elocation-id>262</elocation-id><history><date date-type="received"><day>11</day><month>1</month><year>2018</year></date><date date-type="accepted"><day>26</day><month>6</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold> This article is distributed under the terms of the Creative Commons Attribution 4.0 International License(<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver(<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>Combining genomic data sets from multiple studies is advantageous to increase statistical power in studies where logistical considerations restrict sample size or require the sequential generation of data. However, significant technical heterogeneity is commonly observed across multiple batches of data that are generated from different processing or reagent batches, experimenters, protocols, or profiling platforms. These so-called batch effects often confound true biological relationships in the data, reducing the power benefits of combining multiple batches, and may even lead to spurious results in some combined studies. Therefore there is significant need for effective methods and software tools that account for batch effects in high-throughput genomic studies.</p></sec><sec><title>Results</title><p>Here we contribute multiple methods and software tools for improved combination and analysis of data from multiple batches. In particular, we provide batch effect solutions for cases where the severity of the batch effects is not extreme, and for cases where one high-quality batch can serve as a reference, such as the training set in a biomarker study. We illustrate our approaches and software in both simulated and real data scenarios.</p></sec><sec><title>Conclusions</title><p>We demonstrate the value of these new contributions compared to currently established approaches in the specified batch correction situations.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12859-018-2263-6) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Batch effects</kwd><kwd>Empirical Bayes models</kwd><kwd>Data integration</kwd><kwd>Biomarker development</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000066</institution-id><institution>National Institute of Environmental Health Sciences</institution></institution-wrap></funding-source><award-id>NIH R01ES025002</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000054</institution-id><institution>National Cancer Institute</institution></institution-wrap></funding-source><award-id>U01CA164720</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>In the past two decades, owing to the advent of novel high-throughput techniques, tens of thousands of genome profiling experiments have been performed [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. These massive data sets can be used for many purposes, including understanding basic biological function, classifying molecular subtypes of disease, characterizing disease etiology, or predicting disease prognosis and severity. Initially, the majority of these studies were completed using microarray platforms, but sequencing platforms are now common for many applications. Across these platforms, thousands of variations on technology and annotation have been used [<xref ref-type="bibr" rid="CR3">3</xref>]. Scientists who seek to integrate data across platforms may experience difficulty because platforms introduce distinct technological biases and produce data with different shapes and scales. For example, gene expression microarrays typically measure transcription levels on a continuous (log-) intensity scale, whereas RNA-sequencing measures the same biological phenomena with overdispersed and zero-inflated count data. Furthermore, even with data from the same platform, large amounts of technical and biological heterogeneity is commonly observed between separate batches or experiments. Due to the high cost of these experiments or the difficulty in collecting appropriate samples, datasets are often processed in small batches, at different times, and in different facilities. This proves to be a difficult challenge to researchers wanting to combine studies to increase statistical power in their analyses.</p><p>One illustrative example of cross-platform (and within-platform) heterogeneity can be found in The Cancer Genome Atlas (TCGA) [<xref ref-type="bibr" rid="CR4">4</xref>]. Profiling data types collected by TCGA include RNA expression, microRNA expression, protein expression, DNA methylation, copy number variation, and somatic mutations. Within each profiling type, multiple platforms are often used. For example, RNA expression has been measured with RNA-sequencing (using multiple different protocols) and several different microarray platforms including Agilent G4502A, Affymetrix HG-U133A, and Affymetrix Human Exon 1.0 ST, among others. Many of the tumor samples are profiled by only a subset of the possible data types and platforms, and in almost all cases the samples within each platform were generated in multiple experimental batches. This presents problems to researchers wanting to do comprehensive and integrative analyses, as they often limit their analyses to a single data type or platform. Furthermore, other existing data resources (e.g. ENCODE, LINCS, Epigenome Roadmap) utilize different platforms and protocols, and researchers often want to combine their own experimental data with data from these public repositories. Therefore, these necessitate robust and sophisticated standardization and batch correction methods in order to appropriately integrate data within and across these consortiums.</p><p>Many prior studies have clearly established the need for batch effect correction [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>]. To address these difficulties, existing tools have been developed for batch correction. Some of the first batch effect methods relied on singular value decomposition (SVD) [<xref ref-type="bibr" rid="CR7">7</xref>], machine learning classification approaches (DWD) [<xref ref-type="bibr" rid="CR8">8</xref>], or a block linear model (XPN) [<xref ref-type="bibr" rid="CR9">9</xref>]. The SVD approach relies on the identification of batch effects by the unsupervised matrix decomposition, which will commonly result in the removal of biological signal of interest. The DWD and XPN methods provide supervised approaches for combining data, but are mostly used to combine two batches at a time, and do not account for treatment effects. These methods need to be applied multiple times in an ad hoc manner for studies with three or more batches, and are not flexible enough to handle multiple experimental conditions, or studies with unbalanced experimental designs. More recent and flexible methods rely on robust empirical Bayes regression (ComBat) [<xref ref-type="bibr" rid="CR10">10</xref>], the efficient use of control probes or samples (RUV) [<xref ref-type="bibr" rid="CR11">11</xref>], or more sophisticated unsupervised data decomposition (SVA) [<xref ref-type="bibr" rid="CR12">12</xref>] to remove heterogeneity from multiple studies while preserving the biological signal of interest in the data, even when the experimental design across the studies are not balanced.</p><p>However, despite these useful existing approaches for data cleaning and combination, there are still significant gaps that need to be addressed for certain data integration scenarios. For example, the ComBat approach removes batch effects impacting both the means and variances of each gene across the batches. However, in some cases, the data might require a less (or more) extreme batch adjustment. Below, we present higher order moment-based metrics and visualizations for evaluating the extent to which batch effects impact the data. Then, at least in the case of less severe batch effects, we propose a simplified empirical Bayes approach for batch adjustment.</p><p>Another limitation of the current ComBat model is that it adjusts the data for each gene to match an overall, or common cross-batch mean, estimated using samples from all batches. While this approach is advantageous for cases with small sample size or where the batches are of similar caliber and size, this is not the best solution when one batch is of superior quality or can be considered a natural &#x02019;reference&#x02019;. In addition, the current ComBat approach suffers from sample &#x02019;set bias&#x02019; [<xref ref-type="bibr" rid="CR13">13</xref>], meaning that if samples or batches are added to or removed from the set of samples on hand, the batch adjustment must be reapplied, and the adjusted values will be different&#x02013;even for the samples that remained in the dataset in all scenarios. In some cases, the impact of this set bias can be significant. For example, consider a biomarker study, where a genomic signature is derived in one study batch (training set) and then later applied or validated in future samples/batches (test sets) which were not collected at the time of biomarker generation. Once the test sets are obtained and combined with the training data using ComBat, the post-ComBat training data may change and the biomarker may need to be regenerated. Many statistical tests that are commonly used for biomarker derivation, such as t-test and F-test, involve calculating data variance. As ComBat adjustment reduces or expands the variance for each gene, it will result in a different test statistics, followed by an increased or reduced P value. This may cause certain genes to be included or excluded from the biomarker list, resulting in a different biomarker from before. If ComBat is applied on multiple training/test combinations separately (i.e. say at different times), then the derived biomarker may be different between different dataset combinations. Therefore, the value of establishing the training set as a &#x02019;reference batch&#x02019; to which all future batches will be standardized would have a significant impact. This would allow the training data and biomarker to be fixed a priori but still enable the application of the biomarker on an unlimited set of future validation or clinical cohorts.</p><p>Although these alternative models for batch only represent alternative formulations of the original ComBat modeling approach, their implementation will have significant downstream impacts on certain batch combination scenarios. Below, we detail these modifications and demonstrate their utility and increased efficacy on real data examples.</p></sec><sec id="Sec2"><title>Methods</title><p>We present several approaches for improved diagnostics and batch effect adjustment for certain batch adjustment situations. We focus on developing models based on the ComBat empirical Bayes batch adjustment approach, although similar methods and models can be applied to other existing approaches. One set of diagnostic procedures attempts to characterize the distributional (mean, variance, skewness, kurtosis, etc) differences across batches. We present a solution for the cases where adjusting only the mean of the batch effect is sufficient for harmonizing the data across batches. In addition, we present an approach that allows the user to select a reference batch, or a batch that is left static after batch adjustment, and to which all the other batches are adjusted. This approach makes sense in situations where one batch or dataset is of better quality or less variable. In addition, this approach will be particularly helpful for biomarker studies, where one dataset is used for training a fixed biomarker, then the fixed biomarker is applied on multiple different batches or datasets, even at different times. This approach avoids the negative impacts of test set bias in the generation of the biomarker signatures. Below we describe the methodological developments for these cases.</p><sec id="Sec3"><title>ComBat batch adjustment</title><p>ComBat [<xref ref-type="bibr" rid="CR10">10</xref>] is a flexible and straightforward approach to remove technical artifacts due to processing facility and data batch. ComBat has been established as one of the most common approaches for combining genomic data across experiments, labs, and platforms [<xref ref-type="bibr" rid="CR14">14</xref>], and has been shown to be useful for data from a broad range of types and biological systems [<xref ref-type="bibr" rid="CR15">15</xref>, <xref ref-type="bibr" rid="CR16">16</xref>]. The ComBat batch adjustment approach assumes that batch effects represent non-biological but systematic shifts in the mean or variability of genomic features for all samples within a processing batch. ComBat assumes the genomic data (<italic>Y</italic><sub><italic>ijg</italic></sub>) for gene <italic>g</italic>, batch <italic>i</italic>, and sample <italic>j</italic> (within batch <italic>i</italic>) follows the model: 
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} Y_{ijg} = \alpha_{g} + X_{ij}\beta_{g} + \gamma_{ig} + \delta_{ig}\varepsilon_{ijg} \end{array} $$ \end{document}</tex-math><mml:math id="M2"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>Y</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b1;</mml:mi></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mi>&#x003b2;</mml:mi></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ig</mml:mtext></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b4;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ig</mml:mtext></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2263_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula></p><p>where <italic>&#x003b1;</italic><sub><italic>g</italic></sub> is the overall gene expression. <italic>X</italic><sub><italic>ij</italic></sub> is a known design matrix for sample conditions, and <italic>&#x003b2;</italic><sub><italic>g</italic></sub> is the vector of regression coefficients corresponding to <italic>X</italic><sub><italic>ij</italic></sub>. <italic>&#x003b3;</italic><sub><italic>ig</italic></sub> and <italic>&#x003b4;</italic><sub><italic>ig</italic></sub> represent the additive and multiplicative batch effects of batch <italic>i</italic> for gene <italic>g</italic>, which affect the mean and variance of gene expressions within batch <italic>i</italic>, respectively. The error terms, <italic>&#x003b5;</italic><sub><italic>ijg</italic></sub>, are assumed to follow a normal distribution with expected value of zero and variance <inline-formula id="IEq1"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\sigma _{g}^{2}$\end{document}</tex-math><mml:math id="M4"><mml:msubsup><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="12859_2018_2263_Article_IEq1.gif"/></alternatives></inline-formula>. ComBat assumes either parametric or nonparametric hierarchical Bayesian priors in the batch effect parameters (<italic>&#x003b3;</italic><sub><italic>ig</italic></sub> and <italic>&#x003b4;</italic><sub><italic>ig</italic></sub>) and uses an empirical Bayes procedure to estimate these parameters [<xref ref-type="bibr" rid="CR10">10</xref>]. This procedure pools information across genes in each batch to shrink the batch effect parameter estimates toward the overall mean of the batch effect empirical estimates. These are used to adjust the data for batch effects. This approach provides a robust and often more accurate adjustment for the batch effect on each gene.</p></sec><sec id="Sec4"><title>Moment-based diagnostics for batch effects</title><p>The ComBat model described above robustly estimates both the mean and the variance of each batch using empirical Bayes shrinkage, then adjusts the data according to these estimates. However, in some cases, adjusting only the mean of the batches may be sufficient for further analysis. In other scenarios (see examples in &#x0201c;&#x0201d; below), adjustment of the mean and variance is not sufficient, and thus the adjustment of higher order moments is needed. Here, we present multiple diagnostics for interrogating the shape of the distribution of batches to determine how batch effect should be adjusted.</p><p>As with ComBat, we assume that in the presence of batch effect, the mean and variance (as well as higher order moments) of gene expression demonstrate systematic differences across batches on a standardized scale [<xref ref-type="bibr" rid="CR10">10</xref>]. Thus, we standardize the data as we have done previously, namely by estimating the model (<xref rid="Equ1" ref-type="">1</xref>) above, obtaining the estimates for the parameters and calculating the standardized data, <italic>Z</italic><sub><italic>ijg</italic></sub>, as follows: 
<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} Z_{ijg} = \frac{Y_{ijg} - \hat{\alpha}_{g} - X_{ij}\hat{\beta}_{g}}{\hat{\sigma}_{g}} \end{array} $$ \end{document}</tex-math><mml:math id="M6"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:msub><mml:mrow><mml:mi>Y</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b1;</mml:mi></mml:mrow><mml:mo>&#x0005e;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b2;</mml:mi></mml:mrow><mml:mo>&#x0005e;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow><mml:mo>&#x0005e;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfrac></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2263_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula></p><p>After standardization, we assume the standardized data, <italic>Z</italic><sub><italic>ijg</italic></sub>, originate from a distribution with mean <italic>&#x003b3;</italic><sub><italic>ig</italic></sub>, variance <inline-formula id="IEq2"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\delta _{ig}^{2}$\end{document}</tex-math><mml:math id="M8"><mml:msubsup><mml:mrow><mml:mi>&#x003b4;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ig</mml:mtext></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="12859_2018_2263_Article_IEq2.gif"/></alternatives></inline-formula>, skewness <italic>&#x003b7;</italic><sub><italic>ig</italic></sub>, and kurtosis <italic>&#x003d5;</italic><sub><italic>ig</italic></sub>. In addition, consistent with the ComBat assumptions, we assume that each of these moments originates from a common distribution (henceforth denoted the <italic>hyper-distribution</italic>), namely that the <italic>&#x003b3;</italic><sub><italic>ig</italic></sub> are drawn from a distribution with mean <italic>&#x003b3;</italic><sub><italic>i</italic></sub> that is common across all genes. Similar assumptions of exchangeability across genes are made about the variance, skewness, and kurtosis.</p><p>We apply two tests of significance to individually test for significant differences in these moments across batches. In both of the tests, we estimate and conduct the test on the hyper-moments (i.e. moments of the hyper-distribution) across batches. The first test estimates the hyper-moments within each sample, whereas the other test estimates the hyper-moments within each gene. The first, sample-based test is more robust for small sample size, whereas the second, gene-based test is more robust and sensitive in larger sample size. Finally, for quantile-normalized data [<xref ref-type="bibr" rid="CR17">17</xref>], the sample-wise test will fail because quantile normalization will naturally force all moments to be the same across samples. So for quantile normalized data, the gene-wise test will be needed.</p><sec id="Sec5"><title>Sample-level moments</title><p>The first test is a sample-level test that estimates the hyper-moments by summarizing the moments of gene expression within each sample, and then conducts a standard or <italic>robust</italic> F-test (described below) to compare the moment estimates across batches. For example, for the mean, the sample-wise test first estimates the mean gene expression of each sample, namely 
<disp-formula id="Equ3"><label>3</label><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} \bar{\gamma}_{ij} = \frac{1}{n_{g}}\sum_{g}Z_{ijg} \end{array} $$ \end{document}</tex-math><mml:math id="M10"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>n</mml:mi></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfrac><mml:munder><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:munder><mml:msub><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2263_Article_Equ3.gif" position="anchor"/></alternatives></disp-formula></p><p>where <italic>n</italic><sub><italic>g</italic></sub> is the total number of genes. We then conduct an F-test on the <inline-formula id="IEq3"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\bar {\gamma }_{ij}$\end{document}</tex-math><mml:math id="M12"><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12859_2018_2263_Article_IEq3.gif"/></alternatives></inline-formula> values between batches. Similarly, the variance, skewness, and kurtosis of the <italic>Z</italic><sub><italic>ijg</italic></sub> are estimated across genes within each sample (using standard estimation approaches for these moments) and then tested for significant differences across batches in the same way as the mean. Overall this is not specifically testing the moments of the assumed ComBat model hyper-distribution, but rather the marginal distribution of the data and hyper-distributions of the data.</p></sec><sec id="Sec6"><title>Gene-level moments</title><p>The second test is a gene-level test that estimates the hyper-moments within each gene, using samples in each batch separately, and then conducts a robust F-test comparing the moment estimates across batches. For example, for the mean, the gene-wise test first estimates the mean of each gene across samples within a batch, namely 
<disp-formula id="Equ4"><label>4</label><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} \bar{\gamma}_{ig} = \frac{1}{n_{i}}\sum_{j}Z_{ijg} \end{array} $$ \end{document}</tex-math><mml:math id="M14"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ig</mml:mtext></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>n</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfrac><mml:munder><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:munder><mml:msub><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2263_Article_Equ4.gif" position="anchor"/></alternatives></disp-formula></p><p>where <italic>n</italic><sub><italic>i</italic></sub> is the total number of samples in batch <italic>i</italic>. We then conduct a test of significance on the <inline-formula id="IEq4"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\bar {\gamma }_{ig}$\end{document}</tex-math><mml:math id="M16"><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ig</mml:mtext></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12859_2018_2263_Article_IEq4.gif"/></alternatives></inline-formula> values between batches. Similarly, the variance, skewness, and kurtosis of the <italic>Z</italic><sub><italic>ijg</italic></sub> are estimated across samples within each batch for each gene (using standard estimation approaches for these moments) and then tested for significant differences across batches in the same way as the mean. Unlike the sample-wise test, this test more specifically follows the ComBat hierarchical model assumption, by first estimating the parameters drawn from the hyper-distribution.</p></sec><sec id="Sec7"><title>Robust F-test</title><p>In hypothesis testing, a large enough sample size may cause the <italic>P</italic>-value problem [<xref ref-type="bibr" rid="CR18">18</xref>]: small effects with no practical importance can be detected significant, as the <italic>P</italic>-value quickly drops to zero under a very large sample size. The <italic>P</italic>-value problem influences tests that are sensitive to the sample size, including the F-test used in this study. To address this problem and better interpret the results of the two tests above, we applied a robust F-test. The robust F-test is modified from standard F test, by adding a variance inflation factor in the F statistics, which accounts for the influence of the sample size in <italic>P</italic>-values. Details of the robust F-test are documented in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>.</p><p>The robust F-test is especially useful for the gene-level test, as the total degrees of freedom in this test is in fact the amount of moment estimates, which equals to the number of genes times the number of batches. This value can easily become very large in genomic studies. We used both the robust and the non-robust versions of F statistics in the sample- and gene-level tests, and evaluated and compared their performances in diagnosing the degree of batch effect in our example data.</p></sec></sec><sec id="Sec8"><title>Mean-only adjustment for batch effects</title><p>The current ComBat model adjusts for effects in both the mean and the variance across batches. However, for some datasets, after testing for the moments of the batch effect, it may be determined that differences are only present in the mean across batches. Other datasets may be expected to have variance differences across batches for non-technical reasons, such as in a study combining a in vitro perturbation experiment (low variance) with patient samples (high variance). For cases in which that batch differences are only present in the mean, we have modified the current ComBat model to only adjust the mean batch effect. Specifically, we modify the ComBat model (<xref rid="Equ1" ref-type="">1</xref>) as follows: 
<disp-formula id="Equ5"><label>5</label><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} Y_{ijg} = \alpha_{g} + X_{ij}\beta_{g} + \gamma_{ig} + \varepsilon_{ijg} \end{array} $$ \end{document}</tex-math><mml:math id="M18"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>Y</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b1;</mml:mi></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mi>&#x003b2;</mml:mi></mml:mrow><mml:mrow><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ig</mml:mtext></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2263_Article_Equ5.gif" position="anchor"/></alternatives></disp-formula></p><p>and then use the same approach for standardization and shrinkage as described previously with the exception of not estimating and adjusting for variance differences across batches.</p></sec><sec id="Sec9"><title>Reference batch adjustment</title><p>Many batch adjustment approaches, including ComBat, are dependent on the datasets in hand for their batch adjustments. In other words, if additional samples or batches of data are added, the batch adjustments and adjusted data would be different. We present a reference-based batch adjustment approach that uses one batch as the baseline for the batch adjustment. The reference batch is not changed and the other batches are adjusted to the mean and variance of the reference. Thus, as long as the reference batch does not change, the adjustments and adjusted data would be the same, regardless of the batches of data that are included in the dataset. This also allows batches of data to be adjusted at different times without impacting the results. This approach will be advantageous to data generating consortiums where data arrive sequentially in small batches. It will also be important for applications in personalized medicine where biomarkers need to be established and validated prior to the collection of patient data. For our reference-based version of ComBat, we will assume a model slightly different than the model (<xref rid="Equ1" ref-type="">1</xref>) presented above, namely: 
<disp-formula id="Equ6"><label>6</label><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} Y_{ijg} = \alpha_{rg} + X_{ij}\beta_{rg} + \gamma_{rig} + \delta_{rig}\varepsilon_{ijg} \end{array} $$ \end{document}</tex-math><mml:math id="M20"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>Y</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b1;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">rg</mml:mtext></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mi>&#x003b2;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">rg</mml:mtext></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">rig</mml:mtext></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003b4;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">rig</mml:mtext></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ijg</mml:mtext></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2263_Article_Equ6.gif" position="anchor"/></alternatives></disp-formula></p><p>where <italic>X</italic><sub><italic>ij</italic></sub> and <italic>&#x003b2;</italic><sub><italic>rg</italic></sub> are the design matrix and regression coefficients as described before, but <italic>&#x003b1;</italic><sub><italic>rg</italic></sub> is the average gene expression in the chosen reference batch (<italic>r</italic>). Furthermore, <italic>&#x003b3;</italic><sub><italic>rig</italic></sub> and <italic>&#x003b4;</italic><sub><italic>rig</italic></sub> represent the additive and multiplicative batch differences between the reference batch and batch <italic>i</italic> for gene <italic>g</italic>. The error terms, <italic>&#x003b5;</italic><sub><italic>ijg</italic></sub>, are assumed to follow a normal distribution with expected value of zero and a reference batch variance <inline-formula id="IEq5"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\sigma _{rg}^{2}$\end{document}</tex-math><mml:math id="M22"><mml:msubsup><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">rg</mml:mtext></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="12859_2018_2263_Article_IEq5.gif"/></alternatives></inline-formula>. The empirical Bayes estimates for <italic>&#x003b3;</italic><sub><italic>rig</italic></sub> and <italic>&#x003b4;</italic><sub><italic>rig</italic></sub> will be obtained as in the current ComBat approach.</p></sec><sec id="Sec10"><title>Software implementation</title><p>The models presented here have been integrated into the ComBat function available in the &#x02019;sva&#x02019; Bioconductor package (version 3.26.0) [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR19">19</xref>]. More specifically, ComBat now includes optional parameters &#x02019;mean.only&#x02019;, which if TRUE will only adjust the mean batch effect and not the variance, and &#x02019;ref.batch&#x02019;, which allows the user to specify the batch name or number to be used as the reference batch. Our moment-based diagnostic tests for the mean, variance, skewness, and kurtosis are now available in our &#x02019;BatchQC&#x02019; Bioconductor package [<xref ref-type="bibr" rid="CR20">20</xref>]. BatchQC is an R software package designed to automate many important evaluation tasks needed to properly combine data from multiple batches or studies. BatchQC conducts comprehensive exploratory analyses and constructs interactive graphics for genomic datasets to discover the sources of technical variation that are present across multiple sets of samples. BatchQC currently provides both the supervised diagnostics for known sources of technical variation (data generating batch, reagent date, RNA-quality, etc) as well as an unsupervised evaluation of batch effects to detect unmeasured non-biological variability or &#x02019;surrogate variables&#x02019; [<xref ref-type="bibr" rid="CR12">12</xref>].</p></sec><sec id="Sec11"><title>Dataset descriptions</title><sec id="Sec12"><title>Pathway simulation</title><p>We generated simulated data to represent a case where we (1) derive a gene expression signature of a biological pathway or drug perturbation, and (2) profile the signature into another batch of data to predict pathway activity (or drug efficacy). The study consists of two experimental batches which are designed as follows: batch 1 is given by a 200 (gene) by 6 (sample) matrix of expression data, where the columns contain three replicate samples before pathway activation and three after activation (i.e. overexpressing key pathway driving genes). Among the 200 genes, the first 100 represent &#x02019;signature genes&#x02019; that are differentially expressed (before vs. after) based on a &#x02019;before&#x02019; Gaussian distribution: <italic>N</italic>(0,0.1), and an &#x02019;after&#x02019; distribution: <italic>N</italic>(1,0.1). The rest of the genes are drawn from a <italic>N</italic>(0,0.1) distribution in all 6 samples, representing genes that do not respond to the pathway perturbation. Batch 2 consists of a 200 (gene; same genes as batch 1) by 600 (sample) matrix, and represents a large and highly variable patient data set. The 600 patients are divided equally into 6 subgroups with different levels of pathway activation between groups; signature genes are drawn from a <italic>N</italic>(<italic>&#x003bc;</italic>,10) distribution, where <italic>&#x003bc;</italic>=0.5,0.7,0.9,1.1,1.3, and 1.5 for the six subgroups. The control genes are drawn from a <italic>N</italic>(0.5,10) distribution. We set up these simulation studies based on the design of real signature profiling studies [<xref ref-type="bibr" rid="CR21">21</xref>], and selected parameters to capture the statistical properties of realistic gene expression distributions (Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). Simulation code for this dataset is available at <ext-link ext-link-type="uri" xlink:href="https://github.com/zhangyuqing/meanonly_reference_combat">https://github.com/zhangyuqing/meanonly_reference_combat</ext-link>.</p></sec><sec id="Sec13"><title>Bladder cancer</title><p>We used a previously published bladder cancer microarray dataset, which aims to measure gene expression in superficial transitional cell carcinoma (sTCC) in the presence and absence of carcinoma in situ (CIS) [<xref ref-type="bibr" rid="CR22">22</xref>]. This dataset contains 57 observations generated at 5 different processing times. It was previously established that the processing time is strongly confounded with CIS condition, and batch effect still exists for certain genes after normalization of the data [<xref ref-type="bibr" rid="CR19">19</xref>].</p></sec><sec id="Sec14"><title>Nitric oxide</title><p>This study was designed to investigate whether exposing mammalian cells to nitric oxide (NO) stabilizes mRNAs [<xref ref-type="bibr" rid="CR10">10</xref>]. Human lung fibroblast cells (IMR90) were exposed to NO for 1 h, then transcription inhibited together with control cells for 7.5 h. Expressions in the exposed sample and control cells are measured at 0 h and 7.5 h using Affymetrix HG-U133A microarray, resulting in 4 arrays for each cell pair. The experiment was repeated at 3 different times. The dataset contains the 3 batches of data, each containing 4 arrays of different treatment combinations, which leads to 12 samples in total.</p></sec><sec id="Sec15"><title>Oncogenic signature</title><p>The growth factor receptor network (GFRN) contributes to breast cancer progression and drug response. This RNA-Seq dataset is designed to develop gene signatures for several GFRN pathways: AKT, BAD, HER2, IGF1R, RAF1, KRAS, and EGFR. We used recombinant adenoviruses to express these genes in case samples and produce green fluorescent protein (GFP) in control samples, using replicates of human mammary epithelial cells (HMECs). RNA-Seq data are collected from these HMECs overexpressing GFRN genes and GFP controls [<xref ref-type="bibr" rid="CR21">21</xref>]. This dataset contains 89 samples, which are created in three batches: batch 1 contains 6 replicate samples of each for AKT, BAD, IGF1R, and RAF1, 5 replicates for HER2, and 12 replicates for GFP controls (GEO accession GSE83083); batch 2 consists of 9 replicates of each for three types of KRAS mutants and GFP control (GEO accession GSE83083); batch 3 contains 6 replicates of each for EGFR and its corresponding control (GEO accession GSE59765). We derived signatures from this dataset and predicted pathway activities and drug effects in cell line and patient datasets with ASSIGN [<xref ref-type="bibr" rid="CR23">23</xref>].</p></sec><sec id="Sec16"><title>Lung cancer</title><p>This dataset contains microarray measurements from histologically normal bronchial epithelium cells collected during bronchoscopy from non-smokers, former smokers, and current smokers. Samples are selected from various studies, which are divided into three batches A (GSE994 [<xref ref-type="bibr" rid="CR24">24</xref>], GSE4115 [<xref ref-type="bibr" rid="CR25">25</xref>, <xref ref-type="bibr" rid="CR26">26</xref>], GSE7895 [<xref ref-type="bibr" rid="CR27">27</xref>]), B (GSE66499, [<xref ref-type="bibr" rid="CR28">28</xref>]) and C (GSE37147, [<xref ref-type="bibr" rid="CR29">29</xref>]). The three sub-batches within A are ComBat adjusted before A is combined with B and C. The dataset contains 1051 samples, with 318 samples in batch A, 507 in batch B, and 226 in batch C.</p></sec></sec></sec><sec id="Sec17" sec-type="results"><title>Results</title><sec id="Sec18"><title>Moments-based tests of significance for batch effect</title><p>We introduced sample- and gene-wise tests to detect significant differences in the moments of batch effect distributions. We applied these tests to four different datasets (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>) and observed their properties. We found that the four datasets have different degrees of batch effect, and require different adjustment. The first dataset (bladder cancer) has significant mean differences between the batches (<italic>P</italic>&#x0003c;0.0001), but has <italic>P</italic>-values above 0.33 for variance differences for both tests. Since the bladder cancer dataset only exhibits batch effects in the mean and not in the variance, mean-only adjustment is more suitable for this dataset. In the nitric oxide dataset, however, mean/variance ComBat is required to remove the difference in batch variances detected by the gene-wise test (<italic>P</italic>=0.0005 without adjustment; <italic>P</italic>=0.0042 using mean-only ComBat). All four datasets show certain levels of significant differences in skewness and/or kurtosis even after the mean/variance ComBat is used, which suggests that adjustment for higher order moments may be required, which is beyond the scope of this paper.
<table-wrap id="Tab1"><label>Table 1</label><caption><p><italic>P</italic>-values from sample-wise and gene-wise <italic>robust</italic> tests on four datasets, before and after batch correction</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left"/><th align="left"/><th align="left" colspan="4">Sample-wise tests</th><th align="left" colspan="4">Gene-wise tests</th></tr><tr><th align="left">Dataset</th><th align="left">ComBat</th><th align="left">Mean</th><th align="left">Variance</th><th align="left">Skewness</th><th align="left">Kurtosis</th><th align="left">Mean</th><th align="left">Variance</th><th align="left">Skewness</th><th align="left">Kurtosis</th></tr></thead><tbody><tr><td align="left">Bladder cancer</td><td align="left">None</td><td align="left">&#x0003c;0.0001</td><td align="left">0.6495</td><td align="left">0.0539</td><td align="left">0.3149</td><td align="left">&#x0003c;0.0001</td><td align="left">0.3353</td><td align="left">&#x0003c;0.0001</td><td align="left">0.0012</td></tr><tr><td align="left"/><td align="left">Mean-only</td><td align="left">0.9998</td><td align="left">0.9557</td><td align="left">0.1496</td><td align="left">0.6236</td><td align="left">0.2011</td><td align="left">0.3618</td><td align="left">&#x0003c;0.0001</td><td align="left">0.0012</td></tr><tr><td align="left"/><td align="left">Mean/variance</td><td align="left">1</td><td align="left">0.8989</td><td align="left">0.1826</td><td align="left">0.2737</td><td align="left">0.2538</td><td align="left">0.9816</td><td align="left">&#x0003c;0.0001</td><td align="left">0.0012</td></tr><tr><td align="left">Nitric oxide</td><td align="left">None</td><td align="left">0.1007</td><td align="left">0.3565</td><td align="left">0.1009</td><td align="left">0.866</td><td align="left">&#x0003c;0.0001</td><td align="left">0.0005</td><td align="left">&#x0003c;0.0001</td><td align="left">0.9887</td></tr><tr><td align="left"/><td align="left">Mean-only</td><td align="left">0.9997</td><td align="left">0.577</td><td align="left">0.9838</td><td align="left">0.9485</td><td align="left">0.4595</td><td align="left">0.0042</td><td align="left">&#x0003c;0.0001</td><td align="left">0.9887</td></tr><tr><td align="left"/><td align="left">Mean/variance</td><td align="left">1</td><td align="left">0.982</td><td align="left">0.9847</td><td align="left">0.7013</td><td align="left">0.7245</td><td align="left">0.6219</td><td align="left">&#x0003c;0.0001</td><td align="left">0.9791</td></tr><tr><td align="left">Oncogenic signature</td><td align="left">None</td><td align="left">0.0011</td><td align="left">&#x0003c;0.0001</td><td align="left">0.0001</td><td align="left">0.0235</td><td align="left">&#x0003c;0.0001</td><td align="left">0.0001</td><td align="left">&#x0003c;0.0001</td><td align="left">0.5711</td></tr><tr><td align="left"/><td align="left">Mean/variance</td><td align="left">1</td><td align="left">0.7486</td><td align="left">0.5553</td><td align="left">0.9202</td><td align="left">0.0363</td><td align="left">0.8919</td><td align="left">&#x0003c;0.0001</td><td align="left">0.5711</td></tr><tr><td align="left">Lung cancer</td><td align="left">None</td><td align="left">&#x0003c;0.0001</td><td align="left">&#x0003c;0.0001</td><td align="left">&#x0003c;0.0001</td><td align="left">&#x0003c;0.0001</td><td align="left">&#x0003c;0.0001</td><td align="left">0.0106</td><td align="left">&#x0003c;0.0001</td><td align="left">0.4853</td></tr><tr><td align="left"/><td align="left">Mean/variance</td><td align="left">1</td><td align="left">0.9872</td><td align="left">0.0003</td><td align="left">0.9612</td><td align="left">0.0016</td><td align="left">0.9971</td><td align="left">&#x0003c;0.0001</td><td align="left">0.4853</td></tr></tbody></table><table-wrap-foot><p>The four datasets have different degrees of batch effect. The bladder cancer dataset has differences in batch mean, but does not show any batch effect in the variance. Mean-only ComBat is sufficient to adjust this dataset as there is no need to adjust the variance. In the nitric oxide dataset, the gene-wise test reports significant differences in both the mean and the variance. The full mean/variance ComBat is necessary to remove batch effects in this data. The mean/variance ComBat cannot adjust the skewness or kurtosis. All four datasets exhibit certain levels of batch effect in the skewness and/or kurtosis, which may call for methods that adjust these higher order moments. Results comparing robust and non-robust F tests are summarized in Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref></p></table-wrap-foot></table-wrap>
</p></sec><sec id="Sec19"><title>Mean-only batch adjustment</title><p>We modified the current mean/variance ComBat into a mean-only version of ComBat, which allows users to only adjust the batch effects in mean. It is recommended for cases where milder batch effects are expected (i.e. there is no need to adjust the variance). For example, we have shown in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> and Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref> that in the bladder cancer dataset, the mean, skewness and kurtosis are significantly different across batches. But there is no evidence for significant differences in the variance.</p><p>We applied both the mean-only and mean/variance ComBat on the bladder cancer dataset to compare their performances. We compared batch mean (<inline-formula id="IEq6"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\bar {\gamma }_{ij}$\end{document}</tex-math><mml:math id="M24"><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12859_2018_2263_Article_IEq6.gif"/></alternatives></inline-formula> from Eq. (<xref rid="Equ3" ref-type="">3</xref>)) and variance estimates collected within each sample in the unadjusted data, and in data adjusted by the two versions of ComBat (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). Consistent with the result in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> (<italic>P</italic>&#x0003c;0.0001), the mean estimates in the original data are significantly different across batches. In particular, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref><xref rid="Fig1" ref-type="fig">a</xref> shows mean-level differences in batch 2 compared to the other batches. Because variance estimates are not significantly different across batches, mean-only ComBat is sufficient to adjust the bladder cancer data. Neither version of ComBat makes the variance estimates more similarly distributed to each other than they are in the unadjusted data (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref><xref rid="Fig1" ref-type="fig">b</xref>). This shows that, based on the sample-wise test, adjusting both the mean and variance of batch effects in the bladder cancer data does not give better results than only adjusting the mean.
<fig id="Fig1"><label>Fig. 1</label><caption><p>Distribution of sample-wise mean and variance estimates from each batch in the bladder cancer data. Estimates are calculated within each sample as previously described. <bold>a</bold> Boxplots of sample-wise mean estimates (<inline-formula id="IEq7"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\bar {\gamma }_{ij}$\end{document}</tex-math><mml:math id="M26"><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>&#x003b3;</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">ij</mml:mtext></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12859_2018_2263_Article_IEq7.gif"/></alternatives></inline-formula>, as in Eq. (<xref rid="Equ3" ref-type="">3</xref>)) within each batch. The sample-wise mean estimates for batch 2 in the unadjusted data are significantly different from the other batches. Both mean-only and mean/variance ComBat adequately correct this batch 2 mean difference. <bold>b</bold> Boxplots of sample-wise variance estimates across batches. The sample-wise variance estimates are not significantly different in the unadjusted data. Adjusting either just the mean or both mean and variance does not makes the estimates more similarly distributed, meaning that adjusting the variance is not necessary</p></caption><graphic xlink:href="12859_2018_2263_Fig1_HTML" id="MO1"/></fig>
</p><p>From the gene-wise perspective, we found that the mean/variance ComBat overcorrects the data by shrinking the variance of batch 3 and 4 when pooling information from all batches (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1). Batch 3 (4 samples) and batch 4 (5 samples) have relatively fewer samples than the remaining three batches (11, 18 and 19 samples), and so the gene-wise variance estimates are more likely to be impacted by outlying samples. When mean/variance ComBat estimates the background variance using all batches, variance estimates become less variable in these two batches than in the other batches. Thus, the variance adjustment actually introduces differences in distribution across batches than in the original data (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1). In contrast, mean-only ComBat does not affect the variance estimates, thus avoiding the overcorrection problem. Therefore, mean-only ComBat is more justifiable than mean/variance ComBat for the bladder cancer data, where there is no need to adjust the variance.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Distribution of gene-wise variance estimates from each batch in the bladder cancer data. Batch 3 and batch 4 have smaller sample size than the other batches, thus their variance estimates are impacted more by outlying samples. Mean/variance ComBat brings all estimates to the same levels, over correcting the variance estimates in batches 3 and 4. This leads to unwanted, less variable gene expression (see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1). Mean-only ComBat does not affect or overcorrect the variance estimates</p></caption><graphic xlink:href="12859_2018_2263_Fig2_HTML" id="MO2"/></fig>
</p><sec id="Sec20"><title>Selecting the appropriate ComBat version for each dataset</title><p>Unlike in the bladder cancer data, mean-only ComBat is not sufficient for removing batch effects in the other three datasets. For example, the oncogenic signature dataset displays batch effect in both mean and variance. Mean/variance adjustment is required to remove the technical differences across batches (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S2). As another example, the gene-wise test detects a significant difference in variance (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>, <italic>P</italic>=0.0005) in the nitric oxide dataset. In this dataset, we found that mean-only ComBat cannot completely remove the significant difference in gene-wise variance estimates across batches (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S3). In this case, the mean/variance ComBat is necessary to remove the batch effect.</p><p>We emphasize that it is critical to select the appropriate ComBat version based on the degree of batch effect in different datasets. We simulated datasets with two condition groups, with some genes that differentially express between the two groups. Samples are divided in two batches. We simulated three types of batch effect in the data: 1) no batch effect, 2) only differences in the mean, and 3) both mean and variance batch effects. We applied both the mean-only and the mean-variance ComBat on each dataset. Then in both adjusted and unadjusted data, we performed differential expression analysis, and calculated the type I error rate and statistical power of our detection. We observed that using the ComBat model corresponding to the type of batch effect in the data is able to gain more power of detection, at the same cost of type I error rate increase (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4). These results show that a mean-variance model overfits the data in cases where a mean-only adjustment is needed, and that the mean-only model is not always sufficient. Therefore, it is necessary to evaluate the degree of batch effect, and select the appropriate ComBat version for batch correction. More details of this analysis are available in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>.</p><p>We also note that the nitric oxide dataset gives conflicting results for the sample-wise and gene-wise variance tests. We emphasize that, though both the sample- and gene-wise tests intend to detect differences in the hyper-moments across batches, they interrogate different aspects of the batch effect: sample-wise <italic>P</italic>-values reflect the difference in moments between batches by summarizing information over genes; while gene-wise <italic>P</italic>-values neglect differences between samples by summarizing across samples to estimate the gene-wise moments. We have shown in our previous work that multiple diagnostics are often needed to fully diagnose batch effects, as batch effects can be present in many different ways [<xref ref-type="bibr" rid="CR20">20</xref>]. Thus we recommend using mean/variance ComBat if either of the gene-wise or sample-wise tests show a significant batch effect.</p></sec></sec><sec id="Sec21"><title>Higher order moment-based batch adjustment</title><p>We observed evidence in all four datasets that the current ComBat mean/variance model does not completely remove all batch effects (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>). The bladder cancer has significant differences in gene-wise kurtosis even after mean/variance adjustment. The lung cancer data has remaining batch effect in sample-wise skewness. Also, the gene-wise test on skewness remains significant in all datasets (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). These results suggest that a more severe batch correction targeting the higher order moments may be necessary, indicating the need to develop additional methods and tools for these cases.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Distributions of higher order moments in the bladder cancer dataset after the mean/variance adjustment. The current mean/variance ComBat does not adjust higher order moments, thus distributions of these moment estimates remain significantly different (<bold>a</bold> sample-wise kurtosis: <italic>P</italic>=3.025<italic>e</italic>&#x02212;05 using <italic>non-robust</italic> test; <bold>b</bold> gene-wise skewness: <italic>P</italic>=0; <bold>c</bold> gene-wise kurtosis: <italic>P</italic>=0.0012 using <italic>robust</italic> test) across batches even after batch adjustment. These may cause problems in downstream analysis such as prediction tasks, and call for batch correction methods that adjust the higher order moments</p></caption><graphic xlink:href="12859_2018_2263_Fig3_HTML" id="MO3"/></fig>
</p></sec><sec id="Sec22"><title>Batch adjustment based on a reference batch</title><p>We used pathway signature projection examples to establish the benefits of reference-batch ComBat. First, we use a simulated pathway dataset to compare the benefits of the original and new reference-batch versions of ComBat. The goal of this simulation is to justify the necessity of reference-batch ComBat in scenarios when one batch is of superior quality than the other batches, or when biomarkers need to be generated in one dataset, fixed, and then applied to another dataset. We further illustrate that reference-batch ComBat yields better prediction results than the original ComBat in a real data signature profiling example for predicting drug efficacy.</p><sec id="Sec23"><title>Simulation study</title><p>We used simulated data to represent a gene expression signature study for an activated (or knocked down) biological pathway or drug perturbation that is profiled into another batch of data to predict pathway activity (or drug efficacy). Descriptions of these simulated datasets are detailed in the Dataset descriptions - Pathway simulation section. We used the two versions of ComBat (original and reference) to combine the two batches and to enable the prediction of the activity strength of the pathway from batch 1 into the batch 2 samples. Batch 1 was selected as reference for the reference-batch ComBat. Pathway activation levels are added in both versions ComBat as covariates. Results of not using activation levels as covariates is shown in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S5.</p><p>The original and reference-batch ComBat yield very different results in the two batches (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). The original ComBat uses the overall mean and variance of each gene across all batches as a background profile. Due to the large sample size and variances of the second batch, the estimated background profiles resemble batch 2 in variance. As a result, ComBat significantly increased the variance of batch 1 to match the variance of batch 2. As illustrated in Fig <xref rid="Fig4" ref-type="fig">4</xref>, the original ComBat results in a near complete loss of signal in batch 1. In comparison, reference-batch ComBat does not change the chosen reference (batch 1). It estimates the background means and variances based on batch 1, and adjusts batch 2 accordingly. After adjustment, the true signals of the pathway are recovered in the second batch. In this setting where batch 1 is of better quality, but batch 2 is more variable and larger in size, reference-batch ComBat retrieves biological signals of interest more successfully than the original version. This is further demonstrated quantitatively by the k-means clustering shown in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>. However, we note that the true activation level of signature genes are included as covariates in ComBat in this example. In a more realistic setting, the activation levels are unknown and cannot be included as covariates in the ComBat adjustment. When we applied ComBat without covariates (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S5), the pathway activation signals are less clear in both batches. However, the original version of ComBat still increases the variance in batch 1, making the data less ideal for signature development than those from the reference version.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Simulated pathway datasets before and after batch correction using original and reference-batch ComBat. The figure shows the heatmaps of the gene-by-sample expression matrices for the two simulated batches. Pathway activation levels are included as covariates in the two versions of ComBat. Batch 1 is less variable than batch 2, and is better in quality for identifying signatures for the pathway. Using the original ComBat does not remove the variance in batch 2. Instead, it causes a severe loss of signal in batch 1 by inflating the variance. Reference-batch ComBat does not change the chosen reference (batch 1) and leads to clearer signal detection in batch 2</p></caption><graphic xlink:href="12859_2018_2263_Fig4_HTML" id="MO4"/></fig><fig id="Fig5"><label>Fig. 5</label><caption><p>Cluster assignment of the 200 genes using k-means algorithm, where k=2. Color bars show the 200 genes from top to bottom, which corresponds to the gene labels in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>. The red and blue bars represent signature and control genes, respectively. During batch adjustment, true activation levels are included as covariates, as opposed to using no covariates in both versions of ComBat (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6). In the batch adjusted data, we first clustered genes into 2 groups without specifying the group sizes or labels. Then, clusters are assigned as signature and control by how it best accords with the original separation. <bold>a</bold> In batch 1, genes are correctly separated. But combining batch 2 with batch 1 without ComBat adjustment changes the signature / non-signature separation. Only 58.5% genes remain the same in the combined dataset. <bold>b</bold> Reference-batch ComBat gives cluster assignment that is more consistent with the true separation than original ComBat, in batch 1 only, batch 2 only, and the combined dataset of batch 1 and 2. These results suggest that the original ComBat breaks the similarity between genes in the same group (signature or control), where similarity is measured by the Euclidean distance. Only reference-batch ComBat is able to preserve this similarity</p></caption><graphic xlink:href="12859_2018_2263_Fig5_HTML" id="MO5"/></fig>
</p><p>To quantify the impact of batch correction on batch 1, we use a k-means clustering approach to attempt to identify the biomarker gene set (the first 100 genes are the signature genes and the subsequent 100 genes are unaffected by the perturbation). We treat the gene expression of each sample as high-dimensional vectors (batch 1: 6 samples ; batch 2: 600 samples). We used k-means clustering to divide these vectors into two groups for batch 1 alone, batch 2 alone, and batches 1 and 2 combined, with both ComBat adjustments (original and reference). We compared the clustering assignment of genes with the signature/non-signature separation, and calculated the accuracy as the maximum percentage of correctly classified genes in either way of labeling the two clusters as signatures and non-signatures. We evaluated how using original and reference-batch ComBat affects this accuracy.</p><p>In batch 1 without adjustment, all genes are correctly separated into signature and non-signature. However, this separation is confounded when batch 2 is combined with batch 1, as only 58.5% of the genes are correctly separated in the combined dataset. When using original ComBat, because the variance of batch 1 is artificially increased, the accuracy in batch 1 alone drops from 100 to 54.5%, and only 64.5% of the genes maintain their correct signature/non-signature labels after combining batch 2 with batch 1. In contrast, reference-batch ComBat keeps the cluster assignment in the adjusted batch 1 100% correct, because batch 1 stays intact as the reference, and 91% of the genes retain their correct labels in the combined dataset after adjustment. Thus reference ComBat improves the ability to identify biomarker genes across multiple studies compared to no adjustment and standard ComBat.</p></sec><sec id="Sec24"><title>EGFR signature and drug prediction</title><p>We also considered a real signature study using ASSIGN [<xref ref-type="bibr" rid="CR23">23</xref>], a pathway profiling toolkit based on a Bayesian factor analysis approach, to develop an EGFR pathway signature from the oncogenic signature dataset. ASSIGN allows for derivation of signatures from a pathway perturbation experiment, and adapts signatures from experimental datasets to disease. Our goal was to predict EGFR pathway activity in two RNA-Seq datasets: a breast cancer cell line panel [<xref ref-type="bibr" rid="CR30">30</xref>] and from breast carcinoma patients in TCGA [<xref ref-type="bibr" rid="CR31">31</xref>]. As in the simulation study, the two RNA-Seq test sets were first combined with the EGFR training set separately, to adjust for the batch effect between the training and the test set. ASSIGN then trains biomarkers from the adjusted EGFR training set, and makes predictions of pathway activity in both of the adjusted test sets. We compared the impact of using three versions of ComBat (original, mean-only and reference-batch), as well as frozen SVA and RUV on these predictions (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>).
<table-wrap id="Tab2"><label>Table 2</label><caption><p>Comparison between five batch correction methods in predicting pathway activity and drug efficacy</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left"/><th align="left"/><th align="left" colspan="2">Correlation: EGFR protein expression</th><th align="left" colspan="2">Drug response in cell lines</th></tr><tr><th align="left">ComBat version</th><th align="left">Common genes (cell line vs. TCGA)</th><th align="left">Cell line</th><th align="left">TCGA</th><th align="left">Erlotinib</th><th align="left">GSK1120212</th></tr></thead><tbody><tr><td align="left">Original ComBat</td><td align="left">20 (40%)</td><td align="left">0.316</td><td align="left">0.132</td><td align="left">0.360</td><td align="left">0.401</td></tr><tr><td align="left">Mean-only ComBat</td><td align="left">44 (88%)</td><td align="left">0.331</td><td align="left">-0.042</td><td align="left">0.294</td><td align="left">0.407</td></tr><tr><td align="left">Reference-batch ComBat</td><td align="left">50 (100%)</td><td align="left">0.442</td><td align="left">0.299</td><td align="left">0.415</td><td align="left">0.520</td></tr><tr><td align="left">Frozen SVA</td><td align="left">50 (100%)</td><td align="left">0.115</td><td align="left">0.092</td><td align="left">-0.09</td><td align="left">-0.131</td></tr><tr><td align="left">RUV</td><td align="left">40 (80%)</td><td align="left">0.287</td><td align="left">0.182</td><td align="left">0.332</td><td align="left">0.145</td></tr></tbody></table><table-wrap-foot><p>We combined the oncogenic signature dataset with the cell line and TCGA patient data separately to adjust for batch effect and enable the profiling of EGFR signatures from the oncogenic data to the test sets. We observed the set bias using original ComBat (40% same signature genes), mean-only ComBat (88% same genes), and RUV (80% same genes) to combine the datasets. Reference-batch ComBat and frozen SVA kept the same signature genes. Also, using reference-batch ComBat gave the highest correlations of prediction scores with both protein expression and drug response, among all five batch correction methods. These results support the benefit of using reference-batch ComBat in this context</p></table-wrap-foot></table-wrap>
</p><p>We used ASSIGN to develop a 50-gene signature from the EGFR samples in the training set [<xref ref-type="bibr" rid="CR21">21</xref>]. We first focus on the three versions of ComBat in the ability to generate replicable signatures. Because of the &#x02019;set bias&#x02019; caused by using original ComBat, only 20 (40%) of the signature genes are the same between the signatures developed in the training set adjusted against the cell line test set compared to the training set adjusted against the patient data. The same analysis performed with mean-only ComBat produced gene signatures with 44 (88%) of the genes shared between the two datasets. Because reference-batch ComBat does not change the EGFR dataset, the signatures are identical after (separate) adjustment with the two test sets. This points to the value of using reference ComBat to develop fixed genomic biomarkers that can be projected into multiple datasets, even at different times and without the need to combine all the data together.</p><p>We further compared the correlations of pathway predictions with the following validation datasets: (1) EGFR protein expression data (cell line and TCGA), and (2) EGFR inhibitor drug response (cell lines). As shown in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>, the correlations with protein expression for the reference-batch ComBat adjusted data (Cell Line: 0.442, TCGA: 0.299) are the highest in both test sets among all five methods. The correlations with drug response are also the highest when adjusting the data with reference-batch ComBat. For example, reference adjusted data yield a correlation of 0.415 with Erlotinib response and 0.520 with GSK1120212 response, compared to using the original (Erlotinib: 0.360, GSK: 0.401) and mean-only (Erlotinib: 0.294, GSK: 0.407) ComBat, frozen SVA (Erlotinib: -0.09, GSK: -0.131), and RUV (Erlotinib: 0.332, GSK: 0.145). These results strongly justify the benefits of the reference version of ComBat in pathway profiling and predicting drug efficacy.</p></sec></sec></sec><sec id="Sec25" sec-type="discussion"><title>Discussion</title><p>Combining multiple genomic datasets is beneficial for boosting statistical power of studies, especially in cases where data are generated in small batches necessitated by high experimental cost or difficulties in collecting samples. In addition, combining batches of data from similar experiments from different labs also provides opportunities for increased power for the detection of biological differences, as well as providing ways for testing/validating biomarkers generated in one batch of data. The presence of batch effects due to technical heterogeneity and batch effects due to different profiling platforms, protocols, or other factors can often confound the biological signals in data, which reduces the benefit of combining datasets. Despite the many previously developed techniques for batch adjustment, there are still situations where new methods need to be developed to appropriately or more effectively remove batch effects.</p><p>We introduced new models and tools for addressing batch correction in several scenarios based on the ComBat model. For example, many methods focus on adjusting the means and variances of batches, but some datasets require less (or more) stringent adjustments. We proposed two significance testing approaches, based on the batch effect moment distributions, to diagnose the degree of adjustment required. We visualized different degrees of batch effect detected by these tests in four experimental datasets. In the bladder cancer data where mean is significantly different across batches but not the variance, our proposed mean-only ComBat successfully removes the batch differences. In all four datasets, we presented evidence that adjusting both mean and variance is not sufficient to remove all batch effect, which calls for a method that performs a more severe batch adjustment.</p><p>For datasets where a less severe adjustment targeting the mean is sufficient, adjusting the variance may lead to unnecessary costs in downstream analysis. The original ComBat model pools all samples to estimate both the mean and the variance batch effects, which introduces a correlation structure between the samples. Such correlations may cause issues in further analysis, such as inflating type I error rate in differential expression detection, if we do not account for them properly. Therefore, for the datasets with only mean batch effect, our proposed mean-only ComBat is able to avoid the cost of estimating the variances, and is more beneficial than the mean-variance version.</p><p>It is important to highlight that a thorough evaluation of the degree of batch effect is necessary before applying any version of ComBat. We presented simulation results to demonstrate that using the ComBat model corresponding to the type of batch effect in the data is able to achieve more statistical power at the same cost of type I error rate in differential expression analysis. Therefore, we strongly suggest evaluating whether there is differences in mean and variances between batches with the moment-based significance tests, and selecting the appropriate version of ComBat based on the type of batch effect in the data.</p><p>Also, we noticed that the gene-wise and sample-wise tests yielded different results in a few of our datasets. To resolve a conflicting result from the two tests, we recommend visualizing the batch distributions as we did in this study, and decide the level of adjustment required based on all available information. BatchQC offers visualizations from various aspects including boxplot of gene expressions, dendrogram of clustering, and scatter plot of principal components, which can all assist in diagnosing the degree of batch effect.</p><p>In addition, we illustrated the benefits of selecting a reference batch in batch correction, in situations when one batch is high quality and less variable, and when biomarkers need to be developed from one study, fixed and validated on another study. Particularly in the situation where the goal is to generate fixed biomarkers, including an extra batch of data to those in hand can strongly affect the results, an issue described as &#x02018;set bias&#x02019;. In these situations, analysis need to be re-run in order to process the new batch of data, which can cause the biomarker genes to be largely different.</p><p>Our reference-batch ComBat is proven more successful in retrieving biological signals in signature profiling examples, where one batch shows a clear signal of biological conditions. We demonstrated that reference-batch ComBat resolves the &#x02018;set bias&#x02019; caused by the original version of ComBat in adding data sequentially, and yields better prediction of pathway activities and drug effects. Although these approaches are only alternative expressions of the ComBat model, their implementation has critical impact in real batch correction scenarios.</p></sec><sec id="Sec26" sec-type="conclusion"><title>Conclusions</title><p>We proposed diagnostic tools and improved models based on ComBat to evaluate and address batch effects in certain batch adjustment situations. The significance tests for batch differences can be used to determine the degree of batch effects to be adjusted. We purposed mean-only ComBat for the situation where a less severe adjustment is preferred. The reference-batch ComBat is able to leave one batch unchanged, which is especially useful for generating a fixed biomarker for further clinical use. We have shown in both simulations and real data that these proposed methods provide better solutions to batch effects than the existing ComBat model in their corresponding batch correction scenarios.</p></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec27"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12859_2018_2263_MOESM1_ESM.pdf"><label>Additional file 1</label><caption><p>Supplementary materials (PDF 1612 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12859_2018_2263_MOESM2_ESM.xlsx"><label>Additional file 2</label><caption><p>Mean and variance of gene expression distributions estimated from the EGFR signature and the TCGA breast cancer patient datasets. In TCGA, we used proteomics data of the patients, and binned the EGFR protein expression into 6 gradually increasing levels, partitioning all patients into 6 equal-sized groups. Mean and variances are estimated within each group. Up- and down-regulated genes are both EGFR signature genes derived by ASSIGN. The design and parameters for our simulation studies resemble the real estimates in these tables. Batch 1 represents the EGFR signature dataset with small gene variances, and a clear separation between the two condition groups in the expression of up-regulated genes. Batch 2 resembles the TCGA patient data with much larger variances than Batch 1. (XLSX 10 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12859_2018_2263_MOESM3_ESM.xlsx"><label>Additional file 3</label><caption><p>Comparison of <italic>P</italic>-values for applying robust and non-robust F tests on the four experimental datasets. (XLSX 12 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>CIS</term><def><p>carcinoma in situ</p></def></def-item><def-item><term>DWD</term><def><p>distance weighted discrimination</p></def></def-item><def-item><term>GEO</term><def><p>the Gene Expression Omnibus database</p></def></def-item><def-item><term>GFP</term><def><p>green fluorescent protein</p></def></def-item><def-item><term>GFRN</term><def><p>growth factor receptor network</p></def></def-item><def-item><term>HMEC</term><def><p>human mammary epithelial cell</p></def></def-item><def-item><term>NO</term><def><p>nitric oxide</p></def></def-item><def-item><term>RUV</term><def><p>remove unwanted variation</p></def></def-item><def-item><term>sTCC</term><def><p>superficial transitional cell carcinoma</p></def></def-item><def-item><term>SVA</term><def><p>surrogate variable analysis</p></def></def-item><def-item><term>SVD</term><def><p>singular value decomposition</p></def></def-item><def-item><term>TCGA</term><def><p>The Cancer Genome Atlas</p></def></def-item><def-item><term>XPN</term><def><p>cross-platform normalization</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Electronic supplementary material</bold></p><p>The online version of this article (10.1186/s12859-018-2263-6) contains supplementary material, which is available to authorized users.</p></fn><fn><p>David F. Jenkins and Solaiappan Manimaran contributed equally to this work.</p></fn></fn-group><ack><title>Acknowledgements</title><p>We would like to thank our collaborators in the Spira-Lenburg lab in Division of Computational Biomedicine, Boston University for providing the lung cancer data.</p><sec id="d29e2188"><title>Funding</title><p>This work was supported by grants from the NIH R01ES025002 and U01CA164720. The funding body, NIH, did not play any roles in the design of the study, the collection, analysis, and interpretation of data, or in writing the manuscript.</p></sec><sec id="d29e2193"><title>Availability of data and materials</title><p>The code to recreate the simulated datasets, and to reproduce the results of this study, are available at the author&#x02019;s Github repository, <ext-link ext-link-type="uri" xlink:href="https://github.com/zhangyuqing/meanonly_reference_combat">https://github.com/zhangyuqing/meanonly_reference_combat</ext-link>. The bladder cancer dataset is available in the &#x0201c;bladderbatch&#x0201d; Bioconductor package [<xref ref-type="bibr" rid="CR22">22</xref>]. The nitric oxide dataset is available from the corresponding author on reasonable request. Both the oncogenic signature and the lung cancer datasets are publicly available in the Gene Expression Omnibus database [<xref ref-type="bibr" rid="CR32">32</xref>], with accession numbers (oncogenic signature: GSE83083, GSE59765; lung cancer: GSE994, GSE4115, GSE7895, GSE66499, and GSE37147). See the &#x0201c;<xref rid="Sec11" ref-type="sec">Dataset descriptions</xref>&#x0201d; section for further details.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>YZ performed the data analysis, implemented the reference-batch ComBat software, visualized the results, and wrote and revised the manuscript. DFJ performed the pathway profiling analysis using EGFR signature data, and summarized the results. SM designed and implemented the tests for batch moment distributions, which are distributed through the BatchQC package. WEJ conceived and supervised the project, and wrote the first draft of the manuscript. All authors read and approved the final manuscript.</p></notes><notes notes-type="COI-statement"><sec id="d29e2218"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec id="d29e2223"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="d29e2228"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="d29e2233"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Soon</surname><given-names>WW</given-names></name><name><surname>Hariharan</surname><given-names>M</given-names></name><name><surname>Snyder</surname><given-names>MP</given-names></name></person-group><article-title>High-throughput sequencing for biology and medicine</article-title><source>Mol Syst Biol</source><year>2013</year><volume>9</volume><issue>1</issue><fpage>640</fpage><pub-id pub-id-type="doi">10.1038/msb.2012.61</pub-id><?supplied-pmid 23340846?><pub-id pub-id-type="pmid">23340846</pub-id></element-citation></ref><ref id="CR2"><label>2</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Reuter</surname><given-names>JA</given-names></name><name><surname>Spacek</surname><given-names>DV</given-names></name><name><surname>Snyder</surname><given-names>MP</given-names></name></person-group><article-title>High-throughput sequencing technologies</article-title><source>Mol Cell</source><year>2015</year><volume>58</volume><issue>4</issue><fpage>586</fpage><lpage>97</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2015.05.004</pub-id><?supplied-pmid 26000844?><pub-id pub-id-type="pmid">26000844</pub-id></element-citation></ref><ref id="CR3"><label>3</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Van Dijk</surname><given-names>EL</given-names></name><name><surname>Auger</surname><given-names>H</given-names></name><name><surname>Jaszczyszyn</surname><given-names>Y</given-names></name><name><surname>Thermes</surname><given-names>C</given-names></name></person-group><article-title>Ten years of next-generation sequencing technology</article-title><source>Trends Genet</source><year>2014</year><volume>30</volume><issue>9</issue><fpage>418</fpage><lpage>26</lpage><pub-id pub-id-type="doi">10.1016/j.tig.2014.07.001</pub-id><?supplied-pmid 25108476?><pub-id pub-id-type="pmid">25108476</pub-id></element-citation></ref><ref id="CR4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tomczak</surname><given-names>K</given-names></name><name><surname>Czerwi&#x00144;ska</surname><given-names>P</given-names></name><name><surname>Wiznerowicz</surname><given-names>M</given-names></name></person-group><article-title>The cancer genome atlas (tcga): an immeasurable source of knowledge</article-title><source>Contemp Oncol</source><year>2015</year><volume>19</volume><issue>1A</issue><fpage>68</fpage></element-citation></ref><ref id="CR5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kupfer</surname><given-names>P</given-names></name><name><surname>Guthke</surname><given-names>R</given-names></name><name><surname>Pohlers</surname><given-names>D</given-names></name><name><surname>Huber</surname><given-names>R</given-names></name><name><surname>Koczan</surname><given-names>D</given-names></name><name><surname>Kinne</surname><given-names>RW</given-names></name></person-group><article-title>Batch correction of microarray data substantially improves the identification of genes differentially expressed in rheumatoid arthritis and osteoarthritis</article-title><source>BMC Med Genom</source><year>2012</year><volume>5</volume><issue>1</issue><fpage>23</fpage><pub-id pub-id-type="doi">10.1186/1755-8794-5-23</pub-id></element-citation></ref><ref id="CR6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Luo</surname><given-names>J</given-names></name><name><surname>Schumacher</surname><given-names>M</given-names></name><name><surname>Scherer</surname><given-names>A</given-names></name><name><surname>Sanoudou</surname><given-names>D</given-names></name><name><surname>Megherbi</surname><given-names>D</given-names></name><name><surname>Davison</surname><given-names>T</given-names></name><name><surname>Shi</surname><given-names>T</given-names></name><name><surname>Tong</surname><given-names>W</given-names></name><name><surname>Shi</surname><given-names>L</given-names></name><name><surname>Hong</surname><given-names>H</given-names></name><etal/></person-group><article-title>A comparison of batch effect removal methods for enhancement of prediction performance using maqc-ii microarray gene expression data</article-title><source>Pharmacogenomics J</source><year>2010</year><volume>10</volume><issue>4</issue><fpage>278</fpage><lpage>91</lpage><pub-id pub-id-type="doi">10.1038/tpj.2010.57</pub-id><?supplied-pmid 20676067?><pub-id pub-id-type="pmid">20676067</pub-id></element-citation></ref><ref id="CR7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alter</surname><given-names>O</given-names></name><name><surname>Brown</surname><given-names>PO</given-names></name><name><surname>Botstein</surname><given-names>D</given-names></name></person-group><article-title>Singular value decomposition for genome-wide expression data processing and modeling</article-title><source>Proc Natl Acad Sci</source><year>2000</year><volume>97</volume><issue>18</issue><fpage>10101</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1073/pnas.97.18.10101</pub-id><?supplied-pmid 10963673?><pub-id pub-id-type="pmid">10963673</pub-id></element-citation></ref><ref id="CR8"><label>8</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Benito</surname><given-names>M</given-names></name><name><surname>Parker</surname><given-names>J</given-names></name><name><surname>Du</surname><given-names>Q</given-names></name><name><surname>Wu</surname><given-names>J</given-names></name><name><surname>Xiang</surname><given-names>D</given-names></name><name><surname>Perou</surname><given-names>CM</given-names></name><name><surname>Marron</surname><given-names>JS</given-names></name></person-group><article-title>Adjustment of systematic microarray data biases</article-title><source>Bioinformatics</source><year>2004</year><volume>20</volume><issue>1</issue><fpage>105</fpage><lpage>14</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btg385</pub-id><?supplied-pmid 14693816?><pub-id pub-id-type="pmid">14693816</pub-id></element-citation></ref><ref id="CR9"><label>9</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shabalin</surname><given-names>AA</given-names></name><name><surname>Tjelmeland</surname><given-names>H</given-names></name><name><surname>Fan</surname><given-names>C</given-names></name><name><surname>Perou</surname><given-names>CM</given-names></name><name><surname>Nobel</surname><given-names>AB</given-names></name></person-group><article-title>Merging two gene-expression studies via cross-platform normalization</article-title><source>Bioinformatics</source><year>2008</year><volume>24</volume><issue>9</issue><fpage>1154</fpage><lpage>60</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btn083</pub-id><?supplied-pmid 18325927?><pub-id pub-id-type="pmid">18325927</pub-id></element-citation></ref><ref id="CR10"><label>10</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Johnson</surname><given-names>WE</given-names></name><name><surname>Li</surname><given-names>C</given-names></name><name><surname>Rabinovic</surname><given-names>A</given-names></name></person-group><article-title>Adjusting batch effects in microarray expression data using empirical bayes methods</article-title><source>Biostatistics</source><year>2007</year><volume>8</volume><issue>1</issue><fpage>118</fpage><lpage>27</lpage><pub-id pub-id-type="doi">10.1093/biostatistics/kxj037</pub-id><?supplied-pmid 16632515?><pub-id pub-id-type="pmid">16632515</pub-id></element-citation></ref><ref id="CR11"><label>11</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gagnon-Bartsch</surname><given-names>JA</given-names></name><name><surname>Speed</surname><given-names>TP</given-names></name></person-group><article-title>Using control genes to correct for unwanted variation in microarray data</article-title><source>Biostatistics</source><year>2012</year><volume>13</volume><issue>3</issue><fpage>539</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1093/biostatistics/kxr034</pub-id><?supplied-pmid 22101192?><pub-id pub-id-type="pmid">22101192</pub-id></element-citation></ref><ref id="CR12"><label>12</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leek</surname><given-names>JT</given-names></name><name><surname>Johnson</surname><given-names>WE</given-names></name><name><surname>Parker</surname><given-names>HS</given-names></name><name><surname>Jaffe</surname><given-names>AE</given-names></name><name><surname>Storey</surname><given-names>JD</given-names></name></person-group><article-title>The sva package for removing batch effects and other unwanted variation in high-throughput experiments</article-title><source>Bioinformatics</source><year>2012</year><volume>28</volume><issue>6</issue><fpage>882</fpage><lpage>3</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bts034</pub-id><?supplied-pmid 22257669?><pub-id pub-id-type="pmid">22257669</pub-id></element-citation></ref><ref id="CR13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Patil</surname><given-names>P</given-names></name><name><surname>Bachant-Winner</surname><given-names>P-O</given-names></name><name><surname>Haibe-Kains</surname><given-names>B</given-names></name><name><surname>Leek</surname><given-names>JT</given-names></name></person-group><article-title>Test set bias affects reproducibility of gene signatures</article-title><source>Bioinformatics</source><year>2015</year><volume>31</volume><issue>14</issue><fpage>2318</fpage><lpage>23</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btv157</pub-id><?supplied-pmid 25788628?><pub-id pub-id-type="pmid">25788628</pub-id></element-citation></ref><ref id="CR14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lazar</surname><given-names>C</given-names></name><name><surname>Meganck</surname><given-names>S</given-names></name><name><surname>Taminau</surname><given-names>J</given-names></name><name><surname>Steenhoff</surname><given-names>D</given-names></name><name><surname>Coletta</surname><given-names>A</given-names></name><name><surname>Molter</surname><given-names>C</given-names></name><name><surname>Weiss-Sol&#x000ed;s</surname><given-names>DY</given-names></name><name><surname>Duque</surname><given-names>R</given-names></name><name><surname>Bersini</surname><given-names>H</given-names></name><name><surname>Now&#x000e9;</surname><given-names>A</given-names></name></person-group><article-title>Batch effect removal methods for microarray gene expression data integration: a survey</article-title><source>Brief Bioinform</source><year>2012</year><volume>14</volume><issue>4</issue><fpage>469</fpage><lpage>90</lpage><pub-id pub-id-type="doi">10.1093/bib/bbs037</pub-id><?supplied-pmid 22851511?><pub-id pub-id-type="pmid">22851511</pub-id></element-citation></ref><ref id="CR15"><label>15</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kitchen</surname><given-names>RR</given-names></name><name><surname>Sabine</surname><given-names>VS</given-names></name><name><surname>Sims</surname><given-names>AH</given-names></name><name><surname>Macaskill</surname><given-names>EJ</given-names></name><name><surname>Renshaw</surname><given-names>L</given-names></name><name><surname>Thomas</surname><given-names>JS</given-names></name><name><surname>van Hemert</surname><given-names>JI</given-names></name><name><surname>Dixon</surname><given-names>JM</given-names></name><name><surname>Bartlett</surname><given-names>JM</given-names></name></person-group><article-title>Correcting for intra-experiment variation in illumina beadchip data is necessary to generate robust gene-expression profiles</article-title><source>BMC Genom</source><year>2010</year><volume>11</volume><issue>1</issue><fpage>134</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-11-134</pub-id></element-citation></ref><ref id="CR16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>S&#x000ee;rbu</surname><given-names>A</given-names></name><name><surname>Ruskin</surname><given-names>HJ</given-names></name><name><surname>Crane</surname><given-names>M</given-names></name></person-group><article-title>Cross-platform microarray data normalisation for regulatory network inference</article-title><source>PLoS ONE</source><year>2010</year><volume>5</volume><issue>11</issue><fpage>13822</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0013822</pub-id></element-citation></ref><ref id="CR17"><label>17</label><mixed-citation publication-type="other">Bolstad BM, Irizarry RA, &#x000c5;strand M, Speed TP. A comparison of normalization methods for high density oligonucleotide array data based on variance and bias. Bioinformatics. 2003;19(2):185&#x02013;93.</mixed-citation></ref><ref id="CR18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lin</surname><given-names>M</given-names></name><name><surname>Lucas Jr</surname><given-names>HC</given-names></name><name><surname>Shmueli</surname><given-names>G</given-names></name></person-group><article-title>Research commentary&#x02014;too big to fail: large samples and the <italic>p</italic>-value problem</article-title><source>Inf Syst Res</source><year>2013</year><volume>24</volume><issue>4</issue><fpage>906</fpage><lpage>17</lpage><pub-id pub-id-type="doi">10.1287/isre.2013.0480</pub-id></element-citation></ref><ref id="CR19"><label>19</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leek</surname><given-names>JT</given-names></name><name><surname>Scharpf</surname><given-names>RB</given-names></name><name><surname>Bravo</surname><given-names>HC</given-names></name><name><surname>Simcha</surname><given-names>D</given-names></name><name><surname>Langmead</surname><given-names>B</given-names></name><name><surname>Johnson</surname><given-names>WE</given-names></name><name><surname>Geman</surname><given-names>D</given-names></name><name><surname>Baggerly</surname><given-names>K</given-names></name><name><surname>Irizarry</surname><given-names>RA</given-names></name></person-group><article-title>Tackling the widespread and critical impact of batch effects in high-throughput data</article-title><source>Nat Rev Genet</source><year>2010</year><volume>11</volume><issue>10</issue><fpage>733</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1038/nrg2825</pub-id><?supplied-pmid 20838408?><pub-id pub-id-type="pmid">20838408</pub-id></element-citation></ref><ref id="CR20"><label>20</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Manimaran</surname><given-names>S</given-names></name><name><surname>Selby</surname><given-names>HM</given-names></name><name><surname>Okrah</surname><given-names>K</given-names></name><name><surname>Ruberman</surname><given-names>C</given-names></name><name><surname>Leek</surname><given-names>JT</given-names></name><name><surname>Quackenbush</surname><given-names>J</given-names></name><name><surname>Haibe-Kains</surname><given-names>B</given-names></name><name><surname>Bravo</surname><given-names>HC</given-names></name><name><surname>Johnson</surname><given-names>WE</given-names></name></person-group><article-title>Batchqc: interactive software for evaluating sample and batch effects in genomic data</article-title><source>Bioinformatics</source><year>2016</year><volume>32</volume><issue>24</issue><fpage>3836</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btw538</pub-id><?supplied-pmid 27540268?><pub-id pub-id-type="pmid">27540268</pub-id></element-citation></ref><ref id="CR21"><label>21</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rahman</surname><given-names>M</given-names></name><name><surname>MacNeil</surname><given-names>SM</given-names></name><name><surname>Jenkins</surname><given-names>DF</given-names></name><name><surname>Shrestha</surname><given-names>G</given-names></name><name><surname>Wyatt</surname><given-names>SR</given-names></name><name><surname>McQuerry</surname><given-names>JA</given-names></name><name><surname>Piccolo</surname><given-names>SR</given-names></name><name><surname>Heiser</surname><given-names>LM</given-names></name><name><surname>Gray</surname><given-names>JW</given-names></name><name><surname>Johnson</surname><given-names>WE</given-names></name><etal/></person-group><article-title>Activity of distinct growth factor receptor network components in breast tumors uncovers two biologically relevant subtypes</article-title><source>Genome Med</source><year>2017</year><volume>9</volume><issue>1</issue><fpage>40</fpage><pub-id pub-id-type="doi">10.1186/s13073-017-0429-x</pub-id><?supplied-pmid 28446242?><pub-id pub-id-type="pmid">28446242</pub-id></element-citation></ref><ref id="CR22"><label>22</label><mixed-citation publication-type="other">Leek JT. bladderbatch: Bladder gene expression data illustrating batch effects. R package version 1.18.0. 2018. Available at https://www.bioconductor.org/packages/release/data/experiment/html/bladderbatch.html. Accessed 30 June 2018.</mixed-citation></ref><ref id="CR23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shen</surname><given-names>Y</given-names></name><name><surname>Rahman</surname><given-names>M</given-names></name><name><surname>Piccolo</surname><given-names>SR</given-names></name><name><surname>Gusenleitner</surname><given-names>D</given-names></name><name><surname>El-Chaar</surname><given-names>NN</given-names></name><name><surname>Cheng</surname><given-names>L</given-names></name><name><surname>Monti</surname><given-names>S</given-names></name><name><surname>Bild</surname><given-names>AH</given-names></name><name><surname>Johnson</surname><given-names>WE</given-names></name></person-group><article-title>Assign: context-specific genomic profiling of multiple heterogeneous biological pathways</article-title><source>Bioinformatics</source><year>2015</year><volume>31</volume><issue>11</issue><fpage>1745</fpage><lpage>53</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btv031</pub-id><?supplied-pmid 25617415?><pub-id pub-id-type="pmid">25617415</pub-id></element-citation></ref><ref id="CR24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Spira</surname><given-names>A</given-names></name><name><surname>Beane</surname><given-names>J</given-names></name><name><surname>Shah</surname><given-names>V</given-names></name><name><surname>Liu</surname><given-names>G</given-names></name><name><surname>Schembri</surname><given-names>F</given-names></name><name><surname>Yang</surname><given-names>X</given-names></name><name><surname>Palma</surname><given-names>J</given-names></name><name><surname>Brody</surname><given-names>JS</given-names></name></person-group><article-title>Effects of cigarette smoke on the human airway epithelial cell transcriptome</article-title><source>Proc Natl Acad Sci U S A</source><year>2004</year><volume>101</volume><issue>27</issue><fpage>10143</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1073/pnas.0401422101</pub-id><?supplied-pmid 15210990?><pub-id pub-id-type="pmid">15210990</pub-id></element-citation></ref><ref id="CR25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Spira</surname><given-names>A</given-names></name><name><surname>Beane</surname><given-names>JE</given-names></name><name><surname>Shah</surname><given-names>V</given-names></name><name><surname>Steiling</surname><given-names>K</given-names></name><name><surname>Liu</surname><given-names>G</given-names></name><name><surname>Schembri</surname><given-names>F</given-names></name><name><surname>Gilman</surname><given-names>S</given-names></name><name><surname>Dumas</surname><given-names>Y-M</given-names></name><name><surname>Calner</surname><given-names>P</given-names></name><name><surname>Sebastiani</surname><given-names>P</given-names></name><etal/></person-group><article-title>Airway epithelial gene expression in the diagnostic evaluation of smokers with suspect lung cancer</article-title><source>Nat Med</source><year>2007</year><volume>13</volume><issue>3</issue><fpage>361</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1038/nm1556</pub-id><?supplied-pmid 17334370?><pub-id pub-id-type="pmid">17334370</pub-id></element-citation></ref><ref id="CR26"><label>26</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gustafson</surname><given-names>AM</given-names></name><name><surname>Soldi</surname><given-names>R</given-names></name><name><surname>Anderlind</surname><given-names>C</given-names></name><name><surname>Scholand</surname><given-names>MB</given-names></name><name><surname>Qian</surname><given-names>J</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>Cooper</surname><given-names>K</given-names></name><name><surname>Walker</surname><given-names>D</given-names></name><name><surname>McWilliams</surname><given-names>A</given-names></name><name><surname>Liu</surname><given-names>G</given-names></name><etal/></person-group><article-title>Airway pi3k pathway activation is an early and reversible event in lung cancer development</article-title><source>Sci Transl Med</source><year>2010</year><volume>2</volume><issue>26</issue><fpage>26</fpage><lpage>252625</lpage><pub-id pub-id-type="doi">10.1126/scitranslmed.3000251</pub-id></element-citation></ref><ref id="CR27"><label>27</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Beane</surname><given-names>J</given-names></name><name><surname>Sebastiani</surname><given-names>P</given-names></name><name><surname>Liu</surname><given-names>G</given-names></name><name><surname>Brody</surname><given-names>JS</given-names></name><name><surname>Lenburg</surname><given-names>ME</given-names></name><name><surname>Spira</surname><given-names>A</given-names></name></person-group><article-title>Reversible and permanent effects of tobacco smoke exposure on airway epithelial gene expression</article-title><source>Genome Biol</source><year>2007</year><volume>8</volume><issue>9</issue><fpage>201</fpage><pub-id pub-id-type="doi">10.1186/gb-2007-8-9-r201</pub-id><pub-id pub-id-type="pmid">17274841</pub-id></element-citation></ref><ref id="CR28"><label>28</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Silvestri</surname><given-names>GA</given-names></name><name><surname>Vachani</surname><given-names>A</given-names></name><name><surname>Whitney</surname><given-names>D</given-names></name><name><surname>Elashoff</surname><given-names>M</given-names></name><name><surname>Porta Smith</surname><given-names>K</given-names></name><name><surname>Ferguson</surname><given-names>JS</given-names></name><name><surname>Parsons</surname><given-names>E</given-names></name><name><surname>Mitra</surname><given-names>N</given-names></name><name><surname>Brody</surname><given-names>J</given-names></name><name><surname>Lenburg</surname><given-names>ME</given-names></name><etal/></person-group><article-title>A bronchial genomic classifier for the diagnostic evaluation of lung cancer</article-title><source>N Engl J Med</source><year>2015</year><volume>373</volume><issue>3</issue><fpage>243</fpage><lpage>51</lpage><pub-id pub-id-type="doi">10.1056/NEJMoa1504601</pub-id><?supplied-pmid 25981554?><pub-id pub-id-type="pmid">25981554</pub-id></element-citation></ref><ref id="CR29"><label>29</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Steiling</surname><given-names>K</given-names></name><name><surname>Van Den Berge</surname><given-names>M</given-names></name><name><surname>Hijazi</surname><given-names>K</given-names></name><name><surname>Florido</surname><given-names>R</given-names></name><name><surname>Campbell</surname><given-names>J</given-names></name><name><surname>Liu</surname><given-names>G</given-names></name><name><surname>Xiao</surname><given-names>J</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>Duclos</surname><given-names>G</given-names></name><name><surname>Drizik</surname><given-names>E</given-names></name><etal/></person-group><article-title>A dynamic bronchial airway gene expression signature of chronic obstructive pulmonary disease and lung function impairment</article-title><source>Am J Respir Crit Care Med</source><year>2013</year><volume>187</volume><issue>9</issue><fpage>933</fpage><lpage>42</lpage><pub-id pub-id-type="doi">10.1164/rccm.201208-1449OC</pub-id><?supplied-pmid 23471465?><pub-id pub-id-type="pmid">23471465</pub-id></element-citation></ref><ref id="CR30"><label>30</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Daemen</surname><given-names>A</given-names></name><name><surname>Griffith</surname><given-names>OL</given-names></name><name><surname>Heiser</surname><given-names>LM</given-names></name><name><surname>Wang</surname><given-names>NJ</given-names></name><name><surname>Enache</surname><given-names>OM</given-names></name><name><surname>Sanborn</surname><given-names>Z</given-names></name><name><surname>Pepin</surname><given-names>F</given-names></name><name><surname>Durinck</surname><given-names>S</given-names></name><name><surname>Korkola</surname><given-names>JE</given-names></name><name><surname>Griffith</surname><given-names>M</given-names></name><etal/></person-group><article-title>Modeling precision treatment of breast cancer</article-title><source>Genome Biol</source><year>2013</year><volume>14</volume><issue>10</issue><fpage>110</fpage><pub-id pub-id-type="doi">10.1186/gb-2013-14-10-r110</pub-id><pub-id pub-id-type="pmid">23651872</pub-id></element-citation></ref><ref id="CR31"><label>31</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Network</surname><given-names>CGA</given-names></name><etal/></person-group><article-title>Comprehensive molecular portraits of human breast tumours</article-title><source>Nature</source><year>2012</year><volume>490</volume><issue>7418</issue><fpage>61</fpage><lpage>70</lpage><pub-id pub-id-type="doi">10.1038/nature11412</pub-id><pub-id pub-id-type="pmid">23000897</pub-id></element-citation></ref><ref id="CR32"><label>32</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edgar</surname><given-names>R</given-names></name><name><surname>Domrachev</surname><given-names>M</given-names></name><name><surname>Lash</surname><given-names>AE</given-names></name></person-group><article-title>Gene expression omnibus: Ncbi gene expression and hybridization array data repository</article-title><source>Nucleic Acids Res</source><year>2002</year><volume>30</volume><issue>1</issue><fpage>207</fpage><lpage>10</lpage><pub-id pub-id-type="doi">10.1093/nar/30.1.207</pub-id><?supplied-pmid 11752295?><pub-id pub-id-type="pmid">11752295</pub-id></element-citation></ref></ref-list></back></article>