<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.3 20210610//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1-3.dtd?><?SourceDTD.Version 1.3?><?ConverterInfo.XSLTName jats2jats3.xsl?><?ConverterInfo.Version 1?><?properties open_access?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">Sensors (Basel)</journal-id><journal-id journal-id-type="iso-abbrev">Sensors (Basel)</journal-id><journal-id journal-id-type="publisher-id">sensors</journal-id><journal-title-group><journal-title>Sensors (Basel, Switzerland)</journal-title></journal-title-group><issn pub-type="epub">1424-8220</issn><publisher><publisher-name>MDPI</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">10181759</article-id><article-id pub-id-type="doi">10.3390/s23094491</article-id><article-id pub-id-type="publisher-id">sensors-23-04491</article-id><article-categories><subj-group subj-group-type="heading"><subject>Article</subject></subj-group></article-categories><title-group><article-title>SNOWED: Automatically Constructed Dataset of Satellite Imagery for Water Edge Measurements</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Andria</surname><given-names>Gregorio</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Methodology" vocab-term-identifier="https://credit.niso.org/contributor-roles/methodology/">Methodology</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Investigation" vocab-term-identifier="https://credit.niso.org/contributor-roles/investigation/">Investigation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Visualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/visualization/">Visualization</role></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0002-2632-7357</contrib-id><name><surname>Scarpetta</surname><given-names>Marco</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Conceptualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Methodology" vocab-term-identifier="https://credit.niso.org/contributor-roles/methodology/">Methodology</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Software" vocab-term-identifier="https://credit.niso.org/contributor-roles/software/">Software</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Validation" vocab-term-identifier="https://credit.niso.org/contributor-roles/validation/">Validation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Formal analysis" vocab-term-identifier="https://credit.niso.org/contributor-roles/formal-analysis/">Formal analysis</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Investigation" vocab-term-identifier="https://credit.niso.org/contributor-roles/investigation/">Investigation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Data curation" vocab-term-identifier="https://credit.niso.org/contributor-roles/data-curation/">Data curation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; original draft" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-original-draft/">Writing &#x02013; original draft</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Visualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/visualization/">Visualization</role><xref rid="c1-sensors-23-04491" ref-type="corresp">*</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0002-7827-1156</contrib-id><name><surname>Spadavecchia</surname><given-names>Maurizio</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Conceptualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Methodology" vocab-term-identifier="https://credit.niso.org/contributor-roles/methodology/">Methodology</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Validation" vocab-term-identifier="https://credit.niso.org/contributor-roles/validation/">Validation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Formal analysis" vocab-term-identifier="https://credit.niso.org/contributor-roles/formal-analysis/">Formal analysis</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Investigation" vocab-term-identifier="https://credit.niso.org/contributor-roles/investigation/">Investigation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Resources" vocab-term-identifier="https://credit.niso.org/contributor-roles/resources/">Resources</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; original draft" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-original-draft/">Writing &#x02013; original draft</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Visualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/visualization/">Visualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Project administration" vocab-term-identifier="https://credit.niso.org/contributor-roles/project-administration/">Project administration</role><xref rid="c1-sensors-23-04491" ref-type="corresp">*</xref></contrib><contrib contrib-type="author"><name><surname>Affuso</surname><given-names>Paolo</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Conceptualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Methodology" vocab-term-identifier="https://credit.niso.org/contributor-roles/methodology/">Methodology</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Software" vocab-term-identifier="https://credit.niso.org/contributor-roles/software/">Software</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Investigation" vocab-term-identifier="https://credit.niso.org/contributor-roles/investigation/">Investigation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Data curation" vocab-term-identifier="https://credit.niso.org/contributor-roles/data-curation/">Data curation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0001-9209-4195</contrib-id><name><surname>Giaquinto</surname><given-names>Nicola</given-names></name><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Conceptualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/conceptualization/">Conceptualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Methodology" vocab-term-identifier="https://credit.niso.org/contributor-roles/methodology/">Methodology</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Validation" vocab-term-identifier="https://credit.niso.org/contributor-roles/validation/">Validation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Formal analysis" vocab-term-identifier="https://credit.niso.org/contributor-roles/formal-analysis/">Formal analysis</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Investigation" vocab-term-identifier="https://credit.niso.org/contributor-roles/investigation/">Investigation</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Resources" vocab-term-identifier="https://credit.niso.org/contributor-roles/resources/">Resources</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; original draft" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-original-draft/">Writing &#x02013; original draft</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Writing &#x02013; review &#x00026; editing" vocab-term-identifier="https://credit.niso.org/contributor-roles/writing-review-editing/">Writing &#x02013; review &#x00026; editing</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Visualization" vocab-term-identifier="https://credit.niso.org/contributor-roles/visualization/">Visualization</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Supervision" vocab-term-identifier="https://credit.niso.org/contributor-roles/supervision/">Supervision</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Project administration" vocab-term-identifier="https://credit.niso.org/contributor-roles/project-administration/">Project administration</role><role vocab="credit" vocab-identifier="https://credit.niso.org/" vocab-term="Funding acquisition" vocab-term-identifier="https://credit.niso.org/contributor-roles/funding-acquisition/">Funding acquisition</role></contrib></contrib-group><contrib-group><contrib contrib-type="editor"><name><surname>Serio</surname><given-names>Carmine</given-names></name><role>Academic Editor</role></contrib></contrib-group><aff id="af1-sensors-23-04491">Department of Electrical and Information Engineering, Polytechnic University of Bari, Via E. Orabona 4, 70125 Bari, Italy<email><EMAIL></email> (N.G.)</aff><author-notes><corresp id="c1-sensors-23-04491"><label>*</label>Correspondence: <email><EMAIL></email> (M.S.); <email><EMAIL></email> (M.S.)</corresp></author-notes><pub-date pub-type="epub"><day>05</day><month>5</month><year>2023</year></pub-date><pub-date pub-type="collection"><month>5</month><year>2023</year></pub-date><volume>23</volume><issue>9</issue><elocation-id>4491</elocation-id><history><date date-type="received"><day>22</day><month>2</month><year>2023</year></date><date date-type="rev-recd"><day>28</day><month>4</month><year>2023</year></date><date date-type="accepted"><day>03</day><month>5</month><year>2023</year></date></history><permissions><copyright-statement>&#x000a9; 2023 by the authors.</copyright-statement><copyright-year>2023</copyright-year><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (<ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">https://creativecommons.org/licenses/by/4.0/</ext-link>).</license-p></license></permissions><abstract><p>Monitoring the shoreline over time is essential to quickly identify and mitigate environmental issues such as coastal erosion. Monitoring using satellite images has two great advantages, i.e., global coverage and frequent measurement updates; but adequate methods are needed to extract shoreline information from such images. To this purpose, there are valuable non-supervised methods, but more recent research has concentrated on deep learning because of its greater potential in terms of generality, flexibility, and measurement accuracy, which, in contrast, derive from the information contained in large datasets of labeled samples. The first problem to solve, therefore, lies in obtaining large datasets suitable for this specific measurement problem, and this is a difficult task, typically requiring human analysis of a large number of images. In this article, we propose a technique to automatically create a dataset of labeled satellite images suitable for training machine learning models for shoreline detection. The method is based on the integration of data from satellite photos and data from certified, publicly accessible shoreline data. It involves several automatic processing steps, aimed at building the best possible dataset, with images including both sea and land regions, and correct labeling also in the presence of complicated water edges (which can be open or closed curves). The use of independently certified measurements for labeling the satellite images avoids the great work required to manually annotate them by visual inspection, as is done in other works in the literature. This is especially true when convoluted shorelines are considered. In addition, possible errors due to the subjective interpretation of satellite images are also eliminated. The method is developed and used specifically to build a new dataset of Sentinel-2 images, denoted SNOWED; but is applicable to different satellite images with trivial modifications. The accuracy of labels in SNOWED is directly determined by the uncertainty of the shoreline data used, which leads to sub-pixel errors in most cases. Furthermore, the quality of the SNOWED dataset is assessed through the visual comparison of a random sample of images and their corresponding labels, and its functionality is shown by training a neural model for sea&#x02013;land segmentation.</p></abstract><kwd-group><kwd>satellite monitoring</kwd><kwd>deep learning</kwd><kwd>sea&#x02013;land segmentation</kwd><kwd>shoreline detection</kwd><kwd>AI-based measurements</kwd><kwd>automatic labeled dataset construction</kwd><kwd>Sentinel-2</kwd><kwd>benchmark datasets</kwd></kwd-group><funding-group><award-group><funding-source>Polytechnic University of Bari</funding-source></award-group><award-group><funding-source>PON-MITIGO</funding-source><award-id>ARS01_00964</award-id></award-group><funding-statement>This research was funded by Polytechnic University of Bari and by research project PON-MITIGO (ARS01_00964).</funding-statement></funding-group></article-meta></front><body><sec sec-type="intro" id="sec1-sensors-23-04491"><title>1. Introduction</title><p>Coastlines are crucial ecosystems with both environmental and economic significance, as nearly half of the world&#x02019;s population lives within 100 km of the sea [<xref rid="B1-sensors-23-04491" ref-type="bibr">1</xref>]. These areas face various threats, including, fishing, pollution, shipping, and various consequences of climate change [<xref rid="B2-sensors-23-04491" ref-type="bibr">2</xref>,<xref rid="B3-sensors-23-04491" ref-type="bibr">3</xref>,<xref rid="B4-sensors-23-04491" ref-type="bibr">4</xref>], making it imperative to monitor them for early detection of potential issues such as coastal erosion, that can cause harm to the environment and human settlements. Coastal monitoring can include detecting microplastics [<xref rid="B5-sensors-23-04491" ref-type="bibr">5</xref>,<xref rid="B6-sensors-23-04491" ref-type="bibr">6</xref>], and monitoring seagrasses [<xref rid="B7-sensors-23-04491" ref-type="bibr">7</xref>,<xref rid="B8-sensors-23-04491" ref-type="bibr">8</xref>], water quality [<xref rid="B9-sensors-23-04491" ref-type="bibr">9</xref>,<xref rid="B10-sensors-23-04491" ref-type="bibr">10</xref>], and antibiotics pollution [<xref rid="B11-sensors-23-04491" ref-type="bibr">11</xref>,<xref rid="B12-sensors-23-04491" ref-type="bibr">12</xref>] among others. Monitoring using in situ measurements is the most precise, but it can be costly and time-consuming, especially for large areas and/or frequent measurements. Remote sensing is an alternative solution that has evolved from aerial imagery taken from aircraft for the use of Unmanned Aerial Vehicles (UAVs) and Unmanned Underwater Vehicles (UUVs). Such methods of remote sensing offer advantages over in situ measurements, but still require extensive human intervention and specialized technologies.</p><p>More recently, satellite imagery has become a promising additional monitoring technique. Satellite data are characterized by global coverage and high temporal resolution and are often publicly accessible. Sentinel-2 and Landsat 8 are two of the most used Earth observation satellites, capturing multispectral images of the Earth&#x02019;s surface with a resolution of up to 10 m. They provide, for a wide range of users including governments, academic institutions, and private companies, valuable information for monitoring changes in land cover and land use, as well as for detecting and mapping natural hazards [<xref rid="B13-sensors-23-04491" ref-type="bibr">13</xref>,<xref rid="B14-sensors-23-04491" ref-type="bibr">14</xref>,<xref rid="B15-sensors-23-04491" ref-type="bibr">15</xref>]. The revisiting time of a few days enables near real-time monitoring of dynamic events on the earth&#x02019;s surface. The increasing demand for high-quality earth observation data makes Sentinel-2 and Landsat 8 expected to remain key players in the earth observation satellite market in the future.</p><p>Lines delimiting water regions may be extracted from satellite images using traditional signal processing methods. Even in the AI era, these methods are valuable and often optimal tools to extract information from signals and images [<xref rid="B16-sensors-23-04491" ref-type="bibr">16</xref>,<xref rid="B17-sensors-23-04491" ref-type="bibr">17</xref>,<xref rid="B18-sensors-23-04491" ref-type="bibr">18</xref>,<xref rid="B19-sensors-23-04491" ref-type="bibr">19</xref>,<xref rid="B20-sensors-23-04491" ref-type="bibr">20</xref>,<xref rid="B21-sensors-23-04491" ref-type="bibr">21</xref>]. As regards the specific topic of coastline monitoring, edge detection algorithms are used in [<xref rid="B16-sensors-23-04491" ref-type="bibr">16</xref>] for Sentinel Synthetic Aperture Radar (SAR) images, obtaining an extracted coastline with a mean distance of 1 pixel from the reference shoreline, measured through in situ analysis. In [<xref rid="B22-sensors-23-04491" ref-type="bibr">22</xref>] coastline is achieved from very-high-resolution Pl&#x000e9;iades imagery using the Normalized Difference Water Index (NDWI), which is one of the most popular techniques for automatic coastline extraction. NDWI is also used in [<xref rid="B23-sensors-23-04491" ref-type="bibr">23</xref>], but, in this case, results are improved by using repeated measurements and adaptive thresholding. Another example of traditional signal processing for coastline detection is [<xref rid="B18-sensors-23-04491" ref-type="bibr">18</xref>], where shorelines were extracted from multispectral images using a new water-land index that enhances the contrast between water and land pixels. Yet another example is [<xref rid="B19-sensors-23-04491" ref-type="bibr">19</xref>], where unsupervised pixel classification is used for extracting shorelines from high-resolution satellite images.</p><p>Sentinel-2 satellite images are frequently employed for the purpose of coastline extraction, owing to their high spatial resolution and multispectral capabilities. In [<xref rid="B24-sensors-23-04491" ref-type="bibr">24</xref>], shoreline changes in the Al Batinah region of Oman and the impact of Cyclone Kyarr are analyzed using Sentinel-2 images and the Digital Shoreline Analysis System (DSAS). In [<xref rid="B25-sensors-23-04491" ref-type="bibr">25</xref>], the effectiveness of MODIS, Landsat 8, and Sentinel-2 in measuring regional shoreline changes is compared. Shorelines are extracted, again, with the DSAS and Sentinel-2 is identified as the most effective source of satellite images due to its higher spatial resolution. Another tool proposed for shoreline extraction is the SHOREX system [<xref rid="B26-sensors-23-04491" ref-type="bibr">26</xref>,<xref rid="B27-sensors-23-04491" ref-type="bibr">27</xref>]. It is able to automatically define the instantaneous shoreline position at a sub-pixel level from Landsat 8 and Sentinel 2 images. In [<xref rid="B28-sensors-23-04491" ref-type="bibr">28</xref>], shoreline changes associated with volcanic activity in Anak Krakatau, Indonesia, are analyzed using a NDWI-based method on Sentinel-2 multispectral imagery.</p><p>In more recent years, semantic interpretation of images is being performed more and more by means of supervised machine learning, i.e., deep neural networks (DNN), due to its successful applicability in very different fields and to very different kinds of images, and shoreline extraction from satellite imagery is no exception [<xref rid="B29-sensors-23-04491" ref-type="bibr">29</xref>,<xref rid="B30-sensors-23-04491" ref-type="bibr">30</xref>]. The well-known U-Net architecture [<xref rid="B31-sensors-23-04491" ref-type="bibr">31</xref>], in particular, is often used for effective DNN-based coast monitoring on a global scale [<xref rid="B32-sensors-23-04491" ref-type="bibr">32</xref>,<xref rid="B33-sensors-23-04491" ref-type="bibr">33</xref>,<xref rid="B34-sensors-23-04491" ref-type="bibr">34</xref>,<xref rid="B35-sensors-23-04491" ref-type="bibr">35</xref>,<xref rid="B36-sensors-23-04491" ref-type="bibr">36</xref>,<xref rid="B37-sensors-23-04491" ref-type="bibr">37</xref>,<xref rid="B38-sensors-23-04491" ref-type="bibr">38</xref>,<xref rid="B39-sensors-23-04491" ref-type="bibr">39</xref>,<xref rid="B40-sensors-23-04491" ref-type="bibr">40</xref>]. Different types of satellite images have been used for this purpose, including Sentinel-1 SAR images [<xref rid="B32-sensors-23-04491" ref-type="bibr">32</xref>,<xref rid="B33-sensors-23-04491" ref-type="bibr">33</xref>,<xref rid="B34-sensors-23-04491" ref-type="bibr">34</xref>], Landsat 8 and Gaofen-1 multispectral images [<xref rid="B35-sensors-23-04491" ref-type="bibr">35</xref>,<xref rid="B40-sensors-23-04491" ref-type="bibr">40</xref>], and true color images (TCI) from Google Earth [<xref rid="B36-sensors-23-04491" ref-type="bibr">36</xref>,<xref rid="B37-sensors-23-04491" ref-type="bibr">37</xref>,<xref rid="B38-sensors-23-04491" ref-type="bibr">38</xref>,<xref rid="B39-sensors-23-04491" ref-type="bibr">39</xref>]. In [<xref rid="B41-sensors-23-04491" ref-type="bibr">41</xref>], eight deep learning models, including different variations of U-Net, are used for coastline detection, and their performances are compared. Other kinds of deep neural networks have also been proposed for coastline detection. In [<xref rid="B42-sensors-23-04491" ref-type="bibr">42</xref>], ALOS-2 SAR images are analyzed using a densely connected neural network with two hidden layers. A multi-task network which includes both a sea&#x02013;land segmentation and a sea&#x02013;land boundary detection module, named BS-Net is instead proposed in [<xref rid="B43-sensors-23-04491" ref-type="bibr">43</xref>].</p><p>The key requirement to successful supervised machine learning is, of course, the availability of large datasets of accurately labeled samples. The problem with coastline detection from satellite images is that datasets of appropriate size are not common. A possible solution is to build synthetic datasets, i.e., a collection of artificially generated realistic images, produced by a computer program together with the associated &#x0201c;exact&#x0201d; labels. Synthetic datasets have been built and used successfully in many applications [<xref rid="B44-sensors-23-04491" ref-type="bibr">44</xref>,<xref rid="B45-sensors-23-04491" ref-type="bibr">45</xref>,<xref rid="B46-sensors-23-04491" ref-type="bibr">46</xref>], but their construction is unpractical for satellite images, which contain complex patterns difficult to reproduce realistically with computer graphics. This is true for TCI images and even more for images in other spectral bands. Manual labeling, based on visual interpretation of TCIs, is a long and boring task, but usable effectively as long as the shorelines are comparatively simple, e.g., with sea and land separated by a single line; when many images in the dataset have elaborate shorelines (as in the example that will be shown later), it becomes impractical and very burdensome. </p><p>The present paper, extending the preliminary research in [<xref rid="B47-sensors-23-04491" ref-type="bibr">47</xref>] (where a much smaller dataset is obtained), presents a method for automatically building a dataset of labeled satellite images for sea&#x02013;land segmentation and shoreline detection. The method is based on the usage of publicly available shoreline data, together with publicly available satellite images. In particular, the method is developed to use shoreline data from the National Oceanic and Atmospheric Administration (NOAA) and satellite images from Copernicus Sentinel 2 project, obtaining the &#x0201c;Sentinel2-NOAA Water Edges Dataset&#x0201d; (SNOWED) [<xref rid="B48-sensors-23-04491" ref-type="bibr">48</xref>], whose main features are the following.</p><list list-type="bullet"><list-item><p>SNOWED is constructed with a fully automatic algorithm, without human intervention or interpretations.</p></list-item><list-item><p>SNOWED is annotated using certified shoreline measurements.</p></list-item><list-item><p>SNOWED contains satellite images of different types of coasts, located in a wide geographical area, including images related to very elaborate shorelines.</p></list-item></list><p>One intrinsic drawback of the automatic generation process is that some satellite images can contain water regions not included in the shoreline data used for labeling it and, hence, may have an incomplete label. This problem is however identified and handled as described in <xref rid="sec4dot1-sensors-23-04491" ref-type="sec">Section 4.1</xref>. </p><p>With respect to other datasets of this type that have been proposed in recent years, the method presented in the paper to generate the SNOWED dataset is characterized by some innovative aspects. Datasets found in the literature are all based on the visual interpretation of satellite images and require therefore a strong human effort for labeling. Furthermore, the accuracy of sea/land segmentation labels depends directly on the quality of satellite images selected for the dataset and on how well they can be visually interpreted by humans. The methodology designed and implemented for this work uses instead independent measurements to automatically generate the labeled dataset, without any human intervention. This translates both to avoidance of tiresome human work and to the generation of sea/land labels having known and very low uncertainty. In addition, by using the proposed method, the source of satellite images (which is the Sentinel 2 project in the case of SNOWED) can be easily changed while using still the same shoreline measurements, leading to broader possibilities of application.</p><p>The paper is organized as follows. In <xref rid="sec2-sensors-23-04491" ref-type="sec">Section 2</xref>, a review of the available datasets of satellite images for sea/land segmentation tasks is presented. In <xref rid="sec3-sensors-23-04491" ref-type="sec">Section 3</xref>, the automatic dataset generation procedure is presented. In <xref rid="sec4-sensors-23-04491" ref-type="sec">Section 4</xref>, results of the application of the proposed generation method are reported, together with quality assessment results. In <xref rid="sec5-sensors-23-04491" ref-type="sec">Section 5</xref> are the conclusions.</p></sec><sec id="sec2-sensors-23-04491"><title>2. Publicly Available Datasets of Satellite Images for Sea/Land Segmentation</title><p>The aim of this section is to illustrate the already available public datasets developed for training deep learning models for sea/land segmentation. Particular attention is dedicated to the general characteristics of the provided datasets and to the generation process used to obtain them. This is useful to understand the novelty and relevance of the proposed work, and to conveniently compare the proposed SNOWED dataset with the other available alternatives. </p><p>Two major features are considered for each dataset: the number of samples containing both water and land pixels, and the source of labeling information. These features are indeed the only ones strictly related to the effectiveness of a dataset for training a neural network. We consider the number of images containing both land and sea, rather than the absolute number of images, because samples containing only one class can be trivially extracted from large areas that are known to contain only sea or only land. Besides, we highlight that the source of labeling information determines the accuracy of the labels, and hence the accuracy of models trained using the dataset.</p><sec id="sec2dot1-sensors-23-04491"><title>2.1. Water Segmentation Data Set (QueryPlanet Project)</title><p>The water segmentation data set [<xref rid="B49-sensors-23-04491" ref-type="bibr">49</xref>] has been created as a part of the QueryPlanet project, which has been funded by the European Space Agency (ESA). The dataset is composed of satellite images of size <inline-formula><mml:math id="mm1" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>64</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>64</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> from the Sentinel-2 Level-1C product. Each of them has been manually labeled by volunteer users of a collaborative web app. Volunteers were prompted with an initial label obtained by calculating the NDWI [<xref rid="B50-sensors-23-04491" ref-type="bibr">50</xref>] and had to visually compare it with the corresponding satellite TCI and correct eventual discrepancies based on their interpretation of the image. The online labeling campaign led to the creation of 7671 samples, but only 5177 of them contain both sea and land pixels.</p></sec><sec id="sec2dot2-sensors-23-04491"><title>2.2. Sea&#x02013;Land Segmentation Benchmark Dataset</title><p>The dataset proposed in [<xref rid="B51-sensors-23-04491" ref-type="bibr">51</xref>] contains labeled Landsat-8 Operational Land Imager (OLI) satellite images, of different types of Chinese shorelines: sandy, muddy, artificial and rocky coasts. The labels of the dataset are obtained through a multi-step human annotation procedure. First, Landset-8 OLI images with less than 5% cloud cover are selected along the Chinese shoreline. These images are pre-processed by applying radiometric calibration and atmospheric correction and then are manually annotated by dividing all their pixels into two classes: sea and land. Finally, satellite images are cut into small patches and each patch is checked to remove the defective ones (e.g., blank and cloud-covered patches). At the end of the procedure, 3361 images of size <inline-formula><mml:math id="mm2" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>512</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>512</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> are obtained, but only 831 of them contain both classes. </p></sec><sec id="sec2dot3-sensors-23-04491"><title>2.3. YTU-WaterNet</title><p>The YTU-WaterNet dataset proposed in [<xref rid="B52-sensors-23-04491" ref-type="bibr">52</xref>] contains Landsat-8 OLI images too. The dataset is created starting from 63 Landsat-8 OLI full-frames containing coastal regions of Europe, South and North America, and Africa. Only the blue, red and near-infrared bands are used for the samples of the dataset to reduce the dataset size and the computational load needed for training operations. The satellite images are cut into <inline-formula><mml:math id="mm3" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>512</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>512</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> patches and binary segmented by exploiting OpenStreetMap (OSM) water polygons data [<xref rid="B43-sensors-23-04491" ref-type="bibr">43</xref>]. OSM data is created by volunteers based on their geographical knowledge of the area or on visual interpretation of satellite images. This data is available as vector polygons, which are then converted to raster images representing the water regions of the sample. Finally, a filtering operation is performed to eliminate cloud-covered samples and samples with only one class, while samples with a mismatching label are identified and eliminated by visual inspection. The YTU-WaterNet dataset contains 1008 images.</p></sec><sec id="sec2dot4-sensors-23-04491"><title>2.4. Sentinel-2 Water Edges Dataset (SWED)</title><p>The most recent dataset is the Sentinel-2 Water Edges Dataset (SWED), proposed in a research work supported by the UK Hydrographic Office [<xref rid="B53-sensors-23-04491" ref-type="bibr">53</xref>]. SWED uses Sentinel-2 Level-2A imagery, semantically annotated through a semi-automatic procedure. The first step of the dataset creation process is the selection of Sentinel-2 images between 2017 and 2021. Only clear and cloud-free images are selected, by filtering on the &#x02018;cloudy pixel percentage&#x02019; metadata associated with each image, and then by visually inspecting the obtained search results. Furthermore, images are manually selected to cover a wide variety of geographical areas and types of coasts. A water/non-water segmentation mask is therefore created for each of the selected Sentinel-2 images. First, a false color image with visually good contrast between water and non-water pixels is searched by trial and error among those that can be obtained by rendering different combinations of Sentinel-2 bands in the RGB channels. The selected combination of bands is not the same for each image, although three combinations are found to be a good starting point. Secondly, a manually refined k-means-based procedure is applied to the rendered false color images to collect their pixels into two clusters, corresponding to water and non-water regions. Finally, segmentation masks are manually corrected by visual comparison against high-resolution aerial imagery available on Google Earth and Bing Maps. This imagery is, however, obtained as a composition of multiple images acquired on different and not precisely known dates, and therefore in some cases can represent inaccurately the actual state of the coasts at the Sentinel-2 acquisition time. The SWED dataset contains 26,468 images of size <inline-formula><mml:math id="mm4" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula>, cut from the annotated Sentinel-2 full-tiles, but only 9013 of them contain both classes.</p></sec><sec id="sec2dot5-sensors-23-04491"><title>2.5. Summary of the Characteristics of the Already Available Datasets, and of the New SNOWED Dataset</title><p>The described datasets are the result of a very intense effort and provide solid solutions for training and benchmarking machine learning models for shoreline recognition. Of course, a larger number of samples, or another dataset that can be used together with them, is desirable. Another improvable feature is the labeling process accuracy: a specific quality assurance on the shoreline labels would be a clear plus. </p><p>The methodology described in this work aims precisely at these goals: providing further samples useful for training neural models for satellite coastline measurements, along with labels coming from certified coastline measurements.</p><p><xref rid="sensors-23-04491-t001" ref-type="table">Table 1</xref> summarizes the main characteristics of the four datasets in the literature described in this section, and those of the SNOWED dataset obtained with the procedure illustrated in the present work. The number of images reported in <xref rid="sensors-23-04491-t001" ref-type="table">Table 1</xref> refers to images containing both sea and land classes. As can be seen, the image size is not the same for all the datasets and therefore a conversion is needed to directly compare the number of images of the two datasets. For example, the 1008 512&#x000d7;512 images of YTU-WaterNet correspond to 4032 256 &#x000d7; 256 images.</p></sec></sec><sec id="sec3-sensors-23-04491"><title>3. Data and Methods</title><p>The methodology presented in this paper consists in combining publicly available satellite images and shoreline data. This is a non-trivial task since many preprocessing operations and quality checks are needed to obtain accurately annotated samples. The methodology, and the involved processing, are illustrated with the concrete construction of a dataset, where the source of satellite imagery is the Level-1C data product of the Sentinel-2 mission, and the source of shoreline data is the Continually Updated Shoreline Product (CUSP).</p><sec id="sec3dot1-sensors-23-04491"><title>3.1. Data Sources</title><sec id="sec3dot1dot1-sensors-23-04491"><title>3.1.1. Sentinel-2 Satellite Imagery</title><p>The Sentinel-2 mission consists of a constellation of two satellites for Earth observation, phased at 180&#x000b0; to each other to provide a revisit period of at most 5 days. The satellites are equipped with the MultiSpectral Instrument (MSI) that acquires images in 13 spectral bands, with spatial resolutions up to 10 m (four bands have a resolution of 10 m, six bands have a resolution of 20 m, three bands have a resolution of 60 m). Level-1C data products provide Top-Of-Atmosphere reflectances measured through the MSI as <inline-formula><mml:math id="mm5" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>100</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>100</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> km<sup>2</sup> ortho-images (tiles) in UTM/WGS84 projection [<xref rid="B54-sensors-23-04491" ref-type="bibr">54</xref>]. Level-1C data covers all continental land and sea water up to 20 km from the coast, from June 2015 to the current date [<xref rid="B55-sensors-23-04491" ref-type="bibr">55</xref>].</p><p>Sentinel-2 data has been selected since this mission provides better performances compared to other public continuous Earth observation missions, in terms of both spatial resolution and revisit period. Landsat 8/9 mission, for example, has a spatial resolution of at most 15 m and a revisit period of 8 days. The choice of satellite imagery data sources, however, is not a conditioning factor for the dataset creation process and other products can be used with few trivial changes in the procedure.</p></sec><sec id="sec3dot1dot2-sensors-23-04491"><title>3.1.2. Shoreline Data</title><p>CUSP is developed by U.S. NOAA with the aim of providing essential information to manage coastal areas and conduct environmental analyses. This dataset includes all continental U.S. shoreline with portions of Alaska, Hawaii, the U.S. Virgin Islands, Pacific Islands, and Puerto Rico. CUSP provides the mean-high water shoreline, measured through vertical modeling or image interpretation using both water level stations and/or shoreline indicators. All data included in CUSP is verified by contemporary imagery or shoreline from other sources [<xref rid="B56-sensors-23-04491" ref-type="bibr">56</xref>]. Another important feature of CUSP is that the shoreline is split into shorter paths and each of them has additional information associated, including the date and type of source data used to measure the shoreline, the type of coast and the horizontal accuracy, which represents the circular error at the 95% confidence level [<xref rid="B57-sensors-23-04491" ref-type="bibr">57</xref>]. An analysis of the horizontal accuracy shows that 90% of paths have measurement errors &#x02264; 10 m, while 99.97% of paths have measurement errors &#x02264; 20 m. NOAA&#x02019;s CUSP paths have therefore a very high accuracy, comparable and, in most cases, overcoming the resolution of Sentinel 2 imagery. To our knowledge, NOAA&#x02019;s CUSP is the only publicly available source of shoreline data with these features, which are essential to perform the dataset generation procedure proposed in this work. In principle, nothing prevents one from using other sets of shoreline measurements, with the same essential features, i.e., geographic coordinates, date, high accuracy, and possibly the measurement method.</p></sec></sec><sec id="sec3dot2-sensors-23-04491"><title>3.2. Shoreline Data Preprocessing (Selection and Merging)</title><p>A preliminary filtering operation is performed on CUSP data to exclude shoreline that has been extracted from observations prior to the Sentinel-2 mission launch in June 2015. A representation of the shoreline remaining after this preliminary operation is depicted in the map of North America in <xref rid="sensors-23-04491-f001" ref-type="fig">Figure 1</xref> which shows that locations of useful shorelines are very heterogeneous, spanning most areas of the U.S. coast. This is an advantageous feature since it guarantees a great variability of the satellite images included in the final produced dataset.</p><p>Selected shoreline paths that share one terminal point and have the same date are then merged, in order to optimize the satellite images selection procedure described in the following. Statistics about the NOAA CUSP shoreline data and the selected paths are reported in <xref rid="sensors-23-04491-t002" ref-type="table">Table 2</xref>.</p></sec><sec id="sec3dot3-sensors-23-04491"><title>3.3. Selection of Satellite Images</title><p>In the practical implementation of the procedure, we have obtained the Sentinel-2 Level-1C tiles of our interest, which are <inline-formula><mml:math id="mm6" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>10,980</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>10,980</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> pixels, using the <italic toggle="yes">Plateforme d&#x02019;Exploitation des Produits Sentinel</italic> of the <italic toggle="yes">Centre National d&#x02019;&#x000e9;tudes Spatiales</italic> (PEPS CNES) [<xref rid="B58-sensors-23-04491" ref-type="bibr">58</xref>]. </p><p>Satellite images are selected on the basis of the location and the date of the merged shoreline paths obtained in the former step. It is important here to clarify the issue of <italic toggle="yes">dates and times</italic> of satellite images and on-field measurements used for labeling.</p><p>Obviously, the ideal situation is to have perfect simultaneity between the acquisition of the satellite image and the field measurements of the area it takes. It is easy to understand, however, that this situation is unfeasible and never occurs in practice. </p><p>Even when in situ measurements are made specifically for image labeling, simultaneity is not achieved in practice (see e.g., [<xref rid="B7-sensors-23-04491" ref-type="bibr">7</xref>]). Satellite images, indeed, are taken at intervals of some days (5 days in the case of Sentinel-2), and an image is not always usable, due to the presence of clouds or other causes: hence, usable images have dates that cannot be chosen as desired by the user and can be spaced between them by many days. For this same reason, satellite monitoring is not designed to keep track of changes that occur in a few hours, but of changes over months and years. In general, one must always choose the image <italic toggle="yes">with the date nearer to that of interest</italic>. When labeling a dataset, the date of the image must be as close as possible to that of the in situ measurement.</p><p>On the basis of the above consideration, PEPS CNES has been queried according to the following criteria.</p><list list-type="bullet"><list-item><p>Sentinel tiles must contain the shoreline path.</p></list-item><list-item><p>Cloud cover of Sentinel tiles must be lesser than 10% (parameter: <italic toggle="yes">cloud_cover</italic>).</p></list-item><list-item><p>Sentinel tiles acquisition date must be at most 30 days (parameter: <italic toggle="yes">time_difference</italic>) before or after the shoreline date.</p></list-item></list><p>The time difference between satellite images and NOAA CUSP measurements is exactly known and recorded in the dataset. It never exceeds the parameter <italic toggle="yes">time_difference</italic>: otherwise, the data sample is not generated. When more than one result is obtained, the tile having the acquisition date closest to the shoreline date is selected.</p><p>It is possible to choose different values for the parameters <italic toggle="yes">cloud_cover</italic> and <italic toggle="yes">time_difference</italic>. A value of <italic toggle="yes">cloud_cover</italic> &#x0003e; 10% generates more dataset samples, but it is more likely that they will be discarded in the following steps (see <xref rid="sec3dot4-sensors-23-04491" ref-type="sec">Section 3.4</xref>), due to the presence of clouds. A parameter <italic toggle="yes">time_difference</italic> &#x0003c; 30 days generates fewer dataset samples, but the shorelines in the obtained images are likely to be negligibly different since the resolution of Sentinel-2 is 10 m / pixel, and therefore only changes of the order of tens of meters are important. In any case, a <italic toggle="yes">time_difference</italic> &#x0003c; 10 days is not reasonable due to the revisit time of Sentinel-2.</p><p>The selection obtained with these constraints has been found to provide a good compromise between the computational resources required to generate the dataset (directly related to the number of selected tiles) and the final size of the dataset, which can grow if more tiles, and hence more shoreline paths, are considered. It is worth highlighting that:<list list-type="simple"><list-item><label>(1)</label><p>The quality of each sample generated with this method is assured by later checks, which are also automatic, being based on Sentinel data themselves (see <xref rid="sec3-sensors-23-04491" ref-type="sec">Section 3</xref>, in particular, <xref rid="sec3dot4-sensors-23-04491" ref-type="sec">Section 3.4</xref>). For example, the presence of clouds in localized areas of the tile is not detrimental.</p></list-item><list-item><label>(2)</label><p>Further releasing the constraints (cloud coverage and time difference) do not lead, ultimately, to a significant increase in the dataset&#x02019;s size.</p></list-item></list></p><p>The described procedure has obtained 987 tiles, containing 102,283 shoreline paths (about 20% less than the overall number of paths).</p></sec><sec id="sec3dot4-sensors-23-04491"><title>3.4. Extraction of Samples and Labeling</title><p>The selected Sentinel-2 tiles are then downloaded and processed singularly to extract the semantically annotated samples. Two preliminary operations are performed before the actual extraction phase. </p><p>First, Level-2A products are generated from Level-1C products using the sen2cor processor [<xref rid="B59-sensors-23-04491" ref-type="bibr">59</xref>]. Level-2A products are composed of (i) a scene classification (SC) mask and (ii) surface reflectance obtained through atmospheric correction [<xref rid="B60-sensors-23-04491" ref-type="bibr">60</xref>]. The SC mask assigns one of 12 classes (including water, vegetated and non-vegetated land, and clouds) to each pixel of the tile and is the only data needed in subsequent steps. </p><p>Second, the shoreline paths associated with each Sentinel tile are projected to the plane of the UTM/WGS84 zone containing the Sentinel tile. The UTM coordinates of the vertices of the Sentinel tile in the plane of the UTM zone are also known (they can be downloaded from [<xref rid="B61-sensors-23-04491" ref-type="bibr">61</xref>]), and thus the pixel coordinates of the shoreline points inside the tile can be computed.</p><p>The <inline-formula><mml:math id="mm7" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>10,980</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>10,980</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> pixels Sentinel tile is then split into sub-tiles of size <inline-formula><mml:math id="mm8" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula>, among which the samples of the dataset are selected. The sub-tile size has been chosen so that a direct comparison, and a side-to-side utilization, is possible of the obtained dataset and that described in [<xref rid="B53-sensors-23-04491" ref-type="bibr">53</xref>], the most recent and numerous for water segmentation in the literature. In <xref rid="sensors-23-04491-f002" ref-type="fig">Figure 2</xref> an example of a sub-tiling grid is depicted.</p><p>The subsequent processing involves only sub-tiles containing shoreline paths, as shown in <xref rid="sensors-23-04491-f002" ref-type="fig">Figure 2</xref>. The basic task is to create, for each sub-tile, a binary segmentation map based on NOAA CUSP shoreline paths. This operation is of some complexity and must be illustrated in detail.</p><p>First of all, we specify that, for any sub-tile, all shoreline paths partially or completely contained in it are considered, <italic toggle="yes">independent of their date</italic>. A strict constraint on the dates of all the used shoreline segments leads to discarding many sub-tiles, due to the short shoreline segments with dates too different from that of the tile. Instead, completing the shoreline including also short segments measured in different dates allows the construction of a dataset with much more sample, without compromising meaningfully the quality of the shoreline data. Besides, the date of each shoreline path is supplied in the dataset, so that samples can be later selected, if deemed useful, according to arbitrary constraints on the time difference between the Sentinel date and the CUSP shoreline dates.</p><p>The process used for generating the binary segmentation mask of sub-tiles from CUSP shoreline paths is depicted in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>. As a first step, shoreline paths completely or partially contained in the sub-tile are selected (<xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>a). The second step is to merge contacting paths: merged paths, depicted with different colors in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>b, can be closed (e.g., the light green path and the gray path) or open (e.g., the orange, blue and red paths). If open paths have an end inside the sub-tile, the sub-tile is discarded; otherwise, paths are clipped using the sub-tile borders as the clipping window. Only closed polygons are obtained after this operation, as shown in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>c. To obtain a binary mask, polygons are filled with ones and summed, producing the matrix in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>d; finally, a binary map (<xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>e) is obtained by selecting the even and odds elements of the matrix in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>d.</p><p>The segmentation label of the sub-tile is created by assigning water and land categories to the two classes of the mask in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>e based on the previously computed Level-2A SC layer, depicted in <xref rid="sensors-23-04491-f004" ref-type="fig">Figure 4</xref> for the case considered in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>. The class in the mask containing more Level-2A water pixels is categorized as water, while the class containing more non-water Level-2A pixels is categorized as land. The SC layer is also used to evaluate the correctness of the label. In particular, the sub-tile is discarded if the water class and land class contain less than 80% Level-2A water pixels and non-water pixels, respectively.</p></sec><sec id="sec3dot5-sensors-23-04491"><title>3.5. Overview of the Dataset Generation Procedure</title><p>For the sake of clarity, a flowchart of the previously described dataset generation method is depicted in <xref rid="sensors-23-04491-f005" ref-type="fig">Figure 5</xref>. The proposed method is an original solution, and each step except one (marked with a solid line) has been designed and implemented purposely for this work. The flowchart highlights the novelty of this method compared to others reported in the literature and reviewed in <xref rid="sec2-sensors-23-04491" ref-type="sec">Section 2</xref>. In these works, sea/land labeling is fundamentally based on the human interpretation of satellite imagery, while the proposed method uses shoreline measurements from NOAA CUSP as a source for automatic labeling.</p><p>In the flowchart in <xref rid="sensors-23-04491-f005" ref-type="fig">Figure 5</xref>, operations with a gray background, namely B and C, are specific to Sentinel 2 and need major refactoring if other imagery sources are used. All the other operations require instead only trivial changes. The general logic and overall procedure for dataset generation are, however, the same even if other imagery sources are used. Furthermore, the changes required for operations B and C are not substantial. In particular, for operation B, the same constraints for selecting satellite images must be used to query the appropriate satellite imagery platform (PEPS CNES is used in this work for Sentinel images). For operation C, the only required output is a low-accuracy sea/land classification of the satellite image, used later in operation H, and this can be easily obtained e.g., by computing the NDWI.</p></sec></sec><sec sec-type="results" id="sec4-sensors-23-04491"><title>4. Results and Discussion</title><p>The annotated dataset generated using the proposed method counts 4334 samples of size <inline-formula><mml:math id="mm9" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> containing both water and land pixels. The dataset has been built retrieving all 13 Sentinel-2 MSI bands, which are therefore all present in each sample. The resolution of Sentinel-2 images is different for the various bands, the best being 10 m per pixel. The images in all 13 bands have been linearly rescaled to a uniform 10 m per pixel spatial resolution, a standard operation that allows one to have all the images in a single 3-d array. Each sample is provided with the water/land segmentation label and with the following additional information.</p><list list-type="bullet"><list-item><p>Level-2A SC mask.</p></list-item><list-item><p>Shoreline paths are used for labeling, each with its measurement date.</p></list-item><list-item><p>PEPS CNES identifier of the Sentinel-2 Level-1C tile.</p></list-item><list-item><p>Acquisition date of the Sentinel-2 Level-1C tile.</p></list-item><list-item><p>Pixels offset of the sub-tile in the complete Sentinel-2 image.</p></list-item></list><p>Some examples taken from the generated dataset are depicted in <xref rid="sensors-23-04491-f006" ref-type="fig">Figure 6</xref>. It is possible to appreciate the accuracy of the labeling, especially in the two cases of complicated water edges.</p><p>In the next subsections, the quality of the dataset is assessed both visually, by comparing images of a random subset of the dataset with their corresponding labels, and from a functional point of view, by training and testing a deep learning model using SNOWED.</p><sec id="sec4dot1-sensors-23-04491"><title>4.1. Dataset Visual Quality Assessment</title><p>In premise, it is important to remember that <italic toggle="yes">any</italic> dataset is prone to including inconsistent labels. In datasets that are manually labeled by subject matter experts there is room for human mistakes and subjective interpretations; in automatically labeled datasets problems may arise from intrinsic imperfections in the labeling algorithm. The problem of inconsistent labels is negligible only in synthetically generated datasets, which, in contrast, are prone to providing samples not similar enough to the actual samples with which the model must work. Therefore, assessing the quality of a dataset, and providing methods to improve it, can be considered a good metrological practice, always advisable.</p><p>The automatic method presented here to construct the dataset, together with its clear advantages, has an intrinsic drawback, that occasionally produces samples with incomplete labels. The problem arises from the fact that, in general, there is no guarantee that <italic toggle="yes">the set of measured shorelines includes all the water edges in each sample</italic>. In the case of SNOWED, a Sentinel-2 sub-tile may include water edges that have not been measured and included in CUSP NOAA. This problem can be avoided only by using a (hypothetical) collection of shorelines that is guaranteed to include <italic toggle="yes">all</italic> the water edges present in a large enough geographic region. This is not the case with NOAA CUSP.</p><p>Because of the considerations above, we provide here a procedure to check, assess and improve the dataset quality.</p><p>Any single sample of the dataset can be checked by inspecting three images provided in it, i.e., the TCI, the label, and the Sentinel-2 scene classification, as shown in <xref rid="sensors-23-04491-f007" ref-type="fig">Figure 7</xref>. The TCI and the label are visually compared, and the Sentinel-2 SC is used as a guide. It is important to remember that the latter image may only serve as a guide for a human, and not for an automatic check: the scene classification is, indeed, not very accurate, and in some cases misclassifies regions of the satellite image. </p><p>In <xref rid="sensors-23-04491-f007" ref-type="fig">Figure 7</xref> it is clear that, in the upper left corner, there is a small water edge, and therefore a small portion of land, not included in the label. This water edge was simply not present in NOAA CUSP and is of a length comparable with that of the labeled water edge in the sample. This sample, therefore, is considered &#x0201c;bad&#x0201d;. </p><p>In <xref rid="sensors-23-04491-f008" ref-type="fig">Figure 8</xref>, instead, there is a sample that we consider &#x0201c;suspect&#x0201d;. In this sample, the main shoreline is clear and labeled, but there is a small region which is classified as water by Sentinel-2 SC and as land according NOAA CUSP. It is not obvious if the water edge is real or not&#x02014;a further check with an independent source should be made, e.g., using commercial satellite imagery with very high resolution&#x02014;and the (possibly) missing edge is much shorter than the labeled one.</p><p>We want to highlight that, together with &#x0201c;bad&#x0201d; and &#x0201c;suspect&#x0201d; samples, the dataset has many samples that are &#x0201c;particularly good&#x0201d;, in the sense that the label includes elaborated shorelines, difficult to recognize by a human, and that the label is also completely ignored by Sentinel-2 SC. An example is in <xref rid="sensors-23-04491-f009" ref-type="fig">Figure 9</xref>.</p><p>We have assessed the dataset quality by checking a randomly selected subset of <inline-formula><mml:math id="mm10" overflow="scroll"><mml:mrow><mml:mrow><mml:mi>n</mml:mi><mml:mo>=</mml:mo><mml:mn>200</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> samples, out of <inline-formula><mml:math id="mm11" overflow="scroll"><mml:mrow><mml:mrow><mml:mi>N</mml:mi><mml:mo>=</mml:mo><mml:mn>4334</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> sample total. In the selected samples, we found five &#x0201c;bad&#x0201d; samples, with clearly incomplete labels, and 30 &#x0201c;suspect&#x0201d; samples, with possibly incomplete labels and ambiguous interpretation. The point estimate of the fraction of &#x0201c;bad&#x0201d; and &#x0201c;suspect&#x0201d; samples in a dataset (a conservative estimate of the fraction of improvable samples) is therefore:<disp-formula id="FD1-sensors-23-04491"><mml:math id="mm12" display="block" overflow="scroll"><mml:mrow><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>p</mml:mi></mml:mrow><mml:mo>^</mml:mo></mml:mover><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mn>35</mml:mn></mml:mrow><mml:mrow><mml:mn>200</mml:mn></mml:mrow></mml:mfrac><mml:mo>=</mml:mo><mml:mn>17.5</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mrow></mml:math></disp-formula>
and the 95% confidence interval for this fraction is, approximately:<disp-formula id="FD2-sensors-23-04491"><mml:math id="mm13" display="block" overflow="scroll"><mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>p</mml:mi></mml:mrow><mml:mrow><mml:mn>95</mml:mn><mml:mo>%</mml:mo></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mover accent="true"><mml:mrow><mml:mi>p</mml:mi></mml:mrow><mml:mo>^</mml:mo></mml:mover><mml:mo>&#x000b1;</mml:mo><mml:mn>1.96</mml:mn><mml:msqrt><mml:mfrac><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>p</mml:mi></mml:mrow><mml:mo>^</mml:mo></mml:mover><mml:mfenced separators="|"><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mover accent="true"><mml:mrow><mml:mi>p</mml:mi></mml:mrow><mml:mo>^</mml:mo></mml:mover></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:mfrac><mml:mfrac><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>n</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfrac></mml:msqrt><mml:mo>=</mml:mo><mml:mfenced separators="|"><mml:mrow><mml:mn>17.5</mml:mn><mml:mo>&#x000b1;</mml:mo><mml:mn>5.1</mml:mn></mml:mrow></mml:mfenced><mml:mo>%</mml:mo></mml:mrow></mml:mrow></mml:math></disp-formula>
where the Gaussian approximation of the hypergeometric distribution has been applied, taking into account the term <inline-formula><mml:math id="mm14" overflow="scroll"><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>n</mml:mi><mml:mo>)</mml:mo><mml:mo>/</mml:mo><mml:mo>(</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> to correctly account for the sampling without replacement in the acceptance sampling procedure [<xref rid="B62-sensors-23-04491" ref-type="bibr">62</xref>,<xref rid="B63-sensors-23-04491" ref-type="bibr">63</xref>].</p><p>The samples in this fraction can be further processed, by humans or by an algorithm using a different source of water edge data, to improve the labeling. It can also be discarded, even if this choice does not seem appropriate for the &#x0201c;suspect&#x0201d; samples, whose labels always include the &#x0201c;main&#x0201d; shorelines in the image.</p><p>In the next subsection, the dataset is used &#x0201c;as is&#x0201d;, without discarding or correcting neither the bad samples nor the suspect ones found in the assessment process. It is shown that the dataset trains quite effectively a simple neural model for shoreline detection. </p></sec><sec id="sec4dot2-sensors-23-04491"><title>4.2. Example Application of the Dataset</title><p>The SNOWED dataset has been employed to train a standard U-Net segmentation model [<xref rid="B31-sensors-23-04491" ref-type="bibr">31</xref>]. The dataset has been shuffled and then split into a training and a validation subset, corresponding to 80% and 20% of the samples respectively. Afterwards, the U-Net neural network has been trained for 30 epochs, using the Adam optimizer [<xref rid="B64-sensors-23-04491" ref-type="bibr">64</xref>]. Cross-entropy has been used as a loss function in the optimization, while mean intersection over union (IoU) is the metric to evaluate the performance of the neural model.</p><p>The training process is depicted in <xref rid="sensors-23-04491-f010" ref-type="fig">Figure 10</xref>. The final mean IoU for the validation set, obtained after completing the training phase, is 0.936. In <xref rid="sensors-23-04491-f011" ref-type="fig">Figure 11</xref>, the sea/land segmentation masks produced by the trained U-Net model for the first four samples of the validation set are depicted. The visual inspection of these results shows that an accurate sea/water segmentation is obtained, even if we used the standard U-Net model without any further optimization.</p></sec></sec><sec sec-type="conclusions" id="sec5-sensors-23-04491"><title>5. Conclusions</title><p>Measuring boundaries between land and water is important for understanding and managing environmental phenomena like erosion, accretion, sea level rise, etc. Measurements from satellite imagery are particularly useful to provide consistent information over large areas and long periods of time (even if with limited spatial resolution). There is not a single best method to measure shorelines using satellite data, but artificial intelligence techniques are also acquiring more and more importance in this field. Therefore, recent research is devoted to the construction of datasets of satellite images with shoreline labels, customarily obtained with human work of image interpretation and annotation. Constructing datasets with human intervention has obvious costs and drawbacks, which are especially meaningful considering that a dataset of labeled images of a given satellite cannot be used to work with images of other satellites.</p><p>Based on these considerations, we have focused on the task of constructing a labeled dataset of satellite images for shoreline detection <italic toggle="yes">without any human intervention</italic>. The algorithm uses NOAA CUSP shoreline data to properly select and annotate satellite images. By annotating Sentinel-2 Level-1C images, the algorithm has constructed the SNOWED dataset, which can be used alongside the very recent SWED dataset. With minimal adjustments, the algorithm can be used to construct datasets for different satellites. </p><p>The concept and results presented in this work show that it is possible, in general, to construct readily a dataset of labeled satellite images, if a set of in situ measurements with geographic and temporal data are available. Therefore, satellite monitoring and measurements can receive a great boost by increasing the public availability of measurement data coming from accurate ground surveys.</p></sec></body><back><ack><title>Acknowledgments</title><p>The authors wish to thank Vito Ivano D&#x02019;Alessandro and Luisa De Palma for their useful hints and discussions in the initial stage of the work.</p></ack><fn-group><fn><p><bold>Disclaimer/Publisher&#x02019;s Note:</bold> The statements, opinions and data contained in all publications are solely those of the individual author(s) and contributor(s) and not of MDPI and/or the editor(s). MDPI and/or the editor(s) disclaim responsibility for any injury to people or property resulting from any ideas, methods, instructions or products referred to in the content.</p></fn></fn-group><notes><title>Author Contributions</title><p>Conceptualization, M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia), P.A. and N.G.; Methodology, G.A., M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia), P.A. and N.G.; Software, M.S. (Marco Scarpetta) and P.A.; Validation, M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia) and N.G.; Formal analysis, M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia) and N.G.; Investigation, G.A., M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia), P.A. and N.G.; Resources, M.S. (Maurizio Spadavecchia) and N.G.; Data curation, M.S. (Marco Scarpetta) and P.A.; Writing&#x02014;original draft, M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia) and N.G.; Writing&#x02014;review &#x00026; editing, G.A., M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia), P.A. and N.G.; Visualization, G.A., M.S. (Marco Scarpetta), M.S. (Maurizio Spadavecchia) and N.G.; Supervision, M.S. (Maurizio Spadavecchia) N.G.; Project administration, G.A., M.S. (Maurizio Spadavecchia) and N.G.; Funding acquisition, G.A., N.G. All authors have read and agreed to the published version of the manuscript.</p></notes><notes><title>Institutional Review Board Statement</title><p>Not applicable.</p></notes><notes><title>Informed Consent Statement</title><p>Not applicable.</p></notes><notes notes-type="data-availability"><title>Data Availability Statement</title><p>The data presented in this study are openly available in Zenodo at <uri xlink:href="https://doi.org/10.5281/Zenodo.7871636">https://doi.org/10.5281/Zenodo.7871636</uri>, reference number [<xref rid="B48-sensors-23-04491" ref-type="bibr">48</xref>].</p></notes><notes notes-type="COI-statement"><title>Conflicts of Interest</title><p>The authors declare no conflict of interest.</p></notes><ref-list><title>References</title><ref id="B1-sensors-23-04491"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Mart&#x000ed;nez</surname><given-names>M.L.</given-names></name>
<name><surname>Intralawan</surname><given-names>A.</given-names></name>
<name><surname>V&#x000e1;zquez</surname><given-names>G.</given-names></name>
<name><surname>P&#x000e9;rez-Maqueo</surname><given-names>O.</given-names></name>
<name><surname>Sutton</surname><given-names>P.</given-names></name>
<name><surname>Landgrave</surname><given-names>R.</given-names></name>
</person-group><article-title>The Coasts of Our World: Ecological, Economic and Social Importance</article-title><source>Ecol. Econ.</source><year>2007</year><volume>63</volume><fpage>254</fpage><lpage>272</lpage><pub-id pub-id-type="doi">10.1016/j.ecolecon.2006.10.022</pub-id></element-citation></ref><ref id="B2-sensors-23-04491"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Halpern</surname><given-names>B.S.</given-names></name>
<name><surname>Frazier</surname><given-names>M.</given-names></name>
<name><surname>Afflerbach</surname><given-names>J.</given-names></name>
<name><surname>Lowndes</surname><given-names>J.S.</given-names></name>
<name><surname>Micheli</surname><given-names>F.</given-names></name>
<name><surname>O&#x02019;Hara</surname><given-names>C.</given-names></name>
<name><surname>Scarborough</surname><given-names>C.</given-names></name>
<name><surname>Selkoe</surname><given-names>K.A.</given-names></name>
</person-group><article-title>Recent Pace of Change in Human Impact on the World&#x02019;s Ocean</article-title><source>Sci. Rep.</source><year>2019</year><volume>9</volume><fpage>11609</fpage><pub-id pub-id-type="doi">10.1038/s41598-019-47201-9</pub-id><?supplied-pmid 31406130?><pub-id pub-id-type="pmid">31406130</pub-id></element-citation></ref><ref id="B3-sensors-23-04491"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Adamo</surname><given-names>F.</given-names></name>
<name><surname>Andria</surname><given-names>G.</given-names></name>
<name><surname>Cavone</surname><given-names>G.</given-names></name>
<name><surname>De Capua</surname><given-names>C.</given-names></name>
<name><surname>Lanzolla</surname><given-names>A.M.L.</given-names></name>
<name><surname>Morello</surname><given-names>R.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
</person-group><article-title>Estimation of Ship Emissions in the Port of Taranto</article-title><source>Measurement</source><year>2014</year><volume>47</volume><fpage>982</fpage><lpage>988</lpage><pub-id pub-id-type="doi">10.1016/j.measurement.2013.09.012</pub-id></element-citation></ref><ref id="B4-sensors-23-04491"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cotecchia</surname><given-names>F.</given-names></name>
<name><surname>Vitone</surname><given-names>C.</given-names></name>
<name><surname>Sollecito</surname><given-names>F.</given-names></name>
<name><surname>Mali</surname><given-names>M.</given-names></name>
<name><surname>Miccoli</surname><given-names>D.</given-names></name>
<name><surname>Petti</surname><given-names>R.</given-names></name>
<name><surname>Milella</surname><given-names>D.</given-names></name>
<name><surname>Ruggieri</surname><given-names>G.</given-names></name>
<name><surname>Bottiglieri</surname><given-names>O.</given-names></name>
<name><surname>Santaloia</surname><given-names>F.</given-names></name>
<etal/>
</person-group><article-title>A Geo-Chemo-Mechanical Study of a Highly Polluted Marine System (Taranto, Italy) for the Enhancement of the Conceptual Site Model</article-title><source>Sci. Rep.</source><year>2021</year><volume>11</volume><fpage>4017</fpage><pub-id pub-id-type="doi">10.1038/s41598-021-82879-w</pub-id><pub-id pub-id-type="pmid">33597633</pub-id></element-citation></ref><ref id="B5-sensors-23-04491"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Tiwari</surname><given-names>M.</given-names></name>
<name><surname>Rathod</surname><given-names>T.D.</given-names></name>
<name><surname>Ajmal</surname><given-names>P.Y.</given-names></name>
<name><surname>Bhangare</surname><given-names>R.C.</given-names></name>
<name><surname>Sahu</surname><given-names>S.K.</given-names></name>
</person-group><article-title>Distribution and Characterization of Microplastics in Beach Sand from Three Different Indian Coastal Environments</article-title><source>Mar. Pollut. Bull.</source><year>2019</year><volume>140</volume><fpage>262</fpage><lpage>273</lpage><pub-id pub-id-type="doi">10.1016/j.marpolbul.2019.01.055</pub-id><?supplied-pmid 30803642?><pub-id pub-id-type="pmid">30803642</pub-id></element-citation></ref><ref id="B6-sensors-23-04491"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Vedolin</surname><given-names>M.C.</given-names></name>
<name><surname>Teophilo</surname><given-names>C.Y.S.</given-names></name>
<name><surname>Turra</surname><given-names>A.</given-names></name>
<name><surname>Figueira</surname><given-names>R.C.L.</given-names></name>
</person-group><article-title>Spatial Variability in the Concentrations of Metals in Beached Microplastics</article-title><source>Mar. Pollut. Bull.</source><year>2018</year><volume>129</volume><fpage>487</fpage><lpage>493</lpage><pub-id pub-id-type="doi">10.1016/j.marpolbul.2017.10.019</pub-id><pub-id pub-id-type="pmid">29033167</pub-id></element-citation></ref><ref id="B7-sensors-23-04491"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Traganos</surname><given-names>D.</given-names></name>
<name><surname>Aggarwal</surname><given-names>B.</given-names></name>
<name><surname>Poursanidis</surname><given-names>D.</given-names></name>
<name><surname>Topouzelis</surname><given-names>K.</given-names></name>
<name><surname>Chrysoulakis</surname><given-names>N.</given-names></name>
<name><surname>Reinartz</surname><given-names>P.</given-names></name>
</person-group><article-title>Towards Global-Scale Seagrass Mapping and Monitoring Using Sentinel-2 on Google Earth Engine: The Case Study of the Aegean and Ionian Seas</article-title><source>Remote Sens.</source><year>2018</year><volume>10</volume><elocation-id>1227</elocation-id><pub-id pub-id-type="doi">10.3390/rs10081227</pub-id></element-citation></ref><ref id="B8-sensors-23-04491"><label>8.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Scarpetta</surname><given-names>M.</given-names></name>
<name><surname>Affuso</surname><given-names>P.</given-names></name>
<name><surname>De Virgilio</surname><given-names>M.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
<name><surname>Andria</surname><given-names>G.</given-names></name>
<name><surname>Giaquinto</surname><given-names>N.</given-names></name>
</person-group><article-title>Monitoring of Seagrass Meadows Using Satellite Images and U-Net Convolutional Neural Network</article-title><source>Proceedings of the 2022 IEEE International Instrumentation and Measurement Technology Conference (I2MTC)</source><conf-loc>Ottawa, ON, Canada</conf-loc><conf-date>16&#x02013;19 May 2022</conf-date><fpage>1</fpage><lpage>6</lpage></element-citation></ref><ref id="B9-sensors-23-04491"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Adamo</surname><given-names>F.</given-names></name>
<name><surname>Attivissimo</surname><given-names>F.</given-names></name>
<name><surname>Carducci</surname><given-names>C.G.C.</given-names></name>
<name><surname>Lanzolla</surname><given-names>A.M.L.</given-names></name>
</person-group><article-title>A Smart Sensor Network for Sea Water Quality Monitoring</article-title><source>IEEE Sens. J.</source><year>2015</year><volume>15</volume><fpage>2514</fpage><lpage>2522</lpage><pub-id pub-id-type="doi">10.1109/JSEN.2014.2360816</pub-id></element-citation></ref><ref id="B10-sensors-23-04491"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Attivissimo</surname><given-names>F.</given-names></name>
<name><surname>Carducci</surname><given-names>C.G.C.</given-names></name>
<name><surname>Lanzolla</surname><given-names>A.M.L.</given-names></name>
<name><surname>Massaro</surname><given-names>A.</given-names></name>
<name><surname>Vadrucci</surname><given-names>M.R.</given-names></name>
</person-group><article-title>A Portable Optical Sensor for Sea Quality Monitoring</article-title><source>IEEE Sens. J.</source><year>2015</year><volume>15</volume><fpage>146</fpage><lpage>153</lpage><pub-id pub-id-type="doi">10.1109/JSEN.2014.2340437</pub-id></element-citation></ref><ref id="B11-sensors-23-04491"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Lu</surname><given-names>J.</given-names></name>
<name><surname>Wu</surname><given-names>J.</given-names></name>
<name><surname>Zhang</surname><given-names>C.</given-names></name>
<name><surname>Zhang</surname><given-names>Y.</given-names></name>
<name><surname>Lin</surname><given-names>Y.</given-names></name>
<name><surname>Luo</surname><given-names>Y.</given-names></name>
</person-group><article-title>Occurrence, Distribution, and Ecological-Health Risks of Selected Antibiotics in Coastal Waters along the Coastline of China</article-title><source>Sci. Total Environ.</source><year>2018</year><volume>644</volume><fpage>1469</fpage><lpage>1476</lpage><pub-id pub-id-type="doi">10.1016/j.scitotenv.2018.07.096</pub-id><pub-id pub-id-type="pmid">30743859</pub-id></element-citation></ref><ref id="B12-sensors-23-04491"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Zhang</surname><given-names>R.</given-names></name>
<name><surname>Pei</surname><given-names>J.</given-names></name>
<name><surname>Zhang</surname><given-names>R.</given-names></name>
<name><surname>Wang</surname><given-names>S.</given-names></name>
<name><surname>Zeng</surname><given-names>W.</given-names></name>
<name><surname>Huang</surname><given-names>D.</given-names></name>
<name><surname>Wang</surname><given-names>Y.</given-names></name>
<name><surname>Zhang</surname><given-names>Y.</given-names></name>
<name><surname>Wang</surname><given-names>Y.</given-names></name>
<name><surname>Yu</surname><given-names>K.</given-names></name>
</person-group><article-title>Occurrence and Distribution of Antibiotics in Mariculture Farms, Estuaries and the Coast of the Beibu Gulf, China: Bioconcentration and Diet Safety of Seafood</article-title><source>Ecotoxicol. Environ. Saf.</source><year>2018</year><volume>154</volume><fpage>27</fpage><lpage>35</lpage><pub-id pub-id-type="doi">10.1016/j.ecoenv.2018.02.006</pub-id><pub-id pub-id-type="pmid">29454268</pub-id></element-citation></ref><ref id="B13-sensors-23-04491"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kaku</surname><given-names>K.</given-names></name>
</person-group><article-title>Satellite Remote Sensing for Disaster Management Support: A Holistic and Staged Approach Based on Case Studies in Sentinel Asia</article-title><source>Int. J. Disaster Risk Reduct.</source><year>2019</year><volume>33</volume><fpage>417</fpage><lpage>432</lpage><pub-id pub-id-type="doi">10.1016/j.ijdrr.2018.09.015</pub-id></element-citation></ref><ref id="B14-sensors-23-04491"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>S&#x000f2;ria-Perpiny&#x000e0;</surname><given-names>X.</given-names></name>
<name><surname>Vicente</surname><given-names>E.</given-names></name>
<name><surname>Urrego</surname><given-names>P.</given-names></name>
<name><surname>Pereira-Sandoval</surname><given-names>M.</given-names></name>
<name><surname>Tenjo</surname><given-names>C.</given-names></name>
<name><surname>Ru&#x000ed;z-Verd&#x000fa;</surname><given-names>A.</given-names></name>
<name><surname>Delegido</surname><given-names>J.</given-names></name>
<name><surname>Soria</surname><given-names>J.M.</given-names></name>
<name><surname>Pe&#x000f1;a</surname><given-names>R.</given-names></name>
<name><surname>Moreno</surname><given-names>J.</given-names></name>
</person-group><article-title>Validation of Water Quality Monitoring Algorithms for Sentinel-2 and Sentinel-3 in Mediterranean Inland Waters with In Situ Reflectance Data</article-title><source>Water</source><year>2021</year><volume>13</volume><elocation-id>686</elocation-id><pub-id pub-id-type="doi">10.3390/w13050686</pub-id></element-citation></ref><ref id="B15-sensors-23-04491"><label>15.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Angelini</surname><given-names>M.G.</given-names></name>
<name><surname>Costantino</surname><given-names>D.</given-names></name>
<name><surname>Di Nisio</surname><given-names>A.</given-names></name>
</person-group><article-title>ASTER Image for Environmental Monitoring Change Detection and Thermal Map</article-title><source>Proceedings of the 2017 IEEE International Instrumentation and Measurement Technology Conference (I2MTC)</source><conf-loc>Turin, Italy</conf-loc><conf-date>22&#x02013;25 May 2017</conf-date><fpage>1</fpage><lpage>6</lpage></element-citation></ref><ref id="B16-sensors-23-04491"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Spinosa</surname><given-names>A.</given-names></name>
<name><surname>Ziemba</surname><given-names>A.</given-names></name>
<name><surname>Saponieri</surname><given-names>A.</given-names></name>
<name><surname>Damiani</surname><given-names>L.</given-names></name>
<name><surname>El Serafy</surname><given-names>G.</given-names></name>
</person-group><article-title>Remote Sensing-Based Automatic Detection of Shoreline Position: A Case Study in Apulia Region</article-title><source>J. Mar. Sci. Eng.</source><year>2021</year><volume>9</volume><elocation-id>575</elocation-id><pub-id pub-id-type="doi">10.3390/jmse9060575</pub-id></element-citation></ref><ref id="B17-sensors-23-04491"><label>17.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Scarpetta</surname><given-names>M.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
<name><surname>Andria</surname><given-names>G.</given-names></name>
<name><surname>Ragolia</surname><given-names>M.A.</given-names></name>
<name><surname>Giaquinto</surname><given-names>N.</given-names></name>
</person-group><article-title>Simultaneous Measurement of Heartbeat Intervals and Respiratory Signal Using a Smartphone</article-title><source>Proceedings of the 2021 IEEE International Symposium on Medical Measurements and Applications (MeMeA)</source><conf-loc>Lausanne, Switzerland</conf-loc><conf-date>23&#x02013;25 June 2021</conf-date><fpage>1</fpage><lpage>5</lpage></element-citation></ref><ref id="B18-sensors-23-04491"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Abdelhady</surname><given-names>H.U.</given-names></name>
<name><surname>Troy</surname><given-names>C.D.</given-names></name>
<name><surname>Habib</surname><given-names>A.</given-names></name>
<name><surname>Manish</surname><given-names>R.</given-names></name>
</person-group><article-title>A Simple, Fully Automated Shoreline Detection Algorithm for High-Resolution Multi-Spectral Imagery</article-title><source>Remote Sens.</source><year>2022</year><volume>14</volume><elocation-id>557</elocation-id><pub-id pub-id-type="doi">10.3390/rs14030557</pub-id></element-citation></ref><ref id="B19-sensors-23-04491"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sekar</surname><given-names>C.S.</given-names></name>
<name><surname>Kankara</surname><given-names>R.S.</given-names></name>
<name><surname>Kalaivanan</surname><given-names>P.</given-names></name>
</person-group><article-title>Pixel-Based Classification Techniques for Automated Shoreline Extraction on Open Sandy Coast Using Different Optical Satellite Images</article-title><source>Arab. J. Geosci.</source><year>2022</year><volume>15</volume><fpage>939</fpage><pub-id pub-id-type="doi">10.1007/s12517-022-10239-7</pub-id></element-citation></ref><ref id="B20-sensors-23-04491"><label>20.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Ragolia</surname><given-names>M.A.</given-names></name>
<name><surname>Andria</surname><given-names>G.</given-names></name>
<name><surname>Attivissimo</surname><given-names>F.</given-names></name>
<name><surname>Nisio</surname><given-names>A.D.</given-names></name>
<name><surname>Maria Lucia Lanzolla</surname><given-names>A.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
<name><surname>Larizza</surname><given-names>P.</given-names></name>
<name><surname>Brunetti</surname><given-names>G.</given-names></name>
</person-group><article-title>Performance Analysis of an Electromagnetic Tracking System for Surgical Navigation</article-title><source>Proceedings of the 2019 IEEE International Symposium on Medical Measurements and Applications (MeMeA)</source><conf-loc>Istanbul, Turkey</conf-loc><conf-date>26&#x02013;28 June 2019</conf-date><fpage>1</fpage><lpage>6</lpage></element-citation></ref><ref id="B21-sensors-23-04491"><label>21.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>De Palma</surname><given-names>L.</given-names></name>
<name><surname>Scarpetta</surname><given-names>M.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
</person-group><article-title>Characterization of Heart Rate Estimation Using Piezoelectric Plethysmography in Time- and Frequency-Domain</article-title><source>Proceedings of the 2020 IEEE International Symposium on Medical Measurements and Applications (MeMeA)</source><conf-loc>Bari, Italy</conf-loc><conf-date>1 June&#x02013;1 July 2020</conf-date><fpage>1</fpage><lpage>6</lpage></element-citation></ref><ref id="B22-sensors-23-04491"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Alcaras</surname><given-names>E.</given-names></name>
<name><surname>Falchi</surname><given-names>U.</given-names></name>
<name><surname>Parente</surname><given-names>C.</given-names></name>
<name><surname>Vallario</surname><given-names>A.</given-names></name>
</person-group><article-title>Accuracy Evaluation for Coastline Extraction from Pl&#x000e9;iades Imagery Based on NDWI and IHS Pan-Sharpening Application</article-title><source>Appl. Geomat.</source><year>2022</year><pub-id pub-id-type="doi">10.1007/s12518-021-00411-1</pub-id></element-citation></ref><ref id="B23-sensors-23-04491"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Dai</surname><given-names>C.</given-names></name>
<name><surname>Howat</surname><given-names>I.M.</given-names></name>
<name><surname>Larour</surname><given-names>E.</given-names></name>
<name><surname>Husby</surname><given-names>E.</given-names></name>
</person-group><article-title>Coastline Extraction from Repeat High Resolution Satellite Imagery</article-title><source>Remote Sens. Environ.</source><year>2019</year><volume>229</volume><fpage>260</fpage><lpage>270</lpage><pub-id pub-id-type="doi">10.1016/j.rse.2019.04.010</pub-id></element-citation></ref><ref id="B24-sensors-23-04491"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Al Ruheili</surname><given-names>A.M.</given-names></name>
<name><surname>Boluwade</surname><given-names>A.</given-names></name>
</person-group><article-title>Quantifying Coastal Shoreline Erosion Due to Climatic Extremes Using Remote-Sensed Estimates from Sentinel-2A Data</article-title><source>Environ. Process.</source><year>2021</year><volume>8</volume><fpage>1121</fpage><lpage>1140</lpage><pub-id pub-id-type="doi">10.1007/s40710-021-00522-2</pub-id></element-citation></ref><ref id="B25-sensors-23-04491"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sunny</surname><given-names>D.S.</given-names></name>
<name><surname>Islam</surname><given-names>K.M.A.</given-names></name>
<name><surname>Mullick</surname><given-names>M.R.A.</given-names></name>
<name><surname>Ellis</surname><given-names>J.T.</given-names></name>
</person-group><article-title>Performance Study of Imageries from MODIS, Landsat 8 and Sentinel-2 on Measuring Shoreline Change at a Regional Scale</article-title><source>Remote Sens. Appl. Soc. Environ.</source><year>2022</year><volume>28</volume><fpage>100816</fpage><pub-id pub-id-type="doi">10.1016/j.rsase.2022.100816</pub-id></element-citation></ref><ref id="B26-sensors-23-04491"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>S&#x000e1;nchez-Garc&#x000ed;a</surname><given-names>E.</given-names></name>
<name><surname>Palomar-V&#x000e1;zquez</surname><given-names>J.M.</given-names></name>
<name><surname>Pardo-Pascual</surname><given-names>J.E.</given-names></name>
<name><surname>Almonacid-Caballer</surname><given-names>J.</given-names></name>
<name><surname>Cabezas-Rabad&#x000e1;n</surname><given-names>C.</given-names></name>
<name><surname>G&#x000f3;mez-Pujol</surname><given-names>L.</given-names></name>
</person-group><article-title>An Efficient Protocol for Accurate and Massive Shoreline Definition from Mid-Resolution Satellite Imagery</article-title><source>Coast. Eng.</source><year>2020</year><volume>160</volume><fpage>103732</fpage><pub-id pub-id-type="doi">10.1016/j.coastaleng.2020.103732</pub-id></element-citation></ref><ref id="B27-sensors-23-04491"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cabezas-Rabad&#x000e1;n</surname><given-names>C.</given-names></name>
<name><surname>Pardo-Pascual</surname><given-names>J.E.</given-names></name>
<name><surname>Palomar-V&#x000e1;zquez</surname><given-names>J.</given-names></name>
<name><surname>Fern&#x000e1;ndez-Sarr&#x000ed;a</surname><given-names>A.</given-names></name>
</person-group><article-title>Characterizing Beach Changes Using High-Frequency Sentinel-2 Derived Shorelines on the Valencian Coast (Spanish Mediterranean)</article-title><source>Sci. Total Environ.</source><year>2019</year><volume>691</volume><fpage>216</fpage><lpage>231</lpage><pub-id pub-id-type="doi">10.1016/j.scitotenv.2019.07.084</pub-id><?supplied-pmid 31319257?><pub-id pub-id-type="pmid">31319257</pub-id></element-citation></ref><ref id="B28-sensors-23-04491"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Novellino</surname><given-names>A.</given-names></name>
<name><surname>Engwell</surname><given-names>S.L.</given-names></name>
<name><surname>Grebby</surname><given-names>S.</given-names></name>
<name><surname>Day</surname><given-names>S.</given-names></name>
<name><surname>Cassidy</surname><given-names>M.</given-names></name>
<name><surname>Madden-Nadeau</surname><given-names>A.</given-names></name>
<name><surname>Watt</surname><given-names>S.</given-names></name>
<name><surname>Pyle</surname><given-names>D.</given-names></name>
<name><surname>Abdurrachman</surname><given-names>M.</given-names></name>
<name><surname>Edo Marshal Nurshal</surname><given-names>M.</given-names></name>
<etal/>
</person-group><article-title>Mapping Recent Shoreline Changes Spanning the Lateral Collapse of Anak Krakatau Volcano, Indonesia</article-title><source>Appl. Sci.</source><year>2020</year><volume>10</volume><elocation-id>536</elocation-id><pub-id pub-id-type="doi">10.3390/app10020536</pub-id></element-citation></ref><ref id="B29-sensors-23-04491"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Guo</surname><given-names>Z.</given-names></name>
<name><surname>Wu</surname><given-names>L.</given-names></name>
<name><surname>Huang</surname><given-names>Y.</given-names></name>
<name><surname>Guo</surname><given-names>Z.</given-names></name>
<name><surname>Zhao</surname><given-names>J.</given-names></name>
<name><surname>Li</surname><given-names>N.</given-names></name>
</person-group><article-title>Water-Body Segmentation for SAR Images: Past, Current, and Future</article-title><source>Remote Sens.</source><year>2022</year><volume>14</volume><elocation-id>1752</elocation-id><pub-id pub-id-type="doi">10.3390/rs14071752</pub-id></element-citation></ref><ref id="B30-sensors-23-04491"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Tsiakos</surname><given-names>C.-A.D.</given-names></name>
<name><surname>Chalkias</surname><given-names>C.</given-names></name>
</person-group><article-title>Use of Machine Learning and Remote Sensing Techniques for Shoreline Monitoring: A Review of Recent Literature</article-title><source>Appl. Sci.</source><year>2023</year><volume>13</volume><elocation-id>3268</elocation-id><pub-id pub-id-type="doi">10.3390/app13053268</pub-id></element-citation></ref><ref id="B31-sensors-23-04491"><label>31.</label><element-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Ronneberger</surname><given-names>O.</given-names></name>
<name><surname>Fischer</surname><given-names>P.</given-names></name>
<name><surname>Brox</surname><given-names>T.</given-names></name>
</person-group><article-title>U-Net: Convolutional Networks for Biomedical Image Segmentation</article-title><source>Part III, Proceedings of the Medical Image Computing and Computer-Assisted Intervention&#x02014;MICCAI 2015, Munich, Germany, 5&#x02013;9 October 2015</source><person-group person-group-type="editor">
<name><surname>Navab</surname><given-names>N.</given-names></name>
<name><surname>Hornegger</surname><given-names>J.</given-names></name>
<name><surname>Wells</surname><given-names>W.M.</given-names></name>
<name><surname>Frangi</surname><given-names>A.F.</given-names></name>
</person-group><publisher-name>Springer International Publishing</publisher-name><publisher-loc>Cham, Switzerland</publisher-loc><year>2015</year><fpage>234</fpage><lpage>241</lpage></element-citation></ref><ref id="B32-sensors-23-04491"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Baumhoer</surname><given-names>C.A.</given-names></name>
<name><surname>Dietz</surname><given-names>A.J.</given-names></name>
<name><surname>Kneisel</surname><given-names>C.</given-names></name>
<name><surname>Kuenzer</surname><given-names>C.</given-names></name>
</person-group><article-title>Automated Extraction of Antarctic Glacier and Ice Shelf Fronts from Sentinel-1 Imagery Using Deep Learning</article-title><source>Remote Sens.</source><year>2019</year><volume>11</volume><elocation-id>2529</elocation-id><pub-id pub-id-type="doi">10.3390/rs11212529</pub-id></element-citation></ref><ref id="B33-sensors-23-04491"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Heidler</surname><given-names>K.</given-names></name>
<name><surname>Mou</surname><given-names>L.</given-names></name>
<name><surname>Baumhoer</surname><given-names>C.</given-names></name>
<name><surname>Dietz</surname><given-names>A.</given-names></name>
<name><surname>Zhu</surname><given-names>X.X.</given-names></name>
</person-group><article-title>HED-UNet: Combined Segmentation and Edge Detection for Monitoring the Antarctic Coastline</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2022</year><volume>60</volume><fpage>4300514</fpage><pub-id pub-id-type="doi">10.1109/TGRS.2021.3064606</pub-id></element-citation></ref><ref id="B34-sensors-23-04491"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Zhang</surname><given-names>S.</given-names></name>
<name><surname>Xu</surname><given-names>Q.</given-names></name>
<name><surname>Wang</surname><given-names>H.</given-names></name>
<name><surname>Kang</surname><given-names>Y.</given-names></name>
<name><surname>Li</surname><given-names>X.</given-names></name>
</person-group><article-title>Automatic Waterline Extraction and Topographic Mapping of Tidal Flats From SAR Images Based on Deep Learning</article-title><source>Geophys. Res. Lett.</source><year>2022</year><volume>49</volume><fpage>e2021GL096007</fpage><pub-id pub-id-type="doi">10.1029/2021GL096007</pub-id></element-citation></ref><ref id="B35-sensors-23-04491"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Aghdami-Nia</surname><given-names>M.</given-names></name>
<name><surname>Shah-Hosseini</surname><given-names>R.</given-names></name>
<name><surname>Rostami</surname><given-names>A.</given-names></name>
<name><surname>Homayouni</surname><given-names>S.</given-names></name>
</person-group><article-title>Automatic Coastline Extraction through Enhanced Sea-Land Segmentation by Modifying Standard U-Net</article-title><source>Int. J. Appl. Earth Obs. Geoinf.</source><year>2022</year><volume>109</volume><fpage>102785</fpage><pub-id pub-id-type="doi">10.1016/j.jag.2022.102785</pub-id></element-citation></ref><ref id="B36-sensors-23-04491"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Shamsolmoali</surname><given-names>P.</given-names></name>
<name><surname>Zareapoor</surname><given-names>M.</given-names></name>
<name><surname>Wang</surname><given-names>R.</given-names></name>
<name><surname>Zhou</surname><given-names>H.</given-names></name>
<name><surname>Yang</surname><given-names>J.</given-names></name>
</person-group><article-title>A Novel Deep Structure U-Net for Sea-Land Segmentation in Remote Sensing Images</article-title><source>IEEE J. Sel. Top. Appl. Earth Obs. Remote Sens.</source><year>2019</year><volume>12</volume><fpage>3219</fpage><lpage>3232</lpage><pub-id pub-id-type="doi">10.1109/JSTARS.2019.2925841</pub-id></element-citation></ref><ref id="B37-sensors-23-04491"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Li</surname><given-names>R.</given-names></name>
<name><surname>Liu</surname><given-names>W.</given-names></name>
<name><surname>Yang</surname><given-names>L.</given-names></name>
<name><surname>Sun</surname><given-names>S.</given-names></name>
<name><surname>Hu</surname><given-names>W.</given-names></name>
<name><surname>Zhang</surname><given-names>F.</given-names></name>
<name><surname>Li</surname><given-names>W.</given-names></name>
</person-group><article-title>DeepUNet: A Deep Fully Convolutional Network for Pixel-Level Sea-Land Segmentation</article-title><source>IEEE J. Sel. Top. Appl. Earth Obs. Remote Sens.</source><year>2018</year><volume>11</volume><fpage>3954</fpage><lpage>3962</lpage><pub-id pub-id-type="doi">10.1109/JSTARS.2018.2833382</pub-id></element-citation></ref><ref id="B38-sensors-23-04491"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cheng</surname><given-names>D.</given-names></name>
<name><surname>Meng</surname><given-names>G.</given-names></name>
<name><surname>Xiang</surname><given-names>S.</given-names></name>
<name><surname>Pan</surname><given-names>C.</given-names></name>
</person-group><article-title>FusionNet: Edge Aware Deep Convolutional Networks for Semantic Segmentation of Remote Sensing Harbor Images</article-title><source>IEEE J. Sel. Top. Appl. Earth Obs. Remote Sens.</source><year>2017</year><volume>10</volume><fpage>5769</fpage><lpage>5783</lpage><pub-id pub-id-type="doi">10.1109/JSTARS.2017.2747599</pub-id></element-citation></ref><ref id="B39-sensors-23-04491"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cheng</surname><given-names>D.</given-names></name>
<name><surname>Meng</surname><given-names>G.</given-names></name>
<name><surname>Cheng</surname><given-names>G.</given-names></name>
<name><surname>Pan</surname><given-names>C.</given-names></name>
</person-group><article-title>SeNet: Structured Edge Network for Sea&#x02013;Land Segmentation</article-title><source>IEEE Geosci. Remote Sens. Lett.</source><year>2017</year><volume>14</volume><fpage>247</fpage><lpage>251</lpage><pub-id pub-id-type="doi">10.1109/LGRS.2016.2637439</pub-id></element-citation></ref><ref id="B40-sensors-23-04491"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cui</surname><given-names>B.</given-names></name>
<name><surname>Jing</surname><given-names>W.</given-names></name>
<name><surname>Huang</surname><given-names>L.</given-names></name>
<name><surname>Li</surname><given-names>Z.</given-names></name>
<name><surname>Lu</surname><given-names>Y.</given-names></name>
</person-group><article-title>SANet: A Sea&#x02013;Land Segmentation Network Via Adaptive Multiscale Feature Learning</article-title><source>IEEE J. Sel. Top. Appl. Earth Obs. Remote Sens.</source><year>2021</year><volume>14</volume><fpage>116</fpage><lpage>126</lpage><pub-id pub-id-type="doi">10.1109/JSTARS.2020.3040176</pub-id></element-citation></ref><ref id="B41-sensors-23-04491"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Dang</surname><given-names>K.B.</given-names></name>
<name><surname>Dang</surname><given-names>V.B.</given-names></name>
<name><surname>Ngo</surname><given-names>V.L.</given-names></name>
<name><surname>Vu</surname><given-names>K.C.</given-names></name>
<name><surname>Nguyen</surname><given-names>H.</given-names></name>
<name><surname>Nguyen</surname><given-names>D.A.</given-names></name>
<name><surname>Nguyen</surname><given-names>T.D.L.</given-names></name>
<name><surname>Pham</surname><given-names>T.P.N.</given-names></name>
<name><surname>Giang</surname><given-names>T.L.</given-names></name>
<name><surname>Nguyen</surname><given-names>H.D.</given-names></name>
<etal/>
</person-group><article-title>Application of Deep Learning Models to Detect Coastlines and Shorelines</article-title><source>J. Environ. Manag.</source><year>2022</year><volume>320</volume><fpage>115732</fpage><pub-id pub-id-type="doi">10.1016/j.jenvman.2022.115732</pub-id><?supplied-pmid 35930878?><pub-id pub-id-type="pmid">35930878</pub-id></element-citation></ref><ref id="B42-sensors-23-04491"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Tajima</surname><given-names>Y.</given-names></name>
<name><surname>Wu</surname><given-names>L.</given-names></name>
<name><surname>Watanabe</surname><given-names>K.</given-names></name>
</person-group><article-title>Development of a Shoreline Detection Method Using an Artificial Neural Network Based on Satellite SAR Imagery</article-title><source>Remote Sens.</source><year>2021</year><volume>13</volume><elocation-id>2254</elocation-id><pub-id pub-id-type="doi">10.3390/rs13122254</pub-id></element-citation></ref><ref id="B43-sensors-23-04491"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Jing</surname><given-names>W.</given-names></name>
<name><surname>Cui</surname><given-names>B.</given-names></name>
<name><surname>Lu</surname><given-names>Y.</given-names></name>
<name><surname>Huang</surname><given-names>L.</given-names></name>
</person-group><article-title>BS-Net: Using Joint-Learning Boundary and Segmentation Network for Coastline Extraction from Remote Sensing Images</article-title><source>Remote Sens. Lett.</source><year>2021</year><volume>12</volume><fpage>1260</fpage><lpage>1268</lpage><pub-id pub-id-type="doi">10.1080/2150704X.2021.1979271</pub-id></element-citation></ref><ref id="B44-sensors-23-04491"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Scarpetta</surname><given-names>M.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
<name><surname>Adamo</surname><given-names>F.</given-names></name>
<name><surname>Ragolia</surname><given-names>M.A.</given-names></name>
<name><surname>Giaquinto</surname><given-names>N.</given-names></name>
</person-group><article-title>Detection and Characterization of Multiple Discontinuities in Cables with Time-Domain Reflectometry and Convolutional Neural Networks</article-title><source>Sensors</source><year>2021</year><volume>21</volume><elocation-id>8032</elocation-id><pub-id pub-id-type="doi">10.3390/s21238032</pub-id><pub-id pub-id-type="pmid">34884035</pub-id></element-citation></ref><ref id="B45-sensors-23-04491"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Frolov</surname><given-names>V.</given-names></name>
<name><surname>Faizov</surname><given-names>B.</given-names></name>
<name><surname>Shakhuro</surname><given-names>V.</given-names></name>
<name><surname>Sanzharov</surname><given-names>V.</given-names></name>
<name><surname>Konushin</surname><given-names>A.</given-names></name>
<name><surname>Galaktionov</surname><given-names>V.</given-names></name>
<name><surname>Voloboy</surname><given-names>A.</given-names></name>
</person-group><article-title>Image Synthesis Pipeline for CNN-Based Sensing Systems</article-title><source>Sensors</source><year>2022</year><volume>22</volume><elocation-id>2080</elocation-id><pub-id pub-id-type="doi">10.3390/s22062080</pub-id><pub-id pub-id-type="pmid">35336251</pub-id></element-citation></ref><ref id="B46-sensors-23-04491"><label>46.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Scarpetta</surname><given-names>M.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
<name><surname>Andria</surname><given-names>G.</given-names></name>
<name><surname>Ragolia</surname><given-names>M.A.</given-names></name>
<name><surname>Giaquinto</surname><given-names>N.</given-names></name>
</person-group><article-title>Analysis of TDR Signals with Convolutional Neural Networks</article-title><source>Proceedings of the 2021 IEEE International Instrumentation and Measurement Technology Conference (I2MTC)</source><conf-loc>Virtual</conf-loc><conf-date>17&#x02013;20 May 2021</conf-date><fpage>1</fpage><lpage>6</lpage></element-citation></ref><ref id="B47-sensors-23-04491"><label>47.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Scarpetta</surname><given-names>M.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
<name><surname>D&#x02019;Alessandro</surname><given-names>V.I.</given-names></name>
<name><surname>Palma</surname><given-names>L.D.</given-names></name>
<name><surname>Giaquinto</surname><given-names>N.</given-names></name>
</person-group><article-title>A New Dataset of Satellite Images for Deep Learning-Based Coastline Measurement</article-title><source>Proceedings of the 2022 IEEE International Conference on Metrology for Extended Reality, Artificial Intelligence and Neural Engineering (MetroXRAINE)</source><conf-loc>Rome, Italy</conf-loc><conf-date>26&#x02013;28 October 2022</conf-date><fpage>635</fpage><lpage>640</lpage></element-citation></ref><ref id="B48-sensors-23-04491"><label>48.</label><element-citation publication-type="webpage"><person-group person-group-type="author">
<name><surname>Andria</surname><given-names>G.</given-names></name>
<name><surname>Scarpetta</surname><given-names>M.</given-names></name>
<name><surname>Spadavecchia</surname><given-names>M.</given-names></name>
<name><surname>Affuso</surname><given-names>P.</given-names></name>
<name><surname>Giaquinto</surname><given-names>N.</given-names></name>
</person-group><article-title>Sentinel2-NOAA Water Edges Dataset (SNOWED)</article-title><comment>Available online: </comment><pub-id pub-id-type="doi">10.5281/Zenodo.7871636</pub-id><date-in-citation content-type="access-date" iso-8601-date="2023-04-27">(accessed on 27 April 2023)</date-in-citation></element-citation></ref><ref id="B49-sensors-23-04491"><label>49.</label><element-citation publication-type="webpage"><article-title>QueryPlanet Water Segmentation Data Set</article-title><comment>Available online: <ext-link xlink:href="http://queryplanet.sentinel-hub.com/index.html?prefix=/#waterdata" ext-link-type="uri">http://queryplanet.sentinel-hub.com/index.html?prefix=/#waterdata</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2022-06-28">(accessed on 28 June 2022)</date-in-citation></element-citation></ref><ref id="B50-sensors-23-04491"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Mcfeeters</surname><given-names>S.K.</given-names></name>
</person-group><article-title>The Use of the Normalized Difference Water Index (NDWI) in the Delineation of Open Water Features</article-title><source>Int. J. Remote Sens.</source><year>1996</year><volume>17</volume><fpage>1425</fpage><lpage>1432</lpage><pub-id pub-id-type="doi">10.1080/01431169608948714</pub-id></element-citation></ref><ref id="B51-sensors-23-04491"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Yang</surname><given-names>T.</given-names></name>
<name><surname>Jiang</surname><given-names>S.</given-names></name>
<name><surname>Hong</surname><given-names>Z.</given-names></name>
<name><surname>Zhang</surname><given-names>Y.</given-names></name>
<name><surname>Han</surname><given-names>Y.</given-names></name>
<name><surname>Zhou</surname><given-names>R.</given-names></name>
<name><surname>Wang</surname><given-names>J.</given-names></name>
<name><surname>Yang</surname><given-names>S.</given-names></name>
<name><surname>Tong</surname><given-names>X.</given-names></name>
<name><surname>Kuc</surname><given-names>T.</given-names></name>
</person-group><article-title>Sea-Land Segmentation Using Deep Learning Techniques for Landsat-8 OLI Imagery</article-title><source>Mar. Geod.</source><year>2020</year><volume>43</volume><fpage>105</fpage><lpage>133</lpage><pub-id pub-id-type="doi">10.1080/01490419.2020.1713266</pub-id></element-citation></ref><ref id="B52-sensors-23-04491"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Erdem</surname><given-names>F.</given-names></name>
<name><surname>Bayram</surname><given-names>B.</given-names></name>
<name><surname>Bakirman</surname><given-names>T.</given-names></name>
<name><surname>Bayrak</surname><given-names>O.C.</given-names></name>
<name><surname>Akpinar</surname><given-names>B.</given-names></name>
</person-group><article-title>An Ensemble Deep Learning Based Shoreline Segmentation Approach (WaterNet) from Landsat 8 OLI Images</article-title><source>Adv. Space Res.</source><year>2021</year><volume>67</volume><fpage>964</fpage><lpage>974</lpage><pub-id pub-id-type="doi">10.1016/j.asr.2020.10.043</pub-id></element-citation></ref><ref id="B53-sensors-23-04491"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Seale</surname><given-names>C.</given-names></name>
<name><surname>Redfern</surname><given-names>T.</given-names></name>
<name><surname>Chatfield</surname><given-names>P.</given-names></name>
<name><surname>Luo</surname><given-names>C.</given-names></name>
<name><surname>Dempsey</surname><given-names>K.</given-names></name>
</person-group><article-title>Coastline Detection in Satellite Imagery: A Deep Learning Approach on New Benchmark Data</article-title><source>Remote Sens. Environ.</source><year>2022</year><volume>278</volume><fpage>113044</fpage><pub-id pub-id-type="doi">10.1016/j.rse.2022.113044</pub-id></element-citation></ref><ref id="B54-sensors-23-04491"><label>54.</label><element-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Snyder</surname><given-names>J.P.</given-names></name>
</person-group><source>Map Projections&#x02014;A Working Manual</source><publisher-name>US Government Printing Office</publisher-name><publisher-loc>Washington, DC, USA</publisher-loc><year>1987</year><volume>Volume 1395</volume></element-citation></ref><ref id="B55-sensors-23-04491"><label>55.</label><element-citation publication-type="webpage"><article-title>Sentinel-2&#x02014;Missions&#x02014;Sentinel Online&#x02014;Sentinel Online</article-title><comment>Available online: <ext-link xlink:href="https://sentinel.esa.int/en/web/sentinel/missions/sentinel-2" ext-link-type="uri">https://sentinel.esa.int/en/web/sentinel/missions/sentinel-2</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2022-06-27">(accessed on 27 June 2022)</date-in-citation></element-citation></ref><ref id="B56-sensors-23-04491"><label>56.</label><element-citation publication-type="gov"><article-title>NOAA Shoreline Website</article-title><comment>Available online: <ext-link xlink:href="https://shoreline.noaa.gov/data/datasheets/cusp.html" ext-link-type="uri">https://shoreline.noaa.gov/data/datasheets/cusp.html</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2022-06-29">(accessed on 29 June 2022)</date-in-citation></element-citation></ref><ref id="B57-sensors-23-04491"><label>57.</label><element-citation publication-type="gov"><person-group person-group-type="author">
<name><surname>Aslaksen</surname><given-names>M.L.</given-names></name>
<name><surname>Blackford</surname><given-names>T.</given-names></name>
<name><surname>Callahan</surname><given-names>D.</given-names></name>
<name><surname>Clark</surname><given-names>B.</given-names></name>
<name><surname>Doyle</surname><given-names>T.</given-names></name>
<name><surname>Engelhardt</surname><given-names>W.</given-names></name>
<name><surname>Espey</surname><given-names>M.</given-names></name>
<name><surname>Gillens</surname><given-names>D.</given-names></name>
<name><surname>Goodell</surname><given-names>S.</given-names></name>
<name><surname>Graham</surname><given-names>D.</given-names></name>
<etal/>
</person-group><article-title>Scope of Work for Shoreline Mapping under the Noaa Coastal Mapping Program, Version 15</article-title><comment>Available online: <ext-link xlink:href="https://geodesy.noaa.gov/ContractingOpportunities/cmp-sow-v15.pdf" ext-link-type="uri">https://geodesy.noaa.gov/ContractingOpportunities/cmp-sow-v15.pdf</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-04-13">(accessed on 13 April 2023)</date-in-citation></element-citation></ref><ref id="B58-sensors-23-04491"><label>58.</label><element-citation publication-type="webpage"><article-title>PEPS&#x02014;Operating Platform Sentinel Products (CNES)</article-title><comment>Available online: <ext-link xlink:href="https://peps.cnes.fr/rocket/#/home" ext-link-type="uri">https://peps.cnes.fr/rocket/#/home</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2022-06-29">(accessed on 29 June 2022)</date-in-citation></element-citation></ref><ref id="B59-sensors-23-04491"><label>59.</label><element-citation publication-type="webpage"><article-title>Sen2Cor&#x02014;STEP</article-title><comment>Available online: <ext-link xlink:href="http://step.esa.int/main/snap-supported-plugins/sen2cor/" ext-link-type="uri">http://step.esa.int/main/snap-supported-plugins/sen2cor/</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-01-31">(accessed on 31 January 2023)</date-in-citation></element-citation></ref><ref id="B60-sensors-23-04491"><label>60.</label><element-citation publication-type="webpage"><article-title>Level-2A Algorithm&#x02014;Sentinel-2 MSI Technical Guide&#x02014;Sentinel Online</article-title><comment>Available online: <ext-link xlink:href="https://copernicus.eu/technical-guides/sentinel-2-msi/level-2a/algorithm-overview" ext-link-type="uri">https://copernicus.eu/technical-guides/sentinel-2-msi/level-2a/algorithm-overview</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-04-17">(accessed on 17 April 2023)</date-in-citation></element-citation></ref><ref id="B61-sensors-23-04491"><label>61.</label><element-citation publication-type="webpage"><article-title>Sentinel-2&#x02014;Data Products&#x02014;Sentinel Handbook&#x02014;Sentinel Online</article-title><comment>Available online: <ext-link xlink:href="https://sentinel.esa.int/en/web/sentinel/missions/sentinel-2/data-products" ext-link-type="uri">https://sentinel.esa.int/en/web/sentinel/missions/sentinel-2/data-products</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2023-01-31">(accessed on 31 January 2023)</date-in-citation></element-citation></ref><ref id="B62-sensors-23-04491"><label>62.</label><element-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Cavone</surname><given-names>G.</given-names></name>
<name><surname>Giaquinto</surname><given-names>N.</given-names></name>
<name><surname>Fabbiano</surname><given-names>L.</given-names></name>
<name><surname>Vacca</surname><given-names>G.</given-names></name>
</person-group><article-title>Design of Single Sampling Plans by Closed-Form Equations</article-title><source>Proceedings of the 2013 IEEE International Instrumentation and Measurement Technology Conference (I2MTC)</source><conf-loc>Minneapolis, MN, USA</conf-loc><conf-date>6&#x02013;9 May 2013</conf-date><fpage>597</fpage><lpage>602</lpage></element-citation></ref><ref id="B63-sensors-23-04491"><label>63.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Nicholson</surname><given-names>W.L.</given-names></name>
</person-group><article-title>On the Normal Approximation to the Hypergeometric Distribution</article-title><source>Ann. Math. Stat.</source><year>1956</year><volume>27</volume><fpage>471</fpage><lpage>483</lpage><pub-id pub-id-type="doi">10.1214/aoms/1177728270</pub-id></element-citation></ref><ref id="B64-sensors-23-04491"><label>64.</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kingma</surname><given-names>D.P.</given-names></name>
<name><surname>Ba</surname><given-names>J.</given-names></name>
</person-group><article-title>Adam: A Method for Stochastic Optimization</article-title><source>arXiv</source><year>2014</year><pub-id pub-id-type="arxiv">1412.6980</pub-id></element-citation></ref></ref-list></back><floats-group><fig position="float" id="sensors-23-04491-f001"><label>Figure 1</label><caption><p>Selected CUSP shorelines (those from June 2015 onwards) compared to the complete data.</p></caption><graphic xlink:href="sensors-23-04491-g001" position="float"/></fig><fig position="float" id="sensors-23-04491-f002"><label>Figure 2</label><caption><p>Example of Sentinel-2 Level-1C tile (true color image) split into sub-tiles of size <inline-formula><mml:math id="mm15" overflow="scroll"><mml:mrow><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula>. Only sub-tiles containing shoreline based on measurements performed in dates within 30 days from the Sentinel tile&#x02019;s acquisition date are analyzed.</p></caption><graphic xlink:href="sensors-23-04491-g002" position="float"/></fig><fig position="float" id="sensors-23-04491-f003"><label>Figure 3</label><caption><p>Steps for generating the binary segmentation mask for a sample of the dataset. (<bold>a</bold>) Selection of the shoreline paths inside the sub-tile. Paths with dates compatible with the Sentinel-2 tile&#x02019;s date are depicted in red and the other paths in yellow. (<bold>b</bold>) Merging of contacting paths. Distinct merged paths are depicted using different colors, while the sub-tile border is in black. (<bold>c</bold>) Clipping of merged paths using the sub-tile border as the clipping window. After this stage, closed polygons are obtained. (<bold>d</bold>) Starting from a zero-filled matrix of the sub-tile, ones are added in the regions defined by the polygons. (<bold>e</bold>) A binary map is obtained by classifying the pixels of matrix (<bold>d</bold>) into even and odds.</p></caption><graphic xlink:href="sensors-23-04491-g003" position="float"/></fig><fig position="float" id="sensors-23-04491-f004"><label>Figure 4</label><caption><p>Sentinel-2 Level-2A scene classification (SC) mask of the sub-tile in <xref rid="sensors-23-04491-f003" ref-type="fig">Figure 3</xref>. For the sake of clarity, the legend includes all the 12 classes provided by Level-2A SC, although only some of them are identified in this case.</p></caption><graphic xlink:href="sensors-23-04491-g004" position="float"/></fig><fig position="float" id="sensors-23-04491-f005"><label>Figure 5</label><caption><p>Flowchart of the proposed method. Details about each operation are provided in the previous subsections. Operations performed with novel methods, proposed in the paper, are marked with a dashed blue line. Operations performed with known methods are marked with a solid blue line. Operations with gray backgrounds are those specific for Sentinel 2 imagery.</p></caption><graphic xlink:href="sensors-23-04491-g005" position="float"/></fig><fig position="float" id="sensors-23-04491-f006"><label>Figure 6</label><caption><p>Examples of semantically annotated Sentinel-2 satellite images (true color image on the left) and labels (on the right) contained in the proposed dataset.</p></caption><graphic xlink:href="sensors-23-04491-g006" position="float"/></fig><fig position="float" id="sensors-23-04491-f007"><label>Figure 7</label><caption><p>Examples of &#x0201c;bad&#x0201d; samples with a clearly incomplete label, found in the process of quality assessment of the dataset. (<bold>a</bold>) True color image. (<bold>b</bold>) Sea/land segmentation. (<bold>c</bold>) Sentinel-2 Level-2A scene classification. There is a portion of land not included in the label, in the upper left corner.</p></caption><graphic xlink:href="sensors-23-04491-g007" position="float"/></fig><fig position="float" id="sensors-23-04491-f008"><label>Figure 8</label><caption><p>Example of a &#x0201c;suspect&#x0201d; sample, with a possibly incomplete label. There is a region that is water according to Sentinel-2 scene interpretation. The main shoreline in the sample is labeled. (<bold>a</bold>) True color image. (<bold>b</bold>) Sea/land segmentation. (<bold>c</bold>) Sentinel-2 Level-2A scene classification.</p></caption><graphic xlink:href="sensors-23-04491-g008" position="float"/></fig><fig position="float" id="sensors-23-04491-f009"><label>Figure 9</label><caption><p>Example of a &#x0201c;particularly good&#x0201d; sample, with an elaborated water edge that is ignored by Sentinel-2 SC. (<bold>a</bold>) True color image. (<bold>b</bold>) Sea/land segmentation. (<bold>c</bold>) Sentinel-2 Level-2A scene classification.</p></caption><graphic xlink:href="sensors-23-04491-g009" position="float"/></fig><fig position="float" id="sensors-23-04491-f010"><label>Figure 10</label><caption><p>Loss function and mean IoU versus number of training epochs.</p></caption><graphic xlink:href="sensors-23-04491-g010" position="float"/></fig><fig position="float" id="sensors-23-04491-f011"><label>Figure 11</label><caption><p>(<bold>a</bold>&#x02013;<bold>d</bold>) Sea/land segmentation results obtained with the trained U-Net model for the first four samples of the validation set.</p></caption><graphic xlink:href="sensors-23-04491-g011" position="float"/></fig><table-wrap position="float" id="sensors-23-04491-t001"><object-id pub-id-type="pii">sensors-23-04491-t001_Table 1</object-id><label>Table 1</label><caption><p>Main characteristics of the four examined datasets. The same characteristics for the new SNOWED dataset are included for comparison. They highlight that SNOWED (i) is compatible with, and adds up, to SWED; (ii) uses NOAA measurements, instead of human interpretation of images.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1">Dataset ID</th><th align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1">N. of Images</th><th align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1">Image Size</th><th align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1">Source of Coastline Data</th></tr></thead><tbody><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">QueryPlanet [<xref rid="B49-sensors-23-04491" ref-type="bibr">49</xref>]</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">5177</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">64 &#x000d7; 64</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Human interpretation of TCI images</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Sea&#x02013;land segmentation benchmark dataset [<xref rid="B51-sensors-23-04491" ref-type="bibr">51</xref>]</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">831</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">512 &#x000d7; 512</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Human interpretation of TCI images</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">YTU-WaterNet [<xref rid="B52-sensors-23-04491" ref-type="bibr">52</xref>]</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">1008</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">512 &#x000d7; 512</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Human-generated OpenStreetMap water polygons data</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">SWED [<xref rid="B53-sensors-23-04491" ref-type="bibr">53</xref>]</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">9013</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">256 &#x000d7; 256</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Human interpretation of high-resolution aerial imagery available in Google Earth and Bing Maps</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">SNOWED</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">4334</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">256 &#x000d7; 256</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">U.S. NOAA shoreline measurements</td></tr></tbody></table></table-wrap><table-wrap position="float" id="sensors-23-04491-t002"><object-id pub-id-type="pii">sensors-23-04491-t002_Table 2</object-id><label>Table 2</label><caption><p>Statistics about the NOAA&#x02019;s CUSP shoreline data.</p></caption><table frame="hsides" rules="groups"><tbody><tr><td align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1">Initial number of paths</td><td align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1">779,954</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Total length of the paths</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">403,707 km</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Number of selected paths</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">221,331</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Total length of selected paths</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">107,600 km</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Number of paths after merging</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">126,938</td></tr></tbody></table></table-wrap></floats-group></article>