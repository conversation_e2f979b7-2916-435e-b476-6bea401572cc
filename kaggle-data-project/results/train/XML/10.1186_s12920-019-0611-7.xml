<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Med Genomics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Med Genomics</journal-id><journal-title-group><journal-title>BMC Medical Genomics</journal-title></journal-title-group><issn pub-type="epub">1755-8794</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6882202</article-id><article-id pub-id-type="publisher-id">611</article-id><article-id pub-id-type="doi">10.1186/s12920-019-0611-7</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Identification and ranking of recurrent neo-epitopes in cancer</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Blanc</surname><given-names>Eric</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Holtgrewe</surname><given-names>Manuel</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Dhamodaran</surname><given-names>Arunraj</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Messerschmidt</surname><given-names>Clemens</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Willimsky</surname><given-names>Gerald</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff4">4</xref><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Blankenstein</surname><given-names>Thomas</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Beule</surname><given-names>Dieter</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.484013.a</institution-id><institution>Core Unit Bioinformatics, Berlin Institute of Health, </institution></institution-wrap>Charit&#x000e9;platz 1, Berlin, 10117 Germany </aff><aff id="Aff2"><label>2</label>Institute of Immunology, Charit&#x000e9; - Universit&#x000e4;tsmedizin Berlin, corporate member of Freie Universit&#x000e4;t Berlin, Humboldt-Universit&#x000e4;t zu Berlin, and Berlin Institute of Health, Lindenberger Weg 80, Berlin, 13125 Germany </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 1014 0849</institution-id><institution-id institution-id-type="GRID">grid.419491.0</institution-id><institution>Max Delbr&#x000fc;ck Center for Molecular Medicine in the Helmholtz Association (MDC), </institution></institution-wrap>Robert-R&#x000f6;ssle-Str. 10, Berlin, 13092 Germany </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="GRID">grid.484013.a</institution-id><institution>Berlin Institute of Health, </institution></institution-wrap>Charit&#x000e9;platz 1, Berlin, 10117 Germany </aff><aff id="Aff5"><label>5</label>Charit&#x000e9; - Universit&#x000e4;tsmedizin Berlin, corporate member of Freie Universit&#x000e4;t Berlin, Humboldt-Universit&#x000e4;t zu Berlin, and Berlin Institute of Health, Charit&#x000e9;platz 1, Berlin, 10117 Germany </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0492 0584</institution-id><institution-id institution-id-type="GRID">grid.7497.d</institution-id><institution>German Cancer Research Center (DKFZ), </institution></institution-wrap>Im Neuenheimer Feld 280, Heidelberg, 69120 Germany </aff></contrib-group><pub-date pub-type="epub"><day>27</day><month>11</month><year>2019</year></pub-date><pub-date pub-type="pmc-release"><day>27</day><month>11</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>12</volume><elocation-id>171</elocation-id><history><date date-type="received"><day>9</day><month>4</month><year>2019</year></date><date date-type="accepted"><day>25</day><month>10</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2019</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold> This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Immune escape is one of the hallmarks of cancer and several new treatment approaches attempt to modulate and restore the immune system&#x02019;s capability to target cancer cells. At the heart of the immune recognition process lies antigen presentation from somatic mutations. These neo-epitopes are emerging as attractive targets for cancer immunotherapy and new strategies for rapid identification of relevant candidates have become a priority.</p></sec><sec><title>Methods</title><p id="Par2">We carefully screen TCGA data sets for recurrent somatic amino acid exchanges and apply MHC class I binding predictions.</p></sec><sec><title>Results</title><p id="Par3">We propose a method for <italic>in silico</italic> selection and prioritization of candidates which have a high potential for neo-antigen generation and are likely to appear in multiple patients. While the percentage of patients carrying a specific neo-epitope and HLA-type combination is relatively small, the sheer number of new patients leads to surprisingly high reoccurence numbers. We identify 769 epitopes which are expected to occur in 77629 patients per year.</p></sec><sec><title>Conclusion</title><p id="Par4">While our candidate list will definitely contain false positives, the results provide an objective order for wet-lab testing of reusable neo-epitopes. Thus recurrent neo-epitopes may be suitable to supplement existing personalized T cell treatment approaches with precision treatment options.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Cancer</kwd><kwd>Immunotherapy</kwd><kwd>Neo-epitope</kwd><kwd>Neo-antigen</kwd><kwd>Precision treatment</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001659</institution-id><institution>Deutsche Forschungsgemeinschaft</institution></institution-wrap></funding-source><award-id>SFB-TR36</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001659</institution-id><institution>Deutsche Forschungsgemeinschaft</institution></institution-wrap></funding-source><award-id>SFB-TR36</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100005972</institution-id><institution>Deutsche Krebshilfe</institution></institution-wrap></funding-source><award-id>111546</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100005972</institution-id><institution>Deutsche Krebshilfe</institution></institution-wrap></funding-source><award-id>111546</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution>Berlin Institute of Health</institution></funding-source><award-id>CRG-1</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution>Berlin Institute of Health</institution></funding-source><award-id>CRG-1</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2019</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>Increasing evidence suggests that clinical efficacy of cancer immunotherapy is driven by T cell reactivity against neo-antigens [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR5">5</xref>]. While not yet fully understood, immune response and recognition of tumor cells containing specific peptides depends critically on the ability of the MHC class I complexes to bind to the peptide in order to present it to a T cell. Neo-antigens can be created by a multitude of processes like aberrant expression of genes normally restricted to immuno-privileged tissues, viral etiology or by tumor specific DNA alterations that result in the formation of novel protein sequences. Furthermore there is now evidence for neo-epitopes generated from alternative splicing [<xref ref-type="bibr" rid="CR6">6</xref>] and alterations in non-coding regions [<xref ref-type="bibr" rid="CR7">7</xref>].</p><p>With the advent of affordable short read sequencing, comprehensive neo-antigen screening based on whole exome sequencing has become feasible and many cancer immune therapeutic approaches try to utilize detailed understanding of the neo-epitope spectrum to create additional or boost pre-existing T cell reactivity for therapeutic purposes [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR9">9</xref>]. However, in practice the selection and validation of the most promising neo-epitope candidates is a difficult and time-consuming task. The typical approach is based on the private mutational catalogue of the individual patient: exome sequencing data is subjected to bioinformatics analysis and used to predict neo-epitopes and their binding affinities to the MHC class I complex. Our study aims to complement this approach by a precision medicine perspective. We search and prioritize neo-epitope candidates which have a high potential for neo-antigen generation and are likely to appear in multiple patients. These neo-antigens hold the potential for development of <italic>off the shelf T cell therapies</italic> for sub groups of cancer patients. We use epidemiological data to give rough estimates for the expected number of patients in these groups.</p><p>Candidate prediction always relies on somatic variant detection workflows and affinity prediction algorithms based on machine learning, see e.g. [<xref ref-type="bibr" rid="CR10">10</xref>]. Binding prediction far from perfect [<xref ref-type="bibr" rid="CR11">11</xref>] especially for rarer HLA types, and may also depend on mutational context [<xref ref-type="bibr" rid="CR12">12</xref>]. Catalogues of the neo-epitope landscape across various cancer entities have been created by various authors [<xref ref-type="bibr" rid="CR13">13</xref>&#x02013;<xref ref-type="bibr" rid="CR15">15</xref>]. While neoantigen landscape is diverse and sparse [<xref ref-type="bibr" rid="CR13">13</xref>], here we provide an unbiased, comprehensive ranking of candidates, defined as neo-epitopes arising from recurrent mutations, predicted to be binding to a specific HLA-1 allele. The candidates are ranked according to the expected number of target patients.</p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Data sets</title><p>Somatic variants for different cancer entities have been determined using matched pairs of tumor and blood whole exome or whole genome sequencing in the TCGA consortium. We downloaded the open-access somatic variants from GDC data release 7.0 [<xref ref-type="bibr" rid="CR16">16</xref>], consisting of 33 TCGA projects and 10,182 donors in total. Details of the somatic variant calling can be found in [<xref ref-type="bibr" rid="CR17">17</xref>]. We excluded patients without corresponding entries in the clinical information tables, and 7 projects with less than 100 samples, yielding 9,641 samples covering 26 cancer studies. Figure&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> provides an overview of the complete bioinformatics process, from the GDC somatic single nucleotide variants to the identification of the candidates.
<fig id="Fig1"><label>Fig. 1</label><caption><p>Workflow overview <bold>a</bold> Overview of the recurrent neo-epitope candidates generation process: TCGA studies are selected for at least 100 donors with clinical annotations. For each of these studies, recurrent strongly supported missense Single-Nucleotide Variants are collected. Neo-epitopes binding to 11 HLA-1 types are predicted, redundancy is removed from that set (see B) and strong binders are retained. <bold>b</bold> Example of epitope redundancy: the 18 amino-acids long sequence surrounding recurrent variant GLRA3:S274L generates 7 binding neo-epitopes for the type HLA-A*02:01. Our pipeline retains only the strongest predicted binder for a given variant and HLA-1 type pair (the first, with an IC<sub>50</sub> of 8.8 nM in the example). <bold>c</bold> Number of SNVs occuring in genes classified as Oncogenes or Tumor Suppressors by Vogelstein et al. [<xref ref-type="bibr" rid="CR28">28</xref>], at various point of the variant selection and neo-epitope selection process</p></caption><graphic xlink:href="12920_2019_611_Fig1_HTML" id="MO1"/></fig>
</p></sec><sec id="Sec4"><title>Variant selection</title><p>For each sample we selected all single nucleotide variants obtained by the &#x0201c;mutect2&#x0201d; pipeline, that had a &#x0201c;Variant_Type&#x0201d; equal to &#x0201c;SNP&#x0201d;, a valid ENSEMBL transcript ID and a valid protein mutation in &#x0201c;HGVSp_Short&#x0201d;. From these variants, we selected those with a &#x0201c;Variant_Classification&#x0201d; equal to &#x0201c;Missense_Mutation&#x0201d;. We checked that all variants had a &#x0201c;Mutation_Status&#x0201d; equal to (up to capitalisation) &#x0201c;Somatic&#x0201d;, that the total depth &#x0201c;t_depth&#x0201d; was the sum of the reference &#x0201c;t_ref_count&#x0201d; and the alternate &#x0201c;t_alt_count&#x0201d; alleles counts, and that the genomics variant length is one nucleotide. To avoid high number of false positives we consider only variants that are supported by at least 5 reads and have a VAF of at least 10%. Furthermore we removed any variant that occurs with more than 1% in any population contained in the ExAC database version 0.31 [<xref ref-type="bibr" rid="CR18">18</xref>], by coordinates liftover from the GRCh38 to hg19 human genome versions. This way we obtained 26 cancer entity data sets containing a total of 9,641 samples with an overall 1,384,531 variants.</p></sec><sec id="Sec5"><title>Recurrent protein variant selection</title><p>We define recurrence strictly on the protein/amino acid exchange level, i.e. different nucleotide acid variants leading to the same amino acid exchange due to code redundancy will be counted together. Recurrent protein variants are defined within each TCGA study. A protein variant is deemed recurrent when it appears in at least 1% of all the patients in the cohort. As cancer types are only considered when the number of patients involved in the studies is greater than 100, this threshold ensures that every recurrent variant has been observed in at least 2 patients for a given cancer type. To be conservative, the recurrence frequency has been computed using, for the denominator, all patients with clinical information in the study, including those without high-confidence missense SNVs. Using this definition, the total number of recurrent amino acid changes is 1055. A variant recurrent in multiple cancer types is counted multiple times in the above number, the number of unique recurrent variants regardless of the cancer is 869. Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref> shows the most frequent amino acid exchanges across 25 cancer entities, as no variant from project TCGA-KIRC&#x02019;s donors is labeled as recurrent.</p><p>Recurrent variants occurring at the same positions (for example when gene&#x02019;s IDH1 codon R132 is mutated to amino acid H, C, G or S) have been merged into 819 variants suitable for comparisons with the cancer hot spots lists [<xref ref-type="bibr" rid="CR14">14</xref>]. 122 out of the 819 merged variants belong to the set of 470 cancer hotspot variants, and 5 (PCBP1:L100, SPTLC3:R97, EEF1A1:T432, BCLAF1:E163 &#x00026; TTN:S3271) to the set of presumptive false positives hotspots listed in the supplementary material of [<xref ref-type="bibr" rid="CR14">14</xref>].</p></sec><sec id="Sec6"><title>MHC class i binding prediction and epitopes selection</title><p>For all recurrent variants identified, we assess <italic>in silico</italic> their predicted propensity that the amino-acid exchange generates a binding neo-epitope.</p><p>A variety of machine learning algorithms have been developed to determine the MHC binding <italic>in silico</italic>, see ref. [<xref ref-type="bibr" rid="CR19">19</xref>] for review. Most methods are trained on Immune Epitope Database (IEDB) [<xref ref-type="bibr" rid="CR20">20</xref>] entries and use allele specific predictors for frequent alleles, while pan-methods are applied to extrapolate to less common alleles. We predicted the MHC class I binding using NetMHCcons [<xref ref-type="bibr" rid="CR21">21</xref>] v1.1, which predicts peptides IC<sub>50</sub> binding, and classifies these predictions as non-binder, weak and strong binders, based on the relative ranking of binding predictions. As the range of IC<sub>50</sub> binding values strongly depend on the HLA-1 allele [<xref ref-type="bibr" rid="CR22">22</xref>], we have used the NetMHCcons classification to select our neo-epitope candidates.</p><p>For a given recurrent variant and a given HLA-1 type, the epitope prediction pipeline can produce multiple overlapping epitopes candidates, differing by their length and/or their position (see Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>B). To remove such size redundancy, only the epitope with the lowest predicted mutant sequence IC<sub>50</sub> is retained. This procedure also removes non-overlapping epitopes, to keep only at most one epitope per recurrent protein variant and HLA-1 type. For comparison we also compute the IC<sub>50</sub> for the respective wild type peptide.</p><p>For MHC class I binding prediction we selected 11 frequent HLA-1 types: HLA-A*01:01, HLA-A*02:01, HLA-A*03:01, HLA-A*11:01, HLA-B*07:02, HLA-B*08:01, HLA-B*15:01, HLA-C*04:01, HLA-C*06:02, HLA-C*07:01, HLA-C*07:02. We limited the search for poly-peptides 9, 10 and 11 amino-acids long. For these alleles, we obtain 769 strong binding recurrent peptides and 1829 weak binders, over all considered cancer types. Their complete list is in Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>, where each candidate is listed with the HLA-1 type it is preticted to bind to.</p></sec><sec id="Sec7"><title>Data QC</title><p>To ensure that the proportion of variants caused by technical artifacts is small, we have computed the proportion of SNVs called in poly-A, poly-C, poly-G or poly-T repeats of length greater than 6 have been computed for each data study [<xref ref-type="bibr" rid="CR23">23</xref>], for unique variants (that occur in only one patient across a project cohort), and for variants that are observed more than once in a cohort (Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>). For comparison, we have computed the expected frequency of such events, assuming that all possible 11-mers (the mutated nucleotide at the center, flanked by 5 nucleotides on each side) are equiprobable, regardless of their sequence.</p><p>Based on this equiprobable model, we have computed the probability that the number of mutations found in repeat locii is equal to or greater than the observed numbers. When considering variants appearing more than once, this probability is not significant for all studies; when unique variants are considered, those appear in repeat locii significantly more often than expected by chance in 7 out of 26 studies (TCGA-COAD, TCGA-KIRP, TCGA-LIHC, TCGA-READ, TCGA-SKCM, TCGA-TGCT &#x00026; TCGA-UCAC, significance level set to 0.05 after Benjamini-Hochberg multiple testing correction).</p></sec><sec id="Sec8"><title>Mice</title><p>ABabDII mice (described in detail in [<xref ref-type="bibr" rid="CR24">24</xref>]) have been used for this study. They are transgenic for entire human <italic>TCR</italic>- <italic>&#x003b1;</italic> and <italic>TCR</italic>- <italic>&#x003b2;</italic> gene loci, as well as for <italic>HHD</italic> molecule [<xref ref-type="bibr" rid="CR25">25</xref>] and deficient for the murine <italic>Tcr</italic>- <italic>&#x003b1;</italic> and - <italic>&#x003b2;</italic> chains, as well as for murine <italic>&#x003b2;</italic><italic>2m</italic> and <italic>H2</italic>- <italic>D</italic><sup><italic>b</italic></sup> genes. The mice used in the study were generated and housed under SPF conditions (caged enriched with bedding material, 3-5 mice/cage, standard light/dark cycle, food and water ad libitum) at the Max-Delbr&#x000fc;ck-Center animal facility. All animal experiments were approved by the Landesamt f&#x000fc;r Arbeitsschutz, Gesundheitsschutz und technische Sicherheit, Berlin, Germany.</p></sec><sec id="Sec9"><title>Generation of mutation-specific t cells in ABabDII mice</title><p>For each candidate, 3 ABabDII mice between 8 to 12 weeks old (6 in total) underwent immunisation. They were injected subcutaneously with 100 <italic>&#x003bc;</italic>g of mutant short peptide (9-10mers, JPT) supplemented with 50 <italic>&#x003bc;</italic>g CpG 1826 (TIB Molbiol), emulsified in incomplete Freund&#x02019;s adjuvant (Sigma). Repetitive immunizations were performed with the same mixture at least three weeks apart. Mutation-specific CD8 <sup>+</sup> T cells in the peripheral blood of immunized animals were assessed by intracellular cytokine staining (ICS) for IFN <italic>&#x003b3;</italic> 7 days after each boost. All 6 animals were peptide-reactive. The 6 mice were sacrificed for spleen preparation by cervical dislocation after isofluran anasthesia.</p></sec><sec id="Sec10"><title>Patient number estimates and HLA-1 frequencies</title><p>HLA-1 frequency data <italic>f</italic><sub><italic>h</italic></sub> for the U.S. population was retrieved from the Allele Frequency Net Database (AFND) [<xref ref-type="bibr" rid="CR26">26</xref>]. Frequency data were estimated by averaging the allele frequencies of multiple population datasets from the North American (NAM) geographical region. The major U.S. ethnic groups were included and sampled under the NAM category. Cancer incidence data for the U.S. population (<italic>N</italic><sub><italic>d</italic></sub>) was retrieved from the GLOBOCAN 2012 project of the International Agency for Research on Cancer, WHO [<xref ref-type="bibr" rid="CR27">27</xref>].</p><p>Assuming that the fraction of a recurrent variant in the U.S. population affected by cancer entity <italic>d</italic> (<italic>r</italic><sub><italic>d</italic></sub>) is identical to the observed ratio of that variant in the corresponding TCGA study, the number of patients of HLA-1 type <italic>h</italic> whose tumor contain the variant is expected to be
<disp-formula id="Equa"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$ n_{h} = f_{h} \sum_{d} r_{d} N_{d}. $$ \end{document}</tex-math><mml:math id="M2"><mml:mrow><mml:msub><mml:mrow><mml:mi>n</mml:mi></mml:mrow><mml:mrow><mml:mi>h</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mi>h</mml:mi></mml:mrow></mml:msub><mml:munder><mml:mrow><mml:mo mathsize="big">&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>d</mml:mi></mml:mrow></mml:munder><mml:msub><mml:mrow><mml:mi>r</mml:mi></mml:mrow><mml:mrow><mml:mi>d</mml:mi></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mi>d</mml:mi></mml:mrow></mml:msub><mml:mi>.</mml:mi></mml:mrow></mml:math><graphic xlink:href="12920_2019_611_Article_Equa.gif" position="anchor"/></alternatives></disp-formula></p><p>The summation runs over 18 diseases <italic>d</italic> for which both the TCGA projects and the cancer incidence data are available.</p></sec></sec><sec id="Sec11" sec-type="results"><title>Results</title><sec id="Sec12"><title>Recurrent variants and candidates</title><p>From the GDC repository [<xref ref-type="bibr" rid="CR16">16</xref>], we have collected somatic variants for 33 TGCA studies. After removing patients without clinical meta-data, and studies with less than 100 patients, we have selected 1,384,531 high-confidence missense SNPs from 9,641 patients, see methods for details. Using this data, 1,055 variants are deemed recurrent (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>), as they can be found in more than 1% of the patients in the respective study cohort. These recurrent variants correspond to 869 unique protein changes, as some appear in multiple cancer entities. 77 of the recurrent variants occur in at least 3% of their cohort (43 unique protein changes).</p><p>From these 869 unique protein changes, we have generated candidates that are predicted to be strong MHC class I binders in frequent HLA-1 types that we considered for initial selection. 415 (48%) of them lead to a strong binder prediction. In total, there are 772 candidates that are recurrent in a cancer entity cohort, and predicted as binding for a considered HLA-1 type. These candidates are non-redundant among all the 9-, 10- &#x00026; 11-mers containing the variant: the selection process retains only the peptide sequence with the lowest predicted IC<sub>50</sub>. Figure&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> and Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> provide an overview of the variant selection and neo-epitope candidates generation processes, while Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref> lists all neo-epitopes (weak and strong predicted binders) after removing redundancy.
<table-wrap id="Tab1"><label>Table 1</label><caption><p>Overview of the 33 TCGA studies used in this analysis</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Project name</th><th align="left" colspan="2">Number of patients</th><th align="left" colspan="2">Variants per patients</th><th align="left" colspan="2">Missense variant per patient</th><th align="left">Recurrent variants</th><th align="left">Strong binders</th></tr><tr><th align="left"/><th align="left">Total</th><th align="left">With clinical data</th><th align="left">Average</th><th align="left">Median</th><th align="left">Average</th><th align="left">Median</th><th align="left"/><th align="left"/></tr></thead><tbody><tr><td align="left">TCGA-BLCA</td><td align="left">412</td><td align="left">412</td><td align="left">326</td><td align="left">226</td><td align="left">157</td><td align="left">109</td><td align="left">22</td><td align="left">14</td></tr><tr><td align="left">TCGA-BRCA</td><td align="left">986</td><td align="left">986</td><td align="left">123</td><td align="left">62</td><td align="left">50</td><td align="left">25</td><td align="left">8</td><td align="left">10</td></tr><tr><td align="left">TCGA-CESC</td><td align="left">289</td><td align="left">289</td><td align="left">358</td><td align="left">157</td><td align="left">143</td><td align="left">62</td><td align="left">17</td><td align="left">16</td></tr><tr><td align="left">TCGA-COAD</td><td align="left">399</td><td align="left">397</td><td align="left">666</td><td align="left">176</td><td align="left">288</td><td align="left">82</td><td align="left">41</td><td align="left">34</td></tr><tr><td align="left">TCGA-ESCA</td><td align="left">184</td><td align="left">184</td><td align="left">246</td><td align="left">187</td><td align="left">95</td><td align="left">73</td><td align="left">80</td><td align="left">72</td></tr><tr><td align="left">TCGA-GBM</td><td align="left">393</td><td align="left">390</td><td align="left">212</td><td align="left">70</td><td align="left">93</td><td align="left">36</td><td align="left">15</td><td align="left">5</td></tr><tr><td align="left">TCGA-HNSC</td><td align="left">508</td><td align="left">508</td><td align="left">201</td><td align="left">139</td><td align="left">97</td><td align="left">66</td><td align="left">14</td><td align="left">3</td></tr><tr><td align="left">TCGA-KIRC</td><td align="left">336</td><td align="left">336</td><td align="left">79</td><td align="left">69</td><td align="left">33</td><td align="left">31</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">TCGA-KIRP</td><td align="left">281</td><td align="left">281</td><td align="left">85</td><td align="left">82</td><td align="left">39</td><td align="left">38</td><td align="left">5</td><td align="left">2</td></tr><tr><td align="left">TCGA-LAML</td><td align="left">143</td><td align="left">143</td><td align="left">69</td><td align="left">15</td><td align="left">16</td><td align="left">6</td><td align="left">14</td><td align="left">7</td></tr><tr><td align="left">TCGA-LGG</td><td align="left">508</td><td align="left">507</td><td align="left">70</td><td align="left">36</td><td align="left">33</td><td align="left">16</td><td align="left">14</td><td align="left">0</td></tr><tr><td align="left">TCGA-LIHC</td><td align="left">364</td><td align="left">364</td><td align="left">149</td><td align="left">120</td><td align="left">70</td><td align="left">58</td><td align="left">11</td><td align="left">16</td></tr><tr><td align="left">TCGA-LUAD</td><td align="left">567</td><td align="left">515</td><td align="left">367</td><td align="left">242</td><td align="left">180</td><td align="left">113</td><td align="left">7</td><td align="left">0</td></tr><tr><td align="left">TCGA-LUSC</td><td align="left">492</td><td align="left">492</td><td align="left">368</td><td align="left">301</td><td align="left">187</td><td align="left">153</td><td align="left">20</td><td align="left">19</td></tr><tr><td align="left">TCGA-OV</td><td align="left">436</td><td align="left">435</td><td align="left">173</td><td align="left">121</td><td align="left">58</td><td align="left">47</td><td align="left">10</td><td align="left">7</td></tr><tr><td align="left">TCGA-PAAD</td><td align="left">178</td><td align="left">178</td><td align="left">168</td><td align="left">50</td><td align="left">77</td><td align="left">19</td><td align="left">24</td><td align="left">12</td></tr><tr><td align="left">TCGA-PCPG</td><td align="left">179</td><td align="left">179</td><td align="left">13</td><td align="left">12</td><td align="left">5</td><td align="left">4</td><td align="left">8</td><td align="left">5</td></tr><tr><td align="left">TCGA-PRAD</td><td align="left">495</td><td align="left">495</td><td align="left">59</td><td align="left">35</td><td align="left">27</td><td align="left">15</td><td align="left">3</td><td align="left">7</td></tr><tr><td align="left">TCGA-READ</td><td align="left">137</td><td align="left">136</td><td align="left">475</td><td align="left">148</td><td align="left">232</td><td align="left">70</td><td align="left">320</td><td align="left">186</td></tr><tr><td align="left">TCGA-SARC</td><td align="left">237</td><td align="left">237</td><td align="left">119</td><td align="left">70</td><td align="left">45</td><td align="left">26</td><td align="left">2</td><td align="left">0</td></tr><tr><td align="left">TCGA-SKCM</td><td align="left">467</td><td align="left">467</td><td align="left">841</td><td align="left">472</td><td align="left">413</td><td align="left">229</td><td align="left">266</td><td align="left">220</td></tr><tr><td align="left">TCGA-STAD</td><td align="left">437</td><td align="left">437</td><td align="left">488</td><td align="left">157</td><td align="left">211</td><td align="left">74</td><td align="left">17</td><td align="left">14</td></tr><tr><td align="left">TCGA-TGCT</td><td align="left">144</td><td align="left">128</td><td align="left">23</td><td align="left">21</td><td align="left">9</td><td align="left">8</td><td align="left">9</td><td align="left">6</td></tr><tr><td align="left">TCGA-THCA</td><td align="left">492</td><td align="left">492</td><td align="left">22</td><td align="left">12</td><td align="left">6</td><td align="left">5</td><td align="left">4</td><td align="left">3</td></tr><tr><td align="left">TCGA-THYM</td><td align="left">123</td><td align="left">123</td><td align="left">39</td><td align="left">24</td><td align="left">10</td><td align="left">4</td><td align="left">6</td><td align="left">2</td></tr><tr><td align="left">TCGA-UCEC</td><td align="left">530</td><td align="left">530</td><td align="left">1672</td><td align="left">149</td><td align="left">708</td><td align="left">54</td><td align="left">118</td><td align="left">109</td></tr><tr><td align="left">TCGA-ACC</td><td align="left">92</td><td align="left">92</td><td align="left">117</td><td align="left">36</td><td align="left">0</td><td align="left">0</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">TCGA-CHOL</td><td align="left">51</td><td align="left">45</td><td align="left">110</td><td align="left">62</td><td align="left">0</td><td align="left">0</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">TCGA-DLBC</td><td align="left">37</td><td align="left">37</td><td align="left">173</td><td align="left">157</td><td align="left">0</td><td align="left">0</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">TCGA-KICH</td><td align="left">66</td><td align="left">66</td><td align="left">44</td><td align="left">25</td><td align="left">0</td><td align="left">0</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">TCGA-MESO</td><td align="left">82</td><td align="left">82</td><td align="left">47</td><td align="left">44</td><td align="left">0</td><td align="left">0</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">TCGA-UCS</td><td align="left">57</td><td align="left">57</td><td align="left">183</td><td align="left">67</td><td align="left">0</td><td align="left">0</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">TCGA-UVM</td><td align="left">80</td><td align="left">80</td><td align="left">23</td><td align="left">16</td><td align="left">0</td><td align="left">0</td><td align="left">0</td><td align="left">0</td></tr><tr><td align="left">Total</td><td align="left">10182</td><td align="left">10100</td><td align="right" colspan="2">Total number: 3155183</td><td align="right" colspan="2">Total number: 1384531</td><td align="left">1055</td><td align="left">769</td></tr></tbody></table><table-wrap-foot><p>The 7 studies displayed at the bottom have not been used for the determination of recurrent vairants, as the number of patients is less than 100. The number of strong binders includes all occurrences of neo-epitopes candidates, so a candidate may be counted multiple times when it is predicted to be binding several HLA-1 types</p></table-wrap-foot></table-wrap>
</p><p>Despite large differences between variant selection protocols, 123 variants deemed recurrent by the above process can be found among the 470 variants identified in the cancer hotspot datasets [<xref ref-type="bibr" rid="CR14">14</xref>] (Additional file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>). This overlap is strongly dependent on how frequent those variants are observed: there are 54 common variants out of the 61 variants observed more than 10 times over our dataset (&#x0003e;88%). Among the 819 variants retained for the comparison (see methods for details), only 5 appear among the variants flagged as possible false positive by Chang et al. (&#x0003c;1%).</p></sec><sec id="Sec13"><title>Enrichment in known cancer related genes</title><p>We observe that recurrent variants occur substantially more frequently in known cancer-related genes than in other genes (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>c). Initially approximatively one percent of all observed variants are found in genes that have been described [<xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR29">29</xref>] as oncogenes (54 genes) or tumor suppressor genes (71 genes). When recurrent unique protein changes are considered, the fraction of known oncogenes or tumor suppressor genes is substantially increased to 13% and 6.5% respectively (a <italic>&#x003c7;</italic><sup>2</sup> test between unique protein changes and unique recurrent variants gives a <italic>P</italic> value smaller than 10<sup>&#x02212;16</sup>). These fractions only marginally increase to 14% and 7% when only the unique protein changes leading to predicted strong binders for frequent HLA-1 types are considered (a <italic>&#x003c7;</italic><sup>2</sup> test between unique recurrent variants and strong binders gives a non-significant <italic>P</italic> value). Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref> shows a similar enrichment of known cancer-related genes per cohort. We observe that the enrichment is stronger for oncogenes than for tumor suppressors. This might be expected, as activating mutations in oncogenes are mainly distributed on a few protein positions, while loss of function mutations in tumor suppressors are generally distributed more broadly along the protein sequence.</p><p>It is interesting to observe that several of the highly prevalent neo-epitope candidates occur in genes that are involved in known immune escape mechanisms: RAC1:P29S is recurrent in study SKCM (melanoma), is predicted to lead to strong binding neo-epitopes for HLA-A*01:01 and HLA-A*02:01, and is reported to up-regulate PD-L1 in melanoma [<xref ref-type="bibr" rid="CR30">30</xref>]. CTNNB1:S33C is recurrent in studies LIHC (liver hepatocellular carcinoma) and UCEC (uterine corpus endometrial carcinoma), is predicted to lead to strong binding neo-epitopes for HLA-A*02:01, and has been shown to increase the expression of the Wnt-signalling pathway in hepatocellular carcinoma [<xref ref-type="bibr" rid="CR31">31</xref>], leading to modulation of the immune response [<xref ref-type="bibr" rid="CR32">32</xref>] and ultimately to tumor immune escape [<xref ref-type="bibr" rid="CR33">33</xref>]. In a separate study, Cho et al. [<xref ref-type="bibr" rid="CR34">34</xref>] show that this mutation confers acquired resistance to the drug imatinib in metastatic melanoma. Finally, FLT3:D835Y recurrent in study LAML (acute myeloid leukemia), is predicted to lead to a strong binding neo-epitope for HLA-A*01:01, HLA-A*02:01 and HLA-C*06:02, and following Reiter et al. [<xref ref-type="bibr" rid="CR35">35</xref>], Tyrosine Kinase Inhibitors promote the surface expression of the mutated FLT3, enhancing FLT3-directed immunotherapy options, as its surface expression is negatively correlated with proliferation.</p><p>While the described mechanisms are probably sufficient to explain immune escape in tumor evolution, the candidates could nevertheless be viable targets for adoptive T cell therapy or TCR gene therapy.</p></sec><sec id="Sec14"><title>Recurrent neo-epitopes in patient populations</title><p>Upon assumption of statistical independence, the product of the frequency of a recurrent variant with the frequency of class I alleles in the population and the incidence rates of cancer types provides an estimate for the number of patients that carry that specific candidate. Using the number of newly diagnosed patients per year and HLA-1 frequency in the US population, we are able to compute the expected number of patients for 18 cancer entities for which both cancer census data and a TCGA study are available. The occurrence numbers for individual candidates range from 0 to 2,254 for PIK3CA:H1047R in breast cancer patients of type HLA-C*07:01; Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref> presents a summary of expected patient numbers for the complete set of candidates. We estimate that, in the US alone, the previously discussed RAC1:P29S mutation might be present in 628 new patients carrying the HLA-A*02:01 allele each year (in 556 melanoma patients and in 72 lung small cell, head &#x00026; neck or uterine carcinomas patients, see Additional file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref> for details). For the CTNNB1:S33C mutation, the total number of HLA-A*02:01 patients in the US is expected to be 364, from uterine corpus, prostate and liver cancer types. As another example, 115 myeloid leukemia patients in the US are expected to be of type HLA-A*02:01 and carry the FLT3:D835Y mutation.
<table-wrap id="Tab2"><label>Table 2</label><caption><p>Expected number of newly diagnosed U.S. patients by HLA-1 type and cancer entity</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">A)</th><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/><th align="left"/></tr><tr><th align="left">Cancer entity</th><th align="left">Study</th><th align="left">Number of patients</th><th align="left">HLA-A*01:01 (7.61%)</th><th align="left">HLA-A*02:01 (20.36%)</th><th align="left">HLA-A*03:01 (6.60%)</th><th align="left">HLA-A*11:01 (4.37%)</th><th align="left">HLA-B*07:02 (6.51%)</th><th align="left">HLA-B*08:01 (4.80%)</th><th align="left">HLA-B*15:01 (4.46%)</th><th align="left">HLA-C*04:01 (16.69%)</th><th align="left">HLA-C*06:02 (5.72%)</th><th align="left">HLA-C*07:01 (9.28%)</th><th align="left">HLA-C*07:02 (15.39%)</th></tr></thead><tbody><tr><td align="justify">Bladder Urothelial Carcinoma</td><td align="justify">BLCA</td><td align="justify">69300</td><td align="justify">340</td><td align="justify">638</td><td align="justify">197</td><td align="justify">152</td><td align="justify">142</td><td align="justify">112</td><td align="justify">89</td><td align="justify">223</td><td align="justify">376</td><td align="justify">596</td><td align="justify">383</td></tr><tr><td align="justify">Invasive Breast Carcinoma</td><td align="justify">BRCA</td><td align="justify">204800</td><td align="justify">110</td><td align="justify">1090</td><td align="justify">461</td><td align="justify">199</td><td align="justify">108</td><td align="justify">375</td><td align="justify">120</td><td align="justify">482</td><td align="justify">1411</td><td align="justify">2305</td><td align="justify">1234</td></tr><tr><td align="justify">Cervical Squamous Cell Carcinoma</td><td align="justify">CESC</td><td align="justify">14000</td><td align="justify">58</td><td align="justify">135</td><td align="justify">16</td><td align="justify">17</td><td align="justify">13</td><td align="justify">14</td><td align="justify">23</td><td align="justify">142</td><td align="justify">27</td><td align="justify">66</td><td align="justify">152</td></tr><tr><td align="justify">Colon Adenocarcinoma</td><td align="justify">COAD</td><td align="justify">154840</td><td align="justify">861</td><td align="justify">1628</td><td align="justify">770</td><td align="justify">760</td><td align="justify">450</td><td align="justify">222</td><td align="justify">375</td><td align="justify">2961</td><td align="justify">1019</td><td align="justify">1620</td><td align="justify">1800</td></tr><tr><td align="justify">Esophageal Adenocarcinoma</td><td align="justify">ESCA</td><td align="justify">4750</td><td align="justify">23</td><td align="justify">151</td><td align="justify">26</td><td align="justify">16</td><td align="justify">20</td><td align="justify">5</td><td align="justify">13</td><td align="justify">101</td><td align="justify">23</td><td align="justify">26</td><td align="justify">83</td></tr><tr><td align="justify">Glioblastoma Multiforme</td><td align="justify">GBM</td><td align="justify">3204</td><td align="justify">7</td><td align="justify">17</td><td align="justify">3</td><td align="justify">7</td><td align="justify">0</td><td align="justify">0</td><td align="justify">3</td><td align="justify">8</td><td align="justify">5</td><td align="justify">3</td><td align="justify">3</td></tr><tr><td align="justify">Head &#x00026; Neck Squamous Cell Carcinoma</td><td align="justify">HNSC</td><td align="justify">58000</td><td align="justify">43</td><td align="justify">208</td><td align="justify">112</td><td align="justify">45</td><td align="justify">81</td><td align="justify">11</td><td align="justify">81</td><td align="justify">301</td><td align="justify">136</td><td align="justify">220</td><td align="justify">175</td></tr><tr><td align="justify">Renal Clear Cell Carcinoma</td><td align="justify">KIRC</td><td align="justify">57600</td><td align="justify">13</td><td align="justify">70</td><td align="justify">23</td><td align="justify">0</td><td align="justify">0</td><td align="justify">8</td><td align="justify">0</td><td align="justify">0</td><td align="justify">10</td><td align="justify">0</td><td align="justify">26</td></tr><tr><td align="justify">Papilliary Renal Cell Carcinoma</td><td align="justify">KIRP</td><td align="justify">8064</td><td align="justify">0</td><td align="justify">6</td><td align="justify">0</td><td align="justify">3</td><td align="justify">0</td><td align="justify">0</td><td align="justify">0</td><td align="justify">29</td><td align="justify">0</td><td align="justify">0</td><td align="justify">0</td></tr><tr><td align="justify">Acute Myeloid Leukemia</td><td align="justify">LAML</td><td align="justify">13500</td><td align="justify">77</td><td align="justify">115</td><td align="justify">0</td><td align="justify">8</td><td align="justify">49</td><td align="justify">0</td><td align="justify">0</td><td align="justify">0</td><td align="justify">48</td><td align="justify">9</td><td align="justify">73</td></tr><tr><td align="justify">Hepatocellular Carcinoma</td><td align="justify">LIHC</td><td align="justify">29700</td><td align="justify">12</td><td align="justify">496</td><td align="justify">131</td><td align="justify">84</td><td align="justify">69</td><td align="justify">12</td><td align="justify">7</td><td align="justify">68</td><td align="justify">69</td><td align="justify">112</td><td align="justify">174</td></tr><tr><td align="justify">Lung Squamous Cell Carcinoma</td><td align="justify">LUSC</td><td align="justify">66000</td><td align="justify">181</td><td align="justify">642</td><td align="justify">328</td><td align="justify">168</td><td align="justify">282</td><td align="justify">13</td><td align="justify">77</td><td align="justify">593</td><td align="justify">151</td><td align="justify">172</td><td align="justify">366</td></tr><tr><td align="justify">Serous Ovarian Cancer</td><td align="justify">OV</td><td align="justify">16800</td><td align="justify">26</td><td align="justify">24</td><td align="justify">33</td><td align="justify">10</td><td align="justify">15</td><td align="justify">2</td><td align="justify">14</td><td align="justify">39</td><td align="justify">9</td><td align="justify">14</td><td align="justify">36</td></tr><tr><td align="justify">Prostate Adenocarcinoma</td><td align="justify">PRAD</td><td align="justify">260000</td><td align="justify">120</td><td align="justify">852</td><td align="justify">69</td><td align="justify">69</td><td align="justify">34</td><td align="justify">50</td><td align="justify">70</td><td align="justify">175</td><td align="justify">387</td><td align="justify">628</td><td align="justify">1202</td></tr><tr><td align="justify">Melanoma</td><td align="justify">SKCM</td><td align="justify">75000</td><td align="justify">2649</td><td align="justify">7890</td><td align="justify">1817</td><td align="justify">936</td><td align="justify">861</td><td align="justify">530</td><td align="justify">203</td><td align="justify">2186</td><td align="justify">438</td><td align="justify">1000</td><td align="justify">2457</td></tr><tr><td align="justify">Stomach Adenocarcinoma</td><td align="justify">STAD</td><td align="justify">25000</td><td align="justify">47</td><td align="justify">172</td><td align="justify">56</td><td align="justify">66</td><td align="justify">26</td><td align="justify">8</td><td align="justify">30</td><td align="justify">206</td><td align="justify">114</td><td align="justify">166</td><td align="justify">130</td></tr><tr><td align="justify">Thyroid Cancer</td><td align="justify">THCA</td><td align="justify">46400</td><td align="justify">394</td><td align="justify">0</td><td align="justify">0</td><td align="justify">0</td><td align="justify">0</td><td align="justify">14</td><td align="justify">0</td><td align="justify">0</td><td align="justify">0</td><td align="justify">0</td><td align="justify">44</td></tr><tr><td align="justify">Endometrial Carcinoma</td><td align="justify">UCEC</td><td align="justify">55000</td><td align="justify">942</td><td align="justify">2804</td><td align="justify">817</td><td align="justify">501</td><td align="justify">369</td><td align="justify">290</td><td align="justify">493</td><td align="justify">2222</td><td align="justify">856</td><td align="justify">1602</td><td align="justify">1779</td></tr><tr><td align="justify">Total</td><td align="justify"/><td align="justify">1161958</td><td align="justify">5904</td><td align="justify">16936</td><td align="justify">4858</td><td align="justify">3033</td><td align="justify">2517</td><td align="justify">1666</td><td align="justify">1598</td><td align="justify">9736</td><td align="justify">5080</td><td align="justify">8539</td><td align="justify">10116</td></tr><tr><td align="justify">B)</td><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/><td align="justify"/></tr><tr><td align="justify">Number of candidates in diseases</td><td align="justify"/><td align="justify"/><td align="justify">55</td><td align="justify">91</td><td align="justify">68</td><td align="justify">64</td><td align="justify">33</td><td align="justify">24</td><td align="justify">24</td><td align="justify">48</td><td align="justify">48</td><td align="justify">50</td><td align="justify">55</td></tr></tbody></table><table-wrap-foot><p>A) Expected number of patients of a given HLA-1 type who harbor at least one potentially immunogenic neo-epitope candidate for that HLA-1 type. Both the cancer incidence and the allele frequency are estimated for the U.S. population. The probability that a patient carries at least one variant from the set of neo-epitope candidates is computed under the assumption that the occurrence of variants in a cancer patient stems from statistically independent events. B) Number of neo-epitope candidates identified in the 18 studies shown in A, which are predicted to be strong binders to the corresponding HLA-1 type</p></table-wrap-foot></table-wrap>
</p><p>Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> shows the cumulative expected number of patients that carry a specific epitope, and with matching HLA-1 type, for the 50 candidates with the highest expected patients number. The number of patients is derived from the sum over all cancer entities, including those in which the candidate is not recurrent according to our criteria. For example, among newly diagnosed US patients of type HLA-C*04:01, 88 prostate cancer patients are expected to carry the mutation PIK3CA:R88Q, even though its observed frequency in the PRAD study is as low as 0.2%. The data shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> can be found in Additional file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>.
<fig id="Fig2"><label>Fig. 2</label><caption><p>50 most frequent candidates in patients for which strong MHC I binding is predicted. For each candidate, the expected number of patients is obtained by summing over the 18 cancer entities for which the number of newly diagnosed patients in the US is available, and for which a corresponding TCGA study has been included in our analysis</p></caption><graphic xlink:href="12920_2019_611_Fig2_HTML" id="MO2"/></fig>
</p></sec><sec id="Sec15"><title>Accessible patient population</title><p>As our current understanding of peptide immunogenicity is still incomplete [<xref ref-type="bibr" rid="CR36">36</xref>], not all candidates predicted by our pipeline can be expected to trigger an immunogenic response in patients. To further evaluate the usefulness of our results we consider the list of candidates (neoepitope and HLA type pairs) selected form our ranking. Assuming a T cell therapy could be generated for every candidate we can compute the number of patients that would benefit, see methods. Because of imperfections in candidate prediction, not all candidates hold the potential for an effective T cell therapy, and these ineffective candidates can be thus viewed as &#x0201c;false positives&#x0201d;. Because it is impossible to create a reliable estimated for the fraction of these false positives due to the complexity of the underlying algorithm and biological process we decided to consider a broad range of possible values from 50% to 95%, cf. Figure&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>. Using a subset of 6868 patients for whom HLA types were known, we predict the number of patients for whom such positive response might be expected, as a function of the proportion of &#x0201c;false positives&#x0201d; in our candidates. To estimate the impact of such &#x0201c;false positives&#x0201d;, we have randomly flagged 1000 times 337, 539, 607 &#x00026; 640 candidates as &#x0201c;false positives&#x0201d;, which is corresponding to a fraction of about 50%, 80%, 90% and 95% of the total 674 candidates. This procedure left us with 1000 sets of 337, 135, 67 &#x00026; 34 candidates that were not flagged as &#x0201c;false positives&#x0201d;. Figure&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> shows that for a pessimistic 90% of false positive candidates, more than 1.5% of patients over all cancer entities (95% CI between 1.25% &#x00026; 2.65%, mean 1.78%, median 1.72%, both corresponding to about 20000 new patients per year in the U.S.) are still expected to carry at least one of the 67 remaining candidates&#x02019; mutation and corresponding HLA allele. While the proportions are modest, the absolute number of patients seems relevant. The figure in Additional file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref> shows that there are considerable differences between entities: the proportion of matching patients is much higher in diseases with high mutational load such as melanomas (TCGA-SKCM, median about 9% for 90% false positives), than in diseases with lower mutational load, such as thyroid cancer (TCGA-THCA, 0.2%, 90% false positives).
<fig id="Fig3"><label>Fig. 3</label><caption><p>Expected influence of the proportion of false positive neo-epitope candidates on the patient population. Proportion of the patients that carry at least one neo-epitope candidate mutation, and whose HLA-I allele set contains the candidate HLA type, when a limited percentage of the neo-epitope candidates is considered. The patient cohort considered here consists of 6868 patients from the 18 TCGA cohorts for whom the HLA types are known. For each false positive proportion, the false positive candidates have been selected 1000 times at random</p></caption><graphic xlink:href="12920_2019_611_Fig3_HTML" id="MO3"/></fig>
</p></sec><sec id="Sec16"><title>Confirmational evidence</title><p>A limited validation of our method was performed in two steps: first, we confirmed that our pipeline was able to identify candidates that have been previously reported as eliciting spontaneous CD8 <sup>+</sup> T-cell responses in cancer patients in whom the target epitopes were subsequently discovered [<xref ref-type="bibr" rid="CR37">37</xref>, <xref ref-type="bibr" rid="CR38">38</xref>]. Both sets together (Additional file&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref>) contain 37 epitopes, 35 of which could be mapped to an ENSEMBL transcript (33 unique genes). For 27 of these epitopes our pipeline predicted strong binding with the specific HLA-1 type reported in the corresponding wet-lab investigations. Another 5 epitopes where predicted as weak binders, some of the latter are also predicted to be strong binders in other HLA-1 types. Our pipeline classified 70% of a set of known tumor neo-antigens as strong binders and another 14% as weak binders.</p><p>4 out of 34 unique identifiable variants studied by van Buuren et al. [<xref ref-type="bibr" rid="CR38">38</xref>] and Fritsch [<xref ref-type="bibr" rid="CR37">37</xref>] are found among our set of high confidence missense variants, but only one (CTNNB1:S37F) fulfills the 1% recurrence threshold (9 uterine carcinoma patients). This variant was shown to trigger immunological response against HLA-A*24:02 [<xref ref-type="bibr" rid="CR39">39</xref>], which isn&#x02019;t in the set of alleles that we have systematically tested. However, our prediction show that the same peptide might also be reactive against HLA-C07:02.</p><p>Finally, the CDK4:R24C peptide (sequence ACDPHSGHFV, see Additional file&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref>) is not predicted to bind to HLA-A*02:01, even though it leads to confirmed T cell response [<xref ref-type="bibr" rid="CR40">40</xref>], and has been related to cutaneous malignant melanoma and hereditary cutaneous melanoma [<xref ref-type="bibr" rid="CR41">41</xref>], [<xref ref-type="bibr" rid="CR42">42</xref>]. Taken together, these results show that our candidate prediction pipeline is able to recapitulate most clinically validated neo-epitopes reported in [<xref ref-type="bibr" rid="CR38">38</xref>] and [<xref ref-type="bibr" rid="CR37">37</xref>], and that some of these neo-epitopes occur from recurrent variants.</p><p>We have also performed preliminary validation for two candidates: RAC1:P29S &#x00026; TRRAP:S722F binding to HLA-A*02:01 (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). We utilized ABabDII mice, transgenic animals that harbour the human TCR <italic>&#x003b1;</italic><italic>&#x003b2;</italic> gene loci, a chimeric HLA-A2 gene and are deficient for mouse TCR <italic>&#x003b1;</italic><italic>&#x003b2;</italic> and mouse MHC I genes. These mice have been shown to express a diverse human TCR repertoire [<xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR43">43</xref>] and thus mimic human T cell response. They were immunized at least twice with mutant peptides and IFN <italic>&#x003b3;</italic> producing CD8 <sup>+</sup> T cells were monitored in <italic>ex vivo</italic> ICS analysis 7 days after the last immunization. CD8 <sup>+</sup> T cells were purified from spleen cell cultures of reactive mice using either IFN <italic>&#x003b3;</italic>-capture or tetramer-guided FACSort. Sequencing of specific TCR <italic>&#x003b1;</italic> and <italic>&#x003b2;</italic> chain amplicons that were obtained by RACE-PCR revealed that this procedure yields an almost monoclonal CD8 <sup>+</sup> T cell population (not shown). In both cases, tested neo-antigen candidates lead to T cell reactivity, confirming not only predicted MHC binding by our pipeline but also immunogenicity in vivo in human TCR transgenic mice. Therefore this workflow also allows to generate potentially therapeutic relevant TCRs to be used in the clinics for cancer immunotherapy.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Recognition of predicted epitopes by CD8 <sup>+</sup> T cells. Epitopes for recurrent mutations that have been identified <italic>in silico</italic> to bind to HLA-A*02:01 using our pipeline were synthesized and used for immunization of human TCR transgenic ABabDII mice. Examples (RAC1:P29S and TRRAP:S722F) of <italic>ex vivo</italic> ICS analysis of mutant peptide immunized ABabDII mice 7 days after the last immunization are shown. Polyclonal stimulation with CD3/CD28 dynabeads was used as positive control, stimulation with an irrelevant peptide served as negative control (data not shown)</p></caption><graphic xlink:href="12920_2019_611_Fig4_HTML" id="MO4"/></fig>
</p></sec></sec><sec id="Sec17" sec-type="discussion"><title>Discussion</title><p>By virtue of the underlying mutational processes, the genome architecture and accessibility as well as for functional reasons within the disease process, certain somatic mutations will be present in multiple patients while still being highly specific to the tumor [<xref ref-type="bibr" rid="CR14">14</xref>]. Using existing cancer studies and neo-epitope binding predictions to MHC class I proteins, we propose a ranking of candidates which mutation occur frequently in observed cancer patient cohorts. The candidates are ranked according to the expected number of target patients. For one candidate, the target patients are defined as those who bear the candidate&#x02019;s mutation, and whose HLA types contain the candidate&#x02019;s. The expected number of target patients is proportional to the HLA type frequency in the population, and to the frequency of the mutation in the cancer cohorts. Taking into account the fact that MHC binding is a necessary but not sufficient condition for T cell activity, and the limitations of MHC binding prediction algorithms, our method provides an objective ranking of neo-epitopes based on recurrent variants, as a basis for the development of off-the-shelf immunotherapy treatments.</p><p>Despite numerous mechanisms of immune evasion, neo-epitopes are important targets of endogenous immunity [<xref ref-type="bibr" rid="CR5">5</xref>]. In some cases at least, it has been shown that they contribute to tumor recognition [<xref ref-type="bibr" rid="CR44">44</xref>], achieve high objective response (in melanoma, see ref. [<xref ref-type="bibr" rid="CR45">45</xref>, <xref ref-type="bibr" rid="CR46">46</xref>]), and a single of them is presumably sufficient for tumor regression [<xref ref-type="bibr" rid="CR47">47</xref>]. Moreover, positive association has been shown between antigen load and cytolytic activity [<xref ref-type="bibr" rid="CR48">48</xref>], activated T cells [<xref ref-type="bibr" rid="CR13">13</xref>] and high levels of the PD-1 ligand [<xref ref-type="bibr" rid="CR49">49</xref>]. Taken together, these results suggest that neo-epitopes occupy a central role in regulating immune response to cancer, and that this role can be exploited for cancer immunotherapy. Even though the question of negative selection for strong binding neo-epitopes and its relation to other immune evasion mechanisms like HLA loss or PD-L1, CTLA4 dis-regulation is still open [<xref ref-type="bibr" rid="CR50">50</xref>]. A recent CRISPR screen suggest that more then 500 genes are essential for cancer immunotherapy [<xref ref-type="bibr" rid="CR51">51</xref>].</p><p>Targeting neo-epitopes based on non-recurrent, <italic>private</italic> somatic variants requires generation of private TCRs or CARs for each individual patient, which is challenging [<xref ref-type="bibr" rid="CR52">52</xref>]. Successful treatments based on genetically engineered lymphocytes has been shown for epitopes arising from unmutated proteins, i.e. <italic>public epitopes</italic>: MART-1 and gp100 proteins have been targeted in melanoma cases [<xref ref-type="bibr" rid="CR53">53</xref>]. In another trial, Robbins et al. [<xref ref-type="bibr" rid="CR54">54</xref>] have studied long-term follow-up of patients who were treated with TCR-transduced T cells against NY-ESO-1, a protein whose expression is normally restricted to testis, but which is frequently aberrantly expressed in tumor cells. They show that treatment may be effective for some patients. These results show that immune treatments based on <italic>public</italic> variants can be beneficial, suggesting that similar success may potentially be achieved using candidates based on recurrent variants.</p><p>However, targeting such non somatic epitopes presents safety and efficacy concerns [<xref ref-type="bibr" rid="CR2">2</xref>]. The administration of T cells transduced with MART-1 specific T-cell receptor have led to fatal outcomes [<xref ref-type="bibr" rid="CR55">55</xref>]. Cross-reactivity of TCR against MAGE-A3 (a protein normally restricted to testis and placenta) caused cardiovascular toxicity [<xref ref-type="bibr" rid="CR56">56</xref>]. Neo-epitopes based on recurrent somatic variants potentially alleviate such problems, as the target sequences are truly restricted to tumor cells.</p><p>Our computation of expected targetable patient groups assumes that neither the cancer type nor the patient&#x02019;s mutanome are associated with the patient&#x02019;s HLA-1 alleles. In a recent study, Van den Eyden et al. [<xref ref-type="bibr" rid="CR50">50</xref>] show that there is little (if any) antigen depletion due to the negative selection pressure from the immune response. Molecular evolution methods applied to somatic mutations show that nearly all mutations escape negative selection [<xref ref-type="bibr" rid="CR57">57</xref>]. Taken together, these results suggest that the expected probability of a recurrent variant being present in a patient somatic mutations pool should not be affected (significantly) by the patient&#x02019;s HLA-1 alleles.</p><p>The neo-epitope landscape is diverse and sparse [<xref ref-type="bibr" rid="CR13">13</xref>]. Few neo-epitopes are predicted to be both strong binders and present in multiple patients. In their analysis, Hartmaier et al. [<xref ref-type="bibr" rid="CR58">58</xref>] estimate that neo-epitopes suitable for precision immuno-therapy might be relevant for about 0.3% of the patients, which is in agreement with our results. However, the absolute number of patients is still considerable, see Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>. Our study shows that a relatively large number of patients (about 1% of newly diagnosed patients) might benefit from a small library of candidates proven to generate immunological response. These numbers must be compared to &#x0201c;conventional&#x0201d; personalised immunotherapy, where a immunologically active candidate must be identified for each new patient for which efficacy and safety are always unknown. Even if a substantial part of the neo-epitopes we suggest turns out to be false positives due to the limitation of prediction algorithms and understanding of immune response, there is potential to help tens of thousands of patients.</p></sec><sec id="Sec18" sec-type="conclusion"><title>Conclusions</title><p>Off the shelf immune treatments can be faster, less costly and safer for individual patients, because each neo-epitope based treatment scheme can be reused on hundreds of patients per year. In this respect, they might open the way to supplement existing personalized cancer immune treatments approaches with precision treatment options.</p><p>We believe that our ranking provides a rational order for testing for and selecting off the shelf neo-epitope based therapies. Our preliminary in vivo mouse experiments show that this in principle feasible.</p></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec19"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12920_2019_611_MOESM1_ESM.xlsx"><caption><p><bold>Additional file 1</bold> 1055 recurrent variants identified in 26 TCGA studies. For each variant, the number of cases harboring the variant (Number of occurrences), the cohort size and the fraction of cases in the cohort (Fraction) are given. When available, COSMIC entries (from ENSEMBL) are are also listed, as well as the highest allele frequency from all populations quoted in ExAC version 0.31 ([<xref ref-type="bibr" rid="CR18">18</xref>]). Gene annotations from Vogelstein et al. ([<xref ref-type="bibr" rid="CR28">28</xref>]) &#x00026; Rubio-Perez et al. ([<xref ref-type="bibr" rid="CR29">29</xref>]) are also provided.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12920_2019_611_MOESM2_ESM.xlsx"><caption><p><bold>Additional file 2</bold> Neo-epitope candidates from recurrent variants. Recurrent variants leading to binding (strong and weak binders) neo-epitopes for one of the 11 HLA types considered. Peptide length redundancy has been removed from the variant list, and each variant is listed only once, even if it is recurrent in multiple study cohorts.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12920_2019_611_MOESM3_ESM.pdf"><caption><p><bold>Additional file 3</bold> Frequency of Single Nucleotides Variants (SNVs) that fall in a poly-A, poly-C, poly-G or poly-T sequence of length at least 6. The variants that appear only once in the whole study are colored in blue, while the variants that appear more than once are colored in red. The dotted line shows the expected fraction of such variants, if the sequences were all random. Except for the LIHC study, all variants that occur more than once in the cohort are found in difficult-to-sequence regions less than expected by chance.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12920_2019_611_MOESM4_ESM.pdf"><caption><p><bold>Additional file 4</bold> Overlap between recurrent variants and hotspot variants. The overlap is based on the codon position, so that all variants occurring at the same protein sequence position are pooled together. The recurrent variants that match the alternate codon definition in Chang et al. are added to the overlap. The recurrent variants are pooled by codon and sorted by decreasing occurrence frequency in the study. The overlap between hotspots and highly recurrent variants is high, and the common variants fraction decreases when recurrent variants become less frequent. The overlap between recurrent variants and the list of suspected false positive hotspots compiled by Chang et al. ([<xref ref-type="bibr" rid="CR14">14</xref>]) is very limited. Inset: Venn diagram of the total overlap between the recurrent variants called in this study, and the hotspot variants described in Chang et al. ([<xref ref-type="bibr" rid="CR14">14</xref>]).</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12920_2019_611_MOESM5_ESM.xlsx"><caption><p><bold>Additional file 5</bold> Oncogenes and tumor suppressors. Number of variants occurring in genes classified as tumor suppressor genes and oncongenes by ([<xref ref-type="bibr" rid="CR28">28</xref>]), for each study. The numbers are given for the full set of variants, among recurrent variants only and among variants leading to neo-epitope candidates. As each protein change is considered only once, the total number of variants is always smaller or equal to the sum over all studies, as protein changes appearing in multiple studies are counted only once in the total.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="12920_2019_611_MOESM6_ESM.xlsx"><caption><p><bold>Additional file 6</bold> Expected number of target patients for each neo-epitope candidates, for the 18 cancer entities with associated epidemiological data. The expected number of patients is the product between the number of new cases, the observed variant frequency and the HLA type frequency in the US population. The total expected number of patients for each candidate is the sum over the expected number of candidates by study.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="12920_2019_611_MOESM7_ESM.pdf"><caption><p><bold>Additional file 7</bold> Expected frequency of patients with at least one candidate not labelled as false positive. For each TCGA cohort, we have selected at random 1000 times 50%, 20%, 10% and 5% from the candidates, to conservately model a high rate of false positive within the candidates. From these selected candidates, we have computed the expected frequency of patients with a HLA-1 allele and a mutation matching at least one selected candidate.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="12920_2019_611_MOESM8_ESM.xlsx"><caption><p><bold>Additional file 8</bold> Confirmation Status with Gold Standard Data Set. The protein changes described in van Buuren et al. ([<xref ref-type="bibr" rid="CR38">38</xref>]) and Fritsch et al. ([<xref ref-type="bibr" rid="CR37">37</xref>]) have been mapped to the ENSEMBL protein set and neo-epitopes have been computed using our standard pipeline. 26 of these epitopes are exactly recovered by the pipeline, for one of them the pipeline predicts a strong binder for a shorter peptide, and 5 of them are predicted to be weakly binding. Column 7 to 10 are copied from ([<xref ref-type="bibr" rid="CR37">37</xref>]) and ([<xref ref-type="bibr" rid="CR38">38</xref>]).</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="12920_2019_611_MOESM9_ESM.docx"><caption><p><bold>Additional file 9</bold> ARRIVE checklist concerning the animals used for the experimental validation of the in vivo presentation of two peptides.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term><sup><italic>a</italic></sup></term><def><p>SLC: small cell lung cancer, NSLC: non-small-cell lung cancer</p></def></def-item><def-item><term><sup><italic>b</italic></sup></term><def><p>Parent and mutant sequences have been exchanged in Fritsch et al. ([<xref ref-type="bibr" rid="CR37">37</xref>])</p></def></def-item><def-item><term><sup><italic>c</italic></sup></term><def><p>A strong binder is found for the shorter mutant peptide KINKNPKYK</p></def></def-item><def-item><term><sup><italic>d</italic></sup></term><def><p>No exact match found by alignment against not redundant human proteins, not found in manual inspection of proteins P19971 and E5KRG5 from gene TYMP</p></def></def-item><def-item><term><sup><italic>e</italic></sup></term><def><p>Found by alignment against non-redundant human proteins in EAW69514.1 (melanoma associated antigen (mutated) 1, isoform CRA_e (not in ENSEMBL peptides))</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s12920-019-0611-7.</p></sec><ack><title>Acknowledgements</title><p>The results presented here are based upon data generated by the TCGA Research Network (<ext-link ext-link-type="uri" xlink:href="http://cancergenome.nih.gov">http://cancergenome.nih.gov</ext-link>).</p></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>D.B. conceived and designed the project. E.B. analysed the somatic variant data. E.B., M.H. &#x00026; C.M. generated neo-epitope candidates, analysed by E.B. G.W. performed in vivo validations, and A.D. &#x00026; E.B. performed the epidemiological analysis. D.B, T.B., G.W. &#x00026; E.B. contributed to the interpretation of results, and wrote the manuscript. All authors read and approved the final manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>Partially supported by Deutsche Forschungsgemeinschaft (SFB-TR36; T.B., G.W.), Deutsche Krebshilfe (111546; G.W., T.B.) and the Berlin Institute of Health (CRG-1; T.B., A.D.). The funding bodies played no role in the design of the study and collection, analysis, and interpretation of data and in writing the manuscript.</p></notes><notes><title>Ethics approval and consent to participate</title><p>All animal experiments were performed according to institutional and national guidelines and regulations. The experiments were approved by the governmental authority (Landesamt f&#x000fc;r Gesundheit und Soziales, Berlin).</p></notes><notes><title>Consent for publication</title><p>Not applicable</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p>The authors declare the following competing interests: in 2013, the Max-Delbr&#x000fc;ck Center (MDC) (T.B. &#x00026; G.W.) has filed a patent on mutation-specific TCRs (US20150307585A1).</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schumacher</surname><given-names>TN</given-names></name><name><surname>Schreiber</surname><given-names>RD</given-names></name></person-group><article-title>Neoantigens in cancer immunotherapy</article-title><source>Sci (NY)</source><year>2015</year><volume>348</volume><issue>6230</issue><fpage>69</fpage><lpage>74</lpage><pub-id pub-id-type="doi">10.1126/science.aaa4971</pub-id></element-citation></ref><ref id="CR2"><label>2</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Blankenstein</surname><given-names>T</given-names></name><name><surname>Leisegang</surname><given-names>M</given-names></name><name><surname>Uckert</surname><given-names>W</given-names></name><name><surname>Schreiber</surname><given-names>H</given-names></name></person-group><article-title>Targeting cancer-specific mutations by T cell receptor gene therapy</article-title><source>Curr Opin Immun</source><year>2015</year><volume>33</volume><fpage>112</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1016/j.coi.2015.02.005</pub-id></element-citation></ref><ref id="CR3"><label>3</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rosenberg</surname><given-names>SA</given-names></name><name><surname>Restifo</surname><given-names>NP</given-names></name></person-group><article-title>Adoptive cell transfer as personalized immunotherapy for human cancer</article-title><source>Sci (NY)</source><year>2015</year><volume>348</volume><issue>6230</issue><fpage>62</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1126/science.aaa4967</pub-id></element-citation></ref><ref id="CR4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wirth</surname><given-names>TC</given-names></name><name><surname>K&#x000fc;hnel</surname><given-names>F</given-names></name></person-group><article-title>Neoantigen Targeting - Dawn of a New Era in Cancer Immunotherapy?</article-title><source>Front Immun</source><year>2017</year><volume>8</volume><fpage>1848</fpage><pub-id pub-id-type="doi">10.3389/fimmu.2017.01848</pub-id></element-citation></ref><ref id="CR5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bethune</surname><given-names>MT</given-names></name><name><surname>Joglekar</surname><given-names>AV</given-names></name></person-group><article-title>Personalized T cell-mediated cancer immunotherapy: progress and challenges</article-title><source>Curr Opin Biotechnol</source><year>2017</year><volume>48</volume><fpage>142</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1016/j.copbio.2017.03.024</pub-id><pub-id pub-id-type="pmid">28494274</pub-id></element-citation></ref><ref id="CR6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kahles</surname><given-names>A</given-names></name><name><surname>Lehmann</surname><given-names>K-V</given-names></name><name><surname>Toussaint</surname><given-names>NC</given-names></name><name><surname>H&#x000fc;ser</surname><given-names>M</given-names></name><name><surname>Stark</surname><given-names>SG</given-names></name><name><surname>Sachsenberg</surname><given-names>T</given-names></name><name><surname>Stegle</surname><given-names>O</given-names></name><name><surname>Kohlbacher</surname><given-names>O</given-names></name><name><surname>Sander</surname><given-names>C</given-names></name><collab>Cancer Genome Atlas Research Network</collab><name><surname>R&#x000e4;tsch</surname><given-names>G</given-names></name></person-group><article-title>Comprehensive Analysis of Alternative Splicing Across Tumors from 8,705 Patients</article-title><source>Canc cell</source><year>2018</year><volume>34</volume><issue>2</issue><fpage>211</fpage><lpage>2246</lpage><pub-id pub-id-type="doi">10.1016/j.ccell.2018.07.001</pub-id></element-citation></ref><ref id="CR7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Laumont</surname><given-names>CM</given-names></name><name><surname>Vincent</surname><given-names>K</given-names></name><name><surname>Hesnard</surname><given-names>L</given-names></name><name><surname>Audemard</surname><given-names>&#x000c9;,</given-names></name><name><surname>Bonneil</surname><given-names>&#x000c9;,</given-names></name><name><surname>Laverdure</surname><given-names>J-P</given-names></name><name><surname>Gendron</surname><given-names>P</given-names></name><name><surname>Courcelles</surname><given-names>M</given-names></name><name><surname>Hardy</surname><given-names>M-P</given-names></name><name><surname>C&#x000f4;t&#x000e9;</surname><given-names>C</given-names></name><name><surname>Durette</surname><given-names>C</given-names></name><name><surname>St-Pierre</surname><given-names>C</given-names></name><name><surname>Benhammadi</surname><given-names>M</given-names></name><name><surname>Lanoix</surname><given-names>J</given-names></name><name><surname>Vobecky</surname><given-names>S</given-names></name><name><surname>Haddad</surname><given-names>E</given-names></name><name><surname>Lemieux</surname><given-names>S</given-names></name><name><surname>Thibault</surname><given-names>P</given-names></name><name><surname>Perreault</surname><given-names>C</given-names></name></person-group><article-title>Noncoding regions are the main source of targetable tumor-specific antigens</article-title><source>Sci Transl Med</source><year>2018</year><volume>10</volume><issue>470</issue><fpage>5516</fpage><pub-id pub-id-type="doi">10.1126/scitranslmed.aau5516</pub-id></element-citation></ref><ref id="CR8"><label>8</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>XS</given-names></name><name><surname>Mardis</surname><given-names>ER</given-names></name></person-group><article-title>Applications of Immunogenomics to Cancer</article-title><source>Cell</source><year>2017</year><volume>168</volume><issue>4</issue><fpage>600</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2017.01.014</pub-id><?supplied-pmid 28187283?><pub-id pub-id-type="pmid">28187283</pub-id></element-citation></ref><ref id="CR9"><label>9</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yarchoan</surname><given-names>M</given-names></name><name><surname>Johnson</surname><given-names>BA</given-names></name><name><surname>Lutz</surname><given-names>ER</given-names></name><name><surname>Laheru</surname><given-names>DA</given-names></name><name><surname>Jaffee</surname><given-names>EM</given-names></name></person-group><article-title>Targeting neoantigens to augment antitumour immunity</article-title><source>Nature Rev Canc</source><year>2017</year><volume>17</volume><issue>4</issue><fpage>209</fpage><lpage>22</lpage><pub-id pub-id-type="doi">10.1038/nrc.2016.154</pub-id></element-citation></ref><ref id="CR10"><label>10</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Luo</surname><given-names>H</given-names></name><name><surname>Ye</surname><given-names>H</given-names></name><name><surname>Ng</surname><given-names>HW</given-names></name><name><surname>Shi</surname><given-names>L</given-names></name><name><surname>Tong</surname><given-names>W</given-names></name><name><surname>Mendrick</surname><given-names>DL</given-names></name><name><surname>Hong</surname><given-names>H</given-names></name></person-group><article-title>Machine Learning Methods for Predicting HLA-Peptide Binding Activity</article-title><source>Bioinforma Biol Insights</source><year>2015</year><volume>9</volume><issue>Suppl 3</issue><fpage>21</fpage><lpage>9</lpage></element-citation></ref><ref id="CR11"><label>11</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gfeller</surname><given-names>D</given-names></name><name><surname>Bassani-Sternberg</surname><given-names>M</given-names></name><name><surname>Schmidt</surname><given-names>J</given-names></name><name><surname>Luescher</surname><given-names>IF</given-names></name></person-group><article-title>Current tools for predicting cancer-specific T cell immunity</article-title><source>Oncoimmunology</source><year>2016</year><volume>5</volume><issue>7</issue><fpage>1177691</fpage><pub-id pub-id-type="doi">10.1080/2162402X.2016.1177691</pub-id></element-citation></ref><ref id="CR12"><label>12</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hundal</surname><given-names>J</given-names></name><name><surname>Kiwala</surname><given-names>S</given-names></name><name><surname>Feng</surname><given-names>Y-Y</given-names></name><name><surname>Liu</surname><given-names>CJ</given-names></name><name><surname>Govindan</surname><given-names>R</given-names></name><name><surname>Chapman</surname><given-names>WC</given-names></name><name><surname>Uppaluri</surname><given-names>R</given-names></name><name><surname>Swamidass</surname><given-names>SJ</given-names></name><name><surname>Griffith</surname><given-names>OL</given-names></name><name><surname>Mardis</surname><given-names>ER</given-names></name><name><surname>Griffith</surname><given-names>M</given-names></name></person-group><article-title>Accounting for proximal variants improves neoantigen prediction</article-title><source>Nature Genet</source><year>2019</year><volume>51</volume><issue>1</issue><fpage>175</fpage><lpage>179</lpage><pub-id pub-id-type="doi">10.1038/s41588-018-0283-9</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Charoentong</surname><given-names>P</given-names></name><name><surname>Finotello</surname><given-names>F</given-names></name><name><surname>Angelova</surname><given-names>M</given-names></name><name><surname>Mayer</surname><given-names>C</given-names></name><name><surname>Efremova</surname><given-names>M</given-names></name><name><surname>Rieder</surname><given-names>D</given-names></name><name><surname>Hackl</surname><given-names>H</given-names></name><name><surname>Trajanoski</surname><given-names>Z</given-names></name></person-group><article-title>Pan-cancer Immunogenomic Analyses Reveal Genotype-Immunophenotype Relationships and Predictors of Response to Checkpoint Blockade</article-title><source>Cell Rep</source><year>2017</year><volume>18</volume><issue>1</issue><fpage>248</fpage><lpage>62</lpage><pub-id pub-id-type="doi">10.1016/j.celrep.2016.12.019</pub-id><?supplied-pmid 28052254?><pub-id pub-id-type="pmid">28052254</pub-id></element-citation></ref><ref id="CR14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chang</surname><given-names>MT</given-names></name><name><surname>Asthana</surname><given-names>S</given-names></name><name><surname>Gao</surname><given-names>SP</given-names></name><name><surname>Lee</surname><given-names>BH</given-names></name><name><surname>Chapman</surname><given-names>JS</given-names></name><name><surname>Kandoth</surname><given-names>C</given-names></name><name><surname>Gao</surname><given-names>J</given-names></name><name><surname>Socci</surname><given-names>ND</given-names></name><name><surname>Solit</surname><given-names>DB</given-names></name><name><surname>Olshen</surname><given-names>AB</given-names></name><name><surname>Schultz</surname><given-names>N</given-names></name><name><surname>Taylor</surname><given-names>BS</given-names></name></person-group><article-title>Identifying recurrent mutations in cancer reveals widespread lineage diversity and mutational specificity</article-title><source>Nature Biotechnol</source><year>2016</year><volume>34</volume><issue>2</issue><fpage>155</fpage><lpage>63</lpage><pub-id pub-id-type="doi">10.1038/nbt.3391</pub-id><pub-id pub-id-type="pmid">26619011</pub-id></element-citation></ref><ref id="CR15"><label>15</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>J</given-names></name><name><surname>Zhao</surname><given-names>W</given-names></name><name><surname>Zhou</surname><given-names>B</given-names></name><name><surname>Su</surname><given-names>Z</given-names></name><name><surname>Gu</surname><given-names>X</given-names></name><name><surname>Zhou</surname><given-names>Z</given-names></name><name><surname>Chen</surname><given-names>S</given-names></name></person-group><article-title>TSNAdb: A Database for Tumor-specific Neoantigens from Immunogenomics Data Analysis</article-title><source>Genomics, Proteomics Bioinforma</source><year>2018</year><volume>16</volume><issue>4</issue><fpage>276</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1016/j.gpb.2018.06.003</pub-id></element-citation></ref><ref id="CR16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grossman</surname><given-names>RL</given-names></name><name><surname>Heath</surname><given-names>AP</given-names></name><name><surname>Ferretti</surname><given-names>V</given-names></name><name><surname>Varmus</surname><given-names>HE</given-names></name><name><surname>Lowy</surname><given-names>DR</given-names></name><name><surname>Kibbe</surname><given-names>WA</given-names></name><name><surname>Staudt</surname><given-names>LM</given-names></name></person-group><article-title>Toward a Shared Vision for Cancer Genomic Data</article-title><source>New Engl J Med</source><year>2016</year><volume>375</volume><issue>12</issue><fpage>1109</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1056/NEJMp1607591</pub-id><?supplied-pmid 27653561?><pub-id pub-id-type="pmid">27653561</pub-id></element-citation></ref><ref id="CR17"><label>17</label><mixed-citation publication-type="other">Institute NC. Bioinformatics Pipeline: DNA-Seq Analysis. <ext-link ext-link-type="uri" xlink:href="https://docs.gdc.cancer.gov/Data/Bioinformatics_Pipelines/DNA_Seq_Variant_Calling_Pipeline/">https://docs.gdc.cancer.gov/Data/Bioinformatics_Pipelines/DNA_Seq_Variant_Calling\Pipeline/</ext-link>. Accessed 09/08/2017.</mixed-citation></ref><ref id="CR18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lek</surname><given-names>M</given-names></name><name><surname>Karczewski</surname><given-names>KJ</given-names></name><name><surname>Minikel</surname><given-names>EV</given-names></name><name><surname>Samocha</surname><given-names>KE</given-names></name><name><surname>Banks</surname><given-names>E</given-names></name><name><surname>Fennell</surname><given-names>T</given-names></name><name><surname>O&#x02019;Donnell-Luria</surname><given-names>AH</given-names></name><name><surname>Ware</surname><given-names>JS</given-names></name><name><surname>Hill</surname><given-names>AJ</given-names></name><name><surname>Cummings</surname><given-names>BB</given-names></name><name><surname>Tukiainen</surname><given-names>T</given-names></name><name><surname>Birnbaum</surname><given-names>DP</given-names></name><name><surname>Kosmicki</surname><given-names>JA</given-names></name><name><surname>Duncan</surname><given-names>LE</given-names></name><name><surname>Estrada</surname><given-names>K</given-names></name><name><surname>Zhao</surname><given-names>F</given-names></name><name><surname>Zou</surname><given-names>J</given-names></name><name><surname>Pierce-Hoffman</surname><given-names>E</given-names></name><name><surname>Berghout</surname><given-names>J</given-names></name><name><surname>Cooper</surname><given-names>DN</given-names></name><name><surname>Deflaux</surname><given-names>N</given-names></name><name><surname>DePristo</surname><given-names>M</given-names></name><name><surname>Do</surname><given-names>R</given-names></name><name><surname>Flannick</surname><given-names>J</given-names></name><name><surname>Fromer</surname><given-names>M</given-names></name><name><surname>Gauthier</surname><given-names>L</given-names></name><name><surname>Goldstein</surname><given-names>J</given-names></name><name><surname>Gupta</surname><given-names>N</given-names></name><name><surname>Howrigan</surname><given-names>D</given-names></name><name><surname>Kiezun</surname><given-names>A</given-names></name><etal/></person-group><article-title>Analysis of protein-coding genetic variation in 60,706 humans</article-title><source>Nature</source><year>2016</year><volume>536</volume><issue>7616</issue><fpage>285</fpage><lpage>91</lpage><pub-id pub-id-type="doi">10.1038/nature19057</pub-id><?supplied-pmid 27535533?><pub-id pub-id-type="pmid">27535533</pub-id></element-citation></ref><ref id="CR19"><label>19</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Snyder</surname><given-names>A</given-names></name><name><surname>Chan</surname><given-names>TA</given-names></name></person-group><article-title>Immunogenic peptide discovery in cancer genomes</article-title><source>Curr Opin Genet Develop</source><year>2015</year><volume>30</volume><fpage>7</fpage><lpage>16</lpage><pub-id pub-id-type="doi">10.1016/j.gde.2014.12.003</pub-id></element-citation></ref><ref id="CR20"><label>20</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vita</surname><given-names>R</given-names></name><name><surname>Overton</surname><given-names>JA</given-names></name><name><surname>Greenbaum</surname><given-names>JA</given-names></name><name><surname>Ponomarenko</surname><given-names>J</given-names></name><name><surname>Clark</surname><given-names>JD</given-names></name><name><surname>Cantrell</surname><given-names>JR</given-names></name><name><surname>Wheeler</surname><given-names>DK</given-names></name><name><surname>Gabbard</surname><given-names>JL</given-names></name><name><surname>Hix</surname><given-names>D</given-names></name><name><surname>Sette</surname><given-names>A</given-names></name><name><surname>Peters</surname><given-names>B</given-names></name></person-group><article-title>The immune epitope database (IEDB) 3.0</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><issue>D1</issue><fpage>405</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1093/nar/gku938</pub-id></element-citation></ref><ref id="CR21"><label>21</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Karosiene</surname><given-names>E</given-names></name><name><surname>Lundegaard</surname><given-names>C</given-names></name><name><surname>Lund</surname><given-names>O</given-names></name><name><surname>Nielsen</surname><given-names>M</given-names></name></person-group><article-title>NetMHCcons: a consensus method for the major histocompatibility complex class I predictions</article-title><source>Immunogenetics</source><year>2012</year><volume>64</volume><issue>3</issue><fpage>177</fpage><lpage>86</lpage><pub-id pub-id-type="doi">10.1007/s00251-011-0579-8</pub-id><?supplied-pmid 22009319?><pub-id pub-id-type="pmid">22009319</pub-id></element-citation></ref><ref id="CR22"><label>22</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Richters</surname><given-names>MM</given-names></name><name><surname>Xia</surname><given-names>H</given-names></name><name><surname>Campbell</surname><given-names>KM</given-names></name><name><surname>Gillanders</surname><given-names>WE</given-names></name><name><surname>Griffith</surname><given-names>OL</given-names></name><name><surname>Griffith</surname><given-names>M</given-names></name></person-group><article-title>Best practices for bioinformatic characterization of neoantigens for clinical utility</article-title><source>Genome Med</source><year>2019</year><volume>11</volume><issue>1</issue><fpage>56</fpage><pub-id pub-id-type="doi">10.1186/s13073-019-0666-2</pub-id><?supplied-pmid 31462330?><pub-id pub-id-type="pmid">31462330</pub-id></element-citation></ref><ref id="CR23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Laehnemann</surname><given-names>D</given-names></name><name><surname>Borkhardt</surname><given-names>A</given-names></name><name><surname>McHardy</surname><given-names>AC</given-names></name></person-group><article-title>Denoising DNA deep sequencing data-high-throughput sequencing errors and their correction</article-title><source>Brief Bioinforma</source><year>2016</year><volume>17</volume><issue>1</issue><fpage>154</fpage><lpage>79</lpage><pub-id pub-id-type="doi">10.1093/bib/bbv029</pub-id></element-citation></ref><ref id="CR24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>L-P</given-names></name><name><surname>Lampert</surname><given-names>JC</given-names></name><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Leitao</surname><given-names>C</given-names></name><name><surname>Popovi&#x00107;</surname><given-names>J</given-names></name><name><surname>M&#x000fc;ller</surname><given-names>W</given-names></name><name><surname>Blankenstein</surname><given-names>T</given-names></name></person-group><article-title>Transgenic mice with a diverse human T cell antigen receptor repertoire</article-title><source>Nature Med</source><year>2010</year><volume>16</volume><issue>9</issue><fpage>1029</fpage><lpage>34</lpage><pub-id pub-id-type="doi">10.1038/nm.2197</pub-id><?supplied-pmid 20693993?><pub-id pub-id-type="pmid">20693993</pub-id></element-citation></ref><ref id="CR25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pascolo</surname><given-names>S</given-names></name><name><surname>Bervas</surname><given-names>N</given-names></name><name><surname>Ure</surname><given-names>JM</given-names></name><name><surname>Smith</surname><given-names>AG</given-names></name><name><surname>Lemonnier</surname><given-names>FA</given-names></name><name><surname>Perarnau</surname><given-names>B</given-names></name></person-group><article-title>HLA-A2.1-restricted education and cytolytic activity of CD8(+) T lymphocytes from beta2 microglobulin (beta2m) HLA-A2.1 monochain transgenic H-2Db beta2m double knockout mice</article-title><source>J Exp Med</source><year>1997</year><volume>185</volume><issue>12</issue><fpage>2043</fpage><lpage>51</lpage><pub-id pub-id-type="doi">10.1084/jem.185.12.2043</pub-id><?supplied-pmid 9182675?><pub-id pub-id-type="pmid">9182675</pub-id></element-citation></ref><ref id="CR26"><label>26</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gonz&#x000e1;lez-Galarza</surname><given-names>FF</given-names></name><name><surname>Takeshita</surname><given-names>LYC</given-names></name><name><surname>Santos</surname><given-names>EJM</given-names></name><name><surname>Kempson</surname><given-names>F</given-names></name><name><surname>Maia</surname><given-names>MHT</given-names></name><name><surname>Silva</surname><given-names>ALSd</given-names></name><name><surname>Silva</surname><given-names>ALTe</given-names></name><name><surname>Ghattaoraya</surname><given-names>GS</given-names></name><name><surname>Alfirevic</surname><given-names>A</given-names></name><name><surname>Jones</surname><given-names>AR</given-names></name><name><surname>Middleton</surname><given-names>D</given-names></name></person-group><article-title>Allele frequency net 2015 update: new features for HLA epitopes, KIR and disease and HLA adverse drug reaction associations</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><issue>D1</issue><fpage>784</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1093/nar/gku1166</pub-id></element-citation></ref><ref id="CR27"><label>27</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ferlay</surname><given-names>J</given-names></name><name><surname>Soerjomataram</surname><given-names>I</given-names></name><name><surname>Dikshit</surname><given-names>R</given-names></name><name><surname>Eser</surname><given-names>S</given-names></name><name><surname>Mathers</surname><given-names>C</given-names></name><name><surname>Rebelo</surname><given-names>M</given-names></name><name><surname>Parkin</surname><given-names>DM</given-names></name><name><surname>Forman</surname><given-names>D</given-names></name><name><surname>Bray</surname><given-names>F</given-names></name></person-group><article-title>Cancer incidence and mortality worldwide: Sources, methods and major patterns in GLOBOCAN 2012</article-title><source>Int J Canc</source><year>2015</year><volume>136</volume><issue>5</issue><fpage>359</fpage><lpage>86</lpage><pub-id pub-id-type="doi">10.1002/ijc.29210</pub-id></element-citation></ref><ref id="CR28"><label>28</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vogelstein</surname><given-names>B</given-names></name><name><surname>Papadopoulos</surname><given-names>N</given-names></name><name><surname>Velculescu</surname><given-names>VE</given-names></name><name><surname>Zhou</surname><given-names>S</given-names></name><name><surname>Diaz</surname><given-names>LA</given-names></name><name><surname>Kinzler</surname><given-names>KW</given-names></name><name><surname>Wunderlich</surname><given-names>JR</given-names></name><name><surname>Somerville</surname><given-names>RP</given-names></name><name><surname>Hogan</surname><given-names>K</given-names></name><name><surname>Hinrichs</surname><given-names>CS</given-names></name><name><surname>Parkhurst</surname><given-names>MR</given-names></name><name><surname>Yang</surname><given-names>JC</given-names></name><name><surname>Rosenberg</surname><given-names>SA</given-names></name></person-group><article-title>Cancer genome landscapes</article-title><source>Sci (NY)</source><year>2013</year><volume>339</volume><issue>6127</issue><fpage>1546</fpage><lpage>58</lpage><pub-id pub-id-type="doi">10.1126/science.1235122</pub-id></element-citation></ref><ref id="CR29"><label>29</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rubio-Perez</surname><given-names>C</given-names></name><name><surname>Tamborero</surname><given-names>D</given-names></name><name><surname>Schroeder</surname><given-names>MP</given-names></name><name><surname>Antol&#x000ed;n</surname><given-names>AA</given-names></name><name><surname>Deu-Pons</surname><given-names>J</given-names></name><name><surname>Perez-Llamas</surname><given-names>C</given-names></name><name><surname>Mestres</surname><given-names>J</given-names></name><name><surname>Gonzalez-Perez</surname><given-names>A</given-names></name><name><surname>Lopez-Bigas</surname><given-names>N</given-names></name></person-group><article-title>In Silico Prescription of Anticancer Drugs to Cohorts of 28 Tumor Types Reveals Targeting Opportunities</article-title><source>Canc Cell</source><year>2015</year><volume>27</volume><issue>3</issue><fpage>382</fpage><lpage>96</lpage><pub-id pub-id-type="doi">10.1016/j.ccell.2015.02.007</pub-id></element-citation></ref><ref id="CR30"><label>30</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vu</surname><given-names>HL</given-names></name><name><surname>Rosenbaum</surname><given-names>S</given-names></name><name><surname>Purwin</surname><given-names>TJ</given-names></name><name><surname>Davies</surname><given-names>MA</given-names></name><name><surname>Aplin</surname><given-names>AE</given-names></name></person-group><article-title>RAC1 P29S regulates PD-L1 expression in melanoma</article-title><source>Pigment Cell Melanoma Res</source><year>2015</year><volume>28</volume><issue>5</issue><fpage>590</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1111/pcmr.12392</pub-id><?supplied-pmid 26176707?><pub-id pub-id-type="pmid">26176707</pub-id></element-citation></ref><ref id="CR31"><label>31</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Austinat</surname><given-names>M</given-names></name><name><surname>Dunsch</surname><given-names>R</given-names></name><name><surname>Wittekind</surname><given-names>C</given-names></name><name><surname>Tannapfel</surname><given-names>A</given-names></name><name><surname>Gebhardt</surname><given-names>R</given-names></name><name><surname>Gaunitz</surname><given-names>F</given-names></name></person-group><article-title>Correlation between <italic>&#x003b2;</italic>-catenin mutations and expression of Wnt-signaling target genes in hepatocellular carcinoma</article-title><source>Mole Canc</source><year>2008</year><volume>7</volume><issue>1</issue><fpage>21</fpage><pub-id pub-id-type="doi">10.1186/1476-4598-7-21</pub-id></element-citation></ref><ref id="CR32"><label>32</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pai</surname><given-names>SG</given-names></name><name><surname>Carneiro</surname><given-names>BA</given-names></name><name><surname>Mota</surname><given-names>JM</given-names></name><name><surname>Costa</surname><given-names>R</given-names></name><name><surname>Leite</surname><given-names>CA</given-names></name><name><surname>Barroso-Sousa</surname><given-names>R</given-names></name><name><surname>Kaplan</surname><given-names>JB</given-names></name><name><surname>Chae</surname><given-names>YK</given-names></name><name><surname>Giles</surname><given-names>FJ</given-names></name></person-group><article-title>Wnt/beta-catenin pathway: modulating anticancer immune response</article-title><source>J Hematol Oncol</source><year>2017</year><volume>10</volume><issue>1</issue><fpage>101</fpage><pub-id pub-id-type="doi">10.1186/s13045-017-0471-6</pub-id><?supplied-pmid 28476164?><pub-id pub-id-type="pmid">28476164</pub-id></element-citation></ref><ref id="CR33"><label>33</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Spranger</surname><given-names>S</given-names></name><name><surname>Gajewski</surname><given-names>TF</given-names></name></person-group><article-title>A new paradigm for tumor immune escape: <italic>&#x003b2;</italic>-catenin-driven immune exclusion</article-title><source>J Immun Canc</source><year>2015</year><volume>3</volume><issue>1</issue><fpage>43</fpage><pub-id pub-id-type="doi">10.1186/s40425-015-0089-6</pub-id></element-citation></ref><ref id="CR34"><label>34</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cho</surname><given-names>J</given-names></name><name><surname>Kim</surname><given-names>SY</given-names></name><name><surname>Kim</surname><given-names>YJ</given-names></name><name><surname>Sim</surname><given-names>MH</given-names></name><name><surname>Kim</surname><given-names>ST</given-names></name><name><surname>Kim</surname><given-names>NKD</given-names></name><name><surname>Kim</surname><given-names>K</given-names></name><name><surname>Park</surname><given-names>W</given-names></name><name><surname>Kim</surname><given-names>JH</given-names></name><name><surname>Jang</surname><given-names>K-T</given-names></name><name><surname>Lee</surname><given-names>J</given-names></name></person-group><article-title>Emergence of CTNNB1 mutation at acquired resistance to KIT inhibitor in metastatic melanoma</article-title><source>Clin Transl Oncol</source><year>2017</year><volume>19</volume><issue>10</issue><fpage>1247</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1007/s12094-017-1662-x</pub-id><?supplied-pmid 28421416?><pub-id pub-id-type="pmid">28421416</pub-id></element-citation></ref><ref id="CR35"><label>35</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Reiter</surname><given-names>K</given-names></name><name><surname>Polzer</surname><given-names>H</given-names></name><name><surname>Krupka</surname><given-names>C</given-names></name><name><surname>Maiser</surname><given-names>A</given-names></name><name><surname>Vick</surname><given-names>B</given-names></name><name><surname>Rothenberg-Thurley</surname><given-names>M</given-names></name><name><surname>Metzeler</surname><given-names>KH</given-names></name><name><surname>D&#x000f6;rfel</surname><given-names>D</given-names></name><name><surname>Salih</surname><given-names>HR</given-names></name><name><surname>Jung</surname><given-names>G</given-names></name><name><surname>N&#x000f6;&#x000df;ner</surname><given-names>E</given-names></name><name><surname>Jeremias</surname><given-names>I</given-names></name><name><surname>Hiddemann</surname><given-names>W</given-names></name><name><surname>Leonhardt</surname><given-names>H</given-names></name><name><surname>Spiekermann</surname><given-names>K</given-names></name><name><surname>Subklewe</surname><given-names>M</given-names></name><name><surname>Greif</surname><given-names>PA</given-names></name></person-group><article-title>Tyrosine kinase inhibition increases the cell surface localization of FLT3-ITD and enhances FLT3-directed immunotherapy of acute myeloid leukemia</article-title><source>Leukemia</source><year>2018</year><volume>32</volume><issue>2</issue><fpage>313</fpage><lpage>22</lpage><pub-id pub-id-type="doi">10.1038/leu.2017.257</pub-id><?supplied-pmid 28895560?><pub-id pub-id-type="pmid">28895560</pub-id></element-citation></ref><ref id="CR36"><label>36</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gfeller</surname><given-names>D</given-names></name><name><surname>Bassani-Sternberg</surname><given-names>M</given-names></name></person-group><article-title>Predicting Antigen Presentation-What Could We Learn From a Million Peptides?</article-title><source>Front Immun</source><year>2018</year><volume>9</volume><fpage>1716</fpage><pub-id pub-id-type="doi">10.3389/fimmu.2018.01716</pub-id></element-citation></ref><ref id="CR37"><label>37</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fritsch</surname><given-names>EF</given-names></name><name><surname>Rajasagi</surname><given-names>M</given-names></name><name><surname>Ott</surname><given-names>PA</given-names></name><name><surname>Brusic</surname><given-names>V</given-names></name><name><surname>Hacohen</surname><given-names>N</given-names></name><name><surname>Wu</surname><given-names>CJ</given-names></name></person-group><article-title>HLA-binding properties of tumor neoepitopes in humans</article-title><source>Canc Immun Res</source><year>2014</year><volume>2</volume><issue>6</issue><fpage>522</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1158/2326-6066.CIR-13-0227</pub-id></element-citation></ref><ref id="CR38"><label>38</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>van Buuren</surname><given-names>MM</given-names></name><name><surname>Calis</surname><given-names>JJ</given-names></name><name><surname>Schumacher</surname><given-names>TN</given-names></name></person-group><article-title>High sensitivity of cancer exome-based CD8 T cell neo-antigen identification</article-title><source>OncoImmunology</source><year>2014</year><volume>3</volume><issue>5</issue><fpage>28836</fpage><pub-id pub-id-type="doi">10.4161/onci.28836</pub-id></element-citation></ref><ref id="CR39"><label>39</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robbins</surname><given-names>PF</given-names></name><name><surname>El-Gamil</surname><given-names>M</given-names></name><name><surname>Li</surname><given-names>YF</given-names></name><name><surname>Kawakami</surname><given-names>Y</given-names></name><name><surname>Loftus</surname><given-names>D</given-names></name><name><surname>Appella</surname><given-names>E</given-names></name><name><surname>Rosenberg</surname><given-names>SA</given-names></name></person-group><article-title>A mutated beta-catenin gene encodes a melanoma-specific antigen recognized by tumor infiltrating lymphocytes</article-title><source>J Experiment Med</source><year>1996</year><volume>183</volume><issue>3</issue><fpage>1185</fpage><lpage>92</lpage><pub-id pub-id-type="doi">10.1084/jem.183.3.1185</pub-id></element-citation></ref><ref id="CR40"><label>40</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>W&#x000f6;lfel</surname><given-names>T</given-names></name><name><surname>Hauer</surname><given-names>M</given-names></name><name><surname>Schneider</surname><given-names>J</given-names></name><name><surname>Serrano</surname><given-names>M</given-names></name><name><surname>W&#x000f6;lfel</surname><given-names>C</given-names></name><name><surname>Klehmann-Hieb</surname><given-names>E</given-names></name><name><surname>De Plaen</surname><given-names>E</given-names></name><name><surname>Hankeln</surname><given-names>T</given-names></name><name><surname>Meyer zum B&#x000fc;schenfelde</surname><given-names>KH</given-names></name><name><surname>Beach</surname><given-names>D</given-names></name></person-group><article-title>A p16INK4a-insensitive CDK4 mutant targeted by cytolytic T lymphocytes in a human melanoma</article-title><source>Sci(NY)</source><year>1995</year><volume>269</volume><issue>5228</issue><fpage>1281</fpage><lpage>4</lpage></element-citation></ref><ref id="CR41"><label>41</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Landsberg</surname><given-names>J</given-names></name><name><surname>Gaffal</surname><given-names>E</given-names></name><name><surname>Cron</surname><given-names>M</given-names></name><name><surname>Kohlmeyer</surname><given-names>J</given-names></name><name><surname>Renn</surname><given-names>M</given-names></name><name><surname>T&#x000fc;ting</surname><given-names>T</given-names></name></person-group><article-title>Autochthonous primary and metastatic melanomas in Hgf-Cdk4R24C mice evade T-cell-mediated immune surveillance</article-title><source>Pigment Cell Melanoma Res</source><year>2010</year><volume>23</volume><issue>5</issue><fpage>649</fpage><lpage>60</lpage><pub-id pub-id-type="doi">10.1111/j.1755-148X.2010.00744.x</pub-id><?supplied-pmid 20649939?><pub-id pub-id-type="pmid">20649939</pub-id></element-citation></ref><ref id="CR42"><label>42</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Platz</surname><given-names>A</given-names></name><name><surname>Ringborg</surname><given-names>U</given-names></name><name><surname>Hansson</surname><given-names>J</given-names></name></person-group><article-title>Hereditary cutaneous melanoma</article-title><source>Sem Canc Biol</source><year>2000</year><volume>10</volume><issue>4</issue><fpage>319</fpage><lpage>26</lpage><pub-id pub-id-type="doi">10.1006/scbi.2000.0149</pub-id></element-citation></ref><ref id="CR43"><label>43</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>L</given-names></name><name><surname>Blankenstein</surname><given-names>T</given-names></name></person-group><article-title>Generation of transgenic mice with megabase-sized human yeast artificial chromosomes by yeast spheroplast-embryonic stem cell fusion</article-title><source>Nature Protoc</source><year>2013</year><volume>8</volume><issue>8</issue><fpage>1567</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1038/nprot.2013.093</pub-id><pub-id pub-id-type="pmid">23868074</pub-id></element-citation></ref><ref id="CR44"><label>44</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robbins</surname><given-names>PF</given-names></name><name><surname>Lu</surname><given-names>Y-C</given-names></name><name><surname>El-Gamil</surname><given-names>M</given-names></name><name><surname>Li</surname><given-names>YF</given-names></name><name><surname>Gross</surname><given-names>C</given-names></name><name><surname>Gartner</surname><given-names>J</given-names></name><name><surname>Lin</surname><given-names>JC</given-names></name><name><surname>Teer</surname><given-names>JK</given-names></name><name><surname>Cliften</surname><given-names>P</given-names></name><name><surname>Tycksen</surname><given-names>E</given-names></name><name><surname>Samuels</surname><given-names>Y</given-names></name><name><surname>Rosenberg</surname><given-names>SA</given-names></name></person-group><article-title>Mining exomic sequencing data to identify mutated antigens recognized by adoptively transferred tumor-reactive T cells</article-title><source>Nature Med</source><year>2013</year><volume>19</volume><issue>6</issue><fpage>747</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1038/nm.3161</pub-id><?supplied-pmid 23644516?><pub-id pub-id-type="pmid">23644516</pub-id></element-citation></ref><ref id="CR45"><label>45</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rosenberg</surname><given-names>SA</given-names></name><name><surname>Dudley</surname><given-names>ME</given-names></name></person-group><article-title>Adoptive cell therapy for the treatment of patients with metastatic melanoma</article-title><source>Curr Opin Immun</source><year>2009</year><volume>21</volume><issue>2</issue><fpage>233</fpage><lpage>40</lpage><pub-id pub-id-type="doi">10.1016/j.coi.2009.03.002</pub-id></element-citation></ref><ref id="CR46"><label>46</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chandran</surname><given-names>SS</given-names></name><name><surname>Somerville</surname><given-names>RPT</given-names></name><name><surname>Yang</surname><given-names>JC</given-names></name><name><surname>Sherry</surname><given-names>RM</given-names></name><name><surname>Klebanoff</surname><given-names>CA</given-names></name><name><surname>Goff</surname><given-names>SL</given-names></name><name><surname>Wunderlich</surname><given-names>JR</given-names></name><name><surname>Danforth</surname><given-names>DN</given-names></name><name><surname>Zlott</surname><given-names>D</given-names></name><name><surname>Paria</surname><given-names>BC</given-names></name><name><surname>Sabesan</surname><given-names>AC</given-names></name><name><surname>Srivastava</surname><given-names>AK</given-names></name><name><surname>Xi</surname><given-names>L</given-names></name><name><surname>Pham</surname><given-names>TH</given-names></name><name><surname>Raffeld</surname><given-names>M</given-names></name><name><surname>White</surname><given-names>DE</given-names></name><name><surname>Toomey</surname><given-names>MA</given-names></name><name><surname>Rosenberg</surname><given-names>SA</given-names></name><name><surname>Kammula</surname><given-names>US</given-names></name></person-group><article-title>Treatment of metastatic uveal melanoma with adoptive transfer of tumour-infiltrating lymphocytes: a single-centre, two-stage, single-arm, phase 2 study</article-title><source>The Lancet Oncol</source><year>2017</year><volume>18</volume><issue>6</issue><fpage>792</fpage><lpage>802</lpage><pub-id pub-id-type="doi">10.1016/S1470-2045(17)30251-6</pub-id><?supplied-pmid 28395880?><pub-id pub-id-type="pmid">28395880</pub-id></element-citation></ref><ref id="CR47"><label>47</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tran</surname><given-names>E</given-names></name><name><surname>Turcotte</surname><given-names>S</given-names></name><name><surname>Gros</surname><given-names>A</given-names></name><name><surname>Robbins</surname><given-names>PF</given-names></name><name><surname>Lu</surname><given-names>Y-C</given-names></name><name><surname>Dudley</surname><given-names>ME</given-names></name><name><surname>Wunderlich</surname><given-names>JR</given-names></name><name><surname>Somerville</surname><given-names>RP</given-names></name><name><surname>Hogan</surname><given-names>K</given-names></name><name><surname>Hinrichs</surname><given-names>CS</given-names></name><name><surname>Parkhurst</surname><given-names>MR</given-names></name><name><surname>Yang</surname><given-names>JC</given-names></name><name><surname>Rosenberg</surname><given-names>SA</given-names></name></person-group><article-title>Cancer immunotherapy based on mutation-specific CD4+ T cells in a patient with epithelial cancer</article-title><source>Sci (NY)</source><year>2014</year><volume>344</volume><issue>6184</issue><fpage>641</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1126/science.1251102</pub-id></element-citation></ref><ref id="CR48"><label>48</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rooney</surname><given-names>MS</given-names></name><name><surname>Shukla</surname><given-names>SA</given-names></name><name><surname>Wu</surname><given-names>CJ</given-names></name><name><surname>Getz</surname><given-names>G</given-names></name><name><surname>Hacohen</surname><given-names>N</given-names></name></person-group><article-title>Molecular and Genetic Properties of Tumors Associated with Local Immune Cytolytic Activity</article-title><source>Cell</source><year>2015</year><volume>160</volume><issue>1-2</issue><fpage>48</fpage><lpage>61</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2014.12.033</pub-id><?supplied-pmid 25594174?><pub-id pub-id-type="pmid">25594174</pub-id></element-citation></ref><ref id="CR49"><label>49</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McGranahan</surname><given-names>N</given-names></name><name><surname>Furness</surname><given-names>AJS</given-names></name><name><surname>Rosenthal</surname><given-names>R</given-names></name><name><surname>Ramskov</surname><given-names>S</given-names></name><name><surname>Lyngaa</surname><given-names>R</given-names></name><name><surname>Saini</surname><given-names>SK</given-names></name><name><surname>Jamal-Hanjani</surname><given-names>M</given-names></name><name><surname>Wilson</surname><given-names>GA</given-names></name><name><surname>Birkbak</surname><given-names>NJ</given-names></name><name><surname>Hiley</surname><given-names>CT</given-names></name><name><surname>Watkins</surname><given-names>TBK</given-names></name><name><surname>Shafi</surname><given-names>S</given-names></name><name><surname>Murugaesu</surname><given-names>N</given-names></name><name><surname>Mitter</surname><given-names>R</given-names></name><name><surname>Akarca</surname><given-names>AU</given-names></name><name><surname>Linares</surname><given-names>J</given-names></name><name><surname>Marafioti</surname><given-names>T</given-names></name><name><surname>Henry</surname><given-names>JY</given-names></name><name><surname>Van Allen</surname><given-names>EM</given-names></name><name><surname>Miao</surname><given-names>D</given-names></name><name><surname>Schilling</surname><given-names>B</given-names></name><name><surname>Schadendorf</surname><given-names>D</given-names></name><name><surname>Garraway</surname><given-names>LA</given-names></name><name><surname>Makarov</surname><given-names>V</given-names></name><name><surname>Rizvi</surname><given-names>NA</given-names></name><name><surname>Snyder</surname><given-names>A</given-names></name><name><surname>Hellmann</surname><given-names>MD</given-names></name><name><surname>Merghoub</surname><given-names>T</given-names></name><name><surname>Wolchok</surname><given-names>JD</given-names></name><name><surname>Shukla</surname><given-names>SA</given-names></name><etal/></person-group><article-title>Clonal neoantigens elicit T cell immunoreactivity and sensitivity to immune checkpoint blockade</article-title><source>Sci (NY)</source><year>2016</year><volume>351</volume><issue>6280</issue><fpage>1463</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1126/science.aaf1490</pub-id></element-citation></ref><ref id="CR50"><label>50</label><mixed-citation publication-type="other">den Eynden JV, Jimenez-Sanchez A, Miller M, Lekholm EL. Lack of detectable neoantigen depletion in the untreated cancer genome. BioRxiv. 2018:478263. 10.1101/478263.</mixed-citation></ref><ref id="CR51"><label>51</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Patel</surname><given-names>SJ</given-names></name><name><surname>Sanjana</surname><given-names>NE</given-names></name><name><surname>Kishton</surname><given-names>RJ</given-names></name><name><surname>Eidizadeh</surname><given-names>A</given-names></name><name><surname>Vodnala</surname><given-names>SK</given-names></name><name><surname>Cam</surname><given-names>M</given-names></name><name><surname>Gartner</surname><given-names>JJ</given-names></name><name><surname>Jia</surname><given-names>L</given-names></name><name><surname>Steinberg</surname><given-names>SM</given-names></name><name><surname>Yamamoto</surname><given-names>TN</given-names></name><name><surname>Merchant</surname><given-names>AS</given-names></name><name><surname>Mehta</surname><given-names>GU</given-names></name><name><surname>Chichura</surname><given-names>A</given-names></name><name><surname>Shalem</surname><given-names>O</given-names></name><name><surname>Tran</surname><given-names>E</given-names></name><name><surname>Eil</surname><given-names>R</given-names></name><name><surname>Sukumar</surname><given-names>M</given-names></name><name><surname>Guijarro</surname><given-names>EP</given-names></name><name><surname>Day</surname><given-names>C-P</given-names></name><name><surname>Robbins</surname><given-names>P</given-names></name><name><surname>Feldman</surname><given-names>S</given-names></name><name><surname>Merlino</surname><given-names>G</given-names></name><name><surname>Zhang</surname><given-names>F</given-names></name><name><surname>Restifo</surname><given-names>NP</given-names></name></person-group><article-title>Identification of essential genes for cancer immunotherapy</article-title><source>Nature</source><year>2017</year><volume>548</volume><issue>7669</issue><fpage>537</fpage><lpage>42</lpage><pub-id pub-id-type="doi">10.1038/nature23477</pub-id><?supplied-pmid 28783722?><pub-id pub-id-type="pmid">28783722</pub-id></element-citation></ref><ref id="CR52"><label>52</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Str&#x000f8;nen</surname><given-names>E</given-names></name><name><surname>Toebes</surname><given-names>M</given-names></name><name><surname>Kelderman</surname><given-names>S</given-names></name><name><surname>van Buuren</surname><given-names>MM</given-names></name><name><surname>Yang</surname><given-names>W</given-names></name><name><surname>van Rooij</surname><given-names>N</given-names></name><name><surname>Donia</surname><given-names>M</given-names></name><name><surname>B&#x000f6;schen</surname><given-names>M-L</given-names></name><name><surname>Lund-Johansen</surname><given-names>F</given-names></name><name><surname>Olweus</surname><given-names>J</given-names></name><name><surname>Schumacher</surname><given-names>TN</given-names></name></person-group><article-title>Targeting of cancer neoantigens with donor-derived T cell receptor repertoires</article-title><source>Sci(NY)</source><year>2016</year><volume>352</volume><issue>6291</issue><fpage>1337</fpage><lpage>41</lpage></element-citation></ref><ref id="CR53"><label>53</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Johnson</surname><given-names>LA</given-names></name><name><surname>June</surname><given-names>CH</given-names></name></person-group><article-title>Driving gene-engineered T cell immunotherapy of cancer</article-title><source>Cell Res</source><year>2017</year><volume>27</volume><issue>1</issue><fpage>38</fpage><lpage>58</lpage><pub-id pub-id-type="doi">10.1038/cr.2016.154</pub-id><?supplied-pmid 28025979?><pub-id pub-id-type="pmid">28025979</pub-id></element-citation></ref><ref id="CR54"><label>54</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robbins</surname><given-names>PF</given-names></name><name><surname>Kassim</surname><given-names>SH</given-names></name><name><surname>Tran</surname><given-names>TLN</given-names></name><name><surname>Crystal</surname><given-names>JS</given-names></name><name><surname>Morgan</surname><given-names>RA</given-names></name><name><surname>Feldman</surname><given-names>SA</given-names></name><name><surname>Yang</surname><given-names>JC</given-names></name><name><surname>Dudley</surname><given-names>ME</given-names></name><name><surname>Wunderlich</surname><given-names>JR</given-names></name><name><surname>Sherry</surname><given-names>RM</given-names></name><name><surname>Kammula</surname><given-names>US</given-names></name><name><surname>Hughes</surname><given-names>MS</given-names></name><name><surname>Restifo</surname><given-names>NP</given-names></name><name><surname>Raffeld</surname><given-names>M</given-names></name><name><surname>Lee</surname><given-names>C-CR</given-names></name><name><surname>Li</surname><given-names>YF</given-names></name><name><surname>El-Gamil</surname><given-names>M</given-names></name><name><surname>Rosenberg</surname><given-names>SA</given-names></name></person-group><article-title>A pilot trial using lymphocytes genetically engineered with an NY-ESO-1-reactive T-cell receptor: long-term follow-up and correlates with response</article-title><source>Clin Canc Res: Official J Am Assoc Canc Res</source><year>2015</year><volume>21</volume><issue>5</issue><fpage>1019</fpage><lpage>27</lpage><pub-id pub-id-type="doi">10.1158/1078-0432.CCR-14-2708</pub-id></element-citation></ref><ref id="CR55"><label>55</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>van den Berg</surname><given-names>JH</given-names></name><name><surname>Gomez-Eerland</surname><given-names>R</given-names></name><name><surname>van de Wiel</surname><given-names>B</given-names></name><name><surname>Hulshoff</surname><given-names>L</given-names></name><name><surname>van den Broek</surname><given-names>D</given-names></name><name><surname>Bins</surname><given-names>A</given-names></name><name><surname>Tan</surname><given-names>HL</given-names></name><name><surname>Harper</surname><given-names>JV</given-names></name><name><surname>Hassan</surname><given-names>NJ</given-names></name><name><surname>Jakobsen</surname><given-names>BK</given-names></name><name><surname>Jorritsma</surname><given-names>A</given-names></name><name><surname>Blank</surname><given-names>CU</given-names></name><name><surname>Schumacher</surname><given-names>TNM</given-names></name><name><surname>Haanen</surname><given-names>JBAG</given-names></name></person-group><article-title>Case Report of a Fatal Serious Adverse Event Upon Administration of T Cells Transduced With a MART-1-specific T-cell Receptor</article-title><source>Mole Therapy</source><year>2015</year><volume>23</volume><issue>9</issue><fpage>1541</fpage><lpage>50</lpage><pub-id pub-id-type="doi">10.1038/mt.2015.60</pub-id></element-citation></ref><ref id="CR56"><label>56</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Linette</surname><given-names>GP</given-names></name><name><surname>Stadtmauer</surname><given-names>EA</given-names></name><name><surname>Maus</surname><given-names>MV</given-names></name><name><surname>Rapoport</surname><given-names>AP</given-names></name><name><surname>Levine</surname><given-names>BL</given-names></name><name><surname>Emery</surname><given-names>L</given-names></name><name><surname>Litzky</surname><given-names>L</given-names></name><name><surname>Bagg</surname><given-names>A</given-names></name><name><surname>Carreno</surname><given-names>BM</given-names></name><name><surname>Cimino</surname><given-names>PJ</given-names></name><name><surname>Binder-Scholl</surname><given-names>GK</given-names></name><name><surname>Smethurst</surname><given-names>DP</given-names></name><name><surname>Gerry</surname><given-names>AB</given-names></name><name><surname>Pumphrey</surname><given-names>NJ</given-names></name><name><surname>Bennett</surname><given-names>AD</given-names></name><name><surname>Brewer</surname><given-names>JE</given-names></name><name><surname>Dukes</surname><given-names>J</given-names></name><name><surname>Harper</surname><given-names>J</given-names></name><name><surname>Tayton-Martin</surname><given-names>HK</given-names></name><name><surname>Jakobsen</surname><given-names>BK</given-names></name><name><surname>Hassan</surname><given-names>NJ</given-names></name><name><surname>Kalos</surname><given-names>M</given-names></name><name><surname>June</surname><given-names>CH</given-names></name></person-group><article-title>Cardiovascular toxicity and titin cross-reactivity of affinity-enhanced T cells in myeloma and melanoma</article-title><source>Blood</source><year>2013</year><volume>122</volume><issue>6</issue><fpage>863</fpage><lpage>71</lpage><pub-id pub-id-type="doi">10.1182/blood-2013-03-490565</pub-id><?supplied-pmid 3743463?><pub-id pub-id-type="pmid">23770775</pub-id></element-citation></ref><ref id="CR57"><label>57</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Martincorena</surname><given-names>I</given-names></name><name><surname>Raine</surname><given-names>KM</given-names></name><name><surname>Gerstung</surname><given-names>M</given-names></name><name><surname>Dawson</surname><given-names>KJ</given-names></name><name><surname>Haase</surname><given-names>K</given-names></name><name><surname>Van Loo</surname><given-names>P</given-names></name><name><surname>Davies</surname><given-names>H</given-names></name><name><surname>Stratton</surname><given-names>MR</given-names></name><name><surname>Campbell</surname><given-names>PJ</given-names></name></person-group><article-title>Universal Patterns of Selection in Cancer and Somatic Tissues</article-title><source>Cell</source><year>2017</year><volume>171</volume><issue>5</issue><fpage>1029</fpage><lpage>104121</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2017.09.042</pub-id><?supplied-pmid 29056346?><pub-id pub-id-type="pmid">29056346</pub-id></element-citation></ref><ref id="CR58"><label>58</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hartmaier</surname><given-names>RJ</given-names></name><name><surname>Charo</surname><given-names>J</given-names></name><name><surname>Fabrizio</surname><given-names>D</given-names></name><name><surname>Goldberg</surname><given-names>ME</given-names></name><name><surname>Albacker</surname><given-names>LA</given-names></name><name><surname>Pao</surname><given-names>W</given-names></name><name><surname>Chmielecki</surname><given-names>J</given-names></name></person-group><article-title>Genomic analysis of 63,220 tumors reveals insights into tumor uniqueness and targeted cancer immunotherapy strategies</article-title><source>Genome Med</source><year>2017</year><volume>9</volume><issue>1</issue><fpage>16</fpage><pub-id pub-id-type="doi">10.1186/s13073-017-0408-2</pub-id><?supplied-pmid 5324279?><pub-id pub-id-type="pmid">28231819</pub-id></element-citation></ref></ref-list></back></article>