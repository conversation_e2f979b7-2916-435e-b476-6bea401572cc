<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">J Cheminform</journal-id><journal-id journal-id-type="iso-abbrev">J Cheminform</journal-id><journal-title-group><journal-title>Journal of Cheminformatics</journal-title></journal-title-group><issn pub-type="epub">1758-2946</issn><publisher><publisher-name>Springer International Publishing</publisher-name><publisher-loc>Cham</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5700017</article-id><article-id pub-id-type="publisher-id">246</article-id><article-id pub-id-type="doi">10.1186/s13321-017-0246-7</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Efficient conformational ensemble generation of protein-bound peptides</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Yan</surname><given-names>Yumeng</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Zhang</surname><given-names>Di</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Huang</surname><given-names>Sheng-You</given-names></name><address><phone>+86-27-87543881</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0368 7223</institution-id><institution-id institution-id-type="GRID">grid.33199.31</institution-id><institution>School of Physics, </institution><institution>Huazhong University of Science and Technology, </institution></institution-wrap>Wuhan, 430074 Hubei People&#x02019;s Republic of China </aff></contrib-group><pub-date pub-type="epub"><day>22</day><month>11</month><year>2017</year></pub-date><pub-date pub-type="pmc-release"><day>22</day><month>11</month><year>2017</year></pub-date><pub-date pub-type="collection"><year>2017</year></pub-date><volume>9</volume><elocation-id>59</elocation-id><history><date date-type="received"><day>19</day><month>6</month><year>2017</year></date><date date-type="accepted"><day>15</day><month>11</month><year>2017</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2017</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><p id="Par1">Conformation generation of protein-bound peptides is critical for the determination of protein&#x02013;peptide complex structures. Despite significant progress in conformer generation of small molecules, few methods have been developed for modeling protein-bound peptide conformations. Here, we have developed a fast de novo peptide modeling algorithm, referred to as MODPEP, for conformational sampling of protein-bound peptides. Given a sequence, MODPEP builds the peptide 3D structure from scratch by assembling amino acids or helix fragments based on constructed rotamer and helix libraries. The MODPEP algorithm was tested on a diverse set of 910 experimentally determined protein-bound peptides with 3&#x02013;30 amino acids from the PDB and obtained an average accuracy of 1.90&#x000a0;&#x000c5; when 200 conformations were sampled for each peptide. On average, MODPEP obtained a success rate of 74.3% for all the 910 peptides and &#x02265;&#x000a0;90% for short peptides with 3&#x02013;10 amino acids in reproducing experimental protein-bound structures. Comparative evaluations of MODPEP with three other conformer generation methods, PEP-FOLD3, RDKit, and Balloon, have also been performed in both accuracy and success rate. MODPEP is fast and can generate 100 conformations for less than one second. The fast MODPEP will be beneficial for large-scale de novo modeling and docking of peptides. The MODPEP program and libraries are available for download at <ext-link ext-link-type="uri" xlink:href="http://huanglab.phys.hust.edu.cn/">http://huanglab.phys.hust.edu.cn/</ext-link>.<graphic position="anchor" xlink:href="13321_2017_246_Figa_HTML" id="MO1"/>
</p><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s13321-017-0246-7) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Conformer generation</kwd><kwd>Peptide</kwd><kwd>Molecular docking</kwd><kwd>Protein&#x02013;peptide interactions</kwd><kwd>Conformational sampling</kwd></kwd-group><funding-group><award-group><funding-source><institution>National Key Research and Development Program of China</institution></funding-source><award-id>2016YFC1305800</award-id><award-id>2016YFC1305805</award-id><principal-award-recipient><name><surname>Huang</surname><given-names>Sheng-You</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001809</institution-id><institution>National Natural Science Foundation of China</institution></institution-wrap></funding-source><award-id>31670724</award-id><principal-award-recipient><name><surname>Huang</surname><given-names>Sheng-You</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100003397</institution-id><institution>Huazhong University of Science and Technology</institution></institution-wrap></funding-source><award-id>3004012104</award-id><principal-award-recipient><name><surname>Huang</surname><given-names>Sheng-You</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2017</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par2">The interactions between peptides and proteins have received increasing attention in drug discovery because of their involvement in critical human diseases, such as cancer and infections [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR4">4</xref>]. It has been found that nearly 40% of protein&#x02013;protein interactions are mediated by short peptides [<xref ref-type="bibr" rid="CR2">2</xref>]. The biological function of a short peptide is related to its three-dimensional structure within its interacting protein. Therefore, determining the structures of protein&#x02013;peptide interactions is valuable for studying their molecular mechanism and thus developing peptide drugs [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>]. However, due to the high cost and technical difficulties, only a small portion of protein&#x02013;peptide complex structures were experimentally determined [<xref ref-type="bibr" rid="CR7">7</xref>], compared to the huge number of peptides involved in cell function [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR9">9</xref>]. As such, a variety of computational methods like molecular docking have been developed to predict the structures of protein&#x02013;peptide complexes [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR13">13</xref>].</p><p id="Par3">Peptides are highly flexible and exist as an ensemble of conformations in solution. The biologically active conformation of a peptide is selected and/or induced when interacting with its protein partner. Therefore, a big challenge in protein&#x02013;peptide docking is to consider the flexibility of peptides [<xref ref-type="bibr" rid="CR12">12</xref>&#x02013;<xref ref-type="bibr" rid="CR16">16</xref>]. One way to consider peptide flexibility in docking is to fully sample the conformations of a peptide on-the-fly guided by its binding energy score [<xref ref-type="bibr" rid="CR17">17</xref>&#x02013;<xref ref-type="bibr" rid="CR19">19</xref>]. However, given so many rotatable bonds in peptides, such sampling is computationally prohibitive. Therefore, current docking approaches often adopt a docking&#x000a0;+&#x000a0;MD protocol [<xref ref-type="bibr" rid="CR20">20</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>]. Nevertheless, this kind of docking&#x000a0;+&#x000a0;MD protocols is still computationally expensive and typically takes at least a few hours for docking a peptide [<xref ref-type="bibr" rid="CR20">20</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>]. Another way to consider peptide flexibility is through ensemble docking [<xref ref-type="bibr" rid="CR23">23</xref>&#x02013;<xref ref-type="bibr" rid="CR25">25</xref>]. Namely, an ensemble of conformations for a peptide are first generated by a conformational sampling method and then docked against the protein by regular rigid docking [<xref ref-type="bibr" rid="CR23">23</xref>]. A few top fits between the protein and the peptide conformations are selected as the predictions that may be subject to further refinement. Because of its high computational efficiency, ensemble docking has been widely used to consider molecular flexibility in both protein&#x02013;protein and protein&#x02013;ligand docking [<xref ref-type="bibr" rid="CR10">10</xref>, <xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR27">27</xref>].</p><p id="Par4">One critical part of ensemble docking is to generate an ensemble of peptide 3D models that include protein-bound peptide conformations, so that the biologically active ones can be selected by the protein during ensemble docking [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. Despite significant progresses in the conformer generation of small molecules [<xref ref-type="bibr" rid="CR29">29</xref>&#x02013;<xref ref-type="bibr" rid="CR36">36</xref>], few approaches have been developed for modeling of biologically active/protein-bound peptide conformations [<xref ref-type="bibr" rid="CR37">37</xref>]. Therefore, a novel strategy is pressingly needed for efficient generation of protein-bound peptides. Meeting the need, we have developed a fast de novo approach for the generation of peptide 3D models, which is referred to as MODPEP. Instead of relying on a template, our MODPEP algorithm builds a peptide structure from scratch by assembling amino acids or helix fragments based on constructed rotamer and helix libraries. The peptide model building process is very fast and can generate a few hundred peptide conformations within seconds. Our method was validated on the peptide structures of 910 experimentally determined protein&#x02013;peptide complexes from the protein data bank (PDB) [<xref ref-type="bibr" rid="CR7">7</xref>].</p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Dataset compilation</title><p id="Par5">To construct rotamer libraries and validate our algorithm, we have developed a non-redundant dataset of experimentally determined protein-bound peptide structures. Specifically, we queried all the X-ray peptide structures in the PDB that met the following criteria. First, the peptide sequence contains at least three but less than 50 amino acids. Second, the structure has a resolution better than 3.0&#x000a0;&#x000c5;. Third, the peptide does not contain non-standard amino acids. Fourth, the peptide must be bound to a protein. As of December 23, 2016, the query yielded a total of 3861 peptides meeting the above criteria. The sequences of the 3861 peptides were then clustered using the program CD-HIT [<xref ref-type="bibr" rid="CR38">38</xref>]. If there are multiple peptide structures for a sequence, the structure with the highest resolution was selected to represent the sequence, resulting in a total of 2731 non-redundant peptide structures. It should be noted that unlike proteins which are often conserved in sequences, peptides often adopt a coil-like structure and are thus normally not conserved in sequences. Of these 2731 peptides, about two thirds (i.e. 1821) were randomly selected as the training database to construct the rotamer and helix libraries for peptide modeling, in which 878 peptides has a resolution between 2.0 and 3.0&#x000a0;&#x000c5;. It should be noted that inclusion of the peptides with resolution of 2&#x02013;3&#x000a0;&#x000c5; should not have a significant influence on the backbone quality of the libraries and thus the prediction of peptide backbone, as according to X-ray crystallography, the positions of backbone and many side chains are clear in the electron density map at 2&#x02013;3&#x000a0;&#x000c5; resolution [<xref ref-type="bibr" rid="CR39">39</xref>]. The rest 910 peptides were used as the test set to validate our algorithm. The frequencies of the peptides with different lengths are shown in Fig. <xref rid="Fig1" ref-type="fig">1</xref> and Table <xref rid="Tab1" ref-type="table">1</xref>.<table-wrap id="Tab1"><label>Table 1</label><caption><p>The average accuracies of our MODPEP method in reproducing protein-bound conformations for the peptides with different lengths when various ensemble sizes were considered</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" colspan="2">Peptide</th><th align="left" colspan="8">cRMSD (&#x000c5;)</th></tr><tr><th align="left">Length</th><th align="left">Number</th><th align="left">50</th><th align="left">100</th><th align="left">150</th><th align="left">200</th><th align="left">250</th><th align="left">300</th><th align="left">500</th><th align="left">1000</th></tr></thead><tbody><tr><td align="left">3</td><td char="." align="char">11</td><td char="." align="char">0.05</td><td char="." align="char">0.04</td><td char="." align="char">0.04</td><td char="." align="char">0.04</td><td char="." align="char">0.04</td><td char="." align="char">0.04</td><td char="." align="char">0.03</td><td char="." align="char">0.03</td></tr><tr><td align="left">4</td><td char="." align="char">43</td><td char="." align="char">0.27</td><td char="." align="char">0.23</td><td char="." align="char">0.22</td><td char="." align="char">0.21</td><td char="." align="char">0.20</td><td char="." align="char">0.19</td><td char="." align="char">0.16</td><td char="." align="char">0.14</td></tr><tr><td align="left">5</td><td char="." align="char">32</td><td char="." align="char">0.59</td><td char="." align="char">0.50</td><td char="." align="char">0.47</td><td char="." align="char">0.45</td><td char="." align="char">0.43</td><td char="." align="char">0.40</td><td char="." align="char">0.36</td><td char="." align="char">0.33</td></tr><tr><td align="left">6</td><td char="." align="char">47</td><td char="." align="char">0.96</td><td char="." align="char">0.78</td><td char="." align="char">0.71</td><td char="." align="char">0.65</td><td char="." align="char">0.64</td><td char="." align="char">0.61</td><td char="." align="char">0.55</td><td char="." align="char">0.52</td></tr><tr><td align="left">7</td><td char="." align="char">49</td><td char="." align="char">1.23</td><td char="." align="char">1.08</td><td char="." align="char">0.98</td><td char="." align="char">0.96</td><td char="." align="char">0.93</td><td char="." align="char">0.88</td><td char="." align="char">0.79</td><td char="." align="char">0.72</td></tr><tr><td align="left">8</td><td char="." align="char">60</td><td char="." align="char">1.70</td><td char="." align="char">1.52</td><td char="." align="char">1.35</td><td char="." align="char">1.29</td><td char="." align="char">1.27</td><td char="." align="char">1.23</td><td char="." align="char">1.16</td><td char="." align="char">1.02</td></tr><tr><td align="left">9</td><td char="." align="char">138</td><td char="." align="char">1.89</td><td char="." align="char">1.71</td><td char="." align="char">1.63</td><td char="." align="char">1.56</td><td char="." align="char">1.52</td><td char="." align="char">1.48</td><td char="." align="char">1.38</td><td char="." align="char">1.26</td></tr><tr><td align="left">10</td><td char="." align="char">60</td><td char="." align="char">1.97</td><td char="." align="char">1.81</td><td char="." align="char">1.73</td><td char="." align="char">1.67</td><td char="." align="char">1.61</td><td char="." align="char">1.56</td><td char="." align="char">1.52</td><td char="." align="char">1.39</td></tr><tr><td align="left">11</td><td char="." align="char">62</td><td char="." align="char">2.33</td><td char="." align="char">2.20</td><td char="." align="char">2.08</td><td char="." align="char">2.04</td><td char="." align="char">2.00</td><td char="." align="char">1.98</td><td char="." align="char">1.88</td><td char="." align="char">1.70</td></tr><tr><td align="left">12</td><td char="." align="char">40</td><td char="." align="char">2.43</td><td char="." align="char">2.25</td><td char="." align="char">2.15</td><td char="." align="char">2.09</td><td char="." align="char">2.06</td><td char="." align="char">2.03</td><td char="." align="char">1.85</td><td char="." align="char">1.71</td></tr><tr><td align="left">13</td><td char="." align="char">50</td><td char="." align="char">2.66</td><td char="." align="char">2.43</td><td char="." align="char">2.40</td><td char="." align="char">2.29</td><td char="." align="char">2.25</td><td char="." align="char">2.22</td><td char="." align="char">2.11</td><td char="." align="char">1.95</td></tr><tr><td align="left">14</td><td char="." align="char">45</td><td char="." align="char">3.05</td><td char="." align="char">2.89</td><td char="." align="char">2.75</td><td char="." align="char">2.64</td><td char="." align="char">2.60</td><td char="." align="char">2.56</td><td char="." align="char">2.44</td><td char="." align="char">2.28</td></tr><tr><td align="left">15</td><td char="." align="char">33</td><td char="." align="char">2.84</td><td char="." align="char">2.68</td><td char="." align="char">2.60</td><td char="." align="char">2.58</td><td char="." align="char">2.56</td><td char="." align="char">2.56</td><td char="." align="char">2.47</td><td char="." align="char">2.35</td></tr><tr><td align="left">16</td><td char="." align="char">29</td><td char="." align="char">2.95</td><td char="." align="char">2.75</td><td char="." align="char">2.71</td><td char="." align="char">2.66</td><td char="." align="char">2.61</td><td char="." align="char">2.55</td><td char="." align="char">2.48</td><td char="." align="char">2.38</td></tr><tr><td align="left">17</td><td char="." align="char">12</td><td char="." align="char">2.95</td><td char="." align="char">2.72</td><td char="." align="char">2.66</td><td char="." align="char">2.56</td><td char="." align="char">2.53</td><td char="." align="char">2.50</td><td char="." align="char">2.37</td><td char="." align="char">2.31</td></tr><tr><td align="left">18</td><td char="." align="char">25</td><td char="." align="char">3.13</td><td char="." align="char">3.03</td><td char="." align="char">2.98</td><td char="." align="char">2.93</td><td char="." align="char">2.91</td><td char="." align="char">2.85</td><td char="." align="char">2.74</td><td char="." align="char">2.55</td></tr><tr><td align="left">19</td><td char="." align="char">21</td><td char="." align="char">2.66</td><td char="." align="char">2.61</td><td char="." align="char">2.46</td><td char="." align="char">2.38</td><td char="." align="char">2.30</td><td char="." align="char">2.29</td><td char="." align="char">2.17</td><td char="." align="char">2.03</td></tr><tr><td align="left">20</td><td char="." align="char">16</td><td char="." align="char">3.52</td><td char="." align="char">3.28</td><td char="." align="char">3.25</td><td char="." align="char">3.14</td><td char="." align="char">3.07</td><td char="." align="char">3.04</td><td char="." align="char">2.92</td><td char="." align="char">2.85</td></tr><tr><td align="left">21</td><td char="." align="char">21</td><td char="." align="char">3.47</td><td char="." align="char">3.24</td><td char="." align="char">3.11</td><td char="." align="char">3.05</td><td char="." align="char">2.99</td><td char="." align="char">2.98</td><td char="." align="char">2.88</td><td char="." align="char">2.74</td></tr><tr><td align="left">22</td><td char="." align="char">21</td><td char="." align="char">2.70</td><td char="." align="char">2.54</td><td char="." align="char">2.44</td><td char="." align="char">2.43</td><td char="." align="char">2.42</td><td char="." align="char">2.39</td><td char="." align="char">2.35</td><td char="." align="char">2.23</td></tr><tr><td align="left">23</td><td char="." align="char">10</td><td char="." align="char">3.32</td><td char="." align="char">3.27</td><td char="." align="char">3.07</td><td char="." align="char">3.01</td><td char="." align="char">3.00</td><td char="." align="char">3.00</td><td char="." align="char">2.78</td><td char="." align="char">2.74</td></tr><tr><td align="left">24</td><td char="." align="char">17</td><td char="." align="char">3.37</td><td char="." align="char">3.24</td><td char="." align="char">3.13</td><td char="." align="char">3.11</td><td char="." align="char">3.08</td><td char="." align="char">3.03</td><td char="." align="char">2.92</td><td char="." align="char">2.74</td></tr><tr><td align="left">25</td><td char="." align="char">15</td><td char="." align="char">2.93</td><td char="." align="char">2.78</td><td char="." align="char">2.72</td><td char="." align="char">2.71</td><td char="." align="char">2.62</td><td char="." align="char">2.51</td><td char="." align="char">2.40</td><td char="." align="char">2.32</td></tr><tr><td align="left">26</td><td char="." align="char">10</td><td char="." align="char">3.06</td><td char="." align="char">2.97</td><td char="." align="char">2.89</td><td char="." align="char">2.77</td><td char="." align="char">2.73</td><td char="." align="char">2.72</td><td char="." align="char">2.67</td><td char="." align="char">2.62</td></tr><tr><td align="left">27</td><td char="." align="char">10</td><td char="." align="char">4.35</td><td char="." align="char">4.14</td><td char="." align="char">4.12</td><td char="." align="char">3.95</td><td char="." align="char">3.89</td><td char="." align="char">3.89</td><td char="." align="char">3.69</td><td char="." align="char">3.51</td></tr><tr><td align="left">28</td><td char="." align="char">14</td><td char="." align="char">3.32</td><td char="." align="char">3.16</td><td char="." align="char">3.10</td><td char="." align="char">3.05</td><td char="." align="char">2.96</td><td char="." align="char">2.93</td><td char="." align="char">2.87</td><td char="." align="char">2.68</td></tr><tr><td align="left">29</td><td char="." align="char">14</td><td char="." align="char">4.63</td><td char="." align="char">4.45</td><td char="." align="char">4.31</td><td char="." align="char">4.24</td><td char="." align="char">4.24</td><td char="." align="char">4.08</td><td char="." align="char">3.98</td><td char="." align="char">3.76</td></tr><tr><td align="left">30</td><td char="." align="char">5</td><td char="." align="char">2.90</td><td char="." align="char">2.87</td><td char="." align="char">2.82</td><td char="." align="char">2.72</td><td char="." align="char">2.72</td><td char="." align="char">2.71</td><td char="." align="char">2.64</td><td char="." align="char">2.64</td></tr><tr><td align="left">All</td><td char="." align="char">910</td><td char="." align="char">2.20</td><td char="." align="char">2.04</td><td char="." align="char">1.95</td><td char="." align="char">1.90</td><td char="." align="char">1.86</td><td char="." align="char">1.83</td><td char="." align="char">1.73</td><td char="." align="char">1.62</td></tr></tbody></table></table-wrap>
<fig id="Fig1"><label>Fig. 1</label><caption><p>The observed frequencies of the peptides with different lengths in the test set, whose numbers are also shown in Tables <xref rid="Tab1" ref-type="table">1</xref>, <xref rid="Tab2" ref-type="table">2</xref>, <xref rid="Tab3" ref-type="table">3</xref> and <xref rid="Tab4" ref-type="table">4</xref>
</p></caption><graphic xlink:href="13321_2017_246_Fig1_HTML" id="MO1999"/></fig>
</p></sec><sec id="Sec4"><title>Rotamer library construction</title><p id="Par7">We have constructed two backbone-dependent rotamer libraries for peptide model building. The first library is called single-letter library, in which each rotamer consists of one amino acid residue (see Fig. <xref rid="Fig2" ref-type="fig">2</xref>a for an example). Therefore, we have a total of 20 single-letter libraries corresponding to 20 types of amino acids. They were used to build the side chain of an amino acid if only its backbone is available. Specifically, for each of the 20 amino acid types, all its residue conformations from the training database of 1821 peptides were aligned according to their N, CA, and C backbone atoms, and clustered using the root mean square deviation (RMSD) of all the heavy atoms of backbone and side chains. Two conformations were grouped into the same cluster if they have an RMSD of &#x0003c;&#x000a0;0.5&#x000a0;&#x000c5;, resulting in multiple clusters for an amino acid type. For each cluster, the conformer including both backbone and side chain with the highest resolution was selected as a representative rotamer of the corresponding amino acid type. Dividing the number of conformations in a cluster by the total number of conformations for an amino acid type gives the probability of the rotamer for the amino acid type. The final number of conformers for an amino acid depends on its type. There are as few as six conformers for ALA and as many as 1075 conformers for ARG in the rotamer libraries.<fig id="Fig2"><label>Fig. 2</label><caption><p>Examples of the <bold>a</bold> pure-rotamer and <bold>b</bold> C-rotamer libraries for amino acid PHE and <bold>c</bold> the helix fragment library with 16 amino acids</p></caption><graphic xlink:href="13321_2017_246_Fig2_HTML" id="MO2"/></fig>
</p><p id="Par8">The second rotamer library is a two-letter library, in which each rotamer is based on two consecutive amino acid residues (i.e. a dipeptide). The generating method for the two-letter library is similar to that for the one-letter library except for two aspects. One is that the rotamer for the two-letter library is based on dipeptides. For the first residue of a dipeptide conformation, only its backbone atoms (i.e. N, CA, C, O) was kept, which we call the HEAD of the dipeptide. The other is that the alignment between two dipeptide conformations is based on their HEAD atoms during the clustering. If two dipeptide conformations have an RMSD of less than 0.5&#x000a0;&#x000c5;, they are grouped into the same cluster. For each cluster of a certain dipeptide type, the conformer with the higher resolution is selected as a representative rotamer of the two-letter or dipeptide type. Therefore, the rotamer in a two-letter library has one more HEAD than that in a single-letter library. Correspondingly, two-letter rotamers are more spread in space than single-letter rotamers (Fig. <xref rid="Fig2" ref-type="fig">2</xref>a, b). As the two-letter library constructed by this way is used to add a residue at the C-terminal of a peptide, we call it the C-rotamer library. Similarly, we have also constructed the N-rotamer library, in which the superimposition during clustering was based on the TAIL of dipeptides (i.e. the backbone atoms of the second residue).</p></sec><sec id="Sec5"><title>Helix library construction</title><p id="Par9">In addition to rotamer libraries, we have also constructed a fragment library for helical structures with different lengths, where the secondary structure information was calculated using the program KSDSSP [<xref ref-type="bibr" rid="CR40">40</xref>]. Because helix structures are relatively stable and do not much depend on sequences, we only kept the backbone atoms (i.e. N, CA, C, O) for the helix library. Side chains will only be added during model building, as described in the following section. Specifically, for a given peptide length, we have collected all the helix structures from the training database of 1821 peptides. All the helix conformations with the same length were then superimposed onto one another and clustered according to the RMSD of backbone atoms. If two helix conformations have an RMSD of less than 0.5&#x000a0;&#x000c5;, they were grouped into the same cluster. It should be noted that the number of helical examples in the training set tended to be more limited for longer helices and thus resulted in fewer clusters. Depending on the lengths, the sizes of the libraries range from two clusters for the 28-residue helix to 37 clusters for the seven-residue helix. For each cluster of a helix length, the helix structure with the higher resolution was selected as a representative conformer of the helix length. For consistency, the backbone atoms (i.e. N, C, and CA) of the first residue of a helix fragment is called the HEAD of the helix, and the backbone atoms (i.e. N, C, and CA) of the last residue is called the TAIL of the helix fragment.</p></sec><sec id="Sec6"><title>Peptide structure modeling</title><p id="Par10">With the constructed rotamer and helix libraries, our MODPEP algorithm can automatically build the three-dimensional structure of a peptide from scratch by assembling amino acids or helix fragments one by one. Specifically, given a peptide sequence, the program PSIPRED was first used to predict the second structure type (i.e. C-coil, S-sheet, or H-helix) of its amino acids [<xref ref-type="bibr" rid="CR41">41</xref>]. Then, a rotamer was randomly selected from the single-letter library for the first amino acid of the sequence. If three or more consecutive amino acids including the current one on the sequence all had a secondary structure type of H-helix, a helix fragment was built by selecting a helix template from the helix library according to the probability of the helix structure and aligning the HEAD of the helix fragment with the corresponding backbone atoms of the current residue. The corresponding side chains for the helix fragment were built using the single-letter rotamer libraries according to the probability of its amino acid types. For all other cases that the next amino acid to be modeled has a secondary structure of C-coil or S-sheet type, the residue structure was stochastically built by selecting a rotamer from the C-rotamer library according to the probability of the rotamer and aligning the HEAD of the rotamer with the backbone of the current residue. The newly added amino acid or helix fragment was subject to an atomic clash checking. If there are severe clashes, the newly added rotamer or fragment will be discarded and a structure rebuilding process will be tried. The process was repeated until the last amino acid of the sequence was reached.</p><p id="Par11">It should be noted that here the peptide 3D conformation of full length was built from N-terminal to C-terminal based on the C-rotamer and helix fragment libraries. However, the peptide structure can also be built from C-terminal to N-terminal by using the N-rotamer and helix fragment libraries. Our MODPEP algorithm can also construct the full peptide 3D structure for a partial one by building residues at both C-terminal and N-terminal. The peptide structure building process is very fast and can normally generate 100 peptide conformations in less than one second.</p><p id="Par12">For computational efficiency, we did not apply a complicated scoring function during model building and do an energy minimization for the generated models. Therefore, there might be a few bad bendings or torsional angles in the generated models. However, this does not affect the accuracy of the predicted models. As shown in a comparison between the original structures and the refined models by the ff14SB force field [<xref ref-type="bibr" rid="CR42">42</xref>] of AMBER (version 14) [<xref ref-type="bibr" rid="CR43">43</xref>], the refined ones are even slightly worse than the original models in terms of accuracy, although the refined models have a better energy scores than the original models (Fig. <xref rid="Fig3" ref-type="fig">3</xref>). The worse accuracy of the refined models compared to the original models can be understood because we are predicting the conformations of protein-bound peptides. The optimization of a peptide without its bound protein partner would drive the model further away from the protein-bound conformations, although the energy can also be minimized. Therefore, we have left the energy minimization of the generated models to users in real applications when they have a specific protein partner to be bound by the peptide.<fig id="Fig3"><label>Fig. 3</label><caption><p>The accuracy distribution in terms of RMSD (<bold>a</bold>) and the energy difference (<inline-formula id="IEq4"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\Delta E=E_{\mathrm{after}}-E_{\mathrm{before}}$$\end{document}</tex-math><mml:math id="M2"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:mi>E</mml:mi><mml:mo>=</mml:mo><mml:msub><mml:mi>E</mml:mi><mml:mi mathvariant="normal">after</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:msub><mml:mi>E</mml:mi><mml:mi mathvariant="normal">before</mml:mi></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_246_Article_IEq4.gif"/></alternatives></inline-formula>) distribution (<bold>b</bold>) of the peptide models before and after minimization with AMBER for the peptides with 10 amino acids</p></caption><graphic xlink:href="13321_2017_246_Fig3_HTML" id="MO3"/></fig>
</p></sec><sec id="Sec7"><title>Evaluation criteria</title><p id="Par13">The quality for a generated peptide model was measured by the root mean square deviation (RMSD) between the model and the experimentally determined peptide structures. Here, the RMSD was calculated based on the C<italic>&#x003b1;</italic> atoms of the peptide (cRMSD) after optimal superimposition of the two structures, as used in PEP-FOLD [<xref ref-type="bibr" rid="CR44">44</xref>]. This is the default quality assessment parameter, unless otherwise specified. In addition, we have also calculated the RMSD of backbone heavy atoms (bRMSD) to evaluate the robustness of our approach and the RMSD of all heavy atoms (aRMSD) to check the capability of our method in predicting side chains.</p><p id="Par14">For an ensemble of <italic>N</italic> conformations generated for a peptide, the accuracy of the ensemble was represented by the RMSD of the best-fit conformation in the ensemble compared to the experimentally observed structure. Therefore, a smaller RMSD means a higher accuracy. The accuracy depends on the number of considered conformations in the ensemble, i.e. the ensemble size.</p><p id="Par15">It was found that a conformer with an RMSD of less than 1.0&#x000a0;&#x000c5; was necessary for achieving a correct binding mode in molecular docking for compound ligands [<xref ref-type="bibr" rid="CR45">45</xref>]. In other words, the generated conformer with an RMSD of less than 1.0&#x000a0;&#x000c5; is similar to the experimental bound structure for short peptides from the perspective of chemistry. For medium-size peptides, an RMSD of less than 2.0&#x000a0;&#x000c5; can be considered as native-like conformations [<xref ref-type="bibr" rid="CR44">44</xref>]. In addition, RMSD is also size-dependent [<xref ref-type="bibr" rid="CR46">46</xref>, <xref ref-type="bibr" rid="CR47">47</xref>], and larger proteins tend to give a larger RMSD for the similar accuracy [<xref ref-type="bibr" rid="CR48">48</xref>]. Therefore, we have used a size-dependent RMSD cutoff as a criterion for successful predictions in the present study [<xref ref-type="bibr" rid="CR48">48</xref>]<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\begin{aligned} {\mathrm{rmsd}}_{\mathrm{C}}({\mathrm{n}})=1.0\times [1+\ln (n/n_0)] \end{aligned}$$\end{document}</tex-math><mml:math id="M4" display="block"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="right"><mml:mrow><mml:msub><mml:mi mathvariant="normal">rmsd</mml:mi><mml:mi mathvariant="normal">C</mml:mi></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="normal">n</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mn>1.0</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>1</mml:mn><mml:mo>+</mml:mo><mml:mo>ln</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>n</mml:mi><mml:mo stretchy="false">/</mml:mo><mml:msub><mml:mi>n</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math><graphic xlink:href="13321_2017_246_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula>where <italic>n</italic> stands for the peptide length and <inline-formula id="IEq6"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$n_0$$\end{document}</tex-math><mml:math id="M6"><mml:msub><mml:mi>n</mml:mi><mml:mn>0</mml:mn></mml:msub></mml:math><inline-graphic xlink:href="13321_2017_246_Article_IEq6.gif"/></alternatives></inline-formula> was set as 3. The RMSD cutoff ranges from 1.0&#x000a0;&#x000c5; for the peptides of 3 residues to 3.3&#x000a0;&#x000c5; for the peptides of 30 residues. Thus, given a peptide of <italic>n</italic> residues, the peptide modeling was defined as a success if the accuracy of the ensemble is less than <inline-formula id="IEq7"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${\mathrm{rmsd}}_{\mathrm{C}}({\mathrm{n}})$$\end{document}</tex-math><mml:math id="M8"><mml:mrow><mml:msub><mml:mi mathvariant="normal">rmsd</mml:mi><mml:mi mathvariant="normal">C</mml:mi></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="normal">n</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_246_Article_IEq7.gif"/></alternatives></inline-formula>.</p></sec><sec id="Sec8"><title>Comparison with other methods</title><p id="Par16">Comparing our MODPEP algorithm with other methods is difficult because few approaches have been developed for modeling protein-bound peptide structures, although there are published methods for conformational sampling of free peptides. Here, we have selected three state-of-art conformer generation algorithms, which are PEP-FOLD3 [<xref ref-type="bibr" rid="CR49">49</xref>], RDKit (version 2016.09.4) [<xref ref-type="bibr" rid="CR50">50</xref>], and Balloon (version 1.6.4.1258) [<xref ref-type="bibr" rid="CR51">51</xref>], respectively. PEP-FOLD3 is a novel approach for de novo prediction of peptides and miniproteins. It assembles the peptide structure using a greedy procedure with Hidden Markov Model-derived structural alphabets [<xref ref-type="bibr" rid="CR44">44</xref>]. RDKit adopts a distance geometry approach to generate conformers of a ligand. The resulting conformers were then optimized with the UFF force field [<xref ref-type="bibr" rid="CR30">30</xref>, <xref ref-type="bibr" rid="CR52">52</xref>]. It was recently shown that RDKit was one of the best conformer ensemble generators on a high-quality benchmark of protein-bound ligand conformations [<xref ref-type="bibr" rid="CR53">53</xref>]. Balloon is a method of conformer ensemble generation for ligands that aims to reproduce protein-bound ligand conformations [<xref ref-type="bibr" rid="CR32">32</xref>]. It is also an implementation of distance geometry like RDKit. For both RDKit and Balloon, the code was downloaded from the authors&#x02019; web sites and evaluated locally. During the evaluation, the default parameters were used except that the number of conformers to be generated was set as 200. For PEP-FOLD3, because its code is not available for download, we obtained the test results by submitting the peptide sequences to the PEP-FOLD3 web server [<xref ref-type="bibr" rid="CR37">37</xref>].</p></sec></sec><sec id="Sec9"><title>Results and discussion</title><sec id="Sec10"><title>Accuracy</title><p id="Par17">With the constructed rotamer and helix libraries, we were able to model peptide structures using our fast MODPEP algorithm. The capacity of our peptide modeling algorithm in reproducing experimentally determined protein-bound conformations was evaluated on a test set of 910 peptides. For each peptide, we have generated an ensemble of 1000 conformations based on its sequence.</p><p id="Par18">Figure <xref rid="Fig4" ref-type="fig">4</xref> shows the average accuracy of our MODPEP in reproducing experimentally determined conformation as a function of ensemble size. The figure also shows the average accuracies of the peptides of six typical lengths (i.e. 3, 6, 9, 15, 21, and 27 amino acids). The detailed accuracies for several ensemble sizes are listed in Table <xref rid="Tab1" ref-type="table">1</xref>. Several features can be observed from the figure and table. First, the accuracies depend on the peptide length. The shorter peptide gave a better accuracy with the lowest RMSD of 0.03&#x000a0;&#x000c5; for 3-amino acid peptides and the highest RMSD of 3.76&#x000a0;&#x000c5; for 29-amino acid peptides when an ensemble of 1000 conformations were considered (Table <xref rid="Tab1" ref-type="table">1</xref>). Second, the accuracies also depend on the ensemble sizes of generated peptide conformations. Third, the accuracy is not a linear relationship with ensemble size. The accuracy changes faster at the beginning and then slower with the increasing number of conformations. On average, our MODPEP obtained an accuracy of 1.90&#x000a0;&#x000c5; for an ensemble size of 200 and 1.62&#x000a0;&#x000c5; for an ensemble size of 1000.<fig id="Fig4"><label>Fig. 4</label><caption><p>The average accuracies (bold solid line) of the best-fit predictions compared to the experimentally observed conformations as a function of ensemble size for the test set of 910 protein-bound peptides. For reference, the average accuracies for peptides of several typical lengths are shown</p></caption><graphic xlink:href="13321_2017_246_Fig4_HTML" id="MO5"/></fig>
</p><p id="Par19">Figure <xref rid="Fig4" ref-type="fig">4</xref> also shows that there roughly exists a crossover around 50 conformations on the accuracy-ensemble size curves for all peptide lengths. Therefore, an ensemble of 50 conformations for a peptide may be used if the computational resource is limited, though the accuracy always tends to be better for a larger ensemble size. Considering the accuracies for the peptides of all lengths, 200 conformations seem to be a good balance between the accuracy and the ensemble size (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). Therefore, we have used 200 as the default ensemble size for our MODPEP algorithm in the following evaluations, though users can choose to generate more conformations in real applications. It can be observed from Table <xref rid="Tab1" ref-type="table">1</xref> that our MODPEP has an RMSD of 0.04&#x000a0;&#x000c5; for the 3-amino acid peptide and an RMSD of 4.24&#x000a0;&#x000c5; for the 29-amino acid peptide when the default ensemble size of 200 was used.</p><p id="Par20">Figure <xref rid="Fig5" ref-type="fig">5</xref> gives 28 examples of the predicted models with the RMSDs ranging from 0.03 to 2.48&#x000a0;&#x000c5; for the peptides with 3&#x02013;30 amino acids, respectively. It can be seen from the figure that the predicted models overlap with the experimental structures very well. Therefore, the present accuracy of MODPEP is good enough for direct docking calculations for peptides with 3&#x02013;20 amino acids or provides a good starting point of docking&#x000a0;+&#x000a0;MD protocols for peptides with more than 20 amino acids. Nevertheless, MODPEP also failed to give models close to the experimental conformations for some peptides even when an ensemble of 1000 conformations were generated (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>). Several features can be found by examining these failed cases, which can help further improve our MODPEP algorithm. First, all the failed cases are medium or large-size peptides with more than 10 amino acids, as longer peptides tend to be more challenging to be predicted. Second, the secondary structures of some peptides are not correctly predicted by PSIPRED. Third, some peptides form a <italic>&#x003b2;</italic>-sheet structure with its protein partner. In such cases, it is challenging to generate correct <italic>&#x003b2;</italic>-sheet structure based on the peptide alone.<fig id="Fig5"><label>Fig. 5</label><caption><p>Examples of the predicted models for peptides with 3&#x02013;30 amino acids, where each peptide is represented by its PDB code_chain ID. The native structure (magenta) is superimposed onto the predicted model (cyan). The corresponding accuracy is listed in parenthesis</p></caption><graphic xlink:href="13321_2017_246_Fig5_HTML" id="MO6"/></fig>
<fig id="Fig6"><label>Fig. 6</label><caption><p>Examples of the predicted models for several challenging peptides, where each peptide is represented by its PDB code_chain ID. The native structure (magenta) is superimposed on the predicted model (cyan). The corresponding accuracy is listed in parenthesis</p></caption><graphic xlink:href="13321_2017_246_Fig6_HTML" id="MO7"/></fig>
</p><p id="Par21">In addition, to check the statistical accuracy of MODPEP, we have repeated the validating procedure by splitting the data set into training and test sets for 10 runs. As shown in the Additional file <xref rid="MOESM1" ref-type="media">1</xref>, the prediction accuracies for different runs are quite consistent. On average, the standard deviations of the accuracies for 10 validating runs are around 0.02&#x000a0;&#x000c5; for most peptide lengths, supporting the statistically robustness of MODPEP.</p><p id="Par22">To further examine the robustness of MODPEP, we have also calculated the RMSD of generated peptide models based on the backbone and all the heavy atoms, respectively. Table <xref rid="Tab2" ref-type="table">2</xref> lists the average accuracies in terms of the RMSDs of C<italic>&#x003b1;</italic>, backbone, and all-heavy atoms for different peptide lengths when an ensemble of 200 conformations were considered. It can be seen from the table that the C<italic>&#x003b1;</italic> and backbone atoms yielded comparable RMSDs, while the all-heavy atoms gave a significant higher RMSD. This means that the higher RMSD of all-heavy atoms than backbone is due to side chains. The large RMSD induced by side chains can be understood as follows. First, although the backbone of protein is clearly visible in the electron density map at resolution of better than 3&#x000a0;&#x000c5;, the accuracy of side chain positions significantly depends on the resolution [<xref ref-type="bibr" rid="CR39">39</xref>]. Therefore, inclusion of side chains will not only impact the quality of the training set, but also the evaluation for the experimental peptide structures in the test set. Second, side chains tend to have larger induced conformational changes when a peptide binds to its protein partner. It is challenging to predict the positions of side chains without its bound protein. In other words, the conformations of side chains for a peptide are different depending on the protein that the peptide binds to. Namely, compared to the backbone, side chains are more binding-dependent and can only be correctly modeled upon binding. Therefore, we have used the C<italic>&#x003b1;</italic> RMSD as the default parameter to measure the accuracy of generated models in this study, as used in PEP-FOLD [<xref ref-type="bibr" rid="CR44">44</xref>].<table-wrap id="Tab2"><label>Table 2</label><caption><p>The average accuracies of our MODPEP method measured using the C<italic>&#x003b1;</italic> (cRMSD), backbone (bRMSD), and all heavy atoms (aRMSD) for the peptides with different lengths when an ensemble of 200 conformations were considered for each peptide</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" colspan="2">Peptide</th><th align="left" colspan="3">RMSD (&#x000c5;)</th></tr><tr><th align="left">Length</th><th align="left">Number</th><th align="left">cRMSD</th><th align="left">bRMSD</th><th align="left">aRMSD</th></tr></thead><tbody><tr><td align="left">3</td><td char="." align="char">11</td><td char="." align="char">0.04</td><td char="." align="char">0.42</td><td char="." align="char">1.18</td></tr><tr><td align="left">4</td><td char="." align="char">43</td><td char="." align="char">0.21</td><td char="." align="char">0.62</td><td char="." align="char">1.29</td></tr><tr><td align="left">5</td><td char="." align="char">32</td><td char="." align="char">0.45</td><td char="." align="char">0.87</td><td char="." align="char">1.69</td></tr><tr><td align="left">6</td><td char="." align="char">47</td><td char="." align="char">0.65</td><td char="." align="char">1.01</td><td char="." align="char">1.97</td></tr><tr><td align="left">7</td><td char="." align="char">49</td><td char="." align="char">0.96</td><td char="." align="char">1.20</td><td char="." align="char">2.25</td></tr><tr><td align="left">8</td><td char="." align="char">60</td><td char="." align="char">1.29</td><td char="." align="char">1.50</td><td char="." align="char">2.59</td></tr><tr><td align="left">9</td><td char="." align="char">138</td><td char="." align="char">1.56</td><td char="." align="char">1.65</td><td char="." align="char">2.85</td></tr><tr><td align="left">10</td><td char="." align="char">60</td><td char="." align="char">1.67</td><td char="." align="char">1.74</td><td char="." align="char">2.93</td></tr><tr><td align="left">11</td><td char="." align="char">62</td><td char="." align="char">2.04</td><td char="." align="char">2.04</td><td char="." align="char">3.31</td></tr><tr><td align="left">12</td><td char="." align="char">40</td><td char="." align="char">2.09</td><td char="." align="char">2.09</td><td char="." align="char">3.41</td></tr><tr><td align="left">13</td><td char="." align="char">50</td><td char="." align="char">2.29</td><td char="." align="char">2.34</td><td char="." align="char">3.69</td></tr><tr><td align="left">14</td><td char="." align="char">45</td><td char="." align="char">2.64</td><td char="." align="char">2.65</td><td char="." align="char">3.96</td></tr><tr><td align="left">15</td><td char="." align="char">33</td><td char="." align="char">2.58</td><td char="." align="char">2.56</td><td char="." align="char">3.99</td></tr><tr><td align="left">16</td><td char="." align="char">29</td><td char="." align="char">2.66</td><td char="." align="char">2.61</td><td char="." align="char">4.00</td></tr><tr><td align="left">17</td><td char="." align="char">12</td><td char="." align="char">2.56</td><td char="." align="char">2.61</td><td char="." align="char">4.14</td></tr><tr><td align="left">18</td><td char="." align="char">25</td><td char="." align="char">2.93</td><td char="." align="char">2.90</td><td char="." align="char">4.20</td></tr><tr><td align="left">19</td><td char="." align="char">21</td><td char="." align="char">2.38</td><td char="." align="char">2.37</td><td char="." align="char">3.55</td></tr><tr><td align="left">20</td><td char="." align="char">16</td><td char="." align="char">3.14</td><td char="." align="char">2.94</td><td char="." align="char">4.44</td></tr><tr><td align="left">21</td><td char="." align="char">21</td><td char="." align="char">3.05</td><td char="." align="char">2.84</td><td char="." align="char">4.08</td></tr><tr><td align="left">22</td><td char="." align="char">21</td><td char="." align="char">2.43</td><td char="." align="char">2.43</td><td char="." align="char">3.71</td></tr><tr><td align="left">23</td><td char="." align="char">10</td><td char="." align="char">3.01</td><td char="." align="char">3.06</td><td char="." align="char">4.37</td></tr><tr><td align="left">24</td><td char="." align="char">17</td><td char="." align="char">3.11</td><td char="." align="char">2.96</td><td char="." align="char">4.33</td></tr><tr><td align="left">25</td><td char="." align="char">15</td><td char="." align="char">2.71</td><td char="." align="char">2.65</td><td char="." align="char">3.69</td></tr><tr><td align="left">26</td><td char="." align="char">10</td><td char="." align="char">2.77</td><td char="." align="char">3.05</td><td char="." align="char">4.46</td></tr><tr><td align="left">27</td><td char="." align="char">10</td><td char="." align="char">3.95</td><td char="." align="char">4.18</td><td char="." align="char">5.49</td></tr><tr><td align="left">28</td><td char="." align="char">14</td><td char="." align="char">3.05</td><td char="." align="char">3.16</td><td char="." align="char">4.43</td></tr><tr><td align="left">29</td><td char="." align="char">14</td><td char="." align="char">4.24</td><td char="." align="char">4.07</td><td char="." align="char">5.33</td></tr><tr><td align="left">30</td><td char="." align="char">5</td><td char="." align="char">2.72</td><td char="." align="char">2.75</td><td char="." align="char">3.95</td></tr><tr><td align="left">All</td><td char="." align="char">910</td><td char="." align="char">1.90</td><td char="." align="char">1.99</td><td char="." align="char">3.18</td></tr></tbody></table></table-wrap>
</p></sec><sec id="Sec11"><title>Success rates</title><p id="Par23">In addition to evaluating the accuracy of MODPEP, we have also calculated the success rate, i.e. the percentage of peptides in the test set that are successfully reproduced within the corresponding RMSD cutoff defined in Eq. <xref rid="Equ1" ref-type="">1</xref>. The corresponding results are shown in Table <xref rid="Tab3" ref-type="table">3</xref>. It can be seen from the table that the success rates significantly depend on the peptide lengths. For example, for the peptides with 3&#x02013;10 amino acids, MODPEP reproduced more than 95% of protein-bound peptide conformations when an ensemble of 200 models were considered (Table <xref rid="Tab3" ref-type="table">3</xref>), while for the peptides with more than 10 amino acids, the success rates dropped below 80%. On average, our algorithm gave a success rate of 74.3% when an ensemble of 200 conformations were considered (Table <xref rid="Tab3" ref-type="table">3</xref>).<table-wrap id="Tab3"><label>Table 3</label><caption><p>The success rates of our MODPEP method in reproducing protein-bound conformations for the peptides with different lengths when various ensemble sizes were considered</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" colspan="2">Peptide</th><th align="left" colspan="8">Success rate (%)</th></tr><tr><th align="left">Length</th><th align="left">Number</th><th align="left">50</th><th align="left">100</th><th align="left">150</th><th align="left">200</th><th align="left">250</th><th align="left">300</th><th align="left">500</th><th align="left">1000</th></tr></thead><tbody><tr><td align="left">3</td><td char="." align="char">11</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td></tr><tr><td align="left">4</td><td char="." align="char">43</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td></tr><tr><td align="left">5</td><td char="." align="char">32</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td></tr><tr><td align="left">6</td><td char="." align="char">47</td><td char="." align="left">97.9</td><td char="." align="left">97.9</td><td char="." align="left">97.9</td><td char="." align="left">97.9</td><td char="." align="left">97.9</td><td char="." align="left">97.9</td><td char="." align="left">97.9</td><td char="." align="left">97.9</td></tr><tr><td align="left">7</td><td char="." align="char">49</td><td char="." align="left">98.0</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td><td char="." align="left">100</td></tr><tr><td align="left">8</td><td char="." align="char">60</td><td char="." align="left">83.3</td><td char="." align="left">91.7</td><td char="." align="left">93.3</td><td char="." align="left">95.0</td><td char="." align="left">96.7</td><td char="." align="left">96.7</td><td char="." align="left">96.7</td><td char="." align="left">96.7</td></tr><tr><td align="left">9</td><td char="." align="char">138</td><td align="left">75.4</td><td align="left">92.8</td><td align="left">96.4</td><td align="left">97.8</td><td align="left">98.6</td><td align="left">98.6</td><td align="left">98.6</td><td align="left">99.3</td></tr><tr><td align="left">10</td><td char="." align="char">60</td><td align="left">71.7</td><td align="left">81.7</td><td align="left">90.0</td><td align="left">96.7</td><td align="left">96.7</td><td align="left">98.3</td><td align="left">98.3</td><td align="left">98.3</td></tr><tr><td align="left">11</td><td char="." align="char">62</td><td align="left">45.2</td><td align="left">58.1</td><td align="left">69.4</td><td align="left">77.4</td><td align="left">79.0</td><td align="left">80.6</td><td align="left">82.3</td><td align="left">90.3</td></tr><tr><td align="left">12</td><td char="." align="char">40</td><td align="left">37.5</td><td align="left">42.5</td><td align="left">47.5</td><td align="left">52.5</td><td align="left">55.0</td><td align="left">57.5</td><td align="left">80.0</td><td align="left">92.5</td></tr><tr><td align="left">13</td><td char="." align="char">50</td><td align="left">38.0</td><td align="left">48.0</td><td align="left">50.0</td><td align="left">58.0</td><td align="left">62.0</td><td align="left">64.0</td><td align="left">74.0</td><td align="left">80.0</td></tr><tr><td align="left">14</td><td char="." align="char">45</td><td align="left">31.1</td><td align="left">33.3</td><td align="left">42.2</td><td align="left">48.9</td><td align="left">51.1</td><td align="left">53.3</td><td align="left">62.2</td><td align="left">80.0</td></tr><tr><td align="left">15</td><td char="." align="char">33</td><td align="left">30.3</td><td align="left">36.4</td><td align="left">36.4</td><td align="left">36.4</td><td align="left">36.4</td><td align="left">36.4</td><td align="left">42.4</td><td align="left">57.6</td></tr><tr><td align="left">16</td><td char="." align="char">29</td><td align="left">27.6</td><td align="left">34.5</td><td align="left">34.5</td><td align="left">41.4</td><td align="left">44.8</td><td align="left">44.8</td><td align="left">48.3</td><td align="left">51.7</td></tr><tr><td align="left">17</td><td char="." align="char">12</td><td align="left">41.7</td><td align="left">41.7</td><td align="left">41.7</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">58.3</td><td align="left">58.3</td></tr><tr><td align="left">18</td><td char="." align="char">25</td><td align="left">36.0</td><td align="left">40.0</td><td align="left">40.0</td><td align="left">44.0</td><td align="left">44.0</td><td align="left">44.0</td><td align="left">48.0</td><td align="left">52.0</td></tr><tr><td align="left">19</td><td char="." align="char">21</td><td align="left">57.1</td><td align="left">61.9</td><td align="left">61.9</td><td align="left">61.9</td><td align="left">66.7</td><td align="left">66.7</td><td align="left">66.7</td><td align="left">71.4</td></tr><tr><td align="left">20</td><td char="." align="char">16</td><td align="left">43.8</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td></tr><tr><td align="left">21</td><td char="." align="char">21</td><td align="left">33.3</td><td align="left">42.9</td><td align="left">42.9</td><td align="left">42.9</td><td align="left">42.9</td><td align="left">47.6</td><td align="left">57.1</td><td align="left">57.1</td></tr><tr><td align="left">22</td><td char="." align="char">21</td><td align="left">57.1</td><td align="left">61.9</td><td align="left">61.9</td><td align="left">61.9</td><td align="left">61.9</td><td align="left">61.9</td><td align="left">61.9</td><td align="left">71.4</td></tr><tr><td align="left">23</td><td char="." align="char">10</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td></tr><tr><td align="left">24</td><td char="." align="char">17</td><td align="left">41.2</td><td align="left">41.2</td><td align="left">47.1</td><td align="left">47.1</td><td align="left">47.1</td><td align="left">47.1</td><td align="left">47.1</td><td align="left">52.9</td></tr><tr><td align="left">25</td><td char="." align="char">15</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">66.7</td><td align="left">66.7</td></tr><tr><td align="left">26</td><td char="." align="char">10</td><td align="left">40.0</td><td align="left">40.0</td><td align="left">40.0</td><td align="left">40.0</td><td align="left">40.0</td><td align="left">40.0</td><td align="left">40.0</td><td align="left">40.0</td></tr><tr><td align="left">27</td><td char="." align="char">10</td><td align="left">20.0</td><td align="left">30.0</td><td align="left">30.0</td><td align="left">30.0</td><td align="left">30.0</td><td align="left">30.0</td><td align="left">30.0</td><td align="left">30.0</td></tr><tr><td align="left">28</td><td char="." align="char">14</td><td align="left">35.7</td><td align="left">42.9</td><td align="left">42.9</td><td align="left">42.9</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">50.0</td><td align="left">57.1</td></tr><tr><td align="left">29</td><td char="." align="char">14</td><td align="left">21.4</td><td align="left">21.4</td><td align="left">21.4</td><td align="left">21.4</td><td align="left">21.4</td><td align="left">21.4</td><td align="left">21.4</td><td align="left">28.6</td></tr><tr><td align="left">30</td><td char="." align="char">5</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td><td align="left">60.0</td></tr><tr><td align="left">All</td><td char="." align="char">910</td><td align="left">61.6</td><td align="left">68.7</td><td align="left">71.5</td><td align="left">74.3</td><td align="left">75.4</td><td align="left">76.0</td><td align="left">79.0</td><td align="left">82.9</td></tr></tbody></table></table-wrap>
</p><p id="Par24">The success rates also depend on the ensemble sizes of generated conformations (Table <xref rid="Tab3" ref-type="table">3</xref>). For example, for the peptides with 12 amino acids, the success rate in reproducing experimental structures is only 37.5% when an ensemble of 50 conformations were considered, but the success rate reached to 92.5% if an ensemble of 1000 conformations were considered (Table <xref rid="Tab3" ref-type="table">3</xref>). The success rate also has a non-linear relationship with the ensemble size of generated conformations. The success rate increases fast at small ensemble sizes and become more stable at large ensemble sizes (Fig. <xref rid="Fig7" ref-type="fig">7</xref>). The algorithm achieved a good balance between the success rate and the ensemble size when 200 conformations were considered. With this ensemble size, peptides of most lengths have a success rate close to its maximum value (Table <xref rid="Tab3" ref-type="table">3</xref>).<fig id="Fig7"><label>Fig. 7</label><caption><p>The success rates (bold solid lines) in reproducing experimentally determined protein-bound peptide conformations as a function of ensemble size. For reference, the results for the peptides of several lengths are shown</p></caption><graphic xlink:href="13321_2017_246_Fig7_HTML" id="MO8"/></fig>
</p><p id="Par25">In addition, we have examined the impact of the secondary structure types on the quality of generated models. It was defined that if a peptide contained a <italic>&#x003b2;</italic>-sheet structure, it was characterized as the SHEET type; otherwise, it was classified as the HELIX type if the peptide contained a helix structure; the rest peptides belonged to the COIL type. Of 910 peptides in the test set, there are 304 peptides of HELIX type, 129 peptides of SHEET type, and 477 peptides of COIL type. MODPEP obtained a success rate of 83.6, 73.0, and 42.6% for the peptides of COIL, HELIX, and SHEET types, respectively, when an ensemble of 200 conformations were considered. This trend may be understood because MODPEP depends on the secondary structure information predicted by PSIPRED. Indeed, the accuracies of secondary structures prediction by PSIPRED showed a similar trend and had an average success rate of 85.1, 78.9, 53.5% for the secondary structures of COIL, HELIX, and SHEET types, respectively.</p></sec><sec id="Sec12"><title>Comparative evaluations</title><p id="Par26">We further compared our MODPEP with three stat-of-art conformational sampling approaches, PEP-FOLD3, Balloon, and RDKit. It should be noted that PEP-FOLD3, Balloon, and RDKit are not designed for generation of protein-bound peptide conformations. Therefore, the present comparison is to provide a performance reference more than a comparative evaluation.</p><p id="Par27">Figure <xref rid="Fig8" ref-type="fig">8</xref> shows the average accuracy and success rate as a function of ensemble size by the four conformational sampling methods, MODPEP, PEP-FOLD3, RDKit, and Balloon, on the test set of 910 peptides. It can be seen from the figure that our method MODPEP obtained a much better performance than RDKit, PEP-FOLD3, and Balloon in terms of both accuracy and success rate. For example, MODPEP had an accuracy of 2.20, 2.04, and 1.90&#x000a0;&#x000c5;, compared to 2.80, 2.71, and 2.63&#x000a0;&#x000c5; for RDKit, 3.76, 3.54, and 3.28&#x000a0;&#x000c5; for PEP-FOLD3, and 4.28, 4.17, and 4.04&#x000a0;&#x000c5; for Balloon when ensembles of 50, 100, and 200 conformations were considered, respectively (Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref>a). Likewise, MODPEP reproduced the most protein-bound peptide conformations with an average success rate of 74.3%, followed by 46.8% for RDKit, 30.1% for PEP-FOLD3, and 19.2% for Balloon when an ensemble of 200 conformations were considered (Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref>b).<fig id="Fig8"><label>Fig. 8</label><caption><p>Comparison of the performances for four conformer generation methods, MODPEP, PEP-FOLD3, RDKit, and Balloon, on the test set of 910 protein-bound peptides. For each peptide, 200 conformers were generated per method. <bold>a</bold> Accuracy versus ensemble size, <bold>b</bold> success rate versus ensemble size</p></caption><graphic xlink:href="13321_2017_246_Fig8_HTML" id="MO9"/></fig>
</p><p id="Par28">Table <xref rid="Tab4" ref-type="table">4</xref> and Fig.&#x000a0;<xref rid="Fig9" ref-type="fig">9</xref> show the average accuracies and success rates of MODPEP, RDKit, PEP-FOLD3, and Balloon for peptides with different lengths, respectively. Similar trends in the performances for the four methods can be observed in both accuracy and success rate. Namely, overall, MODPEP performed the best among the four methods, followed by RDKit, PEP-FOLD3, and Balloon. The relative performances of PEP-FOLD3 and RDKit/Balloon depended on the lengths of peptides. For short peptides with 3&#x02013;8 amino acids, RDKit and Balloon performed better than PEP-FOLD3, while for longer peptides of more than 9 amino acids, PEP-FOLD3 performed better than RDKit and Balloon. For example, RDKit and Balloon had an average accuracy of 0.57 and 0.96&#x000a0;&#x000c5; and a success rate of 100 and 100% for peptides of five amino acids, compared to 2.00&#x000a0;&#x000c5; and 31.2% for PEP-FOLD3. However, for peptides with 17 amino acids, PEP-FOLD3 obtained an accuracy of 3.50&#x000a0;&#x000c5; and a success rate of 50%, while RDKit and Balloon only had an accuracy of 6.33 and 5.41&#x000a0;&#x000c5; and did not reproduce any correct conformations. These results indicate that short peptides with less than 9 amino acids behave more like ligands than proteins and therefore resulted in a fair performance for ligand conformer generator methods like RDKit and Balloon. In contrast, owing to our de novo strategy of residue assembling from the rotamer library, MODPEP can achieve good performances for peptides of all lengths (Table <xref rid="Tab4" ref-type="table">4</xref>).<table-wrap id="Tab4"><label>Table 4</label><caption><p>The average accuracies and success rates of MODPEP, PEP-FOLD3, Balloon, and RDKit in reproducing protein-bound conformations for the peptides with different lengths when an ensemble of 200 conformations were considered for each peptide</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" colspan="2">Peptide</th><th align="left" colspan="4">cRMSD (&#x000c5;)</th><th align="left" colspan="4">Success rate (%)</th></tr><tr><th align="left">Length</th><th align="left">Number</th><th align="left">MODPEP</th><th align="left">PEP-FOLD3</th><th align="left">Balloon</th><th align="left">RDkit</th><th align="left">MODPEP</th><th align="left">PEP-FOLD3</th><th align="left">Balloon</th><th align="left">RDkit</th></tr></thead><tbody><tr><td align="left">3</td><td char="." align="char">11</td><td char="." align="char">0.04</td><td char="." align="char">0.00</td><td char="." align="char">0.27</td><td char="." align="char">0.07</td><td char="." align="left">100</td><td char="." align="char">0.0</td><td align="left">100</td><td align="left">100</td></tr><tr><td align="left">4</td><td char="." align="char">43</td><td char="." align="char">0.21</td><td char="." align="char">0.00</td><td char="." align="char">0.53</td><td char="." align="char">0.28</td><td char="." align="left">100</td><td char="." align="char">0.0</td><td align="left">100</td><td align="left">100</td></tr><tr><td align="left">5</td><td char="." align="char">32</td><td char="." align="char">0.45</td><td char="." align="char">2.00</td><td char="." align="char">0.96</td><td char="." align="char">0.57</td><td char="." align="left">100</td><td char="." align="char">31.2</td><td align="left">96.9</td><td align="left">96.9</td></tr><tr><td align="left">6</td><td char="." align="char">47</td><td char="." align="char">0.65</td><td char="." align="char">2.18</td><td char="." align="char">1.47</td><td char="." align="char">1.03</td><td align="left">97.9</td><td char="." align="char">31.9</td><td align="left">61.7</td><td align="left">87.2</td></tr><tr><td align="left">7</td><td char="." align="char">49</td><td char="." align="char">0.96</td><td char="." align="char">2.88</td><td char="." align="char">2.21</td><td char="." align="char">1.21</td><td align="left">100</td><td char="." align="char">24.5</td><td align="left">30.6</td><td align="left">79.6</td></tr><tr><td align="left">8</td><td char="." align="char">60</td><td char="." align="char">1.29</td><td char="." align="char">2.94</td><td char="." align="char">2.70</td><td char="." align="char">1.75</td><td align="left">95.0</td><td char="." align="char">25.0</td><td align="left">26.7</td><td align="left">73.3</td></tr><tr><td align="left">9</td><td char="." align="char">138</td><td char="." align="char">1.56</td><td char="." align="char">3.32</td><td char="." align="char">3.34</td><td char="." align="char">1.82</td><td align="left">97.8</td><td char="." align="char">15.2</td><td align="left">8.0</td><td align="left">79.7</td></tr><tr><td align="left">10</td><td char="." align="char">60</td><td char="." align="char">1.67</td><td char="." align="char">2.80</td><td char="." align="char">3.61</td><td char="." align="char">2.20</td><td align="left">96.7</td><td char="." align="char">30.0</td><td align="left">6.7</td><td align="left">61.7</td></tr><tr><td align="left">11</td><td char="." align="char">62</td><td char="." align="char">2.04</td><td char="." align="char">3.17</td><td char="." align="char">3.78</td><td char="." align="char">2.81</td><td align="left">77.4</td><td char="." align="char">25.8</td><td align="left">4.8</td><td align="left">43.5</td></tr><tr><td align="left">12</td><td char="." align="char">40</td><td char="." align="char">2.09</td><td char="." align="char">2.48</td><td char="." align="char">3.57</td><td char="." align="char">3.22</td><td align="left">52.5</td><td char="." align="char">47.5</td><td align="left">20.0</td><td align="left">12.5</td></tr><tr><td align="left">13</td><td char="." align="char">50</td><td char="." align="char">2.29</td><td char="." align="char">3.40</td><td char="." align="char">4.53</td><td char="." align="char">3.56</td><td align="left">58.0</td><td char="." align="char">36.0</td><td align="left">6.0</td><td align="left">32.0</td></tr><tr><td align="left">14</td><td char="." align="char">45</td><td char="." align="char">2.64</td><td char="." align="char">3.66</td><td char="." align="char">5.13</td><td char="." align="char">3.67</td><td align="left">48.9</td><td char="." align="char">37.8</td><td align="left">2.2</td><td align="left">26.7</td></tr><tr><td align="left">15</td><td char="." align="char">33</td><td char="." align="char">2.58</td><td char="." align="char">3.71</td><td char="." align="char">5.59</td><td char="." align="char">3.99</td><td align="left">36.4</td><td char="." align="char">30.3</td><td align="left">0.0</td><td align="left">18.2</td></tr><tr><td align="left">16</td><td char="." align="char">29</td><td char="." align="char">2.66</td><td char="." align="char">3.39</td><td char="." align="char">5.49</td><td char="." align="char">4.61</td><td align="left">41.4</td><td char="." align="char">34.5</td><td align="left">0.0</td><td align="left">3.4</td></tr><tr><td align="left">17</td><td char="." align="char">12</td><td char="." align="char">2.56</td><td char="." align="char">3.50</td><td char="." align="char">6.33</td><td char="." align="char">5.41</td><td align="left">50.0</td><td char="." align="char">50.0</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">18</td><td char="." align="char">25</td><td char="." align="char">2.93</td><td char="." align="char">3.61</td><td char="." align="char">5.73</td><td char="." align="char">5.78</td><td align="left">44.0</td><td char="." align="char">32.0</td><td align="left">0.0</td><td align="left">4.0</td></tr><tr><td align="left">19</td><td char="." align="char">21</td><td char="." align="char">2.38</td><td char="." align="char">3.05</td><td char="." align="char">6.24</td><td char="." align="char">5.49</td><td align="left">61.9</td><td char="." align="char">52.4</td><td align="left">0.0</td><td align="left">4.8</td></tr><tr><td align="left">20</td><td char="." align="char">16</td><td char="." align="char">3.14</td><td char="." align="char">4.51</td><td char="." align="char">6.78</td><td char="." align="char">6.37</td><td align="left">50.0</td><td char="." align="char">18.8</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">21</td><td char="." align="char">21</td><td char="." align="char">3.05</td><td char="." align="char">3.09</td><td char="." align="char">5.24</td><td char="." align="char">8.99</td><td align="left">42.9</td><td char="." align="char">52.4</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">22</td><td char="." align="char">21</td><td char="." align="char">2.43</td><td char="." align="char">2.92</td><td char="." align="char">6.09</td><td char="." align="char">7.46</td><td align="left">61.9</td><td char="." align="char">47.6</td><td align="left">0.0</td><td align="left">4.8</td></tr><tr><td align="left">23</td><td char="." align="char">10</td><td char="." align="char">3.01</td><td char="." align="char">5.05</td><td char="." align="char">7.20</td><td char="." align="char">0.00</td><td align="left">50.0</td><td char="." align="char">10.0</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">24</td><td char="." align="char">17</td><td char="." align="char">3.11</td><td char="." align="char">4.12</td><td char="." align="char">7.07</td><td char="." align="char">4.41</td><td align="left">47.1</td><td char="." align="char">41.2</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">25</td><td char="." align="char">15</td><td char="." align="char">2.71</td><td char="." align="char">4.41</td><td char="." align="char">7.48</td><td char="." align="char">0.00</td><td align="left">60.0</td><td char="." align="char">33.3</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">26</td><td char="." align="char">10</td><td char="." align="char">2.77</td><td char="." align="char">3.68</td><td char="." align="char">7.84</td><td char="." align="char">0.00</td><td align="left">40.0</td><td char="." align="char">40.0</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">27</td><td char="." align="char">10</td><td char="." align="char">3.95</td><td char="." align="char">5.33</td><td char="." align="char">8.53</td><td char="." align="char">0.00</td><td align="left">30.0</td><td char="." align="char">30.0</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">28</td><td char="." align="char">14</td><td char="." align="char">3.05</td><td char="." align="char">4.57</td><td char="." align="char">7.89</td><td char="." align="char">6.15</td><td align="left">42.9</td><td char="." align="char">28.6</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">29</td><td char="." align="char">14</td><td char="." align="char">4.24</td><td char="." align="char">5.47</td><td char="." align="char">7.92</td><td char="." align="char">9.77</td><td align="left">21.4</td><td char="." align="char">28.6</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">30</td><td char="." align="char">5</td><td char="." align="char">2.72</td><td char="." align="char">6.38</td><td char="." align="char">8.32</td><td char="." align="char">0.00</td><td align="left">60.0</td><td char="." align="char">0.0</td><td align="left">0.0</td><td align="left">0.0</td></tr><tr><td align="left">All</td><td char="." align="char">910</td><td char="." align="char">1.90</td><td char="." align="char">3.28</td><td char="." align="char">4.04</td><td char="." align="char">2.63</td><td align="left">74.3</td><td char="." align="char">30.1</td><td align="left">19.2</td><td align="left">46.8</td></tr></tbody></table></table-wrap>
</p><p id="Par29">
<fig id="Fig9"><label>Fig. 9</label><caption><p>Comparison of the <bold>a</bold> average accuracies and <bold>b</bold> success rates of four conformer generation methods for peptides of different lengths when an ensemble of 200 conformations were considered</p></caption><graphic xlink:href="13321_2017_246_Fig9_HTML" id="MO10"/></fig>
</p></sec></sec><sec id="Sec13"><title>Conclusions</title><p id="Par30">We have developed a novel peptide modeling algorithm, referred to as MODPEP, for fast conformational ensemble generation of protein-bound peptides. With constructed rotamer and helix libraries, our MODPEP algorithm builds the peptide 3D structure from scratch by assembling amino acids or helix fragments according to a given sequence. MODPEP is fast and can generated 100 peptide conformations for less than one second. The accuracy of MODPEP depended on the ensemble size of generated conformations and on average had an RMSD of 1.90&#x000a0;&#x000c5; on a diverse test set of 910 protein-bound peptides with 3&#x02013;30 amino acids when 200 conformations were considered for each peptide. On average, MODPEP obtained an average success rate of 74.3% in reproducing experimentally determined structures for all the 910 tested peptides and a success rate of &#x0003e;&#x000a0;95% for the short peptides with 3&#x02013;10 amino acids. MODPEP was compared to three other three approaches, PEP-FOLD3, RDKit, and Balloon. It was found that MODPEP performed significantly better in both accuracy and success rate in reproducing protein-bound peptide conformations.</p></sec></body><back><app-group><app id="App1"><sec id="Sec25"><title>Additional file</title><p>
<media position="anchor" xlink:href="13321_2017_246_MOESM1_ESM.zip" id="MOESM1"><caption><p>
<bold>Additional file 1.</bold> The average accuracies and standard deviations of MODPEP for the peptides of 3&#x02013;30 amino acids on ten randomly splitted training/test sets.</p></caption></media>
</p></sec></app></app-group><fn-group><fn><p><bold>Electronic supplementary material</bold></p><p>The online version of this article (10.1186/s13321-017-0246-7) contains supplementary material, which is available to authorized users.</p></fn></fn-group><ack><title>Authors' contributions</title><p>The manuscript was written through contributions of all authors. All authors read and approved the final manuscript.</p><sec id="FPar1"><title>Acknowledgements</title><p id="Par32">We would like to thank Prof. Johannes Kirchmair for providing us the Python script of the RDKit conformer ensemble generator.</p></sec><sec id="FPar2"><title>Competing interests</title><p id="Par33">The authors declare that they have no competing interests.</p></sec><sec id="d29e4290"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec id="FPar3"><title>Funding</title><p id="Par34">This work is supported by the National Key Research and Development Program of China (Grant Nos. 2016YFC1305800, 2016YFC1305805), the National Natural Science Foundation of China (Grant No. 31670724), and the startup grant of Huazhong University of Science and Technology (Grant No. 3004012104).</p></sec><sec id="FPar4"><title>Publisher&#x02019;s Note</title><p id="Par35">Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>Z</given-names></name><name><surname>Su</surname><given-names>M</given-names></name><name><surname>Han</surname><given-names>L</given-names></name><name><surname>Liu</surname><given-names>J</given-names></name><name><surname>Yang</surname><given-names>Q</given-names></name><name><surname>Li</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>R</given-names></name></person-group><article-title>Forging the basis for developing protein&#x02013;ligand interaction scoring functions</article-title><source>Acc Chem Res.</source><year>2017</year><volume>50</volume><fpage>302</fpage><lpage>309</lpage><pub-id pub-id-type="doi">10.1021/acs.accounts.6b00491</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Petsalaki</surname><given-names>E</given-names></name><name><surname>Russell</surname><given-names>RB</given-names></name></person-group><article-title>Peptide-mediated interactions in biological systems: new discoveries and applications</article-title><source>Curr Opin Biotechnol.</source><year>2008</year><volume>19</volume><fpage>344</fpage><lpage>350</lpage><pub-id pub-id-type="doi">10.1016/j.copbio.2008.06.004</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>London</surname><given-names>N</given-names></name><name><surname>Raveh</surname><given-names>B</given-names></name><name><surname>Schueler-Furman</surname><given-names>O</given-names></name></person-group><article-title>Peptide docking and structure-based characterization of peptide binding: from knowledge to know-how</article-title><source>Curr Opin Struct Biol.</source><year>2013</year><volume>23</volume><fpage>894</fpage><lpage>902</lpage><pub-id pub-id-type="doi">10.1016/j.sbi.2013.07.006</pub-id><pub-id pub-id-type="pmid">24138780</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>C</given-names></name><name><surname>Shen</surname><given-names>Q</given-names></name><name><surname>Tang</surname><given-names>B</given-names></name><name><surname>Lai</surname><given-names>L</given-names></name></person-group><article-title>Computational design of helical peptides targeting TNF</article-title><source>Angew Chem Int Ed Engl.</source><year>2013</year><volume>52</volume><fpage>11059</fpage><lpage>62</lpage><pub-id pub-id-type="doi">10.1002/anie.201305963</pub-id><pub-id pub-id-type="pmid">24038781</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fosgerau</surname><given-names>K</given-names></name><name><surname>Hoffmann</surname><given-names>T</given-names></name></person-group><article-title>Peptide therapeutics: current status and future directions</article-title><source>Drug Discov Today.</source><year>2015</year><volume>20</volume><fpage>122</fpage><lpage>128</lpage><pub-id pub-id-type="doi">10.1016/j.drudis.2014.10.003</pub-id><pub-id pub-id-type="pmid">25450771</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Craik</surname><given-names>DJ</given-names></name><name><surname>Fairlie</surname><given-names>DP</given-names></name><name><surname>Liras</surname><given-names>S</given-names></name><name><surname>Price</surname><given-names>D</given-names></name></person-group><article-title>The future of peptide-based drugs</article-title><source>Chem Biol Drug Des.</source><year>2013</year><volume>81</volume><fpage>136</fpage><lpage>147</lpage><pub-id pub-id-type="doi">10.1111/cbdd.12055</pub-id><pub-id pub-id-type="pmid">23253135</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berman</surname><given-names>HM</given-names></name><name><surname>Westbrook</surname><given-names>J</given-names></name><name><surname>Feng</surname><given-names>Z</given-names></name><name><surname>Gilliland</surname><given-names>G</given-names></name><name><surname>Bhat</surname><given-names>TN</given-names></name><name><surname>Weissig</surname><given-names>H</given-names></name><name><surname>Shindyalov</surname><given-names>IN</given-names></name><name><surname>Bourne</surname><given-names>PE</given-names></name></person-group><article-title>The Protein Data Bank</article-title><source>Nucleic Acids Res.</source><year>2000</year><volume>28</volume><fpage>235</fpage><lpage>242</lpage><pub-id pub-id-type="doi">10.1093/nar/28.1.235</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rey</surname><given-names>J</given-names></name><name><surname>Deschavanne</surname><given-names>P</given-names></name><name><surname>Tuffery</surname><given-names>P</given-names></name></person-group><article-title>BactPepDB: a database of predicted peptides from a exhaustive survey of complete prokaryote genomes</article-title><source>Database (Oxford).</source><year>2014</year><volume>2014</volume><fpage>bau106</fpage><pub-id pub-id-type="doi">10.1093/database/bau106</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vetter</surname><given-names>I</given-names></name><name><surname>Davis</surname><given-names>JL</given-names></name><name><surname>Rash</surname><given-names>LD</given-names></name><name><surname>Anangi</surname><given-names>R</given-names></name><name><surname>Mobli</surname><given-names>M</given-names></name><name><surname>Alewood</surname><given-names>PF</given-names></name><name><surname>Lewis</surname><given-names>RJ</given-names></name><name><surname>King</surname><given-names>GF</given-names></name></person-group><article-title>Venomics: a new paradigm for natural products-based drug discovery</article-title><source>Amino Acids.</source><year>2011</year><volume>40</volume><fpage>15</fpage><lpage>28</lpage><pub-id pub-id-type="doi">10.1007/s00726-010-0516-4</pub-id><pub-id pub-id-type="pmid">20177945</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name></person-group><article-title>Search strategies and evaluation in protein&#x02013;protein docking: principles, advances and challenges</article-title><source>Drug Discov Today.</source><year>2014</year><volume>19</volume><fpage>1081</fpage><lpage>1096</lpage><pub-id pub-id-type="doi">10.1016/j.drudis.2014.02.005</pub-id><pub-id pub-id-type="pmid">24594385</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name></person-group><article-title>Exploring the potential of global protein&#x02013;protein docking: an overview and critical assessment of current programs for automatic ab initio docking</article-title><source>Drug Discov Today.</source><year>2015</year><volume>20</volume><fpage>969</fpage><lpage>977</lpage><pub-id pub-id-type="doi">10.1016/j.drudis.2015.03.007</pub-id><pub-id pub-id-type="pmid">25801181</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hauser</surname><given-names>AS</given-names></name><name><surname>Windshugel</surname><given-names>B</given-names></name></person-group><article-title>LEADS-PEP: a benchmark data set for assessment of peptide docking performance</article-title><source>J Chem Inf Model.</source><year>2016</year><volume>56</volume><fpage>188</fpage><lpage>200</lpage><pub-id pub-id-type="doi">10.1021/acs.jcim.5b00234</pub-id><pub-id pub-id-type="pmid">26651532</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yan</surname><given-names>Y</given-names></name><name><surname>Wen</surname><given-names>Z</given-names></name><name><surname>Wang</surname><given-names>X</given-names></name><name><surname>Huang</surname><given-names>SY</given-names></name></person-group><article-title>Addressing recent docking challenges: a hybrid strategy to integrate template-based and free protein&#x02013;protein docking</article-title><source>Proteins.</source><year>2017</year><volume>85</volume><fpage>497</fpage><lpage>512</lpage><pub-id pub-id-type="doi">10.1002/prot.25234</pub-id><pub-id pub-id-type="pmid">28026062</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rentzsch</surname><given-names>R</given-names></name><name><surname>Renard</surname><given-names>BY</given-names></name></person-group><article-title>Docking small peptides remains a great challenge: an assessment using AutoDock Vina</article-title><source>Brief Bioinform.</source><year>2015</year><volume>16</volume><fpage>1045</fpage><lpage>1056</lpage><pub-id pub-id-type="doi">10.1093/bib/bbv008</pub-id><pub-id pub-id-type="pmid">25900849</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sacquin-Mora</surname><given-names>S</given-names></name><name><surname>Prevost</surname><given-names>C</given-names></name></person-group><article-title>Docking peptides on proteins: how to open a lock, in the dark, with a flexible key</article-title><source>Structure.</source><year>2015</year><volume>23</volume><fpage>1373</fpage><lpage>1374</lpage><pub-id pub-id-type="doi">10.1016/j.str.2015.07.004</pub-id><pub-id pub-id-type="pmid">26244840</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tubert-Brohman</surname><given-names>I</given-names></name><name><surname>Sherman</surname><given-names>W</given-names></name><name><surname>Repasky</surname><given-names>M</given-names></name><name><surname>Beuming</surname><given-names>T</given-names></name></person-group><article-title>Improved docking of polypeptides with Glide</article-title><source>J Chem Inf Model.</source><year>2013</year><volume>53</volume><fpage>1689</fpage><lpage>1699</lpage><pub-id pub-id-type="doi">10.1021/ci400128m</pub-id><pub-id pub-id-type="pmid">23800267</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Morris</surname><given-names>GM</given-names></name><name><surname>Goodsell</surname><given-names>DS</given-names></name><name><surname>Halliday</surname><given-names>RS</given-names></name><name><surname>Huey</surname><given-names>R</given-names></name><name><surname>Hart</surname><given-names>WE</given-names></name><name><surname>Belew</surname><given-names>RK</given-names></name><name><surname>Olson</surname><given-names>AJ</given-names></name></person-group><article-title>Automated docking using a Lamarckian genetic algorithm and an empirical binding free energy function</article-title><source>J Comput Chem.</source><year>1998</year><volume>19</volume><fpage>1639</fpage><lpage>1662</lpage><pub-id pub-id-type="doi">10.1002/(SICI)1096-987X(19981115)19:14&#x0003c;1639::AID-JCC10&#x0003e;3.0.CO;2-B</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Staneva</surname><given-names>I</given-names></name><name><surname>Wallin</surname><given-names>S</given-names></name></person-group><article-title>All-atom Monte Carlo approach to protein&#x02013;peptide binding</article-title><source>J Mol Biol.</source><year>2009</year><volume>393</volume><fpage>1118</fpage><lpage>1128</lpage><pub-id pub-id-type="doi">10.1016/j.jmb.2009.08.063</pub-id><pub-id pub-id-type="pmid">19733177</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ewing</surname><given-names>TJ</given-names></name><name><surname>Makino</surname><given-names>S</given-names></name><name><surname>Skillman</surname><given-names>AG</given-names></name><name><surname>Kuntz</surname><given-names>ID</given-names></name></person-group><article-title>DOCK, 4.0: search strategies for automated molecular docking of flexible molecule databases</article-title><source>J Comput Aided Mol Des.</source><year>2001</year><volume>15</volume><fpage>411</fpage><lpage>428</lpage><pub-id pub-id-type="doi">10.1023/A:1011115820450</pub-id><pub-id pub-id-type="pmid">11394736</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yan</surname><given-names>C</given-names></name><name><surname>Xu</surname><given-names>X</given-names></name><name><surname>Zou</surname><given-names>X</given-names></name></person-group><article-title>Fully blind docking at the atomic level for protein&#x02013;peptide complex structure prediction</article-title><source>Structure.</source><year>2016</year><volume>24</volume><fpage>1842</fpage><lpage>1853</lpage><pub-id pub-id-type="doi">10.1016/j.str.2016.07.021</pub-id><pub-id pub-id-type="pmid">27642160</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schindler</surname><given-names>CE</given-names></name><name><surname>de Vries</surname><given-names>SJ</given-names></name><name><surname>Zacharias</surname><given-names>M</given-names></name></person-group><article-title>Fully blind peptide&#x02013;protein docking with pepATTRACT</article-title><source>Structure.</source><year>2015</year><volume>23</volume><fpage>1507</fpage><lpage>1515</lpage><pub-id pub-id-type="doi">10.1016/j.str.2015.05.021</pub-id><pub-id pub-id-type="pmid">26146186</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Trellet</surname><given-names>M</given-names></name><name><surname>Melquiond</surname><given-names>AS</given-names></name><name><surname>Bonvin</surname><given-names>AM</given-names></name></person-group><article-title>A unified conformational selection and induced fit approach to protein&#x02013;peptide docking</article-title><source>PLoS ONE.</source><year>2013</year><volume>8</volume><fpage>e58769</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0058769</pub-id><pub-id pub-id-type="pmid">23516555</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name><name><surname>Zou</surname><given-names>X</given-names></name></person-group><article-title>Ensemble docking of multiple protein structures: considering protein structural variations in molecular docking</article-title><source>Proteins</source><year>2007</year><volume>66</volume><fpage>399</fpage><lpage>421</lpage><pub-id pub-id-type="doi">10.1002/prot.21214</pub-id><pub-id pub-id-type="pmid">17096427</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name><name><surname>Zou</surname><given-names>X</given-names></name></person-group><article-title>Efficient molecular docking of NMR structures: application to HIV-1 protease</article-title><source>Protein Sci.</source><year>2007</year><volume>16</volume><fpage>43</fpage><lpage>51</lpage><pub-id pub-id-type="doi">10.1110/ps.062501507</pub-id><pub-id pub-id-type="pmid">17123961</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name><name><surname>Zou</surname><given-names>X</given-names></name></person-group><article-title>Construction and test of ligand decoy sets using MDock: community structure&#x02013;activity resource benchmarks for binding mode prediction</article-title><source>J Chem Inf Model.</source><year>2011</year><volume>51</volume><fpage>2107</fpage><lpage>2114</lpage><pub-id pub-id-type="doi">10.1021/ci200080g</pub-id><pub-id pub-id-type="pmid">21755952</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name><name><surname>Zou</surname><given-names>X</given-names></name></person-group><article-title>Advances and challenges in protein&#x02013;ligand docking</article-title><source>Int J Mol Sci.</source><year>2010</year><volume>11</volume><fpage>3016</fpage><lpage>3034</lpage><pub-id pub-id-type="doi">10.3390/ijms11083016</pub-id><pub-id pub-id-type="pmid">21152288</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name><name><surname>Grinter</surname><given-names>SZ</given-names></name><name><surname>Zou</surname><given-names>X</given-names></name></person-group><article-title>Scoring functions and their evaluation methods for protein&#x02013;ligand docking: recent advances and future directions</article-title><source>Phys Chem Chem Phys.</source><year>2010</year><volume>12</volume><fpage>12899</fpage><lpage>12908</lpage><pub-id pub-id-type="doi">10.1039/c0cp00151a</pub-id><pub-id pub-id-type="pmid">20730182</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>London</surname><given-names>N</given-names></name><name><surname>Movshovitz-Attias</surname><given-names>D</given-names></name><name><surname>Schueler-Furman</surname><given-names>O</given-names></name></person-group><article-title>The structural basis of peptide&#x02013;protein binding strategies</article-title><source>Structure.</source><year>2010</year><volume>18</volume><fpage>188</fpage><lpage>199</lpage><pub-id pub-id-type="doi">10.1016/j.str.2009.11.012</pub-id><pub-id pub-id-type="pmid">20159464</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hawkins</surname><given-names>PC</given-names></name><name><surname>Nicholls</surname><given-names>A</given-names></name></person-group><article-title>Conformer generation with OMEGA: learning from the data set and the analysis of failures</article-title><source>J Chem Inf Model</source><year>2012</year><volume>52</volume><fpage>2919</fpage><lpage>2936</lpage><pub-id pub-id-type="doi">10.1021/ci300314k</pub-id><pub-id pub-id-type="pmid">23082786</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Riniker</surname><given-names>S</given-names></name><name><surname>Landrum</surname><given-names>GA</given-names></name></person-group><article-title>Better informed distance geometry: using what we know to improve conformation generation</article-title><source>J Chem Inf Model.</source><year>2015</year><volume>55</volume><fpage>2562</fpage><lpage>2574</lpage><pub-id pub-id-type="doi">10.1021/acs.jcim.5b00654</pub-id><pub-id pub-id-type="pmid">26575315</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kothiwale</surname><given-names>S</given-names></name><name><surname>Mendenhall</surname><given-names>JL</given-names></name><name><surname>Meiler</surname><given-names>J</given-names></name></person-group><article-title>BCL::Conf: small molecule conformational sampling using a knowledge based rotamer library</article-title><source>J Cheminform.</source><year>2015</year><volume>7</volume><fpage>47</fpage><pub-id pub-id-type="doi">10.1186/s13321-015-0095-1</pub-id><pub-id pub-id-type="pmid">26473018</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vainio</surname><given-names>MJ</given-names></name><name><surname>Johnson</surname><given-names>MS</given-names></name></person-group><article-title>Generating conformer ensembles using a multiobjective genetic algorithm</article-title><source>J Chem Inf Model.</source><year>2007</year><volume>47</volume><fpage>2462</fpage><lpage>2474</lpage><pub-id pub-id-type="doi">10.1021/ci6005646</pub-id><pub-id pub-id-type="pmid">17892278</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>O&#x02019;Boyle</surname><given-names>NM</given-names></name><name><surname>Vandermeersch</surname><given-names>T</given-names></name><name><surname>Flynn</surname><given-names>CJ</given-names></name><name><surname>Maguire</surname><given-names>AR</given-names></name><name><surname>Hutchison</surname><given-names>GR</given-names></name></person-group><article-title>Confab-Systematic generation of diverse low-energy conformers</article-title><source>J Cheminform</source><year>2011</year><volume>3</volume><fpage>8</fpage><pub-id pub-id-type="doi">10.1186/1758-2946-3-8</pub-id><pub-id pub-id-type="pmid">21410983</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>S</given-names></name><name><surname>Bolton</surname><given-names>EE</given-names></name><name><surname>Bryant</surname><given-names>SH</given-names></name></person-group><article-title>PubChem3D: conformer ensemble accuracy</article-title><source>J Cheminform.</source><year>2013</year><volume>5</volume><fpage>1</fpage><pub-id pub-id-type="doi">10.1186/1758-2946-5-1</pub-id><pub-id pub-id-type="pmid">23289532</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>X</given-names></name><name><surname>Bai</surname><given-names>F</given-names></name><name><surname>Ouyang</surname><given-names>S</given-names></name><name><surname>Wang</surname><given-names>X</given-names></name><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Jiang</surname><given-names>H</given-names></name></person-group><article-title>Cyndi: a multi-objective evolution algorithm based method for bioactive molecular conformational generation</article-title><source>BMC Bioinform.</source><year>2009</year><volume>10</volume><fpage>101</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-10-101</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gursoy</surname><given-names>O</given-names></name><name><surname>Smiesko</surname><given-names>M</given-names></name></person-group><article-title>Searching for bioactive conformations of drug-like ligands with current force fields: how good are we?</article-title><source>J Cheminform.</source><year>2017</year><volume>9</volume><fpage>29</fpage><pub-id pub-id-type="doi">10.1186/s13321-017-0216-0</pub-id><pub-id pub-id-type="pmid">29086109</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lamiable</surname><given-names>A</given-names></name><name><surname>Thevenet</surname><given-names>P</given-names></name><name><surname>Rey</surname><given-names>J</given-names></name><name><surname>Vavrusa</surname><given-names>M</given-names></name><name><surname>Derreumaux</surname><given-names>P</given-names></name><name><surname>Tuffery</surname><given-names>P</given-names></name></person-group><article-title>PEP-FOLD3: faster de novo structure prediction for linear peptides in solution and in complex</article-title><source>Nucleic Acids Res.</source><year>2016</year><volume>44</volume><issue>W1</issue><fpage>W449</fpage><lpage>W454</lpage><pub-id pub-id-type="doi">10.1093/nar/gkw329</pub-id><pub-id pub-id-type="pmid">27131374</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>W</given-names></name><name><surname>Godzik</surname><given-names>A</given-names></name></person-group><article-title>Cd-hit: a fast program for clustering and comparing large sets of protein or nucleotide sequences</article-title><source>Bioinformatics.</source><year>2006</year><volume>22</volume><fpage>1658</fpage><lpage>1659</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btl158</pub-id><pub-id pub-id-type="pmid">16731699</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Sweet</surname><given-names>RM</given-names></name></person-group><source>Outline of crystallography for biologists. By David Blow</source><year>2002</year><publisher-loc>Oxford</publisher-loc><publisher-name>Oxford University Press</publisher-name></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kabsch</surname><given-names>W</given-names></name><name><surname>Sander</surname><given-names>C</given-names></name></person-group><article-title>Dictionary of protein secondary structure: pattern recognition of hydrogen-bonded and geometrical features</article-title><source>Biopolymers.</source><year>1983</year><volume>22</volume><fpage>2577</fpage><lpage>2637</lpage><pub-id pub-id-type="doi">10.1002/bip.360221211</pub-id><pub-id pub-id-type="pmid">6667333</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jones</surname><given-names>DT</given-names></name></person-group><article-title>Protein secondary structure prediction based on position-specific scoring matrices</article-title><source>J Mol Biol.</source><year>1999</year><volume>292</volume><fpage>195</fpage><lpage>202</lpage><pub-id pub-id-type="doi">10.1006/jmbi.1999.3091</pub-id><pub-id pub-id-type="pmid">10493868</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maier</surname><given-names>JA</given-names></name><name><surname>Martinez</surname><given-names>C</given-names></name><name><surname>Kasavajhala</surname><given-names>K</given-names></name><name><surname>Wickstrom</surname><given-names>L</given-names></name><name><surname>Hauser</surname><given-names>KE</given-names></name><name><surname>Simmerling</surname><given-names>C</given-names></name></person-group><article-title>ff14SB: improving the accuracy of protein side chain and backbone parameters from ff99SB</article-title><source>J Chem Theory Comput.</source><year>2015</year><volume>11</volume><fpage>3696</fpage><lpage>3713</lpage><pub-id pub-id-type="doi">10.1021/acs.jctc.5b00255</pub-id><pub-id pub-id-type="pmid">26574453</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Case</surname><given-names>DA</given-names></name><name><surname>Babin</surname><given-names>V</given-names></name><name><surname>Berryman</surname><given-names>JT</given-names></name><name><surname>Betz</surname><given-names>RM</given-names></name><name><surname>Cai</surname><given-names>Q</given-names></name><name><surname>Cerutti</surname><given-names>DS</given-names></name><name><surname>Cheatham</surname><given-names>TE</given-names><suffix>III</suffix></name><name><surname>Darden</surname><given-names>TA</given-names></name><name><surname>Duke</surname><given-names>RE</given-names></name><name><surname>Gohlke</surname><given-names>H</given-names></name><name><surname>Goetz</surname><given-names>AW</given-names></name><name><surname>Gusarov</surname><given-names>S</given-names></name><name><surname>Homeyer</surname><given-names>N</given-names></name><name><surname>Janowski</surname><given-names>P</given-names></name><name><surname>Kaus</surname><given-names>J</given-names></name><name><surname>Kolossvary</surname><given-names>I</given-names></name><name><surname>Kovalenko</surname><given-names>A</given-names></name><name><surname>Lee</surname><given-names>TS</given-names></name><name><surname>LeGrand</surname><given-names>S</given-names></name><name><surname>Luchko</surname><given-names>T</given-names></name><name><surname>Luo</surname><given-names>R</given-names></name><name><surname>Madej</surname><given-names>B</given-names></name><name><surname>Merz</surname><given-names>KM</given-names></name><name><surname>Paesani</surname><given-names>F</given-names></name><name><surname>Roe</surname><given-names>DR</given-names></name><name><surname>Roitberg</surname><given-names>A</given-names></name><name><surname>Sagui</surname><given-names>C</given-names></name><name><surname>Salomon-Ferrer</surname><given-names>R</given-names></name><name><surname>Seabra</surname><given-names>G</given-names></name><name><surname>Simmerling</surname><given-names>CL</given-names></name><name><surname>Smith</surname><given-names>W</given-names></name><name><surname>Swails</surname><given-names>J</given-names></name><name><surname>Walker</surname><given-names>RC</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Wolf</surname><given-names>RM</given-names></name><name><surname>Wu</surname><given-names>X</given-names></name><name><surname>Kollman</surname><given-names>PA</given-names></name></person-group><source>AMBER 14</source><year>2014</year><publisher-loc>San Francisco</publisher-loc><publisher-name>University of California</publisher-name></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maupetit</surname><given-names>J</given-names></name><name><surname>Derreumaux</surname><given-names>P</given-names></name><name><surname>Tuffery</surname><given-names>P</given-names></name></person-group><article-title>A fast method for large-scale de novo peptide and miniprotein structure prediction</article-title><source>J Comput Chem.</source><year>2010</year><volume>31</volume><fpage>726</fpage><lpage>738</lpage><pub-id pub-id-type="pmid">19569182</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>S-Y</given-names></name></person-group><article-title>Comprehensive assessment of flexible-ligand docking algorithms: current effectiveness and challenges</article-title><source>Brief Bioinform.</source><year>2017</year></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Baber</surname><given-names>JC</given-names></name><name><surname>Thompson</surname><given-names>DC</given-names></name><name><surname>Cross</surname><given-names>JB</given-names></name><name><surname>Humblet</surname><given-names>C</given-names></name></person-group><article-title>GARD: a generally applicable replacement for RMSD</article-title><source>J Chem Inf Model.</source><year>2009</year><volume>49</volume><fpage>1889</fpage><lpage>1900</lpage><pub-id pub-id-type="doi">10.1021/ci9001074</pub-id><pub-id pub-id-type="pmid">19618919</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schulz-Gasch</surname><given-names>T</given-names></name><name><surname>Scharfer</surname><given-names>C</given-names></name><name><surname>Guba</surname><given-names>W</given-names></name><name><surname>Rarey</surname><given-names>M</given-names></name></person-group><article-title>TFD: torsion fingerprints as a new measure to compare small molecule conformations</article-title><source>J Chem Inf Model.</source><year>2012</year><volume>52</volume><fpage>1499</fpage><lpage>1512</lpage><pub-id pub-id-type="doi">10.1021/ci2002318</pub-id><pub-id pub-id-type="pmid">22670896</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Carugo</surname><given-names>O</given-names></name><name><surname>Pongor</surname><given-names>S</given-names></name></person-group><article-title>A normalized root-mean-square distance for comparing protein three-dimensional structures</article-title><source>Protein Sci.</source><year>2001</year><volume>10</volume><fpage>1470</fpage><lpage>1473</lpage><pub-id pub-id-type="doi">10.1110/ps.690101</pub-id><pub-id pub-id-type="pmid">11420449</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><mixed-citation publication-type="other">PEP-FOLD (2016) Version 3. <ext-link ext-link-type="uri" xlink:href="http://bioserv.rpbs.univ-paris-diderot.fr/services/PEP-FOLD3/">http://bioserv.rpbs.univ-paris-diderot.fr/services/PEP-FOLD3/</ext-link></mixed-citation></ref><ref id="CR50"><label>50.</label><mixed-citation publication-type="other">RDKit (2016) Version 2016.09.4. <ext-link ext-link-type="uri" xlink:href="http://www.rdkit.org/">http://www.rdkit.org/</ext-link></mixed-citation></ref><ref id="CR51"><label>51.</label><mixed-citation publication-type="other">Balloon (2016) Version 1.6.4.1258. <ext-link ext-link-type="uri" xlink:href="http://users.abo.fi/mivainio/balloon/">http://users.abo.fi/mivainio/balloon/</ext-link></mixed-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rappe</surname><given-names>AK</given-names></name><name><surname>Casewit</surname><given-names>CJ</given-names></name><name><surname>Colwell</surname><given-names>KS</given-names></name><name><surname>Goddard</surname><given-names>WA</given-names></name><name><surname>Skiff</surname><given-names>WM</given-names></name></person-group><article-title>UFF, a full periodic table force field for molecular mechanics and molecular dynamics simulations</article-title><source>J Am Chem Soc.</source><year>1992</year><volume>114</volume><fpage>10024</fpage><lpage>10035</lpage><pub-id pub-id-type="doi">10.1021/ja00051a040</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Friedrich</surname><given-names>NO</given-names></name><name><surname>Meyder</surname><given-names>A</given-names></name><name><surname>de Bruyn</surname><given-names>Kops C</given-names></name><name><surname>Sommer</surname><given-names>K</given-names></name><name><surname>Flachsenberg</surname><given-names>F</given-names></name><name><surname>Rarey</surname><given-names>M</given-names></name><name><surname>Kirchmair</surname><given-names>J</given-names></name></person-group><article-title>High-quality dataset of protein-bound ligand conformations and its application to benchmarking conformer ensemble generators</article-title><source>J Chem Inf Model.</source><year>2017</year><volume>57</volume><fpage>529</fpage><lpage>539</lpage><pub-id pub-id-type="doi">10.1021/acs.jcim.6b00613</pub-id><pub-id pub-id-type="pmid">28206754</pub-id></element-citation></ref></ref-list></back></article>