<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?subarticle SA1?><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-archivearticle1.dtd?><?SourceDTD.Version 1.1?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">eLife</journal-id><journal-id journal-id-type="iso-abbrev">Elife</journal-id><journal-id journal-id-type="publisher-id">eLife</journal-id><journal-title-group><journal-title>eLife</journal-title></journal-title-group><issn pub-type="epub">2050-084X</issn><publisher><publisher-name>eLife Sciences Publications, Ltd</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6395073</article-id><article-id pub-id-type="pmid">30702427</article-id><article-id pub-id-type="publisher-id">42722</article-id><article-id pub-id-type="doi">10.7554/eLife.42722</article-id><article-categories><subj-group subj-group-type="display-channel"><subject>Tools and Resources</subject></subj-group><subj-group subj-group-type="heading"><subject>Neuroscience</subject></subj-group></article-categories><title-group><article-title>Visualization of currents in neural models with similar behavior and different conductance densities</article-title></title-group><contrib-group><contrib id="author-68196" contrib-type="author" corresp="yes"><name><surname>Alonso</surname><given-names>Leandro M</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0001-8211-2855</contrib-id><email><EMAIL></email><xref ref-type="aff" rid="aff1">1</xref><xref ref-type="other" rid="fund2"/><xref ref-type="other" rid="fund4"/><xref ref-type="fn" rid="con1"/><xref ref-type="fn" rid="conf1"/></contrib><contrib id="author-1021" contrib-type="author"><name><surname>Marder</surname><given-names>Eve</given-names></name><contrib-id authenticated="true" contrib-id-type="orcid">https://orcid.org/0000-0001-9632-5448</contrib-id><xref ref-type="aff" rid="aff1">1</xref><xref ref-type="other" rid="fund1"/><xref ref-type="other" rid="fund3"/><xref ref-type="fn" rid="con2"/><xref ref-type="fn" rid="conf2"/></contrib><aff id="aff1"><label>1</label><institution content-type="dept">Volen Center and Biology Department</institution><institution>Brandeis University</institution><addr-line>Waltham</addr-line><country>United States</country></aff></contrib-group><contrib-group><contrib contrib-type="editor"><name><surname>Westbrook</surname><given-names>Gary L</given-names></name><role>Senior Editor</role><aff><institution>Vollum Institute</institution><country>United States</country></aff></contrib><contrib contrib-type="editor"><name><surname>Skinner</surname><given-names>Frances K</given-names></name><role>Reviewing Editor</role><aff><institution>Krembil Research Institute, University Health Network</institution><country>Canada</country></aff></contrib></contrib-group><pub-date date-type="pub" publication-format="electronic"><day>31</day><month>1</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>8</volume><elocation-id>e42722</elocation-id><history><date date-type="received" iso-8601-date="2018-10-09"><day>09</day><month>10</month><year>2018</year></date><date date-type="accepted" iso-8601-date="2019-01-29"><day>29</day><month>1</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; 2019, Alonso and Marder</copyright-statement><copyright-year>2019</copyright-year><copyright-holder>Alonso and Marder</copyright-holder><ali:free_to_read xmlns:ali="http://www.niso.org/schemas/ali/1.0/"/><license xlink:href="http://creativecommons.org/licenses/by/4.0/"><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/">http://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This article is distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use and redistribution provided that the original author and source are credited.</license-p></license></permissions><self-uri content-type="pdf" xlink:href="elife-42722.pdf"/><abstract><p>Conductance-based models of neural activity produce large amounts of data that can be hard to visualize and interpret. We introduce visualization methods to display the dynamics of the ionic currents and to display the models&#x02019; response to perturbations. To visualize the currents&#x02019; dynamics, we compute the percent contribution of each current and display them over time using stacked-area plots. The waveform of the membrane potential and the contribution of each current change as the models are perturbed. To represent these changes over a range of the perturbation control parameter, we compute and display the distributions of these waveforms. We illustrate these procedures in six examples of bursting model neurons with similar activity but that differ as much as threefold in their conductance densities. These visualization methods provide heuristic insight into why individual neurons or networks with similar behavior can respond widely differently to perturbations.</p></abstract><abstract abstract-type="executive-summary"><title>eLife digest</title><p>The nervous system contains networks of neurons that generate electrical signals to communicate with each other and the rest of the body. Such electrical signals are due to the flow of ions into or out of the neurons via proteins known as ion channels. Neurons have many different kinds of ion channels that only allow specific ions to pass. Therefore, for a neuron to produce an electrical signal, the activities of several different ion channels need to be coordinated so that they all open and close at certain times.</p><p>Researchers have previously used data collected from various experiments to develop detailed models of electrical signals in neurons. These models incorporate information about how and when the ion channels may open and close, and can produce numerical simulations of the different ionic currents. However, it can be difficult to display the currents and observe how they change when several different ion channels are involved.</p><p>Alonso and Marder used simple mathematical concepts to develop new methods to display ionic currents in computational models of neurons. These tools use color to capture changes in ionic currents and provide insights into how the opening and closing of ion channels shape electrical signals.</p><p>The methods developed by Alonso and Marder could be adapted to display the behavior of biochemical reactions or other topics in biology and may, therefore, be useful to analyze data generated by computational models of many different types of cells. Additionally, these methods may potentially be used as educational tools to illustrate the coordinated opening and closing of ion channels in neurons and other fundamental principles of neuroscience that are otherwise hard to demonstrate.</p></abstract><kwd-group kwd-group-type="author-keywords"><kwd>neuronal oscillators</kwd><kwd>Na+ channels</kwd><kwd>Ca++ channels</kwd><kwd>K+ channels</kwd><kwd>conductance-based</kwd><kwd>ionic channels</kwd></kwd-group><kwd-group kwd-group-type="research-organism"><title>Research organism</title><kwd>None</kwd></kwd-group><funding-group><award-group id="fund1"><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>R35 NS097343</award-id><principal-award-recipient><name><surname>Marder</surname><given-names>Eve</given-names></name></principal-award-recipient></award-group><award-group id="fund2"><funding-source><institution-wrap><institution>Swartz Foundation</institution></institution-wrap></funding-source><award-id>2017</award-id><principal-award-recipient><name><surname>Alonso</surname><given-names>Leandro M</given-names></name></principal-award-recipient></award-group><award-group id="fund3"><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>MH046742</award-id><principal-award-recipient><name><surname>Marder</surname><given-names>Eve</given-names></name></principal-award-recipient></award-group><award-group id="fund4"><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>T32 NS07292</award-id><principal-award-recipient><name><surname>Alonso</surname><given-names>Leandro M</given-names></name></principal-award-recipient></award-group><funding-statement>The funders had no role in study design, data collection and interpretation, or the decision to submit the work for publication.</funding-statement></funding-group><custom-meta-group><custom-meta specific-use="meta-only"><meta-name>Author impact statement</meta-name><meta-value>Visualization methods display the dynamics of the currents in conductance-based model neurons, and show how their contribution changes in response to perturbation.</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec sec-type="intro" id="s1"><title>Introduction</title><p>Experimental and computational studies have clearly demonstrated that neurons and circuits with similar behaviors can, nonetheless, have very different values of the conductances that control intrinsic excitability and synaptic strength. Using a model of the crustacean stomatogastric ganglion (STG), <xref rid="bib35" ref-type="bibr">Prinz et al. (2004)</xref> showed that similar network activity can arise from widely different sets of membrane and synaptic conductances. Recent experimental measurements have shown two- to six-fold variability in individual components in the same identified neurons (<xref rid="bib38" ref-type="bibr">Schulz et al., 2006</xref>; <xref rid="bib39" ref-type="bibr">Schulz et al., 2007</xref>; <xref rid="bib37" ref-type="bibr">Roffman et al., 2012</xref>; <xref rid="bib42" ref-type="bibr">Swensen and Bean, 2005</xref>). The use of RNA sequencing and other molecular measurements have shown significant cell-to-cell variability in the expression of ion channels (<xref rid="bib44" ref-type="bibr">Temporal et al., 2012</xref>; <xref rid="bib45" ref-type="bibr">Temporal et al., 2014</xref>; <xref rid="bib46" ref-type="bibr">Tobin et al., 2009</xref>). Together these results suggest that similar activities arise from different cellular and network mechanisms. Here, we use conductance-based models to explore how different these mechanisms are and how they respond to perturbation.</p><p>Because of the intrinsic variability, canonical models that capture the mean behavior of a set of observations are not sufficient to address these issues (<xref rid="bib22" ref-type="bibr">Golowasch et al., 2002</xref>; <xref rid="bib4" ref-type="bibr">Balachandar and Prescott, 2018</xref>). To incorporate intrinsic biophysical variability <xref rid="bib35" ref-type="bibr">Prinz et al. (2004)</xref> introduced an ensemble modeling approach. They constructed a database with millions of model parameter combinations, analyzed their solutions to assess network function, and screened for conductance values for which the activity resembled the data (<xref rid="bib9" ref-type="bibr">Calabrese, 2018</xref>). An alternative was used by <xref rid="bib1" ref-type="bibr">Achard and De Schutter (2006)</xref>. They combined evolutionary strategies with a fitness function based on a phase-plane analysis of the models&#x02019; solutions to find parameters that reproduce complex features in electrophysiological recordings of neuronal activity, and applied their procedure to obtain 20 very different computational models of cerebellar Purkinje cells. Here, we adopt a similar approach and apply evolutionary techniques to optimize a different family of landscape functions that rely on thresholds or Poincar&#x000e9; sections to characterize the models&#x02019; solutions.</p><p>In some respects, biological systems are a black-box because one cannot read out the values over time of all their underlying components. In contrast, computational models allow us to inspect how all the components interact and this can be used to develop intuitions and predictions about how these systems will respond to perturbations. Despite this, much modeling work focuses on the variables of the models that are routinely measured in experiments, such as the membrane potential. While in the models we have access to all state variables, this information can be hard to represent when many conductances are at play. Similarly, the effect of perturbations &#x02013; such as the effect of partially or completely blocking or removing a particular channel &#x02013; can be complex and also hard to display in a compact fashion. Here, we address these difficulties and illustrate two novel visualization methods. We represent the currents in a model neuron using stacked area plots: at each time step, we display the shared contribution of each current to the total current through the membrane. This representation is useful to visualize which currents are most important at each instant and allows the development of insight into how these currents behave when the system is perturbed. Perturbation typically results in drastic changes of the waveform of the activity and these changes depend on the kind of perturbation under consideration. Additionally, we developed a novel representation that relies on computing the probability of <inline-formula><mml:math id="inf1"><mml:mrow><mml:mi>V</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, which allows a visualization of these changes. We illustrate the utility of these procedures using models of single neuron bursters or oscillators.</p></sec><sec sec-type="results" id="s2"><title>Results</title><sec id="s2-1"><title>Finding parameters: landscape optimization</title><p>The numerical exploration of conductance-based models of neurons is a commonplace approach to address fundamental questions in neuroscience (<xref rid="bib14" ref-type="bibr">Dayan and Abbott, 2001</xref>). These models can display much of the phenomenology exhibited by intracellular recordings of single neurons and have the major advantage that many of their parameters correspond to measurable quantities (<xref rid="bib27" ref-type="bibr">Herz et al., 2006</xref>). However, finding parameters for these models so that their solutions resemble experimental observations is a difficult task. This difficulty arises because the models are nonlinear, they have many state variables and they contain a large number of parameters (<xref rid="bib7" ref-type="bibr">Bhalla and Bower, 1993</xref>). These models are complex, and we are not aware of a general procedure that would allow the prediction of how an arbitrary perturbation in any of the parameters will affect their solutions. The problem of finding sets of parameters so that a nonlinear system will display a target behavior is ubiquitous in the natural sciences. A general approach to this problem consists of optimizing a score function that compares features of the models&#x02019; solutions to a set of target features. Consequently, landscape-based optimization techniques for finding parameters in compartmental models of neurons have been proposed before (<xref rid="bib1" ref-type="bibr">Achard and De Schutter, 2006</xref>; <xref rid="bib16" ref-type="bibr">Druckmann et al., 2007</xref>; <xref rid="bib5" ref-type="bibr">Ben-Shalom et al., 2012</xref>). Here, we employ these ideas to develop a family of score functions that are useful to find parameters so that their activities reach a desired target.</p><p>In this work, we started with a well-studied model of neural activity described previously (<xref rid="bib30" ref-type="bibr">Liu et al., 1998</xref>; <xref rid="bib21" ref-type="bibr">Goldman et al., 2001</xref>; <xref rid="bib35" ref-type="bibr">Prinz et al., 2004</xref>; <xref rid="bib33" ref-type="bibr">O'Leary et al., 2014</xref>). The neuron is modeled according to the Hodgkin-Huxley formalism using a single compartment with eight currents. Following <xref rid="bib30" ref-type="bibr">Liu et al. (1998)</xref>, the neuron has a sodium current, <inline-formula><mml:math id="inf2"><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula>; transient and slow calcium currents, <inline-formula><mml:math id="inf3"><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> and <inline-formula><mml:math id="inf4"><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula>; a transient potassium current, <inline-formula><mml:math id="inf5"><mml:msub><mml:mi>I</mml:mi><mml:mi>A</mml:mi></mml:msub></mml:math></inline-formula>; a calcium-dependent potassium current, <inline-formula><mml:math id="inf6"><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula>; a delayed rectifier potassium current, <inline-formula><mml:math id="inf7"><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula>; a hyperpolarization-activated inward current, <inline-formula><mml:math id="inf8"><mml:msub><mml:mi>I</mml:mi><mml:mi>H</mml:mi></mml:msub></mml:math></inline-formula>; and a leak current <inline-formula><mml:math id="inf9"><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula>.</p><p>We explored the space of solutions of the model using landscape optimization. The procedure consists of three steps. First, we generate voltage traces by integration of <xref ref-type="disp-formula" rid="equ5">Equation 5</xref> (Materials&#x000a0;and&#x000a0;methods). We then score the traces using an objective or landscape function that defines a target activity. Finally, we attempt to find minima of the objective function. The procedures used to build objective functions whose minima correspond to sets of conductances that yield the target activities are shown in <xref ref-type="fig" rid="fig1">Figure 1</xref>. Voltage traces were generated by integration of <xref ref-type="disp-formula" rid="equ5">Equation 5</xref> and were then scored according to a set of simple measures. The procedure is efficient in part because we chose measures that require little computing power and yet are sufficient to build successful target functions. For example, we avoid the use of Spike Density Functions (SDF) and Fourier transforms when estimating burst frequencies and burst durations. In this section, we describe target functions whose minima correspond to bursting and tonic activity in single compartment models. This approach can also be applied to the case of small circuits of neurons (<xref rid="bib35" ref-type="bibr">Prinz et al., 2004</xref>).</p><fig id="fig1" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.003</object-id><label>Figure 1.</label><caption><title>Landscape optimization can be used to find models with specific sets of features.</title><p>(<bold>A</bold>) Example model bursting neuron.&#x000a0;The activity is described by the burst frequency and the burst duration in units of the period (duty cycle). The spikes detection threshold (red line) is used to determine the spike times. The ISI threshold (cyan) is used to determine which spikes are bursts starts (bs) and bursts ends (be). The slow wave threshold (blue line) is used to ensure that slow wave activity is separated from spiking activity. (<bold>B</bold>) Example model spiking neuron. We use thresholds as before to measure the frequency and the duty cycle of the cell. The additional slow wave thresholds (purple) are used to control the waveform during spike repolarization.</p></caption><graphic xlink:href="elife-42722-fig1"/></fig><p>We begin with the case of bursters (<xref ref-type="fig" rid="fig1">Figure 1A</xref>). We targeted this type of activity by measuring the bursting frequency, the duty cycle, and the number of crossings at a threshold value to ensure that spiking activity is well separated from slow wave activity. To measure the burst frequency and duty cycle of a solution, we first compute the time stamps at which the cell spikes. Given the sequence of values <inline-formula><mml:math id="inf10"><mml:mrow><mml:mi>V</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo stretchy="false">{</mml:mo><mml:msub><mml:mi>V</mml:mi><mml:mi>n</mml:mi></mml:msub><mml:mo stretchy="false">}</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> we determine that a spike occurs every time that <inline-formula><mml:math id="inf11"><mml:mi>V</mml:mi></mml:math></inline-formula> crosses the spike detection threshold <inline-formula><mml:math id="inf12"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>T</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mn>20</mml:mn><mml:mi>m</mml:mi><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> (red in <xref ref-type="fig" rid="fig1">Figure 1</xref>). We build a sequence of spike times <inline-formula><mml:math id="inf13"><mml:mrow><mml:mi>S</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo stretchy="false">{</mml:mo><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo stretchy="false">}</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> by going through the sequence of voltages <inline-formula><mml:math id="inf14"><mml:mrow><mml:mo stretchy="false">{</mml:mo><mml:msub><mml:mi>V</mml:mi><mml:mi>n</mml:mi></mml:msub><mml:mo stretchy="false">}</mml:mo></mml:mrow></mml:math></inline-formula> and keeping the values of <inline-formula><mml:math id="inf15"><mml:mi>n</mml:mi></mml:math></inline-formula> for which <inline-formula><mml:math id="inf16"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>V</mml:mi><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02264;</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mrow><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">d</mml:mi></mml:mrow><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>V</mml:mi><mml:mrow><mml:mi>n</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>T</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula> (we consider upward crossings). Each element <inline-formula><mml:math id="inf17"><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math></inline-formula> of the sequence <inline-formula><mml:math id="inf18"><mml:mi>S</mml:mi></mml:math></inline-formula> contains the time step at which the i-th spike is detected. Bursts are determined from the sequence of spike times <inline-formula><mml:math id="inf19"><mml:mi>S</mml:mi></mml:math></inline-formula>; if two spikes happen within a temporal interval shorter than <inline-formula><mml:math id="inf20"><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>p</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mn>100</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> they are part of a burst. Using this criterion we can find which of the spike times in <inline-formula><mml:math id="inf21"><mml:mi>S</mml:mi></mml:math></inline-formula> correspond to the start and end of bursts. The starts (bs) and ends (be) of bursts are used to estimate the duty cycle and burst frequency. We loop over the sequence of spike times and determine that a burst starts at <inline-formula><mml:math id="inf22"><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math></inline-formula> if <inline-formula><mml:math id="inf23"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mrow><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">d</mml:mi></mml:mrow><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula>. After a burst starts, we define the end of the burst at <inline-formula><mml:math id="inf24"><mml:msub><mml:mi>s</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:math></inline-formula> if <inline-formula><mml:math id="inf25"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mrow><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">d</mml:mi></mml:mrow><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula>. When a burst ends we can measure the burst duration as <inline-formula><mml:math id="inf26"><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mrow></mml:math></inline-formula> and since the next burst starts (by definition) at <inline-formula><mml:math id="inf27"><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:math></inline-formula> we also can measure the &#x02018;period&#x02019; (if periodic) of the oscillation as <inline-formula><mml:math id="inf28"><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>-</mml:mo><mml:msub><mml:mi>s</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>. Every time a burst starts and ends we get an instance of the burst frequency <inline-formula><mml:math id="inf29"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mi>b</mml:mi></mml:msub></mml:mfrac></mml:mrow></mml:math></inline-formula> and the duty cycle <inline-formula><mml:math id="inf30"><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mi>b</mml:mi></mml:msub></mml:mfrac></mml:mrow></mml:math></inline-formula>. We build distributions of these quantities by looping over the sequence <inline-formula><mml:math id="inf31"><mml:mi>S</mml:mi></mml:math></inline-formula> and define the burst frequency and duty cycle as the mean values <inline-formula><mml:math id="inf32"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> and <inline-formula><mml:math id="inf33"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>d</mml:mi><mml:mi>c</mml:mi><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>. Finally, we count downward crossings in the sequence <inline-formula><mml:math id="inf34"><mml:msub><mml:mi>V</mml:mi><mml:mi>n</mml:mi></mml:msub></mml:math></inline-formula> with <italic>two</italic> slow wave thresholds <inline-formula><mml:math id="inf35"><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>w</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> (with <inline-formula><mml:math id="inf36"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>50</mml:mn></mml:mrow><mml:mo>&#x000b1;</mml:mo><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>) and the total number of bursts <inline-formula><mml:math id="inf37"><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mi>b</mml:mi></mml:msub></mml:math></inline-formula> in <inline-formula><mml:math id="inf38"><mml:mi>S</mml:mi></mml:math></inline-formula>.</p><p>For any given set of conductances, we simulated the model for <inline-formula><mml:math id="inf39"><mml:mn>20</mml:mn></mml:math></inline-formula> s and dropped the first <inline-formula><mml:math id="inf40"><mml:mn>10</mml:mn></mml:math></inline-formula> s to mitigate the effects of transient activity. We then computed the burst frequency <inline-formula><mml:math id="inf41"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>, the duty cycle <inline-formula><mml:math id="inf42"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>d</mml:mi><mml:mi>c</mml:mi><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>, the number of crossings with the slow wave thresholds <inline-formula><mml:math id="inf43"><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>w</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> and the number of bursts <inline-formula><mml:math id="inf44"><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mi>b</mml:mi></mml:msub></mml:math></inline-formula>. We discard unstable solutions; a solution is discarded if <inline-formula><mml:math id="inf45"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>s</mml:mi><mml:mi>t</mml:mi><mml:mi>d</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mo fence="false" stretchy="false">{</mml:mo><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mo fence="false" stretchy="false">}</mml:mo><mml:mo stretchy="false">)</mml:mo><mml:mo>&#x02265;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo><mml:mo>&#x000d7;</mml:mo><mml:mn>0.1</mml:mn><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> or <inline-formula><mml:math id="inf46"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>s</mml:mi><mml:mi>t</mml:mi><mml:mi>d</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mo fence="false" stretchy="false">{</mml:mo><mml:mi>d</mml:mi><mml:mi>c</mml:mi><mml:mo fence="false" stretchy="false">}</mml:mo><mml:mo stretchy="false">)</mml:mo><mml:mo>&#x02265;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>d</mml:mi><mml:mi>c</mml:mi><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo><mml:mo>&#x000d7;</mml:mo><mml:mn>0.2</mml:mn><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>. If a solution is not discarded, we can use the following quantities to measure how close it is to the target behavior,<disp-formula id="equ1"><label>(1)</label><mml:math id="m1"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mtable columnalign="left" columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:msub><mml:mi>E</mml:mi><mml:mi>f</mml:mi></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mo stretchy="false">(</mml:mo><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mo stretchy="false">)</mml:mo><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mstyle></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>d</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:msub><mml:mi>c</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>d</mml:mi><mml:mi>c</mml:mi><mml:mspace width="thinmathspace"/><mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mo stretchy="false">)</mml:mo><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mstyle></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mo stretchy="false">(</mml:mo><mml:mfrac><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mn>2</mml:mn></mml:mfrac><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mo stretchy="false">)</mml:mo><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mstyle></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>Here, <inline-formula><mml:math id="inf47"><mml:msub><mml:mi>E</mml:mi><mml:mi>f</mml:mi></mml:msub></mml:math></inline-formula> measures the mismatch of the bursting frequency of the model cell with a target frequency <inline-formula><mml:math id="inf48"><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> and <inline-formula><mml:math id="inf49"><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> accounts for the duty cycle. <inline-formula><mml:math id="inf50"><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>w</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> measures the difference between the number of bursts and the number of crossings with the slow wave thresholds <inline-formula><mml:math id="inf51"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>50</mml:mn></mml:mrow><mml:mo>&#x000b1;</mml:mo><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>. Because we want a clear separation between slow wave activity and spiking activity, we ask that <inline-formula><mml:math id="inf52"><mml:mrow><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mi>b</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>. Note that if during a burst <inline-formula><mml:math id="inf53"><mml:mi>V</mml:mi></mml:math></inline-formula> goes below <inline-formula><mml:math id="inf54"><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>w</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> this solution would be penalized (factor <inline-formula><mml:math id="inf55"><mml:mfrac><mml:mn>1</mml:mn><mml:mn>2</mml:mn></mml:mfrac></mml:math></inline-formula> accounts for using two slow wave thresholds). Let <inline-formula><mml:math id="inf56"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">g</mml:mi></mml:mrow></mml:mrow></mml:mstyle></mml:math></inline-formula> denote a set of parameters, we can then define an objective function<disp-formula id="equ2"><label>(2)</label><mml:math id="m2"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>E</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mi mathvariant="bold">g</mml:mi></mml:mrow><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>f</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b2;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>d</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b3;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mo>,</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula>where the weights <inline-formula><mml:math id="inf57"><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:mo>,</mml:mo><mml:mi>&#x003b2;</mml:mi><mml:mo>,</mml:mo><mml:mi>&#x003b3;</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math></inline-formula> determine the relative importance of the different sources of penalties. In this work we used <inline-formula><mml:math id="inf58"><mml:mrow><mml:mi>&#x003b1;</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf59"><mml:mrow><mml:mi>&#x003b2;</mml:mi><mml:mo>=</mml:mo><mml:mn>100</mml:mn></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf60"><mml:mrow><mml:mi>&#x003b3;</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math></inline-formula>, and the penalties <inline-formula><mml:math id="inf61"><mml:msub><mml:mi>E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math></inline-formula> were calculated using <inline-formula><mml:math id="inf62"><mml:mrow><mml:mi>T</mml:mi><mml:mo>=</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:math></inline-formula> seconds with <inline-formula><mml:math id="inf63"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow><mml:mo>=</mml:mo><mml:mn>0.1</mml:mn></mml:mrow></mml:math></inline-formula> msecs. The target behavior for bursters was defined by <inline-formula><mml:math id="inf64"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msub><mml:mi>c</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>=</mml:mo><mml:mn>0.2</mml:mn></mml:mrow></mml:math></inline-formula> (duty cycle <inline-formula><mml:math id="inf65"><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula>) (<inline-formula><mml:math id="inf66"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msub><mml:mi>c</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>=</mml:mo><mml:mn>0.2</mml:mn></mml:mrow></mml:math></inline-formula>) and bursting frequency <inline-formula><mml:math id="inf67"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>H</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>z</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>.</p><p>We can use similar procedures to target tonic spiking activity. Note that the procedure we described previously to determine bursts from the sequence of spike times <inline-formula><mml:math id="inf68"><mml:mi>S</mml:mi></mml:math></inline-formula> is also useful in this case. If a given spike satisfies the definition of burst start and it also satisfies the definition of burst end then it is a single spike and the burst duration is zero. Therefore, we compute the bursts and duty cycles as before and ask that the the target duty cycle is zero.</p><p>There are multiple ways to produce tonic spiking in this model and some solutions display very different slow wave activity. To further restrict the models, we placed a middle threshold at <inline-formula><mml:math id="inf69"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>i</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>35</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> and detected downward crossings at this value. We defined <inline-formula><mml:math id="inf70"><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> as the lag between the upward crossings at the spiking threshold (<inline-formula><mml:math id="inf71"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>p</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>) and downward crossings at <inline-formula><mml:math id="inf72"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mi>i</mml:mi><mml:mi>d</mml:mi></mml:mrow></mml:msub><mml:mo>.</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>&#x000a0;<inline-formula><mml:math id="inf73"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula> is useful because it takes different values for tonic spikers than it does for single-spike bursters even though their spiking patterns can be identical. Finally, we found that the model attempts to minimize <inline-formula><mml:math id="inf74"><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> at the expense of hyperpolarizing the membrane beyond <inline-formula><mml:math id="inf75"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>50</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and introducing a wiggle that can be different in different solutions. To penalize this we included additional thresholds between <inline-formula><mml:math id="inf76"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>35</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf77"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>45</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>, counted the number of downward crossings at these values <inline-formula><mml:math id="inf78"><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>i</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub></mml:math></inline-formula>, and asked that these numbers are equal to the number of spikes <inline-formula><mml:math id="inf79"><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mi>s</mml:mi></mml:msub></mml:math></inline-formula>. With these definitions, we define the partial errors as before,<disp-formula id="equ3"><label>(3)</label><mml:math id="m3"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mtable columnalign="left left" columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:msub><mml:mi>E</mml:mi><mml:mi>f</mml:mi></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mo stretchy="false">(</mml:mo><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mo stretchy="false">)</mml:mo><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mstyle></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>d</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:msub><mml:mi>c</mml:mi><mml:mrow><mml:mi>t</mml:mi><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>d</mml:mi><mml:mi>c</mml:mi><mml:mspace width="thinmathspace"/><mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mo stretchy="false">)</mml:mo><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mstyle></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mi>i</mml:mi><mml:mi>d</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:munder><mml:mo stretchy="false">(</mml:mo><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mi>i</mml:mi><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>s</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mo stretchy="false">)</mml:mo><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mstyle></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mo stretchy="false">(</mml:mo><mml:msub><mml:mi mathvariant="normal">#</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mo stretchy="false">)</mml:mo><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup><mml:mo>.</mml:mo></mml:mstyle></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>The total error as a function of the conductances reads as follows,<disp-formula id="equ4"><label>(4)</label><mml:math id="m4"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>E</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mi mathvariant="bold">g</mml:mi></mml:mrow><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>f</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b2;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>d</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b3;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mi>i</mml:mi><mml:mi>d</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b4;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mi>w</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b7;</mml:mi><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>g</mml:mi></mml:mrow></mml:msub><mml:mo>.</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>The values <inline-formula><mml:math id="inf80"><mml:mrow><mml:mi>&#x003b1;</mml:mi><mml:mo>=</mml:mo><mml:mn>1000</mml:mn></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf81"><mml:mrow><mml:mi>&#x003b2;</mml:mi><mml:mo>=</mml:mo><mml:mn>1000</mml:mn></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf82"><mml:mrow><mml:mi>&#x003b3;</mml:mi><mml:mo>=</mml:mo><mml:mn>100</mml:mn></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf83"><mml:mrow><mml:mi>&#x003b4;</mml:mi><mml:mo>=</mml:mo><mml:mn>100</mml:mn></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf84"><mml:mrow><mml:mi>&#x003b7;</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math></inline-formula>, produce solutions that are almost identical to the one displayed in <xref ref-type="fig" rid="fig1">Figure 1B</xref>.</p><p>In all cases, evaluation of the objective functions requires that the models are simulated for a number of seconds and this is the part of the procedure that requires most computing power. Longer simulations will provide better estimations for the burst frequency and duty cycle of the cells, but will linearly increase the time it takes to evaluate the objective function. If the simulations are shorter, evaluations of the objective function are faster but the minimization may be more difficult due to transient behaviors and its minima may not correspond to stable solutions. In this work, we minimized the objective function using a standard genetic algorithm (<xref rid="bib28" ref-type="bibr">Holland, 1992</xref>; <xref rid="bib20" ref-type="bibr">Goldberg and Holland, 1988</xref>). The choice of the optimization routine and the choice of the numerical scheme for the simulations are independent of the functions. See Materials&#x000a0;and&#x000a0;methods for details on the how we performed this optimization. The same functions can be utilized to estimate parameters in models with different channel types.</p></sec><sec id="s2-2"><title>Visualizing the dynamics of ionic currents: currentscapes</title><p>Most modeling work focuses on the variables of the models that are routinely measured in experiments such as the membrane potential as is shown in <xref ref-type="fig" rid="fig2">Figure 2A</xref> for a bursting neuron. While in the models we have access to all state variables, this information can be hard to represent when several current types are at play. One difficulty is that some currents like <inline-formula><mml:math id="inf85"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf86"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> vary over several orders of magnitude, while other currents like the <inline-formula><mml:math id="inf87"><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf88"><mml:mi>H</mml:mi></mml:math></inline-formula> span smaller ranges. Additionally, the relative contribution of each current to the total flux through the membrane varies over time. Here, we introduce a novel representation that is simple and permits displaying the dynamics of the currents in a cohesive fashion.</p><fig id="fig2" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.004</object-id><label>Figure 2.</label><caption><title>Currentscape of a model bursting neuron.</title><p>A simple visualization of the dynamics of ionic currents in conductance-based model neurons. (<bold>A</bold>) Membrane potential of a periodic burster. (<bold>B</bold>) Percent contribution of each current type to the total inward and outward currents displayed as pie charts and bars at times <inline-formula><mml:math id="inf89"><mml:msub><mml:mi>T</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:math></inline-formula> and <inline-formula><mml:math id="inf90"><mml:msub><mml:mi>T</mml:mi><mml:mn>2</mml:mn></mml:msub></mml:math></inline-formula>&#x000a0;(<bold>C</bold>) Percent contribution of each current to the total outward and inward currents at each time stamp. The black filled curves on the top and bottom indicate total inward outward currents respectively on a logarithmic scale. The color curves show the time evolution of each current as a percentage of the total current at that time. For example, at <inline-formula><mml:math id="inf91"><mml:mrow><mml:mi>t</mml:mi><mml:mo>=</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula> the total outward current is <inline-formula><mml:math id="inf92"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>2.5</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and the orange shows a large contribution of <inline-formula><mml:math id="inf93"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula>. At <inline-formula><mml:math id="inf94"><mml:mrow><mml:mi>t</mml:mi><mml:mo>=</mml:mo><mml:msub><mml:mi>T</mml:mi><mml:mn>2</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula> the total outward current has increased to <inline-formula><mml:math id="inf95"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>4</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and the <inline-formula><mml:math id="inf96"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current is contributing less to the total.</p></caption><graphic xlink:href="elife-42722-fig2"/></fig><p>At any given time stamp, we can compute the total inward and outward currents. We can then express the values of each current as a percentage of this quantity. The normalized values of the currents at any time can be displayed as a pie chart representing the share of each current type (<xref ref-type="fig" rid="fig2">Figure 2B</xref>). Because we want to observe how these percentages change in time, we display the shares in a bar instead of a disk. The currentscapes are constructed by applying this procedure to all time stamps and stacking the bars. These types of plots are known as stacked area plots and their application to this problem is novel. <xref ref-type="fig" rid="fig2">Figure 2C</xref> shows the currentscape of a periodically bursting model neuron over one cycle. The shares of each current type to the total inward and outward currents are displayed in colors, and the total inward and outward currents are represented by the filled black curves in logarithmic scale in the top and bottom.</p></sec><sec id="s2-3"><title>Visualizing changes in the waveforms as a parameter is changed</title><p>To visualize changes in the activity as a conductance is gradually removed we computed the distribution of membrane potential <inline-formula><mml:math id="inf97"><mml:mi>V</mml:mi></mml:math></inline-formula> values. This reduction contains information about the waveform of the membrane potential, while all temporal information such as frequency can no longer be recovered. The number of times that a given value of <inline-formula><mml:math id="inf98"><mml:mi>V</mml:mi></mml:math></inline-formula> is sampled is proportional to the time the system spends at that value. <xref ref-type="fig" rid="fig3">Figure 3A</xref> shows the distribution of <inline-formula><mml:math id="inf99"><mml:mi>V</mml:mi></mml:math></inline-formula> for a periodic burster with <inline-formula><mml:math id="inf100"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>H</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>z</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf101"><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mi>c</mml:mi></mml:msub><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> sampled from <inline-formula><mml:math id="inf102"><mml:mn>30</mml:mn></mml:math></inline-formula> s of simulation. The count is larger than <inline-formula><mml:math id="inf103"><mml:msup><mml:mn>10</mml:mn><mml:mn>4</mml:mn></mml:msup></mml:math></inline-formula> for values between <inline-formula><mml:math id="inf104"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>52</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf105"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>40</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>, and smaller than <inline-formula><mml:math id="inf106"><mml:msup><mml:mn>10</mml:mn><mml:mn>3</mml:mn></mml:msup></mml:math></inline-formula> for <inline-formula><mml:math id="inf107"><mml:mi>V</mml:mi></mml:math></inline-formula> between <inline-formula><mml:math id="inf108"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>35</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>v</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf109"><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula>. The areas of the shaded regions are proportional to the probability that the system will be observed at the corresponding <inline-formula><mml:math id="inf110"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> range (<xref ref-type="fig" rid="fig3">Figure 3B</xref>). Note that the area of the dark gray region is <inline-formula><mml:math id="inf111"><mml:msup><mml:mn>10</mml:mn><mml:mn>5</mml:mn></mml:msup></mml:math></inline-formula> while the light gray is <inline-formula><mml:math id="inf112"><mml:mrow><mml:mn>0.5</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>4</mml:mn></mml:msup></mml:mrow></mml:math></inline-formula>, so the probability that the cell is, at any given time, in a hyperpolarized state is more than <inline-formula><mml:math id="inf113"><mml:mn>20</mml:mn></mml:math></inline-formula> times larger than the probability that the cell is spiking.</p><fig id="fig3" position="float" orientation="portrait"><object-id pub-id-type="doi">10.7554/eLife.42722.005</object-id><label>Figure 3.</label><caption><title>Membrane potential <inline-formula><mml:math id="inf114"><mml:mi>V</mml:mi></mml:math></inline-formula> distributions.</title><p>(<bold>A</bold>) Distribution of membrane potential <inline-formula><mml:math id="inf115"><mml:mi>V</mml:mi></mml:math></inline-formula> values. The total number of samples is <inline-formula><mml:math id="inf116"><mml:mrow><mml:mi>N</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mn>2.2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>6</mml:mn></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>. Y-axis scale is logarithmic. The area of the dark shaded region can be used to estimate of the probability that the activity is sampled between <inline-formula><mml:math id="inf117"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>50</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf118"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>40</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>, and the area of the light shaded region is proportional to the probability that <inline-formula><mml:math id="inf119"><mml:mrow><mml:mi>V</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> is sampled between <inline-formula><mml:math id="inf120"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>30</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf121"><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula>. The area of the dark region is <inline-formula><mml:math id="inf122"><mml:mn>20</mml:mn></mml:math></inline-formula> times larger than the light region. (<bold>B</bold>) The same distribution in (<bold>A</bold>) represented as a graded bar. (<bold>C</bold>) Distribution of <inline-formula><mml:math id="inf123"><mml:mi>V</mml:mi></mml:math></inline-formula> as a function of <inline-formula><mml:math id="inf124"><mml:mi>V</mml:mi></mml:math></inline-formula> and <inline-formula><mml:math id="inf125"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula>, and waveforms for several <inline-formula><mml:math id="inf126"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> values.The symbols indicate features of the waveforms and their correspondence to the ridges of the distribution of <inline-formula><mml:math id="inf127"><mml:mi>V</mml:mi></mml:math></inline-formula>. (<bold>D</bold>) Waveforms under two conditions and their correspondence to the ridges of the distribution of <inline-formula><mml:math id="inf128"><mml:mi>V</mml:mi></mml:math></inline-formula>. The ridges were enhanced by computing the derivative of the distribution along the <inline-formula><mml:math id="inf129"><mml:mi>V</mml:mi></mml:math></inline-formula> direction.</p></caption><graphic xlink:href="elife-42722-fig3"/><p content-type="supplemental-figure"><fig id="fig3s1" specific-use="child-fig" orientation="portrait" position="anchor"><object-id pub-id-type="doi">10.7554/eLife.42722.006</object-id><label>Figure 3&#x02014;figure supplement 1.</label><caption><title>Probability distributions of membrane potential.</title><p>(<bold>A</bold>) The black trace corresponds to the membrane potential <inline-formula><mml:math id="inf130"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> during a spike within a burst (total time <inline-formula><mml:math id="inf131"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>5.4</mml:mn><mml:mi>m</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi><mml:mi>c</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>). The y-axis is split into four equally sized bins (in colors) that span the full range of V values (<inline-formula><mml:math id="inf132"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>m</mml:mi><mml:mi>i</mml:mi><mml:mi>n</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>&#x02248;</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mn>60</mml:mn><mml:mi>m</mml:mi><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> and <inline-formula><mml:math id="inf133"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>m</mml:mi><mml:mi>a</mml:mi><mml:mi>x</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>&#x02248;</mml:mo><mml:mn>28</mml:mn><mml:mi>m</mml:mi><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>). The probability that <inline-formula><mml:math id="inf134"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> is observed in a given bin at a <italic>random</italic> instant is proportional to the total time <inline-formula><mml:math id="inf135"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> spends at that bin. This is indicated in colors by the preimage of each bin. (<bold>B</bold>) The total time spent in each bin can be interpreted as a coarse-grained probability distribution of <inline-formula><mml:math id="inf136"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>. (<bold>C</bold>) (top) Membrane potential <inline-formula><mml:math id="inf137"><mml:mi>V</mml:mi></mml:math></inline-formula>, more bins and their pre-images. (bottom) Probability distribution of <inline-formula><mml:math id="inf138"><mml:mi>V</mml:mi></mml:math></inline-formula>. (<bold>D</bold>) Idem for <inline-formula><mml:math id="inf139"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>50</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> bins. Note that the probability distribution of <inline-formula><mml:math id="inf140"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> displays sharp peaks for values of <inline-formula><mml:math id="inf141"><mml:mi>V</mml:mi></mml:math></inline-formula> where local maxima (or minima) in time occur. This effect is more noticeable as the number of bins is increased.</p></caption><graphic xlink:href="elife-42722-fig3-figsupp1"/></fig></p></fig><p>The distribution of <inline-formula><mml:math id="inf142"><mml:mi>V</mml:mi></mml:math></inline-formula> features sharp peaks. In many cases, the peaks in these distributions correspond to features of the waveform, such as the amplitudes of the individual spikes, or the minimum membrane potential (see <xref ref-type="fig" rid="fig3s1">Figure 3&#x02014;figure supplement 1</xref>). This happens because every time the membrane potential reaches a maxima or minima (in time) the derivative <inline-formula><mml:math id="inf143"><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:mfrac></mml:math></inline-formula> is close to zero. The system spends more time close to values of <inline-formula><mml:math id="inf144"><mml:mi>V</mml:mi></mml:math></inline-formula> where the velocity <inline-formula><mml:math id="inf145"><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:mfrac></mml:math></inline-formula> is small than in regions where <inline-formula><mml:math id="inf146"><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:mfrac></mml:math></inline-formula> is large, as it occurs during the flanks of spikes. Therefore, when we sample a solution at a random instant, it is more likely that <inline-formula><mml:math id="inf147"><mml:mi>V</mml:mi></mml:math></inline-formula> corresponds to the peak of a spike than to either flank of the spike, while the most likely outcome is that <inline-formula><mml:math id="inf148"><mml:mi>V</mml:mi></mml:math></inline-formula> is in the hyperpolarized range (<inline-formula><mml:math id="inf149"><mml:mrow><mml:mi/><mml:mo>&#x0003c;</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>40</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>). In this particular burster, there are <inline-formula><mml:math id="inf150"><mml:mn>12</mml:mn></mml:math></inline-formula> spikes in the burst but there are only <inline-formula><mml:math id="inf151"><mml:mn>7</mml:mn></mml:math></inline-formula> peaks in the distribution (between <inline-formula><mml:math id="inf152"><mml:mrow><mml:mn>10</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf153"><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula>); some spikes have similar amplitudes so they add to a larger peak in the distribution. The overall or total amplitude of the oscillation can be read from the distribution since the count of <inline-formula><mml:math id="inf154"><mml:mi>V</mml:mi></mml:math></inline-formula> is zero outside a range (<inline-formula><mml:math id="inf155"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>52</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> to <inline-formula><mml:math id="inf156"><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula>). These distributions can also be represented by a graded bar as shown in <xref ref-type="fig" rid="fig3">Figure 3B</xref>. As conductances are gradually removed the waveform of the activity changes and so does the distribution of <inline-formula><mml:math id="inf157"><mml:mi>V</mml:mi></mml:math></inline-formula> values.</p><p><xref ref-type="fig" rid="fig3">Figure 3C</xref> shows how the distribution of <inline-formula><mml:math id="inf158"><mml:mi>V</mml:mi></mml:math></inline-formula> changes as <inline-formula><mml:math id="inf159"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> is decreased. The waveforms at a few values of <inline-formula><mml:math id="inf160"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> are shown for reference. For each value in the range (<inline-formula><mml:math id="inf161"><mml:mrow><mml:mrow><mml:mn>100</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> to <inline-formula><mml:math id="inf162"><mml:mrow><mml:mrow><mml:mn>0</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> with <inline-formula><mml:math id="inf163"><mml:mrow><mml:mi>N</mml:mi><mml:mo>=</mml:mo><mml:mn>1001</mml:mn></mml:mrow></mml:math></inline-formula> values) we computed the count <inline-formula><mml:math id="inf164"><mml:mrow><mml:mi>p</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo>,</mml:mo><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> and display <inline-formula><mml:math id="inf165"><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>o</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msub><mml:mi>g</mml:mi><mml:mn>10</mml:mn></mml:msub><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mrow><mml:mi>p</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo>,</mml:mo><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> in gray scales. In this example, the cell remains in a bursting regime up to <inline-formula><mml:math id="inf166"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mrow><mml:mn>85</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and transitions abruptly into a single-spike bursting mode for further decrements (<inline-formula><mml:math id="inf167"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi mathvariant="normal">%</mml:mi><mml:mn>80</mml:mn><mml:mi>g</mml:mi><mml:mi>N</mml:mi><mml:mi>a</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>). The spikes produce thin ridges in the distribution that show how their individual amplitudes change. The colored symbols indicate the correspondence between features in the waveform and ridges in the distribution. In this example, the peak amplitudes of the spikes are similar for values of <inline-formula><mml:math id="inf168"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> greater than <inline-formula><mml:math id="inf169"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi mathvariant="normal">%</mml:mi><mml:mn>85</mml:mn><mml:mi>g</mml:mi><mml:mi>N</mml:mi><mml:mi>a</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>. After the transition, the amplitudes of the spikes are very different; two spikes go beyond <inline-formula><mml:math id="inf170"><mml:mrow><mml:mn>0</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula> and the rest accumulate near <inline-formula><mml:math id="inf171"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>25</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>. As <inline-formula><mml:math id="inf172"><mml:mrow><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow><mml:mo>&#x02192;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:math></inline-formula> the oscillations collapse onto a small band at <inline-formula><mml:math id="inf173"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> and only one spike is left.</p><p>The distributions allow the visualization of the amplitudes of the individual spikes, the slow waves, and other features as the parameter <inline-formula><mml:math id="inf174"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> is changed. To highlight ridges in the distributions, the center panel in <xref ref-type="fig" rid="fig3">Figure 3D</xref> shows the derivative <inline-formula><mml:math id="inf175"><mml:mrow><mml:mrow><mml:msub><mml:mo>&#x02202;</mml:mo><mml:mi>V</mml:mi></mml:msub><mml:mo>&#x02061;</mml:mo><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>o</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msub><mml:mi>g</mml:mi><mml:mn>10</mml:mn></mml:msub></mml:mrow></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mi>p</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> in color. This operation is similar to performing a Sobel filtering (<xref rid="bib41" ref-type="bibr">Sobel and Feldman, 1968</xref>) of the image in <xref ref-type="fig" rid="fig3">Figure 3C</xref>. The traces on each side of this panel correspond to the control (left) and <inline-formula><mml:math id="inf176"><mml:mrow><mml:mrow><mml:mn>80</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> conditions. Notice how the amplitudes of each spike, features of the slow wave, and overall amplitude correspond to features in the probability distribution. This representation permits displaying how the features of the waveform change for many values of the perturbation parameter <inline-formula><mml:math id="inf177"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula>.</p></sec><sec id="s2-4"><title>The maximal conductances do not fully predict the currentscapes</title><p>We explored the solutions of a classic conductance-based model of neural activity using landscape optimization and found many sets of parameters that produce similar bursting activity. Inspired by intracellular recording performed in the Pyloric Dilator (<inline-formula><mml:math id="inf178"><mml:mrow><mml:mi>P</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>D</mml:mi></mml:mrow></mml:math></inline-formula>) neurons in crabs and lobsters we targeted bursters with frequencies <inline-formula><mml:math id="inf179"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>H</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>z</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and duty cycles <inline-formula><mml:math id="inf180"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>. We built <inline-formula><mml:math id="inf181"><mml:mn>1000</mml:mn></mml:math></inline-formula> bursting model neurons and visually inspected the dynamics of their currents using their currentscapes. Based on this, we selected six models that display similar membrane activity via different current compositions for further study. Because the models are nonlinear, the relationship between the dynamics of a given current type and the value of its maximal conductance is non-trivial. <xref ref-type="fig" rid="fig4">Figure 4</xref> shows the values of the maximal conductances in the models (top) and their corresponding activity together with their currentscapes (bottom).</p><fig id="fig4" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.007</object-id><label>Figure 4.</label><caption><title>Currentscapes of model bursting neurons.</title><p>(top) Maximal conductances of all model bursters. (bottom) The panels show the membrane potential of the cell and the percent contribution of each current over two cycles.</p></caption><graphic xlink:href="elife-42722-fig4"/></fig><p>It can be difficult to predict the currentscapes based on the values of the maximal conductances. In most cases, it appears that the larger the value of the maximal conductance, the larger the contribution of the corresponding current. However, this does not hold in all cases. For example, burster (f) shows the largest <inline-formula><mml:math id="inf182"><mml:mi>A</mml:mi></mml:math></inline-formula> current contribution, but bursters (c) and (e) have larger values of <inline-formula><mml:math id="inf183"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula>. The maximal conductance of the <inline-formula><mml:math id="inf184"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> current is low in model (f) but the contribution of this current to the total is similar to that in models (a) and (b). The values of <inline-formula><mml:math id="inf185"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> are similar for bursters (e) and (f) but the contribution of this current is visibly different in each model.</p></sec><sec id="s2-5"><title>Response to current injection</title><p>The models produce similar activity with different current dynamics. To further reveal differences in how these activities are generated, we subjected the models to simple perturbations. We begin describing the response to constant current injections in <xref ref-type="fig" rid="fig5">Figure 5</xref>. <xref ref-type="fig" rid="fig5">Figure 5A</xref> and <xref ref-type="fig" rid="fig5">Figure 5B</xref> show the membrane potential of model (a) for different values of injected current. In control, the activity corresponds to regular bursting and larger depolarizing currents result in a plethora of different regimes. The distributions of inter-spike intervals (ISI) provide a means to characterize these regimes (<xref ref-type="fig" rid="fig5">Figure 5C</xref>). When the cell is bursting regularly such as in control and in the <inline-formula><mml:math id="inf186"><mml:mrow><mml:mn>0.8</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula> condition, the interspike interval distributions consist of one large value that corresponds to the interburst interval (<inline-formula><mml:math id="inf187"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>640</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> in control) and several smaller values around <inline-formula><mml:math id="inf188"><mml:mrow><mml:mn>10</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:math></inline-formula> which correspond to the ISI within a burst. There are values of current for which the activity appears irregular and correspondingly, the ISI values are more diverse. <xref ref-type="fig" rid="fig5">Figure 5B</xref> shows the response of the model to larger depolarizing currents. The activity undergoes a sequence of interesting transitions that result in tonic spiking. When <inline-formula><mml:math id="inf189"><mml:mrow><mml:msub><mml:mi>I</mml:mi><mml:mi>e</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mn>3.45</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> the activity is periodic and there are <inline-formula><mml:math id="inf190"><mml:mn>4</mml:mn></mml:math></inline-formula> ISI values. Larger currents result in <inline-formula><mml:math id="inf191"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> ISI values and tonic spiking produces one ISI value. <xref ref-type="fig" rid="fig5">Figure 5C</xref> shows the ISI distributions (y-axis, logarithmic scale) for each value of injected current (x-axis).</p><fig id="fig5" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.008</object-id><label>Figure 5.</label><caption><title>Response to current injections and interspike-intervals (ISI) distributions of model (a).</title><p>(<bold>A</bold>) (top) Control traces (no current injected <inline-formula><mml:math id="inf192"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0</mml:mn><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>), regular bursting (<inline-formula><mml:math id="inf193"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0.8</mml:mn><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>), irregular bursting <inline-formula><mml:math id="inf194"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>1.95</mml:mn><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>. (<bold>B</bold>) (top) Fast regular bursting (<inline-formula><mml:math id="inf195"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>f</mml:mi><mml:mrow><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02248;</mml:mo><mml:mn>6</mml:mn><mml:mi>H</mml:mi><mml:mi>z</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>), quadruplets (<inline-formula><mml:math id="inf196"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>3.45</mml:mn><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>), doublets (<inline-formula><mml:math id="inf197"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>3.75</mml:mn><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>) and singlets (<inline-formula><mml:math id="inf198"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>4.5</mml:mn><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>) (tonic spiking). (<bold>C</bold>) ISI distributions over a range of injected current.</p></caption><graphic xlink:href="elife-42722-fig5"/></fig><p>All these bursters transition into tonic spiking regimes for depolarizing currents larger than <inline-formula><mml:math id="inf199"><mml:mrow><mml:mn>5</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula> but they do so in different ways. To explore these transitions in detail, we computed the inter-spike interval (ISI) distributions over intervals of <inline-formula><mml:math id="inf200"><mml:mrow><mml:mn>60</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:math></inline-formula> for different values of the injected current. <xref ref-type="fig" rid="fig6">Figure 6</xref> shows the ISI distributions for the six models at <inline-formula><mml:math id="inf201"><mml:mrow><mml:mi>N</mml:mi><mml:mo>=</mml:mo><mml:mn>1001</mml:mn></mml:mrow></mml:math></inline-formula> equally spaced values of injected current over the shown range. The y-axis shows the values of all ISIs on a logarithmic scale and the x-axis corresponds to injected current. In the control, the ISI distribution consists of a few small values (<inline-formula><mml:math id="inf202"><mml:mrow><mml:mi/><mml:mo>&#x0003c;</mml:mo><mml:mrow><mml:mn>100</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>) that correspond to the ISIs of spikes within a burst, and a single larger value (<inline-formula><mml:math id="inf203"><mml:mrow><mml:mi/><mml:mo>&#x0003e;</mml:mo><mml:mrow><mml:mn>100</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>) that corresponds to the interval between the last spike of a burst and the first spike of the next burst. When the cell fires tonically the ISI distributions consist of a single value. The ISI distributions exhibit complicated dependences on the control parameter that result in beautiful patterns. For some current values, the cells produce small sets of ISI values indicating that the activity is periodic. However, this activity is quite different across regions. Interspersed with the regions of periodicity there are regions where the ISI distributions densely cover a band of values indicating non-periodic activity. Overall the patterns feature nested forking structures that are reminiscent of classical period doubling routes to chaos (<xref rid="bib18" ref-type="bibr">Feigenbaum, 1978</xref>; <xref rid="bib10" ref-type="bibr">Canavier et al., 1990</xref>).</p><fig id="fig6" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.009</object-id><label>Figure 6.</label><caption><title>ISI distributions of the six model bursting neurons over a range of injected current.</title><p>The panels show all ISI values of each model burster over a range on injected currents (vertical axis is logarithmic). All bursters transition into tonic spiking regimes for injected currents larger than <inline-formula><mml:math id="inf204"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>5</mml:mn><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> and the details of the transitions are different across models.</p></caption><graphic xlink:href="elife-42722-fig6"/></fig></sec><sec id="s2-6"><title>Extracting insights from these visualization tools</title><p>Detailed conductance-based models show complex and rich behaviors in response to all kinds of perturbations. There is a vast amount of information that can be seen in these models and their visualizations in <xref ref-type="fig" rid="fig7">Figures 7 - 15</xref>. It is entirely impossible for us to point out even a fraction of what can be seen or learned from these figures. Nonetheless, we will illustrate a few examples of what can be seen using these methods, knowing that these details will be different for models that are constructed in the future and analyzed using these and similar methods.</p></sec><sec id="s2-7"><title>Perturbing the models with gradual decrements of the maximal conductances</title><p><xref ref-type="fig" rid="fig7">Figures 7</xref> and <xref ref-type="fig" rid="fig8">8</xref> show the effects of gradually decreasing each of the currents in these bursters from <inline-formula><mml:math id="inf205"><mml:mrow><mml:mn>100</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> to <inline-formula><mml:math id="inf206"><mml:mrow><mml:mn>0</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> for all six models. This type of analysis might be relevant to some kinds of pharmacological manipulations or studies of neuromodulators that decrease a given current. The figures show <inline-formula><mml:math id="inf207"><mml:mn>3</mml:mn></mml:math></inline-formula> s of data for each condition. In all panels, the top traces correspond to the control condition (<inline-formula><mml:math id="inf208"><mml:mrow><mml:mn>100</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula>) and the traces below show the activity that results from decreasing the maximal conductance. The dashed lines are placed for reference at <inline-formula><mml:math id="inf209"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>50</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf210"><mml:mrow><mml:mn>0</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula>. Each panel shows the traces for <inline-formula><mml:math id="inf211"><mml:mn>11</mml:mn></mml:math></inline-formula> values of the corresponding maximal conductance equally spaced between <inline-formula><mml:math id="inf212"><mml:mrow><mml:mn>100</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> (control) and <inline-formula><mml:math id="inf213"><mml:mrow><mml:mn>0</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> (completely removed). Each row of panels corresponds to a current type and the columns correspond to the different model bursters. <xref ref-type="fig" rid="fig7">Figure 7</xref> displays the perturbations for the inward currents and <xref ref-type="fig" rid="fig8">Figure 8</xref> shows the outward and leak currents.</p><fig id="fig7" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.010</object-id><label>Figure 7.</label><caption><title>Effects of decreasing maximal conductances: inward currents.</title><p>The figure shows the membrane potential <inline-formula><mml:math id="inf214"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> of all model cells as the maximal conductance <inline-formula><mml:math id="inf215"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula> of each current is gradually decreased from <inline-formula><mml:math id="inf216"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>100</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> to <inline-formula><mml:math id="inf217"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> . Each panel shows <inline-formula><mml:math id="inf218"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>11</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> traces with a duration of 3&#x000a0;s. Dashed lines are placed at <inline-formula><mml:math id="inf219"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0</mml:mn><mml:mi>m</mml:mi><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> and <inline-formula><mml:math id="inf220"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>50</mml:mn><mml:mi>m</mml:mi><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>. The shading indicates values of maximal conductance for which the activity the models differs the most.</p></caption><graphic xlink:href="elife-42722-fig7"/></fig><fig id="fig8" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.011</object-id><label>Figure 8.</label><caption><title>Effects of decreasing maximal conductances: outward currents.</title><p>The figure shows the membrane potential <inline-formula><mml:math id="inf221"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> of all model cells as the maximal conductance <inline-formula><mml:math id="inf222"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mstyle></mml:math></inline-formula> of each current is gradually decreased from <inline-formula><mml:math id="inf223"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>100</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> to <inline-formula><mml:math id="inf224"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>. Each panel shows <inline-formula><mml:math id="inf225"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>11</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> traces with a duration of 3&#x000a0;s. Dashed lines are placed at <inline-formula><mml:math id="inf226"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0</mml:mn><mml:mi>m</mml:mi><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> and <inline-formula><mml:math id="inf227"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>50</mml:mn><mml:mi>m</mml:mi><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>. The shading indicates values of maximal conductance for which the activity the models differs the most.</p></caption><graphic xlink:href="elife-42722-fig8"/></fig><p>Taken together <xref ref-type="fig" rid="fig7">Figures 7</xref> and <xref ref-type="fig" rid="fig8">8</xref> illustrate that each model (a-f) changes its behavior differently in response to decreases in each current. Additionally, decreases in some currents have only relatively small effects but decreases in others have much more profound effects. Because the description of all that can be seen in these figures is beyond the scope of this paper, we chose to focus on the effects of decreasing the <inline-formula><mml:math id="inf228"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> because it has rich and unexpected behaviors.</p><p>The effect of decreasing the <inline-formula><mml:math id="inf229"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> conductance is quite diverse across models. The activities of the models at the intermediate values of <inline-formula><mml:math id="inf230"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> shows visible differences. When <inline-formula><mml:math id="inf231"><mml:mrow><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow><mml:mo>&#x02192;</mml:mo><mml:mrow><mml:mn>0.7</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> models (a), (b) and (c) show bursting activity at different frequencies and with different duty cycles. Models (d), (e) and (f) become tonic spikers at this condition, but their frequencies are different. Note that in the case of model (e) the spiking activity is not regular and the ISIs take several different values. When <inline-formula><mml:math id="inf232"><mml:mrow><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow><mml:mo>&#x02192;</mml:mo><mml:mrow><mml:mn>0.2</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> most models spike tonically but now (e) is regular and (f) shows doublets. When <inline-formula><mml:math id="inf233"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is completely removed, most models transition into a tonic spiking regime with the exception of model (a), that displays a low frequency bursting regime with duty cycle <inline-formula><mml:math id="inf234"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mn>0.5</mml:mn></mml:mrow></mml:math></inline-formula>.</p></sec><sec id="s2-8"><title>Gradually removing one current impacts the dynamics of all currents</title><p>Decreasing any conductance can trigger qualitative changes in the waveform of the membrane potential and in the contributions of each current to the activity. In <xref ref-type="fig" rid="fig9">Figure 9</xref> we plot currentscapes for the effects of decreasing <inline-formula><mml:math id="inf235"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> in model (f). This allows us to examine at higher resolution the changed contributions of currents that give rise to the interesting dynamics seen in <xref ref-type="fig" rid="fig7">Figure 7</xref>. Each panel in <xref ref-type="fig" rid="fig9">Figure 9</xref> corresponds to a different decrement value and shows the membrane potential on top, and the currentscapes at the bottom. The top panels show <inline-formula><mml:math id="inf236"><mml:mn>1</mml:mn></mml:math></inline-formula> second of data and correspond to the <inline-formula><mml:math id="inf237"><mml:mrow><mml:mrow><mml:mn>100</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> (control), <inline-formula><mml:math id="inf238"><mml:mrow><mml:mrow><mml:mn>90</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf239"><mml:mrow><mml:mrow><mml:mn>80</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> conditions. The center panels show <inline-formula><mml:math id="inf240"><mml:mn>0.1</mml:mn></mml:math></inline-formula> s of data for decrements ranging from <inline-formula><mml:math id="inf241"><mml:mrow><mml:mn>70</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> to <inline-formula><mml:math id="inf242"><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> and the bottom panels show <inline-formula><mml:math id="inf243"><mml:mn>2</mml:mn></mml:math></inline-formula> s for the <inline-formula><mml:math id="inf244"><mml:mrow><mml:mn>10</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf245"><mml:mrow><mml:mn>0</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> conditions. As <inline-formula><mml:math id="inf246"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is gradually removed the activity transitions from a bursting regime to a tonic spiking regime.</p><fig id="fig9" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.012</object-id><label>Figure 9.</label><caption><title>Decreasing <inline-formula><mml:math id="inf247"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>C</mml:mi><mml:mi>a</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> in model (f).</title><p>The figure shows the traces and the currentscapes of model (f) as <inline-formula><mml:math id="inf248"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>C</mml:mi><mml:mi>a</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> is gradually decreased. Top panels show <inline-formula><mml:math id="inf249"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>1</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> second of data, center panels show <inline-formula><mml:math id="inf250"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0.1</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> seconds and the bottom panels show <inline-formula><mml:math id="inf251"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> seconds (see full traces in <xref ref-type="fig" rid="fig8">Figure 8</xref>).</p></caption><graphic xlink:href="elife-42722-fig9"/></fig><p>When <inline-formula><mml:math id="inf252"><mml:mrow><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow><mml:mo>&#x02192;</mml:mo><mml:mrow><mml:mrow><mml:mn>90</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> the neuron produces bursts but these become irregular and their durations change. Decreasing the conductance to <inline-formula><mml:math id="inf253"><mml:mrow><mml:mrow><mml:mn>80</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> results in completely different activity. The spiking pattern appears to be periodic but there are at least three different ISI values. It is hard to see changes in the <inline-formula><mml:math id="inf254"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> contribution across these conditions, but changes in other currents are more discernible. The contribution of the <inline-formula><mml:math id="inf255"><mml:mi>A</mml:mi></mml:math></inline-formula> current that is large in the control and <inline-formula><mml:math id="inf256"><mml:mrow><mml:mrow><mml:mn>90</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> conditions, is much smaller in the <inline-formula><mml:math id="inf257"><mml:mrow><mml:mrow><mml:mn>80</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> condition. Additionally, the <inline-formula><mml:math id="inf258"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf259"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> currents show larger contributions, the <inline-formula><mml:math id="inf260"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> current contributes less and the <inline-formula><mml:math id="inf261"><mml:mi>H</mml:mi></mml:math></inline-formula> current is negligible. Further increments in simulated blocker concentration result in tonic spiking regimes with frequencies ranging from <inline-formula><mml:math id="inf262"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>H</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>z</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> to <inline-formula><mml:math id="inf263"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>10</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>H</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>z</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>. The center panels in <xref ref-type="fig" rid="fig9">Figure 9</xref> show the currentscapes for these conditions on a different time scale to highlight the contributions of <inline-formula><mml:math id="inf264"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula>. The leftmost panel shows the <inline-formula><mml:math id="inf265"><mml:mrow><mml:mrow><mml:mn>70</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> condition. In this panel, we placed vertical lines indicating the time stamps at which the peak of the spike and the minimum occur. Notice the large contribution of the <inline-formula><mml:math id="inf266"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current prior to the peak of the spike, and the large contribution of the <inline-formula><mml:math id="inf267"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> current for the next <inline-formula><mml:math id="inf268"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>10</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>. When the membrane potential is at its minimum value the <inline-formula><mml:math id="inf269"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> current dominates the inward currents and remains the largest contributor for the next <inline-formula><mml:math id="inf270"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>10</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>. The <inline-formula><mml:math id="inf271"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> current reduces its share drastically by the time the <inline-formula><mml:math id="inf272"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current is visible and <inline-formula><mml:math id="inf273"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> takes over. The contribution of <inline-formula><mml:math id="inf274"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> remains approximately constant during repolarization and vanishes as the membrane becomes depolarized and the <inline-formula><mml:math id="inf275"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current becomes dominant. The effect of removing <inline-formula><mml:math id="inf276"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is visible on this scale. The waveform of the contribution remains qualitatively the same: largest at the minimum voltage and approximately constant until the next spike. However, the contribution of <inline-formula><mml:math id="inf277"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> during repolarization becomes smaller, and for larger conductance decrements results in a thinner band. Finally, the bottom panels show the cases <inline-formula><mml:math id="inf278"><mml:mrow><mml:mrow><mml:mn>10</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf279"><mml:mrow><mml:mrow><mml:mn>0</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> which correspond to a two-spike burster and a tonic spiker, respectively. Note that even though the contribution of <inline-formula><mml:math id="inf280"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is barely visible, complete removal of this current results in a very different pattern. The activity switched from bursting to spiking and the current composition is different; <inline-formula><mml:math id="inf281"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> disappeared in the <inline-formula><mml:math id="inf282"><mml:mrow><mml:mn>0</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> condition and the <inline-formula><mml:math id="inf283"><mml:mi>A</mml:mi></mml:math></inline-formula> current takes over. Notice also the larger contribution of the <inline-formula><mml:math id="inf284"><mml:mi>H</mml:mi></mml:math></inline-formula> current.</p></sec><sec id="s2-9"><title>Modeling current deletions</title><p>There has been a great deal of work studying the effects of genetic and/or pharmacological deletions of currents. One of the puzzles is why some currents, known to be physiologically important, can have relatively little phenotype in some, or all individuals. For this reason in <xref ref-type="fig" rid="fig10">Figures 10</xref> and <xref ref-type="fig" rid="fig11">11</xref>, we show the effects of deletion of each current in all six models. Each panel shows <inline-formula><mml:math id="inf285"><mml:mn>2</mml:mn></mml:math></inline-formula> seconds of data. The inward currents are portrayed in <xref ref-type="fig" rid="fig10">Figure 10</xref> and the outward and leak currents are shown in <xref ref-type="fig" rid="fig11">Figure 11</xref>.</p><fig id="fig10" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.013</object-id><label>Figure 10.</label><caption><title>Complete removal of one current: inward currents.</title><p>The figure shows the traces and currentscapes for all bursters when one current is completely removed.</p></caption><graphic xlink:href="elife-42722-fig10"/></fig><fig id="fig11" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.014</object-id><label>Figure 11.</label><caption><title>Complete removal of one current: outward currents.</title><p>The figure shows the traces and currentscapes for all bursters when one current is completely removed.</p></caption><graphic xlink:href="elife-42722-fig11"/></fig><p>Removal of some currents has little obvious phenotype differences across the population although the currentscapes are different, such as seen for the <inline-formula><mml:math id="inf286"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf287"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> cases. Removal of some currents produces&#x000a0;similar phenotypes in most, but not all of the six models as seen in the <inline-formula><mml:math id="inf288"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>H</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf289"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula> cases. Removal of <inline-formula><mml:math id="inf290"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> had virtually identical effects both on the phenotype and the currents. For other currents, such as <inline-formula><mml:math id="inf291"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> and the <inline-formula><mml:math id="inf292"><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:math></inline-formula>, we find two types of responses with nearly half of the models for each case (the exception is model (d) <inline-formula><mml:math id="inf293"><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:math></inline-formula>). In the case of the <inline-formula><mml:math id="inf294"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> current both the phenotype and the currents composition are very diverse across models.</p></sec><sec id="s2-10"><title>Changes in waveform as conductances are gradually decreased</title><p>A fuller description of the behavior/phenotype of all of the models for all values of conductance decrements can be seen in <xref ref-type="fig" rid="fig12">Figures 12</xref> and <xref ref-type="fig" rid="fig13">13</xref>. These figures use the probability scheme described in <xref ref-type="fig" rid="fig3">Figure 3</xref> and <xref ref-type="fig" rid="fig3s1">Figure 3&#x02014;figure supplement 1</xref>. Using these methods, it is possible to see exactly how the waveforms change and the boundaries of activity for each model and each conductance. The panels show the ridges of the probability distributions <inline-formula><mml:math id="inf295"><mml:mrow><mml:mi>p</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> of the membrane potential <inline-formula><mml:math id="inf296"><mml:mrow><mml:mi>V</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> for <inline-formula><mml:math id="inf297"><mml:mn>1001</mml:mn></mml:math></inline-formula> values of maximal conductance values (see Materials&#x000a0;and&#x000a0;methods). The probability of <inline-formula><mml:math id="inf298"><mml:mrow><mml:mi>V</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> was computed using 30 s of data after dropping a transient period of <inline-formula><mml:math id="inf299"><mml:mn>120</mml:mn></mml:math></inline-formula> s. It was estimated using <inline-formula><mml:math id="inf300"><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mn>1001</mml:mn></mml:mrow></mml:math></inline-formula> bins in the range <inline-formula><mml:math id="inf301"><mml:mrow><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mn>70</mml:mn></mml:mrow><mml:mo>,</mml:mo><mml:mn>35</mml:mn><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>v</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf302"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>6</mml:mn></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> samples for each maximal conductance value. The system spends more time in regions where <inline-formula><mml:math id="inf303"><mml:mrow><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:mfrac><mml:mo>&#x02248;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:math></inline-formula> and is sampled more at those values. Therefore, features such as the amplitudes of the spikes appear as sharp peaks in the probability distributions. To highlight these peaks and visualize how they change as currents are gradually decreased, we plot the derivative or sharpness of the distribution in colors (see color scale in <xref ref-type="fig" rid="fig3">Figure 3D</xref>). Overall, these plots show that for any given current, there are ranges of the conductance values where a small change results in a smooth deformation of the waveform, and there are specific values at which abrupt transitions take place. As before there is too much detail to describe everything in these figures so we will discuss a subset of the features highlighted by this representation.</p><fig id="fig12" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.015</object-id><label>Figure 12.</label><caption><title>Changes in waveform as currents are gradually removed.</title><p>Inward currents. The figure shows the ridges of the probability distribution of <inline-formula><mml:math id="inf304"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> as a function of <inline-formula><mml:math id="inf305"><mml:mi>V</mml:mi></mml:math></inline-formula> and each maximal conductance <inline-formula><mml:math id="inf306"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>. The ridges of the probability distributions appear as curves and correspond to values of <inline-formula><mml:math id="inf307"><mml:mi>V</mml:mi></mml:math></inline-formula> where the system spends more time, such as extrema. The panels show how different features of the waveform such as total amplitude, and the amplitude of each spike, change as each current is gradually decreased.</p></caption><graphic xlink:href="elife-42722-fig12"/></fig><fig id="fig13" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.016</object-id><label>Figure 13.</label><caption><title>Changes in waveform as currents are gradually removed.</title><p>Outward and leak currents. The figure shows the ridges of the probability distribution of <inline-formula><mml:math id="inf308"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> as a function of <inline-formula><mml:math id="inf309"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>V</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> and each maximal conductance <inline-formula><mml:math id="inf310"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula>. See <xref ref-type="fig" rid="fig12">Figure 12</xref>.</p></caption><graphic xlink:href="elife-42722-fig13"/></fig><p>The top rows in <xref ref-type="fig" rid="fig12">Figure 12</xref> correspond to removing the <inline-formula><mml:math id="inf311"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current in the models. Note that the minimum value of <inline-formula><mml:math id="inf312"><mml:mi>V</mml:mi></mml:math></inline-formula> in control (left) is close to <inline-formula><mml:math id="inf313"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>50</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and a small decrement in <inline-formula><mml:math id="inf314"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> results in larger amplitude. The colored curves inside the envelopes correspond to the spikes&#x02019; amplitudes and features of the slow waves. For instance, when the <inline-formula><mml:math id="inf315"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current is completely removed (right) the amplitude of the oscillation is <inline-formula><mml:math id="inf316"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>40</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and the activity corresponds to a single-spike bursting mode. The spike amplitude is given by the top edge of the colored region and the curve near <inline-formula><mml:math id="inf317"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> indicates the burst &#x02018;belly&#x02019;: the membrane hyperpolarizes slowly after spike termination and there is a wiggle at this transition.</p><p>Removing <inline-formula><mml:math id="inf318"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> in model (a) does not disrupt bursting activity immediately. Notice that the amplitude of the bursts remains approximately constant over a range of <inline-formula><mml:math id="inf319"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> values. The dim red and yellow lines at <inline-formula><mml:math id="inf320"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> show that the amplitudes of the spikes are different and have different dependences with <inline-formula><mml:math id="inf321"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula>. When the model transitions into a tonic spiking regime, the amplitude of the spikes is the same and there is only one amplitude value. This value stays constant over a range but the minimum membrane potential decreases and the overall amplitude therefore increases. The model returns to a bursting regime for values of <inline-formula><mml:math id="inf322"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> smaller than <inline-formula><mml:math id="inf323"><mml:mrow><mml:mrow><mml:mn>30</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula>. Notice that in model (a) the membrane potential during bursts goes below <inline-formula><mml:math id="inf324"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>50</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>, unlike in the control condition. Notice that the waveform of the membrane potential changes abruptly as <inline-formula><mml:math id="inf325"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is reduced and the models transition into a spiking regime. Model (f) is less resilient to this perturbation and this transition takes place at lower conductance values.</p><p>Removing <inline-formula><mml:math id="inf326"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> does not much change the waveform, but it alters the temporal properties of the activity. The models remain bursting up to a critical value and the amplitude of the spikes was changed little. The features of the slow wave do not much change either except in model (f). Model (c) is less resilient to this perturbation since it becomes quiescent for lower decrements of the maximal conductance than the other models. The effect of gradually removing <inline-formula><mml:math id="inf327"><mml:mi>H</mml:mi></mml:math></inline-formula> appears similar to <inline-formula><mml:math id="inf328"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> in this representation. In this case again, the morphology of the waveform is less altered than its temporal properties (except in model (e) where a transition takes place).</p><p><xref ref-type="fig" rid="fig13">Figure 13</xref> shows the same plots for the outward and leak currents. The <inline-formula><mml:math id="inf329"><mml:mi>A</mml:mi></mml:math></inline-formula> current in model (a) is very small (<inline-formula><mml:math id="inf330"><mml:mrow><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>10</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>&#x003bc;</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>) and its removal has little effect on the activity. This translates into curves that appear as parallel lines indicating spikes with different amplitudes that remain unchanged. The rest of the models exhibit a transition into a different regime. The waveforms of this regime appears similar to the waveforms which result from removing <inline-formula><mml:math id="inf331"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> (see <xref ref-type="fig" rid="fig7">Figure 7</xref>) but in this representation it is easier to observe differences such as the overall amplitude of the oscillation. The amplitude decreases as <inline-formula><mml:math id="inf332"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> is decreased and increases as <inline-formula><mml:math id="inf333"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula> is decreased. Removing <inline-formula><mml:math id="inf334"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> has a similar effect to removing <inline-formula><mml:math id="inf335"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> in that the models transition into tonic spiking regimes. The difference is that the spiking regimes that result from removing <inline-formula><mml:math id="inf336"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> have smaller amplitudes and also correspond to more depolarized states.</p><p>All models are very sensitive to removing <inline-formula><mml:math id="inf337"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> and low values result in single-spike bursting modes with large amplitudes. Model (c) is least fragile to this perturbation and exhibits a visible range (<inline-formula><mml:math id="inf338"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x0223c;</mml:mo><mml:mn>100</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> to <inline-formula><mml:math id="inf339"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mo>&#x0223c;</mml:mo><mml:mn>90</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>) with bursting modes. These oscillations break down in a similar way to the <inline-formula><mml:math id="inf340"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> case and display similar patterns. However, an important difference is that unlike in the <inline-formula><mml:math id="inf341"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> case, the overall amplitude of the oscillation increases as <inline-formula><mml:math id="inf342"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> is decreased. As before, the top edge corresponds to the amplitude of the large spike and the curves in the colored region correspond to extrema of the oscillation. After spiking, the membrane remains at a constant depolarized value (<inline-formula><mml:math id="inf343"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>) for a long period and produces a high-frequency oscillation before hyperpolarization. The amplitude of this oscillation increases as <inline-formula><mml:math id="inf344"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> is further decreased, and this results in a white curve that starts above <inline-formula><mml:math id="inf345"><mml:mrow><mml:mn>0</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula> and ends above <inline-formula><mml:math id="inf346"><mml:mrow><mml:mn>0</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula>. The beginning of this curve corresponds to a high-frequency oscillation that occurs after spike termination. This type of activity is termed plateau oscillations and was reported in models of leech heart interneurons (<xref rid="bib13" ref-type="bibr">Cymbalyuk and Calabrese, 2000</xref>) and in experiments in lamprey spinal neurons (<xref rid="bib49" ref-type="bibr">Wang et al., 2014</xref>). These features are hardly visible in the traces in <xref ref-type="fig" rid="fig8">Figure 8</xref> and are highlighted by this representation. Finally, the <inline-formula><mml:math id="inf347"><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:math></inline-formula> case appears similar to mixture of the <inline-formula><mml:math id="inf348"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf349"><mml:mi>A</mml:mi></mml:math></inline-formula> cases. The cells remain bursting over a range of values and some of them transition into a single-spike bursting mode that is different from the <inline-formula><mml:math id="inf350"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> case.</p></sec><sec id="s2-11"><title>Changes in current contributions as conductances are gradually decreased</title><p>The key to the visualization method in <xref ref-type="fig" rid="fig12">Figures 12</xref> and <xref ref-type="fig" rid="fig13">13</xref> is to consider <inline-formula><mml:math id="inf351"><mml:mrow><mml:mi>V</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> not as a time series but as a stochastic variable with a probability distribution (see <xref ref-type="fig" rid="fig3">Figure 3</xref> and supplement). The same procedure can be applied to the time series of each current. However, because the contributions of the currents are different at different times, and at different decrements of conductance values, it is not possible to display this information using the same scale for all channels. To overcome this, we proceed as in the currentscapes and instead focus on the normalized currents or shares to the total inward and outward currents (the rows of matrices <inline-formula><mml:math id="inf352"><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> and <inline-formula><mml:math id="inf353"><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>-</mml:mo></mml:msup></mml:math></inline-formula>, see Materials&#x000a0;and&#x000a0;methods). The current shares <inline-formula><mml:math id="inf354"><mml:mrow><mml:mover accent="true"><mml:msub><mml:mi>C</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> correspond to the width of the color bands in the currentscapes and can also be represented by a time series that is normalized to the interval <inline-formula><mml:math id="inf355"><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:math></inline-formula>. The probability distribution of <inline-formula><mml:math id="inf356"><mml:mrow><mml:mover accent="true"><mml:msub><mml:mi>C</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> permits displaying <italic>changes</italic> in the contributions of each current to the activity as one current is gradually removed. Interpreting these distributions is straightforward as before: the number of times the system is sampled in a given current share configuration is proportional to the time the system spends there. The aim of plotting these distributions is to visualize how the currentscapes would change for all values of the conductance decrement. To illustrate this procedure, we return to <inline-formula><mml:math id="inf357"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> to explore further the causes of the complex behavior of model (f) (see <xref ref-type="fig" rid="fig9">Figure 9</xref>).</p><p><xref ref-type="fig" rid="fig14">Figure 14</xref> shows the probability distributions of the current shares as <inline-formula><mml:math id="inf358"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is gradually decreased in model (f) (see also <xref ref-type="fig" rid="fig9">Figure 9</xref> and <xref ref-type="fig" rid="fig14s1">Figure 14&#x02014;figure supplement 1</xref>). The panels show the share of each current as <inline-formula><mml:math id="inf359"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is gradually decreased and the probability is indicated in colors. In control the <inline-formula><mml:math id="inf360"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf361"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> current shares are distributed in a similar way. Both currents can at times be responsible for <inline-formula><mml:math id="inf362"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>90</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> of the inward current, but most of the time they contribute <inline-formula><mml:math id="inf363"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>. The <inline-formula><mml:math id="inf364"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current is larger right before spike repolarization and the <inline-formula><mml:math id="inf365"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> amounts to <inline-formula><mml:math id="inf366"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>90</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> of the small (<inline-formula><mml:math id="inf367"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>5</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>) total inward current. For larger decrements, the system transitions into tonic spiking and the contribution of the <inline-formula><mml:math id="inf368"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current is more evenly distributed over a wider range. The contribution of the <inline-formula><mml:math id="inf369"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> current is predominantly <inline-formula><mml:math id="inf370"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>15</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> and trends to zero as <inline-formula><mml:math id="inf371"><mml:mrow><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow><mml:mo>&#x02192;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:math></inline-formula>. Note also that as the contribution of <inline-formula><mml:math id="inf372"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> decreases, the contribution of <inline-formula><mml:math id="inf373"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> increases to values larger than <inline-formula><mml:math id="inf374"><mml:mrow><mml:mn>75</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> while in control it contributes with <inline-formula><mml:math id="inf375"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>50</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>. The contribution of the <inline-formula><mml:math id="inf376"><mml:mi>H</mml:mi></mml:math></inline-formula> current is small (<inline-formula><mml:math id="inf377"><mml:mrow><mml:mi/><mml:mo>&#x02264;</mml:mo><mml:mrow><mml:mn>25</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>) between <inline-formula><mml:math id="inf378"><mml:mrow><mml:mrow><mml:mn>100</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf379"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mrow><mml:mn>80</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>; it becomes negligible between <inline-formula><mml:math id="inf380"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mrow><mml:mn>80</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf381"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and becomes dominant after <inline-formula><mml:math id="inf382"><mml:mrow><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula>. The <inline-formula><mml:math id="inf383"><mml:mi>A</mml:mi></mml:math></inline-formula> current behaves similarly to the <inline-formula><mml:math id="inf384"><mml:mi>H</mml:mi></mml:math></inline-formula>. It contributes <inline-formula><mml:math id="inf385"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>90</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> of the (small <inline-formula><mml:math id="inf386"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>) total outward current before burst initiation and its contribution decreases drastically when the system transitions into tonic spiking. As <inline-formula><mml:math id="inf387"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is removed further the <inline-formula><mml:math id="inf388"><mml:mi>A</mml:mi></mml:math></inline-formula> current is more likely to contribute with a larger share. The contribution of the <inline-formula><mml:math id="inf389"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current decreases as <inline-formula><mml:math id="inf390"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is decreased and some of it persists even when <inline-formula><mml:math id="inf391"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula> is completely removed. In contrast, the contribution of the <inline-formula><mml:math id="inf392"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> current does not appear to change much and nor does its role in the activity.</p><fig id="fig14" position="float" orientation="portrait"><object-id pub-id-type="doi">10.7554/eLife.42722.017</object-id><label>Figure 14.</label><caption><title>Changes in waveform of current shares as one current is gradually decreased.</title><p>The panels show the probability distribution of the share of each current <inline-formula><mml:math id="inf393"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mrow><mml:mover><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo stretchy="false">^</mml:mo></mml:mover></mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> for model (f) as <inline-formula><mml:math id="inf394"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>C</mml:mi><mml:mi>a</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> is decreased (see <xref ref-type="fig" rid="fig14s1">Figure 14&#x02014;figure supplement 1</xref>).</p></caption><graphic xlink:href="elife-42722-fig14"/><p content-type="supplemental-figure"><fig id="fig14s1" specific-use="child-fig" orientation="portrait" position="anchor"><object-id pub-id-type="doi">10.7554/eLife.42722.018</object-id><label>Figure 14&#x02014;figure supplement 1.</label><caption><title>Probability distributions of currents shares.</title><p>The Figure shows the relationship between the currentscapes and the distributions of current shares. (<bold>A</bold>) Distribution of <inline-formula><mml:math id="inf395"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> current share to the total outward current for <inline-formula><mml:math id="inf396"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>1001</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> values of <inline-formula><mml:math id="inf397"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>g</mml:mi><mml:mi>C</mml:mi><mml:mi>a</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> between <inline-formula><mml:math id="inf398"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> and <inline-formula><mml:math id="inf399"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>100</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>. (<bold>B</bold>) Share of <inline-formula><mml:math id="inf400"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> current as a time series. The <inline-formula><mml:math id="inf401"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> current contributes with more than <inline-formula><mml:math id="inf402"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>50</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> of the outward current for most of the time. At <inline-formula><mml:math id="inf403"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>70</mml:mn><mml:mi mathvariant="normal">%</mml:mi><mml:mi>g</mml:mi><mml:mi>C</mml:mi><mml:mi>a</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> the cell spikes tonically and the <inline-formula><mml:math id="inf404"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>A</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> contributes <inline-formula><mml:math id="inf405"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>50</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> of the outward current most of the time. The symbols indicate features in the waveform that are mapped to ridges in the distribution. (<bold>C</bold>) Currentscapes. (<bold>D</bold>) Idem (<bold>B</bold>) but for <inline-formula><mml:math id="inf406"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>K</mml:mi><mml:mi>C</mml:mi><mml:mi>a</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>. Notice the share of <inline-formula><mml:math id="inf407"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>K</mml:mi><mml:mi>C</mml:mi><mml:mi>a</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> decreasing as <inline-formula><mml:math id="inf408"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>g</mml:mi><mml:mi>C</mml:mi><mml:mi>a</mml:mi><mml:mi>T</mml:mi><mml:mo stretchy="false">&#x02192;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula>. (<bold>E</bold>) Distribution of <inline-formula><mml:math id="inf409"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>K</mml:mi><mml:mi>C</mml:mi><mml:mi>a</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> current share to the total outward current for <inline-formula><mml:math id="inf410"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>1001</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> values of <inline-formula><mml:math id="inf411"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>g</mml:mi><mml:mi>C</mml:mi><mml:mi>a</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> between <inline-formula><mml:math id="inf412"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>0</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula> and <inline-formula><mml:math id="inf413"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mn>100</mml:mn><mml:mi mathvariant="normal">%</mml:mi></mml:mrow></mml:mstyle></mml:math></inline-formula>.</p></caption><graphic xlink:href="elife-42722-fig14-figsupp1"/></fig></p></fig><p>Performing the same analysis for all conductances results in a large amount of information. Despite this and because we are plotting the normalized currents or current shares, our representation allows us to display this information in a coherent fashion. As an example, in <xref ref-type="fig" rid="fig15">Figure 15</xref> we show the effect of gradually decreasing each current on all the currents in model (c). The rows indicate which conductance is decreased and the columns show the effect of this perturbation on the corresponding current. The first row shows how the shares of each current change as the <inline-formula><mml:math id="inf414"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current is decreased. For instance, the effect of decreasing <inline-formula><mml:math id="inf415"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> on the <inline-formula><mml:math id="inf416"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> current (indicated by *) is as expected, with the maxima of the distribution trending to zero as <inline-formula><mml:math id="inf417"><mml:mrow><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow><mml:mo>&#x02192;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:math></inline-formula>. The effect of removing <inline-formula><mml:math id="inf418"><mml:mrow><mml:mi>g</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> on the other currents is non-trivial and is displayed along the same row. Notice that while the effect of removing a current on that same current (diagonal panels) is relatively predictable, the rest of the currents become rearranged in complicated ways.</p><fig id="fig15" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.019</object-id><label>Figure 15.</label><caption><title>Changes in waveform of current shares as each current is gradually decreased.</title><p>The panels show the probability distribution of the share of each current <inline-formula><mml:math id="inf419"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mrow><mml:mover><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo stretchy="false">^</mml:mo></mml:mover></mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula> for model (c) as each current is decreased.</p></caption><graphic xlink:href="elife-42722-fig15"/></fig><p>Again, a full description of these diagrams is beyond the scope of this work so we will only make some observations. When the pertubations are negligible or weak (<inline-formula><mml:math id="inf420"><mml:mrow><mml:mn>100</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula> to <inline-formula><mml:math id="inf421"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>90</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>) all currents play a role because there are periods of time in which they contribute to at least <inline-formula><mml:math id="inf422"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> of the total current. There are ranges of the conductances over which small changes result in smooth transformations of the current configuration, there are specific values at which sharp transitions take place, and these values are different depending on the current that is decreased. While some of this information can also be extracted from <xref ref-type="fig" rid="fig12">Figures 12</xref> and <xref ref-type="fig" rid="fig13">13</xref>, the diagrams in <xref ref-type="fig" rid="fig15">Figure 15</xref> show how the currents get reorganized at these transitions. In addition, this arrangement is convenient for comparing the effect of decreasing each conductance on a given current. For example, the contributions of the <inline-formula><mml:math id="inf423"><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf424"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:math></inline-formula> currents change little for most perturbations (except when these conductances are decreased). In contrast, the contributions of <inline-formula><mml:math id="inf425"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf426"><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf427"><mml:mi>H</mml:mi></mml:math></inline-formula>, <inline-formula><mml:math id="inf428"><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:math></inline-formula>, and the <inline-formula><mml:math id="inf429"><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:math></inline-formula> change more noticeably. Finally, the contribution of the <inline-formula><mml:math id="inf430"><mml:mi>A</mml:mi></mml:math></inline-formula> current increases for most conductance decrements of any type, except at the transition values where it can grow or shrink in an abrupt manner.</p></sec></sec><sec sec-type="discussion" id="s3"><title>Discussion</title><p>There is an ever larger availability of experimental data to inform detailed models of identified neuron types (<xref rid="bib31" ref-type="bibr">McDougal et al., 2017</xref>). Experimenters have determined the kinetics of many channel types, both in vertebrate and invertebrate neurons. There are also model databases with thousands of parameters which permit the development of large scale models of neural tissue (<xref rid="bib6" ref-type="bibr">Bezaire et al., 2016</xref>). One difficulty in ensemble modeling is the necessity of incorporating the biological variability observed in some of the parameters &#x02013; such as the conductances &#x02013; at the same time that we require the models to capture some target activity. In other words, we may be interested in modeling a type of cell that displays some sterotypical behavior, and would like to obtain many different versions of such models. Two main approaches to this problem were introduced in the past. One consists of building a database of model solutions over a search domain and screening for target solutions: this considers all possible value combinations within an allowed range up to a numerical resolution and then applies quantitative criteria to determine which solutions correspond to the target activity (<xref rid="bib35" ref-type="bibr">Prinz et al., 2004</xref>). An alternative approach consists of designing a target function that assigns a score to the models&#x02019; solutions in such a way that lower scores correspond to solutions that meet the targets, and then optimizing these functions (<xref rid="bib1" ref-type="bibr">Achard and De Schutter, 2006</xref>; <xref rid="bib16" ref-type="bibr">Druckmann et al., 2007</xref>; <xref rid="bib5" ref-type="bibr">Ben-Shalom et al., 2012</xref>).</p><p>Both approaches have advantages and shortcomings. In the case of the database approach, trying all posible parameter combinations in a search range becomes prohibitively expensive as more parameters are allowed to vary. One advantage of this approach is that it provides a notion of how likely it is to find conductances within a search range that will produce the activity. In the landscape approach, we find solutions by optimization and &#x02013; without further analysis &#x02013; we do not know how likely a given solution type is. This approach has the advantage that it can be scaled to include large numbers of parameters. Additionally, if a particular solution is interesting, we can use genetic algorithms on successful target functions to &#x02018;breed&#x02019; as many closely related models as desired. Ultimately, any optimization heuristic requires blind testing random combinations of the parameters, and developing quantitative criteria for screening solutions in a database results in some sort of score function, so the two approaches are complementary. A successful target function can determine if a random perturbation results in disruption of the activity and this can be used to perform population-based sensitivity analyses (<xref rid="bib15" ref-type="bibr">Devenyi and Sobie, 2016</xref>).</p><p>Regardless of the optimization approach, most work is devoted to the design of successful target functions. Different modeling problems require different target functions (<xref rid="bib36" ref-type="bibr">Roemschied et al., 2014</xref>; <xref rid="bib19" ref-type="bibr">Fox et al., 2017</xref>; <xref rid="bib32" ref-type="bibr">Migliore et al., 2018</xref>) and one challenge in their design is that sometimes we do not know a priori if the model contains solutions that will produce good minima. In addition, a poorly constrained target function can feature multiple local minima that could make the optimization harder, so even if there are good minima they may be hard to find. One difference between the landscape functions in <xref rid="bib1" ref-type="bibr">Achard and De Schutter (2006)</xref> and the ones utilized here is that in their setup model solutions are compared to a target <italic>time series</italic> via a phase-plane method. The functions introduced in this work use an analysis based on Poincar&#x000e9; sections or thresholds to characterize the waveform and to define an error or score. Instead of targeting a particular waveform, we ask that some features of the waveform &#x02013; such as the frequency and the burst duration &#x02013; are tightly constrained, while other features &#x02013; such as the concavity of the slow waves &#x02013; can be diverse. This is motivated by the fact that across individuals and species, the activity of the pyloric neurons can be diverse but the neurons always fire in the same sequence and the burst durations have a well-defined mean. Our approach is successful in finding hundreds of models that display a target activity in minutes using a commercially available desktop computer. Application of evolutionary techniques to optimize these functions provides a natural means to model the intrinsic variability observed in biological populations.</p><p>One of the main benefits of computational modeling is that once a behavior of interest is successfully captured we then possess a mechanistic description of the phenomena that can be used to test ideas and inform experiments (<xref rid="bib11" ref-type="bibr">Coggan et al., 2011</xref>; <xref rid="bib29" ref-type="bibr">Lee et al., 2016</xref>; <xref rid="bib15" ref-type="bibr">Devenyi and Sobie, 2016</xref>; <xref rid="bib23" ref-type="bibr">Gong and Sobie, 2018</xref>). As the models gain biophysical detail these advantages wane in the face of the complexity imposed by larger numbers of variables and parameters. Conductance-based models of neural activity generate large amounts of data that can be hard to visualize and interpret. The development of novel visualization procedures has the potential to assist intuition into the details of how these models work (<xref rid="bib24" ref-type="bibr">Gutierrez et al., 2013</xref>). Here, we introduced a novel representation of the dynamics of the ionic currents in a single compartment neuron. Our representation is simple and displays in a concise way the contribution of each current to the activity. This representation is easily generalizable to multi-compartment models and small networks, and to any type of electrically excitable cell, such as models of cardiac cells (<xref rid="bib8" ref-type="bibr">Britton et al., 2017</xref>).</p><p>We employed these procedures to build many similar bursting models with different conductance densities and to study their response to perturbations. The responses of the models to current injections and gradual decrements of their conductances can be diverse and complex. Inspection of the ISI distributions revealed wide ranges of parameter values for which the activity appears irregular, and similar regimes can be attained by gradually removing some of the currents. Period doubling routes to chaos in neurons have been observed experimentally and in conductance-based models (<xref rid="bib25" ref-type="bibr">Hayashi et al., 1982</xref>; <xref rid="bib26" ref-type="bibr">Hayashi and Ishizuka, 1992</xref>; <xref rid="bib43" ref-type="bibr">Sz&#x000fc;cs et al., 2001</xref>; <xref rid="bib10" ref-type="bibr">Canavier et al., 1990</xref>; <xref rid="bib50" ref-type="bibr">Xu et al., 2017</xref>). The sort of bifurcation diagrams displayed by these models upon current injection are qualitatively similar to those exhibited by simplified models of spiking neurons for which further theoretical insight is possible (<xref rid="bib47" ref-type="bibr">Touboul and Brette, 2008</xref>). Period doubling bifurcations and low dimensional chaos arise repeatedly in neural models of different natures including rate models (<xref rid="bib17" ref-type="bibr">Ermentrout, 1984</xref>; <xref rid="bib2" ref-type="bibr">Alonso, 2017</xref>). The bursters studied here are close (in parameter space) to aperiodic or irregular regimes suggesting that such regimes are ubiquitous and not special cases.</p><p>We showed that in these model neurons similar membrane activities can be attained by multiple mechanisms that correspond to different current compositions. Because the dynamical mechanisms driving the activity are different in different models, perturbations can result in qualitatively different scenarios. Our visualization methods allow us to gather intuition on how different these responses can be and to explore the contribution of each current type to the neural activity. Even in the case of single compartment bursters, the response to perturbations of a population can be diverse and hard to describe. To gain intuition into the kind of behaviors the models display upon perturbation, we developed a representation based on the probability of the membrane potential <inline-formula><mml:math id="inf431"><mml:mi>V</mml:mi></mml:math></inline-formula>. This representation permits displaying changes in the waveform of <inline-formula><mml:math id="inf432"><mml:mi>V</mml:mi></mml:math></inline-formula> as each current is blocked. This representation shows that the models respond to perturbations in different ways, but that there are also similarities among their responses. A concise representation of the effect of a perturbation is a necessary step towards developing a classification scheme for the responses.</p></sec><sec sec-type="materials|methods" id="s4"><title>Materials and methods</title><p>Numerical data and data analysis and plotting code, sufficient to reproduce the figures in the paper are available on Dryad Digital Repository (<ext-link ext-link-type="uri" xlink:href="https://dx.doi.org/10.5061/dryad.d0779mb">https://dx.doi.org/10.5061/dryad.d0779mb</ext-link>).</p><sec id="s4-1"><title>Model equations</title><p>The membrane potential <inline-formula><mml:math id="inf433"><mml:mi>V</mml:mi></mml:math></inline-formula> of a cell containing <inline-formula><mml:math id="inf434"><mml:mi>N</mml:mi></mml:math></inline-formula> channels and membrane capacitance <inline-formula><mml:math id="inf435"><mml:mi>C</mml:mi></mml:math></inline-formula> is given by:<disp-formula id="equ5"><label>(5)</label><mml:math id="m5"><mml:mrow><mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:mfrac></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:msub><mml:mi>I</mml:mi><mml:mi>e</mml:mi></mml:msub><mml:mo>-</mml:mo><mml:mrow><mml:munderover><mml:mo largeop="true" movablelimits="false" symmetric="true">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mn>8</mml:mn></mml:munderover><mml:msub><mml:mi>I</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mrow></mml:mrow><mml:mo>.</mml:mo></mml:mrow></mml:math></disp-formula></p><p>Each term in the sum corresponds to a current <inline-formula><mml:math id="inf436"><mml:mrow><mml:msub><mml:mi>I</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02062;</mml:mo><mml:msup><mml:mi>m</mml:mi><mml:msub><mml:mi>p</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:msup><mml:mo>&#x02062;</mml:mo><mml:msup><mml:mi>h</mml:mi><mml:msub><mml:mi>q</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:msup><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mi>V</mml:mi><mml:mo>-</mml:mo><mml:msub><mml:mi>E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf437"><mml:msub><mml:mi>I</mml:mi><mml:mi>e</mml:mi></mml:msub></mml:math></inline-formula> is externally applied current. The maximal conductance of each channel is given by <inline-formula><mml:math id="inf438"><mml:msub><mml:mi>g</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math></inline-formula>, <inline-formula><mml:math id="inf439"><mml:mi>m</mml:mi></mml:math></inline-formula> and <inline-formula><mml:math id="inf440"><mml:mi>h</mml:mi></mml:math></inline-formula> are the activation and inactivation variables, the integers <inline-formula><mml:math id="inf441"><mml:msub><mml:mi>p</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math></inline-formula> and <inline-formula><mml:math id="inf442"><mml:msub><mml:mi>q</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math></inline-formula> are the number of gates in each channel, and <inline-formula><mml:math id="inf443"><mml:msub><mml:mi>E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:math></inline-formula> is the reversal potential of the ion associated with the i-th current. The reversal potential of the Na, K, H and leak currents were kept fixed at <inline-formula><mml:math id="inf444"><mml:mrow><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mn>30</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf445"><mml:mrow><mml:msub><mml:mi>E</mml:mi><mml:mi>K</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>80</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf446"><mml:mrow><mml:msub><mml:mi>E</mml:mi><mml:mi>H</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>20</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf447"><mml:mrow><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>50</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> while the calcium reversal potential <inline-formula><mml:math id="inf448"><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> was computed dynamically using the Nernst equation assuming an extracellular calcium concentration of <inline-formula><mml:math id="inf449"><mml:mrow><mml:mrow><mml:mn>3</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>3</mml:mn></mml:msup></mml:mrow><mml:mo>&#x02062;</mml:mo><mml:mi>&#x003bc;</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:math></inline-formula>. The kinetic equations describing the seven voltage-gated conductances were modeled as in <xref rid="bib30" ref-type="bibr">Liu et al. (1998)</xref>,<disp-formula id="equ6"><label>(6)</label><mml:math id="m6"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mtable columnalign="left left" columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:mstyle displaystyle="true" scriptlevel="0"><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:mfrac><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:msub><mml:mi mathvariant="normal">&#x0221e;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mspace width="thinmathspace"/><mml:mo>&#x02212;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mstyle></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mstyle displaystyle="true" scriptlevel="0"><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:msub><mml:mi>h</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:msub><mml:mi>h</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:mfrac><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mstyle displaystyle="true" scriptlevel="0"><mml:msub><mml:mi>h</mml:mi><mml:mrow><mml:msub><mml:mi mathvariant="normal">&#x0221e;</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>h</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>.</mml:mo></mml:mstyle></mml:mstyle></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>The functions <inline-formula><mml:math id="inf450"><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:msub><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf451"><mml:mrow><mml:msub><mml:mi>m</mml:mi><mml:msub><mml:mi mathvariant="normal">&#x0221e;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:msub><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf452"><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:msub><mml:mi>h</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:msub><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf453"><mml:mrow><mml:msub><mml:mi>h</mml:mi><mml:msub><mml:mi mathvariant="normal">&#x0221e;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:msub><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>V</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> are based on the experimental work of <xref rid="bib48" ref-type="bibr">Turrigiano et al., 1995</xref> and are listed in refs. (<xref rid="bib30" ref-type="bibr">Liu et al., 1998</xref>; <xref rid="bib48" ref-type="bibr">Turrigiano et al., 1995</xref>). The activation functions of the <inline-formula><mml:math id="inf454"><mml:msub><mml:mi>K</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula> current require a measure of the internal calcium concentration <inline-formula><mml:math id="inf455"><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msup><mml:mi>a</mml:mi><mml:mrow><mml:mo>+</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:math></inline-formula>&#x000a0;(<xref rid="bib30" ref-type="bibr">Liu et al., 1998</xref>). This is an important state variable of the cell and its dynamics are given by,<disp-formula id="equ7"><label>(7)</label><mml:math id="m7"><mml:mrow><mml:mrow><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02062;</mml:mo><mml:mfrac><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msup><mml:mi>a</mml:mi><mml:mrow><mml:mo>+</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:mfrac></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mi>F</mml:mi></mml:msub><mml:mo>&#x02062;</mml:mo><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msup><mml:mi>a</mml:mi><mml:mrow><mml:mo>+</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow><mml:mo>+</mml:mo><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mn>0</mml:mn></mml:msub></mml:mrow></mml:mrow></mml:mrow><mml:mo>.</mml:mo></mml:mrow></mml:math></disp-formula></p><p>Here, <inline-formula><mml:math id="inf456"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>C</mml:mi><mml:msub><mml:mi>a</mml:mi><mml:mrow><mml:mi>F</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mn>0.94</mml:mn><mml:mspace width="thinmathspace"/><mml:mfrac><mml:mrow><mml:mi>&#x003bc;</mml:mi><mml:mi>M</mml:mi></mml:mrow><mml:mrow><mml:mi>n</mml:mi><mml:mi>A</mml:mi></mml:mrow></mml:mfrac></mml:mrow></mml:mstyle></mml:math></inline-formula> is a current-to-concentration factor and <inline-formula><mml:math id="inf457"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>C</mml:mi><mml:msub><mml:mi>a</mml:mi><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mn>0.05</mml:mn><mml:mspace width="thinmathspace"/><mml:mrow><mml:mi>&#x003bc;</mml:mi><mml:mi>M</mml:mi></mml:mrow></mml:mrow></mml:mstyle></mml:math></inline-formula>. These values were originally taken from Liu et al. and were kept fixed. Finally, <inline-formula><mml:math id="inf458"><mml:mrow><mml:mi>C</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mn>10</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>F</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>. The number of state variables or dimension of the model is <inline-formula><mml:math id="inf459"><mml:mn>13</mml:mn></mml:math></inline-formula>. We explored the solutions of this model in a range of values of the maximal conductances and calcium buffering time scales. The units for voltage are <inline-formula><mml:math id="inf460"><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math></inline-formula>, the conductances are expressed in <inline-formula><mml:math id="inf461"><mml:mrow><mml:mi>&#x003bc;</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula> and currents in <inline-formula><mml:math id="inf462"><mml:mrow><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula>. Voltage traces were obtained by numerical integration of <xref ref-type="disp-formula" rid="equ5">Equation 5</xref> using a Runge-Kutta order <inline-formula><mml:math id="inf463"><mml:mn>4</mml:mn></mml:math></inline-formula> (RK4) method with a time step of <inline-formula><mml:math id="inf464"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mn>0.1</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>&#x000a0;(<xref rid="bib34" ref-type="bibr">Press et al., 1988</xref>). We used the same set of initial conditions for all simulations in this work <inline-formula><mml:math id="inf465"><mml:mrow><mml:mi>V</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>51</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf466"><mml:mrow><mml:mrow><mml:mi>m</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mi>h</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf467"><mml:mrow><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:msup><mml:mi>a</mml:mi><mml:mrow><mml:mo>+</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mn>5</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>&#x003bc;</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>. For some values of the parameters, the system (<xref ref-type="disp-formula" rid="equ5">Equation 5</xref>) can display multistability (<xref rid="bib12" ref-type="bibr">Cymbalyuk et al., 2002</xref>; <xref rid="bib40" ref-type="bibr">Shilnikov et al., 2005</xref>).</p></sec><sec id="s4-2"><title>Optimization of target function</title><p>Optimization of the objective function <xref ref-type="disp-formula" rid="equ2">Equation 2</xref> is useful to produce sets of parameters <inline-formula><mml:math id="inf468"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">g</mml:mi></mml:mrow></mml:mrow></mml:mstyle></mml:math></inline-formula> that result in bursting regimes. In this work, the optimization was performed over a search space of allowed values listed here: we searched for <inline-formula><mml:math id="inf469"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>N</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>3</mml:mn></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> ([<inline-formula><mml:math id="inf470"><mml:mrow><mml:mi>&#x003bc;</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:math></inline-formula>]), <inline-formula><mml:math id="inf471"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>T</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf472"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>S</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf473"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mi>A</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>2</mml:mn></mml:msup><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf474"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>3</mml:mn></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf475"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mrow><mml:mi>K</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf476"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mi>H</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf477"><mml:mrow><mml:msub><mml:mi>g</mml:mi><mml:mi>L</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>10</mml:mn></mml:mrow><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf478"><mml:mrow><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>3</mml:mn></mml:msup><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> ([msecs]). We minimized the objective function using a standard genetic algorithm <xref rid="bib28" ref-type="bibr">Holland (1992)</xref>. This is optimization technique is useful to produce large pools of different&#x000a0;solutions and is routinely utilized to estimate parameters in biophysical models (see for example <xref rid="bib3" ref-type="bibr">Assaneo and Trevisan, 2010</xref>). The algorithm was started with a population of <inline-formula><mml:math id="inf479"><mml:mn>1000</mml:mn></mml:math></inline-formula> random seeds that were evolved for <inline-formula><mml:math id="inf480"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mn>10000</mml:mn></mml:mrow></mml:math></inline-formula> generations. The mutation rate was <inline-formula><mml:math id="inf481"><mml:mrow><mml:mn>5</mml:mn><mml:mo lspace="0pt" rspace="3.5pt">%</mml:mo></mml:mrow></mml:math></inline-formula>. Fitter individuals were chosen more often to breed new solutions (elitism parameter was <inline-formula><mml:math id="inf482"><mml:mn>1.2</mml:mn></mml:math></inline-formula> with <inline-formula><mml:math id="inf483"><mml:mn>1</mml:mn></mml:math></inline-formula> corresponding to equal breeding probability). The computation was performed on a multicore desktop computer (<inline-formula><mml:math id="inf484"><mml:mn>32</mml:mn></mml:math></inline-formula> threads) and takes about <inline-formula><mml:math id="inf485"><mml:mrow><mml:mi/><mml:mo>&#x02248;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math></inline-formula> hr to produce good solutions.</p></sec><sec id="s4-3"><title>Currentscapes</title><p>The currentscapes are stacked area plots of the normalized currents. Although it is easy to describe their meaning, a precise mathematical definition of the images in <xref ref-type="fig" rid="fig2">Figure 2</xref> can appear daunting in a first glance. Fortunately, the implementation of this procedure results in simple python code.</p><p>The time series of the <inline-formula><mml:math id="inf486"><mml:mn>8</mml:mn></mml:math></inline-formula> currents can be represented by a matrix <inline-formula><mml:math id="inf487"><mml:mi>C</mml:mi></mml:math></inline-formula> with <inline-formula><mml:math id="inf488"><mml:mn>8</mml:mn></mml:math></inline-formula> rows and <inline-formula><mml:math id="inf489"><mml:mrow><mml:mrow><mml:msub><mml:mi>n</mml:mi><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi></mml:mrow></mml:msub><mml:mo>&#x000d7;</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow></mml:mfrac></mml:mrow><mml:mo>=</mml:mo><mml:mi>N</mml:mi></mml:mrow></mml:math></inline-formula> columns. For simplicity, we give a formal definition of the currentscapes for positive currents. The definition is identical for both current signs and is applied independently for each. We construct a matrix of positive currents <inline-formula><mml:math id="inf490"><mml:msup><mml:mi>C</mml:mi><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> by setting all negative elements of <inline-formula><mml:math id="inf491"><mml:mi>C</mml:mi></mml:math></inline-formula> to zero, <inline-formula><mml:math id="inf492"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msubsup><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mo>+</mml:mo></mml:mrow></mml:msubsup><mml:mo>=</mml:mo><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02223;</mml:mo><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>&#x0003e;</mml:mo><mml:mspace width="thinmathspace"/><mml:mn>0</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula> and <inline-formula><mml:math id="inf493"><mml:mrow><mml:msubsup><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow><mml:mo>+</mml:mo></mml:msubsup><mml:mo>=</mml:mo><mml:mrow><mml:mn>0</mml:mn><mml:mo lspace="2.5pt" rspace="2.5pt">&#x02223;</mml:mo><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>&#x02264;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:math></inline-formula>. Summing <inline-formula><mml:math id="inf494"><mml:msup><mml:mi>C</mml:mi><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> over rows results in a normalization vector <inline-formula><mml:math id="inf495"><mml:msup><mml:mi>n</mml:mi><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> with <inline-formula><mml:math id="inf496"><mml:mi>N</mml:mi></mml:math></inline-formula> elements <inline-formula><mml:math id="inf497"><mml:mrow><mml:msubsup><mml:mi>n</mml:mi><mml:mi>j</mml:mi><mml:mo>+</mml:mo></mml:msubsup><mml:mo>=</mml:mo><mml:mrow><mml:msub><mml:mo largeop="true" symmetric="true">&#x02211;</mml:mo><mml:mi>i</mml:mi></mml:msub><mml:msubsup><mml:mi>C</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow><mml:mo>+</mml:mo></mml:msubsup></mml:mrow></mml:mrow></mml:math></inline-formula>. The normalized positive currents can be obtained as <inline-formula><mml:math id="inf498"><mml:mrow><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>+</mml:mo></mml:msup><mml:mo>=</mml:mo><mml:mrow><mml:msup><mml:mi>C</mml:mi><mml:mo>+</mml:mo></mml:msup><mml:mo>/</mml:mo><mml:msup><mml:mi>n</mml:mi><mml:mo>+</mml:mo></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> (element by element or entry-wise product). Matrix <inline-formula><mml:math id="inf499"><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> is hard to visualize as it is. The columns of <inline-formula><mml:math id="inf500"><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> correspond to the shares of each positive current and can be displayed as pie charts (see <xref ref-type="fig" rid="fig2">Figure 2</xref>). Here, instead of mapping the shares to a pie we map them to a segmented vertical &#x02018;churro&#x02019;. The currentscapes are generated by constructing a new matrix <inline-formula><mml:math id="inf501"><mml:msub><mml:mi>C</mml:mi><mml:mi>S</mml:mi></mml:msub></mml:math></inline-formula> whose number of rows is given by a resolution factor <inline-formula><mml:math id="inf502"><mml:mrow><mml:mi>R</mml:mi><mml:mo>=</mml:mo><mml:mn>2000</mml:mn></mml:mrow></mml:math></inline-formula>, and the same number of columns <inline-formula><mml:math id="inf503"><mml:mi>N</mml:mi></mml:math></inline-formula> as <inline-formula><mml:math id="inf504"><mml:mi>C</mml:mi></mml:math></inline-formula>. Each column <inline-formula><mml:math id="inf505"><mml:mi>j</mml:mi></mml:math></inline-formula> of <inline-formula><mml:math id="inf506"><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> produces one column <inline-formula><mml:math id="inf507"><mml:mi>j</mml:mi></mml:math></inline-formula> of <inline-formula><mml:math id="inf508"><mml:msub><mml:mi>C</mml:mi><mml:mi>S</mml:mi></mml:msub></mml:math></inline-formula>. Introducing the auxiliary variable <inline-formula><mml:math id="inf509"><mml:mrow><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:msubsup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow><mml:mo>+</mml:mo></mml:msubsup><mml:mo>*</mml:mo><mml:mi>R</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> we can define the currentscape as,<disp-formula id="equ8"><label>(8)</label><mml:math id="m8"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mrow><mml:msub><mml:mi>S</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mspace width="thinmathspace"/><mml:mo>=</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>k</mml:mi><mml:mo>&#x02223;</mml:mo><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:munderover><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02264;</mml:mo><mml:mi>i</mml:mi><mml:mspace width="thinmathspace"/><mml:mo>&#x0003c;</mml:mo><mml:mspace width="thinmathspace"/><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>k</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:munderover><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>.</mml:mo></mml:mrow></mml:mstyle></mml:math></disp-formula></p><p>The current types are indexed by <inline-formula><mml:math id="inf510"><mml:mrow><mml:mi>k</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mn>7</mml:mn><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> and we assume <inline-formula><mml:math id="inf511"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:munderover><mml:msub><mml:mi>p</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>,</mml:mo><mml:mspace width="thinmathspace"/><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:mstyle></mml:math></inline-formula>. The black filled curve in <xref ref-type="fig" rid="fig2">Figure 2B</xref> corresponds to the normalization vector <inline-formula><mml:math id="inf512"><mml:msup><mml:mi>n</mml:mi><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> plotted in logarithmic scale. We placed dotted lines at <inline-formula><mml:math id="inf513"><mml:mrow><mml:mn>5</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="inf514"><mml:mrow><mml:mn>50</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf515"><mml:mrow><mml:mn>500</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula> for reference throughout this work. The currentscapes for the negative currents are obtained by applying definition (<xref ref-type="disp-formula" rid="equ8">Equation 8</xref>) to a matrix of negative <inline-formula><mml:math id="inf516"><mml:msup><mml:mi>C</mml:mi><mml:mo>-</mml:mo></mml:msup></mml:math></inline-formula> currents defined in an analogous way as <inline-formula><mml:math id="inf517"><mml:msup><mml:mi>C</mml:mi><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula>. Finally, note that matrices <inline-formula><mml:math id="inf518"><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>+</mml:mo></mml:msup></mml:math></inline-formula> and <inline-formula><mml:math id="inf519"><mml:msup><mml:mover accent="true"><mml:mi>C</mml:mi><mml:mo stretchy="false">^</mml:mo></mml:mover><mml:mo>-</mml:mo></mml:msup></mml:math></inline-formula> are difficult to visualize as they are. The transformation given by definition (<xref ref-type="disp-formula" rid="equ8">Equation 8</xref>) is useful to display their contents.</p></sec><sec id="s4-4"><title>ISI distributions</title><p>We inspected the effects of injecting currents in our models by computing the inter-spike interval ISI distributions. For this, we started the models from the same initial condition and simulated them for <inline-formula><mml:math id="inf520"><mml:mn>580</mml:mn></mml:math></inline-formula> s. We dropped the first <inline-formula><mml:math id="inf521"><mml:mn>240</mml:mn></mml:math></inline-formula> s to remove transient activity and kept the last <inline-formula><mml:math id="inf522"><mml:mn>240</mml:mn></mml:math></inline-formula> s for analysis. Spikes were detected as described before. We collected ISI values for <inline-formula><mml:math id="inf523"><mml:mrow><mml:mi>N</mml:mi><mml:mo>=</mml:mo><mml:mn>1001</mml:mn></mml:mrow></mml:math></inline-formula> values of injected current equally spaced between <inline-formula><mml:math id="inf524"><mml:mrow><mml:mo>-</mml:mo><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="inf525"><mml:mrow><mml:mn>5</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>n</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:math></inline-formula>.</p></sec><sec id="s4-5"><title>V distributions</title><p>To sample the distributions of <inline-formula><mml:math id="inf526"><mml:mi>V</mml:mi></mml:math></inline-formula> we simulated the system with high temporal resolution (<inline-formula><mml:math id="inf527"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>t</mml:mi></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mn>0.001</mml:mn><mml:mo>&#x02062;</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>e</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>c</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> ) for <inline-formula><mml:math id="inf528"><mml:mn>30</mml:mn></mml:math></inline-formula> s, after dropping the first <inline-formula><mml:math id="inf529"><mml:mn>120</mml:mn></mml:math></inline-formula> s to remove transients. We then sampled the numerical solution at random time stamps and kept <inline-formula><mml:math id="inf530"><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mn>6</mml:mn></mml:msup></mml:mrow></mml:math></inline-formula> samples <inline-formula><mml:math id="inf531"><mml:mrow><mml:mi>V</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo stretchy="false">{</mml:mo><mml:msub><mml:mi>V</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo stretchy="false">}</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> for each percent value. We took <inline-formula><mml:math id="inf532"><mml:mn>1001</mml:mn></mml:math></inline-formula> values between <inline-formula><mml:math id="inf533"><mml:mn>1</mml:mn></mml:math></inline-formula> and 0.</p></sec><sec id="s4-6"><title>Parameters</title><p>Model parameters used in this study are listed in <xref rid="table1" ref-type="table">Table 1</xref>.</p><table-wrap id="table1" orientation="portrait" position="float"><object-id pub-id-type="doi">10.7554/eLife.42722.020</object-id><label>Table 1.</label><caption><title>Parameters used in this study and error value.</title></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="1" colspan="1"/><th align="center" rowspan="1" colspan="1">gNa</th><th align="center" rowspan="1" colspan="1">gCaT</th><th align="center" rowspan="1" colspan="1">gCaS</th><th align="center" rowspan="1" colspan="1">gA</th><th align="center" rowspan="1" colspan="1">gKCa</th><th rowspan="1" colspan="1">gKd</th><th align="center" rowspan="1" colspan="1">gH</th><th align="center" rowspan="1" colspan="1">gL</th><th align="center" rowspan="1" colspan="1"><bold><inline-formula><mml:math id="inf534"><mml:msub><mml:mi>&#x003c4;</mml:mi><mml:mrow><mml:mi>C</mml:mi><mml:mo>&#x02062;</mml:mo><mml:mi>a</mml:mi></mml:mrow></mml:msub></mml:math></inline-formula></bold></th><th align="left" rowspan="1" colspan="1"><bold><inline-formula><mml:math id="inf535"><mml:mstyle displaystyle="true" scriptlevel="0"><mml:mrow><mml:mi>E</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mi mathvariant="bold">g</mml:mi></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mstyle></mml:math></inline-formula></bold></th></tr></thead><tbody><tr><td rowspan="1" colspan="1">model (a)</td><td align="center" rowspan="1" colspan="1">1076.392</td><td align="center" rowspan="1" colspan="1">6.4056</td><td align="center" rowspan="1" colspan="1">10.048</td><td align="center" rowspan="1" colspan="1">8.0384</td><td align="center" rowspan="1" colspan="1">17.584</td><td rowspan="1" colspan="1">124.0928</td><td align="center" rowspan="1" colspan="1">0.11304</td><td align="center" rowspan="1" colspan="1">0.17584</td><td align="center" rowspan="1" colspan="1">653.5</td><td align="left" rowspan="1" colspan="1">0.051</td></tr><tr><td rowspan="1" colspan="1">model (b)</td><td align="center" rowspan="1" colspan="1">1165.568</td><td align="center" rowspan="1" colspan="1">6.6568</td><td align="center" rowspan="1" colspan="1">9.5456</td><td align="center" rowspan="1" colspan="1">54.5104</td><td align="center" rowspan="1" colspan="1">16.328</td><td rowspan="1" colspan="1">110.7792</td><td align="center" rowspan="1" colspan="1">0.0628</td><td align="center" rowspan="1" colspan="1">0.10676</td><td align="center" rowspan="1" colspan="1">813.88</td><td align="left" rowspan="1" colspan="1">0.053</td></tr><tr><td rowspan="1" colspan="1">model (c)</td><td align="center" rowspan="1" colspan="1">1228.368</td><td align="center" rowspan="1" colspan="1">7.0336</td><td align="center" rowspan="1" colspan="1">11.0528</td><td align="center" rowspan="1" colspan="1">117.5616</td><td align="center" rowspan="1" colspan="1">16.328</td><td rowspan="1" colspan="1">111.2816</td><td align="center" rowspan="1" colspan="1">0.13816</td><td align="center" rowspan="1" colspan="1">0.10676</td><td align="center" rowspan="1" colspan="1">605.98</td><td align="left" rowspan="1" colspan="1">0.027</td></tr><tr><td rowspan="1" colspan="1">model (d)</td><td align="center" rowspan="1" colspan="1">1203.248</td><td align="center" rowspan="1" colspan="1">6.6568</td><td align="center" rowspan="1" colspan="1">10.5504</td><td align="center" rowspan="1" colspan="1">59.5344</td><td align="center" rowspan="1" colspan="1">16.328</td><td rowspan="1" colspan="1">111.4072</td><td align="center" rowspan="1" colspan="1">0.0</td><td align="center" rowspan="1" colspan="1">0.10676</td><td align="center" rowspan="1" colspan="1">653.5</td><td align="left" rowspan="1" colspan="1">0.471</td></tr><tr><td rowspan="1" colspan="1">model (e)</td><td align="center" rowspan="1" colspan="1">1210.784</td><td align="center" rowspan="1" colspan="1">8.164</td><td align="center" rowspan="1" colspan="1">6.28</td><td align="center" rowspan="1" colspan="1">113.04</td><td align="center" rowspan="1" colspan="1">12.56</td><td rowspan="1" colspan="1">118.4408</td><td align="center" rowspan="1" colspan="1">0.1256</td><td align="center" rowspan="1" colspan="1">0.0314</td><td align="center" rowspan="1" colspan="1">393.13</td><td align="left" rowspan="1" colspan="1">0.109</td></tr><tr><td rowspan="1" colspan="1">model (f)</td><td align="center" rowspan="1" colspan="1">1245.952</td><td align="center" rowspan="1" colspan="1">7.7872</td><td align="center" rowspan="1" colspan="1">6.7824</td><td align="center" rowspan="1" colspan="1">84.6544</td><td align="center" rowspan="1" colspan="1">12.56</td><td rowspan="1" colspan="1">113.9192</td><td align="center" rowspan="1" colspan="1">0.02512</td><td align="center" rowspan="1" colspan="1">0.0</td><td align="center" rowspan="1" colspan="1">174.34</td><td align="left" rowspan="1" colspan="1">0.047</td></tr><tr><td rowspan="1" colspan="1">model (<xref ref-type="fig" rid="fig2">Figure 2</xref>)</td><td align="center" rowspan="1" colspan="1">1228.368</td><td align="center" rowspan="1" colspan="1">7.0336</td><td align="center" rowspan="1" colspan="1">11.0528</td><td align="center" rowspan="1" colspan="1">117.5616</td><td align="center" rowspan="1" colspan="1">16.328</td><td rowspan="1" colspan="1">110.7792</td><td align="center" rowspan="1" colspan="1">0.13816</td><td align="center" rowspan="1" colspan="1">0.10048</td><td align="center" rowspan="1" colspan="1">605.98</td><td align="left" rowspan="1" colspan="1">0.007</td></tr><tr><td rowspan="1" colspan="1">model (<xref ref-type="fig" rid="fig3">Figure 3</xref>)</td><td align="center" rowspan="1" colspan="1">895.528</td><td align="center" rowspan="1" colspan="1">3.8936</td><td align="center" rowspan="1" colspan="1">16.5792</td><td align="center" rowspan="1" colspan="1">116.4312</td><td align="center" rowspan="1" colspan="1">21.352</td><td rowspan="1" colspan="1">115.6776</td><td align="center" rowspan="1" colspan="1">0.0</td><td align="center" rowspan="1" colspan="1">0.08792</td><td align="center" rowspan="1" colspan="1">828.73</td><td align="left" rowspan="1" colspan="1">0.058</td></tr></tbody></table></table-wrap></sec></sec></body><back><sec sec-type="funding-information"><title>Funding Information</title><p>This paper was supported by the following grants:</p><list list-type="bullet"><list-item><p><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source>
<award-id>R35 NS097343</award-id> to Eve Marder.</p></list-item><list-item><p><funding-source><institution-wrap><institution>Swartz Foundation</institution></institution-wrap></funding-source>
<award-id>2017</award-id> to Leandro M Alonso.</p></list-item><list-item><p><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source>
<award-id>MH046742</award-id> to Eve Marder.</p></list-item><list-item><p><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source>
<award-id>T32 NS07292</award-id> to Leandro M Alonso.</p></list-item></list></sec><ack id="ack"><title>Acknowledgements</title><p>LMA acknowledges Marcos Trevisan for early discussions on genetic algorithms and Francisco Roslan for programming training.</p></ack><sec id="s5" sec-type="additional-information"><title>Additional information</title><fn-group content-type="competing-interest"><title><bold>Competing interests</bold></title><fn fn-type="COI-statement" id="conf2"><p>Senior editor, <italic>eLife</italic>.</p></fn><fn fn-type="COI-statement" id="conf1"><p>No competing interests declared.</p></fn></fn-group><fn-group content-type="author-contribution"><title><bold>Author contributions</bold></title><fn fn-type="con" id="con1"><p>Software, Formal analysis, Investigation, Visualization, Writing&#x02014;original draft, Writing&#x02014;review and editing.</p></fn><fn fn-type="con" id="con2"><p>Conceptualization, Resources, Supervision, Visualization, Writing&#x02014;review and editing.</p></fn></fn-group></sec><sec id="s6" sec-type="supplementary-material"><title>Additional files</title><supplementary-material content-type="local-data" id="transrepform"><object-id pub-id-type="doi">10.7554/eLife.42722.021</object-id><label>Transparent reporting form</label><media mime-subtype="pdf" mimetype="application" xlink:href="elife-42722-transrepform.pdf" orientation="portrait" id="d35e10104" position="anchor"/></supplementary-material></sec><sec id="s7" sec-type="data-availability"><title>Data availability</title><p>All data generated or analysed during this study are included in the manuscript and supporting files. Source data files have been provided for Figures 2 through 15. Data package available in Dryad: doi:10.5061/dryad.d0779mb.</p><p>The following dataset was generated:</p><p><element-citation publication-type="data" id="dataset1"><person-group person-group-type="author"><name><surname>Alonso</surname><given-names>LM</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name></person-group><year iso-8601-date="2019">2019</year><data-title>Data from: Visualization of currents in neural models with similar behavior and different conductance densities</data-title><source>Dryad Digital Repository</source><pub-id pub-id-type="doi">10.5061/dryad.d0779mb</pub-id></element-citation></p></sec><ref-list><title>References</title><ref id="bib1"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Achard</surname><given-names>P</given-names></name><name><surname>De Schutter</surname><given-names>E</given-names></name></person-group><year>2006</year><article-title>Complex parameter landscape for a complex neuron model</article-title><source>PLOS Computational Biology</source><volume>2</volume><elocation-id>e94</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.0020094</pub-id><?supplied-pmid 16848639?><pub-id pub-id-type="pmid">16848639</pub-id></element-citation></ref><ref id="bib2"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alonso</surname><given-names>LM</given-names></name></person-group><year>2017</year><article-title>Nonlinear resonances and multi-stability in simple neural circuits</article-title><source>Chaos: An Interdisciplinary Journal of Nonlinear Science</source><volume>27</volume><fpage>013118</fpage><pub-id pub-id-type="doi">10.1063/1.4974028</pub-id></element-citation></ref><ref id="bib3"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Assaneo</surname><given-names>MF</given-names></name><name><surname>Trevisan</surname><given-names>MA</given-names></name></person-group><year>2010</year><article-title>Computational model for vocal tract dynamics in a suboscine bird</article-title><source>Physical Review E</source><volume>82</volume><pub-id pub-id-type="doi">10.1103/PhysRevE.82.031906</pub-id></element-citation></ref><ref id="bib4"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Balachandar</surname><given-names>A</given-names></name><name><surname>Prescott</surname><given-names>SA</given-names></name></person-group><year>2018</year><article-title>Origin of heterogeneous spiking patterns from continuously distributed ion channel densities: a computational study in spinal dorsal horn neurons</article-title><source>The Journal of Physiology</source><volume>596</volume><fpage>1681</fpage><lpage>1697</lpage><pub-id pub-id-type="doi">10.1113/JP275240</pub-id><?supplied-pmid 29352464?><pub-id pub-id-type="pmid">29352464</pub-id></element-citation></ref><ref id="bib5"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ben-Shalom</surname><given-names>R</given-names></name><name><surname>Aviv</surname><given-names>A</given-names></name><name><surname>Razon</surname><given-names>B</given-names></name><name><surname>Korngreen</surname><given-names>A</given-names></name></person-group><year>2012</year><article-title>Optimizing ion channel models using a parallel genetic algorithm on graphical processors</article-title><source>Journal of Neuroscience Methods</source><volume>206</volume><fpage>183</fpage><lpage>194</lpage><pub-id pub-id-type="doi">10.1016/j.jneumeth.2012.02.024</pub-id><?supplied-pmid 22407006?><pub-id pub-id-type="pmid">22407006</pub-id></element-citation></ref><ref id="bib6"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bezaire</surname><given-names>MJ</given-names></name><name><surname>Raikov</surname><given-names>I</given-names></name><name><surname>Burk</surname><given-names>K</given-names></name><name><surname>Vyas</surname><given-names>D</given-names></name><name><surname>Soltesz</surname><given-names>I</given-names></name></person-group><year>2016</year><article-title>Interneuronal mechanisms of hippocampal theta oscillations in a full-scale model of the rodent CA1 circuit</article-title><source>eLife</source><volume>5</volume><elocation-id>e18566</elocation-id><pub-id pub-id-type="doi">10.7554/eLife.18566</pub-id><?supplied-pmid 28009257?><pub-id pub-id-type="pmid">28009257</pub-id></element-citation></ref><ref id="bib7"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bhalla</surname><given-names>US</given-names></name><name><surname>Bower</surname><given-names>JM</given-names></name></person-group><year>1993</year><article-title>Exploring parameter space in detailed single neuron models: simulations of the mitral and granule cells of the olfactory bulb</article-title><source>Journal of Neurophysiology</source><volume>69</volume><fpage>1948</fpage><lpage>1965</lpage><pub-id pub-id-type="doi">10.1152/jn.1993.69.6.1948</pub-id><?supplied-pmid 7688798?><pub-id pub-id-type="pmid">7688798</pub-id></element-citation></ref><ref id="bib8"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Britton</surname><given-names>OJ</given-names></name><name><surname>Bueno-Orovio</surname><given-names>A</given-names></name><name><surname>Vir&#x000e1;g</surname><given-names>L</given-names></name><name><surname>Varr&#x000f3;</surname><given-names>A</given-names></name><name><surname>Rodriguez</surname><given-names>B</given-names></name></person-group><year>2017</year><article-title>The electrogenic na<sup>+</sup>/K<sup>+</sup> Pump Is a key determinant of repolarization abnormality susceptibility in human ventricular cardiomyocytes: a population-based simulation study</article-title><source>Frontiers in Physiology</source><volume>8</volume><elocation-id>278</elocation-id><pub-id pub-id-type="doi">10.3389/fphys.2017.00278</pub-id><?supplied-pmid 28529489?><pub-id pub-id-type="pmid">28529489</pub-id></element-citation></ref><ref id="bib9"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Calabrese</surname><given-names>RL</given-names></name></person-group><year>2018</year><article-title>Inconvenient truth to principle of neuroscience</article-title><source>Trends in Neurosciences</source><volume>41</volume><fpage>488</fpage><lpage>491</lpage><pub-id pub-id-type="doi">10.1016/j.tins.2018.05.006</pub-id><?supplied-pmid 30053951?><pub-id pub-id-type="pmid">30053951</pub-id></element-citation></ref><ref id="bib10"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Canavier</surname><given-names>CC</given-names></name><name><surname>Clark</surname><given-names>JW</given-names></name><name><surname>Byrne</surname><given-names>JH</given-names></name></person-group><year>1990</year><article-title>Routes to chaos in a model of a bursting neuron</article-title><source>Biophysical Journal</source><volume>57</volume><fpage>1245</fpage><lpage>1251</lpage><pub-id pub-id-type="doi">10.1016/S0006-3495(90)82643-6</pub-id><?supplied-pmid 1697484?><pub-id pub-id-type="pmid">1697484</pub-id></element-citation></ref><ref id="bib11"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Coggan</surname><given-names>JS</given-names></name><name><surname>Ocker</surname><given-names>GK</given-names></name><name><surname>Sejnowski</surname><given-names>TJ</given-names></name><name><surname>Prescott</surname><given-names>SA</given-names></name></person-group><year>2011</year><article-title>Explaining pathological changes in axonal excitability through dynamical analysis of conductance-based models</article-title><source>Journal of Neural Engineering</source><volume>8</volume><elocation-id>065002</elocation-id><pub-id pub-id-type="doi">10.1088/1741-2560/8/6/065002</pub-id><?supplied-pmid 22058273?><pub-id pub-id-type="pmid">22058273</pub-id></element-citation></ref><ref id="bib12"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cymbalyuk</surname><given-names>GS</given-names></name><name><surname>Gaudry</surname><given-names>Q</given-names></name><name><surname>Masino</surname><given-names>MA</given-names></name><name><surname>Calabrese</surname><given-names>RL</given-names></name></person-group><year>2002</year><article-title>Bursting in leech heart interneurons: cell-autonomous and network-based mechanisms</article-title><source>The Journal of Neuroscience</source><volume>22</volume><fpage>10580</fpage><lpage>10592</lpage><pub-id pub-id-type="doi">10.1523/JNEUROSCI.22-24-10580.2002</pub-id><?supplied-pmid 12486150?><pub-id pub-id-type="pmid">12486150</pub-id></element-citation></ref><ref id="bib13"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cymbalyuk</surname><given-names>GS</given-names></name><name><surname>Calabrese</surname><given-names>RL</given-names></name></person-group><year>2000</year><article-title>Oscillatory behaviors in pharmacologically isolated heart interneurons from the medicinal leech</article-title><source>Neurocomputing</source><volume>32-33</volume><fpage>97</fpage><lpage>104</lpage><pub-id pub-id-type="doi">10.1016/S0925-2312(00)00149-1</pub-id></element-citation></ref><ref id="bib14"><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Dayan</surname><given-names>P</given-names></name><name><surname>Abbott</surname><given-names>LF</given-names></name></person-group><year>2001</year><source>Theoretical Neuroscience</source><publisher-loc>Cambridge</publisher-loc><publisher-name>MIT Press</publisher-name></element-citation></ref><ref id="bib15"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Devenyi</surname><given-names>RA</given-names></name><name><surname>Sobie</surname><given-names>EA</given-names></name></person-group><year>2016</year><article-title>There and back again: iterating between population-based modeling and experiments reveals surprising regulation of calcium transients in rat cardiac myocytes</article-title><source>Journal of Molecular and Cellular Cardiology</source><volume>96</volume><fpage>38</fpage><lpage>48</lpage><pub-id pub-id-type="doi">10.1016/j.yjmcc.2015.07.016</pub-id><?supplied-pmid 26235057?><pub-id pub-id-type="pmid">26235057</pub-id></element-citation></ref><ref id="bib16"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Druckmann</surname><given-names>S</given-names></name><name><surname>Banitt</surname><given-names>Y</given-names></name><name><surname>Gidon</surname><given-names>A</given-names></name><name><surname>Sch&#x000fc;rmann</surname><given-names>F</given-names></name><name><surname>Markram</surname><given-names>H</given-names></name><name><surname>Segev</surname><given-names>I</given-names></name></person-group><year>2007</year><article-title>A novel multiple objective optimization framework for constraining conductance-based neuron models by experimental data</article-title><source>Frontiers in Neuroscience</source><volume>1</volume><fpage>7</fpage><lpage>18</lpage><pub-id pub-id-type="doi">10.3389/neuro.**********.2007</pub-id><?supplied-pmid 18982116?><pub-id pub-id-type="pmid">18982116</pub-id></element-citation></ref><ref id="bib17"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ermentrout</surname><given-names>GB</given-names></name></person-group><year>1984</year><article-title>Period doublings and possible chaos in neural models</article-title><source>SIAM Journal on Applied Mathematics</source><volume>44</volume><fpage>80</fpage><lpage>95</lpage><pub-id pub-id-type="doi">10.1137/0144007</pub-id></element-citation></ref><ref id="bib18"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Feigenbaum</surname><given-names>MJ</given-names></name></person-group><year>1978</year><article-title>Quantitative universality for a class of nonlinear transformations</article-title><source>Journal of Statistical Physics</source><volume>19</volume><fpage>25</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1007/BF01020332</pub-id></element-citation></ref><ref id="bib19"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fox</surname><given-names>DM</given-names></name><name><surname>Tseng</surname><given-names>HA</given-names></name><name><surname>Smolinski</surname><given-names>TG</given-names></name><name><surname>Rotstein</surname><given-names>HG</given-names></name><name><surname>Nadim</surname><given-names>F</given-names></name></person-group><year>2017</year><article-title>Mechanisms of generation of membrane potential resonance in a neuron with multiple resonant ionic currents</article-title><source>PLOS Computational Biology</source><volume>13</volume><elocation-id>e1005565</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1005565</pub-id><?supplied-pmid 28582395?><pub-id pub-id-type="pmid">28582395</pub-id></element-citation></ref><ref id="bib20"><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Goldberg</surname><given-names>DE</given-names></name><name><surname>Holland</surname><given-names>JH</given-names></name></person-group><year>1988</year><chapter-title>Machine Learning and Its Applications</chapter-title><source>Genetic Algorithms and Machine Learning</source><volume>3</volume><publisher-name>Springer</publisher-name><fpage>95</fpage><lpage>99</lpage></element-citation></ref><ref id="bib21"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Goldman</surname><given-names>MS</given-names></name><name><surname>Golowasch</surname><given-names>J</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name><name><surname>Abbott</surname><given-names>LF</given-names></name></person-group><year>2001</year><article-title>Global structure, robustness, and modulation of neuronal models</article-title><source>The Journal of Neuroscience</source><volume>21</volume><fpage>5229</fpage><lpage>5238</lpage><pub-id pub-id-type="doi">10.1523/JNEUROSCI.21-14-05229.2001</pub-id><?supplied-pmid 11438598?><pub-id pub-id-type="pmid">11438598</pub-id></element-citation></ref><ref id="bib22"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Golowasch</surname><given-names>J</given-names></name><name><surname>Goldman</surname><given-names>MS</given-names></name><name><surname>Abbott</surname><given-names>LF</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name></person-group><year>2002</year><article-title>Failure of averaging in the construction of a conductance-based neuron model</article-title><source>Journal of Neurophysiology</source><volume>87</volume><fpage>1129</fpage><lpage>1131</lpage><pub-id pub-id-type="doi">10.1152/jn.00412.2001</pub-id><?supplied-pmid 11826077?><pub-id pub-id-type="pmid">11826077</pub-id></element-citation></ref><ref id="bib23"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gong</surname><given-names>JQX</given-names></name><name><surname>Sobie</surname><given-names>EA</given-names></name></person-group><year>2018</year><article-title>Population-based mechanistic modeling allows for quantitative predictions of drug responses across cell types</article-title><source>Npj Systems Biology and Applications</source><volume>4</volume><elocation-id>11</elocation-id><pub-id pub-id-type="doi">10.1038/s41540-018-0047-2</pub-id><?supplied-pmid 29507757?><pub-id pub-id-type="pmid">29507757</pub-id></element-citation></ref><ref id="bib24"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gutierrez</surname><given-names>GJ</given-names></name><name><surname>O'Leary</surname><given-names>T</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name></person-group><year>2013</year><article-title>Multiple mechanisms switch an electrically coupled, synaptically inhibited neuron between competing rhythmic oscillators</article-title><source>Neuron</source><volume>77</volume><fpage>845</fpage><lpage>858</lpage><pub-id pub-id-type="doi">10.1016/j.neuron.2013.01.016</pub-id><?supplied-pmid 23473315?><pub-id pub-id-type="pmid">23473315</pub-id></element-citation></ref><ref id="bib25"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hayashi</surname><given-names>H</given-names></name><name><surname>Nakao</surname><given-names>M</given-names></name><name><surname>Hirakawa</surname><given-names>K</given-names></name></person-group><year>1982</year><article-title>Chaos in the self-sustained oscillation of an excitable biological membrane under sinusoidal stimulation</article-title><source>Physics Letters A</source><volume>88</volume><fpage>265</fpage><lpage>266</lpage><pub-id pub-id-type="doi">10.1016/0375-9601(82)90245-6</pub-id></element-citation></ref><ref id="bib26"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hayashi</surname><given-names>H</given-names></name><name><surname>Ishizuka</surname><given-names>S</given-names></name></person-group><year>1992</year><article-title>Chaotic nature of bursting discharges in the onchidium pacemaker neuron</article-title><source>Journal of Theoretical Biology</source><volume>156</volume><fpage>269</fpage><lpage>291</lpage><pub-id pub-id-type="doi">10.1016/S0022-5193(05)80676-9</pub-id></element-citation></ref><ref id="bib27"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Herz</surname><given-names>AV</given-names></name><name><surname>Gollisch</surname><given-names>T</given-names></name><name><surname>Machens</surname><given-names>CK</given-names></name><name><surname>Jaeger</surname><given-names>D</given-names></name></person-group><year>2006</year><article-title>Modeling single-neuron dynamics and computations: a balance of detail and abstraction</article-title><source>Science</source><volume>314</volume><fpage>80</fpage><lpage>85</lpage><pub-id pub-id-type="doi">10.1126/science.1127240</pub-id><?supplied-pmid 17023649?><pub-id pub-id-type="pmid">17023649</pub-id></element-citation></ref><ref id="bib28"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Holland</surname><given-names>JH</given-names></name></person-group><year>1992</year><article-title>Genetic algorithms</article-title><source>Scientific American</source><volume>267</volume><fpage>66</fpage><lpage>72</lpage><pub-id pub-id-type="doi">10.1038/scientificamerican0792-66</pub-id></element-citation></ref><ref id="bib29"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lee</surname><given-names>YS</given-names></name><name><surname>Hwang</surname><given-names>M</given-names></name><name><surname>Song</surname><given-names>JS</given-names></name><name><surname>Li</surname><given-names>C</given-names></name><name><surname>Joung</surname><given-names>B</given-names></name><name><surname>Sobie</surname><given-names>EA</given-names></name><name><surname>Pak</surname><given-names>HN</given-names></name></person-group><year>2016</year><article-title>The contribution of ionic currents to Rate-Dependent action potential duration and pattern of reentry in a mathematical model of human atrial fibrillation</article-title><source>PLOS ONE</source><volume>11</volume><elocation-id>e0150779</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0150779</pub-id><?supplied-pmid 26964092?><pub-id pub-id-type="pmid">26964092</pub-id></element-citation></ref><ref id="bib30"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>Z</given-names></name><name><surname>Golowasch</surname><given-names>J</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name><name><surname>Abbott</surname><given-names>LF</given-names></name></person-group><year>1998</year><article-title>A model neuron with activity-dependent conductances regulated by multiple calcium sensors</article-title><source>The Journal of Neuroscience</source><volume>18</volume><fpage>2309</fpage><lpage>2320</lpage><pub-id pub-id-type="doi">10.1523/JNEUROSCI.18-07-02309.1998</pub-id><?supplied-pmid 9502792?><pub-id pub-id-type="pmid">9502792</pub-id></element-citation></ref><ref id="bib31"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McDougal</surname><given-names>RA</given-names></name><name><surname>Morse</surname><given-names>TM</given-names></name><name><surname>Carnevale</surname><given-names>T</given-names></name><name><surname>Marenco</surname><given-names>L</given-names></name><name><surname>Wang</surname><given-names>R</given-names></name><name><surname>Migliore</surname><given-names>M</given-names></name><name><surname>Miller</surname><given-names>PL</given-names></name><name><surname>Shepherd</surname><given-names>GM</given-names></name><name><surname>Hines</surname><given-names>ML</given-names></name></person-group><year>2017</year><article-title>Twenty years of ModelDB and beyond: building essential modeling tools for the future of neuroscience</article-title><source>Journal of Computational Neuroscience</source><volume>42</volume><fpage>1</fpage><lpage>10</lpage><pub-id pub-id-type="doi">10.1007/s10827-016-0623-7</pub-id><?supplied-pmid 27629590?><pub-id pub-id-type="pmid">27629590</pub-id></element-citation></ref><ref id="bib32"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Migliore</surname><given-names>R</given-names></name><name><surname>Lupascu</surname><given-names>CA</given-names></name><name><surname>Bologna</surname><given-names>LL</given-names></name><name><surname>Romani</surname><given-names>A</given-names></name><name><surname>Courcol</surname><given-names>JD</given-names></name><name><surname>Antonel</surname><given-names>S</given-names></name><name><surname>Van Geit</surname><given-names>WAH</given-names></name><name><surname>Thomson</surname><given-names>AM</given-names></name><name><surname>Mercer</surname><given-names>A</given-names></name><name><surname>Lange</surname><given-names>S</given-names></name><name><surname>Falck</surname><given-names>J</given-names></name><name><surname>R&#x000f6;ssert</surname><given-names>CA</given-names></name><name><surname>Shi</surname><given-names>Y</given-names></name><name><surname>Hagens</surname><given-names>O</given-names></name><name><surname>Pezzoli</surname><given-names>M</given-names></name><name><surname>Freund</surname><given-names>TF</given-names></name><name><surname>Kali</surname><given-names>S</given-names></name><name><surname>Muller</surname><given-names>EB</given-names></name><name><surname>Sch&#x000fc;rmann</surname><given-names>F</given-names></name><name><surname>Markram</surname><given-names>H</given-names></name><name><surname>Migliore</surname><given-names>M</given-names></name></person-group><year>2018</year><article-title>The physiological variability of channel density in hippocampal CA1 pyramidal cells and interneurons explored using a unified data-driven modeling workflow</article-title><source>PLOS Computational Biology</source><volume>14</volume><elocation-id>e1006423</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pcbi.1006423</pub-id><?supplied-pmid 30222740?><pub-id pub-id-type="pmid">30222740</pub-id></element-citation></ref><ref id="bib33"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>O'Leary</surname><given-names>T</given-names></name><name><surname>Williams</surname><given-names>AH</given-names></name><name><surname>Franci</surname><given-names>A</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name></person-group><year>2014</year><article-title>Cell types, network homeostasis, and pathological compensation from a biologically plausible ion channel expression model</article-title><source>Neuron</source><volume>82</volume><fpage>809</fpage><lpage>821</lpage><pub-id pub-id-type="doi">10.1016/j.neuron.2014.04.002</pub-id><?supplied-pmid 24853940?><pub-id pub-id-type="pmid">24853940</pub-id></element-citation></ref><ref id="bib34"><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Press</surname><given-names>WH</given-names></name><name><surname>Teukolsky</surname><given-names>SA</given-names></name><name><surname>Vetterling</surname><given-names>WT</given-names></name><name><surname>Flannery</surname><given-names>BP</given-names></name></person-group><year>1988</year><source>Numerical Recipes in C</source><publisher-name>Cambridge University Press</publisher-name></element-citation></ref><ref id="bib35"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Prinz</surname><given-names>AA</given-names></name><name><surname>Bucher</surname><given-names>D</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name></person-group><year>2004</year><article-title>Similar network activity from disparate circuit parameters</article-title><source>Nature Neuroscience</source><volume>7</volume><fpage>1345</fpage><lpage>1352</lpage><pub-id pub-id-type="doi">10.1038/nn1352</pub-id><?supplied-pmid 15558066?><pub-id pub-id-type="pmid">15558066</pub-id></element-citation></ref><ref id="bib36"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Roemschied</surname><given-names>FA</given-names></name><name><surname>Eberhard</surname><given-names>MJ</given-names></name><name><surname>Schleimer</surname><given-names>JH</given-names></name><name><surname>Ronacher</surname><given-names>B</given-names></name><name><surname>Schreiber</surname><given-names>S</given-names></name></person-group><year>2014</year><article-title>Cell-intrinsic mechanisms of temperature compensation in a grasshopper sensory receptor neuron</article-title><source>eLife</source><volume>3</volume><elocation-id>e02078</elocation-id><pub-id pub-id-type="doi">10.7554/eLife.02078</pub-id><?supplied-pmid 24843016?><pub-id pub-id-type="pmid">24843016</pub-id></element-citation></ref><ref id="bib37"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Roffman</surname><given-names>RC</given-names></name><name><surname>Norris</surname><given-names>BJ</given-names></name><name><surname>Calabrese</surname><given-names>RL</given-names></name></person-group><year>2012</year><article-title>Animal-to-animal variability of connection strength in the leech heartbeat central pattern generator</article-title><source>Journal of Neurophysiology</source><volume>107</volume><fpage>1681</fpage><lpage>1693</lpage><pub-id pub-id-type="doi">10.1152/jn.00903.2011</pub-id><?supplied-pmid 22190622?><pub-id pub-id-type="pmid">22190622</pub-id></element-citation></ref><ref id="bib38"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schulz</surname><given-names>DJ</given-names></name><name><surname>Goaillard</surname><given-names>JM</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name></person-group><year>2006</year><article-title>Variable channel expression in identified single and electrically coupled neurons in different animals</article-title><source>Nature Neuroscience</source><volume>9</volume><fpage>356</fpage><lpage>362</lpage><pub-id pub-id-type="doi">10.1038/nn1639</pub-id><?supplied-pmid 16444270?><pub-id pub-id-type="pmid">16444270</pub-id></element-citation></ref><ref id="bib39"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schulz</surname><given-names>DJ</given-names></name><name><surname>Goaillard</surname><given-names>JM</given-names></name><name><surname>Marder</surname><given-names>EE</given-names></name></person-group><year>2007</year><article-title>Quantitative expression profiling of identified neurons reveals cell-specific constraints on highly variable levels of gene expression</article-title><source>PNAS</source><volume>104</volume><fpage>13187</fpage><lpage>13191</lpage><pub-id pub-id-type="doi">10.1073/pnas.0705827104</pub-id><?supplied-pmid 17652510?><pub-id pub-id-type="pmid">17652510</pub-id></element-citation></ref><ref id="bib40"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shilnikov</surname><given-names>A</given-names></name><name><surname>Calabrese</surname><given-names>RL</given-names></name><name><surname>Cymbalyuk</surname><given-names>G</given-names></name></person-group><year>2005</year><article-title>Mechanism of bistability: tonic spiking and bursting in a neuron model</article-title><source>Physical Review E</source><volume>71</volume><elocation-id>056214</elocation-id><pub-id pub-id-type="doi">10.1103/PhysRevE.71.056214</pub-id></element-citation></ref><ref id="bib41"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sobel</surname><given-names>I</given-names></name><name><surname>Feldman</surname><given-names>G</given-names></name></person-group><year>1968</year><article-title>A 3x3 isotropic gradient operator for image processing</article-title><source>A Talk at the Stanford Artificial Project, Pages</source></element-citation></ref><ref id="bib42"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Swensen</surname><given-names>AM</given-names></name><name><surname>Bean</surname><given-names>BP</given-names></name></person-group><year>2005</year><article-title>Robustness of burst firing in dissociated purkinje neurons with acute or long-term reductions in sodium conductance</article-title><source>Journal of Neuroscience</source><volume>25</volume><fpage>3509</fpage><lpage>3520</lpage><pub-id pub-id-type="doi">10.1523/JNEUROSCI.3929-04.2005</pub-id><?supplied-pmid 15814781?><pub-id pub-id-type="pmid">15814781</pub-id></element-citation></ref><ref id="bib43"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sz&#x000fc;cs</surname><given-names>A</given-names></name><name><surname>Elson</surname><given-names>RC</given-names></name><name><surname>Rabinovich</surname><given-names>MI</given-names></name><name><surname>Abarbanel</surname><given-names>HD</given-names></name><name><surname>Selverston</surname><given-names>AI</given-names></name></person-group><year>2001</year><article-title>Nonlinear behavior of sinusoidally forced pyloric pacemaker neurons</article-title><source>Journal of Neurophysiology</source><volume>85</volume><fpage>1623</fpage><lpage>1638</lpage><pub-id pub-id-type="doi">10.1152/jn.2001.85.4.1623</pub-id><?supplied-pmid 11287486?><pub-id pub-id-type="pmid">11287486</pub-id></element-citation></ref><ref id="bib44"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Temporal</surname><given-names>S</given-names></name><name><surname>Desai</surname><given-names>M</given-names></name><name><surname>Khorkova</surname><given-names>O</given-names></name><name><surname>Varghese</surname><given-names>G</given-names></name><name><surname>Dai</surname><given-names>A</given-names></name><name><surname>Schulz</surname><given-names>DJ</given-names></name><name><surname>Golowasch</surname><given-names>J</given-names></name></person-group><year>2012</year><article-title>Neuromodulation independently determines correlated channel expression and conductance levels in motor neurons of the stomatogastric ganglion</article-title><source>Journal of Neurophysiology</source><volume>107</volume><fpage>718</fpage><lpage>727</lpage><pub-id pub-id-type="doi">10.1152/jn.00622.2011</pub-id><?supplied-pmid 21994267?><pub-id pub-id-type="pmid">21994267</pub-id></element-citation></ref><ref id="bib45"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Temporal</surname><given-names>S</given-names></name><name><surname>Lett</surname><given-names>KM</given-names></name><name><surname>Schulz</surname><given-names>DJ</given-names></name></person-group><year>2014</year><article-title>Activity-dependent feedback regulates correlated ion channel mRNA levels in single identified motor neurons</article-title><source>Current Biology</source><volume>24</volume><fpage>1899</fpage><lpage>1904</lpage><pub-id pub-id-type="doi">10.1016/j.cub.2014.06.067</pub-id><?supplied-pmid 25088555?><pub-id pub-id-type="pmid">25088555</pub-id></element-citation></ref><ref id="bib46"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tobin</surname><given-names>AE</given-names></name><name><surname>Cruz-Berm&#x000fa;dez</surname><given-names>ND</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name><name><surname>Schulz</surname><given-names>DJ</given-names></name></person-group><year>2009</year><article-title>Correlations in ion channel mRNA in rhythmically active neurons</article-title><source>PLOS ONE</source><volume>4</volume><elocation-id>e6742</elocation-id><pub-id pub-id-type="doi">10.1371/journal.pone.0006742</pub-id><?supplied-pmid 19707591?><pub-id pub-id-type="pmid">19707591</pub-id></element-citation></ref><ref id="bib47"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Touboul</surname><given-names>J</given-names></name><name><surname>Brette</surname><given-names>R</given-names></name></person-group><year>2008</year><article-title>Dynamics and bifurcations of the adaptive exponential integrate-and-fire model</article-title><source>Biological Cybernetics</source><volume>99</volume><fpage>319</fpage><lpage>334</lpage><pub-id pub-id-type="doi">10.1007/s00422-008-0267-4</pub-id><?supplied-pmid 19011921?><pub-id pub-id-type="pmid">19011921</pub-id></element-citation></ref><ref id="bib48"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Turrigiano</surname><given-names>G</given-names></name><name><surname>LeMasson</surname><given-names>G</given-names></name><name><surname>Marder</surname><given-names>E</given-names></name></person-group><year>1995</year><article-title>Selective regulation of current densities underlies spontaneous changes in the activity of cultured neurons</article-title><source>The Journal of Neuroscience</source><volume>15</volume><fpage>3640</fpage><lpage>3652</lpage><pub-id pub-id-type="doi">10.1523/JNEUROSCI.15-05-03640.1995</pub-id><?supplied-pmid 7538565?><pub-id pub-id-type="pmid">7538565</pub-id></element-citation></ref><ref id="bib49"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>D</given-names></name><name><surname>Grillner</surname><given-names>S</given-names></name><name><surname>Wall&#x000e9;n</surname><given-names>P</given-names></name></person-group><year>2014</year><article-title>Endogenous release of 5-HT modulates the plateau phase of NMDA-induced membrane potential oscillations in lamprey spinal neurons</article-title><source>Journal of Neurophysiology</source><volume>112</volume><fpage>30</fpage><lpage>38</lpage><pub-id pub-id-type="doi">10.1152/jn.00582.2013</pub-id><?supplied-pmid 24740857?><pub-id pub-id-type="pmid">24740857</pub-id></element-citation></ref><ref id="bib50"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>K</given-names></name><name><surname>Maidana</surname><given-names>JP</given-names></name><name><surname>Caviedes</surname><given-names>M</given-names></name><name><surname>Quero</surname><given-names>D</given-names></name><name><surname>Aguirre</surname><given-names>P</given-names></name><name><surname>Orio</surname><given-names>P</given-names></name></person-group><year>2017</year><article-title>Hyperpolarization-Activated current induces Period-Doubling cascades and chaos in a cold thermoreceptor model</article-title><source>Frontiers in Computational Neuroscience</source><volume>11</volume><elocation-id>12</elocation-id><pub-id pub-id-type="doi">10.3389/fncom.2017.00012</pub-id><?supplied-pmid 28344550?><pub-id pub-id-type="pmid">28344550</pub-id></element-citation></ref></ref-list></back><sub-article id="SA1" article-type="decision-letter"><front-stub><article-id pub-id-type="doi">10.7554/eLife.42722.025</article-id><title-group><article-title>Decision letter</article-title></title-group><contrib-group><contrib contrib-type="editor"><name><surname>Skinner</surname><given-names>Frances K</given-names></name><role>Reviewing Editor</role><aff><institution>Krembil Research Institute, University Health Network</institution><country>Canada</country></aff></contrib><contrib contrib-type="reviewer"><name><surname>Lankarany</surname><given-names>Milad</given-names></name><role>Reviewer</role><aff><institution>The Hospital for Sick Children, University of Toronto</institution><country>Canada</country></aff></contrib><contrib contrib-type="reviewer"><name><surname>Britton</surname><given-names>Oliver</given-names></name><role>Reviewer</role></contrib></contrib-group></front-stub><body><boxed-text position="float" orientation="portrait"><p>In the interests of transparency, eLife includes the editorial decision letter and accompanying author responses. A lightly edited version of the letter sent to the authors after peer review is shown, indicating the most substantive concerns; minor comments are not usually included.</p></boxed-text><p>Thank you for submitting your article "Visualization of currents in neural models with similar behavior and different conductance densities" for consideration by <italic>eLife</italic>. Your article has been reviewed by Gary Westbrook as the Senior Editor, a Reviewing Editor, and three reviewers. The following individuals involved in review of your submission have agreed to reveal their identity: Alexandre Guet-McCreight (Skinner lab) (Reviewer#1); Milad Lankarany (Reviewer #2); Oliver Britton (Reviewer #3). The reviewers have discussed the reviews with one another and the Reviewing Editor has drafted this decision to help you prepare a revised submission.</p><p>Summary:</p><p>All of the reviewers were fully appreciative of the 'tools and resources' approach and thought that the visualization tool would be helpful and useful to the wider community. At the same time, there were areas for improvement. Specifically, (i) expansion of the code to make it usable by others, (ii) more elaboration on the method itself (optimization, objective function) and perhaps less on the details of the 'results' which are clear enough from the figures and given that this is a tools and resources paper, and (iii) several small fixes throughout. We regard these points as essential in the revision, but the full comments are provided below for your guidance as you revise the manuscript.</p><p>Essential revisions:</p><p><italic>Reviewer #1:</italic>
</p><p>In this manuscript, the authors present several novel methodologies, including (A) an optimization technique that involves the use of thresholds (i.e. Poincar&#x000e9; sections) as objectives, (B) a visualization technique for better plotting ion channel currents in neuron models, (C) a visualization technique for better demonstrating changes in electrophysiology at different levels of perturbations, as well as (D) combining these two visualization techniques. Specifically, the visualization techniques shown in this manuscript demonstrate very novel, intuitive, and yet surprisingly simple ways of viewing and interpreting complex high-dimensional data. Specifically, the currentscape plots are a much-needed addition to the field and facilitate what both computational and experimental researchers can interpret from models. As well, the plots highlighting changes in voltage and ISI distributions over different perturbation levels provide another surprisingly simple but rich way of viewing large amounts of data. Overall, this manuscript merits publication but may benefit from giving more background on its "landscape" optimization method, and several sections in the results can possibly be boiled down to more succinct take-away messages.</p><p>&#x02013; While the Supplementary files on Dryad are an essential component of this publication, it is not straightforward for every reader to run this code to extract the parameters used in these simulations. As such, it might be helpful for readers who are not immediately intending to try out this code, to include these values in a supplementary table. Related to this, a small glossary table of the variable names might also be helpful.</p><p>&#x02013; For a reader that is somewhat na&#x000ef;ve on the topic of optimizations, it remains unclear to me how landscape optimizations differ from other types of optimizations (i.e. why is it called a "landscape" optimization?). It might be helpful to describe a bit more how landscape optimizations work relative to other types of optimizations &#x02013; it sounds as though the "landscape" part simply refers to the type of target/objective function that is being used? On a related note, very few details are given regarding the "custom genetic algorithm" (subsection &#x0201c;Finding parameters: landscape optimization&#x0201d;). Would it be possible to give more details on this, as well as parameters that were used in the optimizations (e.g. mutation, crossover, population size, etc.)?</p><p>&#x02013; Often throughout the paper, results from the plots are described in more detail than is necessary and this tends to derail the focus of the story. Here I am mainly referring to the more-or-less informal descriptions of what the plots look like across different models, channel mechanisms, currents, manipulations, etc. (e.g. subsection &#x0201c;Perturbing the models with gradual decrements of the maximal conductances&#x0201d; or subsection &#x0201c;Changes in waveform as conductances are gradually decreased&#x0201d;). While these observations are very interesting in themselves, the authors often do not give enough explanation (i.e. of why those observations occur) to warrant mentioning them in the first place. In fact, many of those observations are indicative of the rich amount of information obtainable from these plotting methods, but perhaps beyond the scope this paper. Ultimately, since the authors are presenting a novel visualization technique, the plots should really be able to speak more for themselves. In my opinion, the plots do indeed speak for themselves, better in fact than the results descriptions given by the authors.</p><p>&#x02013; It would be nice to apply the plotting technique in Figure 3 and Figure 5 to a set of experimental data for comparison purposes. Based on the descriptions of how these plots are made, I would imagine that this could be possible, though maybe at a lower resolution(?). If so, it might be worth mentioning, especially for experimental readers. Given that there is diversity in how the electrophysiology of the different models change with removal of channel currents and current injection amplitudes, further steps following model optimization might be to see which models can capture the changes (or diversity in changes) seen in electrophysiology. This might further help narrow the list of possible optimization solutions that can viably capture experimental electrophysiologies.</p><p><italic>Reviewer #2:</italic>
</p><p>This paper presents a comprehensive study on how a model neuron with different maximal conductances show similar membrane activity. I think this paper is well designed and presented. The problem of having identical observation (experimental data) given different parameter settings of a representative model is important in neuroscience. This paper introduces a visualization method to track the dynamics of ionic currents underlying each set of parameters. I have two major points to enhance the quality of the paper. Shortly, my major points are about the consistency of the results of the paper with respect to (i) objective function (total Error) and (ii) type of stimulus.</p><p>Elaborate on the effect of objective function on your results (overall) &#x02013; specifically, if more detailed objective functions (or the same as yours but with different weights) might better distinguish between bursting models (which might not be inspected visually). And, I think the value of objective function (total error, Equation 4) should be reported. For the 6 selected models of bursts, you should report those values.</p><p>My second point is more like a question. What happened to the response of, for example, two models (a and b) given an identical but noisy injected current? Are they still the same?</p><p><italic>Reviewer #3:</italic>
</p><p>This is an interesting manuscript that describes several novel visualization techniques for interpreting the large amount of data that are routinely generated by computational models of neurons and other electrically excitable cells. The authors focus on methods to visualize how the balance of all ionic currents in a model, as well as the voltage, change when the conductance of one current is varied. This is a key part of understanding these highly non-linear models, as changes in one current often have unexpected knock on effects on the behavior of many other currents. These techniques are illustrated using six models with similar control behavior but different conductance values.</p><p>I particularly like that the techniques the authors describe allow both qualitative (e.g. firing pattern) and quantitative (e.g. peak AP voltage) changes in model output under parameter variation to be visualized within a single plot.</p><p>The authors illustrate the use of these techniques by analyzing an eight current single-compartment neuron model. I appreciate the use of a reasonably complex model as an example, rather than a toy model with few currents, as it better illustrates to readers how they might use the techniques in their own research.</p><p>My major criticism of the manuscript is that while I commend the authors for providing well organized source code that is sufficient to reproduce all figures, the study has been submitted as a tools and resources paper, and the code as is does not form a tool for other researchers to use, as it is a series of non-reusable scripts.</p><p>To allow other researchers to easily use these techniques, I would like to see the visualization code repackaged as a Python module encapsulating the main plotting functions (i.e. currentscapes; voltage probability distribution and ridge plots; ISI distribution plots; and conductance against current plots) with a documented interface. Building such a module in Python is relatively simple. I would encourage the authors to look at the Python visualization module Seaborn (https://seaborn.pydata.org/) if they are not familiar with it, to see a good example of an open source scientific visualization library.</p><p>With such a module, the code for a new user to produce, for example, a currentscape plot could be reduced to a few lines of code, e.g.:</p><p>&#x0003e; import current_visualization_module</p><p>&#x0003e; # Load pre-existing arrays of data from a simulation including currents,</p><p>&#x0003e; voltage, time arrays = load(path_to_data)</p><p>&#x0003e; current_names = ['INa', 'ICaT', 'IKA',&#x02026;etc]</p><p>&#x0003e; # Perform the plot</p><p>&#x0003e; current_visualization.plot_currentscape(arrays, current_names)</p><p>Currently, most of the figure plotting code is provided as a set of scripts, but as these scripts are organized into functionally distinct sections (e.g. running the model, currentscape calculations, currentscape plotting), they could easily be converted into reusable functions. The figure plotting scripts could then be rewritten to use these functions, which would provide a gallery of examples for how to use the module. I don't think this revision would need too much extra work as most of the required code is already written and just needs to be encapsulated within a set of functions.</p><p>Releasing such a module this would substantially add to the utility and uptake of this work and provide a valuable tool for multiple electrophysiological modelling communities. From a purely selfish viewpoint, I would use it in my own work!</p></body></sub-article><sub-article id="SA2" article-type="reply"><front-stub><article-id pub-id-type="doi">10.7554/eLife.42722.026</article-id><title-group><article-title>Author response</article-title></title-group></front-stub><body><disp-quote content-type="editor-comment"><p>Essential revisions:</p><p>Reviewer #1:</p><p>In this manuscript, the authors present several novel methodologies, including (A) an optimization technique that involves the use of thresholds (i.e. Poincar&#x000e9; sections) as objectives, (B) a visualization technique for better plotting ion channel currents in neuron models, (C) a visualization technique for better demonstrating changes in electrophysiology at different levels of perturbations, as well as (D) combining these two visualization techniques. Specifically, the visualization techniques shown in this manuscript demonstrate very novel, intuitive, and yet surprisingly simple ways of viewing and interpreting complex high-dimensional data. Specifically, the currentscape plots are a much-needed addition to the field and facilitate what both computational and experimental researchers can interpret from models. As well, the plots highlighting changes in voltage and ISI distributions over different perturbation levels provide another surprisingly simple but rich way of viewing large amounts of data. Overall, this manuscript merits publication but may benefit from giving more background on its "landscape" optimization method, and several sections in the results can possibly be boiled down to more succinct take-away messages.</p><p>&#x02013; While the Supplementary files on Dryad are an essential component of this publication, it is not straightforward for every reader to run this code to extract the parameters used in these simulations. As such, it might be helpful for readers who are not immediately intending to try out this code, to include these values in a supplementary table. Related to this, a small glossary table of the variable names might also be helpful.</p></disp-quote><p>We fixed the supplementary data package on Dryad to make it more accessible to the general public. We also included a list of all utilized parameters in the Materials and methods section and a glossary with variable names.</p><disp-quote content-type="editor-comment"><p>&#x02013; For a reader that is somewhat na&#x000ef;ve on the topic of optimizations, it remains unclear to me how landscape optimizations differ from other types of optimizations (i.e. why is it called a "landscape" optimization?). It might be helpful to describe a bit more how landscape optimizations work relative to other types of optimizations &#x02013; it sounds as though the "landscape" part simply refers to the type of target/objective function that is being used? On a related note, very few details are given regarding the "custom genetic algorithm" (subsection &#x0201c;Finding parameters: landscape optimization&#x0201d;). Would it be possible to give more details on this, as well as parameters that were used in the optimizations (e.g. mutation, crossover, population size, etc.)?</p></disp-quote><p>Landscape is a common way to refer to the cost function (also evolutionary landscape), so yes, the &#x0201c;landscape&#x0201d; is the function. The reason people call these functions &#x0201c;landscapes" is that in the case of 2 dimensions the plot of this function resembles a landscape of mountains, valleys and ridges. Optimization means finding minima of this landscape. This terminology seems fairly common (see Achard et al., 2006) and this is the reason we choose to use it.</p><p>By &#x0201c;custom genetic algorithm&#x0201d; we mean that we are using an algorithm we wrote and not an optimization package that can be referenced. We changed the word &#x0201c;custom" to "standard". One reason the algorithm is not described in detail is that these algorithms are very common and are sufficiently well described. But the main reason we do not discuss this much is that ultimately what matters is the function that is being optimized, and not the optimization technique itself.</p><p>We included fixes throughout the main text to better clarify these issues and discuss technical details in subsection &#x0201c;Optimization of target function&#x0201d;.</p><disp-quote content-type="editor-comment"><p>&#x02013; Often throughout the paper, results from the plots are described in more detail than is necessary and this tends to derail the focus of the story. Here I am mainly referring to the more-or-less informal descriptions of what the plots look like across different models, channel mechanisms, currents, manipulations, etc. (e.g. subsection &#x0201c;Perturbing the models with gradual decrements of the maximal conductances&#x0201d; or subsection &#x0201c;Changes in waveform as conductances are gradually decreased&#x0201d;). While these observations are very interesting in themselves, the authors often do not give enough explanation (i.e. of why those observations occur) to warrant mentioning them in the first place. In fact, many of those observations are indicative of the rich amount of information obtainable from these plotting methods, but perhaps beyond the scope this paper. Ultimately, since the authors are presenting a novel visualization technique, the plots should really be able to speak more for themselves. In my opinion, the plots do indeed speak for themselves, better in fact than the results descriptions given by the authors.</p></disp-quote><p>We acknowledge the reviewer&#x02019;s kind appreciation of our plotting techniques. Reviewer #1 points out that the two techniques in Figure 2 and Figure 3 are combined (D). We feel that this is an important point that may not be immediately clear, so we included a supplemental figure for Figure 14 (Figure 14&#x02014;figure supplement 1) that shows the relationship between the currentscapes and the distributions in Figure 14. We largely modified the text in the Results section to make it more streamlined.</p><disp-quote content-type="editor-comment"><p>&#x02013; It would be nice to apply the plotting technique in Figure 3 and Figure 5 to a set of experimental data for comparison purposes. Based on the descriptions of how these plots are made, I would imagine that this could be possible, though maybe at a lower resolution(?). If so, it might be worth mentioning, especially for experimental readers. Given that there is diversity in how the electrophysiology of the different models change with removal of channel currents and current injection amplitudes, further steps following model optimization might be to see which models can capture the changes (or diversity in changes) seen in electrophysiology. This might further help narrow the list of possible optimization solutions that can viably capture experimental electrophysiologies.</p></disp-quote><p>We agree with the reviewer in that similar plotting techniques could be applied to experimental data. However, we prefer to leave that to the reader since we did not test it and would rather not include experimental data in this work.</p><p>The reviewer points out that since the models responds differently to similar perturbations one could further narrow down the list of possible solutions by targeting models that respond to a perturbation in a prescribed way. We absolutely agree with the reviewer. There are many experimental perturbations &#x02013; such as changes in temperature or pH &#x02013; that result in reproducible changes in the activity. We can build similar target functions whose minima correspond to models that resemble the control data, and at the same time respond in a prescribed way to a given perturbation. We are now doing exactly this in neurons and small networks.</p><disp-quote content-type="editor-comment"><p>Reviewer #2:</p><p>This paper presents a comprehensive study on how a model neuron with different maximal conductances show similar membrane activity. I think this paper is well designed and presented. The problem of having identical observation (experimental data) given different parameter settings of a representative model is important in neuroscience. This paper introduces a visualization method to track the dynamics of ionic currents underlying each set of parameters. I have two major points to enhance the quality of the paper. Shortly, my major points are about the consistency of the results of the paper with respect to (i) objective function (total Error) and (ii) type of stimulus.</p><p>Elaborate on the effect of objective function on your results (overall) &#x02013; specifically, if more detailed objective functions (or the same as yours but with different weights) might better distinguish between bursting models (which might not be inspected visually). And, I think the value of objective function (total error, Eq 4) should be reported. For the 6 selected models of bursts, you shall report those values.</p></disp-quote><p>We expanded the discussion of the cost function. More detailed cost functions (i.e. with additional targets) will result in models that satisfy more requirements. One could of course add additional restrictions like, for example, ask that the number of spikes in a burst is within a range. In this manuscript we show that, using thresholds as objectives, it is easy to design cost functions that target features of physiological relevance such as frequencies and durations of bursts. The values of the cost function for the six models studied (and the models used in Figure 2 and Figure 3) were included in a table in the Materials and methods section.</p><disp-quote content-type="editor-comment"><p>My second point is more like a question. What happened to the response of, for example, two models (a and b) given an identical but noisy injected current? Are they still the same?</p></disp-quote><p>We did not explore the effect of injecting noise in these models. These models are extremely complex from a mathematical standpoint and their response to stochastic stimuli is well beyond the scope of this work. However, on a first principle basis we believe the response to the referee&#x02019;s question is no. If the same noisy input is injected on models (a) and (b) the responses will most likely be different. This is because the models respond differently to identical perturbations, and there is no reason to assume it won&#x02019;t be the case if the perturbation is stochastic.</p><disp-quote content-type="editor-comment"><p>Reviewer #3:</p><p>This is an interesting manuscript that describes several novel visualization techniques for interpreting the large amount of data that are routinely generated by computational models of neurons and other electrically excitable cells. The authors focus on methods to visualize how the balance of all ionic currents in a model, as well as the voltage, change when the conductance of one current is varied. This is a key part of understanding these highly non-linear models, as changes in one current often have unexpected knock on effects on the behavior of many other currents. These techniques are illustrated using six models with similar control behavior but different conductance values.</p><p>I particularly like that the techniques the authors describe allow both qualitative (e.g. firing pattern) and quantitative (e.g. peak AP voltage) changes in model output under parameter variation to be visualized within a single plot.</p><p>The authors illustrate the use of these techniques by analyzing an eight current single-compartment neuron model. I appreciate the use of a reasonably complex model as an example, rather than a toy model with few currents, as it better illustrates to readers how they might use the techniques in their own research.</p><p>My major criticism of the manuscript is that while I commend the authors for providing well organized source code that is sufficient to reproduce all figures, the study has been submitted as a tools and resources paper, and the code as is does not form a tool for other researchers to use as it is a series of non-reusable scripts.</p><p>To allow other researchers to easily use these techniques, I would like to see the visualization code repackaged as a Python module encapsulating the main plotting functions (i.e. currentscapes; voltage probability distribution and ridge plots; ISI distribution plots; and conductance against current plots) with a documented interface. Building such a module in Python is relatively simple. I would encourage the authors to look at the Python visualization module Seaborn (https://seaborn.pydata.org/) if they are not familiar with it, to see a good example of an open source scientific visualization library.</p><p>With such a module, the code for a new user to produce, for example, a currentscape plot could be reduced to a few lines of code, e.g.:</p><p>&#x0003e; import current_visualization_module</p><p>&#x0003e; # Load pre-existing arrays of data from a simulation including currents, voltage, &#x0003e; time arrays = load(path_to_data)</p><p>&#x0003e; current_names = ['INa', 'ICaT', 'IKA',&#x02026;etc]</p><p>&#x0003e; # Perform the plot</p><p>&#x0003e; current_visualization.plot_currentscape(arrays, current_names)</p><p>Currently, most of the figure plotting code is provided as a set of scripts, but as these scripts are organized into functionally distinct sections (e.g. running the model, currentscape calculations, currentscape plotting), they could easily be converted into reusable functions. The figure plotting scripts could then be rewritten to use these functions, which would provide a gallery of examples for how to use the module. I don't think this revision would need too much extra work as most of the required code is already written and just needs to be encapsulated within a set of functions.</p><p>Releasing such a module this would substantially add to the utility and uptake of this work and provide a valuable tool for multiple electrophysiological modelling communities. From a purely selfish viewpoint, I would use it in my own work!</p></disp-quote><p>We followed the suggestions of the reviewer and we now provide a python module encapsulating the main plotting functions. Using this module, Figure 2 can be reproduced with a few lines,</p><p>&#x0003e; import currents_visualization</p><p>&#x0003e; pathtovoltagetrace='./numerical-data-txt/model.Figure 2.voltage.txt'</p><p>&#x0003e; pathtocurrents='./numerical-data-txt/model.Figure 2.currents.txt'</p><p>&#x0003e; voltagetrace = genfromtxt(pathtovoltagetrace)</p><p>&#x0003e; currents=genfromtxt(pathtocurrents)</p><p>&#x0003e; currents_visualization.plotCurrentscape(voltagetrace, currents)</p><p>We agree with the reviewer that this module may be more reusable than the code we used to generate the figures in the manuscript. However, we also believe that different models may require different color schemes, some modelers prefer a different ordering for the currents, and so forth. We do not intend to provide a closed-form solution such as seaboard, that is intended for much more general purposes than the types of plots discussed here.</p><p>We anticipate that these plots will have to be adapted to the details of the problem being studied, such as model definition, perturbation type, etc. We intend to provide code that is easy to read, understand, and modify, rather than it being only efficient. For this reason, the code/data package in this resubmission includes both the exact scripts that reproduce the figures and also the encapsulated python module suggested by the reviewer together with usage examples.</p></body></sub-article></article>