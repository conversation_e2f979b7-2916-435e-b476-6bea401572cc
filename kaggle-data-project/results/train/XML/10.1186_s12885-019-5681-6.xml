<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.2 20190208//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Cancer</journal-id><journal-id journal-id-type="iso-abbrev">BMC Cancer</journal-id><journal-title-group><journal-title>BMC Cancer</journal-title></journal-title-group><issn pub-type="epub">1471-2407</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6580486</article-id><article-id pub-id-type="publisher-id">5681</article-id><article-id pub-id-type="doi">10.1186/s12885-019-5681-6</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Probabilistic modeling of personalized drug combinations from integrated chemical screen and molecular data in sarcoma</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0001-8666-3152</contrib-id><name><surname>Berlow</surname><given-names>Noah E.</given-names></name><address><phone>(*************</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Rikhi</surname><given-names>Rishi</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Geltzeiler</surname><given-names>Mathew</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff18">18</xref></contrib><contrib contrib-type="author"><name><surname>Abraham</surname><given-names>Jinu</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Svalina</surname><given-names>Matthew N.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Davis</surname><given-names>Lara E.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Wise</surname><given-names>Erin</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Mancini</surname><given-names>Maria</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Noujaim</surname><given-names>Jonathan</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6">6</xref><xref ref-type="aff" rid="Aff19">19</xref></contrib><contrib contrib-type="author"><name><surname>Mansoor</surname><given-names>Atiya</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff7">7</xref></contrib><contrib contrib-type="author"><name><surname>Quist</surname><given-names>Michael J.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff8">8</xref></contrib><contrib contrib-type="author"><name><surname>Matlock</surname><given-names>Kevin L.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff20">20</xref></contrib><contrib contrib-type="author"><name><surname>Goros</surname><given-names>Martin W.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff9">9</xref></contrib><contrib contrib-type="author"><name><surname>Hernandez</surname><given-names>Brian S.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff9">9</xref></contrib><contrib contrib-type="author"><name><surname>Doung</surname><given-names>Yee C.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff10">10</xref></contrib><contrib contrib-type="author"><name><surname>Thway</surname><given-names>Khin</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Tsukahara</surname><given-names>Tomohide</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff11">11</xref></contrib><contrib contrib-type="author"><name><surname>Nishio</surname><given-names>Jun</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff12">12</xref></contrib><contrib contrib-type="author"><name><surname>Huang</surname><given-names>Elaine T.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Airhart</surname><given-names>Susan</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff13">13</xref></contrib><contrib contrib-type="author"><name><surname>Bult</surname><given-names>Carol J.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff13">13</xref></contrib><contrib contrib-type="author"><name><surname>Gandour-Edwards</surname><given-names>Regina</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff14">14</xref></contrib><contrib contrib-type="author"><name><surname>Maki</surname><given-names>Robert G.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff15">15</xref></contrib><contrib contrib-type="author"><name><surname>Jones</surname><given-names>Robin L.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Michalek</surname><given-names>Joel E.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff9">9</xref></contrib><contrib contrib-type="author"><name><surname>Milovancev</surname><given-names>Milan</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff16">16</xref></contrib><contrib contrib-type="author"><name><surname>Ghosh</surname><given-names>Souparno</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff17">17</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Pal</surname><given-names>Ranadip</given-names></name><address><phone>(*************</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Keller</surname><given-names>Charles</given-names></name><address><phone>(*************</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.468147.8</institution-id><institution>Children&#x02019;s Cancer Therapy Development Institute, </institution></institution-wrap>12655 SW Beaverdam Road-West, Beaverton, OR 97005 USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2186 7496</institution-id><institution-id institution-id-type="GRID">grid.264784.b</institution-id><institution>Electrical and Computer Engineering, </institution><institution>Texas Tech University, </institution></institution-wrap>Lubbock, TX 79409 USA </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 9758 5690</institution-id><institution-id institution-id-type="GRID">grid.5288.7</institution-id><institution>Department of Pediatrics, </institution><institution>Oregon Health &#x00026; Science University, </institution></institution-wrap>Portland, OR 97239 USA </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 9758 5690</institution-id><institution-id institution-id-type="GRID">grid.5288.7</institution-id><institution>Knight Cancer Institute, </institution><institution>Oregon Health &#x00026; Science University, </institution></institution-wrap>Portland, OR 97239 USA </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="GRID">grid.504326.6</institution-id><institution>Champions Oncology, </institution></institution-wrap>Baltimore, MD 21205 USA </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 1271 4623</institution-id><institution-id institution-id-type="GRID">grid.18886.3f</institution-id><institution>Royal Marsden Hospital and Institute of Cancer Research, </institution></institution-wrap>London, SW3 6JJ UK </aff><aff id="Aff7"><label>7</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 9758 5690</institution-id><institution-id institution-id-type="GRID">grid.5288.7</institution-id><institution>Department of Pathology, </institution><institution>Oregon Health &#x00026; Science University, </institution></institution-wrap>Portland, OR 97239 USA </aff><aff id="Aff8"><label>8</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 9758 5690</institution-id><institution-id institution-id-type="GRID">grid.5288.7</institution-id><institution>Center for Spatial Systems Biomedicine Department of Molecular and Medical Genetics, </institution><institution>Oregon Health &#x00026; Science University, </institution></institution-wrap>Portland, OR 97239 USA </aff><aff id="Aff9"><label>9</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0629 5880</institution-id><institution-id institution-id-type="GRID">grid.267309.9</institution-id><institution>Department of Epidemiology and Biostatistics, </institution><institution>University of Texas Health Science Center San Antonio, </institution></institution-wrap>San Antonio, TX 78229 USA </aff><aff id="Aff10"><label>10</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 9758 5690</institution-id><institution-id institution-id-type="GRID">grid.5288.7</institution-id><institution>Department of Orthopedic Surgery, </institution><institution>Oregon Health &#x00026; Science University, </institution></institution-wrap>Portland, OR 97239 USA </aff><aff id="Aff11"><label>11</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0691 0855</institution-id><institution-id institution-id-type="GRID">grid.263171.0</institution-id><institution>Department of Pathology, </institution><institution>Sapporo Medical University School of Medicine, </institution></institution-wrap>Sapporo, 060-8556 Japan </aff><aff id="Aff12"><label>12</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0672 2176</institution-id><institution-id institution-id-type="GRID">grid.411497.e</institution-id><institution>Department of Orthopaedic Surgery, </institution><institution>Faculty of Medicine, Fukuoka University, </institution></institution-wrap>Fukuoka, 814-0180 Japan </aff><aff id="Aff13"><label>13</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0374 0039</institution-id><institution-id institution-id-type="GRID">grid.249880.f</institution-id><institution>The Jackson Laboratory, </institution></institution-wrap>Bar Harbor, ME 04609 USA </aff><aff id="Aff14"><label>14</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0413 7653</institution-id><institution-id institution-id-type="GRID">grid.416958.7</institution-id><institution>Department of Pathology &#x00026; Laboratory Medicine, </institution><institution>UC Davis Health System, </institution></institution-wrap>Sacramento, CA 95817 USA </aff><aff id="Aff15"><label>15</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0387 3667</institution-id><institution-id institution-id-type="GRID">grid.225279.9</institution-id><institution>Sarcoma Program, </institution><institution>Zucker School of Medicine at Hofstra/Northwell &#x00026; Cold Spring Harbor Laboratory, </institution></institution-wrap>Long Island, NY 10142 USA </aff><aff id="Aff16"><label>16</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2112 1969</institution-id><institution-id institution-id-type="GRID">grid.4391.f</institution-id><institution>Carlson College of Veterinary Medicine, </institution><institution>Oregon State University, </institution></institution-wrap>Corvallis, OR 97331 USA </aff><aff id="Aff17"><label>17</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2186 7496</institution-id><institution-id institution-id-type="GRID">grid.264784.b</institution-id><institution>Department of Mathematics and Statistics, </institution><institution>Texas Tech University, </institution></institution-wrap>Lubbock, TX 79409 USA </aff><aff id="Aff18"><label>18</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 9758 5690</institution-id><institution-id institution-id-type="GRID">grid.5288.7</institution-id><institution>Department of Otolaryngology &#x02013; Head and Neck Surgery, </institution><institution>Oregon Health &#x00026; Science University, </institution></institution-wrap>Portland, OR 97239 USA </aff><aff id="Aff19"><label>19</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0742 1666</institution-id><institution-id institution-id-type="GRID">grid.414216.4</institution-id><institution>H&#x000f4;pital Maisonneuve-Rosemont, </institution></institution-wrap>Montreal, H1T 2M4 Canada </aff><aff id="Aff20"><label>20</label>Omics Data Automation, 12655 SW Beaverdam Road, Beaverton, OR 97005 USA </aff></contrib-group><pub-date pub-type="epub"><day>17</day><month>6</month><year>2019</year></pub-date><pub-date pub-type="pmc-release"><day>17</day><month>6</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>19</volume><elocation-id>593</elocation-id><history><date date-type="received"><day>20</day><month>11</month><year>2018</year></date><date date-type="accepted"><day>7</day><month>5</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2019</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Cancer patients with advanced disease routinely exhaust available clinical regimens and lack actionable genomic medicine results, leaving a large patient population without effective treatments options when their disease inevitably progresses. To address the unmet clinical need for evidence-based therapy assignment when standard clinical approaches have failed, we have developed a probabilistic computational modeling approach which integrates molecular sequencing data with functional assay data to develop patient-specific combination cancer treatments.</p></sec><sec><title>Methods</title><p id="Par2">Tissue taken from a murine model of alveolar rhabdomyosarcoma was used to perform single agent drug screening and DNA/RNA sequencing experiments; results integrated via our computational modeling approach identified a synergistic personalized two-drug combination. Cells derived from the primary murine tumor were allografted into mouse models and used to validate the personalized two-drug combination.</p><p id="Par3">Computational modeling of single agent drug screening and RNA sequencing of multiple heterogenous sites from a single patient&#x02019;s epithelioid sarcoma identified a personalized two-drug combination effective across all tumor regions. The heterogeneity-consensus combination was validated in a xenograft model derived from the patient&#x02019;s primary tumor.</p><p id="Par4">Cell cultures derived from human and canine undifferentiated pleomorphic sarcoma were assayed by drug screen; computational modeling identified a resistance-abrogating two-drug combination common to both cell cultures. This combination was validated in vitro via a cell regrowth assay.</p></sec><sec><title>Results</title><p id="Par5">Our computational modeling approach addresses three major challenges in personalized cancer therapy: synergistic drug combination predictions (validated in vitro and in vivo in a genetically engineered murine cancer model), identification of unifying therapeutic targets to overcome intra-tumor heterogeneity (validated in vivo in a human cancer xenograft), and mitigation of cancer cell resistance and rewiring mechanisms (validated in vitro in a human and canine cancer model).</p></sec><sec><title>Conclusions</title><p id="Par6">These proof-of-concept studies support the use of an integrative functional approach to personalized combination therapy prediction for the population of high-risk cancer patients lacking viable clinical options and without actionable DNA sequencing-based therapy.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12885-019-5681-6) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Personalized therapy</kwd><kwd>Combination therapy</kwd><kwd>Artificial intelligence and machine learning</kwd><kwd>Pediatric cancer</kwd><kwd>Sarcoma</kwd><kwd>Drug screening</kwd><kwd>High-throughput sequencing</kwd><kwd>Computational modeling</kwd></kwd-group><funding-group><award-group><funding-source><institution>Scott Carter Foundation</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>SuperSam Foundation</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100001021</institution-id><institution>Damon Runyon Cancer Research Foundation</institution></institution-wrap></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100006058</institution-id><institution>St. Baldrick's Foundation</institution></institution-wrap></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>Prayers for Elijah</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>AAO-HNSF Saidee Keller Grant</institution></funding-source><award-id>N/A</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution>AAO-HNSF Grant</institution></funding-source><award-id>N/A</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100008982</institution-id><institution>National Science Foundation</institution></institution-wrap></funding-source><award-id>CCF0953366</award-id><principal-award-recipient><name><surname>Pal</surname><given-names>Ranadip</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution>Centralized Otolaryntology Research Effort</institution></funding-source><award-id>N/A</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2019</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par34">Despite decades of advancements in cancer treatment, over 600,000 patients with solid tumors die annually in North America [<xref ref-type="bibr" rid="CR1">1</xref>], including approximately 5000 sarcoma-related deaths. The population of high-risk, late-stage, recurrent, rare or refractory cancer patients who have exhausted standard clinical pathways and lack further treatment options represents a major unmet clinical need. Currently, DNA sequencing of tumors for druggable mutations leaves approximately 60% of patients without an actionable result [<xref ref-type="bibr" rid="CR2">2</xref>, <xref ref-type="bibr" rid="CR3">3</xref>]. Additionally, in many cases, single drug therapy fails to provide sustainable disease control [<xref ref-type="bibr" rid="CR4">4</xref>]. A critical missing element in personalized cancer therapy design is the lack of effective methodologies for model-based prediction, design, and prioritization of patient-specific drug <italic>combinations</italic>, especially in the presence of limited tumor tissue material.</p><p id="Par35">Numerous approaches to computational modeling of drug sensitivity and therapy assignment exist, in part to address ambiguity in DNA sequencing results [<xref ref-type="bibr" rid="CR2">2</xref>, <xref ref-type="bibr" rid="CR5">5</xref>]. These approaches are primarily based on gene expression [<xref ref-type="bibr" rid="CR6">6</xref>], or a combination of genomic and epigenomic data [<xref ref-type="bibr" rid="CR7">7</xref>]. For instance, 1) integrative genomic models using Elastic Net regression techniques have been developed from large datasets such as the Cancer Cell Line Encyclopedia (CCLE) [<xref ref-type="bibr" rid="CR8">8</xref>] database; 2) integrative models using Random Forests with Stacking [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>] to integrate multiple genetic data sets for sensitivity prediction; and 3) a team science based sensitivity prediction challenge produced independent models integrating multiple data types for sensitivity prediction [<xref ref-type="bibr" rid="CR11">11</xref>]; despite 44 individual models and a &#x0201c;wisdom of crowds&#x0201d; approach merging the top-ranked predictive models together, none of the approaches surpassed 70% predictive accuracy [<xref ref-type="bibr" rid="CR11">11</xref>] falling short of a reasonable accuracy threshold for clinical utility. Some recent work has focused on the use of functional data for therapy selection, such as 1) the use of microfluidics to test multiple drugs efficiently on primary patient samples [<xref ref-type="bibr" rid="CR12">12</xref>], 2) the use of shRNA libraries to predict drug combinations for heterogenous tumor populations [<xref ref-type="bibr" rid="CR13">13</xref>], and 3) a re-analysis of the CCLE database used machine learning models integrating functional response data to improve sensitivity prediction accuracy over molecular data-based Elastic Net models [<xref ref-type="bibr" rid="CR14">14</xref>]. Integration of functional data may improve overall predictive accuracy over solely molecular data-based predictive models, especially for individual patient samples, emphasizing the need for improved drug sensitivity prediction to enable patient-specific therapy design.</p><p id="Par36">To address the need for accurate prediction of drug sensitivity and design of multi-drug combinations, we previously developed a functional drug sensitivity-based modeling approach termed Probabilistic Target Inhibition Maps (PTIMs) [<xref ref-type="bibr" rid="CR14">14</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>]. The base PTIM methodology integrates quantified drug-target inhibition information (EC<sub>50</sub> values) and log-scaled experimental drug sensitivities (IC<sub>50</sub> values) to identify mechanistic target combinations explaining drug sensitivity data. PTIM modeling improved predictive accuracy over Elastic Net models from the CCLE dataset [<xref ref-type="bibr" rid="CR14">14</xref>], and has guided in silico validation experiments from primary canine osteosarcoma cell models [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR16">16</xref>&#x02013;<xref ref-type="bibr" rid="CR18">18</xref>] and in vitro validation experiments [<xref ref-type="bibr" rid="CR19">19</xref>] on diffuse intrinsic pontine glioma (DIPG) cell models. Herein, we present proof-of-concept validation experiments of the integrative PTIM pipeline (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>) using soft tissue sarcoma as a paradigm. Each validation experiment applies PTIM combination therapy design to address one of three critical unmet needs in cancer treatment: 1) selection of functional evidence-based synergistic drug combinations, validated in murine alveolar rhabdomyosarcoma (aRMS); 2) consensus modeling of multi-site drug sensitivity data to overcome intra-tumor heterogeneity, validated in epithelioid sarcoma (EPS); and 3) resistance abrogation by targeting of parallel biological pathways, validated in undifferentiated pleomorphic sarcoma (UPS).<fig id="Fig1"><label>Fig. 1</label><caption><p>Schematic representation of experimental and computational approach to personalized combination targeted therapy predictions. Following tumor extraction and culture establishment, biological data is generated (e.g., chemical screening, transcriptome sequencing, exome sequencing, siRNA interference screening and phosphoproteomic analysis) and used as input for PTIM modeling. To briefly explain the graphical model representation, targets A and B denote two independent single points of failure. Targets C and D denote parallel targets, which independently are not predicted to be effective, but together will be synergistic and lead to significant cell growth inhibition. Targets A, B, and the C-D parallel block are in series and may target independent pathways. Series blocks, when inhibited together, may abrogate cancer resistance mechanisms by knockdown of independent pathways. Model sensitivity scores for gene target combinations are used to design and rank follow-up in vitro validation and in vivo validation experiments. The &#x0201c;Exome-Seq&#x0201d; representative images was adapted from an image on the Wikipedia Exome sequencing article originally created by user SarahKusala and available under Creative Commons 3.0 license. An unaltered portion of the image was used. The mouse image used is public domain and accessed through Bing image search at the following weblink: <ext-link ext-link-type="uri" xlink:href="http://img.res.meizu.com/img/download/uc/27/83/20/60/00/2783206/w100h100">http://img.res.meizu.com/img/download/uc/27/83/20/60/00/2783206/w100h100</ext-link></p></caption><graphic xlink:href="12885_2019_5681_Fig1_HTML" id="MO1"/></fig></p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Cell model establishment</title><p id="Par37">The mouse primary tumor cell culture U23674 was established from a tumor at its site of origin in a genetically engineered <italic>Myf6Cre,Pax3:Foxo1,p53</italic> mouse bearing alveolar rhabdomyosarcoma (aRMS) as previously described [<xref ref-type="bibr" rid="CR20">20</xref>]. In brief, the tumor was minced and digested with collagenase (10&#x02009;mg/ml) overnight at 4&#x02009;&#x000b0;C. Dissociated cells were then incubated in Dulbecco&#x02019;s Modified Eagle&#x02019;s Medium (DMEM) (11995&#x02013;073; Thermo Fisher Scientific, Waltham, MA, USA) supplemented with 10% fetal bovine serum (FBS) (26,140,079; Thermo Fisher Scientific) and 1% penicillin-streptomycin (15140&#x02013;122; Thermo Fisher Scientific) in 5% CO<sub>2</sub> at 37&#x02009;&#x000b0;C.</p><p id="Par38">The human epithelioid sarcoma (EPS) sample PCB490 was collected from a patient undergoing planned surgical resection. Tumor tissue was partitioned into 5 distinct regions, minced and digested with collagenase type IV (10&#x02009;mg/ml) overnight at 4&#x02009;&#x000b0;C. The dissociated cells were then incubated in RPMI-1640 (11875&#x02013;093; Thermo Fisher Scientific, Waltham, MA, USA) supplemented with 10% fetal bovine serum (FBS) and 1% penicillin-streptomycin in 5% CO<sub>2</sub> at 37&#x02009;&#x000b0;C. Sections 3, 4, and 5 (PCB490&#x02013;3, PCB490&#x02013;4, PCB490&#x02013;5) successfully grew in culture. Samples from each region were also sent to The Jackson Laboratory (JAX) for patient-derived xenograft (PDX) model establishment. Cultures were maintained at low passage to minimize biological variation from the original patient tumor. Remaining tumor pieces were snap frozen for future DNA, RNA and protein isolation.</p><p id="Par39">The human EPS sample PCB495 was received through the CCuRe-FAST tumor bank program. To create the cell cultures from the PCB495 primary tumor, the tumor was minced and digested with collagenase (10&#x02009;mg/ml) overnight at 4&#x02009;&#x000b0;C. The dissociated cells were then incubated in RPMI-1640 media supplemented with 10% fetal bovine serum (FBS) and 1% penicillin-streptomycin in 5% CO<sub>2</sub> at 37&#x02009;&#x000b0;C.</p><p id="Par40">The human undifferentiated pleomorphic sarcoma (UPS) PCB197 was received through the CCuRe-FAST tumor bank program. To create the cell cultures from the PCB197 primary tumor, the tumor was minced and digested with collagenase (10&#x02009;mg/ml) overnight at 4&#x02009;&#x000b0;C. The dissociated cells were then incubated in RPMI-1640 media supplemented with 10% fetal bovine serum (FBS) and 1% penicillin-streptomycin in 5% CO<sub>2</sub> at 37&#x02009;&#x000b0;C.</p><p id="Par41">All human tissue samples were acquired through the Childhood Cancer Registry for Familial and Sporadic Tumors (CCuRe-FAST) tumor banking program. All patients enrolled in CCuRe-FAST provided informed consent. All aspects of the study were reviewed and approved by the Oregon Health &#x00026; Science University (OHSU) Institutional Review Board (IRB). Patient data and clinical and pathologic information are maintained in a de-identified database.</p><p id="Par42">The canine UPS sample S1&#x02013;12 was obtained from Oregon State University&#x02019;s (OSU) College of Veterinary Medicine. OSU Institutional Animal Care and Use Committee (IACUC) approval was obtained for procurement of the tissue. To establish S1&#x02013;12 cell culture, tumor tissue was minced and digested with collagenase (10&#x02009;mg/ml) overnight at 4&#x02009;&#x000b0;C. The dissociated cells were then incubated in RPMI-1640 media supplemented with 10% fetal bovine serum (FBS) and 1% penicillin-streptomycin in 5% CO<sub>2</sub> at 37&#x02009;&#x000b0;C.</p></sec><sec id="Sec4"><title>Immunoblotting of PCB490</title><p id="Par43">Tumor tissue and cells from PCB490&#x02013;1,2, and 5 were lysed in radioimmunoprecipitation (RIPA) buffer containing both protease and phosphatase inhibitors (Sigma Aldrich, St. Louis, MO). Lysates were homogenized and clarified by centrifugation at 14,000&#x02009;rpm for 10&#x02009;min. Thirty &#x003bc;g of protein was electrophoresed in 7.5% polyacrylamide gels, transferred to PVDF membranes for immunoblot analysis with mouse anti-BAF47 antibody (cat. 612,110, BD Biosciences, San Jose, CA) and mouse anti-&#x003b2;-actin antibody (cat. A1978, Sigma Aldrich), and developed by chemiluminescence (cat. 170&#x02013;5061, BioRad Clarity Western ECL Substrate, Hercules, CA) per the manufacturer&#x02019;s protocol.</p></sec><sec id="Sec5"><title>Cell lines</title><p id="Par44">The VA-ES-BJ (Accession CVCL_1785) cell line was purchased commercially (cat# CRL-2138, ATCC, Manassas, VA). The cell line VA-ES-BJ has been validated before shipment by STR profile and mycoplasma testing. The cell line was used for the experiments directly after reception of the cell line.</p><p id="Par45">The ESX cell line was provided by author TT [<xref ref-type="bibr" rid="CR21">21</xref>]. The FU-EPS-1 (Accession CVCL_E311) cell line was provided by author JNishio [<xref ref-type="bibr" rid="CR22">22</xref>].</p><p id="Par46">Neither ESX nor FU-EPS-1 have available STR validation profiles, and so comparison to a standard STR profile cannot be performed. However, both cell lines were checked for INI1 loss consistent with EPS cell lines. Cell lines were tested for mycoplasma with the Lonza MycoAlert Plus test kit. Cat. LT07&#x02013;703, Lonza Bioscience, Singapore).</p></sec><sec id="Sec6"><title>Patient derived xenograft (PDX) model development</title><p id="Par47">All aspects of cancer tissue sharing for model development were reviewed and approved by the Oregon Health &#x00026; Science University Institutional Review Board. The PCB490 PDX model was generated at JAX (model number J00007860) by implanting surgical human tumor tissue into 4&#x02013;6-week-old female immunodeficient NOD.Cg-<italic>Prkdc</italic><sup><italic>scid</italic></sup>
<italic>Il2rg</italic><sup><italic>tm1Wjl</italic></sup>/SzJ (NSG) mice without prior in vitro culturing of the tumor cells. Time from surgery to implantation was approximately 24&#x02009;h. Once a xenografted tumor reached ~&#x02009;1000&#x02009;mm<sup>3</sup>, the tumor was harvested and divided into 3&#x02013;5&#x02009;mm<sup>3</sup> fragments. Fragments were implanted into five 6&#x02013;8-week-old female NSG mice for expansion to P1. Other fragments were sent for quality control assessment (see below). The remaining fragments were cryopreserved in 10% DMSO. When P1 tumors reached 1000mm<sup>3</sup> they were harvested and divided into quarters: &#x000bc; for quality control, &#x000bc; snap frozen for genomics, &#x000bc; placed into RNALater (Ambion) for RNA-seq, and the remaining &#x000bc; divided into 3&#x02013;5&#x02009;mm<sup>3</sup> pieces and cryopreserved in 10% DMSO.</p><p id="Par48">The quality control procedures employed for PDX model development included testing the patient tumor for LCMV (lymphocytic choriomeningitis virus), bacterial contamination, and tumor cell content. The engrafted tumors at P0 and P1 were DNA fingerprinted using a Short Tandem Repeat (STR) assay to ensure model provenance in subsequent passages.</p><p id="Par49">Model details available online at:</p><p id="Par50">
<ext-link ext-link-type="uri" xlink:href="http://tumor.informatics.jax.org/mtbwi/pdxDetails.do?modelID=J000078604">http://tumor.informatics.jax.org/mtbwi/pdxDetails.do?modelID=J000078604</ext-link>
</p><p id="Par51">Immunohistochemistry (IHC) for human CD45 (IR75161&#x02013;2, Agilent Technologies) was performed on paraffin embedded blocks of engrafted tumors to identify cases of lymphomagenesis which have been reported previously in PDXs. IHC for human ki67 (IR62661&#x02013;2, Agilent Technologies) was used to ensure the propagated tumors were human in origin. H&#x00026;E sections of engrafted tumors were reviewed by a board-certified pathologist (RGE) to evaluate concordance of the morphological features of the engrafted tumor to the patient tumor. Further, tissue was stained with vimentin (IR63061&#x02013;2, Agilent Technologies) to confirm human origin.</p><p id="Par52">Model information is publicly accessible at: <ext-link ext-link-type="uri" xlink:href="http://tumor.informatics.jax.org/mtbwi/pdxSearch.do">http://tumor.informatics.jax.org/mtbwi/pdxSearch.do</ext-link></p></sec><sec id="Sec7"><title>Chemical screens</title><p id="Par53">Four chemical screens were used to generate functional drug screening data. The first screen was a custom 60 agent chemical screen of well-characterized target inhibitors denoted the Pediatric Preclinical Testing Initiative Screen Version 2.1 (PPTI screen). Chemical concentrations of agents in all chemical screens were either [10&#x02009;nM, 100&#x02009;nM, 1&#x02009;&#x003bc;M, 10&#x02009;&#x003bc;M] or [100&#x02009;nM, 1&#x02009;&#x003bc;M, 10&#x02009;&#x003bc;M, 100&#x02009;&#x003bc;M] depending on compound activity range. Fifty-four of the 60 drugs on the chemical screen have a published quantified drug-target inhibition profile.</p><p id="Par54">The second screen was a custom 60 agent chemical screen denoted Drug Screen V3 consisting of a variety of small molecule kinase inhibitors, epigenetic target inhibitors, and cell cycle inhibitors. Fifty-two of 60 drugs on the chemical screen have a published drug-target inhibition profile.</p><p id="Par55">The third chemical screen was a GlaxoSmithKline open access Orphan Kinome-focused chemical screen (denoted GSK screen) consisting of 402 novel and newly characterized tool compounds [<xref ref-type="bibr" rid="CR23">23</xref>] with target inhibition profiles quantified by Nanosyn Screening and Profiling Services. Drug-target interaction was assayed over 300 protein targets for each of the 402 compounds. The compounds were tested at 100&#x02009;nM and 10&#x02009;&#x003bc;M concentrations to bracket the drug-target EC<sub>50</sub> values. The final EC<sub>50</sub> values used for analysis of the chemical screen results were inferred from the available data using hill curve fitting to predict the 50% inhibition point.</p><p id="Par56">The final screen was a Roche-developed open access chemical screen (denoted Roche screen) consisting of 223 novel kinase inhibitor compounds [<xref ref-type="bibr" rid="CR24">24</xref>]. Roche screen compounds had a mixture of quantified or qualified drug-target inhibition profiles, though drug-target inhibition profiles were made available only for sensitive compounds.</p><p id="Par57">Cell cultures were plated in 384-well plates at a seeding density of 5000 cells per well onto gradated concentrations of drug screen compounds. Cells were incubated in model-specific culture media at 37&#x02009;&#x000b0;C, with 5% CO<sub>2</sub>, for 72&#x02009;h. Cell viability was assessed by CellTiter-Glo&#x000ae; Luminescent Cell Viability Assay (cat. G7570, Promega, Madison, WI) per manufacturer&#x02019;s protocol. Luminescence was measured using a BioTek Synergy HT plate reader (BioTek, Winooski, VT). Single agent IC<sub>50</sub> values were determined using a hill curve-fitting algorithm with variable hill slope coefficients performed in Microsoft Excel. Manual curation and re-fitting of the results was performed before results were finalized.</p><p id="Par58">U23674 primary tumor culture was assayed via three drug screens: PPTI drug screen, GSK drug screen, and the Roche drug screen (Additional files <xref rid="MOESM1" ref-type="media">1</xref>, <xref rid="MOESM2" ref-type="media">2</xref>, <xref rid="MOESM3" ref-type="media">3</xref>: Figures S1-S3 and Additional files <xref rid="MOESM15" ref-type="media">15</xref>, <xref rid="MOESM16" ref-type="media">16</xref>, <xref rid="MOESM17" ref-type="media">17</xref>, <xref rid="MOESM18" ref-type="media">18</xref>, <xref rid="MOESM19" ref-type="media">19</xref>, <xref rid="MOESM20" ref-type="media">20</xref>, <xref rid="MOESM21" ref-type="media">21</xref>: Tables S1-S7). S1&#x02013;12 primary tumor culture was screened using the PPTI screen (Additional file <xref rid="MOESM36" ref-type="media">36</xref>: Table S22). PCB197 primary tumor culture was screened using the PPTI screen (Additional file <xref rid="MOESM36" ref-type="media">36</xref>: Table S22). PCB490&#x02013;3, PCB490&#x02013;4, PCB490&#x02013;5 primary cultures were screened with Drug Screen V3 and the Roche drug screen (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>, Additional files <xref rid="MOESM30" ref-type="media">30</xref>, <xref rid="MOESM31" ref-type="media">31</xref>: Tables S15 and S16). Cell lines ESX, FU-EPS-1, and VA-ES-BJ were screened with Drug Screen V3 (Additional file <xref rid="MOESM35" ref-type="media">35</xref>: Table S21). PCB495 primary culture was screened with Drug Screen V3 (Additional file 35: Table S21).</p></sec><sec id="Sec8"><title>U23674 drug combination studies and calculation of combination index (CI)</title><p id="Par60">U23674 drug combination validation experiments were guided by GlaxoSmithKline chemical screen PTIM models. Single agent validations to calculate independent drug efficacy were performed at dosages in the range of 5&#x02009;nM to 100&#x02009;&#x003bc;M to bracket IC<sub>50</sub> and IC<sub>25</sub> dosage values; for combination experiments, the IC<sub>25</sub> dosage for one agent was tested in combination with gradated dosages (5&#x02009;nM to 100&#x02009;&#x003bc;M) of the complementary agent, and vice versa. Single agent and combination agent validation experiments were performed at passage 5.</p><p id="Par61">CI values were generated using the CompuSyn software tool. Effect values for CompuSyn monotherapy and combination therapy were determined by mean cell death based on <italic>n</italic>&#x02009;=&#x02009;3 technical replicates with <italic>n</italic>&#x02009;=&#x02009;4 replicates for the following treatment conditions: OSI-906, GDC-0941, OSI-906&#x02009;+&#x02009;GDC-0941 (OSI-906 at IC<sub>25</sub>&#x02009;+&#x02009;GDC-0941 at varying dosage, OSI-906 at varying dosage + GDC-0941 at IC<sub>25</sub>). CompuSyn CI values were calculated using the non-constant combination setting [<xref ref-type="bibr" rid="CR25">25</xref>] (Additional file <xref rid="MOESM28" ref-type="media">28</xref>: Table S14).</p><p id="Par62">We performed low-dose validation experiments to verify PTIM-identified synergistic mechanisms of action; reduced dosages of the combination agents were set to 5 times the EC<sub>50</sub> value for the predicted target (175&#x02009;nM OSI-906, 50&#x02009;nM GDC-0941). CompuSyn CI values to validate the mechanism of synergy were calculated using the non-constant combination setting [<xref ref-type="bibr" rid="CR25">25</xref>] (Additional file <xref rid="MOESM27" ref-type="media">28</xref>: Table S14).</p><p id="Par63">In both regular dose and low dose experiments, CI values are reported only for functionally relevant dosages, i.e. dosages between the drug target&#x02019;s EC<sub>50</sub> and the drug&#x02019;s maximum achievable human clinical dosage (C<sub>max</sub>). For OSI-906, the functional range is approximately [10&#x02009;nM, 5&#x02009;&#x003bc;M] (mouse pharmacokinetics: ~&#x02009;16&#x02009;&#x003bc;M C<sub>max</sub>, 6.16&#x02009;&#x003bc;M C<sub>ss</sub>; human pharmacokinetics: ~&#x02009;1.481&#x02009;&#x003bc;M C<sub>max</sub>, 720&#x02009;nM C<sub>ss</sub>). For GDC-0941, the functional range is approximately [5&#x02009;nM, 1&#x02009;&#x003bc;M] (mouse pharmacokinetics: ~&#x02009;12&#x02009;&#x003bc;M C<sub>max</sub>, 1.59&#x02009;&#x003bc;M C<sub>ss</sub>, human pharmacokinetics: ~&#x02009;1.481&#x02009;&#x003bc;M C<sub>max</sub>, 720&#x02009;nM C<sub>ss</sub>). CI values outside these ranges are denoted as N/A in Additional file <xref rid="MOESM28" ref-type="media">28</xref>: Table S14.</p></sec><sec id="Sec9"><title>U23674 exome sequencing analysis</title><p id="Par64">Somatic point mutations were identified using the Genome Analysis Toolkit [<xref ref-type="bibr" rid="CR26">26</xref>] (GATK, version 3.5.0) from the Broad Institute. Captured DNA libraries were sequenced with the Illumina HiSeq 1000 in paired-end mode. The reads that passed the Illumina BaseCall chastity filter were used for subsequent analysis. The mate pairs were pooled and mapped as single reads to the NCBI GRCm38/mm10 reference genome using the Burrows-Wheeler Aligner [<xref ref-type="bibr" rid="CR27">27</xref>] (version 0.7.12), with shorter split hits marked as secondary to ensure compatibility with downstream tools. Identified PCR duplicates, defined as reads likely originating from the same original DNA fragments, were removed using Picard Tools MarkDuplicates (version 1.133). Mapping artifacts introduced during initial mapping are realigned using the GATK IndelRealigner, and base quality score recalibration to empirically adjust quality scores for variant calling was performed by the GATK BaseRecalibrator tool. The same process was used to process both the tumor sample and the matched normal tail sample. Variant discovery was performed by MuTect2 [<xref ref-type="bibr" rid="CR28">28</xref>], with the NCBI GRCm38/mm10 dbSNP database used to filter known polymorphisms present in the paired sample. Variant annotation and effect prediction was performed using SnpEff [<xref ref-type="bibr" rid="CR29">29</xref>] using the GRCm38.81 database. Only medium and high impact effect variants are considered for the purpose of downstream analysis and reporting in figures. Exome analysis protocol is based on the GATK Best Practices protocol.</p><p id="Par65">VarScan2 was used for copy number variation analysis of the paired tumor-normal data [<xref ref-type="bibr" rid="CR30">30</xref>]. The Burrows-Wheeler Aligner was used to align the tumor and normal samples to NCBI GRCm38/mm10 reference genome as described previously. Samtools (version 0.1.19) mpileup tool with minimum mapping quality of 10 was used to generate the pileup file required by the VarScan2 copycaller function; log<sub>2</sub> exon coverage ratio data from copycaller was segmented using DNAcopy with the undo.splits&#x02009;=&#x02009;&#x0201c;sdundo&#x0201d; parameter, and deviation from the null hypothesis set above 3 standard deviations. Genes in segments with segment mean above 0.25 or below &#x02212;&#x02009;0.25 and with <italic>p</italic>-value below 1e-10 were called as gained or lost, respectively. Copy number variation analysis protocol was partly based on the VarScan2 user manual [<xref ref-type="bibr" rid="CR31">31</xref>].</p></sec><sec id="Sec10"><title>U23674 RNA deep sequencing analysis</title><p id="Par66">RNA sequencing was performed on a low-passage U23674 culture, and on the control sample consisting of regenerating mouse muscle tissue following cardiotoxin injury in vivo. The paired-end raw reads were aligned to the NCBI GRCm38/mm10 reference mouse genome using TopHat version 2.0.9 [<xref ref-type="bibr" rid="CR32">32</xref>] using Bowtie2 as the short-read aligner. Up to two alignment mismatches were permitted before a read alignment was discarded. The aligned reads were assembled into transcripts using Cufflinks version 2.1.1 [<xref ref-type="bibr" rid="CR33">33</xref>]. Differential gene expression of tumor sample vs. control was performed by Cuffdiff using standard parameters. RNA analysis protocol was largely based on the approach described in the Tophat2 publication [<xref ref-type="bibr" rid="CR34">34</xref>]. Quantified gene expression is provided in Additional file <xref rid="MOESM23" ref-type="media">23</xref>: Table S9.</p></sec><sec id="Sec11"><title>PCB490 exome sequencing analysis</title><p id="Par67">Somatic point mutations were identified using the Genome Analysis Toolkit [<xref ref-type="bibr" rid="CR26">26</xref>] (GATK, version 3.8.0) from the Broad Institute. Captured DNA libraries were sequenced in paired-end mode via the BGISeq 500 system at Beijing Genomics Institute. The reads that passed the Illumina BaseCall chastity filter were used for subsequent analysis. The mate pairs were pooled and mapped as single reads to the NCBI GRCh38 reference genome using the Burrows-Wheeler Aligner [<xref ref-type="bibr" rid="CR27">27</xref>] (version 0.7.12), with shorter split hits marked as secondary to ensure compatibility with downstream tools. Identified PCR duplicates, defined as reads likely originating from the same original DNA fragments, were removed using Picard Tools MarkDuplicates (version 1.133). Mapping artifacts introduced during initial mapping are realigned using the GATK IndelRealigner, and base quality score recalibration to empirically adjust quality scores for variant calling was performed by the GATK BaseRecalibrator tool. The same process was used to process both the tumor sample and the matched normal sample. Variant discovery was performed by MuTect2 [<xref ref-type="bibr" rid="CR28">28</xref>], with the NCBI GRCh38 dbSNP database used to filter known polymorphisms present in the paired sample. Variant annotation and effect prediction was performed using SnpEff [<xref ref-type="bibr" rid="CR29">29</xref>] using the GRCh38.87 database. Only medium and high impact variants are considered for the purpose of downstream analysis and reporting in figures. Exome analysis protocol is based on the GATK Best Practices protocol.</p><p id="Par68">VarScan2 was used for copy number variation analysis of the paired tumor-normal data [<xref ref-type="bibr" rid="CR30">30</xref>]. The Burrows-Wheeler Aligner was used to align the tumor and normal samples to NCBI GRCh38 reference genome as described previously. Samtools (version 1.6) mpileup tool with minimum mapping quality of 10 was used to generate the pileup file required by the VarScan2 copycaller function; log<sub>2</sub> exon coverage ratio data from copycaller was segmented using DNAcopy with the undo.splits&#x02009;=&#x02009;&#x0201c;sdundo&#x0201d; parameter, and deviation from the null hypothesis set above 3 standard deviations. Genes in segments with segment mean 2 standard deviations above or below &#x000b1;0.5 and with <italic>p</italic>-value below 1e-10 were called as gained or lost, respectively. Copy number variation analysis protocol was partly based on the VarScan2 user manual [<xref ref-type="bibr" rid="CR31">31</xref>].</p></sec><sec id="Sec12"><title>PCB490 RNA deep sequencing analysis</title><p id="Par69">The PCB490 transcriptome library was sequenced with the Illumina HiSeq 2500 in paired-end mode. The reads that passed the chastity filter of Illumina BaseCall software were used for subsequent analysis. The paired-end raw reads for each RNA-seq sample were aligned to the UCSC hg38 reference human genome using Bowtie2 as the short-read aligner [<xref ref-type="bibr" rid="CR32">32</xref>] using, allowing up two alignment mismatches before a read alignment was discarded. The aligned reads were assembled into transcripts using Cufflinks version 2.1.1 [<xref ref-type="bibr" rid="CR33">33</xref>] and quantification was performed with Cuffquant [<xref ref-type="bibr" rid="CR33">33</xref>]. RNA analysis protocol was adapted from the approach described in the original TopHat2 publication [<xref ref-type="bibr" rid="CR34">34</xref>] (Additional file <xref rid="MOESM33" ref-type="media">33</xref>: Table S19).</p></sec><sec id="Sec13"><title>RAPID siRNA screen of U23674</title><p id="Par70">U23674 underwent functional single gene knockdown (siRNA interference screen, Additional file <xref rid="MOESM24" ref-type="media">24</xref>: Table S10), however siRNA results were inconsistent with drug screening data (Additional file <xref rid="MOESM25" ref-type="media">25</xref>: Table S11) and are thus relegated to the supplement.</p><p id="Par71">To assess the contribution of individual receptor tyrosine kinases to survival of U23674, we performed RAPID siRNA knockdown screening of U23674. Efficacy of single target knockdown of 85 members of the mouse tyrosine kinase family was performed as previously described [<xref ref-type="bibr" rid="CR35">35</xref>]. Target sensitivity was determined by resulting cell viability quantified using an MTT assay (M6494; Thermo Fisher Scientific, Waltham, MA, USA). Targets with viability two standard deviations below the mean were identified as high-importance targets [<xref ref-type="bibr" rid="CR35">35</xref>] (Additional file <xref rid="MOESM24" ref-type="media">24</xref>: Table S10).</p></sec><sec id="Sec14"><title>Phosphoproteomic screen of U23674</title><p id="Par72">U23674 underwent phosphoproteome quantification (Kinexus phosphoproteomics analysis, Additional file <xref rid="MOESM26" ref-type="media">26</xref>: Table S12), however phosphoproteomics results were inconsistent among sample replicates and are thus relegated to the supplement.</p><p id="Par73">To identify differentially phosphorylated protein targets, phosphoproteomics assays (Kinexus, Vancouver, British Columbia, Canada) were used to compare two duplicate cell lysates from U23674 against two duplicate cell lysates from regenerating muscle tissue acting as normal control. To perform the phosphoproteomics analyses, 50&#x02009;&#x003bc;g of protein lysate from each sample was covalently labeled with a proprietary fluorescent dye. Free dye molecules were removed by gel filtration. After blocking non-specific binding sites on the array, an incubation chamber was mounted onto the microarray to permit the loading of related samples side by side on the same chip. Following sample incubation, unbound proteins were washed away. Each array produces a pair of 16-bit images, which are captured with a Perkin-Elmer ScanArray Reader laser array scanner. Signal quantification was performed with <italic>ImaGene 8.0</italic> from BioDiscovery with predetermined settings for spot segmentation and background correction. The background-corrected raw intensity data are logarithmically transformed. Z scores are calculated by subtracting the overall average intensity of all spots within a sample from the raw intensity for each spot, and dividing it by the standard deviations (SD) of all of the measured intensities within each sample (Additional file <xref rid="MOESM26" ref-type="media">26</xref>: Table S12).</p></sec><sec id="Sec15"><title>Probabilistic target inhibition maps</title><p id="Par74">The Probabilistic Target Inhibition Map (PTIM) approach considers that the underlying mechanism for sensitivity to targeted drugs can be represented by a combination of parallel target groups (all parallel targets need to be inhibited to slow or stop tumor proliferation, similar to Boolean &#x02018;AND&#x02019; logic) and series target groups (inhibiting any all targets in any target group will slow or stop tumor proliferation, similar to Boolean &#x02018;OR&#x02019; logic). For estimating the series and parallel targets, we analyze cancer cell response to multi-target single agent drugs with overlapping but distinct target sets. For instance, drugs having the same selective target (such as pelitinib and erlotinib, which are potent inhibitors of the kinase target <italic>EGFR</italic>) can show different sensitivity in vitro which can be attributed to the biologically relevant side targets of the drugs. Our framework considers primary and secondary drug targets and generates logical groupings of targets (as single-target or multi-target blocks) that best explain chemical screen response data. We now incorporate secondary information to refine PTIM models.</p><graphic position="anchor" xlink:href="12885_2019_5681_Figa_HTML" id="MO2"/><sec id="Sec16"><title>PTIM circuit models</title><p id="Par75">PTIM models are visually represented as circuit models. Each &#x0201c;block&#x0201d; in the circuit represents a combination of two or more gene targets that explain sensitivity of a set of single agent compounds. The drug set represented by an individual block is determined by the PTIM objective function and feature selection algorithm [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR16">16</xref>], and depends on the biological data inputs to the PTIM algorithm.</p><p id="Par76">PTIM circuits consist of multiple blocks. Generally, only target combinations of one to four targets are considered during PTIM modeling. Blocks of one target (represented as single inhibitor symbol, T<sub>1</sub>) are called &#x0201c;single points of failure&#x0201d;, i.e. single targets which alone explain the sensitivity of one or more drug screen agents. Combinations of two targets are visually represented by a rectangular block with two inhibitor symbols (block T<sub>2</sub> &#x02013; T<sub>3</sub>). Combinations of three targets are visually represented by a circular block with three inhibitor symbols (block T<sub>4</sub> &#x02013; T<sub>5</sub> &#x02013; T<sub>6</sub>). Combinations of four targets are visually represented by a circular block with four inhibitor symbols (block T<sub>7</sub> &#x02013; T<sub>8</sub> &#x02013; T<sub>9</sub> &#x02013; T<sub>10</sub>). Each block has an associated score value (e.g. 0.825, 0.800, 0.775, 0.750, respectively) that represents the scaled sensitivity of all drug screen agents grouped in the block&#x02019;s target combination [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR16">16</xref>]. In brief, all single agent sensitivities (as IC<sub>50</sub> values) are log<sub>10</sub> scaled and converted to [0,1] sensitivity values via the following equation:<disp-formula id="Equa"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{sensitivity}=\left\{\begin{array}{c}\ \\ {}\frac{\log \left(\mathrm{maxTestedDose}\right)-\log \left({\mathrm{IC}}_{50}\right)}{\log \left(\mathrm{maxTestedDose}\right)},{\mathrm{IC}}_{50}&#x0003e;{\mathrm{C}}_{\mathrm{max}}\\ {}1,{\mathrm{IC}}_{50}\le {\mathrm{C}}_{\mathrm{max}}\ \end{array}\right. $$\end{document}</tex-math><mml:math id="M2" display="block"><mml:mtext>sensitivity</mml:mtext><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mfrac><mml:mrow><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:mtext>maxTestedDose</mml:mtext></mml:mfenced><mml:mo>&#x02212;</mml:mo><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:msub><mml:mi>IC</mml:mi><mml:mn>50</mml:mn></mml:msub></mml:mfenced></mml:mrow><mml:mrow><mml:mo>log</mml:mo><mml:mfenced close=")" open="("><mml:mtext>maxTestedDose</mml:mtext></mml:mfenced></mml:mrow></mml:mfrac><mml:mo>,</mml:mo><mml:msub><mml:mi>IC</mml:mi><mml:mn>50</mml:mn></mml:msub><mml:mo>&#x0003e;</mml:mo><mml:msub><mml:mi mathvariant="normal">C</mml:mi><mml:mi>max</mml:mi></mml:msub></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:msub><mml:mi>IC</mml:mi><mml:mn>50</mml:mn></mml:msub><mml:mo>&#x02264;</mml:mo><mml:msub><mml:mi mathvariant="normal">C</mml:mi><mml:mi>max</mml:mi></mml:msub><mml:mspace width="0.25em"/></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equa.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par77">Thus, the lower the IC<sub>50</sub>, the higher the sensitivity score. The score assigned to each block is a determined by the sensitivity of the drug screen agents assigned to the block following several correction factors [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR16">16</xref>]. The shape of blocks in PTIM circuits are meant to serve as a convenient visual representation; ordering of PTIM circuit blocks are determined by overall score, with highest scored blocks on the left descending to lowest scored blocks on the right. The general PTIM algorithm is presented in previously published work [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR16">16</xref>&#x02013;<xref ref-type="bibr" rid="CR18">18</xref>]. Methods for integration of secondary biological data are provided in the methods sections for modeling of U23674 and modeling of PCB490.</p></sec><sec id="Sec17"><title>Synergy, heterogeneity, and resistance via PTIM models</title><p id="Par78">PTIM circuit models are also designed to visually represent the clinical challenges PTIM modeling seeks to address. Synergistic drug combinations can be selected for any block with two or more targets by selecting two (or more) drugs which inhibit all targets in the block; the selected combination should kill cancer cells while monotherapy treatment would not. For example, based on (block T<sub>2</sub> &#x02013; T<sub>3</sub>), a drug inhibiting T<sub>2</sub> and a drug inhibiting T<sub>3</sub> will individually not slow tumor growth for the sample patient, while the combination T<sub>2</sub>&#x02009;+&#x02009;T<sub>3</sub> will.</p><p id="Par79">Drug screening multiple spatially-distinct sites from a solid tumor can result in heterogeneous single agent sensitivity. Target group blocks identified as common amongst PTIM models from each distinct region can be used to design a drug combination that should slow or stop tumor growth across the entire heterogeneous tumor. Multi-site PTIM models can thus define heterogeneity-aware drug combinations.</p><p id="Par80">Each block in a PTIM circuit represents a set of effective treatment options; effective options on parallel biological pathways represent multiple distinct treatment options which can individually slow tumor growth. A drug combination which inhibits multiple parallel biological pathway blocks can shut down potential survival mechanisms for cancer cells, thus abrogating development of resistance. Series PTIM blocks can thus define resistance abrogating drug combinations.</p></sec></sec><sec id="Sec18"><title>Integrative nonlinear Boolean modeling for U23674</title><p id="Par81">Probabilistic Target Inhibition Maps (PTIMs) were used for integrative analysis of U23674 biological data [<xref ref-type="bibr" rid="CR16">16</xref>&#x02013;<xref ref-type="bibr" rid="CR18">18</xref>].</p><sec id="Sec19"><title>RNA-seq integration</title><p id="Par82">For targets common to both RNA expression data and drug-target interaction data, we use gene expression data to eliminate possible false positives from chemical screen results and to narrow down the true positives among relevant targets identified by the PTIM approach. False positives are defined here as targets that are inhibited by effective drugs but are not expressed in cancer cells at levels above matched normal cells. Note that we consider the effect of a molecularly-targeted drug is to inhibit the target when it is expressed, thus under-expressed drug targets will have limited impact on drug response. Here, over-expression is determined as gene expression in the tumor sample 50% greater than that in the control sample. The RNA-seq target set is used for PTIM creation via the published model development algorithms.</p><p id="Par83">Formally, RNA-seq data is integrated as below:<disp-formula id="Equb"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{T}:= \mathrm{targets}\ \mathrm{in}\mathrm{hibited}\ \mathrm{in}\ \mathrm{drug}\ \mathrm{screen} $$\end{document}</tex-math><mml:math id="M4" display="block"><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets inhibited in drug screen</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equb.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equc"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{G}:= \mathrm{targets}\ \mathrm{with}\ \mathrm{RNA}-\mathrm{seq}\ \mathrm{expression}\ \mathrm{in}\ \mathrm{tumor}\ \mathrm{and}\ \mathrm{normal}\ \mathrm{cells} $$\end{document}</tex-math><mml:math id="M6" display="block"><mml:mi mathvariant="normal">G</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets with</mml:mtext><mml:mspace width="0.25em"/><mml:mi>RNA</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>seq</mml:mi><mml:mspace width="0.25em"/><mml:mtext>expression in tumor and normal cells</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equc.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equd"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{Tumor}\left(\mathrm{x}\right):= \mathrm{gene}\ \mathrm{expression}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x}\ \mathrm{in}\ \mathrm{tumor}\ \mathrm{sample} $$\end{document}</tex-math><mml:math id="M8" display="block"><mml:mtext>Tumor</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>gene expression of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>in tumor sample</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equd.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Eque"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{Normal}\left(\mathrm{x}\right):= \mathrm{gene}\ \mathrm{expression}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x}\ \mathrm{in}\ \mathrm{normal}\ \mathrm{sample} $$\end{document}</tex-math><mml:math id="M10" display="block"><mml:mtext>Normal</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>gene expression of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>in normal sample</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Eque.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equf"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\in \mathrm{T}\cap \mathrm{G}}\ \mathrm{Ratio}\left(\mathrm{x}\right):= \mathrm{T}\mathrm{umor}\left(\mathrm{x}\right)/\mathrm{Normal}\left(\mathrm{x}\right) $$\end{document}</tex-math><mml:math id="M12" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mtext>Ratio</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>Tumor</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>/</mml:mo><mml:mtext>Normal</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equf.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equg"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\in \mathrm{T}\cap \mathrm{G}}\ \left\{\begin{array}{c}\mathrm{if}\ \mathrm{Ratio}\left(\mathrm{x}\right)\ge 1.5,\mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{for}\ \mathrm{consideration}\ \\ {}\mathrm{if}\ \mathrm{Ratio}\left(\mathrm{x}\right)&#x0003c;1.5,\mathrm{remove}\ \mathrm{target}\ \mathrm{x}\ \mathrm{from}\ \mathrm{consideration}\end{array}\right. $$\end{document}</tex-math><mml:math id="M14" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if Ratio</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02265;</mml:mo><mml:mn>1.5</mml:mn><mml:mo>,</mml:mo><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>for consideration</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if Ratio</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x0003c;</mml:mo><mml:mn>1.5</mml:mn><mml:mo>,</mml:mo><mml:mtext>remove target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>from consideration</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equg.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equh"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\notin \mathrm{T}\cap \mathrm{G}}\ \mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{for}\ \mathrm{consideration} $$\end{document}</tex-math><mml:math id="M16" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02209;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>for consideration</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equh.gif" position="anchor"/></alternatives></disp-formula></p></sec><sec id="Sec20"><title>Exome-seq integration</title><p id="Par84">We use exome sequencing data to identify targets likely important in the biological function of tumor cells. We assume that genetic variants may explain the behavior of compounds inhibiting the mutated/altered targets. Depending on the available evidence for mutations and variations, targets are incorporated into the model search or final PTIM model via the published model development algorithms.</p><p id="Par85">Formally, exome-seq data is integrated as below:<disp-formula id="Equi"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{T}:= \mathrm{targets}\ \mathrm{in}\mathrm{hibited}\ \mathrm{in}\ \mathrm{drug}\ \mathrm{screen} $$\end{document}</tex-math><mml:math id="M18" display="block"><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets inhibited in drug screen</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equi.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equj"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{G}:= \mathrm{targets}\ \mathrm{with}\ \mathrm{RNA}-\mathrm{seq}\ \mathrm{expression}\ \mathrm{in}\ \mathrm{tumor}\ \mathrm{and}\ \mathrm{normal}\ \mathrm{cells} $$\end{document}</tex-math><mml:math id="M20" display="block"><mml:mi mathvariant="normal">G</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets with</mml:mtext><mml:mspace width="0.25em"/><mml:mi>RNA</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>seq</mml:mi><mml:mspace width="0.25em"/><mml:mtext>expression in tumor and normal cells</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equj.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equk"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{Mut}\left(\mathrm{x}\right):= \mathrm{mutation}/\mathrm{indel}\ \mathrm{status}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x}\ \left(\mathrm{low}/\mathrm{med}/\mathrm{high}\ \mathrm{impact}\ \mathrm{mutation}/\mathrm{indel}\right) $$\end{document}</tex-math><mml:math id="M22" display="block"><mml:mi>Mut</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>mutation</mml:mtext><mml:mo>/</mml:mo><mml:mtext>indel status of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mfenced close=")" open="("><mml:mrow><mml:mi>low</mml:mi><mml:mo>/</mml:mo><mml:mi>med</mml:mi><mml:mo>/</mml:mo><mml:mtext>high impact mutation</mml:mtext><mml:mo>/</mml:mo><mml:mtext>indel</mml:mtext></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equk.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equl"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{CNV}\left(\mathrm{x}\right):= \mathrm{copy}\ \mathrm{number}\ \mathrm{status}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x}\ \left(\mathrm{gain}/\mathrm{loss}\right) $$\end{document}</tex-math><mml:math id="M24" display="block"><mml:mi>CNV</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>copy number status of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mfenced close=")" open="("><mml:mrow><mml:mtext>gain</mml:mtext><mml:mo>/</mml:mo><mml:mtext>loss</mml:mtext></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equl.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equm"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\in \mathrm{T}\cap \mathrm{G}}\left\{\begin{array}{c}\ \\ {}\mathrm{if}\ \mathrm{Mut}\left(\mathrm{x}\right)=\mathrm{high}\ \mathrm{AND}\ \mathrm{CNV}\left(\mathrm{x}\right)=\mathrm{gain},\mathrm{include}\ \mathrm{target}\ \mathrm{x}\ \mathrm{in}\ \mathrm{model}\ \\ {}\mathrm{if}\ \mathrm{Mul}\left(\mathrm{x}\right)=\mathrm{med}\ \mathrm{AND}\ \mathrm{CNV}\left(\mathrm{x}\right)=\mathrm{gain},\mathrm{add}\ \mathrm{target}\ \mathrm{x}\ \mathrm{to}\ \mathrm{initial}\ \mathrm{search}\ \mathrm{conditions}\ \\ {}\mathrm{if}\ \mathrm{Mut}\left(\mathrm{x}\right)=\mathrm{high},\mathrm{add}\ \mathrm{target}\ \mathrm{x}\ \mathrm{to}\ \mathrm{initial}\ \mathrm{search}\ \mathrm{conditions}\ \\ {}\mathrm{if}\ \mathrm{Mut}\left(\mathrm{x}\right)=\mathrm{med}\ \mathrm{OR}\ \mathrm{CNV}\left(\mathrm{x}\right)=\mathrm{gain},\mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{in}\ \mathrm{model}\ \mathrm{once}\ \mathrm{added}\ \\ {}\mathrm{otherwise},\mathrm{do}\ \mathrm{nothing}\ \end{array}\right. $$\end{document}</tex-math><mml:math id="M26" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if</mml:mtext><mml:mspace width="0.25em"/><mml:mi>Mut</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mtext>high AND</mml:mtext><mml:mspace width="0.25em"/><mml:mi>CNV</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mtext>gain</mml:mtext><mml:mo>,</mml:mo><mml:mtext>include target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>in model</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if</mml:mtext><mml:mspace width="0.25em"/><mml:mi>Mul</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mi>med</mml:mi><mml:mspace width="0.25em"/><mml:mtext>AND</mml:mtext><mml:mspace width="0.25em"/><mml:mi>CNV</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mtext>gain</mml:mtext><mml:mo>,</mml:mo><mml:mi>add</mml:mi><mml:mspace width="0.25em"/><mml:mtext>target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>to initial search conditions</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if</mml:mtext><mml:mspace width="0.25em"/><mml:mi>Mut</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mtext>high</mml:mtext><mml:mo>,</mml:mo><mml:mi>add</mml:mi><mml:mspace width="0.25em"/><mml:mtext>target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>to initial search conditions</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if</mml:mtext><mml:mspace width="0.25em"/><mml:mi>Mut</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mi>med</mml:mi><mml:mspace width="0.25em"/><mml:mtext>OR</mml:mtext><mml:mspace width="0.25em"/><mml:mi>CNV</mml:mi><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mtext>gain</mml:mtext><mml:mo>,</mml:mo><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>in model once added</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>otherwise</mml:mtext><mml:mo>,</mml:mo><mml:mi>do</mml:mi><mml:mspace width="0.25em"/><mml:mtext>nothing</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equm.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equn"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\notin \mathrm{T}\cap \mathrm{G}}\ \mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{for}\ \mathrm{consideration} $$\end{document}</tex-math><mml:math id="M28" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02209;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>for consideration</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equn.gif" position="anchor"/></alternatives></disp-formula></p></sec><sec id="Sec21"><title>RAPID siRNA screen integration</title><p id="Par86">RAPID screen results identify high sensitivity single target mechanisms of cancer cell growth inhibition; identified hit targets were set as &#x0201c;required&#x0201d; (forced inclusion) in the RAPID siRNA PTIM model effective as sensitive siRNA targets may explain drug sensitivity of agents inhibiting the siRNA targets. Targets not identified by RAPID screening could still have effect in multi-target combinations, and thus were retained for consideration. The RAPID target set is used for PTIM creation via the published model development algorithms.</p><p id="Par87">Formally, RAPID siRNA data is integrated as below:<disp-formula id="Equo"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{T}:= \mathrm{targets}\ \mathrm{in}\mathrm{hibited}\ \mathrm{in}\ \mathrm{drug}\ \mathrm{screen} $$\end{document}</tex-math><mml:math id="M30" display="block"><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets inhibited in drug screen</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equo.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equp"><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{G}:= \mathrm{targets}\ \mathrm{with}\ \mathrm{RAPID}\ \mathrm{siRNA}\ \mathrm{viability}\ \mathrm{data} $$\end{document}</tex-math><mml:math id="M32" display="block"><mml:mi mathvariant="normal">G</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets with RAPID siRNA viability data</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equp.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equq"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{RAPID}\left(\mathrm{x}\right):= \mathrm{cell}\ \mathrm{viability}\ \mathrm{following}\ \mathrm{siRNA}\ \mathrm{knockdown}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x} $$\end{document}</tex-math><mml:math id="M34" display="block"><mml:mtext>RAPID</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>cell viability following siRNA knockdown of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi></mml:math><graphic xlink:href="12885_2019_5681_Article_Equq.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equr"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left(\upmu, \upsigma \right):= \mathrm{mean}\ \mathrm{and}\ \mathrm{standard}\ \mathrm{deviation}\ \mathrm{of}\ \mathrm{RAPID}\ \mathrm{siRNA}\ \mathrm{dataset} $$\end{document}</tex-math><mml:math id="M36" display="block"><mml:mfenced close=")" open="(" separators=","><mml:mi mathvariant="normal">&#x003bc;</mml:mi><mml:mi mathvariant="normal">&#x003c3;</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>mean and standard deviation of RAPID siRNA dataset</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equr.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equs"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\in \mathrm{T}\cap \mathrm{G}}\ \left\{\begin{array}{c}\mathrm{if}\ \mathrm{RAPID}\left(\mathrm{x}\right)+2\upsigma &#x0003c;1.5,\mathrm{add}\ \mathrm{target}\ \mathrm{x}\ \mathrm{to}\ \mathrm{PTIM}\ \mathrm{model}\\ {}\mathrm{if}\ \mathrm{RAPID}\left(\mathrm{x}\right)+2\upsigma \ge 1.5,\mathrm{nothing}\ \end{array}\right. $$\end{document}</tex-math><mml:math id="M38" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if RAPID</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>+</mml:mo><mml:mtext>2&#x003c3;</mml:mtext><mml:mo>&#x0003c;</mml:mo><mml:mn>1.5</mml:mn><mml:mo>,</mml:mo><mml:mi>add</mml:mi><mml:mspace width="0.25em"/><mml:mtext>target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>to PTIM model</mml:mtext></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if RAPID</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>+</mml:mo><mml:mtext>2&#x003c3;</mml:mtext><mml:mo>&#x02265;</mml:mo><mml:mn>1.5</mml:mn><mml:mo>,</mml:mo><mml:mtext>nothing</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equs.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equt"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\notin \mathrm{T}\cap \mathrm{G}}\ \mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{for}\ \mathrm{consideration} $$\end{document}</tex-math><mml:math id="M40" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02209;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>for consideration</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equt.gif" position="anchor"/></alternatives></disp-formula></p></sec><sec id="Sec22"><title>Kinexus phosphoproteomics screen integration</title><p id="Par88">The phosphoproteomics screen results identify differentially phosphorylated targets and associated pathways, phosphorylation of these targets may be pushing the system towards a particular phenotype, and intervention in the form of changing phosphorylation status might result in significant changes to the system. Targets identified as overactive in tumor compared to normal are included in the target set for the PTIM model. The phosphoproteomics target set is used for PTIM creation via the published model development algorithms.<disp-formula id="Equu"><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{T}:= \mathrm{targets}\ \mathrm{in}\mathrm{hibited}\ \mathrm{in}\ \mathrm{drug}\ \mathrm{screen} $$\end{document}</tex-math><mml:math id="M42" display="block"><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets inhibited in drug screen</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equu.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equv"><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{G}:= \mathrm{targets}\ \mathrm{with}\ \mathrm{RAPID}\ \mathrm{siRNA}\ \mathrm{viability}\ \mathrm{data} $$\end{document}</tex-math><mml:math id="M44" display="block"><mml:mi mathvariant="normal">G</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets with RAPID siRNA viability data</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equv.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equw"><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\mathrm{P}}_1\left(\mathrm{x}\right):= \mathrm{z}-\mathrm{score}\ \mathrm{ratio}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x}\ \mathrm{in}\ \mathrm{U}23674\ \mathrm{replicate}\ 1\ \mathrm{vs}\ \mathrm{normal} $$\end{document}</tex-math><mml:math id="M46" display="block"><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mi mathvariant="normal">z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mtext>score ratio of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>in</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">U</mml:mi><mml:mn>23674</mml:mn><mml:mspace width="0.25em"/><mml:mtext>replicate</mml:mtext><mml:mspace width="0.25em"/><mml:mn>1</mml:mn><mml:mspace width="0.25em"/><mml:mi>vs</mml:mi><mml:mspace width="0.25em"/><mml:mtext>normal</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equw.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equx"><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\mathrm{P}}_2\left(\mathrm{x}\right):= \mathrm{z}-\mathrm{score}\ \mathrm{ratio}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x}\ \mathrm{in}\ \mathrm{U}23674\ \mathrm{replicate}\ 2\ \mathrm{vs}\ \mathrm{normal} $$\end{document}</tex-math><mml:math id="M48" display="block"><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mi mathvariant="normal">z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mtext>score ratio of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>in</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">U</mml:mi><mml:mn>23674</mml:mn><mml:mspace width="0.25em"/><mml:mtext>replicate</mml:mtext><mml:mspace width="0.25em"/><mml:mn>2</mml:mn><mml:mspace width="0.25em"/><mml:mi>vs</mml:mi><mml:mspace width="0.25em"/><mml:mtext>normal</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equx.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equy"><alternatives><tex-math id="M49">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\in \mathrm{T}\cap \mathrm{G}}\ \left\{\begin{array}{c}\mathrm{if}\ \left[\ {\mathrm{P}}_1\left(\mathrm{x}\right)\ge 1\ \mathrm{and}\ |{\mathrm{P}}_1\left(\mathrm{x}\right)-{\mathrm{P}}_2\left(\mathrm{x}\right)|\le 0.5\ \right],\mathrm{add}\ \mathrm{target}\ \mathrm{x}\ \mathrm{to}\ \mathrm{PTIM}\ \mathrm{model}\ \\ {}\mathrm{if}\ \left[\ {\mathrm{P}}_1\left(\mathrm{x}\right)\ge 1\ \mathrm{and}\ |{\mathrm{P}}_1\left(\mathrm{x}\right)-{\mathrm{P}}_2\left(\mathrm{x}\right)|&#x0003e;0.5\ \right],\mathrm{nothing}\ \end{array}\right. $$\end{document}</tex-math><mml:math id="M50" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if</mml:mtext><mml:mspace width="0.25em"/><mml:mfenced close="]" open="[" separators="||"><mml:mrow><mml:mspace width="0.25em"/><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02265;</mml:mo><mml:mn>1</mml:mn><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/></mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced></mml:mrow><mml:mrow><mml:mo>&#x02264;</mml:mo><mml:mn>0.5</mml:mn><mml:mspace width="0.25em"/></mml:mrow></mml:mfenced><mml:mo>,</mml:mo><mml:mi>add</mml:mi><mml:mspace width="0.25em"/><mml:mtext>target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>to PTIM model</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if</mml:mtext><mml:mspace width="0.25em"/><mml:mfenced close="]" open="[" separators="||"><mml:mrow><mml:mspace width="0.25em"/><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02265;</mml:mo><mml:mn>1</mml:mn><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/></mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="normal">P</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced></mml:mrow><mml:mrow><mml:mo>&#x0003e;</mml:mo><mml:mn>0.5</mml:mn><mml:mspace width="0.25em"/></mml:mrow></mml:mfenced><mml:mo>,</mml:mo><mml:mtext>nothing</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equy.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equz"><alternatives><tex-math id="M51">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\notin \mathrm{T}\cap \mathrm{G}}\ \mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{for}\ \mathrm{consideration} $$\end{document}</tex-math><mml:math id="M52" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02209;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>for consideration</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equz.gif" position="anchor"/></alternatives></disp-formula></p></sec></sec><sec id="Sec23"><title>Integrative nonlinear Boolean modeling for PCB490</title><p id="Par89">Probabilistic Target Inhibition Maps (PTIMs) were used for integrative analysis of heterogeneous PCB490 biological data [<xref ref-type="bibr" rid="CR16">16</xref>&#x02013;<xref ref-type="bibr" rid="CR18">18</xref>].</p><sec id="Sec24"><title>RNA-seq integration</title><p id="Par90">RNA sequencing data for PCB490&#x02013;5 was used to eliminate under-expressed targets from consideration for PTIM model development, reducing the potential number of models. Due to possessing only tumor tissue for PCB490, RNA sequencing was performed only on the tumor sample; targets with quantified expression above the first quantile were retained for PTIM model development. The RNA-seq target set is used for PTIM creation via the published model development algorithms.</p><p id="Par91">Formally, RNA-seq data is integrated as below:<disp-formula id="Equaa"><alternatives><tex-math id="M53">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{T}:= \mathrm{targets}\ \mathrm{in}\mathrm{hibited}\ \mathrm{in}\ \mathrm{drug}\ \mathrm{screen} $$\end{document}</tex-math><mml:math id="M54" display="block"><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets inhibited in drug screen</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equaa.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equab"><alternatives><tex-math id="M55">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{G}:= \mathrm{targets}\ \mathrm{with}\ \mathrm{RNA}-\mathrm{seq}\ \mathrm{expression}\ \mathrm{in}\ \mathrm{tumor}\ \mathrm{and}\ \mathrm{normal}\ \mathrm{cells} $$\end{document}</tex-math><mml:math id="M56" display="block"><mml:mi mathvariant="normal">G</mml:mi><mml:mo>&#x02254;</mml:mo><mml:mtext>targets with</mml:mtext><mml:mspace width="0.25em"/><mml:mi>RNA</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>seq</mml:mi><mml:mspace width="0.25em"/><mml:mtext>expression in tumor and normal cells</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equab.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equac"><alternatives><tex-math id="M57">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \mathrm{Tumor}\left(\mathrm{x}\right):= \mathrm{gene}\ \mathrm{expression}\ \mathrm{of}\ \mathrm{target}\ \mathrm{x}\ \mathrm{in}\ \mathrm{tumor}\ \mathrm{sample} $$\end{document}</tex-math><mml:math id="M58" display="block"><mml:mtext>Tumor</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02254;</mml:mo><mml:mtext>gene expression of target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>in tumor sample</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equac.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equad"><alternatives><tex-math id="M59">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\mathrm{Q}}_1:= \mathrm{first}\ \mathrm{quartile}\ \mathrm{of}\ \mathrm{Tumor}\left(\ast \right)\ \mathrm{data} $$\end{document}</tex-math><mml:math id="M60" display="block"><mml:msub><mml:mi mathvariant="normal">Q</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>&#x02254;</mml:mo><mml:mtext>first quartile of Tumor</mml:mtext><mml:mfenced close=")" open="("><mml:mo>&#x02217;</mml:mo></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>data</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equad.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equae"><alternatives><tex-math id="M61">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\in \mathrm{T}\cap \mathrm{G}}\ \left\{\begin{array}{c}\mathrm{if}\ \mathrm{T}\mathrm{umor}\left(\mathrm{x}\right)\ge {\mathrm{Q}}_1,\mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{for}\ \mathrm{consideration}\ \\ {}\mathrm{if}\ \mathrm{T}\mathrm{umor}\left(\mathrm{x}\right)&#x0003c;{\mathrm{Q}}_1,\mathrm{remove}\ \mathrm{target}\ \mathrm{x}\ \mathrm{from}\ \mathrm{consideration}\ \end{array}\right. $$\end{document}</tex-math><mml:math id="M62" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if Tumor</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x02265;</mml:mo><mml:msub><mml:mi mathvariant="normal">Q</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>for consideration</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mtext>if Tumor</mml:mtext><mml:mfenced close=")" open="("><mml:mi mathvariant="normal">x</mml:mi></mml:mfenced><mml:mo>&#x0003c;</mml:mo><mml:msub><mml:mi mathvariant="normal">Q</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mtext>remove target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>from consideration</mml:mtext><mml:mspace width="0.25em"/></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12885_2019_5681_Article_Equae.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equaf"><alternatives><tex-math id="M63">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\forall}_{\mathrm{x}\notin \mathrm{T}\cap \mathrm{G}}\ \mathrm{keep}\ \mathrm{target}\ \mathrm{x}\ \mathrm{for}\ \mathrm{consideration} $$\end{document}</tex-math><mml:math id="M64" display="block"><mml:msub><mml:mo>&#x02200;</mml:mo><mml:mrow><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02209;</mml:mo><mml:mi mathvariant="normal">T</mml:mi><mml:mo>&#x02229;</mml:mo><mml:mi mathvariant="normal">G</mml:mi></mml:mrow></mml:msub><mml:mspace width="0.25em"/><mml:mtext>keep target</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="normal">x</mml:mi><mml:mspace width="0.25em"/><mml:mtext>for consideration</mml:mtext></mml:math><graphic xlink:href="12885_2019_5681_Article_Equaf.gif" position="anchor"/></alternatives></disp-formula></p></sec><sec id="Sec25"><title>PTIM Ensemble combination optimization</title><p id="Par92">To address tumor heterogeneity concerns, PTIM computational models were generated for each of the drug screened PCB490 cultures (PCB490&#x02013;3, PCB490&#x02013;4, and PCB490&#x02013;5). The PCB490&#x02013;5 PTIM model integrates RNA sequencing data as above. Combination therapy for PCB490 was designed by identifying PTIM target blocks in each of the three different cell models druggable by the same two-drug combination.</p></sec></sec><sec id="Sec26"><title>Rewiring experiments for U23674</title><p id="Par93">Untreated U23674 cells were screened using the Roche Orphan Kinome Screen and concurrently used to establish 6 additional independent cultures grown in culture media at 37&#x02009;&#x000b0;C with 5% CO2. Upon reaching 70% confluence, low dosage single agents and drug combinations (DMSO vehicle, 175&#x02009;nM OSI-906, 50&#x02009;nM GDC-0941, 175&#x02009;nM OSI-906&#x02009;+&#x02009;50&#x02009;nM GDC-0941) were added to culture plates and incubated for 72&#x02009;h (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S10). Cell plates were then washed in Phosphate Buffered Saline (PBS, Gibco, Grand Island, New York), trypsonized with Trypsin-EDTA (0.25%) (25,200,056, Thermo Fisher Scientific), and screened using the Roche Orphan Kinome Screen (Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Figure S11, Additional file <xref rid="MOESM29" ref-type="media">29</xref>: Table S15). Rewiring data was used to generate PTIM models to identify post-intervention changes to U23674 survival pathways (Additional file <xref rid="MOESM12" ref-type="media">12</xref>: Figure S12, Additional file <xref rid="MOESM27" ref-type="media">27</xref>: Table S13).</p></sec><sec id="Sec27"><title>Resistance abrogation experiments for PCB197 and S1&#x02013;12</title><p id="Par94">PCB197 PPTI screen data and S1&#x02013;12 PPTI screen data were used to generate PTIM models to identify canine and human cross-species mechanistic targets for undifferentiated pleomorphic sarcoma. Consensus targets were chosen for their appearance in human and canine PTIM models; two drugs (obatoclax, an MCL1 inhibitor and panobinostat, a pan-HDAC inhibitor) that most effectively inhibited PTIM-identified blocks at clinically achievable concentrations were selected for validation.</p><p id="Par95">Potential for resistance abrogation by targeting 2 blocks common to both human and canine PTIM models directed a six-arm proof-of-principle experiment to show that inhibition of multiple blocks inhibited could abrogate tumor cell resistance. PCB197 and S1&#x02013;12 cell cultures were seeded in quadruplicate on 6-well plates (6 plates per cell model) with 10,000 cells per well. Cells were plated 24&#x02009;h prior to incubation with any drug. The drug concentrations chosen were 1.5 times the EC<sub>50</sub> of the PTIM target of interest. The drug selection was based on desired targets, as well as requiring drug concentration for reaching 1.5 times target K<sub>d</sub> must also be less than the maximum clinically achievable concentration.</p><p id="Par96">One plate per cell model was assigned to each of the 6 treatment arms: 1) vehicle control; 2) obatoclax for 6&#x02009;days; 3) panobinostat for 6&#x02009;days; 4) obatoclax for 3&#x02009;days, wash, then panobinostat for 3&#x02009;days; 5) panobinostat for 3&#x02009;days, wash, then obatoclax for 3&#x02009;days; 6) obatoclax + panobinostat simultaneously for 6&#x02009;days. After 6&#x02009;days, culture plates were washed with PBS and fresh DMEM with 10% FBS was placed in each well. Wells were monitored until confluency was observed. The primary study endpoint was days to well confluency as determined by a single user. Cells were also counted manually with a hemocytometer and photographed to confirm consistency of the user&#x02019;s definition of confluency. If after 100&#x02009;days the cells did not reach confluency, the remaining cells are counted and the study concluded. The experimental design and results are available in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref><bold>.</bold></p></sec><sec id="Sec28"><title>Orthotopic allograft studies for U23674</title><p id="Par97">We orthotopically engrafted adult <italic>SHO (</italic>SCID/hairless/outbred) mice (Charles River, Wilmington, Massachusetts) with 10<sup>6</sup>&#x02009;U23674 cells. Engraftment was performed after injuring the right gastrocnemius muscle by cardiotoxin injection as previously described [<xref ref-type="bibr" rid="CR35">35</xref>]. Mice were assigned to treatment arms randomly without a specific assignment strategy. Treatment commenced 2&#x02009;days after engraftment; mice were treated with vehicle control (tartaric acid + TWEEN80/methylcellulose), 50&#x02009;mg/kg OSI-906, 150&#x02009;mg/kg GDC-0941, and combination 50&#x02009;mg/kg OSI-906 plus 150&#x02009;mg/kg GDC-0941. Each arm was assigned <italic>n</italic>&#x02009;=&#x02009;8 mice per arm. Sample size was selected to provide 90% power for the statistical tests. The GDC-0941 arm lost one mouse during oral gavage; the corresponding data point was censored. Dosing schedule was once daily by oral gavage up to day 5, at which time dosing was performed every other day due to weight loss on day 4. The change in dosing schedule stabilized weight loss. The endpoint considered for the study and survival analysis was tumor volume&#x02009;=&#x02009;1.4&#x02009;cc. All drug studies in mice were performed after receiving approval from the IACUC at Oregon Health and Science University. Variances between compared groups were similar per Greenwood&#x02019;s Formula. No blinding was performed during in vivo experiments. No adverse events were noted. All animal procedures were conducted in accordance with the Guidelines for the Care and Use of Laboratory Animals and were approved by the Institutional Animal Care and Use Committee at the Oregon Health &#x00026; Science University. At conclusion of the study, mice were sacrificed via isoflurane overdose followed by cervical dislocation.</p></sec><sec id="Sec29"><title>Patient derived xenograft (PDX) model testing for PCB490</title><p id="Par98">Adult female stock mice (Envigo <italic>Foxn1</italic><sup><italic>nu</italic></sup> Athymic nudes) were implanted bilaterally with approximately 5x5x5mm fragments subcutaneously in the left and right flanks with JAX PDX model of Human Epithelioid Sarcoma (J000078604 (PCB490) &#x02013; JAX-001). After the tumors reached 1&#x02013;1.5&#x02009;cm<sup>3</sup>, they were harvested and the viable tumor fragments approximately 5x5x5 mm were implanted subcutaneously in the left flank of the female study mice (Envigo <italic>Foxn1</italic><sup><italic>nu</italic></sup> Athymic nudes). Each animal was implanted with a specific passage lot and documented. J000078604 (PCB490) &#x02013; JAX-001) was P4. Tumor growth was monitored twice a week using digital calipers and the tumor volume (TV) was calculated using the formula (0.52&#x02009;&#x000d7;&#x02009;[length &#x000d7; width<sup>2</sup>]). When the TV reached approximately 150&#x02013;250&#x02009;mm<sup>3</sup> animals were matched by tumor size and assigned into control or treatment groups (3/group for J000078604 (PCB490) &#x02013; JAX-001). Dosing was initiated on Day 0. After the initiation of dosing, animals were weighed using a digital scale and TV was measured twice per week. For J000078604 (PCB490) &#x02013; JAX-001, sunitinib (reconstituted in 5% DMSO + corn oil) was administered PO QD for 21&#x02009;days at 30.0&#x02009;mg/kg/dose and BEZ235 (reconstituted in 10% N-Methyl-2-pyrrolidone [NMP]&#x02009;+&#x02009;90% polyethylene glycol 300) was administered PO QD for 21&#x02009;days at 25.0&#x02009;mg/kg/dose alone and in combination. No adverse events were noted. At conclusion of the study, mice were sacrificed via isoflurane overdose followed by cervical dislocation.</p></sec><sec id="Sec30"><title>Statistics</title><p id="Par99">Spearman correlation coefficients for Epithelioid sarcoma drug screen response data were calculated in SAS, correlating drug screen IC<sub>50</sub> values between all samples. Statistical comparison of correlation coefficients between separate groups was performed in SAS using two-tailed student&#x02019;s T-test.</p><p id="Par100">The Kaplan-Meier curves for the U23674 in vivo orthotropic allograft studies were generated and compared with logrank statistical tests. No blinding was performed. Analysis was performed by an external group of statisticians (MWG, BH, JM, SG).</p><p id="Par101"><italic>P</italic>-values for the PCB490 PDX experiment were generated using a repeated measures linear model of tumor size in terms of group, time, and the group by time interaction based on an autoregressive order 1 correlation assumption with SAS Version 9.4 for Windows (SAS Institute, Cary, NC). Analysis was performed by an external group of statisticians (MWG, BH, JM).</p></sec></sec><sec id="Sec31"><title>Results</title><sec id="Sec32"><title>Computational analysis of functional and molecular data via PTIM analysis</title><p id="Par102">The key PTIM modeling assumption is that in vitro drug sensitivity in cancer cells is driven by a small subset of key gene targets uniquely determined by the patient&#x02019;s biology, and that patient-specific drug sensitivity is most accurately predicted by multivariate modeling of autologous drug sensitivity data. The PTIM pipeline requires drug screening data from multiple (60+) monotherapy agents with quantified drug-target EC<sub>50</sub> values (Fig. <xref rid="Fig1" ref-type="fig">1</xref>, Testing Step). PTIM modeling specifically takes advantage of the promiscuity of targeted compounds by incorporating main-target and off-target EC<sub>50</sub> values during modeling. Correspondingly, PTIM models will better represent the underlying biology of individual cancer samples when complete drug-target interaction EC<sub>50</sub> information is available. Integration of additional patient-specific molecular data (e.g., exome-seq, RNA-seq, phosphoproteomics, siRNA-mediated gene knockdown, Fig. <xref rid="Fig1" ref-type="fig">1</xref>, Testing Step) identifies targets of interest to further refine target selection during model creation.</p><p id="Par103">Drug sensitivity data and secondary molecular data are provided as inputs to the PTIM computational framework [<xref ref-type="bibr" rid="CR14">14</xref>&#x02013;<xref ref-type="bibr" rid="CR19">19</xref>], which provides as output a mathematical model quantifying expected sensitivity of multi-target inhibition of the patient&#x02019;s cancer cells. The model approaches sensitivity prediction as a feature selection machine learning problem, where the &#x0201c;features&#x0201d; are the gene targets inhibited by individual drugs. The objective of the PTIM analysis approach is to find feature sets which group sensitive and insensitive drugs together into binary &#x0201c;bins&#x0201d;, representing a set of inhibited targets. A feature set where drugs in the same bin have similar sensitivity values is considered more optimal than a feature set where bins have high variance. The addition of molecular sequencing data can eliminate certain features from consideration if they are absent in the tumor (e.g. no expression of the gene per RNA-seq data) or can increase likelihood of a feature being included in the model if the feature is of high interest (e.g. highly expressed per RNA-seq, or mutated per exome-seq). The full details of integration of molecular is available in the methods section, including a detailed description of integration of molecular data to drug screening data for validation experiments presented in this manuscript.</p><p id="Par104">Multi-target sensitivity mechanisms are represented graphically as &#x0201c;tumor cell survival circuits&#x0201d; (Fig. <xref rid="Fig1" ref-type="fig">1</xref>, Modeling Step) where target combinations are denoted as &#x0201c;blocks&#x0201d; (e.g. Figure&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>, Modeling Step inhibitor symbols A, B, C&#x02009;+&#x02009;D). The value in the center of each PTIM block represents expected scaled sensitivity following inhibition of associated block targets. The resulting PTIM model enables combination therapy assignment via matching of targets in high-sensitivity PTIM blocks to drugs in clinical investigation or clinical use. A single block denotes monotherapy (e.g. A, B) or combination therapy (synergistic targets, e.g. C&#x02009;+&#x02009;D), while multiple blocks represent independent treatments which can be leveraged to abrogate cancer cell resistance.</p><p id="Par105">If PTIM models from spatially-distinct tumor sites are available, consensus therapy can be selected from distinct models to mitigate potential intra-tumor heterogeneity. When available, additional patient tumor tissue can be used to validate PTIM-predicted combination therapy in vitro or in vivo (Fig. <xref rid="Fig1" ref-type="fig">1</xref>, Validation Step). PTIM modeling is the foundation of our personalized therapy pipeline built with the goal to address the unmet clinical needs of the 600,000 patients dying from cancer every year [<xref ref-type="bibr" rid="CR1">1</xref>].</p><p id="Par106">The MATLAB package to generate basic PTIM models was published in conjunction with a previous publication [<xref ref-type="bibr" rid="CR16">16</xref>] and is available online (<ext-link ext-link-type="uri" xlink:href="http://www.myweb.ttu.edu/rpal/Softwares/PTIM1.zip">http://www.myweb.ttu.edu/rpal/Softwares/PTIM1.zip</ext-link>).</p></sec><sec id="Sec33"><title>Proof-of-concept of synergy prediction by PTIM modeling</title><sec id="Sec34"><title>Chemical screening, biological interrogation, and PTIM modeling of a genetically engineered mouse model (GEMM)-origin aRMS</title><p id="Par107">For our 2-drug synergy proof-of-concept study, we used a low passage primary tumor cell culture of a GEMM-origin aRMS tumor designated U23674 [<xref ref-type="bibr" rid="CR36">36</xref>] as a pilot study of the PTIM personalized therapy pipeline. From our previous work [<xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR37">37</xref>] we reasoned that kinases would be fundamental to the biology of aRMS, thus we interrogated U23674 drug sensitivity via three kinase inhibitor compound libraries: the GlaxoSmithKline (GSK) Open Science Orphan Kinome Library (GSK screen), the Roche Orphan Kinome Screen Library (Roche screen), and a custom Pediatric Preclinical Testing Initiative Drug Screen Version 2.1 (PPTI screen).</p><p id="Par108">The GSK screen [<xref ref-type="bibr" rid="CR38">38</xref>] consists of 305 compounds with experimentally quantified drug-target interaction EC<sub>50</sub> values. Of the 305 screened compounds, 40 (13%) caused at least 50% cell growth inhibition at or below maximum tested in vitro dosage in U23674, hereafter defined as a compound &#x0201c;hit&#x0201d; (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S1 and Additional files <xref rid="MOESM15" ref-type="media">15</xref> and <xref rid="MOESM16" ref-type="media">16</xref>: Tables S1 and S2). The Roche screen consists of 223 novel kinase inhibitor compounds, most with quantified drug-target interactions; 21 of 223 compounds (9.4%) were hits on U23674 (Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Figure S2 and Additional files <xref rid="MOESM17" ref-type="media">17</xref>, <xref rid="MOESM18" ref-type="media">18</xref> and <xref rid="MOESM19" ref-type="media">19</xref>: Tables S3, S4 and S5). The PPTI screen consists of 60 preclinical- or clinical-stage targeted agents; 28 of 60 compounds (46.7%) were hits on U23674 (Additional file <xref rid="MOESM3" ref-type="media">3</xref>: Figure S3 and Additional files <xref rid="MOESM20" ref-type="media">20</xref> and <xref rid="MOESM21" ref-type="media">21</xref>: Tables S6 and S7).</p><p id="Par109">Additionally, U23674 primary tissue was sequenced to enhance therapy design (tumor whole exome sequencing, matched normal whole exome sequencing, and whole transcriptome sequencing, Additional files <xref rid="MOESM22" ref-type="media">22</xref> and <xref rid="MOESM23" ref-type="media">23</xref>: Tables S8 and S9). Exome sequencing of U23674 did not identify any druggable targets both mutated and amplified (Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S4 and Additional files <xref rid="MOESM22" ref-type="media">22</xref> and <xref rid="MOESM23" ref-type="media">23</xref>: Tables S8 and S9); six genes possessed activating mutations (<italic>Fat4, Gm156, Mtmr14, Pcdhb8, Trpm7, Ttn, Zfp58</italic>) and one gene possessed a high-impact frameshift indel (<italic>Ppp2r5a</italic>); none of these seven gene targets are druggable. No gene with a mutation or indel is druggable. Four druggable gene targets show evidence of copy number gain (<italic>Gsk3a, Epha7, Psmb8, Tlk2</italic>). <italic>Gsk3a</italic>, <italic>Psmb8</italic>, and <italic>Tlk2</italic> all show neutral expression or underexpression by RNA-seq. <italic>Gsk3a</italic> inhibitors were effective in 12 of 72 inhibitors (16.667%) across three screens, suggesting Gsk3a is not critical for cancer cell survival in U23674. <italic>Psmb8</italic> inhibition showed in vitro efficacy in nearly all tested cell cultures across multiple tumor types (unpublished internal data) and, along with lack of overexpression, was thus treated as an in vitro screening artifact; furthermore, clinical response of solid tumors to proteasome inhibitors has been limited [<xref ref-type="bibr" rid="CR39">39</xref>]. <italic>Tlk2</italic> has no published inhibitor compounds. While overexpressed, the <italic>Epha7</italic> inhibitor on the PPTI drug screen was ineffective against U23674. Therapy assignment via exome sequencing alone would thus have limited clinical utility for U23674.</p></sec><sec id="Sec35"><title>Probabilistic target inhibition map (PTIM) modeling identifies 2-drug combinations with synergy in vitro</title><p id="Par110">The high average level of target coverage (24 compounds/target), the inclusion of both typical and atypical kinase target combinations, and the thorough characterization of drug-target interactions made the GSK screen the most complete dataset available and was thus selected to guide in vitro and in vivo validation experiments. Baseline (chemical screen data only), RNA-seq informed-, exome-seq informed, siRNA interference informed-, and phosphoproteomics informed-PTIM models were generated from the GSK screen data (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a-c<bold>,</bold> Additional file <xref rid="MOESM5" ref-type="media">5</xref>: Figure S5, Additional files <xref rid="MOESM24" ref-type="media">24</xref>, <xref rid="MOESM25" ref-type="media">25</xref>, <xref rid="MOESM26" ref-type="media">26</xref>, <xref rid="MOESM27" ref-type="media">27</xref>: Tables S10&#x02013;S13). PTIM-identified targets were consistent with known targets of interest in aRMS [<xref ref-type="bibr" rid="CR40">40</xref>, <xref ref-type="bibr" rid="CR41">41</xref>] and identified gene targets involved in established protein-protein interactions [<xref ref-type="bibr" rid="CR42">42</xref>] (Additional file <xref rid="MOESM6" ref-type="media">6</xref>: Figure S6). As multi-drug combinations impart toxicity concerns and dosing limitations, we focus on PTIM blocks (combinations of two or more targets) treatable by at most two drugs. Baseline and genomics-informed PTIM models were also generated for the PPTI and Roche screens (Additional file <xref rid="MOESM7" ref-type="media">7</xref>: Figure S7, Additional file <xref rid="MOESM27" ref-type="media">27</xref>: Table S13), however no validation experiments based on PPTI or Roche PTIM models were performed due to focus on the GSK screen results.<fig id="Fig2"><label>Fig. 2</label><caption><p>Probabilistic Target Inhibition Maps (PTIMs) and experimental in vitro and in vivo results for U23674 alveolar rhabdomyosarcoma (aRMS) drug combinations<bold>.</bold> Targets with adjacent asterisks indicate targets selected for in vitro validation. Values in the center of PTIM blocks represent expected scaled sensitivity following inhibition of associated block targets. <bold>a</bold> Abbreviated baseline chemical screen-informed PTIM. <bold>b</bold> Abbreviated chemical screen RNA-seq&#x02009;+&#x02009;informed PTIM. <bold>c</bold> Abbreviated chemical screen + exome-seq informed PTIM. The values within the target blocks indicate scaled drug sensitivity for the given target combination [<xref ref-type="bibr" rid="CR16">16</xref>] when the targets are inhibited via one or more chemical compounds. More information can be found in prior publications [<xref ref-type="bibr" rid="CR16">16</xref>, <xref ref-type="bibr" rid="CR18">18</xref>]. In (<bold>d-e</bold>), results are based on <italic>n</italic>&#x02009;=&#x02009;3 technical replicates with <italic>n</italic>&#x02009;=&#x02009;4 replicates per treatment condition. <bold>d</bold> Dose response curve for OSI-906 varied dosage + GDC-0941 fixed dosage. The response for GDC-0941 at varied dosages is included. <bold>e</bold> Dose response curve for GDC-0941 varied dosage + OSI-906 fixed dosage. The response for OSI-906 at varied dosages is included. <bold>f</bold> Schematic representation of in vivo experiment design. <bold>g</bold> Kaplan-Meier survival curves for in vivo orthotropic mouse experiment. Mice were treated with vehicle (<italic>n</italic>&#x02009;=&#x02009;8 mice, black line), 50&#x02009;mg/kg OSI-906 (<italic>n</italic>&#x02009;=&#x02009;8 mice, blue line), 150&#x02009;mg/kg GDC-0941 (<italic>n</italic>&#x02009;=&#x02009;7 mice, red line), or combination 50&#x02009;mg/kg OSI-906&#x02009;+&#x02009;150&#x02009;mg/kg GDC-0941 (<italic>n</italic>&#x02009;=&#x02009;8 mice, purple line). The medicine bottle image is public domain, provided by user Kim via <ext-link ext-link-type="uri" xlink:href="http://clker.com">clker.com</ext-link> (<ext-link ext-link-type="uri" xlink:href="http://www.clker.com/clipart-blank-pill-bottle-3.html">http://www.clker.com/clipart-blank-pill-bottle-3.html</ext-link>)</p></caption><graphic xlink:href="12885_2019_5681_Fig2_HTML" id="MO3"/></fig></p><p id="Par111">We selected two combinations for in vitro synergy validation: 1) the RNA-seq-informed target combination <italic>Igf1r</italic> &#x00026; <italic>Pik3ca</italic> (Fig. <xref rid="Fig2" ref-type="fig">2</xref>b) with combination therapy OSI-906&#x02009;+&#x02009;GDC-0941 (a Pik3ca inhibitor selective against Akt/mTOR), and 2) The baseline target combination <italic>Igf1r</italic> &#x00026; <italic>Insr</italic> &#x00026; <italic>Pka</italic> with combination therapy OSI-906 (an Igf1r and Insr inhibitor)&#x02009;+&#x02009;SB-772077-B (Pka inhibitor, denoted GSK-PKA in figures). All compounds were selected based solely on selectivity of interaction with the PTIM-identified targets.</p><p id="Par112">We selected the RNA-seq-informed drug combination due to high block sensitivity, targetability by a two-drug combination, and our previous work showing higher correlation between transcriptome status and drug sensitivity [<xref ref-type="bibr" rid="CR14">14</xref>]. The baseline combination was selected due to targetability by a two-drug combination, higher score compared to other two-drug options, and to serve as a comparison between baseline PTIM models and molecularly-informed models. In vitro validation experiments for OSI-906&#x02009;+&#x02009;GDC-0941 (Fig. <xref rid="Fig2" ref-type="fig">2</xref>d-e) demonstrated synergy as determined by non-constant ratio Combination Index [<xref ref-type="bibr" rid="CR43">43</xref>] (CI) values (Additional file <xref rid="MOESM28" ref-type="media">28</xref>: Table S14). Low-dose combination experiments were also performed to confirm PTIM-predicted drug mechanism of action (Additional file <xref rid="MOESM8" ref-type="media">8</xref>: Figure S8, Additional file <xref rid="MOESM28" ref-type="media">28</xref>: Table S14). Both full-dose and low-dose OSI-906&#x02009;+&#x02009;SB-772077-B in vitro validation experiments (Additional file <xref rid="MOESM9" ref-type="media">9</xref>: Figure S9) demonstrated non-constant ratio Combination Index synergy (Additional file <xref rid="MOESM28" ref-type="media">28</xref>: Table S14), though overall cell viability of was OSI-906&#x02009;+&#x02009;SB-772077-B than higher the RNA-seq-informed combination. In vitro results support the potential of baseline and molecularly-informed PTIM modeling to discover synergistic target combinations, though inclusion of molecular data may narrow focus on targets which are overexpressed and/or aberrant and thus more likely to respond to drug treatment.</p></sec><sec id="Sec36"><title>Tumor cell rewiring following synergy-focused combination therapy</title><p id="Par113">To explore tumor rewiring (activation of secondary signaling pathways to improve chance of survival) following synergy-focused intervention, we treated U23674 cell populations with low-dose monotherapy or combination therapies defined in initial in vitro validation experiments, and subsequently screened the populations via the Roche screen (Additional files <xref rid="MOESM10" ref-type="media">10</xref> and <xref rid="MOESM11" ref-type="media">11</xref>: Figures S10 and S11 and Additional file <xref rid="MOESM29" ref-type="media">29</xref>: Table S15). Unsurprisingly, the cell populations showed evidence of rewiring within hours of monotherapy or combination therapy intervention (Additional file <xref rid="MOESM12" ref-type="media">12</xref>: Figure S12, Additional files <xref rid="MOESM27" ref-type="media">27</xref> and <xref rid="MOESM28" ref-type="media">28</xref>: Tables S13 and S14), emphasizing the importance of simultaneous, multi-pathway drug combinations at full therapeutic doses. While PTIM modeling currently focuses on 2-drug combinations to minimize toxicity concerns, PTIM-predicted combinations of three or more drugs are possible with sufficient evidence of safety and efficacy.</p></sec><sec id="Sec37"><title>Probabilistic target inhibition map (PTIM) modeling predicts 2-drug combination with in vivo efficacy</title><p id="Par115">Having demonstrated in vitro synergy, we next validated OSI-906&#x02009;+&#x02009;GDC-0941 in vivo. We designed a four-arm orthotopic allograft study (Fig. <xref rid="Fig2" ref-type="fig">2</xref>f) comparing vehicle, OSI-906 (50&#x02009;mg/kg), GDC-0941 (150&#x02009;mg/kg), and OSI-906 (50&#x02009;mg/kg)&#x02009;+&#x02009;GDC-0941 (150&#x02009;mg/kg). Kaplan-Meier survival analysis (Fig. <xref rid="Fig2" ref-type="fig">2</xref>g) showed improvement in mouse lifespan from combination treatment (under Bonferroni correction: Vehicle &#x02013; Combo, <italic>p</italic>&#x02009;=&#x02009;0.005, OSI-906 &#x02013; Combo, <italic>p</italic>&#x02009;=&#x02009;0.014, GDC-0941 &#x02013; Combo, <italic>p</italic>&#x02009;=&#x02009;0.079. In all cases, <italic>p</italic>&#x02009;&#x0003c;&#x02009;0.05 uncorrected). Survival of mice treated with either OSI-906 or GDC-0941 alone was indistinguishable from treatment by vehicle (<italic>p</italic>&#x02009;&#x0003e;&#x02009;0.5, both corrected and uncorrected). Since a PTIM block represents targets which are weak independently but synergistic together, U23674 in vivo data supports the hypothesis underlying our modeling approach: synergistic combination targets can be identified through computational modeling of monotherapy chemical agents.</p></sec></sec><sec id="Sec38"><title>Proof-of-concept of heterogeneity-consensus 2-drug combinations predicted by PTIM modeling</title><sec id="Sec39"><title>Development of heterogeneous cell models of Epithelioid Sarcoma (EPS)</title><p id="Par116">EPS is a soft tissue sarcoma of children and adults for which chemotherapy and radiation provides little improvement in survival [<xref ref-type="bibr" rid="CR44">44</xref>]. Effective options beyond wide surgical excision are presently undefined [<xref ref-type="bibr" rid="CR45">45</xref>], making EPS a viable test case for developing targeted personalized therapies.</p><p id="Par117">We have developed several new heterogeneous EPS preclinical resources: three new unpublished cell cultures, as well as (to our knowledge) the first reported patient-derived xenograft (PDX) model of EPS derived from a 22-year-old female with a large proximal (shoulder) EPS tumor (Fig. <xref rid="Fig3" ref-type="fig">3</xref>a). The tumor sample was obtained from surgical resection and was assigned the internal identifier PCB490. Due to the size of the acquired tumor sample and the potential for heterogeneity in solid tumors [<xref ref-type="bibr" rid="CR46">46</xref>], we divided the ~3&#x02009;cm<sup>2</sup> resected tumor mass into five spatially-distinct regions (designated PCB490&#x02013;1 through PCB490&#x02013;5) and cultured each region to develop heterogeneous cell models (Fig. <xref rid="Fig3" ref-type="fig">3</xref>a). PCB490 cultures were maintained at low passage to minimize biological drift from the original patient samples. To confirm EPS diagnosis, three of five tumor sites (1, 2, and 5) were validated by western blot for INI1 protein, which is absent in 93% of EPS samples (Fig. <xref rid="Fig3" ref-type="fig">3</xref>b) [<xref ref-type="bibr" rid="CR44">44</xref>] as well as in published cell lines [<xref ref-type="bibr" rid="CR21">21</xref>, <xref ref-type="bibr" rid="CR22">22</xref>]. Multiple sites were submitted to The Jackson Laboratory for establishment of PDX models; PCB490&#x02013;5 PDX developed a passageable tumor that matched the original PCB490&#x02013;5 sample by both histology and INI1 immunohistochemical staining (Fig. <xref rid="Fig3" ref-type="fig">3</xref>c-f).<fig id="Fig3"><label>Fig. 3</label><caption><p>New cell cultures and patient-derived xenograft model of EPS with chemical space characterization<bold>. a</bold> PCB490 biopsy sample divided into distinct regions to create different primary tumor cell cultures for study. <bold>b</bold> Western blot demonstrating loss of INI1 in multiple primary tumor sites and in published EPS cell lines. <bold>c</bold> Histology of surgical biopsy of PCB490. <bold>d</bold> Immunohistochemical staining of PCB490 for INI1 shows absence in tumor cells (black arrow) but presence in co-mingled non-cancerous cells. <bold>e</bold> Histology of PCB490 patient-derived xenograft. <bold>f</bold> INI1 absence (black arrow) in immunohistochemical staining of PCB490 patient-derived xenograft. <bold>g</bold> Drug Screen V3 results from primary EPS cell cultures, published EPS cell lines, and a normal myoblast cell line. The heat values indicate drug sensitivity as IC50 values, scaled between 10&#x02009;nM (red) and 10&#x02009;&#x003bc;M (white, representing no IC<sub>50</sub> achieved) <bold>h</bold> Heatmap of Pearson correlation coefficients of 60-agent drug screen results between a normal myoblast cell line (SkMC), three EPS cell lines (ESX, VA-ES-BJ, FU-EPS-1), three sites from PCB490 (PCB490&#x02013;3, PCB490&#x02013;4, PCB490&#x02013;5), and an additional EPS patient-derived culture (PCB495). The heat values correspond to Pearson correlation coefficients between drug sensitivities of different cell models</p></caption><graphic xlink:href="12885_2019_5681_Fig3_HTML" id="MO4"/></fig></p></sec><sec id="Sec40"><title>Drug screening, sequencing, and comparison of heterogeneous EPS cell cultures</title><p id="Par118">Cell cultures PCB490&#x02013;3, PCB490&#x02013;4, and PCB490&#x02013;5 grew to sufficient populations (minimum 3&#x02009;&#x000d7;&#x02009;10<sup>6</sup> cells) at low passage (passage 2 or below) to allow for drug screening via the investigator-selected 60-agent screen denoted Drug Screen V3 (Fig. <xref rid="Fig3" ref-type="fig">3</xref>g, Additional file <xref rid="MOESM30" ref-type="media">30</xref>: Table S16) and the previously described Roche screen (Additional file <xref rid="MOESM31" ref-type="media">31</xref>: Table S17). Drug screen endpoints were per-drug IC<sub>50</sub> values.</p><p id="Par119">PCB490 primary tissue was sequenced for tumor whole exome sequencing, matched normal whole exome sequencing, and whole transcriptome sequencing (Additional file <xref rid="MOESM13" ref-type="media">13</xref>: Figure S13, Additional files <xref rid="MOESM32" ref-type="media">32</xref> and <xref rid="MOESM33" ref-type="media">33</xref>: Tables S18 and S19). Sequencing identified germline and tumor amplified, expressed, high-impact druggable variants in two genes (<italic>ABL1</italic>, <italic>NOTCH1</italic>) and expressed, medium impact variants in three additional genes (<italic>MDM4</italic>, <italic>PAK4</italic>, <italic>MAP4K5</italic>). All five variants were identified in both tumor and normal (germline) samples. The <italic>ABL1</italic> variant was previously identified in the 1000 Genomes Project [<xref ref-type="bibr" rid="CR47">47</xref>]. The <italic>ABL1</italic>, <italic>NOTCH1</italic>, <italic>MDM4</italic> and <italic>PAK4</italic> variants were previously submitted to the dbSNP database [<xref ref-type="bibr" rid="CR48">48</xref>]. All variants are of unknown clinical significance (Additional file <xref rid="MOESM34" ref-type="media">34</xref>: Table S20) [<xref ref-type="bibr" rid="CR48">48</xref>, <xref ref-type="bibr" rid="CR49">49</xref>]. PCB490 drug screening results revealed no pathway-specific drug sensitivity of mutated genes (Additional file <xref rid="MOESM14" ref-type="media">14</xref>: Figure S14) suggesting therapy assignment via exome sequencing alone would likely have limited clinical utility for PCB490.</p><p id="Par120">To compare drug sensitivity of PCB490 with other EPS models, three cell lines (ESX, FU-EPS-1, and VA-ES-BJ), a second human-derived cell culture (PCB495), and the SkMc skeletal myoblast cell line were assayed with Drug Screen V3 (Fig. <xref rid="Fig3" ref-type="fig">3</xref>g, Additional file <xref rid="MOESM35" ref-type="media">35</xref>: Table S21). Drug Screen V3 responses were compared by calculating Spearman correlation coefficients (Fig. <xref rid="Fig3" ref-type="fig">3</xref>h) to quantify the similarity between the new EPS models and existing EPS cell models. For the purpose of this analysis, we treat the PCB490 cultures from different regions as independent samples. Correlation within primary cell cultures (PCB490 sites and PCB495) was significantly higher than correlation between primary cultures and cell lines (&#x003bc;&#x02009;=&#x02009;0.6466 vs. &#x003bc;&#x02009;=&#x02009;0.4708, <italic>p</italic>&#x02009;&#x0003c;&#x02009;0.01), suggesting EPS primary cultures may be biologically distinct from EPS cell lines. PCB490 drug screen response differed between sample locations millimeters away from each other, reflective of biological differences arising from spatial tumor heterogeneity. Nonetheless, correlation between chemical screen results from PCB490 cultures was significantly higher than correlation between PCB490 cultures and PCB495 cultures/EPS cell lines (&#x003bc;&#x02009;=&#x02009;0.7671 vs. &#x003bc;&#x02009;=&#x02009;0.4601, p&#x02009;&#x0003c;&#x02009;0.001), suggesting that treatments for PCB490 may be better defined solely by PCB490 biological data.</p></sec><sec id="Sec41"><title>PTIM modeling guides heterogeneity-consensus in vivo drug combination</title><p id="Par121">Highly correlated yet heterogeneous PCB490 drug sensitivity data guided us towards PTIM modeling to design a heterogeneity-consensus personalized drug combination therapy for PCB490. PTIM models of PCB490&#x02013;3 (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, Additional file <xref rid="MOESM27" ref-type="media">27</xref>: Table S13), PCB490&#x02013;4 (Fig. <xref rid="Fig4" ref-type="fig">4</xref>b, Additional file <xref rid="MOESM27" ref-type="media">27</xref>: Table S13), and PCB490&#x02013;5 with integrated RNA-seq data (Fig. <xref rid="Fig4" ref-type="fig">4</xref>c, Additional file 27: Table S13) indicated common efficacious mechanisms across the heterogeneous tumor sites: epigenetic modifiers (HDAC, EHMT), PI3K/mTOR inhibition, and VEGF (KDR) signaling inhibition. We focused on high-scoring PTIM blocks treatable by a two-drug combination, resulting in selection of BEZ235 (PI3K/mTOR inhibitor) and sunitinib (poly-kinase inhibitor, including KDR and AXL). BEZ235&#x02009;+&#x02009;sunitinib was selected solely based on PTIM modeling data, agnostic to previous use of sunitinib in EPS [<xref ref-type="bibr" rid="CR50">50</xref>].<fig id="Fig4"><label>Fig. 4</label><caption><p>Probabilistic Target Inhibition Maps (PTIMs) of Drug Screen V3 and Roche screen results for spatially-distinct epithelioid sarcoma tumor regions<bold>.</bold> Values in the center of PTIM blocks represent expected scaled sensitivity following inhibition of associated block targets. <bold>a-c</bold> PTIMs informed by Roche Orphan Kinase Library and V3 screens. Targets of sunitinib are highlighted red, targets of BEZ235 are highlighted blue. <bold>a</bold> Abbreviated PTIM for PCB490&#x02013;3. <bold>b</bold> Abbreviated PTIM for PCB490&#x02013;4. <bold>c</bold> Abbreviated PTIM from PCB490&#x02013;5 with integrated RNA-seq data. <bold>d</bold> Results from PCB490&#x02013;5 patient-derived xenograft in vivo validation studies presented as group-wide tumor volumes following vehicle treatment (<italic>n</italic>&#x02009;=&#x02009;3 mice, green line), treatment by 30.0&#x02009;mg/kg sunitinib (<italic>n</italic>&#x02009;=&#x02009;3 mice, red line), treatment by 25.0&#x02009;mg/kg BEZ235 (<italic>n</italic>&#x02009;=&#x02009;3 mice, blue line), and treatment by 25.0&#x02009;mg/kg BEZ235&#x02009;+&#x02009;30.0&#x02009;mg/kg sunitinib (<italic>n</italic>&#x02009;=&#x02009;3 mice, purple line)</p></caption><graphic xlink:href="12885_2019_5681_Fig4_HTML" id="MO5"/></fig></p><p id="Par122">To replicate potential clinical conditions for personalized combination therapy, we bypassed in vitro validation and directly initiated in vivo testing of BEZ235&#x02009;+&#x02009;sunitinib in the PCB490 PDX model. Though the PCB490 PDX model originates from the PCB490&#x02013;5 region, heterogeneity of PCB490 suggests the tumor section used to establish the PCB490 PDX can be considered a unique heterogeneous region. PDX testing of BEZ235&#x02009;+&#x02009;sunitinib demonstrated significant slowing of tumor growth over vehicle control (92% slower tumor growth at Day 19, <italic>p</italic>&#x02009;=&#x02009;0.01) (Fig. <xref rid="Fig4" ref-type="fig">4</xref>d). In statistical analysis restricted to treated animals at Day 19, BEZ235&#x02009;+&#x02009;sunitinib significantly slowed PDX tumor growth compared to both BEZ235 (p&#x02009;=&#x02009;0.01) and sunitinib (p&#x02009;=&#x02009;0.01) alone (Fig. <xref rid="Fig4" ref-type="fig">4</xref>d).</p></sec></sec><sec id="Sec42"><title>Proof-of-concept of resistance-abrogating 2-drug combinations predicted by PTIM modeling</title><sec id="Sec43"><title>PTIM modeling of undifferentiated pleomorphic sarcoma (UPS) samples guides cross-species resistance-abrogating drug combination in vitro</title><p id="Par123">The previously discussed U23674 rewiring experiment emphasized the need for multi-pathway targeting when developing personalized treatments. The PTIM modeling approach identifies mechanisms driving in vitro drug sensitivity by identifying effective target combination &#x0201c;blocks&#x0201d;; two blocks operating on different biological pathways represent two independent treatment mechanisms. We reasoned that two-block inhibition could result in resistance-abrogating combination treatments, thus we validate a drug combination designed from two PTIM blocks representing independent biological pathways. PTIM modeling of PPTI screen data from a UPS derived from a 75-year-old man (PCB197, Fig. <xref rid="Fig5" ref-type="fig">5</xref>a, Additional file <xref rid="MOESM35" ref-type="media">36</xref>: Tables S22) and a canine-origin UPS (S1&#x02013;12, Fig. <xref rid="Fig5" ref-type="fig">5</xref>b, Additional file <xref rid="MOESM36" ref-type="media">36</xref>: Table S22) identified species-consensus drug sensitivity mechanisms targetable by a 2-block, 2-drug combination (Fig. <xref rid="Fig5" ref-type="fig">5</xref>c, d, Additional file <xref rid="MOESM27" ref-type="media">27</xref>: Table S13): panobinostat (pan-HDAC inhibitor, HDAC7 block) and obatoclax (MCL1 inhibitor). The combination of panobinostat + obatoclax was predicted to abrogate resistance mechanisms and prevent cancer cell rewiring and regrowth; furthermore, the cross-species nature of the experiment supports the resistance-abrogation effect not being model specific.<fig id="Fig5"><label>Fig. 5</label><caption><p>Undifferentiated pleomorphic sarcoma (UPS) Probabilistic Target Inhibition Map (PTIM)-guided resistance abrogation experiments. Values in the center of PTIM blocks represent expected scaled sensitivity following inhibition of associated block targets. <bold>a</bold> Histology of PCB197 human UPS sample (20x magnification). <bold>b</bold> Histology of S1&#x02013;12 canine UPS sample (20x magnification). <bold>c</bold> Abbreviated PTIM model for the pediatric preclinical testing initiative (PPTI) screen of PCB197 human UPS sample. <bold>d</bold> Abbreviated PTIM model built from the PPTI screen of S1&#x02013;12 canine UPS sample. <bold>e</bold> Schematic of experimental design for resistance abrogation experiments. <bold>f</bold> Cellular regrowth of PCB197 human UPS sample over 100&#x02009;days following treatment by single and multi-agent compounds in sequence and in combination. <bold>g</bold> Cellular regrowth of S1&#x02013;12 canine UPS sample over 100&#x02009;days following treatment by single and multi-agent compounds in sequence and in combination. Data in (<bold>f-g</bold>) is based on <italic>n</italic>&#x02009;=&#x02009;4 replicate experiments</p></caption><graphic xlink:href="12885_2019_5681_Fig5_HTML" id="MO6"/></fig></p><p id="Par124">To validate in vitro resistance abrogation across species, we performed identical six-arm in vitro trials for PCB197 and S1&#x02013;12. Each arm represented a different combination method for the cross-species combination: vehicle treatment, monotherapy treatment, serial monotherapy treatment (panobinostat then obatoclax, obatoclax then panobinostat), and simultaneous combination treatment (concurrent panobinostat + obatoclax) (Fig. <xref rid="Fig5" ref-type="fig">5</xref>e). Resistance abrogation in each arm was determined by cellular regrowth over 100&#x02009;days following treatment. Rewiring and regrowth was expected for all monotherapy and serial treatment modalities. All arms except the simultaneous combination treatment arm experienced cellular regrowth, indicating the development of resistance. In both cultures, the simultaneous combination treatment arm showed no cellular regrowth over 100&#x02009;days, indicating the combination potentially addressed resistance mechanisms (Fig. <xref rid="Fig5" ref-type="fig">5</xref>f, g).</p></sec></sec></sec><sec id="Sec44"><title>Discussion</title><p id="Par125">The work presented here represents validation experiments for three aspects of PTIM-guided personalized cancer therapy design: drug sensitivity and synergy prediction in a GEMM-origin aRMS, heterogeneity-consensus drug combination design and validation in the first-reported EPS PDX model, and mitigation of cancer cell resistance mechanisms in cross-species in vitro validation experiments. Our studies suggest the high value of combining functional screening data with secondary molecular data (especially RNA-seq data) in the design of personalized drug combinations as a supplement to or alternative to DNA sequencing-based therapy assignment. While increased effort is required to generate functional data, the additional information and evidence may prove useful in designing therapeutically effective personalized cancer treatments.</p><p id="Par126">Critically, the timeframe for PTIM-based combination therapy design is less than the time required for standard high-throughput sequencing experiments. The PTIM analysis pipeline can be performed in under 2&#x02009;weeks and without the explicit need for sequencing results. Currently, the time limiting step in integrative PTIM analysis is exome and RNA sequencing, for which new technology is rapidly reducing time and monetary cost. Functional drug screening in standard well plates can be performed for under $300, and CLIA-certified physical sequencing experiments are now under $500 per analyte per experiment; the cost of a complete functional and molecular analysis now represents a fraction of drug cost and may be accessible to a large population of cancer patients.</p><p id="Par127">The three PTIM-guided validation experiments serve as proofs-of-concept rather than a full clinical validation. The current study lacks the large sample size necessary to reach definite conclusions on the large-scale efficacy of PTIM-based personalized cancer therapy. Any treatment strategy, especially a personalized approach, requires a large population to draw clinically-relevant conclusions. Increasing the sample size of personalized treatments designed by the PTIM approach is required to demonstrate clinical use. To that end, the critical next stage in PTIM-based personalized therapy design will be prospective evaluation by partnering with physicians and veterinarians to pilot testing of n-of-1 personalized therapies in individual human patients and animals with spontaneous cancer. As the cost of analysis is low, the major challenges will be 1) administration of FDA-approved drugs, very likely as off-label therapy in combinations potentially not validated in Phase I trials, and 2) financial costs associated with modern targeted therapy regimens, which may currently be prohibitive for some patients.</p><p id="Par128">As drug screen results ultimately guide PTIM modeling, computational modeling of different disease types will require designing disease-specific compound screens to maximize the breadth and depth of disease-relevant multi-target interactions [<xref ref-type="bibr" rid="CR15">15</xref>]. Similarly, different types of secondary molecular data influences target selection during PTIM model construction depending on the underlying analyte or perturbation, with different secondary datasets expectedly producing different PTIM models. Selection of secondary datasets to generate for individual cases will depend on availability of tumor tissue and expected predictive utility of individual datasets. Based on widespread clinical utility and published studies, the current standard secondary datasets for PTIM modeling are exome sequencing data and RNA sequencing data [<xref ref-type="bibr" rid="CR14">14</xref>]. As high-throughput analysis of additional biological analytes becomes available through CLIA certified procedures, new datatypes will be integrated into PTIM models. In particular, recent advances in robust generation of proteomics data from patient samples [<xref ref-type="bibr" rid="CR51">51</xref>&#x02013;<xref ref-type="bibr" rid="CR53">53</xref>] may enable routine integration of proteomics data into PTIM modeling beyond the test case presented in this work.</p><p id="Par129">PTIM-based personalized cancer therapy also requires development of personalized toxicity and dosing prediction methods for designing maximally effective, minimally toxic drug combinations. Research on toxicity prediction is underway, as is research on incorporating chemotherapy backbone into drug combination predictions. While validated PTIM models are currently based on low-passage cell cultures (U23674, PCB490, S1&#x02013;12, PCB197), future application of PTIM models will use direct-to-plate tumor screening to best recapitulate the patient&#x02019;s disease state and to remove the dependence on cell culture establishment. Finally, we will pursue expansion of disease-consensus PTIM modeling [<xref ref-type="bibr" rid="CR19">19</xref>] to establish new disease-specific drug combinations based on integrated drug screening and high-throughput sequencing data.</p></sec><sec id="Sec45"><title>Conclusion</title><p id="Par130">PTIM-based personalized combination therapy has been designed to uniquely leverage patient-specific functional and biological data to address some of the critical unmet clinical needs of the 60% of cancer patients for whom tumor DNA analysis is uninformative [<xref ref-type="bibr" rid="CR2">2</xref>] and the 600,000 patients lost to cancer every year [<xref ref-type="bibr" rid="CR1">1</xref>] who have exhausted clinical options. PTIM modeling can also meet the needs of cancer patients with rare diseases, such as the spectrum of 60+ cancers known as non-rhabdomyosarcoma soft tissue sarcomas (including EPS) for which effective clinical treatments may not exist and may never be developed due to a paucity of disease models for research. These two groups represent a significant cancer patient population for which PTIM modeling may provide evidence-based treatment options where no therapeutic avenues exist.</p></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec46"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12885_2019_5681_MOESM1_ESM.tif"><label>Additional file 1:</label><caption><p>
<bold>Figure S1.</bold> Heat map of merged chemical screen, RNA-seq, siRNA, and phosphoproteomics results for GlaxoSmithKline (GSK) Orphan Kinome screen. Due to the large number of compounds and protein targets, only a limited scope of compounds and targets is shown here (for full data, see Additional file <xref rid="MOESM15" ref-type="media">15</xref>: Table S1). Bright red indicates high sensitivity values, gradating down to white meaning low sensitivity. Gray indicates no interaction or no available data. Asterisk indicates targets later validated in vivo. (TIF 38030 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12885_2019_5681_MOESM2_ESM.tif"><label>Additional file 2:</label><caption><p>
<bold>Figure S2.</bold> Heat map of merged Roche Orphan Kinome chemical screen, RNA-seq, siRNA, and phosphoproteomics results. Due to the large number of compounds and protein targets, only a limited scope of compounds and targets is shown here (For full data, see Additional file <xref rid="MOESM17" ref-type="media">17</xref>: Table S3). Bright red indicates high sensitivity values, gradating down to white meaning low sensitivity. Gray indicates no interaction or no available data. (TIF 58505 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12885_2019_5681_MOESM3_ESM.tif"><label>Additional file 3:</label><caption><p>
<bold>Figure S3.</bold> Heat map of joint version 2.1 chemical screen, RNA-seq, siRNA, and phosphoproteomics results. Due to the large number of compounds and protein targets, only a limited scope of compounds and targets is shown here (For full data, see Additional file <xref rid="MOESM21" ref-type="media">21</xref>: Table S7). Bright red indicates high sensitivity values, gradating down to white meaning low sensitivity. Gray indicates no interaction or no available data (TIF 57387 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12885_2019_5681_MOESM4_ESM.tif"><label>Additional file 4:</label><caption><p>
<bold>Figure S4.</bold> Circos plot of U23674 RNA sequencing and exome sequencing data. The outermost data circle represents log<sub>2</sub>-scaled gene expression [log<sub>2</sub>(expression+&#x02009;1), low expression (white) to high expression (red), with missing values colored black]. The middle circle represents genes with identified mutations or indels (red) or lack thereof (black). The innermost circle represents copy number variations (red is amplification, blue is deletion, black is no variation). (TIF 67243 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12885_2019_5681_MOESM5_ESM.tif"><label>Additional file 5:</label><caption><p>
<bold>Figure S5.</bold> PTIM models developed using secondary datasets. (<bold>A</bold>) Chemical screen + siRNA informed PTIM. Values in the center of PTIM blocks represent expected scaled sensitivity following inhibition of associated block targets. (<bold>B</bold>) Chemical screen + phosphoproteomics informed PTIM. The values within the target blocks indicate scaled drug sensitivity [<xref ref-type="bibr" rid="CR16">16</xref>] when block targets are inhibited. (TIF 22272 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="12885_2019_5681_MOESM6_ESM.tif"><label>Additional file 6:</label><caption><p>
<bold>Figure S6.</bold> STRINGdb visualizations of protein-protein interaction networks implicated by PTIM models. The protein-protein interaction networks here are derived from targets selected to define drug sensitivity during PTIM modeling. Edges in the STRINGdb graph represent confidence of interactions based on data from multiple published sources. Edges with confidence &#x0003e;&#x02009;0.9 are represented on the graph. The asterisk indicates targets validated in vitro<italic>.</italic> (<bold>A</bold>) Network of the set of targets common to the models developed for the GSK Orphan Kinome screen and the PPTI screen. Enrichment <italic>p</italic>-value &#x0003c;&#x02009;0.01. (<bold>B</bold>) Network of the targets identified by the GSK Orphan Kinome screen alone. Enricment p-value &#x0003c;&#x02009;0.01. (TIF 27210 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="12885_2019_5681_MOESM7_ESM.tif"><label>Additional file 7:</label><caption><p>
<bold>Figure S7.</bold> Probabilistic Target Inhibition Map (PTIM) model of U23674 Roche chemical screen hits. Values in the center of PTIM blocks represent expected scaled sensitivity following inhibition of associated block targets. (A) Base chemical screen informed PTIM. (B) RNA-seq&#x02009;+&#x02009;chemical screen informed PTIM. Roche screen hits include CDK2 inhibitors. However, no CDK inhibitor was a known inhibitor of non-CDK targets, limiting development of personalized combinations involving CDK inhibitors. (TIF 29510 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="12885_2019_5681_MOESM8_ESM.tif"><label>Additional file 8:</label><caption><p>
<bold>Figure S8.</bold> Low dose combination validation results for drug combinations GDC-0941&#x02009;+&#x02009;OSI-906. Results are based on <italic>n</italic>&#x02009;=&#x02009;3 technical replicates with <italic>n</italic>&#x02009;=&#x02009;4 replicates per treatment condition. (<bold>A</bold>) Dose response curve for OSI-906 varied dosage + GDC-0941 low fixed dosage. The response for GDC-0941 at varied dosages is included. (<bold>B</bold>) Dose response curve for GDC-0941 varied dosage + OSI-906 low fixed dosage. The response for OSI-906 at varied dosages is included. (TIF 10578 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="12885_2019_5681_MOESM9_ESM.tif"><label>Additional file 9:</label><caption><p>
<bold>Figure S9.</bold> Combination validation results for drug combinations SB-772077-B (GSK-PKA)&#x02009;+&#x02009;OSI-906. Results are based on <italic>n</italic>&#x02009;=&#x02009;3 technical replicates with <italic>n</italic>&#x02009;=&#x02009;4 replicates per treatment condition. (<bold>A</bold>) Dose response curve for OSI-906 varied dosage + SB-772077-B fixed dosage. Response for SB-772077-B at varied dosages is included. (<bold>B</bold>) Dose response curve for OSI-906 varied dosage + SB-772077-B low fixed dosage. (<bold>C</bold>) Dose response curve for SB-772077-B varied dosage + OSI-906 fixed dosage. The response for OSI-906 at varied dosages is included. (<bold>D</bold>) Dose response curve for SB-772077-B varied dosage + OSI-906 low fixed dosage. The response for OSI-906 at varied dosages is included. (TIF 67346 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM10"><media xlink:href="12885_2019_5681_MOESM10_ESM.tif"><label>Additional file 10:</label><caption><p>
<bold>Figure S10.</bold> Schematic of PTIM-informed U23674 rewiring experiment. An initial culture of U23674 is screened using the Roche screen. The same culture is used to seed 6 new cultures, which are grown until the cell population is sufficient for drug screening. Five of the 6 cultures were treated using single agents and drug combinations in low dosages (75&#x02009;nM OSI-906, 50&#x02009;nM GDC-0941) and one culture was left untreated. After treatment and incubation for 72&#x02009;h, the compounds were removed the cells were screened using the Roche Orphan Kinome screen. (TIF 8496 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM11"><media xlink:href="12885_2019_5681_MOESM11_ESM.tif"><label>Additional file 11:</label><caption><p>
<bold>Figure S11.</bold> Heat map of joint Roche Orphan Kinome chemical screen, RNA-seq, siRNA, and phosphoproteomics results from the U23674 Probabilistic Target Inhibition Map (PTIM) rewiring experiment. Due to the large number of compounds and protein targets, only a limited scope of compounds and targets is shown here (For full data, see Additional file <xref rid="MOESM28" ref-type="media">28</xref>: Table S14). Bright red indicates high sensitivity values, gradating down to white meaning low sensitivity. Gray indicates no interaction or no available data. (TIF 96004 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM12"><media xlink:href="12885_2019_5681_MOESM12_ESM.tif"><label>Additional file 12:</label><caption><p>
<bold>Figure S12.</bold> Probabilistic Target Inhibition Map (PTIM) models from U23674 experimental rewiring data. Values in the center of PTIM blocks represent expected scaled sensitivity following inhibition of associated block targets. (<bold>A</bold>) Untreated initial culture PTIM. (<bold>B</bold>) Untreated secondaryculture PTIM. (<bold>C</bold>) OSI-906-treated rewire PTIM. (<bold>D</bold>) GDC-0941-treated rewire PTIM. (<bold>E</bold>) OSI-906&#x02009;+&#x02009;GDC-0941-treated rewire PTIM. (TIF 41768 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM13"><media xlink:href="12885_2019_5681_MOESM13_ESM.tif"><label>Additional file 13:</label><caption><p>
<bold>Figure S13.</bold> Circos plot of PCB490 RNA sequencing and exome sequencing data. <italic>ABL1</italic> and <italic>NOTCH1</italic> are identified as both mutated and amplified, though both variants were also identified in the matched germline sample. The outermost data circle represents log<sub>2</sub>-scaled gene expression [log<sub>2</sub>(expression+&#x02009;1), low expression (white) to high expression (red), with missing values colored black]. The middle circle represents genes with identified mutations or indels (red) or lack thereof (black). The innermost circle represents copy number variations (red is amplification, blue is deletion, black is no variation). (TIF 69008 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM14"><media xlink:href="12885_2019_5681_MOESM14_ESM.tif"><label>Additional file 14:</label><caption><p>
<bold>Figure S14.</bold> Heat map of IC<sub>50</sub> and EC<sub>50</sub> values for Pediatric Preclinical Testing Initiative Version 3 drug screen compounds inhibiting mutated and expressed targets in PCB490. Red in the IC<sub>50</sub> and EC<sub>50</sub> tables indicates low IC<sub>50</sub> and EC<sub>50</sub> values, respectively. No single target or combination of targets showed uniform efficacy across all PCB490 cultures, suggesting variations alone or in conjunction with transcriptome sequencing would not have identified actionable therapeutic targets. Heat values in the IC<sub>50</sub> section of the table represent drug sensitivities as IC<sub>50</sub> values, between 1&#x02009;nM (red) and 6&#x02009;&#x003bc;M or above (white). Heat values in the EC<sub>50</sub> section of the table represent quantified drug-target interaction between chemical agents and gene targets, quantified as 50% inhibitory concentrations between 1&#x02009;nM (red) and 6&#x02009;&#x003bc;M or above (white), with grey representing no interaction. (TIF 13895 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM15"><media xlink:href="12885_2019_5681_MOESM15_ESM.xlsx"><label>Additional file 15:</label><caption><p>
<bold>Table S1.</bold> Merged GSK Screen Data - U23674. (XLSX 271 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM16"><media xlink:href="12885_2019_5681_MOESM16_ESM.xlsx"><label>Additional file 16:</label><caption><p>
<bold>Table S2.</bold> GSK Screen Data IC50 Data - U23674. (XLSX 15 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM17"><media xlink:href="12885_2019_5681_MOESM17_ESM.xlsx"><label>Additional file 17:</label><caption><p>
<bold>Table S3.</bold> Roche Screen Merged Data - U23674. (XLSX 88 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM18"><media xlink:href="12885_2019_5681_MOESM18_ESM.xlsx"><label>Additional file 18:</label><caption><p>
<bold>Table S4.</bold> Roche screen hit references. (XLSX 13 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM19"><media xlink:href="12885_2019_5681_MOESM19_ESM.xlsx"><label>Additional file 19:</label><caption><p>
<bold>Table S5.</bold> Roche screen IC50 data - U23674. (XLSX 9 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM20"><media xlink:href="12885_2019_5681_MOESM20_ESM.xlsx"><label>Additional file 20:</label><caption><p>
<bold>Table S6.</bold> PPTI screen IC50 data - U23674. (XLSX 10 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM21"><media xlink:href="12885_2019_5681_MOESM21_ESM.xlsx"><label>Additional file 21:</label><caption><p>
<bold>Table S7.</bold> PPTI Screen Merged data - U23674. (XLSX 130 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM22"><media xlink:href="12885_2019_5681_MOESM22_ESM.xlsx"><label>Additional file 22:</label><caption><p>
<bold>Table S8.</bold> Exome Sequencing Data - U23674. (XLSX 1792 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM23"><media xlink:href="12885_2019_5681_MOESM23_ESM.xlsx"><label>Additional file 23:</label><caption><p>
<bold>Table S9.</bold> RNA Sequencing Data - U23674. (XLSX 2157 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM24"><media xlink:href="12885_2019_5681_MOESM24_ESM.xlsx"><label>Additional file 24:</label><caption><p>
<bold>Table S10.</bold> RAPID siRNA Screen data - U23674. (XLSX 43 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM25"><media xlink:href="12885_2019_5681_MOESM25_ESM.xlsx"><label>Additional file 25:</label><caption><p>
<bold>Table S11.</bold> Rapid Screen vs Drug screen - U23674. (XLSX 10 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM26"><media xlink:href="12885_2019_5681_MOESM26_ESM.xlsx"><label>Additional file 26:</label><caption><p>
<bold>Table S12.</bold> Phospho Screen data - U23674. (XLSX 657 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM27"><media xlink:href="12885_2019_5681_MOESM27_ESM.xlsx"><label>Additional file 27:</label><caption><p>
<bold>Table S13.</bold> PTIM Map Scores. (XLSX 14 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM28"><media xlink:href="12885_2019_5681_MOESM28_ESM.xlsx"><label>Additional file 28:</label><caption><p>
<bold>Table S14.</bold> Combination Index Values - U23674. (XLSX 13 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM29"><media xlink:href="12885_2019_5681_MOESM29_ESM.xlsx"><label>Additional file 29:</label><caption><p>
<bold>Table S15.</bold> Rewiring Screening Data - U23674. (XLSX 270 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM30"><media xlink:href="12885_2019_5681_MOESM30_ESM.xlsx"><label>Additional file 30:</label><caption><p>
<bold>Table S16.</bold>&#x02009;V3 Drug Screen data - PCB490. (XLSX 10 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM31"><media xlink:href="12885_2019_5681_MOESM31_ESM.xlsx"><label>Additional file 31:</label><caption><p>
<bold>Table S17.</bold> Roche Drug Screen data - PCB490. (XLSX 14 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM32"><media xlink:href="12885_2019_5681_MOESM32_ESM.xlsx"><label>Additional file 32:</label><caption><p>
<bold>Table S18.</bold> Exome Sequencing Data - PCB490. (XLSX 345 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM33"><media xlink:href="12885_2019_5681_MOESM33_ESM.xlsx"><label>Additional file 33:</label><caption><p>
<bold>Table S19.</bold> RNA Sequencing data - PCB490. (XLSX 3314 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM34"><media xlink:href="12885_2019_5681_MOESM34_ESM.xlsx"><label>Additional file 34:</label><caption><p>
<bold>Table S20.</bold> Druggable Exome Targets - PCB490. (XLSX 11 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM35"><media xlink:href="12885_2019_5681_MOESM35_ESM.xlsx"><label>Additional file 35:</label><caption><p>
<bold>Table S21.</bold> EPS Model V3 Drug Screen Data. (XLSX 11 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM36"><media xlink:href="12885_2019_5681_MOESM36_ESM.xlsx"><label>Additional file 36:</label><caption><p>
<bold>Table S22.</bold> PPTI Drug Screening Data &#x02013; UPS. (XLSX 9 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>aRMS</term><def><p id="Par7">Alveolar rhabdomyosarcoma</p></def></def-item><def-item><term>CCLE</term><def><p id="Par8">Cancer Cell Line Encyclopedia</p></def></def-item><def-item><term>CCuRe-FAST</term><def><p id="Par9">Childhood Cancer Registry for Familial and Sporadic Tumors</p></def></def-item><def-item><term>CI</term><def><p id="Par10">Combination Index</p></def></def-item><def-item><term>DIPG</term><def><p id="Par12">Diffuse intrinsic pontine glioma</p></def></def-item><def-item><term>EGA</term><def><p id="Par11">European Genome-Phenome Archive</p></def></def-item><def-item><term>EPS</term><def><p id="Par13">Epithelioid sarcoma</p></def></def-item><def-item><term>FBS</term><def><p id="Par14">Fetal bovine serum</p></def></def-item><def-item><term>GEO</term><def><p id="Par15">Gene Expression Omnibus</p></def></def-item><def-item><term>GSK</term><def><p id="Par16">GlaxoSmithKline</p></def></def-item><def-item><term>IACUC</term><def><p id="Par17">Institutional Animal Care and Use Committee</p></def></def-item><def-item><term>IHC</term><def><p id="Par18">Immunohistochemistry</p></def></def-item><def-item><term>IRB</term><def><p id="Par19">Institutional Review Board</p></def></def-item><def-item><term>JAX</term><def><p id="Par20">The Jackson Laboratory</p></def></def-item><def-item><term>OHSU</term><def><p id="Par21">Oregon Health &#x00026; Science University</p></def></def-item><def-item><term>OSU</term><def><p id="Par22">Oregon State University</p></def></def-item><def-item><term>PBS</term><def><p id="Par23">Phosphate Buffered Saline</p></def></def-item><def-item><term>PDX</term><def><p id="Par24">Dulbecco&#x02019;s Modified Eagle&#x02019;s Medium</p></def></def-item><def-item><term>PDX</term><def><p id="Par25">Patient-derived xenograft</p></def></def-item><def-item><term>PPTI screen</term><def><p id="Par26">Pediatric Preclinical Testing Initiative Drug Screen Version 2.1</p></def></def-item><def-item><term>PTIM</term><def><p id="Par27">Probabilistic Target Inhibition Map</p></def></def-item><def-item><term>RIPA</term><def><p id="Par28">Radioimmunoprecipitation</p></def></def-item><def-item><term>SD</term><def><p id="Par29">Standard deviation</p></def></def-item><def-item><term>SHO</term><def><p id="Par30">SCID/hairless/outbred mice</p></def></def-item><def-item><term>STR</term><def><p id="Par31">Short Tandem Repeat</p></def></def-item><def-item><term>TV</term><def><p id="Par32">Tumor volume</p></def></def-item><def-item><term>UPS</term><def><p id="Par33">Undifferentiated pleomorphic sarcoma</p></def></def-item></def-list></glossary><ack><title>Acknowledgements</title><p>We thank William Zuercher and David Drewry at GlaxoSmithKline and Paul Gillespie at Roche for making the respective compound libraries available for the community and this study.</p><sec id="FPar1"><title>Funding</title><p id="Par131">This work was supported by the Scott Carter Foundation Fellowship grant (to N.E.B), the SuperSam Foundation Fellowship grant (to N.E.B), the Prayers for Elijah Foundation (to C.K. and N.E.B.), NSF award CCF0953366 (to R.P.), the Damon Runyon-Sohn &#x00026; St. Baldrick&#x02019;s Foundation training grants (to L.E.D.), the AAO-HNSF Saidee Keller Memorial Resident Research Grant, American Academy of Otolaryngology &#x02013; Head &#x00026; Neck Surgery Foundation (AAO-HNSF) and the Centralized Otolaryngology Research Effort (CORE) Study Section (to M.N.G), as well as by a gift from an anonymous donor. None of the funding bodies were involved in design or analysis of the experiments, or interpretation of the results.</p></sec><sec id="FPar2" sec-type="data-availability"><title>Availability of data and materials</title><p id="Par132">All analyzed data is available in the supplemental materials for this manuscript. RNA sequencing data is available through the Gene Expression Omnibus (GEO, GSE128766), and DNA sequencing data is available through the European Genome-Phenome Archive (EGA, EGAD00001004885).</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>NEB performed computational modeling and analysis, designed and analyzed experiments, and oversaw the study. RP oversaw computational modeling and analysis and oversaw the study. CK designed and analyzed experiments, wrote the manuscript, and oversaw the study. NEB, RR, JA, and CK helped write the manuscript. RR provided additional support for the study. KLM assisted with figure development for the manuscript. Biological resources (cell lines) for in vitro experiments were provided by JNoujaim, YCD, KT, TT, JNishio, RGM, RLJ, MMilovancev provided biological resources. EH prepared drug screen plates with which LED performed drug screening experiments on cell line models. MNG and MNS processed and cultured tumor tissue and performed drug screening experiments on primary cell cultures of fresh tumor tissue. AM performed histological analysis of tissue samples from fresh tumor tissue. MQ performed sequencing data analysis. In-house in vivo experiments were performed by JA. The Champions Oncology mouse studies were overseen by EW and MMancini. The Jackson Laboratory mouse studies were overseen by SA and CB oversaw. RGE performed histological analysis of PDX implanted mice on behalf of the Jackson Laboratory. JEM, MWG, BSH and SG performed statistical analysis and interpretation of in vivo experimental results. All authors read and approved the final manuscript.</p></notes><notes><title>Ethics approval and consent to participate</title><p id="Par133">Human cell lines used in blotting and drug screening experiments did not require ethics approval for usage.</p><p id="Par134">All animal procedures performed at Oregon Health &#x00026; Science University were conducted in accordance with the Guidelines for the Care and Use of Laboratory Animals and were approved by the Institutional Animal Care and Use Committee at the Oregon Health &#x00026; Science University.</p><p id="Par135">All animal procedures performed at The Jackson Laboratory were conducted in accordance with the Guidelines for the Care and Use of Laboratory Animals and were approved by the Institutional Animal Care and Use Committee at The Jackson Laboratory.</p><p id="Par136">All animal procedures performed at Champions Oncology were conducted in accordance with the Guidelines for the Care and Use of Laboratory Animals and were approved by the Institutional Animal Care and Use Committee at Champions Oncology.</p><p id="Par137">All human tissue samples were acquired through the Childhood Cancer Registry for Familial and Sporadic Tumors (CCuRe-FAST) tumor banking program. All patients enrolled in CCuRe-FAST provided informed consent via written consent. All aspects of the study were reviewed and approved by the Oregon Health &#x00026; Science University (OHSU) Institutional Review Board (IRB). Patient data and clinical and pathologic information are maintained in a de-identified database.</p></notes><notes><title>Consent for publication</title><p id="Par138">Not applicable.</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p id="Par139">Investigators N.E.B., C.K. and R.P. have previously filed invention disclosures for the probabilistic Boolean model that integrates chemical screening and genomics data, and are in the process of forming a related company. The &#x02018;s have declared these conflicts to their respective institutions, which are developing conflict of interest management plans.</p><p id="Par140">All other authors declare no competing interests.</p></notes><notes><title>Publisher&#x02019;s Note</title><p id="Par141">Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><mixed-citation publication-type="other">American Cancer Society. Cancer Facts &#x00026; Figures 2019. Atlanta: American Cancer Society; 2019. <ext-link ext-link-type="uri" xlink:href="https://www.cancer.org/content/dam/cancer-org/research/cancer-facts-and-statistics/annual-cancer-facts-and-figures/2019/cancer-facts-and-figures-2019.pdf">https://www.cancer.org/content/dam/cancer-org/research/cancer-facts-and-statistics/annual-cancer-facts-and-figures/2019/cancer-facts-and-figures-2019.pdf</ext-link>.</mixed-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Parsons</surname><given-names>D</given-names></name><name><surname>Roy</surname><given-names>A</given-names></name><name><surname>Yang</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Diagnostic yield of clinical tumor and germline whole-exome sequencing for children with solid tumors</article-title><source>JAMA Oncol</source><year>2016</year><volume>2</volume><fpage>616</fpage><lpage>624</lpage><pub-id pub-id-type="doi">10.1001/jamaoncol.2015.5699</pub-id><?supplied-pmid 26822237?><pub-id pub-id-type="pmid">26822237</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mody</surname><given-names>Rajen J.</given-names></name><name><surname>Prensner</surname><given-names>John R.</given-names></name><name><surname>Everett</surname><given-names>Jessica</given-names></name><name><surname>Parsons</surname><given-names>D. Williams</given-names></name><name><surname>Chinnaiyan</surname><given-names>Arul M.</given-names></name></person-group><article-title>Precision medicine in pediatric oncology: Lessons learned and next steps</article-title><source>Pediatric Blood &#x00026; Cancer</source><year>2016</year><volume>64</volume><issue>3</issue><fpage>e26288</fpage><pub-id pub-id-type="doi">10.1002/pbc.26288</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Somaiah</surname><given-names>N</given-names></name><name><surname>von Mehren</surname><given-names>M</given-names></name></person-group><article-title>New drugs and combinations for the treatment of soft-tissue sarcoma: a review</article-title><source>Cancer Manag Res</source><year>2012</year><volume>4</volume><fpage>397</fpage><lpage>411</lpage><pub-id pub-id-type="doi">10.2147/CMAR.S23257</pub-id><?supplied-pmid 23226072?><pub-id pub-id-type="pmid">23226072</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sos</surname><given-names>ML</given-names></name><etal/></person-group><article-title>Predicting drug susceptibility of non&#x02013;small cell lung cancers based on genetic lesions</article-title><source>J Clin Invest</source><year>2009</year><volume>119</volume><fpage>1727</fpage><lpage>1740</lpage><pub-id pub-id-type="doi">10.1172/JCI37127</pub-id><?supplied-pmid 19451690?><pub-id pub-id-type="pmid">19451690</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Staunton</surname><given-names>JE</given-names></name><etal/></person-group><article-title>Chemosensitivity prediction by transcriptional profiling</article-title><source>Proc Natl Acad Sci</source><year>2001</year><volume>98</volume><fpage>10787</fpage><lpage>10792</lpage><pub-id pub-id-type="doi">10.1073/pnas.191368598</pub-id><?supplied-pmid 11553813?><pub-id pub-id-type="pmid">11553813</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Garnett</surname><given-names>MJ</given-names></name><etal/></person-group><article-title>Systematic identification of genomic markers of drug sensitivity in cancer cells</article-title><source>Nature</source><year>2012</year><volume>483</volume><fpage>570</fpage><lpage>575</lpage><pub-id pub-id-type="doi">10.1038/nature11005</pub-id><pub-id pub-id-type="pmid">22460902</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barretina</surname><given-names>J</given-names></name><etal/></person-group><article-title>The Cancer cell line encyclopedia enables predictive modelling of anticancer drug sensitivity</article-title><source>Nature</source><year>2012</year><volume>483</volume><fpage>603</fpage><lpage>307</lpage><pub-id pub-id-type="doi">10.1038/nature11003</pub-id><pub-id pub-id-type="pmid">22460905</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wan</surname><given-names>Q</given-names></name><name><surname>Pal</surname><given-names>R</given-names></name></person-group><article-title>An ensemble based top performing approach for NCI-DREAM drug sensitivity prediction challenge</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><fpage>e101183</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0101183</pub-id><?supplied-pmid 24978814?><pub-id pub-id-type="pmid">24978814</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Matlock</surname><given-names>K</given-names></name><name><surname>Niz</surname><given-names>CD</given-names></name><name><surname>Rahman</surname><given-names>R</given-names></name><name><surname>Ghosh</surname><given-names>S</given-names></name><name><surname>Pal</surname><given-names>R</given-names></name></person-group><source>In Proceedings of the 8th ACM International Conference on Bioinformatics, Computational Biology,and Health Informatics</source><year>2017</year><publisher-loc>Boston</publisher-loc><publisher-name>ACM</publisher-name><fpage>772</fpage></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Costello</surname><given-names>JC</given-names></name><etal/></person-group><article-title>A community effort to assess and improve drug sensitivity prediction algorithms</article-title><source>Nat Biotech</source><year>2014</year><volume>32</volume><fpage>1202</fpage><lpage>1212</lpage><pub-id pub-id-type="doi">10.1038/nbt.2877</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eduati</surname><given-names>F</given-names></name><etal/></person-group><article-title>A microfluidics platform for combinatorial drug screening on cancer biopsies</article-title><source>Nat Commun</source><year>2018</year><volume>9</volume><fpage>2434</fpage><pub-id pub-id-type="doi">10.1038/s41467-018-04919-w</pub-id><?supplied-pmid 29934552?><pub-id pub-id-type="pmid">29934552</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Zhao</surname><given-names>B</given-names></name><name><surname>R Pritchard</surname><given-names>J</given-names></name><name><surname>Lauffenburger</surname><given-names>D</given-names></name><name><surname>Hemann</surname><given-names>M</given-names></name></person-group><source>Addressing Genetic tumor heterogeneity through Computationally Predictive Combination Therapy</source><year>2013</year></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berlow</surname><given-names>N</given-names></name><etal/></person-group><article-title>An integrated approach to anti-Cancer drug sensitivity prediction</article-title><source>Comput Biol Bioinform IEEE/ACM Trans</source><year>2014</year><volume>11</volume><fpage>995</fpage><lpage>1008</lpage><pub-id pub-id-type="doi">10.1109/TCBB.2014.2321138</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Berlow</surname><given-names>N</given-names></name><name><surname>Haider</surname><given-names>S</given-names></name><name><surname>Pal</surname><given-names>R</given-names></name><name><surname>Keller</surname><given-names>C</given-names></name></person-group><source>In Genomic Signal Processing and Statistics (GENSIPS)</source><year>2013</year></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berlow</surname><given-names>N</given-names></name><etal/></person-group><article-title>A new approach for prediction of tumor sensitivity to targeted drugs based on functional data</article-title><source>BMC bioinformatics</source><year>2013</year><volume>14</volume><fpage>239</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-14-239</pub-id><?supplied-pmid 23890326?><pub-id pub-id-type="pmid">23890326</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berlow</surname><given-names>N</given-names></name><name><surname>Davis</surname><given-names>L</given-names></name><name><surname>Keller</surname><given-names>C</given-names></name><name><surname>Pal</surname><given-names>R</given-names></name></person-group><article-title>Inference of dynamic biological networks based on responses to drug perturbations</article-title><source>EURASIP J Bioinforma Syst Biol</source><year>2014</year><volume>2014</volume><issue>1</issue><fpage>14</fpage><pub-id pub-id-type="doi">10.1186/s13637-014-0014-1</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><mixed-citation publication-type="other">Pal R, Berlow N. A kinase inhibition map approach for tumor sensitivity prediction and combination therapy design for targeted drugs. Pac Symp Biocomput. 2012:351&#x02013;62. 10.1142/9789814366496_0034.</mixed-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grasso</surname><given-names>CS</given-names></name><etal/></person-group><article-title>Functionally defined therapeutic targets in diffuse intrinsic pontine glioma</article-title><source>Nat Med</source><year>2015</year><volume>21</volume><fpage>555</fpage><lpage>559</lpage><pub-id pub-id-type="doi">10.1038/nm.3855</pub-id><?supplied-pmid 25939062?><pub-id pub-id-type="pmid">25939062</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abraham</surname><given-names>J</given-names></name><etal/></person-group><article-title>Lineage of origin in rhabdomyosarcoma informs pharmacological response</article-title><source>Genes Dev</source><year>2014</year><volume>28</volume><fpage>1578</fpage><lpage>1591</lpage><pub-id pub-id-type="doi">10.1101/gad.238733.114</pub-id><?supplied-pmid 25030697?><pub-id pub-id-type="pmid">25030697</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Emori</surname><given-names>M</given-names></name><etal/></person-group><article-title>High expression of CD109 antigen regulates the phenotype of cancer stem-like cells/cancer-initiating cells in the novel epithelioid sarcoma cell line ESX and is related to poor prognosis of soft tissue sarcoma</article-title><source>PLoS One</source><year>2013</year><volume>8</volume><fpage>e84187</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0084187</pub-id><?supplied-pmid 24376795?><pub-id pub-id-type="pmid">24376795</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><mixed-citation publication-type="other">Nishio J, et al. Establishment of a new human epithelioid sarcoma cell line, FU-EPS-1: molecular cytogenetic characterization by use of spectral karyotyping and comparative genomic hybridization. Int J Oncol. 2005;27(361&#x02013;369).</mixed-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edwards</surname><given-names>Aled M</given-names></name><name><surname>Bountra</surname><given-names>Chas</given-names></name><name><surname>Kerr</surname><given-names>David J</given-names></name><name><surname>Willson</surname><given-names>Timothy M</given-names></name></person-group><article-title>Open access chemical and clinical probes to support drug discovery</article-title><source>Nature Chemical Biology</source><year>2009</year><volume>5</volume><issue>7</issue><fpage>436</fpage><lpage>440</lpage><pub-id pub-id-type="doi">10.1038/nchembio0709-436</pub-id><pub-id pub-id-type="pmid">19536100</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Goodnow</surname><given-names>RA</given-names><suffix>Jr</suffix></name><name><surname>Gillespie</surname><given-names>P</given-names></name></person-group><article-title>Hit and Lead identification: efficient practices for drug discovery</article-title><source>Prog Med Chem</source><year>2007</year><volume>45</volume><fpage>1</fpage><lpage>61</lpage><pub-id pub-id-type="doi">10.1016/s0079-6468(06)45501-6</pub-id><?supplied-pmid 17280901?><pub-id pub-id-type="pmid">17280901</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vogt</surname><given-names>MW</given-names></name><etal/></person-group><article-title>Ribavirin antagonizes the effect of azidothymidine on HIV replication</article-title><source>Science (New York, NY)</source><year>1987</year><volume>235</volume><fpage>1376</fpage><lpage>1379</lpage><pub-id pub-id-type="doi">10.1126/science.2435003</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>DePristo</surname><given-names>MA</given-names></name><etal/></person-group><article-title>A framework for variation discovery and genotyping using next-generation DNA sequencing data</article-title><source>Nat Genet</source><year>2011</year><volume>43</volume><fpage>491</fpage><lpage>498</lpage><pub-id pub-id-type="doi">10.1038/ng.806</pub-id><pub-id pub-id-type="pmid">21478889</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Durbin</surname><given-names>R</given-names></name></person-group><article-title>Fast and accurate short read alignment with burrows-wheeler transform</article-title><source>Bioinformatics (Oxford, England)</source><year>2009</year><volume>25</volume><fpage>1754</fpage><lpage>1760</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btp324</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cibulskis</surname><given-names>K</given-names></name><etal/></person-group><article-title>Sensitive detection of somatic point mutations in impure and heterogeneous cancer samples</article-title><source>Nat Biotech</source><year>2013</year><volume>31</volume><fpage>213</fpage><lpage>219</lpage><pub-id pub-id-type="doi">10.1038/nbt.2514</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cingolani</surname><given-names>P</given-names></name><etal/></person-group><article-title>A program for annotating and predicting the effects of single nucleotide polymorphisms, SnpEff: SNPs in the genome of <italic>Drosophila melanogaster</italic> strain w1118; iso-2; iso-3</article-title><source>Fly</source><year>2012</year><volume>6</volume><fpage>80</fpage><lpage>92</lpage><pub-id pub-id-type="doi">10.4161/fly.19695</pub-id><?supplied-pmid 22728672?><pub-id pub-id-type="pmid">22728672</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Koboldt</surname><given-names>DC</given-names></name><etal/></person-group><article-title>VarScan 2: somatic mutation and copy number alteration discovery in cancer by exome sequencing</article-title><source>Genome Res</source><year>2012</year><volume>22</volume><fpage>568</fpage><lpage>576</lpage><pub-id pub-id-type="doi">10.1101/gr.129684.111</pub-id><?supplied-pmid 22300766?><pub-id pub-id-type="pmid">22300766</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Koboldt</surname><given-names>DC</given-names></name><name><surname>Larson</surname><given-names>DE</given-names></name><name><surname>Wilson</surname><given-names>RK</given-names></name></person-group><article-title>Using VarScan 2 for germline variant calling and somatic mutation detection</article-title><source>Curr Protoc Bioinformatics</source><year>2013</year><volume>44</volume><fpage>15.14.11</fpage><lpage>15.14.17</lpage><pub-id pub-id-type="doi">10.1002/0471250953.bi1504s44</pub-id><pub-id pub-id-type="pmid">25553206</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>D</given-names></name><etal/></person-group><article-title>TopHat2: accurate alignment of transcriptomes in the presence of insertions, deletions and gene fusions</article-title><source>Genome Biol</source><year>2013</year><volume>14</volume><fpage>1</fpage><lpage>13</lpage><pub-id pub-id-type="doi">10.1186/gb-2013-14-4-r36</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Langmead</surname><given-names>B</given-names></name><name><surname>Trapnell</surname><given-names>C</given-names></name><name><surname>Pop</surname><given-names>M</given-names></name><name><surname>Salzberg</surname><given-names>S</given-names></name></person-group><article-title>Ultrafast and memory-efficient alignment of short DNA sequences to the human genome</article-title><source>Genome Biol</source><year>2009</year><volume>10</volume><fpage>R25</fpage><pub-id pub-id-type="doi">10.1186/gb-2009-10-3-r25</pub-id><pub-id pub-id-type="pmid">19261174</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Trapnell</surname><given-names>C</given-names></name><etal/></person-group><article-title>Differential gene and transcript expression analysis of RNA-seq experiments with TopHat and cufflinks</article-title><source>Nat Protocols</source><year>2012</year><volume>7</volume><fpage>562</fpage><lpage>578</lpage><pub-id pub-id-type="doi">10.1038/nprot.2012.016</pub-id><pub-id pub-id-type="pmid">22383036</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Aslam</surname><given-names>MI</given-names></name><etal/></person-group><article-title>PDGFRbeta reverses EphB4 signaling in alveolar rhabdomyosarcoma</article-title><source>Proc Natl Acad Sci U S A</source><year>2014</year><volume>111</volume><fpage>6383</fpage><lpage>6388</lpage><pub-id pub-id-type="doi">10.1073/pnas.1403608111</pub-id><?supplied-pmid 24733895?><pub-id pub-id-type="pmid">24733895</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Keller</surname><given-names>C</given-names></name><name><surname>Hansen</surname><given-names>MS</given-names></name><name><surname>Coffin</surname><given-names>CM</given-names></name><name><surname>Capecchi</surname><given-names>MR</given-names></name></person-group><article-title>Pax3:Fkhr interferes with embryonic Pax3 and Pax7 function: implications for alveolar rhabdomyosarcoma cell of origin</article-title><source>Genes Dev</source><year>2004</year><volume>18</volume><fpage>2608</fpage><lpage>2613</lpage><pub-id pub-id-type="doi">10.1101/gad.1243904</pub-id><?supplied-pmid 15520281?><pub-id pub-id-type="pmid">15520281</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abraham</surname><given-names>J</given-names></name><etal/></person-group><article-title>Evasion mechanisms to Igf1r inhibition in rhabdomyosarcoma</article-title><source>Mol Cancer Ther</source><year>2011</year><volume>10</volume><fpage>697</fpage><lpage>707</lpage><pub-id pub-id-type="doi">10.1158/1535-7163.MCT-10-0695</pub-id><?supplied-pmid 21447712?><pub-id pub-id-type="pmid">21447712</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Drewry</surname><given-names>DH</given-names></name><name><surname>Willson</surname><given-names>TM</given-names></name><name><surname>Zuercher</surname><given-names>WJ</given-names></name></person-group><article-title>Seeding collaborations to advance kinase science with the GSK published kinase inhibitor set (PKIS)</article-title><source>Curr Top Med Chem</source><year>2014</year><volume>14</volume><fpage>340</fpage><lpage>342</lpage><pub-id pub-id-type="doi">10.2174/1568026613666131127160819</pub-id><pub-id pub-id-type="pmid">24283969</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>Z</given-names></name><etal/></person-group><article-title>Efficacy of therapy with bortezomib in solid tumors: a review based on 32 clinical trials</article-title><source>Future Oncol</source><year>2014</year><volume>10</volume><fpage>1795</fpage><lpage>1807</lpage><pub-id pub-id-type="doi">10.2217/fon.14.30</pub-id><?supplied-pmid 25303058?><pub-id pub-id-type="pmid">25303058</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>B</given-names></name><name><surname>Davie</surname><given-names>JK</given-names></name></person-group><article-title>New insights into signalling-pathway alterations in rhabdomyosarcoma</article-title><source>Br J Cancer</source><year>2015</year><volume>112</volume><fpage>227</fpage><lpage>231</lpage><pub-id pub-id-type="doi">10.1038/bjc.2014.471</pub-id><?supplied-pmid 25211658?><pub-id pub-id-type="pmid">25211658</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shern</surname><given-names>JF</given-names></name><etal/></person-group><article-title>Comprehensive genomic analysis of rhabdomyosarcoma reveals a landscape of alterations affecting a common genetic axis in fusion-positive and fusion-negative tumors</article-title><source>Cancer Discov</source><year>2014</year><volume>4</volume><fpage>216</fpage><lpage>231</lpage><pub-id pub-id-type="doi">10.1158/2159-8290.cd-13-0639</pub-id><?supplied-pmid 4462130?><pub-id pub-id-type="pmid">24436047</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Riedemann</surname><given-names>J</given-names></name><name><surname>Macaulay</surname><given-names>VM</given-names></name></person-group><article-title>IGF1R signalling and its inhibition</article-title><source>Endocr Relat Cancer</source><year>2006</year><volume>13</volume><fpage>S33</fpage><lpage>S43</lpage><pub-id pub-id-type="doi">10.1677/erc.1.01280</pub-id><?supplied-pmid 17259557?><pub-id pub-id-type="pmid">17259557</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chou</surname><given-names>T-C</given-names></name></person-group><article-title>Theoretical basis, experimental design, and computerized simulation of synergism and antagonism in drug combination studies</article-title><source>Pharmacol Rev</source><year>2006</year><volume>58</volume><fpage>621</fpage><lpage>681</lpage><pub-id pub-id-type="doi">10.1124/pr.58.3.10</pub-id><?supplied-pmid 16968952?><pub-id pub-id-type="pmid">16968952</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Noujaim</surname><given-names>J</given-names></name><etal/></person-group><source>Front Oncol</source><year>2015</year><volume>5</volume><fpage>186</fpage><pub-id pub-id-type="doi">10.3389/fonc.2015.00186</pub-id><?supplied-pmid 26347853?><pub-id pub-id-type="pmid">26347853</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thway</surname><given-names>K</given-names></name><name><surname>Jones</surname><given-names>RL</given-names></name><name><surname>Noujaim</surname><given-names>J</given-names></name><name><surname>Fisher</surname><given-names>C</given-names></name></person-group><source>Adv Anat Pathol</source><year>2016</year><volume>23</volume><fpage>41</fpage><lpage>49</lpage><pub-id pub-id-type="doi">10.1097/PAP.0000000000000102</pub-id><?supplied-pmid 26645461?><pub-id pub-id-type="pmid">26645461</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gerlinger</surname><given-names>M</given-names></name><etal/></person-group><article-title>Intratumor heterogeneity and branched evolution revealed by multiregion sequencing</article-title><source>N Engl J Med</source><year>2012</year><volume>366</volume><fpage>883</fpage><lpage>892</lpage><pub-id pub-id-type="doi">10.1056/NEJMoa1113205</pub-id><?supplied-pmid 4878653?><pub-id pub-id-type="pmid">22397650</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>The Genomes Project</surname><given-names>C</given-names></name></person-group><article-title>A global reference for human genetic variation</article-title><source>Nature</source><year>2015</year><volume>526</volume><fpage>68</fpage><pub-id pub-id-type="doi">10.1038/nature15393</pub-id><pub-id pub-id-type="pmid">26432245</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sherry</surname><given-names>ST</given-names></name><etal/></person-group><article-title>dbSNP: the NCBI database of genetic variation</article-title><source>Nucleic Acids Res</source><year>2001</year><volume>29</volume><fpage>308</fpage><lpage>311</lpage><pub-id pub-id-type="doi">10.1093/nar/29.1.308</pub-id><pub-id pub-id-type="pmid">11125122</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Forbes</surname><given-names>SA</given-names></name><etal/></person-group><article-title>COSMIC: somatic cancer genetics at high-resolution</article-title><source>Nucleic Acids Res</source><year>2017</year><volume>45</volume><fpage>D777</fpage><lpage>D783</lpage><pub-id pub-id-type="doi">10.1093/nar/gkw1121</pub-id><?supplied-pmid 27899578?><pub-id pub-id-type="pmid">27899578</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Penot</surname><given-names>P</given-names></name><etal/></person-group><source>Br J Dermatol</source><year>2013</year><volume>168</volume><fpage>871</fpage><lpage>873</lpage><pub-id pub-id-type="doi">10.1111/bjd.12038</pub-id><?supplied-pmid 22963050?><pub-id pub-id-type="pmid">22963050</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><mixed-citation publication-type="other">Giudice G, Petsalaki E. Proteomics and phosphoproteomics in precision medicine: applications and challenges. Brief Bioinform. 2017:bbx141&#x02013;1. 10.1093/bib/bbx141.</mixed-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Drake</surname><given-names>JM</given-names></name><etal/></person-group><article-title>Phosphoproteome integration reveals patient-specific networks in prostate Cancer</article-title><source>Cell</source><year>2016</year><volume>166</volume><fpage>1041</fpage><lpage>1054</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2016.07.007</pub-id><?supplied-pmid 27499020?><pub-id pub-id-type="pmid">27499020</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Geho</surname><given-names>DH</given-names></name><name><surname>Petricoin</surname><given-names>EF</given-names></name><name><surname>Liotta</surname><given-names>LA</given-names></name><name><surname>Araujo</surname><given-names>RP</given-names></name></person-group><article-title>Modeling of protein signaling networks in clinical proteomics</article-title><source>Cold Spring Harb Symp Quant Biol</source><year>2005</year><volume>70</volume><fpage>517</fpage><lpage>524</lpage><pub-id pub-id-type="doi">10.1101/sqb.2005.70.022</pub-id><?supplied-pmid 16869790?><pub-id pub-id-type="pmid">16869790</pub-id></element-citation></ref></ref-list></back></article>