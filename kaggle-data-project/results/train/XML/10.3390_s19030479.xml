<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.1 20151215//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1.dtd?><?SourceDTD.Version 1.1?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Sensors (Basel)</journal-id><journal-id journal-id-type="iso-abbrev">Sensors (Basel)</journal-id><journal-id journal-id-type="publisher-id">sensors</journal-id><journal-title-group><journal-title>Sensors (Basel, Switzerland)</journal-title></journal-title-group><issn pub-type="epub">1424-8220</issn><publisher><publisher-name>MDPI</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6386951</article-id><article-id pub-id-type="doi">10.3390/s19030479</article-id><article-id pub-id-type="publisher-id">sensors-19-00479</article-id><article-categories><subj-group subj-group-type="heading"><subject>Article</subject></subj-group></article-categories><title-group><article-title>SLIC Superpixel-Based <italic>l</italic><sub>2,1</sub>-Norm Robust Principal Component Analysis for Hyperspectral Image Classification</article-title></title-group><contrib-group><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0001-7070-831X</contrib-id><name><surname>Zu</surname><given-names>Baokai</given-names></name><xref ref-type="aff" rid="af1-sensors-19-00479">1</xref></contrib><contrib contrib-type="author"><name><surname>Xia</surname><given-names>Kewen</given-names></name><xref ref-type="aff" rid="af1-sensors-19-00479">1</xref><xref rid="c1-sensors-19-00479" ref-type="corresp">*</xref></contrib><contrib contrib-type="author"><name><surname>Li</surname><given-names>Tiejun</given-names></name><xref ref-type="aff" rid="af2-sensors-19-00479">2</xref></contrib><contrib contrib-type="author"><name><surname>He</surname><given-names>Ziping</given-names></name><xref ref-type="aff" rid="af1-sensors-19-00479">1</xref></contrib><contrib contrib-type="author"><name><surname>Li</surname><given-names>Yafang</given-names></name><xref ref-type="aff" rid="af3-sensors-19-00479">3</xref></contrib><contrib contrib-type="author"><name><surname>Hou</surname><given-names>Jingzhong</given-names></name><xref ref-type="aff" rid="af1-sensors-19-00479">1</xref></contrib><contrib contrib-type="author"><name><surname>Du</surname><given-names>Wei</given-names></name><xref ref-type="aff" rid="af4-sensors-19-00479">4</xref></contrib></contrib-group><aff id="af1-sensors-19-00479"><label>1</label>School of Electronics and Information Engineering, Hebei University of Technology, Tianjin 300401, China; <email><EMAIL></email> (B.Z.); <email><EMAIL></email> (Z.H.); <email><EMAIL></email> (J.H.)</aff><aff id="af2-sensors-19-00479"><label>2</label>School of Mechanical Engineering, Hebei University of Technology, Tianjin 300401, China; <email><EMAIL></email></aff><aff id="af3-sensors-19-00479"><label>3</label>Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China; <email><EMAIL></email></aff><aff id="af4-sensors-19-00479"><label>4</label>College of Resources and Environment, Huazhong Agricultural University, Wuhan 430070, China; <email><EMAIL></email></aff><author-notes><corresp id="c1-sensors-19-00479"><label>*</label>Correspondence: <email><EMAIL></email></corresp></author-notes><pub-date pub-type="epub"><day>24</day><month>1</month><year>2019</year></pub-date><pub-date pub-type="collection"><month>2</month><year>2019</year></pub-date><volume>19</volume><issue>3</issue><elocation-id>479</elocation-id><history><date date-type="received"><day>19</day><month>11</month><year>2018</year></date><date date-type="accepted"><day>17</day><month>1</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; 2019 by the authors.</copyright-statement><copyright-year>2019</copyright-year><license license-type="open-access"><license-p>Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>).</license-p></license></permissions><abstract><p>Hyperspectral Images (HSIs) contain enriched information due to the presence of various bands, which have gained attention for the past few decades. However, explosive growth in HSIs&#x02019; scale and dimensions causes &#x0201c;Curse of dimensionality&#x0201d; and &#x0201c;Hughes phenomenon&#x0201d;. Dimensionality reduction has become an important means to overcome the &#x0201c;Curse of dimensionality&#x0201d;. In hyperspectral images, labeled samples are more difficult to collect because they require many labor and material resources. Semi-supervised dimensionality reduction is very important in mining high-dimensional data due to the lack of costly-labeled samples. The promotion of the supervised dimensionality reduction method to the semi-supervised method is mostly done by graph, which is a powerful tool for characterizing data relationships and manifold exploration. To take advantage of the spatial information of data, we put forward a novel graph construction method for semi-supervised learning, called SLIC Superpixel-based <inline-formula><mml:math id="mm1"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm Robust Principal Component Analysis (SURPCA<sub>2,1</sub>), which integrates superpixel segmentation method Simple Linear Iterative Clustering (SLIC) into Low-rank Decomposition. First, the SLIC algorithm is adopted to obtain the spatial homogeneous regions of HSI. Then, the <inline-formula><mml:math id="mm3"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm RPCA is exploited in each superpixel area, which captures the global information of homogeneous regions and preserves spectral subspace segmentation of HSIs very well. Therefore, we have explored the spatial and spectral information of hyperspectral image simultaneously by combining superpixel segmentation with RPCA. Finally, a semi-supervised dimensionality reduction framework based on SURPCA<sub>2,1</sub> graph is used for feature extraction task. Extensive experiments on multiple HSIs showed that the proposed spectral-spatial SURPCA<sub>2,1</sub> is always comparable to other compared graphs with few labeled samples.</p></abstract><kwd-group><kwd>Hyperspectral Image</kwd><kwd>Robust Principal Component Analysis (RPCA)</kwd><kwd>Simple Linear Iterative Clustering (SLIC)</kwd><kwd>superpixel segmentation</kwd></kwd-group></article-meta></front><body><sec sec-type="intro" id="sec1-sensors-19-00479"><title>1. Introduction</title><p>Hyperspectral Images (HSIs) provide comprehensive spectral information of the materials&#x02019; physical properties [<xref rid="B1-sensors-19-00479" ref-type="bibr">1</xref>], which is applied in ecosystem monitoring [<xref rid="B2-sensors-19-00479" ref-type="bibr">2</xref>], agricultural monitoring [<xref rid="B3-sensors-19-00479" ref-type="bibr">3</xref>,<xref rid="B4-sensors-19-00479" ref-type="bibr">4</xref>], environmental monitoring [<xref rid="B5-sensors-19-00479" ref-type="bibr">5</xref>], forestry [<xref rid="B6-sensors-19-00479" ref-type="bibr">6</xref>], urban growth analysis [<xref rid="B7-sensors-19-00479" ref-type="bibr">7</xref>,<xref rid="B8-sensors-19-00479" ref-type="bibr">8</xref>], and mineral identification [<xref rid="B9-sensors-19-00479" ref-type="bibr">9</xref>,<xref rid="B10-sensors-19-00479" ref-type="bibr">10</xref>]. The abundant hyperspectral image bands bring rich spectral information, but they result in the &#x0201c;Hughes phenomenon&#x0201d; [<xref rid="B11-sensors-19-00479" ref-type="bibr">11</xref>,<xref rid="B12-sensors-19-00479" ref-type="bibr">12</xref>] that reduces the accuracy and efficiency of the classification task. By reducing the dimensionality of the data, more compact low-dimensional hyperspectral images can be obtained. Therefore, it is important and practically significant to perform appropriate dimensionality reduction on hyperspectral images.</p><p>Dimensionality reduction refers to finding the low-dimensional representation of the original data by eliminating the redundant elements and retaining their main features, which can overcome the &#x0201c;Curse of dimensionality&#x0201d; problem. Dimensionality reduction mainly includes feature selection and feature extraction [<xref rid="B13-sensors-19-00479" ref-type="bibr">13</xref>]. Feature selection methods reduce the dimensions of the original data by selecting the most representative and distinguishing features [<xref rid="B14-sensors-19-00479" ref-type="bibr">14</xref>]. Feature extraction methods combine multiple features linearly and non-linearly on the basis of maintaining the data structure information [<xref rid="B15-sensors-19-00479" ref-type="bibr">15</xref>]. Recently, many dimensionality reduction algorithms have been put forward. The most classic ones are the early Principal Component Analysis (PCA) [<xref rid="B16-sensors-19-00479" ref-type="bibr">16</xref>], Linear Discriminant Analysis (LDA) [<xref rid="B17-sensors-19-00479" ref-type="bibr">17</xref>], and Independent Component Analysis (ICA) [<xref rid="B18-sensors-19-00479" ref-type="bibr">18</xref>,<xref rid="B19-sensors-19-00479" ref-type="bibr">19</xref>,<xref rid="B20-sensors-19-00479" ref-type="bibr">20</xref>]. In recent years, feature extraction has quickly become one of the hotspots in machine learning and data mining. Compared with the traditional dimensionality reduction method, the main difference of manifold learning is its ability to maintain the invariance of the data structure. The most representative algorithms include Laplacian Eigenmap [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>], Isomap [<xref rid="B22-sensors-19-00479" ref-type="bibr">22</xref>], and Local Linear Embedding (LLE) [<xref rid="B23-sensors-19-00479" ref-type="bibr">23</xref>]. A multimetric learning approach that combines feature extraction and active learning (AL) is proposed to deal with the high dimensionality of the input data and the limited number of the labeled samples simultaneously [<xref rid="B24-sensors-19-00479" ref-type="bibr">24</xref>]. To simultaneously deal with the two issues mentioned above, a regularized multimetric active learning (AL) framework is proposed [<xref rid="B25-sensors-19-00479" ref-type="bibr">25</xref>].</p><p>With the rapid development of data acquisition and storage technology, many unlabeled samples are available, but labeled samples are more difficult to collect. If the training samples are limited, the machine learning methods may be confronted with over-fitting problems when dealing with high-dimensional small sample size problems [<xref rid="B26-sensors-19-00479" ref-type="bibr">26</xref>,<xref rid="B27-sensors-19-00479" ref-type="bibr">27</xref>]. For the classification of hyperspectral images (HSIs), good classification results usually require many labeled samples. To resolve the over-fitting problem, semi-supervised learning was proposed to utilize both labeled samples and the unlabeled samples that conveyed the marginal distribution information to enhance the algorithmic performance [<xref rid="B28-sensors-19-00479" ref-type="bibr">28</xref>,<xref rid="B29-sensors-19-00479" ref-type="bibr">29</xref>,<xref rid="B30-sensors-19-00479" ref-type="bibr">30</xref>]. Semi-supervised dimensionality reduction combines semi-supervised learning with dimensionality reduction, and has gradually become a new branch in the machine learning field. Semi-supervised dimensionality reduction methods not only use the supervised information of the data, but also maintain the structural information of the data. In the dimensionality reduction method, the promotion of the supervised dimensionality reduction method to a semi-supervised method is mostly done by graph-based methods. Therefore, graph-based semi-supervised learning methods have gained a great deal of attention, which construct adjacency graph to extract the local geometry of the data. Graph is a powerful data analysis tool, which is widely used in many research domains, such as dimension reduction [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>,<xref rid="B22-sensors-19-00479" ref-type="bibr">22</xref>,<xref rid="B23-sensors-19-00479" ref-type="bibr">23</xref>], semi-supervised learning [<xref rid="B31-sensors-19-00479" ref-type="bibr">31</xref>,<xref rid="B32-sensors-19-00479" ref-type="bibr">32</xref>], manifold embedding, and machine learning [<xref rid="B33-sensors-19-00479" ref-type="bibr">33</xref>,<xref rid="B34-sensors-19-00479" ref-type="bibr">34</xref>]. In graph-based methods, each point is mapped to a low-dimensional feature vector, trying to maintain the connection between vertices [<xref rid="B30-sensors-19-00479" ref-type="bibr">30</xref>]. The procedure of graph construction effectively determines the potential of the graph-based learning algorithms. Thus, for a specific task, a graph which models the data structure aptly will correspondingly achieve a good performance. Therefore, how to construct a good graph has been widely studied in recent years, and it is still an open problem [<xref rid="B35-sensors-19-00479" ref-type="bibr">35</xref>].</p><p>Within the last several years, numerous algorithms for feature extraction have been put forward. PCA is a widely-used linear subspace algorithm that seeks a low-dimensional representation of high-dimensional data. PCA works against small Gaussian noise in data effectively, but it is highly sensitive to sparse errors of high magnitude. To solve this problem, Cand&#x000e8;s [<xref rid="B36-sensors-19-00479" ref-type="bibr">36</xref>] and Wright et al. [<xref rid="B37-sensors-19-00479" ref-type="bibr">37</xref>] proposed Robust Principal Component Analysis (RPCA), which intends to decompose the observed data into a low-rank matrix and a sparse noises matrix. In [<xref rid="B38-sensors-19-00479" ref-type="bibr">38</xref>], a novel feature extraction method based on non-convex robust principal component analysis (NRPCA) was proposed for hyperspectral image classification. Nie et al. developed a novel model named graph-regularized tensor robust principal component analysis (GTRPCA) for denoising HSIs [<xref rid="B39-sensors-19-00479" ref-type="bibr">39</xref>]. Chen et al. denoised hyperspectral images using principal component analysis and block-matching 4D filtering [<xref rid="B40-sensors-19-00479" ref-type="bibr">40</xref>].</p><p>The above mentioned methods suggest that the pixels in a hyperspectral image lie in a low-rank manifold. However, the spatial correlation among pixels is not revealed. For HSIs, the adjacent pixels&#x02019; correlation is typically extremely high [<xref rid="B41-sensors-19-00479" ref-type="bibr">41</xref>], which has potential low-rank attributes. Pixels in homogenous areas usually consist of similar materials, whose spectral properties are highly similar and can be taken as being approximately in the same subspace. By extracting the low-rank matrix of the data, the low-dimensional structure of each pixel is revealed and we can classify each pixel more accurately. Xu et al. put forward a low-rank decomposition spectral-spatial algorithm, which incorporates global and local correlation [<xref rid="B42-sensors-19-00479" ref-type="bibr">42</xref>]. Fan et al. integrated superpixel segmentation (SS) into Low-rank Representation (LRR) and proposed a novel denoising method called SS-LRR [<xref rid="B43-sensors-19-00479" ref-type="bibr">43</xref>]. A multi-scale superpixel based sparse representation (MSSR) for HSIs&#x02019; classification is proposed to overcome the disadvantages of utilizing structural information [<xref rid="B44-sensors-19-00479" ref-type="bibr">44</xref>]. Sun et al. presented a novel noise reduction method based on superpixel-based low-rank representation for hyperspectral image [<xref rid="B45-sensors-19-00479" ref-type="bibr">45</xref>]. In [<xref rid="B46-sensors-19-00479" ref-type="bibr">46</xref>], Mei et al. proposed a new unmixing method with superpixel segmentation and LRR based on RGBM. Tong et al. proposed multiscale union regions adaptive sparse representation (MURASR) by multiscale patches and superpixels [<xref rid="B47-sensors-19-00479" ref-type="bibr">47</xref>]. A novel method, robust regularization block low-rank discriminant analysis, is proposed for HSIs&#x02019; feature extraction [<xref rid="B48-sensors-19-00479" ref-type="bibr">48</xref>].</p><p>Spatial information can play a very important role in mapping data. However, many graph construction methods do not make full use of data&#x02019;s spatial information. For example, in hyperspectral images, adjacent pixels in a homogenous area usually belong to the same category. Therefore, how to consider the features of the actual data globally to further improve the semi-supervised dimensionality reduction performance is one of the main research components of this paper. Considering the spatial correlative and spectral low-rank characteristics of pixels in HSIs, we integrate the Simple Linear Iterative Clustering (SLIC) segmentation method into low-rank decomposition. <xref ref-type="fig" rid="sensors-19-00479-f001">Figure 1</xref> shows the formulation of the proposed SLIC Superpixel-based <inline-formula><mml:math id="mm6"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm Robust Principal Component Analysis (SURPCA<sub>2,1</sub>) for hyperspectral image classification. As shown in <xref ref-type="fig" rid="sensors-19-00479-f001">Figure 1</xref>, we preprocess the hyperspectral image by the Image Fusion and Recursive Filtering feature (IFRF), which eradicates the noise and redundant information concurrently [<xref rid="B12-sensors-19-00479" ref-type="bibr">12</xref>]. The proposed method divide the image into multiple homogeneous regions by the superpixel segmentation algorithm SLIC. The pixels in each homogeneous region may belong to the same ground object category. Therefore, we stack the pixels in the same homogeneous region into a matrix. Due to the low-rank property of the matrix, RPCA is used to recover the low-dimensional structure of all pixels in the homogeneous region. Then, we combine these low-rank matrices together to an integrated low-rank graph. In addition, we process the semi-supervised discriminant analysis for dimension reduction, which takes advantage of the labeled samples and the distribution of the whole samples. The <italic>k</italic>-Nearest Neighbor (<italic>k</italic>NN) algorithm is applied to handle the low-rank graph for the regularized graph of semi-supervised discriminant analysis. Finally, we implement the Nearest Neighbor classifier method. We summarize the main contributions of the paper in the following paragraphs.
<list list-type="bullet"><list-item><p>Inspired by robust principal component analysis and superpixel segmentation, we put forward a novel graph construction method, SLIC superpixel-based <inline-formula><mml:math id="mm8"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis. The superpixel-based <inline-formula><mml:math id="mm9"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm RPCA extracts the low-rank spectral structure of all pixels in each uniform region, respectively.</p></list-item><list-item><p>The simple linear iterative clustering addresses the spatial characteristics of hyperspectral images. Consequently, the SURPCA<sub>2,1</sub> graph model can classify pixels more accurately.</p></list-item><list-item><p>To investigate the performance of the SLIC superpixel-based <inline-formula><mml:math id="mm11"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis graph model, we conducted extensive experiments on several real multi-class hyperspectral images.</p></list-item></list></p><p>We start with a scientific background in <xref ref-type="sec" rid="sec2-sensors-19-00479">Section 2</xref>. <xref ref-type="sec" rid="sec3-sensors-19-00479">Section 3</xref> deciphers the HSIs classification with <inline-formula><mml:math id="mm12"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis. <xref ref-type="sec" rid="sec4-sensors-19-00479">Section 4</xref> performs comparative experiments on real-world HSIs to examine the performance of the proposed graph. We provide the discussion in <xref ref-type="sec" rid="sec5-sensors-19-00479">Section 5</xref>. Conclusions are presented in <xref ref-type="sec" rid="sec6-sensors-19-00479">Section 6</xref>.</p></sec><sec id="sec2-sensors-19-00479"><title>2. Scientific Background</title><sec id="sec2dot1-sensors-19-00479"><title>2.1. Graph-Based Semi-Supervised Dimensionality Reduction Method</title><p>In the dimensionality reduction method, the promotion of supervised method to semi-supervised method is mostly done by graph-based methods. Maier et al. [<xref rid="B49-sensors-19-00479" ref-type="bibr">49</xref>] showed that different graph structures obtain different results in the same clustering algorithm. A good graph construction method has great influence in graph-based semi-supervised learning. Therefore, how to construct a good graph sometimes seems to be more important than a good objective function [<xref rid="B50-sensors-19-00479" ref-type="bibr">50</xref>].</p><p>Graph is composed by nodes and edges, which represents the structure of the entire dataset. The nodes represent the sample points, and the edges&#x02019; weight represents the similarity between the nodes. Generally, the greater the weight is, the higher the similarity between the nodes will be. If the edge does not exist, the similarity between the two points will be zero. The similarity between nodes is usually measured by distance, such as Euclid, Mahalanobis, and Chebyshev distance [<xref rid="B51-sensors-19-00479" ref-type="bibr">51</xref>]. Different graph construction methods have great impact on the performance of classification results. The graph construction process mainly includes the graph structure and the edges&#x02019; weight function. Two commonly used graph structures are Fully Connected graphs and Nearest Neighbor graphs (<italic>k</italic>-nearest neighbor graph and <inline-formula><mml:math id="mm13"><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow></mml:math></inline-formula>-ball Graph).
<list list-type="bullet"><list-item><p>Fully Connected graphs [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>,<xref rid="B52-sensors-19-00479" ref-type="bibr">52</xref>]: In the fully connected graph, all nodes are connected by edges whose weights are not zero. The fully connected graphs are easy to construct and have good performance for semi-supervised learning methods. However, the disadvantage of the fully connected graphs is that it requires processing all nodes, which would lead to high computational complexity.</p></list-item><list-item><p><italic>k</italic>-nearest neighbor graph [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>]: Each node in the <italic>k</italic>-nearest neighbor graph is only connected with <italic>k</italic> neighbor in a certain distance. The samples <inline-formula><mml:math id="mm14"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm15"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> are considered as neighbor if <inline-formula><mml:math id="mm16"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is among the <italic>k</italic> nearest neighbor of <inline-formula><mml:math id="mm17"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> or <inline-formula><mml:math id="mm18"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is among the <italic>k</italic>-nearest neighbor of <inline-formula><mml:math id="mm19"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><p><inline-formula><mml:math id="mm20"><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow></mml:math></inline-formula>-ball graph [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>]: In the <inline-formula><mml:math id="mm21"><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow></mml:math></inline-formula>-ball graph, the connections between data points occur in the neighborhood of radius <inline-formula><mml:math id="mm22"><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow></mml:math></inline-formula>. That is to say, if the distance between <inline-formula><mml:math id="mm23"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm24"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> exists <inline-formula><mml:math id="mm25"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo><mml:mo>&#x02264;</mml:mo><mml:mi>&#x003b5;</mml:mi><mml:mo>,</mml:mo><mml:mi>&#x003b5;</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="double-struck">R</mml:mi><mml:mo>,</mml:mo><mml:mi>&#x003b5;</mml:mi><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula>, there will be a neighbor relationship between these two nodes. Therefore, the connectivity of the graph is largely influenced by the parameter <inline-formula><mml:math id="mm26"><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow></mml:math></inline-formula>.</p></list-item></list></p><p>It is also necessary to assign weight for the connected edges <inline-formula><mml:math id="mm27"><mml:mrow><mml:mi mathvariant="bold">W</mml:mi></mml:mrow></mml:math></inline-formula>. The followings are commonly used weight functions:<list list-type="bullet"><list-item><p>Inverse of Euclidean distance [<xref rid="B53-sensors-19-00479" ref-type="bibr">53</xref>],
<disp-formula id="FD1-sensors-19-00479"><label>(1)</label><mml:math id="mm28"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">W</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfenced separators="" open="{" close=""><mml:mtable><mml:mtr><mml:mtd><mml:mrow><mml:msup><mml:mrow><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:msub><mml:mi>x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>2</mml:mn></mml:mrow></mml:msup><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced separators="" open="(" close=")"><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>&#x000a0;</mml:mo><mml:mi>o</mml:mi><mml:mi>r</mml:mi><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced separators="" open="(" close=")"><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow><mml:mn>0</mml:mn><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>o</mml:mi><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>w</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi><mml:mo>.</mml:mo></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:mrow></mml:math></disp-formula></p></list-item><list-item><p>0&#x02013;1 weighting [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>],
<disp-formula id="FD2-sensors-19-00479"><label>(2)</label><mml:math id="mm29"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">W</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfenced separators="" open="{" close=""><mml:mtable><mml:mtr><mml:mtd><mml:mrow><mml:mn>1</mml:mn><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced separators="" open="(" close=")"><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>&#x000a0;</mml:mo><mml:mi>o</mml:mi><mml:mi>r</mml:mi><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced separators="" open="(" close=")"><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow><mml:mn>0</mml:mn><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>o</mml:mi><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>w</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi><mml:mo>.</mml:mo></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:mrow></mml:math></disp-formula></p></list-item><list-item><p>Heat kernel weighting [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>],
<disp-formula id="FD3-sensors-19-00479"><label>(3)</label><mml:math id="mm30"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">W</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfenced separators="" open="{" close=""><mml:mtable><mml:mtr><mml:mtd><mml:mrow><mml:mi>exp</mml:mi><mml:mfenced separators="" open="(" close=")"><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:msup><mml:mrow><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:msub><mml:mi>x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:mfenced></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mrow><mml:mn>2</mml:mn><mml:msup><mml:mi>&#x003c3;</mml:mi><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced separators="" open="(" close=")"><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mi>o</mml:mi><mml:mi>r</mml:mi><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mfenced separators="" open="(" close=")"><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow><mml:mn>0</mml:mn><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>o</mml:mi><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>w</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi><mml:mo>.</mml:mo></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:mrow></mml:math></disp-formula></p></list-item></list></p><p><italic>k</italic>NN graph can make full use of the local information of adjacent nodes. Since <italic>k</italic>NN graph is sparse, it can solve the computational complexity and storage problem in the fully connected graph. The parameters <italic>k</italic> and <inline-formula><mml:math id="mm31"><mml:mrow><mml:mi>&#x003b5;</mml:mi></mml:mrow></mml:math></inline-formula> depend on the data density, which is difficult to select. The choice of neighbor size parameter is the key to the effectiveness of the graph-based semi-supervised learning method. The Nearest Neighbor graph lacks global constraints of the data points, and its representation performance is greatly reduced when the data is seriously damaged [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>].</p></sec><sec id="sec2dot2-sensors-19-00479"><title>2.2. Simple Linear Iterative Clustering</title><p>The superpixel is an image segmentation technique proposed by Xiaofeng Ren in 2003 [<xref rid="B54-sensors-19-00479" ref-type="bibr">54</xref>]. It refers to the image regions with certain visual meanings composed of adjacent pixels which have similar physical characteristics such as texture, color, brightness, etc. [<xref rid="B55-sensors-19-00479" ref-type="bibr">55</xref>]. The superpixel segmentation methods segment pixels and replace a large number pixels with a small number of superpixels, which greatly reduces the complexity of image post-processing. It has been widely used in computer vision applications such as image segmentation, pose estimation, target tracking, and target recognition. The boundary information is relatively obvious between the superpixels. Achanta et al. introduced a simple linear iterative clustering algorithm to efficiently produce superpixels that are compact and nearly uniform [<xref rid="B56-sensors-19-00479" ref-type="bibr">56</xref>].</p><p>The superpixel algorithms are broadly classified into graph-based and gradient-ascent-based algorithms. In graph-based algorithms, each pixel is treated as a node in a graph, and edge weights between two nodes are set proportional to the similarity between the pixels. Superpixel segmentations are extracted by effectively minimizing a cost function defined on the graph [<xref rid="B57-sensors-19-00479" ref-type="bibr">57</xref>]. Simple linear iterative clustering was proposed in 2010, and is simple to use and understand. Simple linear iterative clustering is based on the <italic>k</italic>-means clustering algorithm [<xref rid="B58-sensors-19-00479" ref-type="bibr">58</xref>], which is done in the five-dimensional <inline-formula><mml:math id="mm32"><mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi><mml:mi>x</mml:mi><mml:mi>y</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> space, where <inline-formula><mml:math id="mm33"><mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> is the pixel color vector in CIELAB color space, which is widely considered as perceptually uniform for small color distances, and <inline-formula><mml:math id="mm34"><mml:mrow><mml:mrow><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> is the pixel position [<xref rid="B57-sensors-19-00479" ref-type="bibr">57</xref>]. Starting from an initial rough clustering, the clusters from the previous iteration are refined to obtain better segmentation in each iteration by gradient ascent method until convergence. Then, the distance metrics of the five-dimensional feature vectors are constructed. Finally, the pixels are clustered in the image locally [<xref rid="B57-sensors-19-00479" ref-type="bibr">57</xref>,<xref rid="B59-sensors-19-00479" ref-type="bibr">59</xref>]. The method has a high comprehensive evaluation index in terms of computational speed and superpixel shape, which achieves good segmentation results. It has two significant advantages compared with other algorithms. One is that it restricts the search space to proportionate the size of the superpixels, which can significantly reduce the number of distance calculations during the optimization process. The other is that the weighted distance combines color and spatial metrics while control the number and compactness of superpixels.</p><p>The SLIC method performs a <italic>k</italic>-means-based local clustering algorithm in a five-dimensional space distance measurement, which achieves compactness and regularity in superpixel shapes. <italic>L</italic> represents luminosity, and ranges from 0 (black) to 100 (white). The color-associated elements <italic>a</italic> and <italic>b</italic> represent the range of colors from magenta to green and yellow to blue, respectively. The <inline-formula><mml:math id="mm35"><mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> color model not only contains the entire color gamut in <inline-formula><mml:math id="mm36"><mml:mrow><mml:mrow><mml:mi>R</mml:mi><mml:mi>G</mml:mi><mml:mi>B</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm37"><mml:mrow><mml:mrow><mml:mi>C</mml:mi><mml:mi>M</mml:mi><mml:mi>Y</mml:mi><mml:mi>K</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>, but also colors they cannot present. The <inline-formula><mml:math id="mm38"><mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> space can express all colors perceived by the human eyes, and is perceptually uniform for small color distance. Instead of directly using the Euclidean distance in this five-dimensional space, SLIC introduces a new distance measure that considers superpixels&#x02019; size. For an image with <italic>N</italic> pixels, the SLIC algorithm takes a desired number of equally-sized superpixels <inline-formula><mml:math id="mm39"><mml:mrow><mml:mrow><mml:mi>N</mml:mi><mml:mo stretchy="false">/</mml:mo><mml:mi>K</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> as input. There is a superpixel center at each grid interval <inline-formula><mml:math id="mm40"><mml:mrow><mml:mrow><mml:mi>S</mml:mi><mml:mo>=</mml:mo><mml:msqrt><mml:mrow><mml:mi>N</mml:mi><mml:mo stretchy="false">/</mml:mo><mml:mi>K</mml:mi></mml:mrow></mml:msqrt></mml:mrow></mml:mrow></mml:math></inline-formula>. SLIC is easy in practice application because it only needs to set the unique superpixels&#x02019; desired number <italic>K</italic>.</p><p>The clustering procedure begins with <italic>K</italic> initial cluster centers (seeds) <inline-formula><mml:math id="mm41"><mml:mrow><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msup><mml:mrow><mml:mo>[</mml:mo><mml:msub><mml:mi>l</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>b</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>]</mml:mo></mml:mrow><mml:mi>T</mml:mi></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>, which are sampled on regular grids with an <italic>S</italic> pixels interval. Each seed is moved to the lowest gradient position in its <inline-formula><mml:math id="mm42"><mml:mrow><mml:mrow><mml:mn>3</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>3</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> neighborhood to avoid centering the superpixel at the edge position and reduce the chance of seeding with noisy pixels. We assume that the cluster center is located within a <inline-formula><mml:math id="mm43"><mml:mrow><mml:mrow><mml:mrow><mml:mn>2</mml:mn><mml:mi>S</mml:mi></mml:mrow><mml:mo>&#x000d7;</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mi>S</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> area since the spatial extent of a superpixel is about <inline-formula><mml:math id="mm44"><mml:mrow><mml:msup><mml:mrow><mml:mi>S</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:math></inline-formula>. This strategy can accelerate the convergence process, as shown in <xref ref-type="fig" rid="sensors-19-00479-f002">Figure 2</xref>. The SLIC superpixel segmentation algorithm is a simple local <italic>k</italic>-means algorithm, whose search area is the <inline-formula><mml:math id="mm45"><mml:mrow><mml:mrow><mml:mrow><mml:mn>2</mml:mn><mml:mi>S</mml:mi></mml:mrow><mml:mo>&#x000d7;</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mi>S</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> area nearest to each cluster center. Since each pixel is searched by multiple seed pixels, the cluster center of the pixel is the seed with the minimum value.</p><p>Let <inline-formula><mml:math id="mm46"><mml:mrow><mml:msup><mml:mrow><mml:mo>[</mml:mo><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>b</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>]</mml:mo></mml:mrow><mml:mi mathvariant="normal">T</mml:mi></mml:msup></mml:mrow></mml:math></inline-formula> be the five-dimensional vector of a pixel. Calculate the distance between each pixel and the seeds. The distance between the pixel and seed <inline-formula><mml:math id="mm47"><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is shown as follows:<disp-formula id="FD4-sensors-19-00479"><label>(4)</label><mml:math id="mm48"><mml:mrow><mml:mrow><mml:msub><mml:mi>D</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msqrt><mml:mrow><mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>l</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mrow><mml:mo>+</mml:mo><mml:mo>(</mml:mo></mml:mrow><mml:msub><mml:mi>a</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msup><mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mrow><mml:mo>+</mml:mo><mml:mo>(</mml:mo></mml:mrow><mml:msub><mml:mi>b</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>b</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msup><mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:msqrt></mml:mrow></mml:mrow></mml:math></disp-formula>
<disp-formula id="FD5-sensors-19-00479"><label>(5)</label><mml:math id="mm49"><mml:mrow><mml:mrow><mml:msub><mml:mi>D</mml:mi><mml:mrow><mml:mi>x</mml:mi><mml:mi>y</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msqrt><mml:mrow><mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mrow><mml:mo>+</mml:mo><mml:mo>(</mml:mo></mml:mrow><mml:msub><mml:mi>y</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msup><mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:msqrt></mml:mrow></mml:mrow></mml:math></disp-formula>
<disp-formula id="FD6-sensors-19-00479"><label>(6)</label><mml:math id="mm50"><mml:mrow><mml:mrow><mml:msub><mml:mi>D</mml:mi><mml:mi>S</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>D</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:mi>l</mml:mi><mml:mi>S</mml:mi></mml:mfrac><mml:msub><mml:mi>D</mml:mi><mml:mrow><mml:mi>x</mml:mi><mml:mi>y</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>The variable <italic>l</italic> controls the superpixels&#x02019; compactness. The larger the value <italic>l</italic> is, the more compact the cluster will be.</p><p>The above steps are repeated iteratively until convergence, which means that the cluster center of each pixel is no longer changing. <italic>l</italic> ranges from 1 to 20. Here, we chose <inline-formula><mml:math id="mm51"><mml:mrow><mml:mrow><mml:mi>l</mml:mi><mml:mo>=</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula>, which roughly matches the empirically perceptually meaningful <inline-formula><mml:math id="mm52"><mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> distance and provides good balance between spatial proximity and color similarity.</p><p>After the iterative optimization, superpixel multi-connected conditions may occur. Some superpixels&#x02019; sizes may be too small, a single superpixel may be segmented into a plurality of discontinuous superpixels, etc., which can be solved by enhancing the connectivity of the superpixels. The solution is to reassign discrete and small-size superpixels to their adjacent superpixels until all the pixels have been traversed.</p></sec><sec id="sec2dot3-sensors-19-00479"><title>2.3. Robust Principal Component Analysis</title><p>Decomposing the matrix into low-rank and sparse parts can separate the main components from outliers or noise, which is suitable for mining low-dimensional manifold structures in high-dimensional data. We stack the pixels in the same homogeneous region into a matrix <inline-formula><mml:math id="mm53"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:msub><mml:mi mathvariant="bold">n</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>, where <inline-formula><mml:math id="mm54"><mml:mrow><mml:msub><mml:mi mathvariant="bold">n</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the pixel number in the <italic>k</italic>th homogeneous region. Since the matrix has low-rank property, a low-rank matrix recovery algorithm is employed to recover the low-dimensional structure of all pixels in the homogeneous region. After recovering the low-rank matrix, the low-rank matrices are combined together into an integrated low-rank graph. The low-rank matrix restored by low-rank representation is a square matrix, while the dimensionality of the low-rank matrix restored by robust principal component analysis is the same as the original <inline-formula><mml:math id="mm55"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:msub><mml:mi mathvariant="bold">n</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>. Since the number of pixels in each homogeneous region is different, robust principal component analysis is suitable for restoring the low-dimensional structures of all pixels in a homogeneous region here.</p><p>Principal Component Analysis is a fundamental operation in data analysis, which assumes that the data approximately lies in a low-dimensional linear subspace [<xref rid="B16-sensors-19-00479" ref-type="bibr">16</xref>]. The success and popularity of PCA reveals the ubiquity of low-rank matrices. When the data are slightly damaged by small noise, it can be calculated stably and efficiently by singular value decomposition. However, noise and outliers limit PCA&#x02019;s performance and applicability in real scenarios.</p><p>Suppose we stack samples as column vectors of a matrix <inline-formula><mml:math id="mm56"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>n</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>. The problem of classical PCA is to seek the best estimate of <inline-formula><mml:math id="mm57"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow></mml:math></inline-formula> that minimizes the difference between <inline-formula><mml:math id="mm58"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm59"><mml:mrow><mml:mi mathvariant="bold">X</mml:mi></mml:mrow></mml:math></inline-formula>:<disp-formula id="FD7-sensors-19-00479"><label>(7)</label><mml:math id="mm60"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:munder><mml:mi>min</mml:mi><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:munder><mml:mspace width="0.277778em"/><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mi>F</mml:mi></mml:msub></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mi>s</mml:mi><mml:mo>.</mml:mo><mml:mi>t</mml:mi><mml:mo>.</mml:mo><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mi>r</mml:mi><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>k</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>)</mml:mo><mml:mo>&#x02264;</mml:mo><mml:mi>r</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>=</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>+</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula>
where <inline-formula><mml:math id="mm61"><mml:mrow><mml:msub><mml:mrow><mml:mo stretchy="false">&#x02225;</mml:mo><mml:mo>.</mml:mo><mml:mo stretchy="false">&#x02225;</mml:mo></mml:mrow><mml:mi>F</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> denotes the Frobenius norm. <italic>r</italic> is the upper-bound rank of the low-rank matrix <inline-formula><mml:math id="mm62"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow></mml:math></inline-formula>. This problem can be efficiently solved via singular value decomposition (SVD) when the noise <inline-formula><mml:math id="mm63"><mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:math></inline-formula> is independent and identically distributed (i.i.d.) small Gaussian noise. A major disadvantage of classic PCA is its robustness to grossly corrupted or outlying observations [<xref rid="B16-sensors-19-00479" ref-type="bibr">16</xref>]. In fact, even though only one element in the matrix changes, the obtained estimation matrix is far from the ground truth.</p><p>Recently, Wright et al. [<xref rid="B37-sensors-19-00479" ref-type="bibr">37</xref>] proposed an idealized robust PCA model to extract low-rank structures from highly polluted data. In contrast to the traditional PCA, the proposed RPCA can exactly extract the low-rank matrix and the sparse error <inline-formula><mml:math id="mm64"><mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:math></inline-formula> (relative to <inline-formula><mml:math id="mm65"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow></mml:math></inline-formula>). The robust principal component analysis model is expressed as follows:<disp-formula id="FD8-sensors-19-00479"><label>(8)</label><mml:math id="mm66"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:munder><mml:mi>min</mml:mi><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:munder><mml:mspace width="0.277778em"/><mml:mi>r</mml:mi><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>k</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>+</mml:mo><mml:mi>&#x003bb;</mml:mi><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mn>0</mml:mn></mml:msub></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mi>s</mml:mi><mml:mo>.</mml:mo><mml:mi>t</mml:mi><mml:mo>.</mml:mo><mml:mspace width="0.277778em"/><mml:mi mathvariant="bold">X</mml:mi><mml:mo>=</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>+</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula>
where <inline-formula><mml:math id="mm67"><mml:mrow><mml:mi>&#x003bb;</mml:mi></mml:mrow></mml:math></inline-formula> is to balance low-rank matrix <inline-formula><mml:math id="mm68"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow></mml:math></inline-formula> and the error matrix <inline-formula><mml:math id="mm69"><mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:math></inline-formula>. We recover the pair <inline-formula><mml:math id="mm70"><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> that were generated from data <inline-formula><mml:math id="mm71"><mml:mrow><mml:mi mathvariant="bold">X</mml:mi></mml:mrow></mml:math></inline-formula>. Unfortunately, Equation (<xref ref-type="disp-formula" rid="FD8-sensors-19-00479">8</xref>) is a very non-convex optimization problem with no valid solution. Replacing the rank function with the kernel norm, and replacing the <inline-formula><mml:math id="mm72"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>0</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm with the <inline-formula><mml:math id="mm73"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm, results in the following relaxing Equation (<xref ref-type="disp-formula" rid="FD8-sensors-19-00479">8</xref>) convex surrogate:<disp-formula id="FD9-sensors-19-00479"><label>(9)</label><mml:math id="mm74"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:munder><mml:mi>min</mml:mi><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:munder><mml:mspace width="0.277778em"/><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">Z</mml:mi></mml:mfenced><mml:mo>*</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003bb;</mml:mi><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mi>s</mml:mi><mml:mo>.</mml:mo><mml:mi>t</mml:mi><mml:mo>.</mml:mo><mml:mspace width="0.277778em"/><mml:mi mathvariant="bold">X</mml:mi><mml:mo>=</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>+</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula></p><p>The optimization in Equation (<xref ref-type="disp-formula" rid="FD9-sensors-19-00479">9</xref>) can be treated as a general convex optimization problem. Currently, the optimization algorithms of RPCA mainly contain Iterative Thresholding (IT), Accelerated Proximal Gradient (APG), and DUAL. However, the iterative thresholding algorithm proposed in [<xref rid="B37-sensors-19-00479" ref-type="bibr">37</xref>] converges slowly. The APG algorithm is similar to the IT algorithm, but the number of iterations is significantly reduced. In addition, the DUAL algorithm does not require the complete singular value decomposition of the matrix, so it has better scalability than the APG algorithm. Recently, some researchers proposed Augmented Lagrangian Multiplier (ALM) algorithm, which has a faster speed than previous algorithms. The exact ALM (EALM) method turns out that it has a satisfactory <italic>Q</italic>-linear convergence speed, whereas the APG is theoretically only sub-linear. A slight improvement over the exact ALM leads to an inexact ALM (IALM) method, which converges as fast as the exact ALM. Therefore, the IALM method is applied to obtain the optimization here.</p></sec></sec><sec sec-type="methods" id="sec3-sensors-19-00479"><title>3. Methodology</title><p>In the above section, we discuss the simple linear iterative clustering and robust principle component analysis. Furthermore, labeled and unlabeled samples can be exploited simultaneously by depicting the underlying superpixels&#x02019; low-rank subspace structure. Considering this, we have attempted to decipher our present work the superpixel segmentation and <inline-formula><mml:math id="mm75"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm pobust principal component analysis feature extraction model.</p><sec id="sec3dot1-sensors-19-00479"><title>3.1. Superpixel Segmentation and <inline-formula><mml:math id="mm76"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-Norm Robust Principal Component Analysis</title><p>Hyperspectral images preserve low-rank properties, but the category composition corresponding to a hyperspectral image is very complicated in practical applications. The robust principal component analysis model supposes that the data lie in one low-rank subspace, which makes it difficult to characterize the structure of the data with multiple subspaces. Hence, we explore the robust principal component analysis via superpixel, which is usually defined as a homogeneous region. Here, we employ the SLIC superpixel segmentation method to segment the hyperspectral image into spatially homogeneous regions. Each superpixel is a unit with consistent visual perception, and the pixels in one superpixel are almost identical in features and belong to the same class. Initially, we extract the information of the HSIs by image fusion and recursive filtering [<xref rid="B12-sensors-19-00479" ref-type="bibr">12</xref>].</p><p>Suppose <inline-formula><mml:math id="mm77"><mml:mrow><mml:mrow><mml:mi>R</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>r</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>r</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi>r</mml:mi><mml:mi>D</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi>M</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>D</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> represent the original hyperspectral image, which has <italic>M</italic> pixels and <italic>D</italic>-dimensional bands. We segment the whole hyperspectral bands into multiple subsets spectrally. Each subset is composed of <italic>L</italic> contiguous bands. The number of subsets <inline-formula><mml:math id="mm78"><mml:mrow><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>=</mml:mo><mml:mfenced separators="" open="&#x0230a;" close="&#x0230b;"><mml:mrow><mml:mi>D</mml:mi><mml:mo stretchy="false">/</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mfenced></mml:mrow></mml:mrow></mml:math></inline-formula>, which represents the largest integer not greater than <inline-formula><mml:math id="mm79"><mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo stretchy="false">/</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>. The <italic>i</italic>th (<inline-formula><mml:math id="mm80"><mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mo>(</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:mi mathvariant="script">N</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>) subset is shown as follows:<disp-formula id="FD10-sensors-19-00479"><label>(10)</label><mml:math id="mm81"><mml:mrow><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">P</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mfenced separators="" open="{" close=""><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>r</mml:mi><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02217;</mml:mo><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi>r</mml:mi><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02217;</mml:mo><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>,</mml:mo><mml:mspace width="4pt"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="4pt"/><mml:mrow><mml:mo>(</mml:mo><mml:mi>L</mml:mi><mml:mo>&#x02217;</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02264;</mml:mo><mml:mi>D</mml:mi></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>r</mml:mi><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02217;</mml:mo><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi>r</mml:mi><mml:mi>D</mml:mi></mml:msub><mml:mo>)</mml:mo><mml:mo>,</mml:mo><mml:mspace width="4pt"/><mml:mi>o</mml:mi><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>w</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi><mml:mo>.</mml:mo></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Afterwards, the adjacent bands of each subset are fused by an image fusion method (i.e., the averaging method). For example, the <italic>i</italic>th fusion band, that is, the image fusion feature <inline-formula><mml:math id="mm82"><mml:mrow><mml:msub><mml:mi>F</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, is as follows:<disp-formula id="FD11-sensors-19-00479"><label>(11)</label><mml:math id="mm83"><mml:mrow><mml:mrow><mml:msup><mml:mi>Q</mml:mi><mml:mi>i</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>n</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:msubsup><mml:msubsup><mml:mi mathvariant="bold">P</mml:mi><mml:mi>n</mml:mi><mml:mi>i</mml:mi></mml:msubsup></mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfrac></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Here, <inline-formula><mml:math id="mm84"><mml:mrow><mml:msubsup><mml:mi mathvariant="bold">P</mml:mi><mml:mi>n</mml:mi><mml:mi>i</mml:mi></mml:msubsup></mml:mrow></mml:math></inline-formula> refers to the <italic>n</italic>th band in the <italic>i</italic>th subset of a hyperspectral image. <inline-formula><mml:math id="mm85"><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the band number in the <italic>i</italic>th subset. After image fusion, we remove the noise pixels and redundant information for each subset. Then, we transform the domain recursive filtering algorithm on <inline-formula><mml:math id="mm86"><mml:mrow><mml:msup><mml:mrow><mml:mi>Q</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msup></mml:mrow></mml:math></inline-formula> to obtain the <italic>i</italic>th feature:<disp-formula id="FD12-sensors-19-00479"><label>(12)</label><mml:math id="mm87"><mml:mrow><mml:mrow><mml:msup><mml:mi>O</mml:mi><mml:mi>i</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="script">RF</mml:mi><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>s</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>r</mml:mi></mml:msub></mml:mrow></mml:msub><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:msup><mml:mi>Q</mml:mi><mml:mi>i</mml:mi></mml:msup><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Here, <inline-formula><mml:math id="mm88"><mml:mrow><mml:mi mathvariant="script">RF</mml:mi></mml:mrow></mml:math></inline-formula> represents the domain recursive filtering algorithm. <inline-formula><mml:math id="mm89"><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>s</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the filter&#x02019;s spatial standard deviation, and <inline-formula><mml:math id="mm90"><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>r</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is defined as the range standard deviation [<xref rid="B60-sensors-19-00479" ref-type="bibr">60</xref>]. Then, we get the feature image <inline-formula><mml:math id="mm91"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">O</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo stretchy="false">[</mml:mo><mml:msup><mml:mrow><mml:mi>O</mml:mi></mml:mrow><mml:mn>1</mml:mn></mml:msup><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msup><mml:mrow><mml:mi>O</mml:mi></mml:mrow><mml:mi mathvariant="script">N</mml:mi></mml:msup><mml:mo stretchy="false">]</mml:mo></mml:mrow><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mrow><mml:mi mathvariant="double-struck">R</mml:mi></mml:mrow><mml:mrow><mml:mi>M</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi mathvariant="script">N</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>.</p><p>Image fusion and recursive filtering (IFRF) is a simple yet powerful feature extraction method that aims at finding the best subset of hyperspectral bands that provide high classes&#x02019; separability. In other words, the IFRF feature method is used to select better bands that remove the noise and redundant information simultaneously [<xref rid="B12-sensors-19-00479" ref-type="bibr">12</xref>]. Hence, we preprocess the HSIs firstly by IFRF to eliminate redundant information.</p><p>Let <inline-formula><mml:math id="mm92"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>=</mml:mo><mml:msup><mml:mi mathvariant="bold">O</mml:mi><mml:mi mathvariant="normal">T</mml:mi></mml:msup><mml:mo>=</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>M</mml:mi></mml:msub><mml:mo>]</mml:mo></mml:mrow><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> be the preprocessed features vector, where <inline-formula><mml:math id="mm93"><mml:mrow><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> represents a pixel with <inline-formula><mml:math id="mm94"><mml:mrow><mml:mi mathvariant="script">N</mml:mi></mml:mrow></mml:math></inline-formula> band numbers in the hyperspectral image, and <italic>M</italic> is the pixels number. We begin by sampling the <italic>K</italic> cluster centers and moving them to the lowest gradient position in the <inline-formula><mml:math id="mm95"><mml:mrow><mml:mrow><mml:mn>3</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>3</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> neighborhood of the cluster centers. The squared Euclidean norm of the image is calculated by Equation (<xref ref-type="disp-formula" rid="FD13-sensors-19-00479">13</xref>):<disp-formula id="FD13-sensors-19-00479"><label>(13)</label><mml:math id="mm96"><mml:mrow><mml:mrow><mml:mrow><mml:mi mathvariant="normal">G</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="normal">x</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="normal">y</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo></mml:mrow><mml:msup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">I</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="normal">x</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mi mathvariant="normal">y</mml:mi><mml:mo>)</mml:mo><mml:mo>&#x02212;</mml:mo></mml:mrow><mml:mi mathvariant="bold">I</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mi mathvariant="normal">y</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:msup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">I</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="normal">x</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="normal">y</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo><mml:mo>&#x02212;</mml:mo></mml:mrow><mml:mi mathvariant="bold">I</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="normal">x</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="normal">y</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mrow></mml:math></disp-formula>
where <inline-formula><mml:math id="mm97"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">I</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="normal">x</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="normal">y</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> represents the <inline-formula><mml:math id="mm98"><mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mo>,</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> vector corresponding to the pixel at position <inline-formula><mml:math id="mm99"><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, and <inline-formula><mml:math id="mm100"><mml:mrow><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mo>.</mml:mo></mml:mfenced></mml:mrow></mml:math></inline-formula> is the <inline-formula><mml:math id="mm101"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>2</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm. This takes both color and intensity information into account.</p><p>Each pixel in the image is associated with the nearest cluster center whose search area overlaps the pixel. After each pixel is associated with its nearest cluster center, a new center is computed as the average <inline-formula><mml:math id="mm102"><mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mi>a</mml:mi><mml:mi>b</mml:mi><mml:mi>x</mml:mi><mml:mi>y</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> vector of all the pixels in the cluster. Then, we iteratively associate the pixel with the nearest cluster center and recalculate the cluster center until convergence. We enforce the connection by relabeling the disjoint segmentations with their largest neighboring cluster&#x02019;s label. The simple linear iterative clustering algorithm is summarized in Algorithm 1.</p><array orientation="portrait"><tbody><tr><td align="left" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1"><bold>Algorithm 1</bold> SLIC superpixel segmentation.</td></tr><tr><td align="left" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<list list-type="simple"><list-item><label><bold>Input:</bold>&#x000a0;</label><p>Processed HSIs image <inline-formula><mml:math id="mm103"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>, Desired number of approximately superpixels <italic>K</italic>.</p></list-item><list-item><label>&#x000a0;&#x000a0;1:</label><p>Initialize cluster centers <inline-formula><mml:math id="mm104"><mml:mrow><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msup><mml:mrow><mml:mo>[</mml:mo><mml:msub><mml:mi>l</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>b</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>x</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>]</mml:mo></mml:mrow><mml:mi mathvariant="normal">T</mml:mi></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> by sampling pixels at regular grid steps <italic>S</italic>.</p></list-item><list-item><label>&#x000a0;&#x000a0;2:</label><p>Move cluster centers to the lowest gradient position in a <inline-formula><mml:math id="mm105"><mml:mrow><mml:mrow><mml:mn>3</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>3</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> neighborhood.</p></list-item><list-item><label>&#x000a0;&#x000a0;3:</label><p>Set label <inline-formula><mml:math id="mm106"><mml:mrow><mml:mrow><mml:mi>l</mml:mi><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> for each pixel <italic>i</italic>.</p></list-item><list-item><label>&#x000a0;&#x000a0;4:</label><p>Set distance <inline-formula><mml:math id="mm107"><mml:mrow><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo></mml:mrow><mml:mo>&#x0221e;</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> for each pixel <italic>i</italic>.</p></list-item><list-item><label>&#x000a0;&#x000a0;5:</label><p>
<bold>Repeat</bold>
</p></list-item><list-item><label>&#x000a0;&#x000a0;6:</label><p><bold>for</bold> each cluster center <inline-formula><mml:math id="mm108"><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>
<bold>do</bold></p></list-item><list-item><label>&#x000a0;&#x000a0;7:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;<bold>for</bold> each pixel <italic>i</italic> in a <inline-formula><mml:math id="mm109"><mml:mrow><mml:mrow><mml:mrow><mml:mn>2</mml:mn><mml:mi>S</mml:mi></mml:mrow><mml:mo>&#x000d7;</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mi>S</mml:mi></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> region around <inline-formula><mml:math id="mm110"><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>
<bold>do</bold></p></list-item><list-item><label>&#x000a0;&#x000a0;8:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;Compute the distance <italic>D</italic> between <inline-formula><mml:math id="mm111"><mml:mrow><mml:msub><mml:mi>C</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <italic>i</italic>.</p></list-item><list-item><label>&#x000a0;&#x000a0;9:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;<bold>if</bold>
<inline-formula><mml:math id="mm112"><mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>&#x0003c;</mml:mo><mml:mi>d</mml:mi><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>
<bold>then</bold></p></list-item><list-item><label>&#x000a0;10:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;set <inline-formula><mml:math id="mm113"><mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mi>D</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula></p></list-item><list-item><label>&#x000a0;11:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;set <inline-formula><mml:math id="mm114"><mml:mrow><mml:mrow><mml:mi>l</mml:mi><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula></p></list-item><list-item><label>&#x000a0;12:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;&#x000a0;<bold>end if</bold></p></list-item><list-item><label>&#x000a0;13:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;<bold>end for</bold></p></list-item><list-item><label>&#x000a0;14:</label><p>
<bold>end for</bold>
</p></list-item><list-item><label>&#x000a0;15:</label><p>Compute new cluster centers.</p></list-item><list-item><label>&#x000a0;16:</label><p>Compute residual error <italic>E</italic>.</p></list-item><list-item><label>&#x000a0;17:</label><p><bold>Until</bold><inline-formula><mml:math id="mm115"><mml:mrow><mml:mrow><mml:mi>E</mml:mi><mml:mo>&#x02264;</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>threshold.</p></list-item><list-item><label>&#x000a0;18:</label><p>Enforce connectivity.</p></list-item><list-item><label><bold>Output:</bold>&#x000a0;</label><p>Superpixel segmentations.</p></list-item></list>
</td></tr></tbody></array><p>Hyperspectral images are segmented into many irregularly homogeneous regions. We stack the pixels in one homogeneous region into a matrix. We record the segment image as <inline-formula><mml:math id="mm116"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>=</mml:mo><mml:mo>{</mml:mo><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:mo>}</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, where <inline-formula><mml:math id="mm117"><mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mo>{</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:mi>m</mml:mi><mml:mo>}</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> is the index of the superpixels. <italic>m</italic> is the exact total number of superpixels in <inline-formula><mml:math id="mm118"><mml:mrow><mml:mi mathvariant="bold">X</mml:mi></mml:mrow></mml:math></inline-formula>. <inline-formula><mml:math id="mm119"><mml:mrow><mml:msub><mml:mi mathvariant="bold">n</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the number of pixels in the superpixel <inline-formula><mml:math id="mm120"><mml:mrow><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, and each column represents the bands of one pixel. Due to the low-rank attribute of the data, robust principal component analysis is used to extract the low-dimensional structure of all pixels in the homogeneous region. Then, the observed data matrix <inline-formula><mml:math id="mm121"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:msub><mml:mi mathvariant="bold">n</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> can be decomposed into a low-rank matrix and a sparse matrix. That is, <inline-formula><mml:math id="mm122"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:mrow></mml:math></inline-formula>, where <inline-formula><mml:math id="mm123"><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> represents the low-rank matrix of <inline-formula><mml:math id="mm124"><mml:mrow><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, and <inline-formula><mml:math id="mm125"><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> indicates the sparse noise matrix. Further, the RPCA optimization problem for each superpixel region is converted to the following form:<disp-formula id="FD14-sensors-19-00479"><label>(14)</label><mml:math id="mm126"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:munder><mml:mi>min</mml:mi><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:munder><mml:mspace width="0.277778em"/><mml:msub><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfenced><mml:mo>*</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003bb;</mml:mi><mml:msub><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfenced><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mi>s</mml:mi><mml:mo>.</mml:mo><mml:mi>t</mml:mi><mml:mo>.</mml:mo><mml:mspace width="0.277778em"/><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula></p><p>In the RPCA model, noise is required to be sparse, and the structural information of the noise is not considered. However, in machine learning or image processing fields, each column (or row) of the matrix has some meaning (e.g., a picture, and a signal data, etc.). Here, each column represents a pixel, which causes the noise to be sparse in column (or row). This structural information cannot be represented by the definition of an <inline-formula><mml:math id="mm127"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm. To generate structural sparsity, the <inline-formula><mml:math id="mm128"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm is proposed in the robust principal component analysis.</p><p>Unlike the <inline-formula><mml:math id="mm129"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm, the <inline-formula><mml:math id="mm130"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm produces sparsity based on columns (or rows). For the matrix <inline-formula><mml:math id="mm131"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>n</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>, its <inline-formula><mml:math id="mm132"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>- norm is [<xref rid="B61-sensors-19-00479" ref-type="bibr">61</xref>,<xref rid="B62-sensors-19-00479" ref-type="bibr">62</xref>]:<disp-formula id="FD15-sensors-19-00479"><label>(15)</label><mml:math id="mm133"><mml:mrow><mml:mrow><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">X</mml:mi></mml:mfenced><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>n</mml:mi></mml:munderover><mml:msqrt><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>m</mml:mi></mml:munderover><mml:msubsup><mml:mi mathvariant="bold">x</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:msqrt><mml:mo>=</mml:mo><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>m</mml:mi></mml:munderover><mml:msub><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mrow><mml:mo>:</mml:mo><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mfenced><mml:mn>2</mml:mn></mml:msub></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Thus, the <inline-formula><mml:math id="mm134"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm is the sum of the <inline-formula><mml:math id="mm135"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>2</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm of column vectors, which is also a measure of joint sparsity between vectors. For data that are interfered by structured noise, the following <inline-formula><mml:math id="mm136"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm RPCA model in Equation (16) is very suitable:<disp-formula id="FD16-sensors-19-00479"><label>(16)</label><mml:math id="mm137"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:munder><mml:mi>min</mml:mi><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:munder><mml:mspace width="0.277778em"/><mml:msub><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfenced><mml:mo>*</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003bb;</mml:mi><mml:msub><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mfenced><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mi>s</mml:mi><mml:mo>.</mml:mo><mml:mi>t</mml:mi><mml:mo>.</mml:mo><mml:mspace width="0.277778em"/><mml:msub><mml:mi mathvariant="bold">X</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula></p><p>In contrast to the original RPCA model, we call the error term with <inline-formula><mml:math id="mm138"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm in Equation (16) as RPCA<sub>2,1</sub>. Here, the inexact augmented Lagrangian multiplier (ALM) algorithm [<xref rid="B63-sensors-19-00479" ref-type="bibr">63</xref>,<xref rid="B64-sensors-19-00479" ref-type="bibr">64</xref>] is utilized for the optimal solution <inline-formula><mml:math id="mm140"><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>.</p><p>The augmented Lagrangian multiplier method solves the optimization problem by minimizing the Lagrangian function as follows:<disp-formula id="FD17-sensors-19-00479"><label>(17)</label><mml:math id="mm141"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mi>L</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">Y</mml:mi><mml:mo>,</mml:mo><mml:mi>&#x003bc;</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">Z</mml:mi></mml:mfenced><mml:mo>*</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003bb;</mml:mi><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mfenced separators="" open="&#x02329;" close="&#x0232a;"><mml:mrow><mml:mi mathvariant="bold">Y</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:mfenced><mml:mo>+</mml:mo><mml:mfrac><mml:mi>&#x003bc;</mml:mi><mml:mn>2</mml:mn></mml:mfrac><mml:msubsup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:mfenced><mml:mi>F</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula>
where <inline-formula><mml:math id="mm142"><mml:mrow><mml:mi mathvariant="bold">Y</mml:mi></mml:mrow></mml:math></inline-formula> is defined as Lagrangian multipliers and <inline-formula><mml:math id="mm143"><mml:mrow><mml:mrow><mml:mi>&#x003bc;</mml:mi><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> is the penalty parameter. The sub-problem for all variables is convex, which can supply a relevant and unique solution. Alternate iterations <inline-formula><mml:math id="mm144"><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="mm145"><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>, and <inline-formula><mml:math id="mm146"><mml:mrow><mml:mi mathvariant="bold">Y</mml:mi></mml:mrow></mml:math></inline-formula>:<disp-formula id="FD18-sensors-19-00479"><label>(18)</label><mml:math id="mm147"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:munder><mml:mi>L</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mi>&#x003bc;</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mspace width="0.277778em"/></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:munder><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">Z</mml:mi></mml:mfenced><mml:mo>*</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:mfenced separators="" open="&#x02329;" close="&#x0232a;"><mml:mrow><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mo>+</mml:mo><mml:mfrac><mml:mi>&#x003bc;</mml:mi><mml:mn>2</mml:mn></mml:mfrac><mml:msubsup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mi>F</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:munder><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">Z</mml:mi></mml:mfenced><mml:mo>*</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:mi>&#x003bc;</mml:mi><mml:mn>2</mml:mn></mml:mfrac><mml:msubsup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mi>&#x003bc;</mml:mi></mml:mfrac><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfenced><mml:mi>F</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:munder><mml:mfrac><mml:mn>1</mml:mn><mml:mi>&#x003bc;</mml:mi></mml:mfrac><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">Z</mml:mi></mml:mfenced><mml:mo>*</mml:mo></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mn>2</mml:mn></mml:mfrac><mml:msubsup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mi>&#x003bc;</mml:mi></mml:mfrac><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfenced><mml:mi>F</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula>
<disp-formula id="FD19-sensors-19-00479"><label>(19)</label><mml:math id="mm148"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:munder><mml:mi>L</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mi>&#x003bc;</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mspace width="0.277778em"/></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:munder><mml:mi>&#x003bb;</mml:mi><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mfenced separators="" open="&#x02329;" close="&#x0232a;"><mml:mrow><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mo>+</mml:mo><mml:mfrac><mml:mi>&#x003bc;</mml:mi><mml:mn>2</mml:mn></mml:mfrac><mml:msubsup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mfenced><mml:mi>F</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:munder><mml:mi>&#x003bb;</mml:mi><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:mi>&#x003bc;</mml:mi><mml:mn>2</mml:mn></mml:mfrac><mml:msubsup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">E</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>+</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mi>&#x003bc;</mml:mi></mml:mfrac><mml:mi mathvariant="bold">Y</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfenced><mml:mi>F</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:munder><mml:mfrac><mml:mi>&#x003bb;</mml:mi><mml:mi>&#x003bc;</mml:mi></mml:mfrac><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mn>2</mml:mn></mml:mfrac><mml:msubsup><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:mi mathvariant="bold">E</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>+</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mi>&#x003bc;</mml:mi></mml:mfrac><mml:mi mathvariant="bold">Y</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfenced><mml:mi>F</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula></p><p>Note that <inline-formula><mml:math id="mm149"><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm150"><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> are convergent to <inline-formula><mml:math id="mm151"><mml:mrow><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm152"><mml:mrow><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup></mml:mrow></mml:math></inline-formula>, respectively. Then, we update <inline-formula><mml:math id="mm153"><mml:mrow><mml:mi mathvariant="bold">Y</mml:mi></mml:mrow></mml:math></inline-formula> as follows:<disp-formula id="FD20-sensors-19-00479"><label>(20)</label><mml:math id="mm154"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup><mml:mo>)</mml:mo></mml:mrow><mml:mspace width="0.277778em"/></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Finally, parameter <inline-formula><mml:math id="mm155"><mml:mrow><mml:mi>&#x003bc;</mml:mi></mml:mrow></mml:math></inline-formula> is updated:<disp-formula id="FD21-sensors-19-00479"><label>(21)</label><mml:math id="mm156"><mml:mrow><mml:mrow><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfenced separators="" open="{" close=""><mml:mtable><mml:mtr><mml:mtd><mml:mrow><mml:mi>&#x003c1;</mml:mi><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.277778em"/><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup></mml:mrow></mml:mfenced><mml:mi>F</mml:mi></mml:msub><mml:mo stretchy="false">/</mml:mo><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">X</mml:mi></mml:mfenced><mml:mi>F</mml:mi></mml:msub><mml:mo>&#x0003c;</mml:mo><mml:mi>&#x003b5;</mml:mi><mml:mspace width="0.277778em"/></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mi>o</mml:mi><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>w</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:mrow></mml:math></disp-formula>
where <inline-formula><mml:math id="mm157"><mml:mrow><mml:mrow><mml:mi>&#x003c1;</mml:mi><mml:mo>&#x0003e;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> is a constant and <inline-formula><mml:math id="mm158"><mml:mrow><mml:mrow><mml:mi>&#x003b5;</mml:mi><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula> is a relatively small number.</p><p>The optimization process outline of the inexact ALM method [<xref rid="B63-sensors-19-00479" ref-type="bibr">63</xref>] is given in Algorithm 2.</p><p>The initialization <inline-formula><mml:math id="mm159"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mo>=</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo stretchy="false">/</mml:mo><mml:mi>J</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> is defined as
<disp-formula id="FD22-sensors-19-00479"><label>(22)</label><mml:math id="mm160"><mml:mrow><mml:mrow><mml:mi>J</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mi>max</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">X</mml:mi></mml:mfenced><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msup><mml:mi>&#x003bb;</mml:mi><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">X</mml:mi></mml:mfenced><mml:mo>&#x0221e;</mml:mo></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></disp-formula>
where <inline-formula><mml:math id="mm161"><mml:mrow><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mo>&#x000b7;</mml:mo></mml:mfenced><mml:mo>&#x0221e;</mml:mo></mml:msub></mml:mrow></mml:math></inline-formula> is the maximum absolute value of the matrix entries.</p><array orientation="portrait"><tbody><tr><td align="left" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1" colspan="1"><bold>Algorithm 2</bold> Inexact ALM method for solving RPCA.</td></tr><tr><td align="left" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<list list-type="simple"><list-item><label><bold>Input:</bold>&#x000a0;</label><p>Observation matrix <inline-formula><mml:math id="mm162"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi>m</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>n</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> and parameter <inline-formula><mml:math id="mm163"><mml:mrow><mml:mi>&#x003bb;</mml:mi></mml:mrow></mml:math></inline-formula> for local affinity.</p></list-item><list-item><label>&#x000a0;&#x000a0;1:</label><p>Initialize: <inline-formula><mml:math id="mm164"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mo>=</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo stretchy="false">/</mml:mo><mml:mi>J</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mo>=</mml:mo><mml:mn mathvariant="bold">0</mml:mn><mml:mo>;</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mn>0</mml:mn></mml:msub><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn><mml:mo>;</mml:mo><mml:mi>&#x003c1;</mml:mi><mml:mo>&#x0003e;</mml:mo><mml:mn>1</mml:mn><mml:mo>;</mml:mo><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><label>&#x000a0;&#x000a0;2:</label><p><bold>while</bold> not converged <bold>do</bold></p></list-item><list-item><label>&#x000a0;&#x000a0;3:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;//Lines 4-5 resolve <inline-formula><mml:math id="mm165"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:munder><mml:mi>L</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><label>&#x000a0;&#x000a0;4:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;<inline-formula><mml:math id="mm166"><mml:mrow><mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">U</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">S</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">V</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mi>s</mml:mi><mml:mi>v</mml:mi><mml:mi>d</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msup><mml:mrow><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>;</p></list-item><list-item><label>&#x000a0;&#x000a0;5:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;<inline-formula><mml:math id="mm167"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mi mathvariant="bold">U</mml:mi><mml:msub><mml:mi mathvariant="bold">S</mml:mi><mml:msup><mml:mrow><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup></mml:msub><mml:mrow><mml:mo>[</mml:mo><mml:mi mathvariant="bold">S</mml:mi><mml:mo>]</mml:mo></mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">V</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><label>&#x000a0;&#x000a0;6:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;//Line 7 resolves <inline-formula><mml:math id="mm168"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>min</mml:mi></mml:mrow><mml:mi mathvariant="bold">E</mml:mi></mml:munder><mml:mi>L</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><label>&#x000a0;&#x000a0;7:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;<inline-formula><mml:math id="mm169"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="bold">S</mml:mi><mml:msup><mml:mrow><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msup><mml:mrow><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><label>&#x000a0;&#x000a0;8:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;<inline-formula><mml:math id="mm170"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi mathvariant="bold">Y</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">E</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mo>*</mml:mo></mml:msup><mml:mo>)</mml:mo></mml:mrow><mml:mspace width="0.277778em"/><mml:mo>;</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mi>&#x003c1;</mml:mi><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><label>&#x000a0;&#x000a0;9:</label><p>&#x000a0;&#x000a0;&#x000a0;&#x000a0;<inline-formula><mml:math id="mm171"><mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>&#x02190;</mml:mo><mml:mi>i</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></list-item><list-item><label>&#x000a0;10:</label><p>
<bold>end while</bold>
</p></list-item><list-item><label><bold>Output:</bold>&#x000a0;</label><p>The low-rank matrix <inline-formula><mml:math id="mm172"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow></mml:math></inline-formula>.</p></list-item></list>
</td></tr></tbody></array><p>After RPCA<sub>2,1</sub>, we get the low-rank matrix <inline-formula><mml:math id="mm174"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:msub><mml:mi mathvariant="bold">n</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula> of each superpixel. Then, we merge these low-rank matrices to a whole low-rank graph <inline-formula><mml:math id="mm175"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">Z</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:mo>}</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>M</mml:mi></mml:msub><mml:mo>]</mml:mo></mml:mrow><mml:mspace width="3.33333pt"/><mml:mo>&#x02208;</mml:mo><mml:mspace width="3.33333pt"/><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>, which is the low-rank representation of <inline-formula><mml:math id="mm176"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">X</mml:mi><mml:mo>=</mml:mo><mml:mo>[</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">x</mml:mi><mml:mi>M</mml:mi></mml:msub><mml:mo>]</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>.</p></sec><sec id="sec3dot2-sensors-19-00479"><title>3.2. HSIs&#x02019; Classification Based on the SURPCA<sub>2,1</sub> Graph</title><p>To overcome the &#x0201c;Curse of dimensionality&#x0201d; caused by the high dimension, we apply semi-supervised discriminant analysis to reduce the dimension. SDA is derived from linear discriminant analysis (LDA). The rejection matrix <inline-formula><mml:math id="mm178"><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:math></inline-formula> can represent a priori consistency assumptions according to the regularization term [<xref rid="B65-sensors-19-00479" ref-type="bibr">65</xref>]:<disp-formula id="FD23-sensors-19-00479"><label>(23)</label><mml:math id="mm179"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munder><mml:mrow><mml:mi>arg</mml:mi><mml:mo>&#x000a0;</mml:mo><mml:mi>max</mml:mi></mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:munder><mml:mfrac><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:msub><mml:mi>S</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:msub><mml:mi>S</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mo>+</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:mi>J</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi mathvariant="bold">a</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mfrac></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Here, <inline-formula><mml:math id="mm180"><mml:mrow><mml:msub><mml:mi>S</mml:mi><mml:mi>t</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the total class scatter matrix. Parameter <inline-formula><mml:math id="mm181"><mml:mrow><mml:mi>&#x003b1;</mml:mi></mml:mrow></mml:math></inline-formula> is used to balance the complexity and empirical loss of the model. Empirical loss <inline-formula><mml:math id="mm182"><mml:mrow><mml:mrow><mml:mi>J</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="bold">a</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> controls the learning complexity of the hypothesis family. The regularizer term <inline-formula><mml:math id="mm183"><mml:mrow><mml:mrow><mml:mi>J</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="bold">a</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> incorporates the prior knowledge into some particular applications. When a set of unlabeled examples available, we aim to construct a <inline-formula><mml:math id="mm184"><mml:mrow><mml:mrow><mml:mi>J</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="bold">a</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> incorporating the manifold structure.</p><p>Given a set of samples <inline-formula><mml:math id="mm185"><mml:mrow><mml:msubsup><mml:mfenced separators="" open="{" close="}"><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>M</mml:mi></mml:msubsup></mml:mrow></mml:math></inline-formula>, we can construct the graph <inline-formula><mml:math id="mm186"><mml:mrow><mml:mi mathvariant="bold">G</mml:mi></mml:mrow></mml:math></inline-formula> to represent the relationship between nearby samples by <italic>k</italic>NN. The samples <inline-formula><mml:math id="mm187"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm188"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> are considered as <italic>k</italic>-nearest neighbor if <inline-formula><mml:math id="mm189"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is among the <italic>k</italic>-nearest neighbor of <inline-formula><mml:math id="mm190"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> or <inline-formula><mml:math id="mm191"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is among the <italic>k</italic>-nearest neighbor of <inline-formula><mml:math id="mm192"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>. Here, we employ the most simple 0&#x02013;1 weighting [<xref rid="B21-sensors-19-00479" ref-type="bibr">21</xref>] methods to assign weights for <inline-formula><mml:math id="mm193"><mml:mrow><mml:mi mathvariant="bold">S</mml:mi></mml:mrow></mml:math></inline-formula>:<disp-formula id="FD24-sensors-19-00479"><label>(24)</label><mml:math id="mm194"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">S</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfenced separators="" open="{" close=""><mml:mtable><mml:mtr><mml:mtd><mml:mrow><mml:mn>1</mml:mn><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.277778em"/><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mspace width="0.277778em"/><mml:mi>o</mml:mi><mml:mi>r</mml:mi><mml:mspace width="0.277778em"/><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:mo>&#x02208;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow><mml:mn>0</mml:mn><mml:mo>,</mml:mo></mml:mrow></mml:mtd><mml:mtd><mml:mrow><mml:mi>o</mml:mi><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>w</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi><mml:mo>.</mml:mo></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:mrow></mml:math></disp-formula>
where <inline-formula><mml:math id="mm195"><mml:mrow><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> denotes <italic>k</italic>-nearest neighbor of <inline-formula><mml:math id="mm196"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula>.</p><p>The SDA model incorporate the prior knowledge in the regularization term <inline-formula><mml:math id="mm197"><mml:mrow><mml:mrow><mml:mi>J</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="bold">a</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula>, as follows:<disp-formula id="FD25-sensors-19-00479"><label>(25)</label><mml:math id="mm198"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mi>J</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="bold">a</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mstyle displaystyle="true"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:munder></mml:mstyle><mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:msub><mml:mi mathvariant="bold">S</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mo>&#x02003;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>=</mml:mo><mml:mn>2</mml:mn><mml:mstyle displaystyle="true"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mi>i</mml:mi></mml:munder></mml:mstyle><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi mathvariant="bold">D</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:msup><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mi mathvariant="bold">a</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>2</mml:mn><mml:mstyle displaystyle="true"><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:munder></mml:mstyle><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi mathvariant="bold">S</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:msubsup><mml:mi mathvariant="bold">z</mml:mi><mml:mi>j</mml:mi><mml:mi>T</mml:mi></mml:msubsup><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mo>&#x02003;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>=</mml:mo><mml:mn>2</mml:mn><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi mathvariant="bold">D</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">S</mml:mi></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mo>&#x02003;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>&#x000a0;</mml:mo><mml:mo>=</mml:mo><mml:mn>2</mml:mn><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mi mathvariant="bold">ZL</mml:mi><mml:msup><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula></p><p>The diagonal matrix <inline-formula><mml:math id="mm199"><mml:mrow><mml:mi mathvariant="bold">D</mml:mi></mml:mrow></mml:math></inline-formula>, <inline-formula><mml:math id="mm200"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">D</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mo>&#x02211;</mml:mo><mml:mi>j</mml:mi></mml:msub><mml:msub><mml:mi mathvariant="bold">S</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:math></inline-formula> is column (or row, since <inline-formula><mml:math id="mm201"><mml:mrow><mml:mi mathvariant="bold">S</mml:mi></mml:mrow></mml:math></inline-formula> is symmetric) sum of <inline-formula><mml:math id="mm202"><mml:mrow><mml:mi mathvariant="bold">S</mml:mi></mml:mrow></mml:math></inline-formula>. <inline-formula><mml:math id="mm203"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">L</mml:mi><mml:mo>=</mml:mo><mml:mi mathvariant="bold">D</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">S</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula> is the Laplacian matrix [<xref rid="B66-sensors-19-00479" ref-type="bibr">66</xref>]. Then, SDA objective function is:<disp-formula id="FD26-sensors-19-00479"><label>(26)</label><mml:math id="mm204"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mrow><mml:mi>o</mml:mi><mml:mi>p</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munder><mml:mi>max</mml:mi><mml:mi mathvariant="bold">a</mml:mi></mml:munder><mml:mfrac><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:msub><mml:mi>S</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msub><mml:mi>S</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:mi mathvariant="bold">ZL</mml:mi><mml:msup><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:mfrac></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>We obtain the projective vector <inline-formula><mml:math id="mm205"><mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:math></inline-formula> by maximizing the generalized eigenvalue problem where <italic>d</italic> is the weight matrix&#x02019;s rank for the labeled graph.
<disp-formula id="FD27-sensors-19-00479"><label>(27)</label><mml:math id="mm206"><mml:mrow><mml:mrow><mml:msub><mml:mi>S</mml:mi><mml:mi>b</mml:mi></mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mo>=</mml:mo><mml:mi>&#x003bb;</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msub><mml:mi>S</mml:mi><mml:mi>t</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:mi mathvariant="bold">ZL</mml:mi><mml:msup><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>The low-rank matrix extracted by RPCA captures the global correlation of the homogeneous region [<xref rid="B37-sensors-19-00479" ref-type="bibr">37</xref>,<xref rid="B39-sensors-19-00479" ref-type="bibr">39</xref>,<xref rid="B67-sensors-19-00479" ref-type="bibr">67</xref>,<xref rid="B68-sensors-19-00479" ref-type="bibr">68</xref>], whereas the <italic>k</italic>-nearest neighbor algorithm characterizes the local correlation of pixel points [<xref rid="B69-sensors-19-00479" ref-type="bibr">69</xref>]. The probability that the neighboring pixels belong to the same category is large, which corresponds to the spatial distribution structure of the hyperspectral image. Therefore, the SURPCA<sub>2,1</sub> graph can achieve good feature representations for graph-based semi-supervised dimensionality reduction.</p><p>Given a set of samples <inline-formula><mml:math id="mm208"><mml:mrow><mml:msubsup><mml:mfenced separators="" open="{" close="}"><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>y</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mo>)</mml:mo></mml:mrow></mml:mfenced><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>l</mml:mi></mml:msubsup></mml:mrow></mml:math></inline-formula> and unlabeled samples <inline-formula><mml:math id="mm209"><mml:mrow><mml:msubsup><mml:mfenced separators="" open="{" close="}"><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mi>l</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>m</mml:mi></mml:msubsup></mml:mrow></mml:math></inline-formula> with <italic>c</italic> classes, the <inline-formula><mml:math id="mm210"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> is the <italic>k</italic>th class&#x02019;s samples number. The algorithmic procedure of HSIs&#x02019; classification by applying the SLIC superpixel-based <inline-formula><mml:math id="mm211"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis method is stated in the following paragraphs:</p><p>Step 1&#x02003;Construct the adjacency graph: Construct the block low-rank and <italic>k</italic>NN graph <inline-formula><mml:math id="mm212"><mml:mrow><mml:mi mathvariant="bold">S</mml:mi></mml:mrow></mml:math></inline-formula> in Equation (<xref ref-type="disp-formula" rid="FD24-sensors-19-00479">24</xref>) for the regularization term. Furthermore, calculate the graph Laplacian <inline-formula><mml:math id="mm213"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">L</mml:mi><mml:mo>=</mml:mo><mml:mi mathvariant="bold">D</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="bold">S</mml:mi></mml:mrow></mml:mrow></mml:math></inline-formula>.</p><p>Step 2&#x02003;Construct the labeled graph: For the labeled graph, construct the matrix as:<disp-formula id="FD28-sensors-19-00479"><label>(28)</label><mml:math id="mm214"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">W</mml:mi><mml:mo>=</mml:mo><mml:mfenced separators="" open="[" close="]"><mml:mtable><mml:mtr><mml:mtd><mml:msub><mml:mi mathvariant="bold">W</mml:mi><mml:mrow><mml:mi mathvariant="bold">l</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi mathvariant="bold">l</mml:mi></mml:mrow></mml:msub></mml:mtd><mml:mtd><mml:mn>0</mml:mn></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mn>0</mml:mn></mml:mtd><mml:mtd><mml:mn>0</mml:mn></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Define identity matrix <inline-formula><mml:math id="mm215"><mml:mrow><mml:mrow><mml:mover accent="true"><mml:mi mathvariant="bold">I</mml:mi><mml:mo>&#x002dc;</mml:mo></mml:mover><mml:mo>=</mml:mo><mml:mfenced separators="" open="[" close="]"><mml:mtable><mml:mtr><mml:mtd><mml:mi mathvariant="bold">I</mml:mi></mml:mtd><mml:mtd><mml:mn>0</mml:mn></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mn>0</mml:mn></mml:mtd><mml:mtd><mml:mn>0</mml:mn></mml:mtd></mml:mtr></mml:mtable></mml:mfenced><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi>l</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>l</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>.</p><p>Step 3&#x02003;Eigen-problem: Calculate the eigenvectors for the generalized eigenvector problem:<disp-formula id="FD29-sensors-19-00479"><label>(29)</label><mml:math id="mm216"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">ZW</mml:mi><mml:msup><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mi mathvariant="bold">a</mml:mi><mml:mo>=</mml:mo><mml:mi>&#x003bb;</mml:mi><mml:mi mathvariant="bold">Z</mml:mi><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mover accent="true"><mml:mi mathvariant="bold">I</mml:mi><mml:mo>&#x002dc;</mml:mo></mml:mover><mml:mo>+</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:mi mathvariant="bold">L</mml:mi></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:msup><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow><mml:mi>T</mml:mi></mml:msup><mml:mi mathvariant="bold">a</mml:mi></mml:mrow></mml:mrow></mml:math></disp-formula></p><p><italic>d</italic> is the rank of <inline-formula><mml:math id="mm217"><mml:mrow><mml:mi mathvariant="bold">W</mml:mi></mml:mrow></mml:math></inline-formula>, and <inline-formula><mml:math id="mm218"><mml:mrow><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mi>d</mml:mi></mml:msub><mml:mo>}</mml:mo></mml:mrow></mml:mrow></mml:math></inline-formula> is the <italic>d</italic> eigenvectors.</p><p>Step 4&#x02003;Regularize discriminant analysis embedding: Let the transformation matrix <inline-formula><mml:math id="mm219"><mml:mrow><mml:mrow><mml:mi>A</mml:mi><mml:mo>=</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mn>1</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mn>2</mml:mn></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x022ef;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mi mathvariant="bold">a</mml:mi><mml:mi>d</mml:mi></mml:msub><mml:mo>]</mml:mo></mml:mrow><mml:mo>&#x02208;</mml:mo><mml:msup><mml:mi mathvariant="double-struck">R</mml:mi><mml:mrow><mml:mi mathvariant="script">N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>d</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mrow></mml:math></inline-formula>. Then, embedding the data into <italic>d</italic>-dimensional subspace,
<disp-formula id="FD30-sensors-19-00479"><label>(30)</label><mml:math id="mm220"><mml:mrow><mml:mrow><mml:mi mathvariant="bold">z</mml:mi><mml:mo>&#x02192;</mml:mo><mml:msup><mml:mi mathvariant="bold">z</mml:mi><mml:mo>&#x02032;</mml:mo></mml:msup><mml:mo>=</mml:mo><mml:msup><mml:mi>A</mml:mi><mml:mi mathvariant="normal">T</mml:mi></mml:msup><mml:mi mathvariant="bold">z</mml:mi></mml:mrow></mml:mrow></mml:math></disp-formula></p><p>Step 5&#x02003;Finally, apply the simple and ubiquitously-used classifiers nearest neighbor and Support Vector Machine (SVM) for classification.</p><p><xref ref-type="fig" rid="sensors-19-00479-f003">Figure 3</xref> shows a model diagram of the SURPCA<sub>2,1</sub> for hyperspectral image classification. The low-rank matrix measures the global correlation of the homogeneous regions, while the <italic>k</italic>NN algorithm preserves the local correlation of the pixels. The probability that adjacent pixels belong to the same category in the <italic>k</italic>NN is large, which corresponds to the spatial distribution structure of the hyperspectral image. Therefore, the SURPCA<sub>2,1</sub> graph can be used to achieve high-quality data representation.</p></sec></sec><sec id="sec4-sensors-19-00479"><title>4. Experiments and Analysis</title><p>To investigate the performance of the SLIC superpixel-based <inline-formula><mml:math id="mm223"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis graph model, we conducted extensive experiments on several real multi-class hyperspectral images. We performed experiments on a computer with CPU 2.60 GHz and 8 GB RAM.</p><sec id="sec4dot1-sensors-19-00479"><title>4.1. Experimental Setup</title><sec id="sec4dot1dot1-sensors-19-00479"><title>4.1.1. Hyperspectral Images</title><p>The Indian Pines (<uri xlink:href="https://purr.purdue.edu/publications/1947/usage?v=1">https://purr.purdue.edu/publications/1947/usage?v=1</uri>), Pavia University scene, and Salinas (<uri xlink:href="http://www.ehu.eus/ccwintco/index.php/Hyperspectral_Remote_Sensing_Scenes">http://www.ehu.eus/ccwintco/index.php/Hyperspectral_Remote_Sensing_Scenes</uri>) hyperspectral images were evaluated in the experiments.
<list list-type="bullet"><list-item><p>The Indian Pines image is for the agricultural Indian Pine test site in Northwestern Indiana, which was a 220 Band AVIRIS Hyperspectral Image Data Set: 12 June 1992 Indian Pine Test Site 3. It was acquired over the Purdue University Agronomy farm northwest of West Lafayette and the surrounding area. The data were acquired to support soils research being conducted by Prof. Marion Baumgardner and his students [<xref rid="B70-sensors-19-00479" ref-type="bibr">70</xref>]. The wavelength is from 400 to 2500 nm. The resolution is 145 &#x000d7; 145 pixels. Because some of the crops present (e.g., corn and soybean) are in the early stages of growth in June, the coverage is minuscule&#x02014;approximately less than 5%. The ground truth is divided into sixteen classes, and are not all mutually exclusive. The Indian Pines false-color image and the ground truth image are presented in <xref ref-type="fig" rid="sensors-19-00479-f004">Figure 4</xref>.</p></list-item><list-item><p>The Pavia University scene was obtained from an urban area surrounding the University of Pavia, Italy on 8 July 2002 with a spatial resolution of 1.3 m. The wavelength ranges from 0.43 to 0.86 &#x003bc;m. There are 115 bands with size 610 &#x000d7; 340 pixels in the image. After removing 12 water absorption bands, 103 channels were left for testing. The Pavia University scene&#x02019;s false-color image and the corresponding ground truth image are shown in <xref ref-type="fig" rid="sensors-19-00479-f005">Figure 5</xref>.</p></list-item></list>
<list list-type="bullet"><list-item><p>The Salinas image is from the Salinas Valley, California, USA, which was obtained from an AVIRIS sensor with 3.7 m spatial resolution. The image includes a size of 512 &#x000d7; 217 with 224 bands. Twenty water absorption bands were discarded here. There are 16 classes containing vegetables, bare soil, vineyards, etc. <xref ref-type="fig" rid="sensors-19-00479-f0A1">Figure A1</xref> displays the Salinas image&#x02019;s false-color and ground truth images.</p></list-item></list></p></sec><sec id="sec4dot1dot2-sensors-19-00479"><title>4.1.2. Evaluation Criteria</title><p>We give some evaluation criteria to evaluate the proposed method for HSIs, as follows.</p><p>Classification accuracy (CA) is the classification accuracy of each category in the image. The confusion matrix [<xref rid="B71-sensors-19-00479" ref-type="bibr">71</xref>] is often used in the remote sensing classification field, and its form is defined as: <inline-formula><mml:math id="mm225"><mml:mrow><mml:mrow><mml:mi>M</mml:mi><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mo>[</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>]</mml:mo></mml:mrow><mml:mrow><mml:mi>n</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>n</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:math></inline-formula>, where <inline-formula><mml:math id="mm226"><mml:mrow><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula> indicates that the number of pixels labeled by the <italic>j</italic> class should belong to the <italic>i</italic> class. <italic>n</italic> is the class number. The reliability of classification depends on the diagonal values of the confusion matrix. The higher the values on the diagonal of the confusion matrix are, the better the classification results will be.</p><p>We also used the following three main indicators: overall accuracy (OA), average accuracy (AA), and the kappa coefficient [<xref rid="B72-sensors-19-00479" ref-type="bibr">72</xref>]. OA refers to the percentage of the overall correct classification. AA estimates the average correct classification percentage for all categories. The kappa coefficient takes the chance agreement into account and fixes it, whereas OA and AA check how many pixels are classified correctly. Assuming that the reference classification (i.e., ground truth) is true, then how well they agree is measured. Here, we assumed that both classification and reference classification were independent class assignments of equal reliability. The advantage of the kappa coefficient over overall accuracy is that the kappa coefficient considers chance agreement and corrects it. The chance agreement is the probability that the classification and reference classification agree by mere chance. Assuming statistical independence, we obtained this probability estimation [<xref rid="B73-sensors-19-00479" ref-type="bibr">73</xref>,<xref rid="B74-sensors-19-00479" ref-type="bibr">74</xref>].</p></sec><sec id="sec4dot1dot3-sensors-19-00479"><title>4.1.3. Comparative Algorithms</title><p>We give several comparative methods to illustrate the great improvement by the SLIC superpixel-based <inline-formula><mml:math id="mm227"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis graph model in the HSIs&#x02019; classification. For fairness, these comparative algorithms incorporate the simple linear iterative clustering (SLIC), which are shown below.
<list list-type="bullet"><list-item><p>RPCA (robust principal component analysis) method [<xref rid="B63-sensors-19-00479" ref-type="bibr">63</xref>]: The original robust principal component analysis with <inline-formula><mml:math id="mm228"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm.</p></list-item><list-item><p>PCA (principal component analysis) method [<xref rid="B63-sensors-19-00479" ref-type="bibr">63</xref>]: PCA [<xref rid="B16-sensors-19-00479" ref-type="bibr">16</xref>] seeks the best low-rank representation of the given data matrix that minimizes the difference between <inline-formula><mml:math id="mm229"><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm230"><mml:mrow><mml:mi mathvariant="bold">X</mml:mi></mml:mrow></mml:math></inline-formula>:
<disp-formula id="FD31-sensors-19-00479"><label>(31)</label><mml:math id="mm231"><mml:mrow><mml:mtable><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:munder><mml:mi>min</mml:mi><mml:mrow><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:munder><mml:mspace width="0.277778em"/><mml:msub><mml:mfenced open="&#x02225;" close="&#x02225;"><mml:mi mathvariant="bold">E</mml:mi></mml:mfenced><mml:mi>F</mml:mi></mml:msub></mml:mrow></mml:mtd></mml:mtr><mml:mtr><mml:mtd columnalign="left"><mml:mrow><mml:mspace width="0.277778em"/><mml:mi>s</mml:mi><mml:mo>.</mml:mo><mml:mi>t</mml:mi><mml:mo>.</mml:mo><mml:mspace width="0.277778em"/><mml:mspace width="0.277778em"/><mml:mi>r</mml:mi><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>k</mml:mi><mml:mo>(</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>)</mml:mo><mml:mo>&#x02264;</mml:mo><mml:mi>r</mml:mi><mml:mo>,</mml:mo><mml:mi mathvariant="bold">X</mml:mi><mml:mo>=</mml:mo><mml:mi mathvariant="bold">Z</mml:mi><mml:mo>+</mml:mo><mml:mi mathvariant="bold">E</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math></disp-formula></p></list-item><list-item><p>IFRF (image fusion and recursive filtering) algorithm.</p></list-item><list-item><p>Origin: Original bands of the unprocessed image.</p></list-item></list></p></sec></sec><sec id="sec4dot2-sensors-19-00479"><title>4.2. Classification of Hyperspectral Images</title><p>We performed experiments on the three hyperspectral images to examine the performance of the SURPCA<sub>2,1</sub> graph. Ten independent runs of each algorithm were evaluated by resampling the training samples in each run. We chose the mean values as the results. In practice, it is difficult to obtain labeled samples, while unlabeled samples are often available and in large numbers. Unlike most of the existing HSI classifications, we tested the performance of all comparative methods using only small rate of the labeled samples. <xref rid="sensors-19-00479-t001" ref-type="table">Table 1</xref> shows the training and testing sets for all datasets, where the training sets are chosen randomly. The training samples were approximately 4%, 3%, and 0.4% for the three images, which were minimal sets to the entire dataset. Considering the classes with a meager number of samples, we incorporated a minimum threshold of training samples. Here, we set the minimum threshold of training samples for each class as five, which can eradicate the difference between the classes with a low number of samples. The SLIC superpixels&#x02019; number for the three HSIs is 200, 600, and 400, respectively. <xref ref-type="fig" rid="sensors-19-00479-f004">Figure 4</xref>b, <xref ref-type="fig" rid="sensors-19-00479-f005">Figure 5</xref>b and <xref ref-type="fig" rid="sensors-19-00479-f0A1">Figure A1</xref>b show the segmentation maps. The amount of reduced dimension is 30 for the three hyperspectral images. The spatial standard deviation and range standard deviation of the filter are <inline-formula><mml:math id="mm233"><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>s</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> and <inline-formula><mml:math id="mm234"><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>r</mml:mi></mml:msub></mml:mrow></mml:math></inline-formula> with 200 and 0.3, respectively. The parameter <inline-formula><mml:math id="mm235"><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow></mml:math></inline-formula> in <italic>k</italic>-nearest neighbor <inline-formula><mml:math id="mm236"><mml:mrow><mml:mrow><mml:msub><mml:mi mathvariant="bold">S</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mi>exp</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:msup><mml:mrow><mml:mfenced separators="" open="&#x02225;" close="&#x02225;"><mml:mrow><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi mathvariant="bold">i</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi mathvariant="bold">z</mml:mi><mml:mi mathvariant="bold">j</mml:mi></mml:msub></mml:mrow></mml:mfenced></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mrow><mml:mn>2</mml:mn><mml:msup><mml:mi>&#x003c3;</mml:mi><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mrow></mml:mrow></mml:math></inline-formula> is 0.1, which is provided randomly.</p><p>In the beginning, we utilized different manifold mapping graphs to obtain the manifold matrix of the hyperspectral images. Then, we applied the SDA algorithm for semi-supervised dimensionality reduction. Both the nearest neighbor and support vector machine algorithms were applied as the classifiers here to test and verify the proposed SURPCA<sub>2,1</sub> graph. <xref rid="sensors-19-00479-t002" ref-type="table">Table 2</xref>, <xref rid="sensors-19-00479-t003" ref-type="table">Table 3</xref> and <xref rid="sensors-19-00479-t0A1" ref-type="table">Table A1</xref> give detailed classification results for CA, OA, AA, and kappa coefficients received from different methods, where bold numbers represent the best ones for various graphs. <xref ref-type="fig" rid="sensors-19-00479-f006">Figure 6</xref>, <xref ref-type="fig" rid="sensors-19-00479-f007">Figure 7</xref> and <xref ref-type="fig" rid="sensors-19-00479-f0A2">Figure A2</xref> indicate classification maps of the hyperspectral images acquired by several methods (randomly selected from our experiments above) with the NN classifier. It was observed that the results in the figures were random for each method. We analyzed the running times of different models on the Indian Pines image, Pavia University scene, and Salinas scene image. Ten separate runs were calculated for the total time.</p><p>In <xref rid="sensors-19-00479-t002" ref-type="table">Table 2</xref>, <xref rid="sensors-19-00479-t003" ref-type="table">Table 3</xref>, and <xref rid="sensors-19-00479-t0A1" ref-type="table">Table A1</xref>, we can notice that the SURPCA<sub>2,1</sub> graph is superior to the other graph models. Therefore, it significantly improves the classification performance of hyperspectral images, which indicates that the SURPCA<sub>2,1</sub> graph is a superior HSIs feature extraction method with both NN and SVM classifiers. For example, in <xref rid="sensors-19-00479-t002" ref-type="table">Table 2</xref>, although the overall accuracy and kappa coefficient of other graphs are high, the average accuracy is not good. The accuracy of Grass-pasture-mowed and Oats are more (i.e., 12&#x02013;37%) improved with the NN classifier and with SVM classifier (i.e., 25&#x02013;64%) than the compared graphs. In addition, the Corn, Soybean-notill, and Soybean-clean classes are significantly improved, especially the Grass-pasture-mowed, for the Indian Pines image. For the Salinas dataset, the classification of Fallow, Lettuce_romaine_6wk, and Vinyard_untrained classes are obviously improved. We can see that, although sometimes the overall accuracy is not very different, the average accuracy is far ahead of the compared graphs. This is because, when there are fewer samples in some classes, it is very easy to be confused. For the Indian Pines image, the accuracy of all categories is more than 89.81%, except for the 81.96% accuracy of the Grass-pasture-mowed with the NN classifier, and it is also more than 95.03% with the SVM classifier, which meets the general accuracy requirement of remote sensing. For the Pavia University scene and Salinas image, the accuracy of all categories is larger than 91.69% and 91.95%, and 91.7% and 97.45% with NN and SVM, respectively. We can also see that our graph performed well in all categories with both of the classifiers. However, for other methods, they might perform much worse in some confusing categories, especially with the NN classifier. This phenomenon shows that our proposed graph is robust to classifier methods.</p><p>We analyzed the running times of different models on the three hyperspectral images. We used ten separate runs to calculate the total time. We give the mean running time. As shown in <xref rid="sensors-19-00479-t004" ref-type="table">Table 4</xref>, the execution time of the SURPCA<sub>2,1</sub> graph method is slightly longer than the other methods. Although our algorithm is slower than the traditional algorithms, the performance is much better than these baselines at an acceptable running time. Note that the RPCA<sub>2,1</sub> is significantly faster than the classic RPCA. This is owing to the superior ability of the <inline-formula><mml:math id="mm242"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm to converge more quickly than the <inline-formula><mml:math id="mm243"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm.</p><p>Although the classification results with NN are slightly worse with the SVM algorithm, the running time is much less than the SVM classifier. Moreover, in <xref rid="sensors-19-00479-t002" ref-type="table">Table 2</xref>, <xref rid="sensors-19-00479-t003" ref-type="table">Table 3</xref>, and <xref rid="sensors-19-00479-t0A1" ref-type="table">Table A1</xref>, we can see that the classification results with NN classifier are acceptable. Therefore, in the following experiments, the nearest neighbor is applied.</p></sec><sec id="sec4dot3-sensors-19-00479"><title>4.3. Robustness of the SURPCA<sub>2,1</sub> Graph</title><p>We evaluated the performance of the proposed SURPCA<sub>2,1</sub> graph. To fully determine the superiority of the proposed graph model, we also analyzed the robustness of SURPCA<sub>2,1</sub>. Considering the practical situation, we analyzed the robustness of SURPCA<sub>2,1</sub> on the size of the labeled samples and noise situation.</p><sec id="sec4dot3dot1-sensors-19-00479"><title>4.3.1. Labeled Size Robustness</title><p>We analyzed the impact of different sizes of training and testing sets. The experiments were carried out with ten independent runs for each algorithm. We calculated the mean values and standard deviations of the results. <xref ref-type="fig" rid="sensors-19-00479-f008">Figure 8</xref> shows the classification results of different graph models. It compares the overall classification results with different training size samples in each class. The training samples percentages were 0.2&#x02013;10%, 0.2&#x02013;6%, and 0.05&#x02013;3% for Indian Pines image, Pavia University scene, and Salinas image, respectively.</p><p>In most cases, our graph model SURPCA<sub>2,1</sub> consistently achieves the best results, which are robust to the label percentage variations. With increasing size of the training sample, OA generally increases in all methods, showing a similar trend. When the labeled ratio is fixed, the SURPCA<sub>2,1</sub> method is usually superior to others. Similarly, the three classification criteria are increased with the increasing training samples simultaneously.</p><p>Note that, our proposed graph realize higher classification results, even with a low label ratio. The other comparison algorithms are especially not as robust as the SURPCA<sub>2,1</sub> graph with low label ratio. In particular, we can see that our graph model perform relatively better, and the difference between other comparison methods is larger when the labeling rate is smaller. Therefore, due to the high cost and difficulty of labeled samples, our proposed graph is much more robust and suitable for real-world hyperspectral images.</p></sec><sec id="sec4dot3dot2-sensors-19-00479"><title>4.3.2. Noise Robustness</title><p>We evaluated the robustness to noise of the SURPCA<sub>2,1</sub> graph on the three hyperspectral images. We added zero-mean Gaussian noise with a signal-to-noise ratio (SNR) of 20 dB to each band. <xref ref-type="fig" rid="sensors-19-00479-f009">Figure 9</xref> shows an example noise image of three randomly selected bands in the Indian Pines image, which is similar to the other two hyperspectral images.</p><p>We evaluated different graphs&#x02019; performance in the noisy situation. Each algorithm ran ten times independently, with re-sampling of training samples. We calculated the mean and standard deviation results. The labeled sampling ratios were 4%, 3%, and 0.4%, respectively, as described in <xref rid="sensors-19-00479-t005" ref-type="table">Table 5</xref>. The bold numbers represent the best results for various graphs. <xref ref-type="fig" rid="sensors-19-00479-f010">Figure 10</xref>, <xref ref-type="fig" rid="sensors-19-00479-f011">Figure 11</xref>, and <xref ref-type="fig" rid="sensors-19-00479-f0A3">Figure A3</xref> display classification maps of the three noise hyperspectral images correlated with OA values (randomly selected from our experiments above). It is observed that the results in the figures were random for each method.</p><p>From the results of these different graph models, we can see that our graph is robust to noise and the sample size of the labeled set. In a noisy environment, our method is relatively less degraded and more robust. With few labeled samples, the SURPCA<sub>2,1</sub> graph is more forceful than the other graphs due to the <inline-formula><mml:math id="mm253"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis noise robustness. Moreover, the SURPCA<sub>2,1</sub> graph performs very well for all three experimental hyperspectral images.</p></sec></sec><sec id="sec4dot4-sensors-19-00479"><title>4.4. Parameters of SURPCA<sub>2,1</sub> Model</title><p>We evaluated the SURPCA<sub>2,1</sub> graph&#x02019;s parameters superpixel number <italic>m</italic> and the reduced dimensions of SDA. We conducted ten independent runs per algorithm and calculated the mean values and standard deviations of the results. <xref rid="sensors-19-00479-t006" ref-type="table">Table 6</xref> and <xref ref-type="fig" rid="sensors-19-00479-f012">Figure 12</xref> show the performance of different superpixel number <italic>m</italic> and the reduced dimensions, respectively. For these three hyperspectral images, the labeled ratio was about 4%, 3%, and 0.4%, respectively. The superpixel segmentation numbers are shown in <xref rid="sensors-19-00479-t006" ref-type="table">Table 6</xref> for the three hyperspectral images.</p><p>In general, the classification results are not greatly affected by the number of superpixel segmentations, indicating that our graph is robust to the number of superpixel segmentations. It is observed that the increase in superpixel numbers simultaneously accelerated the running time considerably. However, the impact is relatively small when the number of samples is large. Therefore, we could use relatively small superpixel numbers in actual situations for the purpose of efficiency with minimal overall loss of classification accuracy.</p><p>We can observe that the reduction dimension is robust in <xref ref-type="fig" rid="sensors-19-00479-f012">Figure 12</xref>. It will reach steady high accuracy at a relatively low dimension such as 10 or 15.</p></sec></sec><sec sec-type="discussion" id="sec5-sensors-19-00479"><title>5. Discussion</title><p>The hyperspectral images&#x02019; classification plays a pivotal role in HSIs&#x02019; application. In the present work, we propose a novel graph construction model for HSIs&#x02019; semi-supervised learning, SLIC superpixel-based <inline-formula><mml:math id="mm257"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis. Our goal is to improve HSIs&#x02019; classification results through useful graph model. The experimental results show that SURPCA<sub>2,1</sub> is a competitive graph model for hyperspectral images&#x02019; classification.</p><p><xref rid="sensors-19-00479-t002" ref-type="table">Table 2</xref>, <xref rid="sensors-19-00479-t003" ref-type="table">Table 3</xref>, and <xref rid="sensors-19-00479-t0A1" ref-type="table">Table A1</xref> show that SURPCA<sub>2,1</sub> is an effective graph model that achieves the highest classification accuracy. Even with simple classifiers and few labeled samples, the performance is excellent. In most cases, the graph model SURPCA<sub>2,1</sub> obtains the highest classification results, which indicates that the SURPCA<sub>2,1</sub> graph is a good graph construction model and can notably enhance the hyperspectral images&#x02019; classification performance. In some cases, other graph models (e.g., RPCA and PCA) may perform well in some categories, but they are not as robust as our graph in all categories. This is because the structural information cannot be well represented from the definition of <inline-formula><mml:math id="mm262"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mn>1</mml:mn></mml:msub></mml:mrow></mml:math></inline-formula>-norm, while the <inline-formula><mml:math id="mm263"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm RPCA<sub>2,1</sub> produces sparsity based on columns (or rows). The SLIC superpixel segmentation method considers the spatial correlation of pixels in hyperspectral images. For an HSI, the correlation of the adjacent pixels is usually very high since their spectral features are approximately in the same subspace [<xref rid="B41-sensors-19-00479" ref-type="bibr">41</xref>]. By extracting a low-rank matrix in each homogeneous region, we can reveal the low-dimensional structure of pixels and enhance the classification results. Consequently, the SURPCA<sub>2,1</sub> graph model is a superior hyperspectral image&#x02019;s feature graph which significantly improves the performance of HSIs&#x02019; classification.</p><p>We analyzed the robustness to variations in the labeled samples ratio and noisy situations. <xref ref-type="fig" rid="sensors-19-00479-f008">Figure 8</xref> shows that the SURPCA<sub>2,1</sub> graph model provides the best results, which are robust to the varying label percentage. <xref ref-type="fig" rid="sensors-19-00479-f008">Figure 8</xref> also shows that the proposed graph achieves high classification accuracy even at meager label rates, while other graphs may not be as robust as the SURPCA<sub>2,1</sub> graph, particularly when the label rate is low. Therefore, since labeling data is a costly and difficult task, our SURPCA<sub>2,1</sub> model is more suitable and robust for real-world applications. From these results of different graph models, we can see that our graph is robust to the sample ratio of the labeled set as well as the noisy environments. Upon adding noise, the performance of the SURPCA<sub>2,1</sub> graph does not see a large decrease. However, for the comparative graphs, the overall accuracy drops much more than that of SURPCA<sub>2,1</sub>. In addition to the India Pines image, we could see the same results from the other two hyperspectral images. This benefits from the robustness of the <inline-formula><mml:math id="mm271"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis to sparse noise, whereas RPCA and PCA may be sensitive to sparse noise. Therefore, the SURPCA<sub>2,1</sub> graph is robust to noisy environments and the labeled sample ratio, which indicates that it is highly competitive in practical situations.</p><p>We evaluated the performance of parameters with different superpixel segmentations number and reduction dimensions for the SDA method. In <xref rid="sensors-19-00479-t006" ref-type="table">Table 6</xref>, we can see that the classification results are not greatly affected by the number of superpixel segmentations, indicating that our graph is robust to the number of superpixel segmentations. Therefore, we could use a relatively small number of superpixels in actual situations for the purpose of efficiency, with barely any classification accuracy loss. Moreover, in <xref ref-type="fig" rid="sensors-19-00479-f012">Figure 12</xref>, we can see that the classification results are robust after the dimension reached a certain numerical value. It reaches a high accuracy at a relatively low dimension (e.g., 10 or 15). Overall, our proposed SURPCA<sub>2,1</sub> graph is very much robust and shows excellent performance in the classification of HSIs.</p></sec><sec sec-type="conclusions" id="sec6-sensors-19-00479"><title>6. Conclusions</title><p>In this paper, we present a novel graph for HSI feature extraction, referred to as SLIC superpixel-based <inline-formula><mml:math id="mm274"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm based robust principal component analysis. The SLIC superpixel segmentation method considers the spatial correlation of pixels in HSIs. The <inline-formula><mml:math id="mm275"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis is used to extract the low-rank structure of all pixels in each homogeneous area, which captures the spectral correlation of the hyperspectral images. Therefore, the classification performance of the SURPCA<sub>2,1</sub> graph is much better than the compared methods RPCA, PCA, etc. Experiments on real-world HSIs indicated that the SURPCA<sub>2,1</sub> graph is a robust and efficient graph construction model.</p></sec></body><back><notes><title>Author Contributions</title><p>B.Z. conceived of and designed the experiments and wrote the paper. K.X. gave some comments and ideas for the work. T.L. helped revise the paper and improve the writing. Z.H. and Y.L. performed some experiments and analyzed the data. J.H. and W.D. contributed to the review and revision.</p></notes><notes><title>Funding</title><p>This work was supported by Key Supporting Project of the Joint Fund of the National Natural Science Foundation of China (No. U1813222), Tianjin Natural Science Foundation (No. 18JCYBJC16500), Hebei Province Natural Science Foundation (No. E2016202341), and Tianjin Education Commission Research Project (No. 2016CJ20).</p></notes><notes notes-type="COI-statement"><title>Conflicts of Interest</title><p>The authors declare no conflict of interest.</p></notes><app-group><app id="app1-sensors-19-00479"><title>Appendix A</title><fig id="sensors-19-00479-f0A1" orientation="portrait" position="anchor"><label>Figure A1</label><caption><p>Salinas dataset: (<bold>a</bold>) false-color image; (<bold>b</bold>) segmentation map; and (<bold>c</bold>,<bold>d</bold>) ground truth image and reference data.</p></caption><graphic xlink:href="sensors-19-00479-g0A1"/></fig><table-wrap id="sensors-19-00479-t0A1" orientation="portrait" position="anchor"><object-id pub-id-type="pii">sensors-19-00479-t0A1_Table A1</object-id><label>Table A1</label><caption><p>Salinas image classification results.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Method</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origion</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origin</th></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>Classifier</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>NN</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>SVM</bold>
</td></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">C1</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9999</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.932</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9726</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9982</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C2</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9994</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9929</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9952</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9651</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9796</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9926</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9959</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9969</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9789</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9913</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C3</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9969</td><td align="center" valign="middle" rowspan="1" colspan="1">0.997</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9949</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9501</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8262</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9990</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9984</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9986</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9982</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9652</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C4</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9633</td><td align="center" valign="middle" rowspan="1" colspan="1">0.916</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9081</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8496</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9703</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9745</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9718</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9715</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9542</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9685</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C5</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9903</td><td align="center" valign="middle" rowspan="1" colspan="1">0.966</td><td align="center" valign="middle" rowspan="1" colspan="1">0.983</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9592</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9581</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9997</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9991</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9986</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9990</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9001</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C6</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9952</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9958</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9997</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9997</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9998</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9916</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9997</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C7</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9971</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9881</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9901</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9692</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9202</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9991</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9975</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9986</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9900</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9989</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C8</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9882</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9666</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9714</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8995</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6584</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9958</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9925</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9927</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9881</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7304</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C9</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9999</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9947</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9976</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9932</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9408</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9998</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9995</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9995</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9979</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9592</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C10</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9969</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9905</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9946</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9681</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8393</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9957</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9888</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9931</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9886</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9283</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C11</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9961</td><td align="center" valign="middle" rowspan="1" colspan="1">0.982</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9926</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9548</td><td align="center" valign="middle" rowspan="1" colspan="1">0.851</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9994</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9989</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9866</td><td align="center" valign="middle" rowspan="1" colspan="1">0.951</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C12</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9563</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9481</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9631</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9259</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8886</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9907</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9786</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9819</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9903</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8949</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C13</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9394</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8897</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9354</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7101</td><td align="center" valign="middle" rowspan="1" colspan="1">0.763</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9999</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9982</td><td align="center" valign="middle" rowspan="1" colspan="1">0.993</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9406</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8367</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C14</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9865</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9845</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9725</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9791</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9145</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9759</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9835</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9688</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9837</td><td align="center" valign="middle" rowspan="1" colspan="1">0.864</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C15</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9195</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8994</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8874</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7298</td><td align="center" valign="middle" rowspan="1" colspan="1">0.5459</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9980</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9966</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9983</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9771</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6202</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">C16</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9998</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9994</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9944</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9398</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9996</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9993</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9999</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9911</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9955</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">OA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9809</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9668</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9692</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9653</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8185</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9964</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9947</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9951</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9867</td><td align="center" valign="middle" rowspan="1" colspan="1">0.866</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">AA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9831</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9697</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9741</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9705</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8727</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.995</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9937</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9931</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9847</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9126</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Kappa</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9787</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.963</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9657</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9614</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.7976</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>0.996</bold>
</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9941</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9945</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9852</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8506</td></tr></tbody></table></table-wrap><fig id="sensors-19-00479-f0A2" orientation="portrait" position="anchor"><label>Figure A2</label><caption><p>Salinas image Classification results: (<bold>a</bold>) RPCA<sub>2,1</sub>; (<bold>b</bold>) RPCA; (<bold>c</bold>) PCA; (<bold>d</bold>) IFRF; and (<bold>e</bold>) Origin. OA, overall accuracy.</p></caption><graphic xlink:href="sensors-19-00479-g0A2"/></fig><fig id="sensors-19-00479-f0A3" orientation="portrait" position="anchor"><label>Figure A3</label><caption><p>Noisy Salinas image classification results: (<bold>a</bold>) RPCA<sub>2,1</sub>; (<bold>b</bold>) RPCA; (<bold>c</bold>) PCA; (<bold>d</bold>) IFRF; and (<bold>e</bold>) Origin. OA, overall accuracy.</p></caption><graphic xlink:href="sensors-19-00479-g0A3"/></fig></app></app-group><ref-list><title>References</title><ref id="B1-sensors-19-00479"><label>1.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>L.</given-names></name><name><surname>Zhang</surname><given-names>H.</given-names></name><name><surname>Zhao</surname><given-names>M.</given-names></name><name><surname>Chu</surname><given-names>D.</given-names></name><name><surname>Li</surname><given-names>Y.</given-names></name></person-group><article-title>Integrating spectral and spatial features for hyperspectral image classification using low-rank representation</article-title><source>Proceedings of the 2017 IEEE International Conference on Industrial Technology (ICIT)</source><conf-loc>Toronto, ON, Canada</conf-loc><conf-date>22&#x02013;25 March 2017</conf-date><fpage>1024</fpage><lpage>1029</lpage></element-citation></ref><ref id="B2-sensors-19-00479"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ustin</surname><given-names>S.L.</given-names></name><name><surname>Roberts</surname><given-names>D.A.</given-names></name><name><surname>Gamon</surname><given-names>J.A.</given-names></name><name><surname>Asner</surname><given-names>G.P.</given-names></name><name><surname>Green</surname><given-names>R.O.</given-names></name></person-group><article-title>Using imaging spectroscopy to study ecosystem processes and properties</article-title><source>AIBS Bull.</source><year>2004</year><volume>54</volume><fpage>523</fpage><lpage>534</lpage><pub-id pub-id-type="doi">10.1641/0006-3568(2004)054[0523:UISTSE]2.0.CO;2</pub-id></element-citation></ref><ref id="B3-sensors-19-00479"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Haboudane</surname><given-names>D.</given-names></name><name><surname>Miller</surname><given-names>J.R.</given-names></name><name><surname>Pattey</surname><given-names>E.</given-names></name><name><surname>Zarco-Tejada</surname><given-names>P.J.</given-names></name><name><surname>Strachan</surname><given-names>I.B.</given-names></name></person-group><article-title>Hyperspectral vegetation indices and novel algorithms for predicting green LAI of crop canopies: Modeling and validation in the context of precision agriculture</article-title><source>Remote Sens. Environ.</source><year>2004</year><volume>90</volume><fpage>337</fpage><lpage>352</lpage><pub-id pub-id-type="doi">10.1016/j.rse.2003.12.013</pub-id></element-citation></ref><ref id="B4-sensors-19-00479"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dorigo</surname><given-names>W.A.</given-names></name><name><surname>Zurita-Milla</surname><given-names>R.</given-names></name><name><surname>de Wit</surname><given-names>A.J.</given-names></name><name><surname>Brazile</surname><given-names>J.</given-names></name><name><surname>Singh</surname><given-names>R.</given-names></name><name><surname>Schaepman</surname><given-names>M.E.</given-names></name></person-group><article-title>A review on reflective remote sensing and data assimilation techniques for enhanced agroecosystem modeling</article-title><source>Int. J. Appl. Earth Obs. Geoinf.</source><year>2007</year><volume>9</volume><fpage>165</fpage><lpage>193</lpage><pub-id pub-id-type="doi">10.1016/j.jag.2006.05.003</pub-id></element-citation></ref><ref id="B5-sensors-19-00479"><label>5.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Cocks</surname><given-names>T.</given-names></name><name><surname>Jenssen</surname><given-names>R.</given-names></name><name><surname>Stewart</surname><given-names>A.</given-names></name><name><surname>Wilson</surname><given-names>I.</given-names></name><name><surname>Shields</surname><given-names>T.</given-names></name></person-group><article-title>The HyMapTM airborne hyperspectral sensor: The system, calibration and performance</article-title><source>Proceedings of the 1st EARSeL workshop on Imaging Spectroscopy (EARSeL)</source><conf-loc>Paris, France</conf-loc><conf-date>6&#x02013;8 October 1998</conf-date><fpage>37</fpage><lpage>42</lpage></element-citation></ref><ref id="B6-sensors-19-00479"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dalponte</surname><given-names>M.</given-names></name><name><surname>Bruzzone</surname><given-names>L.</given-names></name><name><surname>Vescovo</surname><given-names>L.</given-names></name><name><surname>Gianelle</surname><given-names>D.</given-names></name></person-group><article-title>The role of spectral resolution and classifier complexity in the analysis of hyperspectral images of forest areas</article-title><source>Remote Sens. Environ.</source><year>2009</year><volume>113</volume><fpage>2345</fpage><lpage>2355</lpage><pub-id pub-id-type="doi">10.1016/j.rse.2009.06.013</pub-id></element-citation></ref><ref id="B7-sensors-19-00479"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>De Morsier</surname><given-names>F.</given-names></name><name><surname>Borgeaud</surname><given-names>M.</given-names></name><name><surname>Gass</surname><given-names>V.</given-names></name><name><surname>Thiran</surname><given-names>J.P.</given-names></name><name><surname>Tuia</surname><given-names>D.</given-names></name></person-group><article-title>Kernel low-rank and sparse graph for unsupervised and semi-supervised classification of hyperspectral images</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2016</year><volume>54</volume><fpage>3410</fpage><lpage>3420</lpage><pub-id pub-id-type="doi">10.1109/TGRS.2016.2517242</pub-id></element-citation></ref><ref id="B8-sensors-19-00479"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hughes</surname><given-names>G.</given-names></name></person-group><article-title>On the mean accuracy of statistical pattern recognizers</article-title><source>IEEE Trans. Inf. Theory</source><year>1968</year><volume>14</volume><fpage>55</fpage><lpage>63</lpage><pub-id pub-id-type="doi">10.1109/TIT.1968.1054102</pub-id></element-citation></ref><ref id="B9-sensors-19-00479"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Heinz</surname><given-names>D.C.</given-names></name></person-group><article-title>Fully constrained least squares linear spectral mixture analysis method for material quantification in hyperspectral imagery</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2001</year><volume>39</volume><fpage>529</fpage><lpage>545</lpage><pub-id pub-id-type="doi">10.1109/36.911111</pub-id></element-citation></ref><ref id="B10-sensors-19-00479"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kruse</surname><given-names>F.A.</given-names></name><name><surname>Boardman</surname><given-names>J.W.</given-names></name><name><surname>Huntington</surname><given-names>J.F.</given-names></name></person-group><article-title>Comparison of airborne hyperspectral data and EO-1 Hyperion for mineral mapping</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2003</year><volume>41</volume><fpage>1388</fpage><lpage>1400</lpage><pub-id pub-id-type="doi">10.1109/TGRS.2003.812908</pub-id></element-citation></ref><ref id="B11-sensors-19-00479"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gu</surname><given-names>Y.</given-names></name><name><surname>Wang</surname><given-names>Q.</given-names></name><name><surname>Wang</surname><given-names>H.</given-names></name><name><surname>You</surname><given-names>D.</given-names></name><name><surname>Zhang</surname><given-names>Y.</given-names></name></person-group><article-title>Multiple kernel learning via low-rank nonnegative matrix factorization for classification of hyperspectral imagery</article-title><source>IEEE J. Sel. Top. Appl. Earth Obs. Remote Sens.</source><year>2015</year><volume>8</volume><fpage>2739</fpage><lpage>2751</lpage><pub-id pub-id-type="doi">10.1109/JSTARS.2014.2362116</pub-id></element-citation></ref><ref id="B12-sensors-19-00479"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kang</surname><given-names>X.</given-names></name><name><surname>Li</surname><given-names>S.</given-names></name><name><surname>Benediktsson</surname><given-names>J.A.</given-names></name></person-group><article-title>Feature extraction of hyperspectral images with image fusion and recursive filtering</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2014</year><volume>52</volume><fpage>3742</fpage><lpage>3752</lpage><pub-id pub-id-type="doi">10.1109/TGRS.2013.2275613</pub-id></element-citation></ref><ref id="B13-sensors-19-00479"><label>13.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Duda</surname><given-names>R.O.</given-names></name><name><surname>Hart</surname><given-names>P.E.</given-names></name><name><surname>Stork</surname><given-names>D.G.</given-names></name></person-group><source>Pattern Classification</source><edition>2nd ed.</edition><publisher-name>Wiley</publisher-name><publisher-loc>New York, NY, USA</publisher-loc><year>2001</year><volume>Volume 55</volume></element-citation></ref><ref id="B14-sensors-19-00479"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guyon</surname><given-names>I.</given-names></name><name><surname>Elisseeff</surname><given-names>A.</given-names></name></person-group><article-title>An introduction to variable and feature selection</article-title><source>J. Mach. Learn. Res.</source><year>2003</year><volume>3</volume><fpage>1157</fpage><lpage>1182</lpage></element-citation></ref><ref id="B15-sensors-19-00479"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sacha</surname><given-names>D.</given-names></name><name><surname>Zhang</surname><given-names>L.</given-names></name><name><surname>Sedlmair</surname><given-names>M.</given-names></name><name><surname>Lee</surname><given-names>J.A.</given-names></name><name><surname>Peltonen</surname><given-names>J.</given-names></name><name><surname>Weiskopf</surname><given-names>D.</given-names></name><name><surname>North</surname><given-names>S.C.</given-names></name><name><surname>Keim</surname><given-names>D.A.</given-names></name></person-group><article-title>Visual interaction with dimensionality reduction: A structured literature analysis</article-title><source>IEEE Trans. Vis. Comput. Graph.</source><year>2017</year><volume>23</volume><fpage>241</fpage><lpage>250</lpage><pub-id pub-id-type="doi">10.1109/TVCG.2016.2598495</pub-id><?supplied-pmid 27875141?><pub-id pub-id-type="pmid">27875141</pub-id></element-citation></ref><ref id="B16-sensors-19-00479"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wold</surname><given-names>S.</given-names></name><name><surname>Esbensen</surname><given-names>K.</given-names></name><name><surname>Geladi</surname><given-names>P.</given-names></name></person-group><article-title>Principal component analysis</article-title><source>Chemom. Intell. Lab. Syst.</source><year>1987</year><volume>2</volume><fpage>37</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1016/0169-7439(87)80084-9</pub-id></element-citation></ref><ref id="B17-sensors-19-00479"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shuiwang</surname><given-names>J.</given-names></name><name><surname>Jieping</surname><given-names>Y.</given-names></name></person-group><article-title>Generalized linear discriminant analysis: a unified framework and efficient model selection</article-title><source>IEEE Trans. Neural Netw.</source><year>2008</year><volume>19</volume><fpage>1768</fpage><pub-id pub-id-type="doi">10.1109/TNN.2008.2002078</pub-id><?supplied-pmid 18842480?><pub-id pub-id-type="pmid">18842480</pub-id></element-citation></ref><ref id="B18-sensors-19-00479"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stone</surname><given-names>J.V.</given-names></name></person-group><article-title>Independent component analysis: An introduction</article-title><source>Trends Cogn. Sci.</source><year>2002</year><volume>6</volume><fpage>59</fpage><lpage>64</lpage><pub-id pub-id-type="doi">10.1016/S1364-6613(00)01813-1</pub-id><pub-id pub-id-type="pmid">15866182</pub-id></element-citation></ref><ref id="B19-sensors-19-00479"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hyv&#x000e4;rinen</surname><given-names>A.</given-names></name><name><surname>Hoyer</surname><given-names>P.O.</given-names></name><name><surname>Inki</surname><given-names>M.</given-names></name></person-group><article-title>Topographic independent component analysis</article-title><source>Neural Comput.</source><year>2001</year><volume>13</volume><fpage>1527</fpage><lpage>1558</lpage><pub-id pub-id-type="doi">10.1162/089976601750264992</pub-id><pub-id pub-id-type="pmid">11440596</pub-id></element-citation></ref><ref id="B20-sensors-19-00479"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Draper</surname><given-names>B.A.</given-names></name><name><surname>Baek</surname><given-names>K.</given-names></name><name><surname>Bartlett</surname><given-names>M.S.</given-names></name><name><surname>Beveridge</surname><given-names>J.R.</given-names></name></person-group><article-title>Recognizing faces with PCA and ICA</article-title><source>Comput. Vis. Image Underst.</source><year>2003</year><volume>91</volume><fpage>115</fpage><lpage>137</lpage><pub-id pub-id-type="doi">10.1016/S1077-3142(03)00077-8</pub-id></element-citation></ref><ref id="B21-sensors-19-00479"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Belkin</surname><given-names>M.</given-names></name><name><surname>Niyogi</surname><given-names>P.</given-names></name></person-group><article-title>Laplacian eigenmaps for dimensionality reduction and data representation</article-title><source>Neural Comput.</source><year>2003</year><volume>15</volume><fpage>1373</fpage><lpage>1396</lpage><pub-id pub-id-type="doi">10.1162/089976603321780317</pub-id></element-citation></ref><ref id="B22-sensors-19-00479"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tenenbaum</surname><given-names>J.B.</given-names></name><name><surname>De Silva</surname><given-names>V.</given-names></name><name><surname>Langford</surname><given-names>J.C.</given-names></name></person-group><article-title>A global geometric framework for nonlinear dimensionality reduction</article-title><source>Science</source><year>2000</year><volume>290</volume><fpage>2319</fpage><lpage>2323</lpage><pub-id pub-id-type="doi">10.1126/science.290.5500.2319</pub-id><pub-id pub-id-type="pmid">11125149</pub-id></element-citation></ref><ref id="B23-sensors-19-00479"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Roweis</surname><given-names>S.T.</given-names></name><name><surname>Saul</surname><given-names>L.K.</given-names></name></person-group><article-title>Nonlinear dimensionality reduction by locally linear embedding</article-title><source>Science</source><year>2000</year><volume>290</volume><fpage>2323</fpage><lpage>2326</lpage><pub-id pub-id-type="doi">10.1126/science.290.5500.2323</pub-id><pub-id pub-id-type="pmid">11125150</pub-id></element-citation></ref><ref id="B24-sensors-19-00479"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>Z.</given-names></name><name><surname>Pasolli</surname><given-names>E.</given-names></name><name><surname>Yang</surname><given-names>L.</given-names></name><name><surname>Crawford</surname><given-names>M.M.</given-names></name></person-group><article-title>Multi-metric Active Learning for Classification of Remote Sensing Data</article-title><source>IEEE Geosci. Remote Sens. Lett.</source><year>2016</year><volume>13</volume><fpage>1007</fpage><lpage>1011</lpage><pub-id pub-id-type="doi">10.1109/LGRS.2016.2560623</pub-id></element-citation></ref><ref id="B25-sensors-19-00479"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>Z.</given-names></name><name><surname>Crawford</surname><given-names>M.M.</given-names></name></person-group><article-title>A Batch-Mode Regularized Multimetric Active Learning Framework for Classification of Hyperspectral Images</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2017</year><volume>55</volume><fpage>1</fpage><lpage>16</lpage><pub-id pub-id-type="doi">10.1109/TGRS.2017.2730583</pub-id></element-citation></ref><ref id="B26-sensors-19-00479"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ul Haq</surname><given-names>Q.S.</given-names></name><name><surname>Tao</surname><given-names>L.</given-names></name><name><surname>Sun</surname><given-names>F.</given-names></name><name><surname>Yang</surname><given-names>S.</given-names></name></person-group><article-title>A fast and robust sparse approach for hyperspectral data classification using a few labeled samples</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2012</year><volume>50</volume><fpage>2287</fpage><lpage>2302</lpage><pub-id pub-id-type="doi">10.1109/TGRS.2011.2172617</pub-id></element-citation></ref><ref id="B27-sensors-19-00479"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kuo</surname><given-names>B.C.</given-names></name><name><surname>Chang</surname><given-names>K.Y.</given-names></name></person-group><article-title>Feature extractions for small sample size classification problem</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2007</year><volume>45</volume><fpage>756</fpage><lpage>764</lpage><pub-id pub-id-type="doi">10.1109/TGRS.2006.885074</pub-id></element-citation></ref><ref id="B28-sensors-19-00479"><label>28.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Zhou</surname><given-names>D.</given-names></name><name><surname>Bousquet</surname><given-names>O.</given-names></name><name><surname>Lal</surname><given-names>T.N.</given-names></name><name><surname>Weston</surname><given-names>J.</given-names></name><name><surname>Sch&#x000f6;lkopf</surname><given-names>B.</given-names></name></person-group><article-title>Learning with local and global consistency</article-title><source>Proceedings of the Advances in Neural Information Processing Systems</source><conf-loc>Whistler, BC, Canada</conf-loc><conf-date>9&#x02013;11 December 2003</conf-date><fpage>321</fpage><lpage>328</lpage></element-citation></ref><ref id="B29-sensors-19-00479"><label>29.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Guo</surname><given-names>Z.</given-names></name><name><surname>Zhang</surname><given-names>Z.</given-names></name><name><surname>Xing</surname><given-names>E.P.</given-names></name><name><surname>Faloutsos</surname><given-names>C.</given-names></name></person-group><article-title>Semi-supervised learning based on semiparametric regularization</article-title><source>Proceedings of the 2008 SIAM International Conference on Data Mining (SIAM)</source><conf-loc>Atlanta, GA, USA</conf-loc><conf-date>24&#x02013;26 April 2008</conf-date><fpage>132</fpage><lpage>142</lpage></element-citation></ref><ref id="B30-sensors-19-00479"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Subramanya</surname><given-names>A.</given-names></name><name><surname>Talukdar</surname><given-names>P.P.</given-names></name></person-group><article-title>Graph-based semi-supervised learning</article-title><source>Synth. Lect. Artif. Intell. Mach. Learn.</source><year>2014</year><volume>8</volume><fpage>1</fpage><lpage>125</lpage><pub-id pub-id-type="doi">10.2200/S00590ED1V01Y201408AIM029</pub-id></element-citation></ref><ref id="B31-sensors-19-00479"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zheng</surname><given-names>M.</given-names></name><name><surname>Bu</surname><given-names>J.</given-names></name><name><surname>Chen</surname><given-names>C.</given-names></name><name><surname>Wang</surname><given-names>C.</given-names></name><name><surname>Zhang</surname><given-names>L.</given-names></name><name><surname>Qiu</surname><given-names>G.</given-names></name><name><surname>Cai</surname><given-names>D.</given-names></name></person-group><article-title>Graph regularized sparse coding for image representation</article-title><source>IEEE Trans. Image Process.</source><year>2011</year><volume>20</volume><fpage>1327</fpage><lpage>1336</lpage><pub-id pub-id-type="doi">10.1109/TIP.2010.2090535</pub-id><?supplied-pmid 21047712?><pub-id pub-id-type="pmid">21047712</pub-id></element-citation></ref><ref id="B32-sensors-19-00479"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tian</surname><given-names>X.</given-names></name><name><surname>Gasso</surname><given-names>G.</given-names></name><name><surname>Canu</surname><given-names>S.</given-names></name></person-group><article-title>A multiple kernel framework for inductive semi-supervised SVM learning</article-title><source>Neurocomputing</source><year>2012</year><volume>90</volume><fpage>46</fpage><lpage>58</lpage><pub-id pub-id-type="doi">10.1016/j.neucom.2011.12.036</pub-id></element-citation></ref><ref id="B33-sensors-19-00479"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cai</surname><given-names>D.</given-names></name><name><surname>He</surname><given-names>X.</given-names></name><name><surname>Han</surname><given-names>J.</given-names></name><name><surname>Huang</surname><given-names>T.S.</given-names></name></person-group><article-title>Graph regularized nonnegative matrix factorization for data representation</article-title><source>IEEE Trans. Pattern Anal. Mach. Intell.</source><year>2011</year><volume>33</volume><fpage>1548</fpage><lpage>1560</lpage><?supplied-pmid 21173440?><pub-id pub-id-type="pmid">21173440</pub-id></element-citation></ref><ref id="B34-sensors-19-00479"><label>34.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Gao</surname><given-names>S.</given-names></name><name><surname>Tsang</surname><given-names>I.W.H.</given-names></name><name><surname>Chia</surname><given-names>L.T.</given-names></name><name><surname>Zhao</surname><given-names>P.</given-names></name></person-group><article-title>Local features are not lonely&#x02014;Laplacian sparse coding for image classification</article-title><source>Proceedings of the 2010 IEEE Computer Society Conference on Computer Vision and Pattern Recognition</source><conf-loc>San Francisco, CA, USA</conf-loc><conf-date>13&#x02013;18 June 2010</conf-date></element-citation></ref><ref id="B35-sensors-19-00479"><label>35.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>G.</given-names></name><name><surname>Lin</surname><given-names>Z.</given-names></name><name><surname>Yu</surname><given-names>Y.</given-names></name></person-group><article-title>Robust subspace segmentation by low-rank representation</article-title><source>Proceedings of the 27th International Conference on Machine Learning (ICML-10)</source><conf-loc>Haifa, Israel</conf-loc><conf-date>21&#x02013;24 June 2010</conf-date><fpage>663</fpage><lpage>670</lpage></element-citation></ref><ref id="B36-sensors-19-00479"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cand&#x000e8;s</surname><given-names>E.J.</given-names></name><name><surname>Li</surname><given-names>X.</given-names></name><name><surname>Ma</surname><given-names>Y.</given-names></name><name><surname>Wright</surname><given-names>J.</given-names></name></person-group><article-title>Robust principal component analysis?</article-title><source>J. ACM</source><year>2011</year><volume>58</volume><fpage>11</fpage><pub-id pub-id-type="doi">10.1145/1970392.1970395</pub-id></element-citation></ref><ref id="B37-sensors-19-00479"><label>37.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Wright</surname><given-names>J.</given-names></name><name><surname>Ganesh</surname><given-names>A.</given-names></name><name><surname>Rao</surname><given-names>S.</given-names></name><name><surname>Peng</surname><given-names>Y.</given-names></name><name><surname>Ma</surname><given-names>Y.</given-names></name></person-group><article-title>Robust principal component analysis: Exact recovery of corrupted low-rank matrices via convex optimization</article-title><source>Proceedings of the Advances in Neural Information Processing Systems</source><conf-loc>Vancouver, BC, Canada</conf-loc><conf-date>7&#x02013;10 December 2009</conf-date><fpage>2080</fpage><lpage>2088</lpage></element-citation></ref><ref id="B38-sensors-19-00479"><label>38.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Binge</surname><given-names>C.</given-names></name><name><surname>Zongqi</surname><given-names>F.</given-names></name><name><surname>Xiaoyun</surname><given-names>X.</given-names></name><name><surname>Yong</surname><given-names>Z.</given-names></name><name><surname>Liwei</surname><given-names>Z.</given-names></name></person-group><article-title>A Novel Feature Extraction Method for Hyperspectral Image Classification</article-title><source>Proceedings of the 2016 International Conference on Intelligent Transportation, Big Data &#x00026; Smart City (ICITBS)</source><conf-loc>Changsha, China</conf-loc><conf-date>17&#x02013;18 December 2016</conf-date><fpage>51</fpage><lpage>54</lpage></element-citation></ref><ref id="B39-sensors-19-00479"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nie</surname><given-names>Y.</given-names></name><name><surname>Chen</surname><given-names>L.</given-names></name><name><surname>Zhu</surname><given-names>H.</given-names></name><name><surname>Du</surname><given-names>S.</given-names></name><name><surname>Yue</surname><given-names>T.</given-names></name><name><surname>Cao</surname><given-names>X.</given-names></name></person-group><article-title>Graph-regularized tensor robust principal component analysis for hyperspectral image denoising</article-title><source>Appl. Opt.</source><year>2017</year><volume>56</volume><fpage>6094</fpage><lpage>6102</lpage><pub-id pub-id-type="doi">10.1364/AO.56.006094</pub-id><pub-id pub-id-type="pmid">29047801</pub-id></element-citation></ref><ref id="B40-sensors-19-00479"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>G.</given-names></name><name><surname>Bui</surname><given-names>T.D.</given-names></name><name><surname>Quach</surname><given-names>K.G.</given-names></name><name><surname>Qian</surname><given-names>S.E.</given-names></name></person-group><article-title>Denoising hyperspectral imagery using principal component analysis and block-matching 4D filtering</article-title><source>Can. J. Remote Sens.</source><year>2014</year><volume>40</volume><fpage>60</fpage><lpage>66</lpage><pub-id pub-id-type="doi">10.1080/07038992.2014.917582</pub-id></element-citation></ref><ref id="B41-sensors-19-00479"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>H.</given-names></name><name><surname>He</surname><given-names>W.</given-names></name><name><surname>Zhang</surname><given-names>L.</given-names></name><name><surname>Shen</surname><given-names>H.</given-names></name><name><surname>Yuan</surname><given-names>Q.</given-names></name></person-group><article-title>Hyperspectral image restoration using low-rank matrix recovery</article-title><source>IEEE Trans. Geosci. Remote Sens.</source><year>2014</year><volume>52</volume><fpage>4729</fpage><lpage>4743</lpage><pub-id pub-id-type="doi">10.1109/TGRS.2013.2284280</pub-id></element-citation></ref><ref id="B42-sensors-19-00479"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>Y.</given-names></name><name><surname>Wu</surname><given-names>Z.</given-names></name><name><surname>Wei</surname><given-names>Z.</given-names></name></person-group><article-title>Spectral&#x02013;spatial classification of hyperspectral image based on low-rank decomposition</article-title><source>IEEE J. Sel. Top. Appl. Earth Obs. Remote Sens.</source><year>2015</year><volume>8</volume><fpage>2370</fpage><lpage>2380</lpage><pub-id pub-id-type="doi">10.1109/JSTARS.2015.2434997</pub-id></element-citation></ref><ref id="B43-sensors-19-00479"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fan</surname><given-names>F.</given-names></name><name><surname>Ma</surname><given-names>Y.</given-names></name><name><surname>Li</surname><given-names>C.</given-names></name><name><surname>Mei</surname><given-names>X.</given-names></name><name><surname>Huang</surname><given-names>J.</given-names></name><name><surname>Ma</surname><given-names>J.</given-names></name></person-group><article-title>Hyperspectral image denoising with superpixel segmentation and low-rank representation</article-title><source>Inf. Sci.</source><year>2017</year><volume>397</volume><fpage>48</fpage><lpage>68</lpage><pub-id pub-id-type="doi">10.1016/j.ins.2017.02.044</pub-id></element-citation></ref><ref id="B44-sensors-19-00479"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>S.</given-names></name><name><surname>Li</surname><given-names>S.</given-names></name><name><surname>Fu</surname><given-names>W.</given-names></name><name><surname>Fang</surname><given-names>L.</given-names></name></person-group><article-title>Multiscale superpixel-based sparse representation for hyperspectral image classification</article-title><source>Remote Sens.</source><year>2017</year><volume>9</volume><elocation-id>139</elocation-id><pub-id pub-id-type="doi">10.3390/rs9020139</pub-id></element-citation></ref><ref id="B45-sensors-19-00479"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sun</surname><given-names>L.</given-names></name><name><surname>Jeon</surname><given-names>B.</given-names></name><name><surname>Soomro</surname><given-names>B.N.</given-names></name><name><surname>Zheng</surname><given-names>Y.</given-names></name><name><surname>Wu</surname><given-names>Z.</given-names></name><name><surname>Xiao</surname><given-names>L.</given-names></name></person-group><article-title>Fast Superpixel Based Subspace Low Rank Learning Method for Hyperspectral Denoising</article-title><source>IEEE Access</source><year>2018</year><volume>6</volume><fpage>12031</fpage><lpage>12043</lpage><pub-id pub-id-type="doi">10.1109/ACCESS.2018.2808474</pub-id></element-citation></ref><ref id="B46-sensors-19-00479"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mei</surname><given-names>X.</given-names></name><name><surname>Ma</surname><given-names>Y.</given-names></name><name><surname>Li</surname><given-names>C.</given-names></name><name><surname>Fan</surname><given-names>F.</given-names></name><name><surname>Huang</surname><given-names>J.</given-names></name><name><surname>Ma</surname><given-names>J.</given-names></name></person-group><article-title>Robust GBM hyperspectral image unmixing with superpixel segmentation based low rank and sparse representation</article-title><source>Neurocomputing</source><year>2018</year><volume>275</volume><fpage>2783</fpage><lpage>2797</lpage><pub-id pub-id-type="doi">10.1016/j.neucom.2017.11.052</pub-id></element-citation></ref><ref id="B47-sensors-19-00479"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tong</surname><given-names>F.</given-names></name><name><surname>Tong</surname><given-names>H.</given-names></name><name><surname>Jiang</surname><given-names>J.</given-names></name><name><surname>Zhang</surname><given-names>Y.</given-names></name></person-group><article-title>Multiscale union regions adaptive sparse representation for hyperspectral image classification</article-title><source>Remote Sens.</source><year>2017</year><volume>9</volume><elocation-id>872</elocation-id><pub-id pub-id-type="doi">10.3390/rs9090872</pub-id></element-citation></ref><ref id="B48-sensors-19-00479"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zu</surname><given-names>B.</given-names></name><name><surname>Xia</surname><given-names>K.</given-names></name><name><surname>Du</surname><given-names>W.</given-names></name><name><surname>Li</surname><given-names>Y.</given-names></name><name><surname>Ali</surname><given-names>A.</given-names></name><name><surname>Chakraborty</surname><given-names>S.</given-names></name></person-group><article-title>Classification of Hyperspectral Images with Robust Regularized Block Low-Rank Discriminant Analysis</article-title><source>Remote Sens.</source><year>2018</year><volume>10</volume><elocation-id>817</elocation-id><pub-id pub-id-type="doi">10.3390/rs10060817</pub-id></element-citation></ref><ref id="B49-sensors-19-00479"><label>49.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Maier</surname><given-names>M.</given-names></name><name><surname>Luxburg</surname><given-names>U.V.</given-names></name><name><surname>Hein</surname><given-names>M.</given-names></name></person-group><article-title>Influence of graph construction on graph-based clustering measures</article-title><source>Proceedings of the Advances in Neural Information Processing Systems</source><conf-loc>Vancouver, BC, Canada</conf-loc><conf-date>7&#x02013;10 December 2009</conf-date><fpage>1025</fpage><lpage>1032</lpage></element-citation></ref><ref id="B50-sensors-19-00479"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>X.</given-names></name><name><surname>Goldberg</surname><given-names>A.B.</given-names></name><name><surname>Brachman</surname><given-names>R.</given-names></name><name><surname>Dietterich</surname><given-names>T.</given-names></name></person-group><article-title>Introduction to Semi-Supervised Learning</article-title><source>Semi-Superv. Learn.</source><year>2009</year><volume>3</volume><fpage>130</fpage><pub-id pub-id-type="doi">10.2200/S00196ED1V01Y200906AIM006</pub-id></element-citation></ref><ref id="B51-sensors-19-00479"><label>51.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>X.</given-names></name></person-group><source>Semi-Supervised Learning Literature Survey</source><publisher-name>Department of Computer Science, University of Wisconsin-Madison</publisher-name><publisher-loc>Madison, WI, USA</publisher-loc><year>2006</year><volume>Volume 2</volume><fpage>4</fpage></element-citation></ref><ref id="B52-sensors-19-00479"><label>52.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Stickel</surname><given-names>M.E.</given-names></name></person-group><source>A Nonclausal Connection-Graph Resolution Theorem-Proving Program</source><comment>Technical Report</comment><publisher-name>Sri International Menlo Park Ca Artificial Intelligence Center</publisher-name><publisher-loc>Menlo Park, CA, USA</publisher-loc><year>1982</year></element-citation></ref><ref id="B53-sensors-19-00479"><label>53.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Cortes</surname><given-names>C.</given-names></name><name><surname>Mohri</surname><given-names>M.</given-names></name></person-group><article-title>On transductive regression</article-title><source>Proceedings of the Advances in Neural Information Processing Systems</source><conf-loc>Vancouver, BC, Canada</conf-loc><conf-date>3&#x02013;6 December 2007</conf-date><volume>Volume 19</volume><fpage>305</fpage></element-citation></ref><ref id="B54-sensors-19-00479"><label>54.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Ren</surname><given-names>X.</given-names></name><name><surname>Malik</surname><given-names>J.</given-names></name></person-group><article-title>Learning a Classification Model for Segmentation</article-title><source>Proceedings of the Ninth IEEE International Conference on Computer Vision</source><conf-loc>Nice, France</conf-loc><conf-date>13&#x02013;16 October 2003</conf-date><fpage>10</fpage><lpage>17</lpage></element-citation></ref><ref id="B55-sensors-19-00479"><label>55.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Moore</surname><given-names>A.P.</given-names></name><name><surname>Prince</surname><given-names>S.J.D.</given-names></name><name><surname>Warrell</surname><given-names>J.</given-names></name><name><surname>Mohammed</surname><given-names>U.</given-names></name><name><surname>Jones</surname><given-names>G.</given-names></name></person-group><article-title>Superpixel lattices</article-title><source>Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR 2008)</source><conf-loc>Anchorage, AK, USA</conf-loc><conf-date>23&#x02013;28 June 2008</conf-date><fpage>1</fpage><lpage>8</lpage></element-citation></ref><ref id="B56-sensors-19-00479"><label>56.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Achanta</surname><given-names>R.</given-names></name><name><surname>Shaji</surname><given-names>A.</given-names></name><name><surname>Smith</surname><given-names>K.</given-names></name><name><surname>Lucchi</surname><given-names>A.</given-names></name><name><surname>Fua</surname><given-names>P.</given-names></name><name><surname>S&#x000fc;sstrunk</surname><given-names>S.</given-names></name></person-group><article-title>SLIC superpixels compared to state-of-the-art superpixel methods</article-title><source>IEEE Trans. Pattern Anal. Mach. Intell.</source><year>2012</year><volume>34</volume><fpage>2274</fpage><lpage>2282</lpage><pub-id pub-id-type="doi">10.1109/TPAMI.2012.120</pub-id><pub-id pub-id-type="pmid">22641706</pub-id></element-citation></ref><ref id="B57-sensors-19-00479"><label>57.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Achanta</surname><given-names>R.</given-names></name><name><surname>Shaji</surname><given-names>A.</given-names></name><name><surname>Smith</surname><given-names>K.</given-names></name><name><surname>Lucchi</surname><given-names>A.</given-names></name><name><surname>Fua</surname><given-names>P.</given-names></name><name><surname>S&#x000fc;sstrunk</surname><given-names>S.</given-names></name></person-group><source>Slic Superpixels</source><comment>EPFL Technical Report</comment><publisher-name>EPFL</publisher-name><publisher-loc>Lausanne, Switzerland</publisher-loc><year>2010</year></element-citation></ref><ref id="B58-sensors-19-00479"><label>58.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zitnick</surname><given-names>C.L.</given-names></name><name><surname>Kang</surname><given-names>S.B.</given-names></name></person-group><article-title>Stereo for Image-Based Rendering using Image Over-Segmentation</article-title><source>Int. J. Comput. Vis.</source><year>2007</year><volume>75</volume><fpage>49</fpage><lpage>65</lpage><pub-id pub-id-type="doi">10.1007/s11263-006-0018-8</pub-id></element-citation></ref><ref id="B59-sensors-19-00479"><label>59.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Ren</surname><given-names>C.Y.</given-names></name><name><surname>Reid</surname><given-names>I.</given-names></name></person-group><source>gSLIC: A Real-Time Implementation of SLIC Superpixel Segmentation</source><publisher-name>University of Oxford</publisher-name><publisher-loc>Oxford, UK</publisher-loc><year>2011</year></element-citation></ref><ref id="B60-sensors-19-00479"><label>60.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gastal</surname><given-names>E.S.</given-names></name><name><surname>Oliveira</surname><given-names>M.M.</given-names></name></person-group><article-title>Domain transform for edge-aware image and video processing</article-title><source>ACM Trans. Graph.</source><year>2011</year><volume>30</volume><fpage>69</fpage><pub-id pub-id-type="doi">10.1145/2010324.1964964</pub-id></element-citation></ref><ref id="B61-sensors-19-00479"><label>61.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ren</surname><given-names>C.-X.</given-names></name><name><surname>Dai</surname><given-names>D.-Q.</given-names></name><name><surname>Yan</surname><given-names>H.</given-names></name></person-group><article-title>Robust classification using L 2,1-norm based regression model</article-title><source>Pattern Recognit.</source><year>2012</year><volume>45</volume><fpage>2708</fpage><lpage>2718</lpage><pub-id pub-id-type="doi">10.1016/j.patcog.2012.01.003</pub-id></element-citation></ref><ref id="B62-sensors-19-00479"><label>62.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Nie</surname><given-names>F.</given-names></name><name><surname>Huang</surname><given-names>H.</given-names></name><name><surname>Cai</surname><given-names>X.</given-names></name><name><surname>Ding</surname><given-names>C.H.</given-names></name></person-group><article-title>Efficient and robust feature selection via joint L 2, 1-norms minimization</article-title><source>Proceedings of the Advances in Neural Information Processing Systems</source><conf-loc>Vancouver, BC, Canada</conf-loc><conf-date>6&#x02013;9 December 2010</conf-date><fpage>1813</fpage><lpage>1821</lpage></element-citation></ref><ref id="B63-sensors-19-00479"><label>63.</label><element-citation publication-type="other"><person-group person-group-type="author"><name><surname>Lin</surname><given-names>Z.</given-names></name><name><surname>Chen</surname><given-names>M.</given-names></name><name><surname>Ma</surname><given-names>Y.</given-names></name></person-group><article-title>The augmented lagrange multiplier method for exact recovery of corrupted low-rank matrices</article-title><source>arXiv</source><year>2010</year><pub-id pub-id-type="arxiv">1009.5055</pub-id></element-citation></ref><ref id="B64-sensors-19-00479"><label>64.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>G.</given-names></name><name><surname>Lin</surname><given-names>Z.</given-names></name><name><surname>Yan</surname><given-names>S.</given-names></name><name><surname>Sun</surname><given-names>J.</given-names></name><name><surname>Yu</surname><given-names>Y.</given-names></name><name><surname>Ma</surname><given-names>Y.</given-names></name></person-group><article-title>Robust recovery of subspace structures by low-rank representation</article-title><source>IEEE Trans. Pattern Anal. Mach. Intell.</source><year>2013</year><volume>35</volume><fpage>171</fpage><lpage>184</lpage><pub-id pub-id-type="doi">10.1109/TPAMI.2012.88</pub-id><?supplied-pmid 22487984?><pub-id pub-id-type="pmid">22487984</pub-id></element-citation></ref><ref id="B65-sensors-19-00479"><label>65.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Cai</surname><given-names>D.</given-names></name><name><surname>He</surname><given-names>X.</given-names></name><name><surname>Han</surname><given-names>J.</given-names></name></person-group><article-title>Semi-supervised discriminant analysis</article-title><source>Proceedings of the IEEE 11th International Confece on IEEE Computer Vision (ICCV)</source><conf-loc>Rio de Janeiro, Brazil</conf-loc><conf-date>14&#x02013;21 October 2007</conf-date><fpage>1</fpage><lpage>7</lpage></element-citation></ref><ref id="B66-sensors-19-00479"><label>66.</label><element-citation publication-type="confproc"><person-group person-group-type="author"><name><surname>Yan</surname><given-names>S.</given-names></name><name><surname>Wang</surname><given-names>H.</given-names></name></person-group><article-title>Semi-supervised Learning by Sparse Representation</article-title><source>Proceedings of the 2009 SIAM International Confece on Data Mining</source><conf-loc>Sparks, NV, USA</conf-loc><conf-date>30 April&#x02013;2 May 2009</conf-date><fpage>792</fpage><lpage>801</lpage></element-citation></ref><ref id="B67-sensors-19-00479"><label>67.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wright</surname><given-names>J.</given-names></name><name><surname>Yang</surname><given-names>A.Y.</given-names></name><name><surname>Ganesh</surname><given-names>A.</given-names></name><name><surname>Sastry</surname><given-names>S.S.</given-names></name><name><surname>Ma</surname><given-names>Y.</given-names></name></person-group><article-title>Robust face recognition via sparse representation</article-title><source>IEEE Trans. Pattern Anal. Mach. Intell.</source><year>2009</year><volume>31</volume><fpage>210</fpage><lpage>227</lpage><pub-id pub-id-type="doi">10.1109/TPAMI.2008.79</pub-id><pub-id pub-id-type="pmid">19110489</pub-id></element-citation></ref><ref id="B68-sensors-19-00479"><label>68.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zu</surname><given-names>B.</given-names></name><name><surname>Xia</surname><given-names>K.</given-names></name><name><surname>Pan</surname><given-names>Y.</given-names></name><name><surname>Niu</surname><given-names>W.</given-names></name></person-group><article-title>A Novel Graph Constructor for Semisupervised Discriminant Analysis: Combined Low-Rank and-Nearest Neighbor Graph</article-title><source>Comput. Intell. Neurosci.</source><year>2017</year><volume>2017</volume><fpage>9290230</fpage><pub-id pub-id-type="doi">10.1155/2017/9290230</pub-id><?supplied-pmid 28316616?><pub-id pub-id-type="pmid">28316616</pub-id></element-citation></ref><ref id="B69-sensors-19-00479"><label>69.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Peterson</surname><given-names>L.</given-names></name></person-group><article-title>K-nearest neighbor</article-title><source>Scholarpedia</source><year>2009</year><volume>4</volume><fpage>1883</fpage><pub-id pub-id-type="doi">10.4249/scholarpedia.1883</pub-id></element-citation></ref><ref id="B70-sensors-19-00479"><label>70.</label><element-citation publication-type="web"><person-group person-group-type="author"><name><surname>Baumgardner</surname><given-names>M.F.</given-names></name><name><surname>Biehl</surname><given-names>L.L.</given-names></name><name><surname>Landgrebe</surname><given-names>D.A.</given-names></name></person-group><article-title>220 Band AVIRIS Hyperspectral Image Data Set: June 12, 1992 Indian Pine Test Site 3</article-title><year>2015</year><comment>Available online: <ext-link ext-link-type="uri" xlink:href="https://purr.purdue.edu/publications/1947/1">https://purr.purdue.edu/publications/1947/1</ext-link></comment><date-in-citation content-type="access-date" iso-8601-date="2018-01-18">(accessed on 18 January 2018)</date-in-citation></element-citation></ref><ref id="B71-sensors-19-00479"><label>71.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Congalton</surname><given-names>R.G.</given-names></name></person-group><article-title>A review of assessing the accuracy of classifications of remotely sensed data</article-title><source>Remote Sens. Environ.</source><year>1991</year><volume>37</volume><fpage>35</fpage><lpage>46</lpage><pub-id pub-id-type="doi">10.1016/0034-4257(91)90048-B</pub-id></element-citation></ref><ref id="B72-sensors-19-00479"><label>72.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Richards</surname><given-names>J.A.</given-names></name><name><surname>Richards</surname><given-names>J.</given-names></name></person-group><source>Remote Sensing Digital Image Analysis</source><publisher-name>Springer</publisher-name><publisher-loc>Berlin, Germany</publisher-loc><year>1999</year><volume>Volume 3</volume></element-citation></ref><ref id="B73-sensors-19-00479"><label>73.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thompson</surname><given-names>W.D.</given-names></name><name><surname>Walter</surname><given-names>S.D.</given-names></name></person-group><article-title>A reappraisal of the kappa coefficient</article-title><source>J. Clin. Epidemiol.</source><year>1988</year><volume>41</volume><fpage>949</fpage><lpage>958</lpage><pub-id pub-id-type="doi">10.1016/0895-4356(88)90031-5</pub-id><pub-id pub-id-type="pmid">3057117</pub-id></element-citation></ref><ref id="B74-sensors-19-00479"><label>74.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gwet</surname><given-names>K.</given-names></name></person-group><article-title>Kappa statistic is not satisfactory for assessing the extent of agreement between raters</article-title><source>Stat. Methods Inter-Rater Reliab. Assess.</source><year>2002</year><volume>1</volume><fpage>1</fpage><lpage>6</lpage></element-citation></ref></ref-list></back><floats-group><fig id="sensors-19-00479-f001" orientation="portrait" position="float"><label>Figure 1</label><caption><p>Formulation of the proposed SLIC superpixel-based <inline-formula><mml:math id="mm282"><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:math></inline-formula>-norm robust principal component analysis for HSIs classification.</p></caption><graphic xlink:href="sensors-19-00479-g001"/></fig><fig id="sensors-19-00479-f002" orientation="portrait" position="float"><label>Figure 2</label><caption><p>Simple linear iterative clustering (SLIC) reducing the search regions.</p></caption><graphic xlink:href="sensors-19-00479-g002"/></fig><fig id="sensors-19-00479-f003" orientation="portrait" position="float"><label>Figure 3</label><caption><p>Model diagram of the SURPCA<sub>2,1</sub> for hyperspectral image classification.</p></caption><graphic xlink:href="sensors-19-00479-g003"/></fig><fig id="sensors-19-00479-f004" orientation="portrait" position="float"><label>Figure 4</label><caption><p>Indian Pines dataset: (<bold>a</bold>) false-color image; (<bold>b</bold>) segmentation map; and (<bold>c</bold>,<bold>d</bold>) ground truth image and reference data.</p></caption><graphic xlink:href="sensors-19-00479-g004"/></fig><fig id="sensors-19-00479-f005" orientation="portrait" position="float"><label>Figure 5</label><caption><p>Pavia University scene; (<bold>a</bold>) false-color image; (<bold>b</bold>) segmentation map; and (<bold>c</bold>,<bold>d</bold>) ground truth image and reference data.</p></caption><graphic xlink:href="sensors-19-00479-g005"/></fig><fig id="sensors-19-00479-f006" orientation="portrait" position="float"><label>Figure 6</label><caption><p>Indian Pines image classification results: (<bold>a</bold>) robust principal component analysis (RPCA<sub>2,1</sub>); (<bold>b</bold>) RPCA; (<bold>c</bold>) PCA; (<bold>d</bold>) image fusion and recursive filtering (IFRF); and (<bold>e</bold>) Origin. OA, overall accuracy.</p></caption><graphic xlink:href="sensors-19-00479-g006"/></fig><fig id="sensors-19-00479-f007" orientation="portrait" position="float"><label>Figure 7</label><caption><p>Pavia University scene classification results: (<bold>a</bold>) RPCA<sub>2,1</sub>; (<bold>b</bold>) RPCA; (<bold>c</bold>) PCA; (<bold>d</bold>) IFRF; and (<bold>e</bold>) Origin. OA, overall accuracy.</p></caption><graphic xlink:href="sensors-19-00479-g007"/></fig><fig id="sensors-19-00479-f008" orientation="portrait" position="float"><label>Figure 8</label><caption><p>HSIs classification accuracy with varying labeled ratio.</p></caption><graphic xlink:href="sensors-19-00479-g008"/></fig><fig id="sensors-19-00479-f009" orientation="portrait" position="float"><label>Figure 9</label><caption><p>False-color clean and noisy images of the Indian Pines image.</p></caption><graphic xlink:href="sensors-19-00479-g009"/></fig><fig id="sensors-19-00479-f010" orientation="portrait" position="float"><label>Figure 10</label><caption><p>Noisy Indian Pines image classification results: (<bold>a</bold>) RPCA<sub>2,1</sub>; (<bold>b</bold>) RPCA; (<bold>c</bold>) PCA; (<bold>d</bold>) IFRF; and (<bold>e</bold>) Origin. OA, overall accuracy.</p></caption><graphic xlink:href="sensors-19-00479-g010"/></fig><fig id="sensors-19-00479-f011" orientation="portrait" position="float"><label>Figure 11</label><caption><p>Noisy Pavia University scene classification results: (<bold>a</bold>) RPCA<sub>2,1</sub>; (<bold>b</bold>) RPCA; (<bold>c</bold>) PCA; (<bold>d</bold>) IFRF; and (<bold>e</bold>) Origin. OA, overall accuracy.</p></caption><graphic xlink:href="sensors-19-00479-g011"/></fig><fig id="sensors-19-00479-f012" orientation="portrait" position="float"><label>Figure 12</label><caption><p>Classification accuracy of HSIs with different reduced dimensions.</p></caption><graphic xlink:href="sensors-19-00479-g012"/></fig><table-wrap id="sensors-19-00479-t001" orientation="portrait" position="float"><object-id pub-id-type="pii">sensors-19-00479-t001_Table 1</object-id><label>Table 1</label><caption><p>Training and testing samples for the three hyperspectral images.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-top:solid thin" rowspan="1" colspan="1">Class</th><th colspan="3" align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1">Indian Pines Image</th><th colspan="3" align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1">Pavia University Scene</th><th colspan="3" align="center" valign="middle" style="border-top:solid thin;border-bottom:solid thin" rowspan="1">Salinas Image</th></tr><tr><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Train</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Test</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Sample No.</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Train</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Test</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Sample No.</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Train</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Test</th><th align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Sample No.</th></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">C1</td><td align="center" valign="middle" rowspan="1" colspan="1">7</td><td align="center" valign="middle" rowspan="1" colspan="1">39</td><td align="center" valign="middle" rowspan="1" colspan="1">46</td><td align="center" valign="middle" rowspan="1" colspan="1">205</td><td align="center" valign="middle" rowspan="1" colspan="1">6426</td><td align="center" valign="middle" rowspan="1" colspan="1">6631</td><td align="center" valign="middle" rowspan="1" colspan="1">14</td><td align="center" valign="middle" rowspan="1" colspan="1">1995</td><td align="center" valign="middle" rowspan="1" colspan="1">2009</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C2</td><td align="center" valign="middle" rowspan="1" colspan="1">63</td><td align="center" valign="middle" rowspan="1" colspan="1">1365</td><td align="center" valign="middle" rowspan="1" colspan="1">1428</td><td align="center" valign="middle" rowspan="1" colspan="1">565</td><td align="center" valign="middle" rowspan="1" colspan="1">18,084</td><td align="center" valign="middle" rowspan="1" colspan="1">18,649</td><td align="center" valign="middle" rowspan="1" colspan="1">20</td><td align="center" valign="middle" rowspan="1" colspan="1">3706</td><td align="center" valign="middle" rowspan="1" colspan="1">3726</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C3</td><td align="center" valign="middle" rowspan="1" colspan="1">39</td><td align="center" valign="middle" rowspan="1" colspan="1">791</td><td align="center" valign="middle" rowspan="1" colspan="1">830</td><td align="center" valign="middle" rowspan="1" colspan="1">69</td><td align="center" valign="middle" rowspan="1" colspan="1">2030</td><td align="center" valign="middle" rowspan="1" colspan="1">2099</td><td align="center" valign="middle" rowspan="1" colspan="1">13</td><td align="center" valign="middle" rowspan="1" colspan="1">1963</td><td align="center" valign="middle" rowspan="1" colspan="1">1976</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C4</td><td align="center" valign="middle" rowspan="1" colspan="1">15</td><td align="center" valign="middle" rowspan="1" colspan="1">222</td><td align="center" valign="middle" rowspan="1" colspan="1">237</td><td align="center" valign="middle" rowspan="1" colspan="1">97</td><td align="center" valign="middle" rowspan="1" colspan="1">2967</td><td align="center" valign="middle" rowspan="1" colspan="1">3064</td><td align="center" valign="middle" rowspan="1" colspan="1">11</td><td align="center" valign="middle" rowspan="1" colspan="1">1383</td><td align="center" valign="middle" rowspan="1" colspan="1">1394</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C5</td><td align="center" valign="middle" rowspan="1" colspan="1">25</td><td align="center" valign="middle" rowspan="1" colspan="1">458</td><td align="center" valign="middle" rowspan="1" colspan="1">483</td><td align="center" valign="middle" rowspan="1" colspan="1">46</td><td align="center" valign="middle" rowspan="1" colspan="1">1299</td><td align="center" valign="middle" rowspan="1" colspan="1">1345</td><td align="center" valign="middle" rowspan="1" colspan="1">16</td><td align="center" valign="middle" rowspan="1" colspan="1">2662</td><td align="center" valign="middle" rowspan="1" colspan="1">2678</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C6</td><td align="center" valign="middle" rowspan="1" colspan="1">35</td><td align="center" valign="middle" rowspan="1" colspan="1">695</td><td align="center" valign="middle" rowspan="1" colspan="1">730</td><td align="center" valign="middle" rowspan="1" colspan="1">156</td><td align="center" valign="middle" rowspan="1" colspan="1">4873</td><td align="center" valign="middle" rowspan="1" colspan="1">5029</td><td align="center" valign="middle" rowspan="1" colspan="1">21</td><td align="center" valign="middle" rowspan="1" colspan="1">3938</td><td align="center" valign="middle" rowspan="1" colspan="1">3959</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C7</td><td align="center" valign="middle" rowspan="1" colspan="1">7</td><td align="center" valign="middle" rowspan="1" colspan="1">21</td><td align="center" valign="middle" rowspan="1" colspan="1">28</td><td align="center" valign="middle" rowspan="1" colspan="1">45</td><td align="center" valign="middle" rowspan="1" colspan="1">1285</td><td align="center" valign="middle" rowspan="1" colspan="1">1330</td><td align="center" valign="middle" rowspan="1" colspan="1">20</td><td align="center" valign="middle" rowspan="1" colspan="1">3559</td><td align="center" valign="middle" rowspan="1" colspan="1">3579</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C8</td><td align="center" valign="middle" rowspan="1" colspan="1">25</td><td align="center" valign="middle" rowspan="1" colspan="1">453</td><td align="center" valign="middle" rowspan="1" colspan="1">478</td><td align="center" valign="middle" rowspan="1" colspan="1">116</td><td align="center" valign="middle" rowspan="1" colspan="1">3566</td><td align="center" valign="middle" rowspan="1" colspan="1">3682</td><td align="center" valign="middle" rowspan="1" colspan="1">51</td><td align="center" valign="middle" rowspan="1" colspan="1">11,220</td><td align="center" valign="middle" rowspan="1" colspan="1">11,271</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C9</td><td align="center" valign="middle" rowspan="1" colspan="1">6</td><td align="center" valign="middle" rowspan="1" colspan="1">14</td><td align="center" valign="middle" rowspan="1" colspan="1">20</td><td align="center" valign="middle" rowspan="1" colspan="1">34</td><td align="center" valign="middle" rowspan="1" colspan="1">913</td><td align="center" valign="middle" rowspan="1" colspan="1">947</td><td align="center" valign="middle" rowspan="1" colspan="1">30</td><td align="center" valign="middle" rowspan="1" colspan="1">6173</td><td align="center" valign="middle" rowspan="1" colspan="1">6203</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C10</td><td align="center" valign="middle" rowspan="1" colspan="1">44</td><td align="center" valign="middle" rowspan="1" colspan="1">928</td><td align="center" valign="middle" rowspan="1" colspan="1">972</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">19</td><td align="center" valign="middle" rowspan="1" colspan="1">3259</td><td align="center" valign="middle" rowspan="1" colspan="1">3278</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C11</td><td align="center" valign="middle" rowspan="1" colspan="1">104</td><td align="center" valign="middle" rowspan="1" colspan="1">2351</td><td align="center" valign="middle" rowspan="1" colspan="1">2455</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">10</td><td align="center" valign="middle" rowspan="1" colspan="1">1058</td><td align="center" valign="middle" rowspan="1" colspan="1">1068</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C12</td><td align="center" valign="middle" rowspan="1" colspan="1">29</td><td align="center" valign="middle" rowspan="1" colspan="1">564</td><td align="center" valign="middle" rowspan="1" colspan="1">593</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">13</td><td align="center" valign="middle" rowspan="1" colspan="1">1914</td><td align="center" valign="middle" rowspan="1" colspan="1">1927</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C13</td><td align="center" valign="middle" rowspan="1" colspan="1">14</td><td align="center" valign="middle" rowspan="1" colspan="1">191</td><td align="center" valign="middle" rowspan="1" colspan="1">205</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">9</td><td align="center" valign="middle" rowspan="1" colspan="1">907</td><td align="center" valign="middle" rowspan="1" colspan="1">916</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C14</td><td align="center" valign="middle" rowspan="1" colspan="1">56</td><td align="center" valign="middle" rowspan="1" colspan="1">1209</td><td align="center" valign="middle" rowspan="1" colspan="1">1265</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">10</td><td align="center" valign="middle" rowspan="1" colspan="1">1060</td><td align="center" valign="middle" rowspan="1" colspan="1">1070</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C15</td><td align="center" valign="middle" rowspan="1" colspan="1">21</td><td align="center" valign="middle" rowspan="1" colspan="1">365</td><td align="center" valign="middle" rowspan="1" colspan="1">386</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">-</td><td align="center" valign="middle" rowspan="1" colspan="1">35</td><td align="center" valign="middle" rowspan="1" colspan="1">7233</td><td align="center" valign="middle" rowspan="1" colspan="1">7268</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">C16</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">9</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">84</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">93</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">-</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">-</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">-</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">13</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">1794</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">1807</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Total</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">702</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">9547</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">10,249</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">1762</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">41,014</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">42,776</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">305</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">53,824</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">54,129</td></tr></tbody></table></table-wrap><table-wrap id="sensors-19-00479-t002" orientation="portrait" position="float"><object-id pub-id-type="pii">sensors-19-00479-t002_Table 2</object-id><label>Table 2</label><caption><p>Indian Pines image classification results.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Method</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origion</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origin</th></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>Classifier</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>NN</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>SVM</bold>
</td></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">C1</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9870</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9809</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8051</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9473</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C2</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9686</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9442</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9380</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9487</td><td align="center" valign="middle" rowspan="1" colspan="1">0.4961</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9676</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9701</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9295</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9482</td><td align="center" valign="middle" rowspan="1" colspan="1">0.5169</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C3</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9667</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9524</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9718</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9342</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6572</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9659</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9679</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9426</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9574</td><td align="center" valign="middle" rowspan="1" colspan="1">0.3793</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C4</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9578</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9698</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9589</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9122</td><td align="center" valign="middle" rowspan="1" colspan="1">0.3964</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9819</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9133</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9383</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9420</td><td align="center" valign="middle" rowspan="1" colspan="1">0.5671</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C5</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9868</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9463</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9384</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9687</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8225</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9933</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9856</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9821</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9868</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8809</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C6</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9837</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9833</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9394</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9483</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7568</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9986</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9986</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8476</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C7</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8686</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7254</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6164</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8107</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6786</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9375</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9886</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9188</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C8</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9486</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9367</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C9</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9800</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7812</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8496</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7469</td><td align="center" valign="middle" rowspan="1" colspan="1">0.5042</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8042</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C10</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9342</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9139</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9215</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8742</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6777</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9647</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9618</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9743</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9471</td><td align="center" valign="middle" rowspan="1" colspan="1">0.4916</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C11</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9721</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9700</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9748</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9749</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6933</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9672</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9604</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9626</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9677</td><td align="center" valign="middle" rowspan="1" colspan="1">0.5560</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C12</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9602</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9024</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9753</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9344</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6833</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9854</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9908</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9658</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9701</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6454</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C13</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9705</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9748</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9935</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9240</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9987</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9909</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C14</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9952</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9895</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9939</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9915</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9217</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9954</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9996</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9975</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9975</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9190</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C15</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9624</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9711</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9768</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9379</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6666</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9916</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9937</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9958</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9897</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7551</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">C16</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>0.9905</bold>
</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9881</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9881</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9881</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9871</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9866</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9859</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9712</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9846</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9825</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">OA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9706</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9585</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9594</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9514</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7077</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9786</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9759</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9682</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9713</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6536</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">AA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9686</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9399</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9378</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9341</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7262</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9875</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9792</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9787</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9798</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7587</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Kappa</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9664</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9527</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9536</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9446</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.6633</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>0.9755</bold>
</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9724</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9636</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9672</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.6000</td></tr></tbody></table></table-wrap><table-wrap id="sensors-19-00479-t003" orientation="portrait" position="float"><object-id pub-id-type="pii">sensors-19-00479-t003_Table 3</object-id><label>Table 3</label><caption><p>Pavia University scene classification results.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Method</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origion</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origin</th></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>Classifier</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>NN</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>SVM</bold>
</td></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">C1</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9581</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9630</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9632</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9581</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8969</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9771</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9745</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9698</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9657</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8119</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C2</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9925</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9942</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9935</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9944</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8491</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9957</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.996</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9956</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9951</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8676</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C3</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9465</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9420</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9212</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9543</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6836</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9758</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9678</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9715</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9578</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6425</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C4</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9923</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9841</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9865</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9880</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9360</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9918</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9889</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9938</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9895</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8802</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C5</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9993</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9989</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9957</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9985</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9929</td><td align="center" valign="middle" rowspan="1" colspan="1">0.917</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9377</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9433</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9355</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9972</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C6</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9966</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9953</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9921</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9981</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7228</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9965</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9963</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9953</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9977</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7286</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C7</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9177</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9090</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8921</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8822</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7385</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9751</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9689</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9642</td><td align="center" valign="middle" rowspan="1" colspan="1">0.963</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6193</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">C8</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9169</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9082</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9182</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9135</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7990</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9656</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9589</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9575</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9574</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7057</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">C9</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9353</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9302</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9369</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9509</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>1</bold>
</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.964</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9577</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9546</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9575</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9996</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">OA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9751</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9744</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9734</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9748</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8425</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9849</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9839</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9832</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9815</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8238</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">AA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9617</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9583</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9555</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9598</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8465</td><td align="center" valign="middle" rowspan="1" colspan="1">
<bold>0.9732</bold>
</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9719</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9717</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9688</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8058</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Kappa</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9670</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9660</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9647</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9666</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.7869</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>0.98</bold>
</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9787</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9777</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9754</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.7628</td></tr></tbody></table></table-wrap><table-wrap id="sensors-19-00479-t004" orientation="portrait" position="float"><object-id pub-id-type="pii">sensors-19-00479-t004_Table 4</object-id><label>Table 4</label><caption><p>Different graphs&#x02019; running time on real-word hyperspectral images (HSIs) (unit:s). NN, nearest neighbor; SVM, support vector machine.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Method</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origion</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origin</th></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">
<bold>Classifier</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>NN</bold>
</td><td colspan="5" align="center" valign="middle" style="border-bottom:solid thin" rowspan="1">
<bold>SVM</bold>
</td></tr></thead><tbody><tr><td align="center" valign="middle" rowspan="1" colspan="1">Indian Pines image</td><td align="center" valign="middle" rowspan="1" colspan="1">16.73</td><td align="center" valign="middle" rowspan="1" colspan="1">21.62</td><td align="center" valign="middle" rowspan="1" colspan="1">5.16</td><td align="center" valign="middle" rowspan="1" colspan="1">5.31</td><td align="center" valign="middle" rowspan="1" colspan="1">5.69</td><td align="center" valign="middle" rowspan="1" colspan="1">27.94</td><td align="center" valign="middle" rowspan="1" colspan="1">34.53</td><td align="center" valign="middle" rowspan="1" colspan="1">14.39</td><td align="center" valign="middle" rowspan="1" colspan="1">16.90</td><td align="center" valign="middle" rowspan="1" colspan="1">17.34</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">University of Pavia</td><td align="center" valign="middle" rowspan="1" colspan="1">83.89</td><td align="center" valign="middle" rowspan="1" colspan="1">100.31</td><td align="center" valign="middle" rowspan="1" colspan="1">54.28</td><td align="center" valign="middle" rowspan="1" colspan="1">56.92</td><td align="center" valign="middle" rowspan="1" colspan="1">47.11</td><td align="center" valign="middle" rowspan="1" colspan="1">115.84</td><td align="center" valign="middle" rowspan="1" colspan="1">135.86</td><td align="center" valign="middle" rowspan="1" colspan="1">88.52</td><td align="center" valign="middle" rowspan="1" colspan="1">90.26</td><td align="center" valign="middle" rowspan="1" colspan="1">116.61</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Salinas image</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">15.55</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">18.14</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">4.24</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">4.27</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">3.22</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">28.56</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">32.61</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">14.39</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">14.32</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">15.29</td></tr></tbody></table></table-wrap><table-wrap id="sensors-19-00479-t005" orientation="portrait" position="float"><object-id pub-id-type="pii">sensors-19-00479-t005_Table 5</object-id><label>Table 5</label><caption><p>Classification results with Gaussian noise on three HSIs.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Images</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">
</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA<sub>2,1</sub></th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">RPCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">PCA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">IFRF</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Origin</th></tr></thead><tbody><tr><td rowspan="3" align="center" valign="middle" style="border-bottom:solid thin" colspan="1">Indian Pines image</td><td align="center" valign="middle" rowspan="1" colspan="1">OA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9004 &#x000b1; 0.0103</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8615 &#x000b1; 0.0137</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8736 &#x000b1; 0.0067</td><td align="center" valign="middle" rowspan="1" colspan="1">0.86261 &#x000b1; 0.0937</td><td align="center" valign="middle" rowspan="1" colspan="1">0.5008 &#x000b1; 0.006</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">AA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9198 &#x000b1; 0.0179</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8481 &#x000b1; 0.0261</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8573 &#x000b1; 0.024</td><td align="center" valign="middle" rowspan="1" colspan="1">0.8367 &#x000b1; 0.0231</td><td align="center" valign="middle" rowspan="1" colspan="1">0.4530 &#x000b1; 0.0256</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Kappa</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8863 &#x000b1; 0.011</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8426 &#x000b1; 0.0143</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8561 &#x000b1; 0.0257</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8434 &#x000b1; 0.00763</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.4230 &#x000b1; 0.0069</td></tr><tr><td rowspan="3" align="center" valign="middle" style="border-bottom:solid thin" colspan="1">University of Pavia image</td><td align="center" valign="middle" rowspan="1" colspan="1">OA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9261 &#x000b1; 0.0041</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9156 &#x000b1; 0.004</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9116 &#x000b1; 0.0057</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9182 &#x000b1; 0.0035</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6721 &#x000b1; 0.0079</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">AA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9212 &#x000b1; 0.0054</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9085 &#x000b1; 0.0066</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9055 &#x000b1; 0.0107</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9105 &#x000b1; 0.0077</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6162 &#x000b1; 0.0088</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Kappa</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9012 &#x000b1; 0.0055</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8871 &#x000b1; 0.0055</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8815 &#x000b1; 0.0078</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8906 &#x000b1; 0.0048</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.5527 &#x000b1; 0.0097</td></tr><tr><td rowspan="3" align="center" valign="middle" style="border-bottom:solid thin" colspan="1">Salinas image</td><td align="center" valign="middle" rowspan="1" colspan="1">OA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9329 &#x000b1; 0.0078</td><td align="center" valign="middle" rowspan="1" colspan="1">0.911 &#x000b1; 0.0062</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9125 &#x000b1; 0.0072</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9095 &#x000b1; 0.0108</td><td align="center" valign="middle" rowspan="1" colspan="1">0.6658 &#x000b1; 0.0101</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">AA</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9523 &#x000b1; 0.0051</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9268 &#x000b1; 0.0054</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9294 &#x000b1; 0.0089</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9247 &#x000b1; 0.0102</td><td align="center" valign="middle" rowspan="1" colspan="1">0.7088 &#x000b1; 0.0098</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">Kappa</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9253 &#x000b1; 0.0086</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.901 &#x000b1; 0.0069</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9027 &#x000b1; 0.008</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.8994 &#x000b1; 0.012</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.625 &#x000b1; 0.0106</td></tr></tbody></table></table-wrap><table-wrap id="sensors-19-00479-t006" orientation="portrait" position="float"><object-id pub-id-type="pii">sensors-19-00479-t006_Table 6</object-id><label>Table 6</label><caption><p>SURPCA<sub>2,1</sub> graph&#x02019;s classification accuracy with different segmentation numbers.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Images</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">No. <italic>m</italic> of Superpixels</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">OA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">AA</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Kappa</th><th align="center" valign="middle" style="border-bottom:solid thin;border-top:solid thin" rowspan="1" colspan="1">Time (s)</th></tr></thead><tbody><tr><td rowspan="3" align="center" valign="middle" style="border-bottom:solid thin" colspan="1">Indian Pines image</td><td align="center" valign="middle" rowspan="1" colspan="1">104</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9633 &#x000b1; 0.0072</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9507 &#x000b1; 0.027</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9581 &#x000b1; 0.0091</td><td align="center" valign="middle" rowspan="1" colspan="1">12.43</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">226</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9703 &#x000b1; 0.006</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9687 &#x000b1; 0.032</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9661 &#x000b1; 0.0062</td><td align="center" valign="middle" rowspan="1" colspan="1">17.96</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">329</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9721 &#x000b1; 0.0053</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9657 &#x000b1; 0.0254</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9682 &#x000b1; 0.0069</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">21.80</td></tr><tr><td rowspan="4" align="center" valign="middle" style="border-bottom:solid thin" colspan="1">University of Pavia image</td><td align="center" valign="middle" rowspan="1" colspan="1">195</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9697 &#x000b1; 0.0035</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9473 &#x000b1; 0.0061</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9598 &#x000b1; 0.0047</td><td align="center" valign="middle" rowspan="1" colspan="1">75.15</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">386</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9711 &#x000b1; 0.0043</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9564 &#x000b1; 0.0084</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9616 &#x000b1; 0.0058</td><td align="center" valign="middle" rowspan="1" colspan="1">78.33</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">594</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9753 &#x000b1; 0.0026</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9614 &#x000b1; 0.0046</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9672 &#x000b1; 0.0035</td><td align="center" valign="middle" rowspan="1" colspan="1">81.20</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">813</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.975 &#x000b1; 0.0018</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9596 &#x000b1; 0.0053</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9668 &#x000b1; 0.0024</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">85.97</td></tr><tr><td rowspan="3" align="center" valign="middle" style="border-bottom:solid thin" colspan="1">Salinas image</td><td align="center" valign="middle" rowspan="1" colspan="1">187</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9666 &#x000b1; 0.0059</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9743 &#x000b1; 0.0064</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9629 &#x000b1; 0.0065</td><td align="center" valign="middle" rowspan="1" colspan="1">87.71</td></tr><tr><td align="center" valign="middle" rowspan="1" colspan="1">389</td><td align="center" valign="middle" rowspan="1" colspan="1">0.978 &#x000b1; 0.0042</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9813 &#x000b1; 0.003</td><td align="center" valign="middle" rowspan="1" colspan="1">0.9755 &#x000b1; 0.0047</td><td align="center" valign="middle" rowspan="1" colspan="1">92.96</td></tr><tr><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">593</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9823 &#x000b1; 0.0037</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9826 &#x000b1; 0.003</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">0.9804 &#x000b1; 0.0041</td><td align="center" valign="middle" style="border-bottom:solid thin" rowspan="1" colspan="1">98.91</td></tr></tbody></table></table-wrap></floats-group></article>