<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Genomics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Genomics</journal-id><journal-title-group><journal-title>BMC Genomics</journal-title></journal-title-group><issn pub-type="epub">1471-2164</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7114785</article-id><article-id pub-id-type="publisher-id">6657</article-id><article-id pub-id-type="doi">10.1186/s12864-020-6657-2</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>An improved de novo genome assembly of the common marmoset genome yields improved contiguity and increased mapping rates of sequence data</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Jayakumar</surname><given-names>Vasanthan</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Ishii</surname><given-names>Hiromi</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Seki</surname><given-names>Misato</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Kumita</surname><given-names>Wakako</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Inoue</surname><given-names>Takashi</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Hase</surname><given-names>Sumitaka</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Sato</surname><given-names>Kengo</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Okano</surname><given-names>Hideyuki</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Sasaki</surname><given-names>Erika</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Sakakibara</surname><given-names>Yasubumi</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 9959</institution-id><institution-id institution-id-type="GRID">grid.26091.3c</institution-id><institution>Department of Biosciences and Informatics, </institution><institution>Keio University, </institution></institution-wrap>Yokohama, Kanagawa 223-8522 Japan </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0376 978X</institution-id><institution-id institution-id-type="GRID">grid.452212.2</institution-id><institution>Department of Marmoset Biology and Medicine, </institution><institution>Central Institute for Experimental Animals, </institution></institution-wrap>Kawasaki, Kanagawa 210-0821 Japan </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 9959</institution-id><institution-id institution-id-type="GRID">grid.26091.3c</institution-id><institution>Department of Physiology, </institution><institution>Keio University School of Medicine, </institution></institution-wrap>Shinjuku, Tokyo, 160-8582 Japan </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="GRID">grid.474690.8</institution-id><institution>Laboratory for Marmoset Neural Architecture, </institution><institution>RIKEN Center for Brain Science, </institution></institution-wrap>Wako-shi, Saitama, 351-0198 Japan </aff></contrib-group><pub-date pub-type="epub"><day>2</day><month>4</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>2</day><month>4</month><year>2020</year></pub-date><pub-date pub-type="collection"><year>2020</year></pub-date><volume>21</volume><issue>Suppl 3</issue><issue-sponsor>Publication of this supplement has not been supported by sponsorship. Information about the source of funding for publication charges can be found in the individual articles. The articles have undergone the journal's standard peer review process for supplements. The Supplement Editors declare that they have no competing interests.</issue-sponsor><elocation-id>243</elocation-id><history><date date-type="received"><day>5</day><month>3</month><year>2020</year></date><date date-type="accepted"><day>9</day><month>3</month><year>2020</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2020</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article's Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article's Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated in a credit line to the data.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">The common marmoset (<italic>Callithrix jacchus</italic>) is one of the most studied primate model organisms. However, the marmoset genomes available in the public databases are highly fragmented and filled with sequence gaps, hindering research advances related to marmoset genomics and transcriptomics.</p></sec><sec><title>Results</title><p id="Par2">Here we utilize single-molecule, long-read sequence data to improve and update the existing genome assembly and report a near-complete genome of the common marmoset. The assembly is of 2.79 Gb size, with a contig N50 length of 6.37&#x02009;Mb and a chromosomal scaffold N50 length of 143.91&#x02009;Mb, representing the most contiguous and high-quality marmoset genome up to date. Approximately 90% of the assembled genome was represented in contigs longer than 1&#x02009;Mb, with approximately 104-fold improvement in contiguity over the previously published marmoset genome. More than 98% of the gaps from the previously published genomes were filled successfully, which improved the mapping rates of genomic and transcriptomic data on to the assembled genome.</p></sec><sec><title>Conclusions</title><p id="Par3">Altogether the updated, high-quality common marmoset genome assembly provide improvements at various levels over the previous versions of the marmoset genome assemblies. This will allow researchers working on primate genomics to apply the genome more efficiently for their genomic and transcriptomic sequence data.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Common marmoset</kwd><kwd><italic>Callithrix jacchus</italic></kwd><kwd>De novo assembly</kwd><kwd>Non-human primate genomics</kwd><kwd>Chromosome-scale scaffolds</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001691</institution-id><institution>Japan Society for the Promotion of Science</institution></institution-wrap></funding-source><award-id>Kakenhi 16H06279 and 18H04127</award-id><principal-award-recipient><name><surname>Sakakibara</surname><given-names>Yasubumi</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001700</institution-id><institution>Ministry of Education, Culture, Sports, Science and Technology</institution></institution-wrap></funding-source><award-id>Innovative areas 221S0002</award-id><principal-award-recipient><name><surname>Sakakibara</surname><given-names>Yasubumi</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100009619</institution-id><institution>Japan Agency for Medical Research and Development</institution></institution-wrap></funding-source><award-id>JP19kk0305008</award-id><principal-award-recipient><name><surname>Sakakibara</surname><given-names>Yasubumi</given-names></name></principal-award-recipient></award-group></funding-group><conference xlink:href="https://incob2019.org/"><conf-name>International Conference on Bioinformatics (InCoB 2019)</conf-name><conf-acronym>InCoB 2019</conf-acronym><conf-loc>Jakarta, Indonesia</conf-loc><conf-date>10-12 September 2019</conf-date></conference><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par18">The common marmoset (<italic>Callithrix jacchus</italic>) is a small, new-world monkey, which can be handled in laboratories with relative ease [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR3">3</xref>]. Mouse is a widely used model animal, however the genetic, physiological, and anatomical differences between mice and primates prevent their application for human studies, thus insisting on the necessity of non-human primate (NHP) models [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. Common marmosets have an effective breeding capacity relatively among primates, and they show some characteristics which are more related to humans than the other NHPs [<xref ref-type="bibr" rid="CR2">2</xref>]. Common marmosets have been utilized as models for numerous neurological diseases [<xref ref-type="bibr" rid="CR2">2</xref>], and a multiscale brain atlas project called Brain/MINDS had been initiated with a 10-year roadmap [<xref ref-type="bibr" rid="CR4">4</xref>]. In addition, marmosets were the first transgenic NHPs to be generated with germline transmission [<xref ref-type="bibr" rid="CR1">1</xref>], while also their embryonic stem cell lines and induced pluripotent stem cell lines are being widely researched [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>]. In light of the increasing importance of marmosets as an alternative NHP model animal for biomedical and neuroscience research, the genome was first sequenced by the marmoset genome sequencing and analysis consortium [<xref ref-type="bibr" rid="CR7">7</xref>]. The 2.26 Gb assembled genome from a female marmoset, although was sorted out into chromosomes, contained many shorter contigs and also 187,214 gap regions. These hard to assemble gap regions cannot be ignored, as they can lead to false positive results [<xref ref-type="bibr" rid="CR8">8</xref>], and the gap regions could harbor many functionally relevant genes [<xref ref-type="bibr" rid="CR9">9</xref>]. Recent studies have uncovered that many genes were wrongly labelled as missing in bird genomes, because of the locality of those genes being GC-rich and hence had posed challenges in identifying them [<xref ref-type="bibr" rid="CR9">9</xref>]. To improve such poorly assembled regions of the marmoset genome, second-generation sequencing technology (Illumina) based short reads were employed, which helped fill approximately one-third (65,384) of the gap regions [<xref ref-type="bibr" rid="CR10">10</xref>]. However, the genome still remains largely fragmented (contig N50&#x02009;=&#x02009;61 kbp) aside from the numerous undetermined bps. Sequence gaps and fragmented contigs are characteristic features in genome assemblies, however, with the rise of third-generation sequencing technologies such as Pacific Biosciences (PacBio) and Oxford Nanopore sequencing technologies, a large number of genomes are being updated to high contiguity genomes with the help of longer reads [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR12">12</xref>]. As an example, the genomes of apes assembled using first and second-generation sequencing data were abounded with tens to hundreds of thousands of gaps impacting multi-genome sequence alignments which limited sequence based discoveries [<xref ref-type="bibr" rid="CR13">13</xref>]. Recently, the ape genomes of Gorilla, Orangutan, and, Chimpanzee, were sequenced and reassembled using PacBio reads resulting in large scale improvements in the respective genomes [<xref ref-type="bibr" rid="CR13">13</xref>]. Similarly, for the common marmoset, after first- and second-generation sequencing technologies left the genome fragmented with many gaps, we have employed PacBio sequencing, a third-generation, single-molecule sequencing technology, and here, we report the updated version of the common marmoset genome with fewer gaps and high contiguity.</p></sec><sec id="Sec2"><title>Results and discussions</title><sec id="Sec3"><title>De novo assembly and pseudo-chromosome construction</title><p id="Par19">The long-read sequence data obtained from the PacBio RSII sequencer amounted to 114.80 Gb, covering approximately 43&#x02009;&#x000d7;&#x02009;of the genome (Fig. <xref rid="MOESM2" ref-type="media">S1</xref>). In the context of the size of the genome, the lengths of the PacBio reads (N50: 16.41 kbp; average sequence length: 11 kbp) were only slightly shorter than the contiguous gap-free regions from the previous marmoset genome assembly (N50: 61 kbp; average sequence length: 24 kbp). After assembling the sequence data with several assembly tools as part of the assembly workflow (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>), SMARTdenovo and wtdbg assemblies produced better results compared to the other assemblers in terms of contiguity, with N50 values reaching more than 6&#x02009;Mb (Table <xref rid="MOESM4" ref-type="media">S1</xref>). SMARTdenovo produced a slightly shorter N50 in comparison to wtdbg, but was chosen as the final assembly considering that SMARTdenovo had relatively longer average contig lengths and the least number of contigs. After constructing pseudo-chromosomes for all the assemblies (Table <xref rid="MOESM5" ref-type="media">S2</xref>), the alternate assemblies were used to fill the gaps in the SMARTdenovo pseudo-chromosomes, resulting in 1771 sequence gaps. The final assembly size was 2.79 Gb, with a scaffold N50 value of 143.89&#x02009;Mb corresponding to the chromosome lengths, and a contig N50 value of 6.38&#x02009;Mb (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). The obtained contig N50 value compared favorably against the recently updated genome assemblies using PacBio reads, whose average contig N50 was 6.34&#x02009;Mb (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). The N50 values were better for the other genome assemblies, only when multiple additional technologies such as Hi-C, CHICAGO, and BioNano optical maps were employed. When considering assemblies which used only third-generation sequence data for updating the genome, the marmoset genome assembly&#x02019;s contig N50 ranked the best among them, in spite of the relatively longer genome size (Table <xref rid="Tab2" ref-type="table">2</xref>). The assembly was estimated to comprise 39.37% repeat content. As expected in the primate genomes, the LINE1 elements contributed to most (21.44%) of the repeats. SINES (9.18%) and LTR elements (5.24%) were also distributed throughout the genome (Table <xref rid="MOESM6" ref-type="media">S3</xref>). Only a very small percentage (0.07%) of the repeats was left unclassified. A total of 18,385 gene models, along with 78,992 alternatively spliced transcripts, were obtained using the combined approach.
<fig id="Fig1"><label>Fig. 1</label><caption><p>The de novo assembly workflow</p></caption><graphic xlink:href="12864_2020_6657_Fig1_HTML" id="MO1"/></fig>
<table-wrap id="Tab1"><label>Table 1</label><caption><p>The common marmoset genome assembly statistics</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Contigs</th><th>Scaffolds</th></tr></thead><tbody><tr><td># sequences</td><td>1788</td><td>65</td></tr><tr><td>Total assembled sequence</td><td>2.79 Gb</td><td>2.79 Gb</td></tr><tr><td>Longest sequence length</td><td>46.03&#x02009;Mb</td><td>213.27&#x02009;Mb</td></tr><tr><td>N50</td><td>6.38&#x02009;Mb</td><td>143.89&#x02009;Mb</td></tr><tr><td>N75</td><td>2.58&#x02009;Mb</td><td>115.91&#x02009;Mb</td></tr><tr><td>L50</td><td>117</td><td>8</td></tr><tr><td>L75</td><td>289</td><td>14</td></tr></tbody></table></table-wrap>
<table-wrap id="Tab2"><label>Table 2</label><caption><p>A survey of contig N50s obtained in recent studies, involving improvement of genomes, primarily using long read sequence technologies</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Species</th><th>Common name</th><th>Assembled genome size</th><th>Contig N50</th><th>Additional technologies*</th></tr></thead><tbody><tr><td><italic>Aedes aegypti</italic></td><td>Mosquito</td><td>1.28 Gb</td><td>11.76&#x02009;Mb [<xref ref-type="bibr" rid="CR11">11</xref>]</td><td>Yes</td></tr><tr><td><italic>Brassica rapa</italic></td><td>Brassica</td><td>0.35 Gb</td><td>1.44&#x02009;Mb [<xref ref-type="bibr" rid="CR14">14</xref>]</td><td>Yes</td></tr><tr><td><italic>Bubalus bubalis</italic></td><td>Water buffalo</td><td>2.6 Gb</td><td>22.4&#x02009;Mb [<xref ref-type="bibr" rid="CR15">15</xref>]</td><td>Yes</td></tr><tr><td><italic>Calypte anna</italic></td><td>Anna&#x02019;s hummingbird</td><td>1.01 Gb</td><td>5.36&#x02009;Mb [<xref ref-type="bibr" rid="CR16">16</xref>]</td><td>No</td></tr><tr><td><italic>Camponotus floridanus</italic></td><td>Ant</td><td>0.28 Gb</td><td>1.22&#x02009;Mb [<xref ref-type="bibr" rid="CR17">17</xref>]</td><td>Yes</td></tr><tr><td><italic>Capra hircus</italic></td><td>Domestic goat</td><td>2.92 Gb</td><td>18.7&#x02009;Mb [<xref ref-type="bibr" rid="CR18">18</xref>]</td><td>Yes</td></tr><tr><td><italic>Columba livia</italic></td><td>Rock pigeon</td><td>1.10 Gb</td><td>0.02&#x02009;Mb [<xref ref-type="bibr" rid="CR19">19</xref>]</td><td>Yes</td></tr><tr><td><italic>Fragaria vesca</italic></td><td>Woodland strawberry</td><td>0.22 Gb</td><td>7.9&#x02009;Mb [<xref ref-type="bibr" rid="CR20">20</xref>]</td><td>Yes</td></tr><tr><td><italic>Gallus gallus</italic></td><td>Chicken</td><td>1.21 Gb</td><td>2.9&#x02009;Mb [<xref ref-type="bibr" rid="CR21">21</xref>]</td><td>Yes</td></tr><tr><td><italic>Gorilla gorilla</italic></td><td>Gorilla</td><td>3.08 Gb</td><td>10.02&#x02009;Mb [<xref ref-type="bibr" rid="CR22">22</xref>]</td><td>Yes</td></tr><tr><td><italic>Harpegnathos saltator</italic></td><td>Ant</td><td>0.34 Gb</td><td>0.88&#x02009;Mb [<xref ref-type="bibr" rid="CR17">17</xref>]</td><td>Yes</td></tr><tr><td><italic>Hordeum vulgare</italic> L. var. nudum</td><td>Tibetan hulless barley</td><td>4.00 Gb</td><td>1.56&#x02009;Mb [<xref ref-type="bibr" rid="CR23">23</xref>]</td><td>Yes</td></tr><tr><td><italic>Pan troglodytes</italic></td><td>Chimpanzee</td><td>2.99 Gb</td><td>12.42&#x02009;Mb [<xref ref-type="bibr" rid="CR13">13</xref>]</td><td>Yes</td></tr><tr><td><italic>Pongo abelii</italic></td><td>Orangutan</td><td>3.04 Gb</td><td>11.07&#x02009;Mb [<xref ref-type="bibr" rid="CR13">13</xref>]</td><td>Yes</td></tr><tr><td><italic>Rubus occidentalis</italic></td><td>Black raspberry</td><td>0.29 Gb</td><td>5.1&#x02009;Mb [<xref ref-type="bibr" rid="CR24">24</xref>]</td><td>Yes</td></tr><tr><td><italic>Siraitia grosvenorii</italic></td><td>Monk fruit</td><td>0.46 Gb</td><td>0.43&#x02009;Mb [<xref ref-type="bibr" rid="CR25">25</xref>]</td><td>No</td></tr><tr><td><italic>Symphodus melops</italic></td><td>Corkwing wrasse</td><td>0.61 Gb</td><td>0.46&#x02009;Mb [<xref ref-type="bibr" rid="CR26">26</xref>]</td><td>No</td></tr><tr><td><italic>Taeniopygia guttata</italic></td><td>Zebra finch</td><td>1.14 Gb</td><td>5.80&#x02009;Mb [<xref ref-type="bibr" rid="CR16">16</xref>]</td><td>No</td></tr><tr><td><italic>Zea mays</italic></td><td>Maize</td><td>2.10 Gb</td><td>1.18&#x02009;Mb [<xref ref-type="bibr" rid="CR12">12</xref>]</td><td>Yes</td></tr></tbody></table><table-wrap-foot><p>*Additional technologies include Hi-C, CHICAGO, BioNano optical maps, and others</p></table-wrap-foot></table-wrap></p></sec><sec id="Sec4"><title>Evaluation of the assembly</title><p id="Par20">As of March 31, 2019, the NCBI database for assembly of the common marmoset genome contained four assemblies: i) the first genome submitted at 2010 [<xref ref-type="bibr" rid="CR7">7</xref>], ii) the improved genome using Illumina submitted at 2015 [<xref ref-type="bibr" rid="CR10">10</xref>], iii) an Ion-torrent based genome submitted at 2015, and iv) an Illumina based genome anchored by Hi-C and CHICAGO libraries submitted at 2017 (Table <xref rid="MOESM7" ref-type="media">S4</xref>). The ion-torrent based assembly contained more than one Gb of the genome missing and hence was ignored for the evaluation purpose, while the rest of the assemblies were designated names in this manuscript, according to their submission years as CJ2010, CJ2015, and CJ2017, respectively. The genome assembly presented in this study was designated as CJ2019. When BUSCO [<xref ref-type="bibr" rid="CR27">27</xref>] was executed for the assemblies, the BUSCO scores yielded 92.9% completeness for both CJ2017 and CJ2019 assemblies, and 92.1 and 91.9% completeness for CJ2010 and CJ2015 assemblies respectively. This indicated that all the assemblies had high level of completeness at the level of conserved genes. The major differences between the current assembly and the previous assemblies were observed in contiguity, number of sequence gaps, and mapping rates of sequence data.</p></sec><sec id="Sec5"><title>Improved contiguity</title><p id="Par21">Using the contig N50 value as a metric, CJ2019 produced 217.76, 104.42, and 41.06 fold contiguity improvements over CJ2010, CJ2015, and CJ2017 assemblies, respectively (Fig. <xref rid="MOESM3" ref-type="media">S2</xref>). The CJ2019 assembly also showed a range of 36.88 to 177.22 fold improvements in contig N75 values over the previous marmoset assemblies (Fig. <xref rid="MOESM3" ref-type="media">S2</xref>). To further insist on the quality of the CJ2019 assembly at the contiguity level, 2.50 Gb of the 2.79 Gb genome was represented in contigs which were longer than 1&#x02009;Mb. Also, 54 contigs were of length more than 10&#x02009;Mb, while it should be noted that there was not even a single contig which managed to reach a length of 1&#x02009;Mb in any of the previous marmoset assemblies. When the N(x) values are plotted, although the scaffold N(x) values were similar across the assemblies (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a), the other assemblies fell below in comparison to the CJ2019 contig graph (Fig. <xref rid="Fig2" ref-type="fig">2</xref>b). All the assemblies produced scaffolds in the range of chromosomal lengths, with the N50 values reaching 132.17&#x02009;Mb, 140.45&#x02009;Mb, 129.2&#x02009;Mb, and 143.89&#x02009;Mb for CJ2010, CJ2015, CJ2017, and CJ2019 assemblies respectively.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Contiguity of all the marmoset genome assemblies (CJ2010, CJ2015, CJ2017, and CJ2019). <bold>a</bold>) N(X) plot of scaffolds, <bold>b</bold>) N(X) plot of contigs</p></caption><graphic xlink:href="12864_2020_6657_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec6"><title>Reduced sequence gaps</title><p id="Par22">The other major improvement in the CJ2019 assembly is a significant reduction in the number of sequence gaps (Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>). While the CJ2015 genome closed approximately 41% of the gaps in the CJ2010 genome, the current CJ2019 genome resulted in 1771 gaps, closing more than 98% of the sequence gaps from the previous versions of the marmoset genome. This resulted in more than 99% of the genome being sequenced, leaving only complex regions such as centromeric repeats, and large segmental duplications to be filled. The closing of the gaps greatly facilitated the mapping of all kinds of data onto the CJ2019 assembly, increasing the space for repeat and gene identifications.
<table-wrap id="Tab3"><label>Table 3</label><caption><p>Statistics of the gaps in the published marmoset genome assemblies</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th colspan="3"># Gaps</th><th colspan="2">Improvements in CJ2019</th></tr><tr><th>Chromosome</th><th>CJ2010</th><th>CJ2015</th><th>CJ2019</th><th>Vs CJ2010</th><th>Vs CJ2015</th></tr></thead><tbody><tr><td>1</td><td>13,245</td><td>7859</td><td>177</td><td>98.66</td><td>97.75</td></tr><tr><td>2</td><td>11,874</td><td>6677</td><td>84</td><td>99.29</td><td>98.74</td></tr><tr><td>3</td><td>9897</td><td>5404</td><td>48</td><td>99.52</td><td>99.11</td></tr><tr><td>4</td><td>9532</td><td>5271</td><td>83</td><td>99.13</td><td>98.43</td></tr><tr><td>5</td><td>11,928</td><td>7264</td><td>166</td><td>98.61</td><td>97.71</td></tr><tr><td>6</td><td>8890</td><td>4993</td><td>116</td><td>98.70</td><td>97.68</td></tr><tr><td>7</td><td>10,453</td><td>6137</td><td>64</td><td>99.39</td><td>98.96</td></tr><tr><td>8</td><td>7048</td><td>3956</td><td>26</td><td>99.63</td><td>99.34</td></tr><tr><td>9</td><td>8252</td><td>4781</td><td>66</td><td>99.20</td><td>98.62</td></tr><tr><td>10</td><td>8328</td><td>4673</td><td>57</td><td>99.32</td><td>98.78</td></tr><tr><td>11</td><td>8419</td><td>5131</td><td>113</td><td>98.66</td><td>97.80</td></tr><tr><td>12</td><td>8444</td><td>5023</td><td>72</td><td>99.15</td><td>98.57</td></tr><tr><td>13</td><td>6469</td><td>3549</td><td>45</td><td>99.30</td><td>98.73</td></tr><tr><td>14</td><td>6444</td><td>3702</td><td>75</td><td>98.84</td><td>97.97</td></tr><tr><td>15</td><td>5580</td><td>3193</td><td>36</td><td>99.35</td><td>98.87</td></tr><tr><td>16</td><td>5409</td><td>3114</td><td>24</td><td>99.56</td><td>99.23</td></tr><tr><td>17</td><td>3879</td><td>2086</td><td>13</td><td>99.66</td><td>99.38</td></tr><tr><td>18</td><td>3031</td><td>1762</td><td>39</td><td>98.71</td><td>97.79</td></tr><tr><td>19</td><td>3128</td><td>1861</td><td>31</td><td>99.01</td><td>98.33</td></tr><tr><td>20</td><td>3159</td><td>1899</td><td>32</td><td>98.99</td><td>98.31</td></tr><tr><td>21</td><td>2872</td><td>1713</td><td>25</td><td>99.13</td><td>98.54</td></tr><tr><td>22</td><td>6638</td><td>4783</td><td>67</td><td>98.99</td><td>98.60</td></tr><tr><td>X</td><td>10,542</td><td>7314</td><td>300</td><td>97.15</td><td>95.90</td></tr><tr><td>Y</td><td>269</td><td>184</td><td>12</td><td>95.54</td><td>93.48</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec7"><title>Improved mapping rates</title><p id="Par23">When marmoset RNAseq reads from 12 different brain tissues were aligned against the assemblies, the average mapping rate was below 80% for all the previous assemblies. In contrast, the CJ2019 genome assembly displayed more than 80% alignment in all but one of the samples (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a). On average, the mapping rates were 8.26, 9.93, and 5.13% higher than the CJ2010, CJ2015, and CJ2017 assemblies respectively. Also, Human Gencode (Release 29) transcripts, mapped more to the CJ2019 genome than the previous versions. When BAC-end data were mapped, against the current chromosomal genomes (CJ2010 and CJ2015) at NCBI, the difference in mapping rates was more than 12% (Fig. <xref rid="Fig3" ref-type="fig">3</xref>b). Compared to the CJ2017 assembly, the concordant mapping rate of BAC-end data was increased by 6.30%. The increase in mapping rates is further proof that the genome has been improved significantly.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Improved mapping rates of all the marmoset genome assemblies (CJ2010, CJ2015, CJ2017, and CJ2019). <bold>a</bold>) Box plot of the mapping rates of RNAseq data from different brain samples, <bold>b</bold>) Bar plot of the mapping rates of the common marmoset&#x02019;s BAC-end data</p></caption><graphic xlink:href="12864_2020_6657_Fig3_HTML" id="MO3"/></fig></p></sec><sec id="Sec8"><title>Possible structural errors</title><p id="Par24">CJ2017 and CJ2019 genome assemblies were aligned against each other with minimap2 [<xref ref-type="bibr" rid="CR28">28</xref>] and visualized using dot plots generated by d-genies [<xref ref-type="bibr" rid="CR29">29</xref>]. Numerous small and large inversions were observed between the two assemblies. Hi-C scaffolding can erroneously introduce inversions in short contigs [<xref ref-type="bibr" rid="CR30">30</xref>], and this can be attributed to the small inversions observed in the dot plots (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a). However, larger inversions, such as that observed in chromosome 16 (Fig. <xref rid="Fig4" ref-type="fig">4</xref>b), could be actual misassembled structural errors. The mapping of long-range paired BAC-end reads did not support the chromosome 16 inversion of CJ2019 genome, indirectly hinting that the make-up at this particular location is more accurate in CJ2017 genome. It has to be noted that the original marmoset genome assembly was constructed with human genome as a guiding factor, and hence some parts are effectively humanized and could be actually structural errors. A new common marmoset genome, as a part of the Vertebrate Genome Project, is under development, which includes 55.69 x coverage of 10x genomics data, 105.81 x coverage of Arima Hi-C data, 154.52 x coverage of BioNano optical map data, along with PacBio and Illumina sequence reads [<xref ref-type="bibr" rid="CR31">31</xref>]. Although vast improvements in terms of contiguity, sequence gaps, and mapping rates of various genomic elements could be observed in the CJ2019 assembly, structural errors could potentially remain which would need the combination of the above data to effectively resolve them.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Dot plot of the alignment between CJ2017 and CJ2019. <bold>a</bold>) Whole genome view, <bold>b</bold>) Focused view of chromosome 16. The X-axis represents CJ2017 scaffolds and Y-axis represent CJ2019 scaffolds. A diagonal straight line indicates synteny among the genomes</p></caption><graphic xlink:href="12864_2020_6657_Fig4_HTML" id="MO4"/></fig></p></sec></sec><sec id="Sec9"><title>Conclusions</title><p id="Par25">The high-quality genome constructed as part of this manuscript has shown vast improvements in terms of contiguity, gaps, genomic features, and mapping rates of sequence data and will be widely useful for researchers involved in the field of primate genomics.</p></sec><sec id="Sec10"><title>Methods</title><sec id="Sec11"><title>Sample preparation and sequencing</title><p id="Par26">The liver sample (Animal I2075) from the Central Institute for Experimental Animals (CIEA), Japan, was originally used to improve the Marmoset genome using Illumina [<xref ref-type="bibr" rid="CR10">10</xref>], with ample part of the animal&#x02019;s samples retained for future use. Briefly, an 8-year, 4-month-old male marmoset liver was used for DNA extraction using the phenol-chloroform-isoamyl alcohol extraction method. The genomic DNA for long-read sequencing in this study was extracted from the kidney of the same animal described above. The genomic DNA was extracted using QIAGEN Genomic-tip 500/G (Qiagen, Hilden, Germany) according to the manufacturer&#x02019;s instruction. To avoid blood contamination, bloodletting was performed completely at the dissection. The genomic sample was sequenced using PacBio RSII sequencer on 93 SMRT cells. In addition, RNA sequencing was also employed to improve the prediction of gene models. Two male marmosets (Animals: I5998 and I6289) that were 2&#x02009;years old and obtained from CLEA Japan Inc. were deeply anaesthetized with isoflurane and euthanized by exsanguination from the femoral artery. Twelve tissue samples from the brain (Table <xref rid="MOESM8" ref-type="media">S5</xref>) were collected and immediately frozen using liquid nitrogen and were rapidly broken down in solution D using a polytron homogenizer, before extracting the total RNA by the acid guanidinium thiocyanate-phenol-chloroform method. The quality and the concentration of the sample were measured using the 2100 Bioanalyzer instrument with the Agilent RNA 6000 Nano kit. The cDNA sequencing libraries were constructed with 1000 nanograms of total RNA using the TruSeq RNA Sample Prep Kit (Illumina), following the instructions in the TruSeq RNA Sample Preparation V2 Guide (Illumina). MiSeq Reagent Kit v3 (600-cycles) was used for sequencing the RNA samples (2 * 150&#x02009;bp PE reads) with the MiSeq sequencer.</p></sec><sec id="Sec12"><title>De novo assembly and pseudo-chromosome construction</title><p id="Par27">The sequenced reads were input to several assembly tools as recommended in the benchmark article [<xref ref-type="bibr" rid="CR32">32</xref>], including Canu [<xref ref-type="bibr" rid="CR33">33</xref>], SMARTdenovo [<xref ref-type="bibr" rid="CR34">34</xref>], wtdbg [<xref ref-type="bibr" rid="CR35">35</xref>], miniasm [<xref ref-type="bibr" rid="CR36">36</xref>], Flye [<xref ref-type="bibr" rid="CR37">37</xref>], Falcon [<xref ref-type="bibr" rid="CR38">38</xref>], and MECAT [<xref ref-type="bibr" rid="CR39">39</xref>], as part of the assembly workflow (Fig. <xref rid="Fig1" ref-type="fig">1</xref>). MECAT aborted with a segmentation fault, while Falcon produced relatively shorter contigs, and both were subsequently left out from the further analysis. Quiver [<xref ref-type="bibr" rid="CR40">40</xref>] was executed in two iterative rounds to polish all the assemblies. In the case of wtdbg and miniasm assemblies, an additional round of quiver was performed owing to their relatively high error rates in the consensus sequences. RaGOO [<xref ref-type="bibr" rid="CR41">41</xref>] was used to construct pseudo-chromosomes from the assembled contigs by using the previous marmoset genome [<xref ref-type="bibr" rid="CR10">10</xref>], as a reference. To fill the gaps, a hybrid assembly was constructed by mapping, using minimap2, the flanking regions of sequence gaps in the SMARTdenovo assembly against the contiguous regions of the other assemblies, and replacing the gaps with nucleotide bps. Two rounds of consensus polishing by Pilon [<xref ref-type="bibr" rid="CR42">42</xref>], using Illumina data from the same sample [<xref ref-type="bibr" rid="CR10">10</xref>], was also executed to polish the assemblies further.</p></sec><sec id="Sec13"><title>Genome annotation</title><p id="Par28">Repeat content was assessed using RepeatModeler and RepeatMasker [<xref ref-type="bibr" rid="CR43">43</xref>]. The repeats, in the contiguous sequences of the assembly, were first identified by RepeatModeler to construct repeat family libraries, which were in turn used by RepeatMasker to annotate and mask the repetitive regions of the assembled pseudo-chromosomes. For gene annotation, a random set of 1000 multi-exon genes were obtained from Ensembl (Release version 95) database&#x02019;s common marmoset gene annotation to train gene models using Augustus [<xref ref-type="bibr" rid="CR44">44</xref>]. In addition, a combination of ab-initio based, homology based, and transcriptome-based strategies were applied to predict and update the predicted genes. In the homology based approach, protein sequences were collected from a) the recently assembled Chimpanzee and Orangutan genomes [<xref ref-type="bibr" rid="CR13">13</xref>], from NCBI, b) NCBI&#x02019;s NR protein database for the Gorilla genome, and c) the Gencode (Release 29) database for the Human genome, and were aligned against the assembled genome using funannotate [<xref ref-type="bibr" rid="CR45">45</xref>], which in-turn uses diamond [<xref ref-type="bibr" rid="CR46">46</xref>], and exonerate [<xref ref-type="bibr" rid="CR47">47</xref>]. Parallelly, Marmoset ESTs downloaded from NCBI were input to PASA [<xref ref-type="bibr" rid="CR48">48</xref>], to model gene structures from the EST alignments. These EST and protein alignments were provided as hints for the Augustus ab-initio gene prediction program with the trained gene model as the species parameter on the assembled genome, which was earlier soft-masked for repeats using RepeatMasker. Later, RNAseq reads from the 12 different brain tissues (Table <xref rid="MOESM8" ref-type="media">S5</xref>), as well as marmoset samples from the study, SRP051959 [<xref ref-type="bibr" rid="CR49">49</xref>], were de novo assembled into transcripts using Trinity [<xref ref-type="bibr" rid="CR50">50</xref>]. PASA was used once again, in conjunction with the Trinity transcripts, to update the UTR and alternate splicing information of the predicted genes.</p></sec><sec id="Sec14"><title>Evaluation of the assembly</title><p id="Par29">BUSCO was used to evaluate the completeness of the evolutionarily conserved genes in the assemblies. To further evaluate the quality of the de novo assembly, different sets of DNA and RNA data were aligned against the assembled genomes. RNAseq reads, Human Gencode transcripts, and BAC-end data, were aligned against the genome assemblies using STAR [<xref ref-type="bibr" rid="CR51">51</xref>], GMAP [<xref ref-type="bibr" rid="CR52">52</xref>], and bowtie2 [<xref ref-type="bibr" rid="CR53">53</xref>] aligners respectively. Minimap2 [<xref ref-type="bibr" rid="CR28">28</xref>] was used to align the whole genome assemblies against each other, and d-geneis [<xref ref-type="bibr" rid="CR29">29</xref>] was used to obtain and visualize dot plots from the alignments.</p></sec></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec15"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12864_2020_6657_MOESM1_ESM.pdf"><caption><p><bold>Additional file 1.</bold> Parameters used for the tools.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12864_2020_6657_MOESM2_ESM.png"><caption><p><bold>Additional file 2: Figure S1.</bold> Histogram of the sequenced read lengths.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12864_2020_6657_MOESM3_ESM.png"><caption><p><bold>Additional file 3: Figure S2.</bold> Comparison of the N50 and N75 values across all the assembled genomes.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12864_2020_6657_MOESM4_ESM.xlsx"><caption><p><bold>Additional file 4: Table S1.</bold> Assembly statistics of the marmoset genome from different assemblers.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12864_2020_6657_MOESM5_ESM.xlsx"><caption><p><bold>Additional file 5: Table S2.</bold> Pseudo-chromosome statistics of the marmoset genome assemblies from different assemblers.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="12864_2020_6657_MOESM6_ESM.xlsx"><caption><p><bold>Additional file 6: Table S3.</bold> Repeat statistics of the final marmoset genome assembly.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="12864_2020_6657_MOESM7_ESM.xlsx"><caption><p><bold>Additional file 7: Table S4.</bold> Summary of the marmoset genome assemblies in the NCBI Assembly database.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="12864_2020_6657_MOESM8_ESM.xlsx"><caption><p><bold>Additional file 8: Table S5.</bold> RNAseq of brain tissue samples.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>SMRT</term><def><p id="Par4">Single Molecule Real Time</p></def></def-item><def-item><term>RNA</term><def><p id="Par5">RiboNucleic Acid</p></def></def-item><def-item><term>LINE</term><def><p id="Par6">Long Interspersed Nuclear Elements</p></def></def-item><def-item><term>SINE</term><def><p id="Par7">Short Interspersed Nuclear Elements</p></def></def-item><def-item><term>LTR</term><def><p id="Par8">Long Terminal Repeats</p></def></def-item><def-item><term>NCBI</term><def><p id="Par9">National Center for Biotechnology Information</p></def></def-item><def-item><term>NR</term><def><p id="Par10">Non Redundant database</p></def></def-item><def-item><term>CDS</term><def><p id="Par11">Coding sequence</p></def></def-item><def-item><term>EST</term><def><p id="Par12">Expressed Sequence Tags</p></def></def-item><def-item><term>bp</term><def><p id="Par13">basepair</p></def></def-item><def-item><term>kbp</term><def><p id="Par14">kilo basepair</p></def></def-item><def-item><term>Mb</term><def><p id="Par15">mega basepair</p></def></def-item><def-item><term>Gb</term><def><p id="Par16">Giga basepair</p></def></def-item><def-item><term>BAC-end</term><def><p id="Par17">Bacterial Artificial Clone end</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s12864-020-6657-2.</p></sec><ack><title>Acknowledgements</title><p>Not applicable.</p><sec id="FPar1"><title>About this supplement</title><p id="Par30">This article has been published as part of BMC Genomics, Volume 21 Supplement 3, 2020: 18th International Conference on Bioinformatics. The full contents of the supplement are available at <ext-link ext-link-type="uri" xlink:href="https://bmcgenomics.biomedcentral.com/articles/supplements/volume-21-supplement-3">https://bmcgenomics.biomedcentral.com/articles/supplements/volume-21-supplement-3</ext-link>.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>Y.S. and H.O. designed the resequencing project; V.J. executed genome assembly, annotation and comparative analysis; H. I, M.S. and S.H. performed RNA extraction and sequencing; W.K. performed DNA extraction; K.S. performed quality and adapter trimming of Illumina sequence data; E.S. and T.I. provided samples for genomic sequencing; Y.S. coordinated manuscript writing and high-throughput sequencing. All authors have read and approved the manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>Publication of this supplement is funded by grants from the Strategic Research Program for Brain Sciences; and by grants from the Ministry of Education, Culture, Sports, Science, and Technology (MEXT) of Japan to E.S., H.O., and Y.S.; Y.S. was also supported by JSPS KAKENHI Grant Numbers 16H06279 and 18H04127 of Japan. V.J. and Y.S. were also supported by Innovative Areas [no. 221S0002] from MEXT, Japan, and Program for Promoting Platform of Genomics based Drug Discovery from the Japan Agency AMED under Grant Number JP19kk0305008.</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>All data has been submitted to the DNA DataBank of Japan (DDBJ). The Bioproject, the Biosample, and the DDBJ read archive accession numbers for the genome data are PRJDB8242, SAMD00169834, and DRA008324 respectively. The genome assembly has the accession numbers: BJKT01000001&#x02013;BJKT01000065. The RNAseq data has accession numbers: DRA008173, DRA006301, and DRA008220.</p></notes><notes><title>Ethics approval and consent to participate</title><p id="Par31">The animal experiment protocol was approved by the CIEA Institutional Animal Care and Use Committee (approval nos. 16041 and 17031). Also, all the animal experiments were approved by the Institutional Animal Care and Use Committee (approval nos. 12025 and 13071). The study was conducted in accordance with the guidelines of CIEA that comply with the Guidelines for Proper Conduct of Animal Experiments published by the Science Council of Japan. Animal care was conducted in accordance with the Guide for the Care and Use of Laboratory Animals (Institute for Laboratory Animal Resources, 2011).</p></notes><notes><title>Consent for publication</title><p id="Par32">Not applicable.</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p id="Par33">H.O is a paid Scientific Advisory Board of SanBio Co. Ltd. And K Pharma Inc. The other authors declare no competing financial interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sasaki</surname><given-names>E</given-names></name><name><surname>Suemizu</surname><given-names>H</given-names></name><name><surname>Shimada</surname><given-names>A</given-names></name><name><surname>Hanazawa</surname><given-names>K</given-names></name><name><surname>Oiwa</surname><given-names>R</given-names></name><name><surname>Kamioka</surname><given-names>M</given-names></name><etal/></person-group><article-title>Generation of transgenic non-human primates with germline transmission</article-title><source>Nature.</source><year>2009</year><volume>459</volume><fpage>523</fpage><lpage>527</lpage><pub-id pub-id-type="pmid">19478777</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Okano</surname><given-names>H</given-names></name><name><surname>Hikishima</surname><given-names>K</given-names></name><name><surname>Iriki</surname><given-names>A</given-names></name><name><surname>Sasaki</surname><given-names>E</given-names></name></person-group><article-title>The common marmoset as a novel animal model system for biomedical and neuroscience research applications</article-title><source>Semin Fetal Neonatal Med</source><year>2012</year><volume>17</volume><fpage>336</fpage><lpage>340</lpage><pub-id pub-id-type="pmid">22871417</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kishi</surname><given-names>N</given-names></name><name><surname>Sato</surname><given-names>K</given-names></name><name><surname>Sasaki</surname><given-names>E</given-names></name><name><surname>Okano</surname><given-names>H</given-names></name></person-group><article-title>Common marmoset as a new model animal for neuroscience research and genome editing technology</article-title><source>Dev Growth Differ</source><year>2014</year><volume>56</volume><fpage>53</fpage><lpage>62</lpage><pub-id pub-id-type="pmid">24387631</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Okano</surname><given-names>H</given-names></name><name><surname>Sasaki</surname><given-names>E</given-names></name><name><surname>Yamamori</surname><given-names>T</given-names></name><name><surname>Iriki</surname><given-names>A</given-names></name><name><surname>Shimogori</surname><given-names>T</given-names></name><name><surname>Yamaguchi</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Brain/MINDS: a Japanese national brain project for marmoset neuroscience</article-title><source>Neuron.</source><year>2016</year><volume>92</volume><fpage>582</fpage><lpage>590</lpage><pub-id pub-id-type="pmid">27809998</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sasaki</surname><given-names>E</given-names></name><name><surname>Hanazawa</surname><given-names>K</given-names></name><name><surname>Kurita</surname><given-names>R</given-names></name><name><surname>Akatsuka</surname><given-names>A</given-names></name><name><surname>Yoshizaki</surname><given-names>T</given-names></name><name><surname>Ishii</surname><given-names>H</given-names></name><etal/></person-group><article-title>Establishment of novel embryonic stem cell lines derived from the common marmoset (<italic>Callithrix jacchus</italic>)</article-title><source>Stem Cells</source><year>2005</year><volume>23</volume><fpage>1304</fpage><lpage>1313</lpage><pub-id pub-id-type="pmid">16109758</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tomioka</surname><given-names>I</given-names></name><name><surname>Maeda</surname><given-names>T</given-names></name><name><surname>Shimada</surname><given-names>H</given-names></name><name><surname>Kawai</surname><given-names>K</given-names></name><name><surname>Okada</surname><given-names>Y</given-names></name><name><surname>Igarashi</surname><given-names>H</given-names></name><etal/></person-group><article-title>Generating induced pluripotent stem cells from common marmoset (<italic>Callithrix jacchus</italic>) fetal liver cells using defined factors, including Lin28</article-title><source>Genes Cells</source><year>2010</year><volume>15</volume><fpage>959</fpage><lpage>969</lpage><pub-id pub-id-type="pmid">20670273</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Worley</surname><given-names>KC</given-names></name><name><surname>Warren</surname><given-names>WC</given-names></name><name><surname>Rogers</surname><given-names>J</given-names></name><name><surname>Locke</surname><given-names>D</given-names></name><name><surname>Muzny</surname><given-names>DM</given-names></name><name><surname>Mardis</surname><given-names>ER</given-names></name><etal/></person-group><article-title>The common marmoset genome provides insight into primate biology and evolution</article-title><source>Nat Genet</source><year>2014</year><volume>46</volume><fpage>850</fpage><lpage>857</lpage><pub-id pub-id-type="pmid">25038751</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><mixed-citation publication-type="other">Domanska D, Kanduri C, Simovski B, Sandve GK. Mind the gaps: overlooking inaccessible regions confounds statistical testing in genome analysis. BMC Bioinformatics. 2018;19(481).</mixed-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Peona</surname><given-names>V</given-names></name><name><surname>Weissensteiner</surname><given-names>MH</given-names></name><name><surname>Suh</surname><given-names>A</given-names></name></person-group><article-title>How complete are &#x0201c;complete&#x0201d; genome assemblies?-an avian perspective</article-title><source>Mol Ecol Resour</source><year>2018</year><volume>18</volume><fpage>1188</fpage><lpage>1195</lpage><pub-id pub-id-type="pmid">30035372</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><mixed-citation publication-type="other">Sato K, Kuroki Y, Kumita W, Fujiyama A, Toyoda A, Kawai J, et al. Resequencing of the common marmoset genome improves genome assemblies and gene-coding sequence analysis. Sci Rep. 2015;16894.</mixed-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Matthews</surname><given-names>BJ</given-names></name><name><surname>Dudchenko</surname><given-names>O</given-names></name><name><surname>Kingan</surname><given-names>SB</given-names></name><name><surname>Koren</surname><given-names>S</given-names></name><name><surname>Antoshechkin</surname><given-names>I</given-names></name><name><surname>Crawford</surname><given-names>JE</given-names></name><etal/></person-group><article-title>Improved reference genome of <italic>Aedes aegypti</italic> informs arbovirus vector control</article-title><source>Nature.</source><year>2018</year><volume>563</volume><issue>7732</issue><fpage>501</fpage><lpage>507</lpage><pub-id pub-id-type="pmid">30429615</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jiao</surname><given-names>Y</given-names></name><name><surname>Peluso</surname><given-names>P</given-names></name><name><surname>Shi</surname><given-names>J</given-names></name><name><surname>Liang</surname><given-names>T</given-names></name><name><surname>Stitzer</surname><given-names>MC</given-names></name><name><surname>Wang</surname><given-names>B</given-names></name><etal/></person-group><article-title>Improved maize reference genome with single-molecule technologies</article-title><source>Nature.</source><year>2017</year><volume>546</volume><issue>7659</issue><fpage>524</fpage><lpage>527</lpage><pub-id pub-id-type="pmid">28605751</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kronenberg</surname><given-names>ZN</given-names></name><name><surname>Fiddes</surname><given-names>IT</given-names></name><name><surname>Gordon</surname><given-names>D</given-names></name><name><surname>Murali</surname><given-names>S</given-names></name><name><surname>Cantsilieris</surname><given-names>S</given-names></name><name><surname>Meyerson</surname><given-names>OS</given-names></name><etal/></person-group><article-title>High-resolution comparative analysis of great ape genomes</article-title><source>Science</source><year>2018</year><volume>360</volume><fpage>eaar6343</fpage><pub-id pub-id-type="pmid">29880660</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>L</given-names></name><name><surname>Cai</surname><given-names>X</given-names></name><name><surname>Wu</surname><given-names>J</given-names></name><name><surname>Liu</surname><given-names>M</given-names></name><name><surname>Grob</surname><given-names>S</given-names></name><name><surname>Cheng</surname><given-names>F</given-names></name><etal/></person-group><article-title>Improved <italic>Brassica rapa</italic> reference genome by single-molecule sequencing and chromosome conformation capture technologies</article-title><source>Hortic Res</source><year>2018</year><volume>5</volume><issue>1</issue><fpage>50</fpage><pub-id pub-id-type="pmid">30131865</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><mixed-citation publication-type="other">Low WY, Tearle R, Bickhart DM, Rosen BD, Kingan SB, Swale T, et al. Chromosome-level assembly of the water buffalo genome surpasses human and goat genomes in sequence contiguity. Nat Commun. 2019;10(260).</mixed-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Korlach</surname><given-names>J</given-names></name><name><surname>Gedman</surname><given-names>G</given-names></name><name><surname>Kingan</surname><given-names>SB</given-names></name><name><surname>Chin</surname><given-names>CS</given-names></name><name><surname>Howard</surname><given-names>JT</given-names></name><name><surname>Audet</surname><given-names>JN</given-names></name><etal/></person-group><article-title><italic>De novo</italic> PacBio long-read and phased avian genome assemblies correct and add to reference genes generated with intermediate and short reads</article-title><source>Gigascience.</source><year>2017</year><volume>6</volume><fpage>1</fpage><lpage>16</lpage></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shields</surname><given-names>EJ</given-names></name><name><surname>Sheng</surname><given-names>L</given-names></name><name><surname>Weiner</surname><given-names>AK</given-names></name><name><surname>Garcia</surname><given-names>BA</given-names></name><name><surname>Bonasio</surname><given-names>R</given-names></name></person-group><article-title>High-quality genome assemblies reveal long non-coding RNAs expressed in ant brains</article-title><source>Cell Rep</source><year>2018</year><volume>23</volume><fpage>3078</fpage><lpage>3090</lpage><pub-id pub-id-type="pmid">29874592</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bickhart</surname><given-names>DM</given-names></name><name><surname>Rosen</surname><given-names>BD</given-names></name><name><surname>Koren</surname><given-names>S</given-names></name><name><surname>Sayre</surname><given-names>BL</given-names></name><name><surname>Hastie</surname><given-names>AR</given-names></name><name><surname>Chan</surname><given-names>S</given-names></name><etal/></person-group><article-title>Single-molecule sequencing and chromatin conformation capture enable <italic>de novo</italic> reference assembly of the domestic goat genome</article-title><source>Nat Genet</source><year>2017</year><volume>49</volume><fpage>643</fpage><lpage>650</lpage><pub-id pub-id-type="pmid">28263316</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Holt</surname><given-names>C</given-names></name><name><surname>Campbell</surname><given-names>M</given-names></name><name><surname>Keays</surname><given-names>DA</given-names></name><name><surname>Edelman</surname><given-names>N</given-names></name><name><surname>Kapusta</surname><given-names>A</given-names></name><name><surname>Maclary</surname><given-names>E</given-names></name><etal/></person-group><article-title>Improved genome assembly and annotation for the rock pigeon (<italic>Columba livia</italic>)</article-title><source>G3:Genes|Genomes|Genetics</source><year>2018</year><volume>8</volume><fpage>1391</fpage><lpage>1398</lpage><pub-id pub-id-type="pmid">29519939</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edger</surname><given-names>PP</given-names></name><name><surname>VanBuren</surname><given-names>R</given-names></name><name><surname>Colle</surname><given-names>M</given-names></name><name><surname>Poorten</surname><given-names>TJ</given-names></name><name><surname>Wai</surname><given-names>CM</given-names></name><name><surname>Niederhuth</surname><given-names>CE</given-names></name><etal/></person-group><article-title>Single-molecule sequencing and optical mapping yields an improved genome of woodland strawberry (<italic>Fragaria vesca</italic>) with chromosome-scale contiguity</article-title><source>GigaScience.</source><year>2018</year><volume>7</volume><fpage>1</fpage><lpage>7</lpage></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Warren</surname><given-names>WC</given-names></name><name><surname>Hillier</surname><given-names>LW</given-names></name><name><surname>Tomlinson</surname><given-names>C</given-names></name><name><surname>Minx</surname><given-names>P</given-names></name><name><surname>Kremitzki</surname><given-names>M</given-names></name><name><surname>Graves</surname><given-names>T</given-names></name><etal/></person-group><article-title>A new chicken genome assembly provides insight into avian genome structure</article-title><source>G3: Genes|Genomes|Genetics</source><year>2017</year><volume>7</volume><fpage>109</fpage><lpage>117</lpage><pub-id pub-id-type="pmid">27852011</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><mixed-citation publication-type="other">Gordon D, Huddleston J, Chaisson MJP, Hill CM, Kronenberg ZN, Munson KM, et al. Long-read sequence assembly of the gorilla genome. Science. 2016;352(6281).</mixed-citation></ref><ref id="CR23"><label>23.</label><mixed-citation publication-type="other">Nyima T, Zeng X, Li X, Bai L, Wang Y, Xu T, et al. Improved high-quality genome assembly and annotation of Tibetan hulless barley. bioRxiv. 2018;409136.</mixed-citation></ref><ref id="CR24"><label>24.</label><mixed-citation publication-type="other">VanBuren R, Wai CM, Colle M, Wang J, Sullivan S, Bushakra JM, et al. A near complete, chromosome-scale assembly of the black raspberry (<italic>Rubus occidentalis</italic>) genome. Gigascience. 2018;7(8).</mixed-citation></ref><ref id="CR25"><label>25.</label><mixed-citation publication-type="other">Xia M, Han X, He H, Yu R, Zhen G, Jia X, et al. Improved <italic>de novo</italic> genome assembly and analysis of the Chinese cucurbit <italic>Siraitia grosvenorii,</italic> also known as monk fruit or luo-han-guo. Gigascience. 2018;7(6).</mixed-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mattingsdal</surname><given-names>M</given-names></name><name><surname>Jentoft</surname><given-names>S</given-names></name><name><surname>T&#x000f8;rresen</surname><given-names>OK</given-names></name><name><surname>Knutsen</surname><given-names>H</given-names></name><name><surname>Hansen</surname><given-names>MM</given-names></name><name><surname>Robalo</surname><given-names>JI</given-names></name><etal/></person-group><article-title>A continuous genome assembly of the corkwing wrasse (<italic>Symphodus melops</italic>)</article-title><source>Genomics.</source><year>2018</year><volume>110</volume><issue>6</issue><fpage>399</fpage><lpage>403</lpage><pub-id pub-id-type="pmid">29665418</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sim&#x000e3;o</surname><given-names>FA</given-names></name><name><surname>Waterhouse</surname><given-names>RM</given-names></name><name><surname>Ioannidis</surname><given-names>P</given-names></name><name><surname>Kriventseva</surname><given-names>EV</given-names></name><name><surname>Zdobnov</surname><given-names>EM</given-names></name></person-group><article-title>BUSCO: assessing genome assembly and annotation completeness with single-copy orthologs</article-title><source>Bioinformatics.</source><year>2015</year><volume>31</volume><issue>19</issue><fpage>3210</fpage><lpage>3212</lpage><pub-id pub-id-type="pmid">26059717</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name></person-group><article-title>Minimap2: pairwise alignment for nucleotide sequences</article-title><source>Bioinformatics.</source><year>2018</year><volume>34</volume><issue>18</issue><fpage>3094</fpage><lpage>3100</lpage><pub-id pub-id-type="pmid">29750242</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cabanettes</surname><given-names>F</given-names></name><name><surname>Klopp</surname><given-names>C</given-names></name></person-group><article-title>D-GENIES: dot plot large genomes in an interactive, efficient and simple way</article-title><source>PeerJ.</source><year>2018</year><volume>6</volume><fpage>e4958</fpage><pub-id pub-id-type="pmid">29888139</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ghurye</surname><given-names>J</given-names></name><name><surname>Rhie</surname><given-names>A</given-names></name><name><surname>Walenz</surname><given-names>BP</given-names></name><name><surname>Schmitt</surname><given-names>A</given-names></name><name><surname>Selvaraj</surname><given-names>S</given-names></name><name><surname>Pop</surname><given-names>M</given-names></name><etal/></person-group><article-title>Integrating hi-C links with assembly graphs for chromosome-scale assembly</article-title><source>PLoS Comput Biol</source><year>2019</year><volume>15</volume><issue>8</issue><fpage>e1007273</fpage><pub-id pub-id-type="pmid">31433799</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><mixed-citation publication-type="other">Marmoset genome data at Vertebrate Genome Project. Available at: <ext-link ext-link-type="uri" xlink:href="https://vgp.github.io/genomeark/Callithrix_jacchus">https://vgp.github.io/genomeark/Callithrix_jacchus</ext-link>. Accessed on Oct 28, 2019.</mixed-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jayakumar</surname><given-names>V</given-names></name><name><surname>Sakakibara</surname><given-names>Y</given-names></name></person-group><article-title>Comprehensive evaluation of non-hybrid genome assembly tools for third-generation PacBio long-read sequence data</article-title><source>Brief Bioinform</source><year>2017</year><volume>20</volume><issue>3</issue><fpage>866</fpage><lpage>876</lpage></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Koren</surname><given-names>S</given-names></name><name><surname>Walenz</surname><given-names>BP</given-names></name><name><surname>Berlin</surname><given-names>K</given-names></name><name><surname>Miller</surname><given-names>JR</given-names></name><name><surname>Bergman</surname><given-names>NH</given-names></name><name><surname>Phillippy</surname><given-names>AM</given-names></name></person-group><article-title>Canu: scalable and accurate long-read assembly via adaptive &#x003ba;-mer weighting and repeat separation</article-title><source>Genome Res</source><year>2017</year><volume>27</volume><fpage>722</fpage><lpage>736</lpage><pub-id pub-id-type="pmid">28298431</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><mixed-citation publication-type="other">SMARTdenovo. Available at: <ext-link ext-link-type="uri" xlink:href="https://github.com/ruanjue/smartdenovo">https://github.com/ruanjue/smartdenovo</ext-link>. Accessed on Oct 1, 2019.</mixed-citation></ref><ref id="CR35"><label>35.</label><mixed-citation publication-type="other">Ruan J, Li H. Fast and accurate long-read assembly with wtdbg2. bioRxiv. 2019;530972.</mixed-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name></person-group><article-title>Minimap and miniasm: fast mapping and <italic>de novo</italic> assembly for noisy long sequences</article-title><source>Bioinformatics.</source><year>2016</year><volume>32</volume><fpage>2103</fpage><lpage>2110</lpage><pub-id pub-id-type="pmid">27153593</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kolmogorov</surname><given-names>M</given-names></name><name><surname>Yuan</surname><given-names>J</given-names></name><name><surname>Lin</surname><given-names>Y</given-names></name><name><surname>Pevzner</surname><given-names>PA</given-names></name></person-group><article-title>Assembly of long, error-prone reads using repeat graphs</article-title><source>Nat Biotechnol</source><year>2019</year><volume>37</volume><fpage>540</fpage><lpage>546</lpage><pub-id pub-id-type="pmid">30936562</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chin</surname><given-names>CS</given-names></name><name><surname>Peluso</surname><given-names>P</given-names></name><name><surname>Sedlazeck</surname><given-names>FJ</given-names></name><name><surname>Nattestad</surname><given-names>M</given-names></name><name><surname>Concepcion</surname><given-names>GT</given-names></name><name><surname>Clum</surname><given-names>A</given-names></name><etal/></person-group><article-title>Phased diploid genome assembly with single-molecule real-time sequencing</article-title><source>Nat Methods</source><year>2016</year><volume>13</volume><fpage>1050</fpage><lpage>1054</lpage><pub-id pub-id-type="pmid">27749838</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Le Xiao</surname><given-names>C</given-names></name><name><surname>Chen</surname><given-names>Y</given-names></name><name><surname>Xie</surname><given-names>SQ</given-names></name><name><surname>Chen</surname><given-names>KN</given-names></name><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Han</surname><given-names>Y</given-names></name><etal/></person-group><article-title>MECAT: fast mapping, error correction, and <italic>de novo</italic> assembly for single-molecule sequencing reads</article-title><source>Nat Methods</source><year>2017</year><volume>14</volume><fpage>1072</fpage><lpage>1074</lpage><pub-id pub-id-type="pmid">28945707</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chin</surname><given-names>C-S</given-names></name><name><surname>Alexander</surname><given-names>DH</given-names></name><name><surname>Marks</surname><given-names>P</given-names></name><name><surname>Klammer</surname><given-names>AA</given-names></name><name><surname>Drake</surname><given-names>J</given-names></name><name><surname>Heiner</surname><given-names>C</given-names></name><etal/></person-group><article-title>nonhybrid, finished microbial genome assemblies from long-read SMRT sequencing data</article-title><source>Nat Methods</source><year>2013</year><volume>10</volume><issue>6</issue><fpage>563</fpage><lpage>569</lpage><pub-id pub-id-type="pmid">23644548</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alonge</surname><given-names>M</given-names></name><name><surname>Soyk</surname><given-names>S</given-names></name><name><surname>Ramakrishnan</surname><given-names>S</given-names></name><name><surname>Wang</surname><given-names>X</given-names></name><name><surname>Goodwin</surname><given-names>S</given-names></name><name><surname>Sedlazeck</surname><given-names>FJ</given-names></name><etal/></person-group><article-title>RaGOO: fast and accurate reference-guided scaffolding of draft genomes</article-title><source>Genome Biol</source><year>2019</year><volume>20</volume><issue>1</issue><fpage>224</fpage><pub-id pub-id-type="pmid">31661016</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Walker</surname><given-names>BJ</given-names></name><name><surname>Abeel</surname><given-names>T</given-names></name><name><surname>Shea</surname><given-names>T</given-names></name><name><surname>Priest</surname><given-names>M</given-names></name><name><surname>Abouelliel</surname><given-names>A</given-names></name></person-group><article-title>Pilon: an integrated tool for comprehensive microbial variant detection and genome assembly improvement</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><fpage>e112963</fpage><pub-id pub-id-type="pmid">25409509</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tarailo-Graovac</surname><given-names>M</given-names></name><name><surname>Chen</surname><given-names>N</given-names></name></person-group><article-title>Using RepeatMasker to identify repetitive elements in genomic sequences</article-title><source>Curr Protoc Bioinformatics</source><year>2009</year><volume>25</volume><fpage>4.10.1</fpage><lpage>4.10.14</lpage></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stanke</surname><given-names>M</given-names></name><name><surname>Waack</surname><given-names>S</given-names></name></person-group><article-title>Gene prediction with a hidden Markov model and a new intron submodel</article-title><source>Bioinformatics</source><year>2003</year><volume>19</volume><issue>Suppl 2</issue><fpage>ii215</fpage><lpage>ii225</lpage><pub-id pub-id-type="pmid">14534192</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><mixed-citation publication-type="other">Funannotate. Available at: <ext-link ext-link-type="uri" xlink:href="https://github.com/nextgenusfs/funannotate">https://github.com/nextgenusfs/funannotate</ext-link>. Accessed on Oct 1, 2019.</mixed-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Buchfink</surname><given-names>B</given-names></name><name><surname>Xie</surname><given-names>C</given-names></name><name><surname>Huson</surname><given-names>DH</given-names></name></person-group><article-title>Fast and sensitive protein alignment using DIAMOND</article-title><source>Nat Methods</source><year>2015</year><volume>12</volume><issue>1</issue><fpage>59</fpage><lpage>60</lpage><pub-id pub-id-type="pmid">25402007</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Slater</surname><given-names>G</given-names></name><name><surname>Birney</surname><given-names>E</given-names></name></person-group><article-title>Automated generation of heuristics for biological sequence comparison</article-title><source>BMC Bioinformatics</source><year>2005</year><volume>6</volume><issue>1</issue><fpage>31</fpage><pub-id pub-id-type="pmid">15713233</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Haas</surname><given-names>BJ</given-names></name><name><surname>Delcher</surname><given-names>AL</given-names></name><name><surname>Mount</surname><given-names>SM</given-names></name><name><surname>Wortman</surname><given-names>JR</given-names></name><name><surname>Smith</surname><given-names>RK</given-names></name><name><surname>Hannick</surname><given-names>LI</given-names></name><etal/></person-group><article-title>Improving the Arabidopsis genome annotation using maximal transcript alignment assemblies</article-title><source>Nucleic Acids Res</source><year>2003</year><volume>31</volume><issue>19</issue><fpage>5654</fpage><lpage>5666</lpage><pub-id pub-id-type="pmid">14500829</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Peng</surname><given-names>X</given-names></name><name><surname>Thierry-Mieg</surname><given-names>J</given-names></name><name><surname>Thierry-Mieg</surname><given-names>D</given-names></name><name><surname>Nishida</surname><given-names>A</given-names></name><name><surname>Pipes</surname><given-names>L</given-names></name><name><surname>Bozinoski</surname><given-names>M</given-names></name><etal/></person-group><article-title>Tissue-specific transcriptome sequencing analysis expands the non-human primate reference transcriptome resource (NHPRTR)</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><issue>D1</issue><fpage>D737</fpage><lpage>D742</lpage><pub-id pub-id-type="pmid">25392405</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grabherr</surname><given-names>MG</given-names></name><name><surname>Haas</surname><given-names>BJ</given-names></name><name><surname>Yassour</surname><given-names>M</given-names></name><name><surname>Levin</surname><given-names>JZ</given-names></name><name><surname>Thompson</surname><given-names>DA</given-names></name><name><surname>Amit</surname><given-names>I</given-names></name><etal/></person-group><article-title>Full-length transcriptome assembly from RNA-Seq data without a reference genome</article-title><source>Nat Biotechnol</source><year>2011</year><volume>29</volume><issue>7</issue><fpage>644</fpage><lpage>652</lpage><pub-id pub-id-type="pmid">21572440</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dobin</surname><given-names>A</given-names></name><name><surname>Davis</surname><given-names>CA</given-names></name><name><surname>Schlesinger</surname><given-names>F</given-names></name><name><surname>Drenkow</surname><given-names>J</given-names></name><name><surname>Zaleski</surname><given-names>C</given-names></name><name><surname>Jha</surname><given-names>S</given-names></name><etal/></person-group><article-title>STAR: ultrafast universal RNA-seq aligner</article-title><source>Bioinformatics.</source><year>2013</year><volume>29</volume><issue>1</issue><fpage>15</fpage><lpage>21</lpage><pub-id pub-id-type="pmid">23104886</pub-id></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>TD</given-names></name><name><surname>Watanabe</surname><given-names>CK</given-names></name></person-group><article-title>GMAP: a genomic mapping and alignment program for mRNA and EST sequences</article-title><source>Bioinformatics.</source><year>2005</year><volume>21</volume><issue>9</issue><fpage>1859</fpage><lpage>1875</lpage><pub-id pub-id-type="pmid">15728110</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Langmead</surname><given-names>B</given-names></name><name><surname>Salzberg</surname><given-names>SL</given-names></name></person-group><article-title>Fast gapped-read alignment with bowtie 2</article-title><source>Nat Methods</source><year>2012</year><volume>9</volume><issue>4</issue><fpage>357</fpage><lpage>359</lpage><pub-id pub-id-type="pmid">22388286</pub-id></element-citation></ref></ref-list></back></article>