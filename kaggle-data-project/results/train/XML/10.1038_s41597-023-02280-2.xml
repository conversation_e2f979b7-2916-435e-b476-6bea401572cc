<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ali="http://www.niso.org/schemas/ali/1.0/" article-type="data-paper" dtd-version="1.3"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">Sci Data</journal-id><journal-id journal-id-type="iso-abbrev">Sci Data</journal-id><journal-title-group><journal-title>Scientific Data</journal-title></journal-title-group><issn pub-type="epub">2052-4463</issn><publisher><publisher-name>Nature Publishing Group UK</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">10287764</article-id><article-id pub-id-type="pmid">37349345</article-id><article-id pub-id-type="publisher-id">2280</article-id><article-id pub-id-type="doi">10.1038/s41597-023-02280-2</article-id><article-categories><subj-group subj-group-type="heading"><subject>Data Descriptor</subject></subj-group></article-categories><title-group><article-title>A large expert-curated cryo-EM image dataset for machine learning protein particle picking</article-title></title-group><contrib-group><contrib contrib-type="author" equal-contrib="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-4047-9947</contrib-id><name><surname>Dhakal</surname><given-names>Ashwin</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-7052-4964</contrib-id><name><surname>Gyawali</surname><given-names>Rajan</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Wang</surname><given-names>Liguo</given-names></name><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-0305-2853</contrib-id><name><surname>Cheng</surname><given-names>Jianlin</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.134936.a</institution-id><institution-id institution-id-type="ISNI">0000 0001 2162 3504</institution-id><institution>Department of Electrical Engineering and Computer Science, NextGen Precision Health, </institution><institution>University of Missouri, </institution></institution-wrap>Columbia, MO 65211 USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.202665.5</institution-id><institution-id institution-id-type="ISNI">0000 0001 2188 4229</institution-id><institution>Laboratory for BioMolecular Structure (LBMS), </institution><institution>Brookhaven National Laboratory, </institution></institution-wrap>Upton, NY 11973 USA </aff></contrib-group><pub-date pub-type="epub"><day>22</day><month>6</month><year>2023</year></pub-date><pub-date pub-type="pmc-release"><day>22</day><month>6</month><year>2023</year></pub-date><pub-date pub-type="collection"><year>2023</year></pub-date><volume>10</volume><elocation-id>392</elocation-id><history><date date-type="received"><day>1</day><month>3</month><year>2023</year></date><date date-type="accepted"><day>30</day><month>5</month><year>2023</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2023</copyright-statement><license><ali:license_ref specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p><bold>Open Access</bold> This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The images or other third party material in this article are included in the article&#x02019;s Creative Commons license, unless indicated otherwise in a credit line to the material. If material is not included in the article&#x02019;s Creative Commons license and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this license, visit <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>.</license-p></license></permissions><abstract id="Abs1"><p id="Par1">Cryo-electron microscopy (cryo-EM) is a powerful technique for determining the structures of biological macromolecular complexes. Picking single-protein particles from cryo-EM micrographs is a crucial step in reconstructing protein structures. However, the widely used template-based particle picking process is labor-intensive and time-consuming. Though machine learning and artificial intelligence (AI) based particle picking can potentially automate the process, its development is hindered by lack of large, high-quality labelled training data. To address this bottleneck, we present CryoPPP, a large, diverse, expert-curated cryo-EM image dataset for protein particle picking and analysis. It consists of labelled cryo-EM micrographs (images) of 34 representative protein datasets selected from the Electron Microscopy Public Image Archive (EMPIAR). The dataset is 2.6 terabytes and includes 9,893 high-resolution micrographs with labelled protein particle coordinates. The labelling process was rigorously validated through 2D particle class validation and 3D density map validation with the gold standard. The dataset is expected to greatly facilitate the development of both AI and classical methods for automated cryo-EM protein particle picking.</p></abstract><kwd-group kwd-group-type="npg-subject"><title>Subject terms</title><kwd>Data publication and archiving</kwd><kwd>Cryoelectron microscopy</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">https://doi.org/10.13039/100000009</institution-id><institution>Foundation for the National Institutes of Health (Foundation for the National Institutes of Health, Inc.)</institution></institution-wrap></funding-source><award-id>R01GM146340</award-id><principal-award-recipient><name><surname>Cheng</surname><given-names>Jianlin</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution>This work was supported by National Institute of Health grant (grant #: R01GM146340) to JC and LW.</institution></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; Springer Nature Limited 2023</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background &#x00026; Summary</title><p id="Par2">Cryo-electron microscopy (cryo-EM) is an experimental technique that captures 2D images of biological molecules and assemblies (protein particles, virus, etc.) at cryogenic temperature using &#x02018;direct&#x02019; electron-detection camera technology<sup><xref ref-type="bibr" rid="CR1">1</xref></sup>. With the advent of cryo-EM, there has been a boom in structural discoveries relating to biomolecules, particularly large protein complexes and assemblies. These 3D structures of proteins<sup><xref ref-type="bibr" rid="CR2">2</xref></sup> are important for understanding their biological functions<sup><xref ref-type="bibr" rid="CR3">3</xref></sup> and their interactions with ligands<sup><xref ref-type="bibr" rid="CR4">4</xref>,<xref ref-type="bibr" rid="CR5">5</xref></sup>, which can aid both basic biological research and structure-based drug discovery<sup><xref ref-type="bibr" rid="CR4">4</xref>,<xref ref-type="bibr" rid="CR6">6</xref></sup>. A key step of constructing protein structures form cryo-EM data is to pick protein particles in cryo-EM images (micrographs). Before diving into recent developments in protein particle picking and the bottleneck it faces, it is important to understand the physics and chemistry behind the grid preparation and micrograph image acquisition in cryo-EM experiments.</p></sec><sec id="Sec2"><title>Cryo-EM grid preparation and image acquisition</title><p id="Par3">The process of acquiring the two-dimensional projections of biomolecular samples (e.g., protein particles) can be summarized in four brief steps: (1) sample purification, (2) cryo-EM grid preparation, (3) grid screening and evaluation, and (4) image capturing. Once the sample is purified according to the standard protocols<sup><xref ref-type="bibr" rid="CR7">7</xref></sup>; the next step of the single-particle procedure is to prepare the cryo-EM specimen. The grid preparation process, also known as vitrification, is straightforward. An aqueous sample is applied to a grid, which is then made thin. Eventually, the grid is plunged frozen at a time scale that inhibits the crystalline ice formation. Additionally, the particles must be evenly distributed across the grid in a wide range of orientations. It is very difficult to achieve a perfect cryo-EM grid because particles may choose to adhere to the carbon layer instead of being partitioned into holes. They may also adopt preferred orientations within the vitrified ice layer, which reduces the number of unique views<sup><xref ref-type="bibr" rid="CR8">8</xref></sup>. The grid is ready for analysis once the cryo-EM sample is successfully inserted into the electron microscope<sup><xref ref-type="bibr" rid="CR9">9</xref></sup>. Images are routinely captured during the screening phase at various magnifications to check for ice and particle quality. After the grids are optimized and ready for cryo-EM data collection, they are taken to a cryo-EM facility where qualified professionals load specimens into the microscope. To enable the best high-quality image capturing, experts adjust several parameters such as magnification, defocus range, electron exposure, and hole targeting techniques (see Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1A&#x02013;F</xref> illustrating the process of preparing cryo-EM samples and acquiring cryo-EM images). More details regarding cryo-EM sample preparation and image acquisition can be found in these studies<sup><xref ref-type="bibr" rid="CR7">7</xref>,<xref ref-type="bibr" rid="CR10">10</xref></sup>.<fig id="Fig1"><label>Fig. 1</label><caption><p>Overview of Cryo-EM pipeline, from sample preparation to particle recognition. (<bold>A</bold>) Aqueous sample preparation that contains variably dispersed heterogenous structure. (<bold>B</bold>) Cryo-EM grid containing holes that are filled with dispersed protein particles. (<bold>C</bold>) Magnified image of square patch illustrating microscopic holes in carbon. (<bold>D</bold>) Zoomed-in view of single hole containing suspended protein particles in thin layer of vitreous ice. (<bold>E</bold>) Cryo-Electron microscope used to facilitate high quality image generation. (<bold>F</bold>) Stack of 2D movie frames generated from microscope, called micrographs. (<bold>G</bold>) Motion corrected 2D micrograph images. (<bold>H</bold>) Particle picking using manual intervention or automatic procedures (green circles represent picked particles). (<bold>I</bold>) Initial 2D classes that contain quality protein particles along with junks and aggregates. (<bold>J</bold>) Best quality protein particles identified through computational analysis and visual inspection for 3D protein structure reconstruction.</p></caption><graphic xlink:href="41597_2023_2280_Fig1_HTML" id="d32e354"/></fig></p></sec><sec id="Sec3"><title>Cryo-EM micrographs and single particle analysis</title><p id="Par4">When the electron beam passes through a thin vitrified sample, it creates 2D image projections (see Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> for a visual illustration) of the samples (e.g., protein particles). The projections of the particles in various orientations are stored in different image formats (MRC, TIFF, TBZ, EER, PNG, etc.) which are called micrographs. Once the micrographs are obtained, the objective is to locate individual protein particles in each micrograph while avoiding crystalline ice contamination, malformed particles and grayscale background regions. In other words, the input for the particle picking problem is a micrograph, while the desired output is the coordinates of every protein particle in that micrograph (refer to Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> for the entire pipeline). Accurate detection of particles is necessary, as the presence of false positive particles can complicate subsequent processing, and eventually cause the 3D reconstruction process to fail entirely. The picking task is challenging due to several factors, including high noise levels caused by ice and contamination, low contrast of particle images, particles with heterogenous conformations, and unpredictability in an individual particle&#x02019;s appearance caused by variation in orientation. Once the particles are extracted from the micrographs, single particle analysis is performed to reconstruct the 3D density map and protein structure.</p></sec><sec id="Sec4"><title>Advances and challenges in single protein particle picking</title><p id="Par5">Several research initiatives were carried out worldwide to improve hardware<sup><xref ref-type="bibr" rid="CR11">11</xref>&#x02013;<xref ref-type="bibr" rid="CR13">13</xref></sup> and software<sup><xref ref-type="bibr" rid="CR14">14</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref></sup> to streamline and automate the cryo-EM data collection and processing steps for the determination of 3D structures<sup><xref ref-type="bibr" rid="CR18">18</xref></sup>. The recent technological advances in sample preparation, instrumentation, and computation methodologies have enabled the cryo-EM technology to solve many protein structures at better than 3&#x02009;A resolution. To obtain a high-resolution protein structure, selecting enough high-quality protein particles in cryo-EM images is critical. However, protein particle picking is still largely a computationally expensive and time-consuming process. One challenge facing cryo-EM data analysis is to develop automated particle picking techniques to circumvent manual intervention. To tackle the problem, numerous automatic and semi-automatic particle-picking procedures have been developed.</p><p id="Par6">A common technique for particle picking, known as template matching, uses user-predefined particles as templates for identifying particles in micrographs through image matching. However, because of varied ice contamination, carbon areas, overlapping particles, and other issues, the template matching often selects invalid particles (e.g., false positives). So subsequent manual particle selection is necessary.</p><p id="Par7">To deal with the issue, artificial intelligence (AI) and machine learning-based approaches have been proposed, which can be less sensitive to impurities and more suitable for large-scale data processing and therefore hold the potential of fully automating the particle picking process. XMIPP<sup><xref ref-type="bibr" rid="CR19">19</xref></sup>, APPLE picker<sup><xref ref-type="bibr" rid="CR20">20</xref></sup>, DeepPicker<sup><xref ref-type="bibr" rid="CR21">21</xref></sup>, DeepEM<sup><xref ref-type="bibr" rid="CR22">22</xref></sup>, FastParticle Picker<sup><xref ref-type="bibr" rid="CR23">23</xref></sup>, crYOLO<sup><xref ref-type="bibr" rid="CR24">24</xref></sup>, PIXER<sup><xref ref-type="bibr" rid="CR25">25</xref></sup>, PARSED<sup><xref ref-type="bibr" rid="CR26">26</xref></sup>, WARP<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>, Topaz<sup><xref ref-type="bibr" rid="CR28">28</xref></sup>, AutoCryoPicker<sup><xref ref-type="bibr" rid="CR29">29</xref></sup>, and DeepCryoPicker<sup><xref ref-type="bibr" rid="CR30">30</xref></sup>, can be taken as good examples of such efforts.</p><p id="Par8">The datasets used to train and test machine learning particle picking methods were curated from EMPIAR<sup><xref ref-type="bibr" rid="CR31">31</xref></sup>. It contains almost all the publicly available raw cryo-EM micrographs. It is a public repository containing 1,159 entries/datasets (2.39 PB) as of Jan 29, 2023. It includes not just cryo-EM images of proteins, but also Soft X-ray Tomography (SXT), cryo-ET and many other microscopic projections of other biological samples. Only some cryo-EM images of a small number of datasets in EMPIAR contain particles manually labelled by the original authors of the data. Therefore, most existing machine learning methods for particle picking were trained and tested on only a few manually labeled datasets of a few proteins like Apoferritin and Keyhole Limpet Hemocyanin (KLH). The methods trained on the limited amount of particle data of one or a few proteins cannot generalize well to pick particles of various shapes in the cryo-EM micrographs of many diverse proteins in the real world. Therefore, even though machine learning particle picking is a promising direction, no machine learning method has been able to replace the labor-intensive template-based particle picking in practice. Therefore, the lack of manually labelled particle image data of diverse protein types is hindering the development of machine learning and AI methods to automate protein particle picking.</p><p id="Par9">Creating a high-quality manually labelled single-protein particle dataset of a large, diverse set of representative proteins to facilitate machine learning is a challenging task. Single-particle cryo-EM images suffer from high background noise and low contrast due to the limited electron dose to minimize the radiation damage to the biomolecules of interest during imaging, which makes particle picking difficult even for humans. Low signal-to-noise ratio (SNR) of the micrographs, presence of contaminants, contrast differences owing to varying ice thickness, background noise fluctuation, and lack of well-segregated particles further increases the difficulty in particle identification<sup><xref ref-type="bibr" rid="CR32">32</xref></sup>. This is one reason there is still a lack of large manually curated protein particle datasets in the field.</p><p id="Par10">A common problem of the particle picking algorithms trained on a small amount of particle data of a few proteins is that they cannot distinguish &#x02018;good&#x02019; and &#x02018;bad&#x02019; particles well, including overlapped particles, local aggregates, ice contamination and carbon-rich areas<sup><xref ref-type="bibr" rid="CR33">33</xref></sup>. For instance, the methods: DRPnet<sup><xref ref-type="bibr" rid="CR34">34</xref></sup>, TransPicker<sup><xref ref-type="bibr" rid="CR35">35</xref></sup>, CASSPER<sup><xref ref-type="bibr" rid="CR36">36</xref></sup>, and McSweeney <italic>et al.&#x02019;s</italic> method<sup><xref ref-type="bibr" rid="CR37">37</xref></sup> that made significant contributions to the particle selection problem suffered the two similar problems. Firstly, there is not a sufficient and diversified dataset to train them. Secondly, there are not enough manually curated data to test them. The similar problems happened to other supervised and unsupervised machine learning methods, such as an unsupervised clustering approach<sup><xref ref-type="bibr" rid="CR38">38</xref></sup>, AutoCryoPicker<sup><xref ref-type="bibr" rid="CR29">29</xref></sup>, DeepCryoPicker<sup><xref ref-type="bibr" rid="CR30">30</xref></sup>, APPLE picker<sup><xref ref-type="bibr" rid="CR20">20</xref></sup>, Mallick <italic>et al.&#x02019;s</italic> method<sup><xref ref-type="bibr" rid="CR39">39</xref></sup>, gEMpicker<sup><xref ref-type="bibr" rid="CR40">40</xref></sup>, Langlois <italic>et al.&#x02019;s</italic> method<sup><xref ref-type="bibr" rid="CR33">33</xref></sup>, DeepPicker<sup><xref ref-type="bibr" rid="CR21">21</xref></sup>, DeepEM<sup><xref ref-type="bibr" rid="CR22">22</xref></sup>, Xiao <italic>et al.&#x02019;s</italic> method<sup><xref ref-type="bibr" rid="CR23">23</xref></sup>, APPLE picker<sup><xref ref-type="bibr" rid="CR20">20</xref></sup>, Warp<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>, SPHIRE-crYOLO<sup><xref ref-type="bibr" rid="CR41">41</xref></sup>, and HydraPicker<sup><xref ref-type="bibr" rid="CR42">42</xref></sup> all encountered similar problems. They usually perform well on the small, standard datasets used to train and test them (e.g., Apoferritin and KLH), but may not generalize well to non-ideal, realistic datasets containing protein particles of irregular and complex shapes, which are generated daily by the cryo-EM facility around the world.</p><p id="Par11">To address this key bottleneck hindering the development of machine learning and AI methods for automated cryo-EM protein particle picking, we created a large dataset of cryo-EM micrographs, named CryoPPP<sup><xref ref-type="bibr" rid="CR43">43</xref></sup>, which includes manually labelled protein particles. The micrographs are associated with 34 representative proteins of diverse sequences and structures that cover a much larger protein particle space than the existing datasets of a few proteins such as Apoferritin and KLH.</p><p id="Par12">In this study, we ensured the inclusivity of diverse protein particles by considering various protein types, shape patterns, sizes (ranging from 77.14&#x02009;kDa to 2198.78&#x02009;kDa), source organisms, and different variations of the micrographs. Supplementary Table&#x000a0;<xref rid="MOESM4" ref-type="media">S3</xref> provides the additional information on the different protein types that were selected to label particles. Additionally, we incorporated cryo-EM images with varying defocus values associated with each EMPIAR ID to include a diverse set of micrographs. The box-whisker plot in Supplementary Fig.&#x000a0;<xref rid="MOESM1" ref-type="media">S1</xref> shows the diversity of the defocus values in each EMPIAR ID.</p><p id="Par13">Machine learning methods yield best results when being trained on a diverse set of representative data. Hence, we included micrographs containing particles with different physical features and complexities, as shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>. Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2A&#x02013;D</xref> depict the most ideal cases of particle picking in which the protein particles are visible to naked eyes. If machine learning methods are only trained solely on these micrographs, they perform relatively well in the simple cases but poorly on more challenging micrographs that have low signal-to-noise ratio. Thus, we selected difficult micrographs that contain both particles and lots of ice patches, contaminations (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2B</xref>) and carbon regions (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2C</xref>).<fig id="Fig2"><label>Fig. 2</label><caption><p>Examples of diverse cryo-EM micrographs in CryoPPP used for particle labelling. (<bold>A</bold>) Ideal micrographs (EMPIAR-10590) containing protein particles with sufficient contrast that can be easily identified by naked eye. (<bold>B</bold>) Micrographs containing an abundance of ice patches and contaminations (EMPIAR-10532). Crystalline ice is evident by the cluster of light and dark patches making it difficult to distinguish true protein particles from false positives. (<bold>C</bold>) Micrograph containing large carbon areas (EMPIAR-10005). (<bold>D</bold>) Micrographs containing a monodisperse distribution of protein particles (EMPIAR-10852). (<bold>E</bold>) Large Cluster of clumped protein particles making it difficult to recognize and pick particles (EMPIAR-10387). (<bold>F</bold>) Micrographs (EMPIAR-11057) containing protein particles overcrowded at upper half region. (<bold>G</bold>) Difference of ice thickness resulting in the top left part to appear brighter and the bottom right part darker. A crack within the vitrified hole in the lower right part of micrograph causing blurring effect (EMPIAR-10017). (<bold>H</bold>) Micrograph (EMPIAR-10093) containing heterogeneous top, side, and inclined views of protein particles.</p></caption><graphic xlink:href="41597_2023_2280_Fig2_HTML" id="d32e609"/></fig></p><p id="Par14">In addition to the aforementioned variations in the micrographs, CryoPPP also comprises micrographs containing mono-dispersed (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2D</xref>) particles characterized by particles of uniform size and micrographs with heterogenous confirmations that have varying top, side and inclined views of particles. Other micrographs with sub-optimal concentration of particles, clusters of proteins, and non-uniform ice distributions were included. The detailed features of the 34 diverse sets of cryoEM micrographs in CryoPPP are described in Supplementary Table&#x000a0;<xref rid="MOESM4" ref-type="media">S3</xref>.</p><p id="Par15">The quality of the manually labeled particles of selected proteins was eventually validated rigorously. This validation was done against particles originally labelled by the authors who generated the cryo-EM data (called gold standard here) by both 2D particle class validation and 3D cryo-EM density map validation. The quality of our manual annotation is on par with the annotations provided by the experts who created the data in the first place, which confirms our manual particle labelling process is effective. Therefore, we believe CryoPPP is a valuable resource for training and testing machine learning and AI methods for automated protein particle picking.</p></sec><sec id="Sec5"><title>Methods</title><p id="Par16">CryoPPP was created through a series of steps as shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>. We first crawled the data from the EMPIAR website using python API and FTP scripts fetched through Bash scripting. We filtered out microscopic images of various non-single-protein particles (e.g., bacteria, filaments, RNA, protein fibril, virus-like particles) and retained only high-resolution micrographs acquired by cryo-EM technique for manual particle labeling.<fig id="Fig3"><label>Fig. 3</label><caption><p>Graphical illustration of the overall methodology of creating CryoPPP dataset. (<bold>A</bold>&#x02013;<bold>D</bold>) represent the steps for data acquisition and protein metadata preparation. (1&#x02013;8) represent subsequent steps for the ground truth annotation and validation of picked protein particles. The iterative approach between step (5) and (6) is carried out to achieve the high-quality picking of particles.</p></caption><graphic xlink:href="41597_2023_2280_Fig3_HTML" id="d32e643"/></fig></p><p id="Par17">After importing the micrographs with all the physiochemical parameters gathered from the corresponding published literature, we performed motion correction and Contrast Transfer Function (CTF) estimation for them. After preparing the micrographs, two human experts conducted an initial manual particle selection process separately, using low pass filter values and appropriate particle diameter settings, on approximately 20 out of about 300 micrographs per selected protein (EMPIAR ID).</p><p id="Par18">The expert-picked particles were cross-validated and then went through 2D particle classification. The best particles based on resolution, particle count, and visually appealing and sensible 2D classes were selected and further used for template-based particle picking and further human inspection. After iterating the 2D classes from template-based picking and human inspection, we ultimately obtained the final set of highly confident protein particles as ground truth and exported them in the files in star, csv and mrc formats. The first two files (.star and.csv) contain the coordinates of the protein particles and the latter (.mrc) store particle stacks. The process of creating CryoPPP in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> is described in the following sections in detail.</p><sec id="Sec6"><title>EMPIAR metadata collection and filtering</title><p id="Par19">The process of preparing the dataset began with collecting metadata about cryo-EM image datasets in EMPIAR. Data collection scripts that use python API and FTP protocols were used to automatically download the metadata from the EMPIAR web portal<sup><xref ref-type="bibr" rid="CR31">31</xref></sup>. The metadata includes EMPIAR ID of each cryo-EM dataset of a protein, the corresponding Electron Microscopy Data Bank (EMDB) ID, Protein Data Bank (PDB) ID, size of dataset, resolution, total number of micrographs, image size/type, pixel spacing, micrograph file extension, gain/motion correction file extension, FTP path for micrograph/gain files, Globus path for micrograph/gain files, and publication information.</p><p id="Par20">Following the metadata collection, the individual cryo-EM datasets in the collection were filtered as depicted in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> (<bold>Steps 1&#x02013;5</bold>). First, we only chose EMPIAR IDs (datasets) that have their volume maps deposited in EMDB. From the chosen EMPIAR datasets, we only selected ones that had corresponding protein structures in the Protein Data Bank (PDB).<fig id="Fig4"><label>Fig. 4</label><caption><p>The step-by-step procedure for collecting and selecting Cryo-EM protein datasets from EMPIAR database. 335 unique EMPIAR datasets (IDs) of 335 proteins were selected at the end.</p></caption><graphic xlink:href="41597_2023_2280_Fig4_HTML" id="d32e676"/></fig></p><p id="Par21">To ensure high data quality, we then retained only the EMPIAR datasets whose resolution was better than 4 Angstrom (&#x000c5;). We observed that there were some redundant EMPIAR datasets (e.g., EMPIAR ID: 10709 &#x00026; 10707, EMPIAR ID: 10899 &#x00026; 10897) that correspond to the same biomolecule with the same PDB and EMDB IDs. Hence, we eliminated those duplicate entries. After removing duplicate records, we selected only EMPIAR datasets that contained micrographs of protein particles, excluding other biological samples such as viruses. This filtering step required some literature study of individual EMPIAR datasets. The motion correction and gain correction files for the selected datasets were extracted from the EMPIAR if required. The final list of meta data includes 335 EMPIAR entries, 34 out of which were used for manual labelling. Refer to the <italic>EMPIAR_metadata_335.xlsx</italic> file in CryoPPP for further information about the list of 335 datasets of 355 proteins.</p></sec><sec id="Sec7"><title>Manual particle labeling</title><p id="Par22">Manually picking particles in cryo-EM micrographs through the GUI interfaces of cryo-EM analysis tools such as CryoSPARC<sup><xref ref-type="bibr" rid="CR16">16</xref></sup>, EMAN2<sup><xref ref-type="bibr" rid="CR14">14</xref></sup> and RELION<sup><xref ref-type="bibr" rid="CR15">15</xref></sup> is very time consuming. We carefully imported large micrographs, carried out the motion correction, and estimated CTF, especially in the case when flipping and rotating gain files are required to match the input array shape of the micrograph and gain file. Furthermore, it takes a lot of disk space to store the labelled particle data together with the corresponding micrographs and particle stack files. Therefore, we chose 34 representative EMPIAR datasets out of 335 entries selected in the previous section for manual particle labelling to create the CryoPPP dataset, considering diverse particle size/shapes, density distribution, noise level, and ice and carbon areas. Moreover, proteins from a wide range of categories, such as: metal binding, transport, membrane, nuclear, signaling, and viral proteins were selected. See supplementary Tables&#x000a0;<xref rid="MOESM2" ref-type="media">S1</xref>, <xref rid="MOESM4" ref-type="media">S3</xref> for more details about the 34 proteins (cryo-EM datasets). Most of the pre-processing, manual particle labelling, real-time quality assessment, and decision-making workflows were performed using CryoSPARC v4.1.1<sup><xref ref-type="bibr" rid="CR16">16</xref></sup>, EMAN2<sup><xref ref-type="bibr" rid="CR14">14</xref></sup>, and RELION 4.0<sup><xref ref-type="bibr" rid="CR15">15</xref></sup>.</p><p id="Par23">CryoPPP includes a total of 9,893 micrographs (~300 Cryo-EM images per selected EMPIAR dataset). We labelled ~300 micrographs per EMPIAR data because using all the micrographs in each dataset would result in 32.993 TB of data, making it too large to store, transfer, and use for most machine learning tasks.</p><p id="Par24">There is no rule of thumb to determine the number of particles required for training machine learning methods to achieve optimal performance. It varies from protein to protein. It is also worth noting that the resolution of 3D density map is not solely dependent on the number of picked 2D particles, but also on the inclusion of a diverse range of particle orientations, which can significantly improve the resolution. CryoPPP contains 2.7 million particles of 34 different proteins, which should be adequate for machine learning algorithms to learn relevant particle features. Typically, we utilized approximately 300 micrographs per protein to pick particles. However, if adequate particle views were not extracted from the first 300 micrographs, a larger number of micrographs were employed. The different orientations of protein particles were assessed during the 2D classification and manual inspection stages which are elaborated in the subsequent sections.</p><sec id="Sec8"><title>Importing movies</title><p id="Par25">This is the crucial first step of particle labeling. For each EMPIAR dataset, we import two inputs: micrographs and gain reference (motion correction files). We analyzed the description of the EM data acquisition and grid preparation for each dataset in order to collect the important information such as raw pixel size (&#x000c5;), acceleration voltage (kV), spherical aberration (mm), and total exposure dose (e/&#x000c5;<sup>2</sup>) for the micrographs in the dataset.</p><p id="Par26">Furthermore, we obtained gain reference for micrographs if their motion was not corrected before. We used <italic>e2proc2d</italic>, a generic 2-D image processing program in EMAN2<sup><xref ref-type="bibr" rid="CR14">14</xref></sup>, to convert different formats of motion correction file (e.g.,.dm4,.tiff,.dat, etc.) to.mrc file since CryoSPARC accepts only.mrc extension. Then, based on the microscope camera settings and how the data was acquired during the imaging process, we applied geometrical transformations (flip gain reference and defect file left-to-right/top-to-bottom (in x/y axis) or rotate gain reference clockwise/anti-clockwise by certain degrees) relative to the image data. Supplementary Table&#x000a0;<xref rid="MOESM2" ref-type="media">S1</xref> contains the details of input parameters for each EMPIAR ID. After importing movies and motion correction files, we proceeded to the job inspection panel of CryoSPARC to ensure that all input settings and loaded micrographs were correct.</p></sec><sec id="Sec9"><title>Patch motion correction</title><p id="Par27">When specimens are exposed to an electron beam, the mobility of sample molecules (protein particles) during data acquisition can affect the overall quality of electron micrographs and lower the final resolution<sup><xref ref-type="bibr" rid="CR44">44</xref></sup>. Hence, it is necessary to correct the movement of particles (referred to as &#x02018;beam-induced motion&#x02019;).</p><p id="Par28">The causes behind this motion can be categorized into two types: (1) Motion from Microscope: It is caused by stage drift and usually occurs in microscope due to little amount of vibration left over after the stage has been aligned to a new position<sup><xref ref-type="bibr" rid="CR45">45</xref></sup>. It moves the sample relative to the beam and optical axis. This motion is quite jagged in time, with sharp accelerations or twitches, but is consistent. The entire image will move in the same direction over time. (2) Motion from sample deformation: This motion is caused by the energy deposited into the ice by the beam, or energy already trapped in it, due to strained forces locked in during freezing. It is eventually released during the image capturing process. As the electrons pass through the samples, the energy from the beam and the temperature change causes the ice to physically deform and bend. That deformation is often smoother over time, but it can be highly anisotropic in space. In this case, various parts of the same image can move in different directions at the same time.</p><p id="Par29">Both motions must be estimated and corrected to obtain high-resolution reconstructions from the data. In the patch-based motion correction step, we corrected both global motion (stage drift) and local motion (beam-induced anisotropic sample deformation) for the micrographs (as shown in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5A</xref> using CryoSPARC. In the anisotropic deformation plot in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5B</xref>, each red circle indicates the center of a single &#x0201c;patch&#x0201d; of the image, and the curves emerging from each circle show the motion of that portion of the sample. We can observe the correlation between the motion of adjacent patches. They move somewhat similarly to one another. To prevent the fit from being distorted by random noise in the micrograph, the patch motion correction algorithm imposes smoothness constraints on the motion.<fig id="Fig5"><label>Fig. 5</label><caption><p>The patch-based local and global motion correction pipeline for EMPIAR ID 10737 (E. coli cytochrome bo3 in MSP Nanodiscs). (<bold>A</bold>) Full frames of micrographs as input. (<bold>B</bold>) Anisotropic deformation. (<bold>C</bold>) Rigid motion trajectories plots. Blue: original trajectory, Radish: trajectory with small smoothing penalty, Green: trajectory with fine smoothing. Left: Overall motion trajectory over X and Y motion. Center: X-motion plot over time. Right: Y-motion plot over time. (<bold>D</bold>) Non-dose weighted aligned averaged micrographs with the highest amount of signal and least amount of motion blur as output.</p></caption><graphic xlink:href="41597_2023_2280_Fig5_HTML" id="d32e785"/></fig></p><p id="Par30">Figure&#x000a0;<xref rid="Fig5" ref-type="fig">5C</xref> are the examples of plots generated by patch motion correction that depict the computed trajectories. The set of plots shows overall motion correction (an actual trajectory plot, followed by X-motion plot and Y-motion plots over time). In the overall motion trajectory over X and Y motion (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5C</xref>, <bold>Left</bold>), each dot represents the sample&#x02019;s position from frame to frame. Here, the x and y axes represent the units of pixels in the raw data&#x02019;s pixel size. The sample begins at point (<bold>X</bold>), moves downward, makes a curve and again changes direction toward the left-top, and then continues to descend to the left. We apply this trajectory to the input data by shifting each image in reverse of what the motion trajectory suggests and finally averaging images together. In other words, we track a sample&#x02019;s motion during the exposure to undo it.</p><p id="Par31">While curating CryoPPP, we noticed several factors (protein size, charge, and ice thickness) potentially influencing the scale of local motion in Cryo-EM micrographs. Larger proteins tend to exhibit slower local motion than smaller ones, due to their increased mass and higher structural complexity. The charge distribution on a protein surface can also affect the scale of local motion, with highly charged proteins exhibiting more restricted motion due to the electrostatic interactions with the surrounding ice, while relatively neutral proteins may be more mobile. Additionally, we observed that the thickness of the ice layer surrounding a protein can influence local motion; thicker ice layers can provide more structural stability but may introduce more noise and distortion in the micrograph.</p></sec><sec id="Sec10"><title>Patch-based CTF estimation</title><p id="Par32">The contrast of images captured in the electron microscope is affected by imaging defocus and lens aberrations, which are adjusted by microscope operators to enhance the contrast. The relationship between lens aberrations and the contrast in the image is defined by the CTF. It explains how information is transferred as a function of spatial frequency.</p><p id="Par33">It is important to estimate CTF, which is then corrected during 2D particle classification and 3D reconstruction steps. Otherwise, the feasible reconstruction will have extremely low resolution. A full treatment of the effects of the CTF usually proceeds in two stages: CTF estimation and CTF correction. In CryoSPARC used in this work, the CTF model is given by the Eq.&#x000a0;<xref rid="Equ1" ref-type="disp-formula">1</xref>.<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${C}{T}{F}=-{c}{o}{s}\left({\pi }\Delta {z}{{\lambda }}_{{e}}{f}^{2}-\frac{{\pi }}{2}{C}_{{s}}{{\lambda }}_{{e}}^{3}{f}^{4}+{\Phi }\right)$$\end{document}</tex-math><mml:math id="M2" display="block"><mml:mi mathvariant="italic">C</mml:mi><mml:mi mathvariant="italic">T</mml:mi><mml:mi mathvariant="italic">F</mml:mi><mml:mo>=</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="italic">c</mml:mi><mml:mi mathvariant="italic">o</mml:mi><mml:mi mathvariant="italic">s</mml:mi><mml:mfenced close=")" open="("><mml:mrow><mml:mi mathvariant="italic">&#x003c0;</mml:mi><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:mi mathvariant="italic">z</mml:mi><mml:msub><mml:mrow><mml:mi mathvariant="italic">&#x003bb;</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">e</mml:mi></mml:mrow></mml:msub><mml:msup><mml:mrow><mml:mspace width="0.10em"/><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:mrow><mml:mi mathvariant="italic">&#x003c0;</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:mfrac><mml:msub><mml:mrow><mml:mi>C</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">s</mml:mi></mml:mrow></mml:msub><mml:msubsup><mml:mrow><mml:mi mathvariant="italic">&#x003bb;</mml:mi></mml:mrow><mml:mrow><mml:mi mathvariant="italic">e</mml:mi></mml:mrow><mml:mrow><mml:mn>3</mml:mn></mml:mrow></mml:msubsup><mml:msup><mml:mrow><mml:mi>f</mml:mi></mml:mrow><mml:mrow><mml:mn>4</mml:mn></mml:mrow></mml:msup><mml:mi>+</mml:mi><mml:mi mathvariant="italic">&#x003a6;</mml:mi></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="41597_2023_2280_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula>where &#x00394;<italic>z</italic> is defocus, <italic>&#x003bb;</italic><sub><italic>e</italic></sub> is the wavelength of the incident electrons, <italic>C</italic><sub><italic>s</italic></sub>
<italic>is</italic> spherical aberration, and <italic>f</italic> is spatial frequency. <italic>&#x003a6;</italic> represents a phase shift factor.</p><p id="Par34">Most cryo-EM samples are not &#x02018;flat&#x02019;. Before a sample is frozen, particles tend to concentrate around the air-water interfaces, and the ice surface itself is usually not flat<sup><xref ref-type="bibr" rid="CR46">46</xref>,<xref ref-type="bibr" rid="CR47">47</xref></sup>. Because defocus has an impact on the CTF, distinct particles can have various defoci and hence various CTFs within a single image. To address this problem, CryoSPARC offers a patch-based CTF estimator that analyzes numerous regions of a micrograph to calculate a &#x0201c;defocus landscape&#x0201d;.</p><p id="Par35">We performed a 1D search over defocus for every micrograph. Figure&#x000a0;<xref rid="Fig6" ref-type="fig">6A</xref> depicts the 1D search for a particular micrograph of EMPIAR ID 10737<sup><xref ref-type="bibr" rid="CR48">48</xref></sup>. This plot helps identify a particular defocus value that stands out among a variety of other defocus values (x-axis). Patch CTF creates a plot showing how closely the input micrographs&#x02019; observed power spectrum and the calculated CTF match. The CTF fit plot in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6B</xref> shows that the computed CTF matches the observed power spectrum up to a resolution of 3. 993&#x02009;&#x000c5;. The cross correlation between the observed spectrum and the calculated CTF is depicted by the cyan line in the plot. The vertical green line in the plot represents the frequency at which the fit deviates from CryoSPARC&#x02019;s cross-correlation threshold of 0.3 for a successful fit.<fig id="Fig6"><label>Fig. 6</label><caption><p>Diagnostic plots of CTF for EMPAIR 10737 (E. coli cytochrome bo3 in MSP Nanodiscs). (<bold>A</bold>) 1D search over varying defocus values (underfocus). (<bold>B</bold>) CTF fit plot. X-axis displays frequency, in units in inverse angstroms (&#x000c5;<sup>&#x02212;1</sup>) and Y-axis shows correlation metric between power spectrum (PS) and CTF value. Black: observed experimental power spectrum. Red: calculated CTF. Cyan: cross-correlation (fit).</p></caption><graphic xlink:href="41597_2023_2280_Fig6_HTML" id="d32e962"/></fig></p><p id="Par36">We executed the patch CTF to obtain the output micrographs with data on their average defocus and the defocus landscape. When particles were extracted, this data was automatically used to assign each particle a local defocus value based on its position in the landscape.</p></sec><sec id="Sec11"><title>Manual particle picking</title><p id="Par37">After performing the motion correction and CTF estimation, we manually picked particles interactively from aligned/motion-corrected micrographs with the goal of creating particle templates for auto-picking using &#x02018;Manual Picker&#x02018; job in CryoSPARC. Depending on the size and shape of the protein particles, we adjusted the box size and the particle diameter. Since picking particles on raw micrographs is extremely difficult, we tweaked the &#x02018;Contrast Intensity Override&#x02019; while viewing micrographs in order to obtain the best distinctive view for picking particles.</p><p id="Par38">It is particularly challenging to manually pick particles from micrographs with smaller defocus levels. Figure&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref> illustrates the visualization of micrographs in the same dataset with different defocus levels for EMPAIR 10532<sup><xref ref-type="bibr" rid="CR48">48</xref></sup>. It is worth pointing out that the task of particle picking becomes significantly more challenging for AI methods when the micrographs have a low signal-to-noise ratio, contain numerous ice-patches, carbon areas, aggregated particles, and non-uniform ice distribution, as illustrated in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2B,C,E,G</xref>). Hence, to generate comprehensive templates, we manually picked particles from diverse micrographs and the micrographs with wide defocus range (see supplementary Fig.&#x000a0;<xref rid="MOESM1" ref-type="media">S1</xref>) and CTF fit values.<fig id="Fig7"><label>Fig. 7</label><caption><p>Cryo-EM micrograph images of EMPIAR ID 10532 (Influenza Hemagglutinin) with different defocus values. Micrographs with smaller defocus values make particle picking difficult and vice-versa.</p></caption><graphic xlink:href="41597_2023_2280_Fig7_HTML" id="d32e993"/></fig></p><p id="Par39">As manual picking was very time intensive, we selected a subset of micrographs (around 20 micrographs of each EMPIAR dataset) for manually picking initial particles for the subsequent template-based particle picking.</p><p id="Par40">To ensure data reproducibility, the intermediate star files of manually expert-picked particles are deposited in CryoPPP and can be found under the <italic>ground-truth</italic> subdirectory. Additional information regarding the total number of manually picked particles, number of micrographs considered for manual picking, particle diameter, angular sampling, and minimum separation distance of particles are provided in the Supplementary Table&#x000a0;<xref rid="MOESM3" ref-type="media">S2</xref>.</p></sec><sec id="Sec12"><title>Forming and selecting best 2D particle classes</title><p id="Par41">The manually picked particles went through the 2D classification step. This step helped to classify the picked particles into several 2D classes to facilitate stack cleaning and junk particles removal. To analyze the distribution of views within the dataset qualitatively, we specified a specific number of 2D classes. By doing this, we investigated the particle quality and removed junk particle classes, which ultimately facilitated the selection of good particle classes.</p><p id="Par42">We specified the initial Classification Uncertainty Factor (ICUF) and maximum alignment resolution to align particles to the classes with 40 expectation maximization (EM) iterations. The diameter of the circular mask that was applied to the 2D classes at each iteration was controlled using the circular mask diameter in the case of crowded particles.</p><p id="Par43">After the 2D classes were formed, we selected the best particle classes interactively to remove the junks. Figure&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref> shows an example of 2D classification and selection of highly confident particles for EMPIAR ID 10017<sup><xref ref-type="bibr" rid="CR49">49</xref></sup>. We used three diagnostic measures to select the 2D classes: resolution (&#x000c5;) of a class, the number of particles of a class (higher, better), visual appearance of a class. Considering only the number of particles in a class is not sufficient because some classes containing a small number of particles may represent a unique view of the protein.<fig id="Fig8"><label>Fig. 8</label><caption><p>2D classes for EMPIAR ID 10017 (Beta-galactosidase), ordered ascendingly by the number of particles assigned to each class. Green: High quality particle classes selected for further template-based picking. Red: Rejected particle classes.</p></caption><graphic xlink:href="41597_2023_2280_Fig8_HTML" id="d32e1028"/></fig></p><p id="Par44">Refer to the Supplementary Table&#x000a0;<xref rid="MOESM3" ref-type="media">S2</xref> for additional details regarding the number of 2D classes in each EMPIAR dataset, window inner and outer radii, recenter mask threshold, number of iterations to anneal sigma as well as other relevant thresholds and parameters.</p></sec><sec id="Sec13"><title>Template based picking and manual inspection and extraction of particles</title><p id="Par45">After the best particle classes were selected and exported, we used a template generated from the &#x02018;<bold>Forming and Selecting Best 2D</bold>&#x02019; step to pick more particles. The process was iterative, meaning that the output of a round of &#x02018;template-based picking and inspection&#x02019; was again utilized for &#x02018;2D class formation&#x02019; step to form and select best 2D classes under the human inspection. This process was repeated until we acquired high resolution particles that include all possible particle projection angles.</p><p id="Par46">The final templates with green boxes (as shown in Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref>) were used to execute auto-pick particles from micrographs. With CryoSPARC&#x02019;s Template Picker, we used high resolution templates to precisely select particles that matched the geometry of the target structure. Figure&#x000a0;<xref rid="Fig9" ref-type="fig">9A</xref> represents manually picked particles for EMPIAR-10017<sup><xref ref-type="bibr" rid="CR49">49</xref></sup> that work as templates to facilitate template-based picking that eventually results in template-based picked particles ready for human inspection as shown in Fig.&#x000a0;<xref rid="Fig9" ref-type="fig">9B</xref>. We specified constraints like particle diameter in angstrom (see Supplementary Table&#x000a0;<xref rid="MOESM3" ref-type="media">S2</xref> for more information) and a minimum distance between particles to generate the templates based on the SK97 sampling algorithm<sup><xref ref-type="bibr" rid="CR34">34</xref></sup> to remove any signals from the corners and prevent crowding. We observed that the blob-based in picking in RELION required minimum and maximum allowed diameter of the blobs, whereas defining a single value for particle&#x02019;s diameter works well in CryoSPARC&#x02019;s template particle picking step.<fig id="Fig9"><label>Fig. 9</label><caption><p>Cryo-EM micrograph image of EMPIAR ID 10017 (defocus value: &#x02212;3.63&#x02009;&#x000b5;m) used for template-based particle picking. (<bold>A</bold>) Micrograph with manually picked protein particles (encircled with green circle, particle diameter: 190&#x02009;&#x000c5;, low pass filter value: 25). (<bold>B</bold>) Intermediate picked protein particles with template-based picking ready for manual inspection and the adjustment of power value and NCC score.</p></caption><graphic xlink:href="41597_2023_2280_Fig9_HTML" id="d32e1079"/></fig></p><p id="Par47">Finally, the particles obtained by the template picking went through the manual inspection step, where we examined and modified picks using various thresholds. We adjusted the lowpass filter, optimum power score range, and normalized cross-correlation (NCC) score (see Supplementary Table&#x000a0;<xref rid="MOESM3" ref-type="media">S2</xref>) to improve the visibility of the picks. In this step, we removed false positive particles as depicted in Fig.&#x000a0;<xref rid="Fig12" ref-type="fig">12B</xref>. While efforts were made to minimize false negatives during particle picking, it is important to recognize that the complete elimination of false negatives is impossible in one round of annotation. The 2D colored histogram plots as depicted in Fig.&#x000a0;<xref rid="Fig12" ref-type="fig">12</xref> were used to scrutinize micrograph median pick scores versus defocus for extracting the coordinates of high-quality protein particles. Hence, we have strived to optimize particle picking, aiming to minimize both false positives and false negatives and thus improving the accuracy and reliability of CryoPPP. We will continue to improve and update CryoPPP throughout its life cycle, considering the input from external users.</p></sec></sec></sec><sec id="Sec14"><title>Data Records</title><p id="Par48">The CryoPPP dataset<sup><xref ref-type="bibr" rid="CR43">43</xref></sup> consists of manually labelled 9,893 micrographs of 34 diverse, representative cryo-EM datasets of 34 protein complexes selected from EMPIAR. Each EMPIAR dataset identified by a unique EMPIAR ID has about ~300 cryo-EM images in which the coordinates of protein particles were labeled and cross-validated by two experts aided by software tools. The total size of CryoPPP is 2.6 TB. Additional statistical information about CryoPPP can be found in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>.<table-wrap id="Tab1"><label>Table 1</label><caption><p>The statistics of each EMPIAR protein dataset in CryoPPP (* Theoretical weight).</p></caption><table frame="hsides" rules="groups"><thead><tr><th>SN</th><th>EMPAIR ID</th><th>Protein Type</th><th>Size (TB)</th><th>Number of Micrographs</th><th>Image size</th><th>Particle Diameter (px)</th><th>Total Structure Weight (kDa)</th><th>Number of True Protein Particles</th></tr></thead><tbody><tr><td>1</td><td>10389<sup><xref ref-type="bibr" rid="CR56">56</xref></sup></td><td>Metal Binding Protein</td><td>0.224</td><td>300</td><td>(3838, 3710)</td><td>313</td><td>1042.17</td><td>10870</td></tr><tr><td>2</td><td>10081<sup><xref ref-type="bibr" rid="CR53">53</xref></sup></td><td>Transport Protein</td><td>0.052</td><td>300</td><td>(3710, 3838)</td><td>154</td><td>298.57</td><td>39352</td></tr><tr><td>3</td><td>10289<sup><xref ref-type="bibr" rid="CR57">57</xref></sup></td><td>Transport Protein</td><td>0.048</td><td>300</td><td>(3710, 3838)</td><td>162</td><td>361.39</td><td>61517</td></tr><tr><td>4</td><td>11057<sup><xref ref-type="bibr" rid="CR58">58</xref></sup></td><td>Hydrolase</td><td>2.100</td><td>300</td><td>(5760, 4092)</td><td>186</td><td>149.43</td><td>45219</td></tr><tr><td>5</td><td>10444<sup><xref ref-type="bibr" rid="CR59">59</xref></sup></td><td>Membrane Protein</td><td>2.399</td><td>300</td><td>(5760, 4092)</td><td>217</td><td>295.89</td><td>58731</td></tr><tr><td>6</td><td>10576<sup><xref ref-type="bibr" rid="CR60">60</xref></sup></td><td>Nuclear Protein (DNA)</td><td>0.722</td><td>295</td><td>(7420, 7676)</td><td>265</td><td>290.21</td><td>75220</td></tr><tr><td>7</td><td>10816<sup><xref ref-type="bibr" rid="CR61">61</xref></sup></td><td>Transport Protein</td><td>1.500</td><td>300</td><td>(7676, 7420)</td><td>359</td><td>166.62</td><td>45363</td></tr><tr><td>8</td><td>10526<sup><xref ref-type="bibr" rid="CR62">62</xref></sup></td><td>Ribosome (50&#x02009;S)</td><td>0.460</td><td>294</td><td>(7676, 7420)</td><td>482</td><td>1085.81</td><td>3265</td></tr><tr><td>9</td><td>11051<sup><xref ref-type="bibr" rid="CR63">63</xref></sup></td><td>Transcription/DNA/RNA</td><td>2.300</td><td>300</td><td>(3838, 3710)</td><td>214</td><td>357.31</td><td>83227</td></tr><tr><td>10</td><td>10760<sup><xref ref-type="bibr" rid="CR64">64</xref></sup></td><td>Membrane Protein</td><td>0.199</td><td>300</td><td>(3838, 3710)</td><td>106</td><td>321.69</td><td>173664</td></tr><tr><td>11</td><td>11183<sup><xref ref-type="bibr" rid="CR65">65</xref></sup></td><td>Signaling Protein</td><td>0.326</td><td>300</td><td>(5760, 4092)</td><td>159</td><td>139.36</td><td>80014</td></tr><tr><td>12</td><td>10671<sup><xref ref-type="bibr" rid="CR66">66</xref></sup></td><td>Signaling Protein</td><td>1.600</td><td>298</td><td>(5760, 4092)</td><td>133</td><td>77.14</td><td>69012</td></tr><tr><td>13</td><td>10291<sup><xref ref-type="bibr" rid="CR57">57</xref></sup></td><td>Transport Protein</td><td>0.016</td><td>300</td><td>(3710, 3838)</td><td>130</td><td>361.39</td><td>99808</td></tr><tr><td>14</td><td>10669<sup><xref ref-type="bibr" rid="CR67">67</xref></sup></td><td>Proteasome (Plant Protein)</td><td>13.899</td><td>300</td><td>(7676, 7420)</td><td>730</td><td>1681.81</td><td>19660</td></tr><tr><td>15</td><td>10077<sup><xref ref-type="bibr" rid="CR68">68</xref></sup></td><td>Ribosome (70&#x02009;S)</td><td>0.774</td><td>300</td><td>(4096, 4096)</td><td>216</td><td>2198.78</td><td>31919</td></tr><tr><td>16</td><td>10061<sup><xref ref-type="bibr" rid="CR69">69</xref></sup></td><td>Hydrolase (Beta-galactosidase)</td><td>0.319</td><td>300</td><td>(7676, 7420)</td><td>471</td><td>467.06</td><td>35218</td></tr><tr><td>17</td><td>10028<sup><xref ref-type="bibr" rid="CR52">52</xref></sup></td><td>Ribosome (80&#x02009;S)</td><td>1.100</td><td>300</td><td>(4096, 4096)</td><td>224</td><td>2135.89</td><td>26391</td></tr><tr><td>18</td><td>10096<sup><xref ref-type="bibr" rid="CR70">70</xref></sup></td><td>Viral Protein</td><td>1.199</td><td>300</td><td>(3838, 3710)</td><td>84</td><td>150*</td><td>231351</td></tr><tr><td>19</td><td>10737<sup><xref ref-type="bibr" rid="CR48">48</xref></sup></td><td>Membrane Protein (E-coli)</td><td>0.831</td><td>293</td><td>(5760, 4092)</td><td>179</td><td>155.83</td><td>59265</td></tr><tr><td>20</td><td>10387<sup><xref ref-type="bibr" rid="CR71">71</xref></sup></td><td>Viral Protein (DNA)</td><td>0.105</td><td>300</td><td>(3710, 3838)</td><td>213</td><td>185.87</td><td>101778</td></tr><tr><td>21</td><td>10532<sup><xref ref-type="bibr" rid="CR48">48</xref></sup></td><td>Viral Protein</td><td>0.196</td><td>300</td><td>(4096, 4096)</td><td>174</td><td>191.76</td><td>87933</td></tr><tr><td>22</td><td>10240<sup><xref ref-type="bibr" rid="CR72">72</xref></sup></td><td>Lipid Transport Protein</td><td>0.111</td><td>300</td><td>(3838, 3710)</td><td>156</td><td>171.72</td><td>85958</td></tr><tr><td>23</td><td>10005<sup><xref ref-type="bibr" rid="CR73">73</xref></sup></td><td>TRPV1 Transport protein</td><td>0.044</td><td>30</td><td>(3710, 3710)</td><td>142</td><td>272.97</td><td>5374</td></tr><tr><td>24</td><td>10017<sup><xref ref-type="bibr" rid="CR49">49</xref></sup></td><td>&#x003b2; -galactosidase</td><td>0.005</td><td>84</td><td>(4096, 4096)</td><td>108</td><td>450*</td><td>49391</td></tr><tr><td>25</td><td>10075<sup><xref ref-type="bibr" rid="CR74">74</xref></sup></td><td>Bacteriophage MS2</td><td>0.046</td><td>300</td><td>(4096, 4096)</td><td>233</td><td>1000*</td><td>12682</td></tr><tr><td>26</td><td>10184<sup><xref ref-type="bibr" rid="CR75">75</xref></sup></td><td>Aldolase</td><td>0.084</td><td>300</td><td>(3838, 3710)</td><td>118</td><td>150*</td><td>219849</td></tr><tr><td>27</td><td>10059<sup><xref ref-type="bibr" rid="CR75">75</xref></sup></td><td>Transport Protein (TRPV1)</td><td>0.062</td><td>295</td><td>(3838, 3710)</td><td>132</td><td>317.88</td><td>190398</td></tr><tr><td>28</td><td>10406<sup><xref ref-type="bibr" rid="CR55">55</xref></sup></td><td>Ribosome (70&#x02009;S)</td><td>0.141</td><td>300</td><td>(3838, 3710)</td><td>212</td><td>632.89</td><td>24703</td></tr><tr><td>29</td><td>10590<sup><xref ref-type="bibr" rid="CR51">51</xref></sup></td><td>TRPV1 with DkTx and RTX</td><td>0.252</td><td>300</td><td>(3710, 3838)</td><td>158</td><td>1000*</td><td>62493</td></tr><tr><td>30</td><td>10093<sup><xref ref-type="bibr" rid="CR76">76</xref></sup></td><td>Membrane Protein</td><td>0.097</td><td>300</td><td>(3838, 3710)</td><td>172</td><td>779.4</td><td>56394</td></tr><tr><td>31</td><td>10345<sup><xref ref-type="bibr" rid="CR54">54</xref></sup></td><td>Signaling Protein</td><td>0.085</td><td>300</td><td>(3838, 3710)</td><td>149</td><td>244.68</td><td>15894</td></tr><tr><td>32</td><td>11056<sup><xref ref-type="bibr" rid="CR77">77</xref></sup></td><td>Transport Protein</td><td>0.164</td><td>361</td><td>(5760, 4092)</td><td>164</td><td>88.94</td><td>125908</td></tr><tr><td>33</td><td>10852<sup><xref ref-type="bibr" rid="CR78">78</xref></sup></td><td>Signaling Protein</td><td>0.227</td><td>343</td><td>(5760, 4092)</td><td>123</td><td>157.81</td><td>310291</td></tr><tr><td>34</td><td>10947<sup><xref ref-type="bibr" rid="CR79">79</xref></sup></td><td>Viral Protein</td><td>0.048</td><td>400</td><td>(4096, 4096)</td><td>240</td><td>443.92</td><td>106393</td></tr></tbody></table></table-wrap></p><p id="Par49">The full dataset is available at <ext-link ext-link-type="uri" xlink:href="https://github.com/BioinfoMachineLearning/cryoppp">https://github.com/BioinfoMachineLearning/cryoppp</ext-link>. For researchers who have limited disk space, a much smaller light version of CryoPPP, called CryoPPP_Lite, can also be downloaded from the website. CryoPPP_Lite includes the micrograph files in the 8-bit JPG format and the particle ground truth files that only need 121 GB disk space in total, which is easier to store and transfer.</p><p id="Par50">Each data folder (named by its corresponding EMPIAR ID) includes the following information: original micrographs (either motion-corrected or not), gain motion correction file, new motion-corrected micrographs (if original micrographs are not motion-corrected), ground truth labels (manually picked particles), and particles stack. The directory structure of each data entry is illustrated in Fig.&#x000a0;<xref rid="Fig10" ref-type="fig">10</xref>. The data in each directory is described as follows. It is worth noting that if the original micrographs were not motion-corrected, we applied the motion correction to them to create their motion-corrected counterparts.<fig id="Fig10"><label>Fig. 10</label><caption><p>The directory structure of each expert-labelled data entry of CryoPPP. The directory contains micrographs, motion correction files, particle stacks, and ground truth labels (manually picked particles). The blocks with numbers on the left represent corresponding EMPIAR IDs.</p></caption><graphic xlink:href="41597_2023_2280_Fig10_HTML" id="d32e2227"/></fig></p><sec id="Sec15"><title>Raw micrographs</title><p id="Par51">These are the two-dimensional projections of the protein particles in different orientations stored in different image formats (MRC, TIFF, EER, TIF, etc.). They can be considered as the photos taken by cryo-EM microscope. Original micrographs are from EMPIAR and can be either motion corrected or not. If an entry has a &#x02018;<italic>gain&#x02019;</italic> folder, it includes both raw non-motion-corrected micrographs and their motion-corrected counterparts created by us. Users are supposed to use the motion corrected micrographs as input for machine learning tasks. The scripts for the motion correction are available at CryoPPP&#x02019;s GitHub website.</p></sec><sec id="Sec16"><title>Motion correction (gain files)</title><p id="Par52">It contains motion correction files (if motion in original micrographs not corrected before) stored in different formats like dm4 and mrc. It is used to correct both global motion (stage drift) and local motion (beam-induced anisotropic sample deformation) that occur when specimens (protein particles) are exposed to the electron beam during imaging. Correcting the motion enables the high-resolution reconstruction from the data.</p></sec><sec id="Sec17"><title>Particle stack</title><p id="Par53">Particle stack comprises of the mrc files (with names corresponding to individual micrographs&#x02019; filenames) of manually picked protein particles (ground truth labels). These are three-dimensional grids of voxels with values corresponding to electron density (i.e., a stack of 2D images). To browse and examine this file, utilize EMAN2<sup><xref ref-type="bibr" rid="CR14">14</xref></sup>, UCSF Chimera<sup><xref ref-type="bibr" rid="CR50">50</xref></sup>, or UCSF ChimeraX<sup><xref ref-type="bibr" rid="CR51">51</xref></sup>.</p></sec><sec id="Sec18"><title>Ground truth labels</title><p id="Par54">Ground truth data contain the star and CSV files for both all true particles (positives) and some typical false positives (e.g., ice contaminations, aggregates, and carbon edges). The positive star (and corresponding CSV) files are the ground truth position of the picked particles combined in a single file for all ~300 micrographs per EMPIAR ID. While the negative star file consists position of the false positive particles. These star files contain information like X-coordinate, Y-coordinate, Angle-Psi, Origin X (Ang), Origin Y (Ang), Defocus U, Defocus V, Defocus Angle, Phase Shift, CTF B Factor, Optics Group, and Class Number of the particles.</p><p id="Par55">To ensure the reproducibility of the dataset, we have included the intermediate data (Fig.&#x000a0;<xref rid="Fig10" ref-type="fig">10</xref>, <italic>ground_truth &#x0003e;&#x0003e; intermediate_data</italic>) along with the intermediate metadata (presented in Supplementary Table&#x000a0;<xref rid="MOESM3" ref-type="media">S2</xref>). The intermediate data comprise the star files of manually expert-picked particles, which were utilized to construct templates for further particle selection.</p><p id="Par56">Besides, there is a subdirectory called <italic>particle_coordinates</italic> inside <italic>ground_truth</italic>, which contains csv files, with same name as raw micrographs. This sub-directory comprises individual protein particle&#x02019;s X-Coordinate, Y-Coordinate along with their diameter and other physico-chemical information.</p></sec></sec><sec id="Sec19"><title>Technical Validation</title><p id="Par57">To ensure that the dataset is of high quality, we applied numerous validations and statistical analyses throughout the data curation process.</p><sec id="Sec20"><title>Quality of data</title><p id="Par58">As noted in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>, we ensure that the dataset exclusively contains micrographs obtained using the Cryo-EM technique. Only the EMPIAR IDs with resolution better than 4&#x02009;&#x000c5; are chosen for creating refined protein metadata and ground truth labels of protein particles. The detailed quality control procedures are described as follows.</p></sec><sec id="Sec21"><title>Distribution of data</title><sec id="Sec22"><title>Diverse protein types</title><p id="Par59">To be inclusive and ensure unbiased data generation, we selected the cryo-EM data of 34 different, diverse protein types (e.g., membrane, transport, metal binding, signalling, nuclear, viral proteins) to manually label protein particles, which can enable machine learning methods trained on them to work for many different proteins in the real-world. We selected the datasets covering different particle size, distribution density, noise level, ice and carbon areas, and particle shape as they are influential in particle picking.</p></sec><sec id="Sec23"><title>Diverse micrographs within the same protein type</title><p id="Par60">The variance in micrographs&#x02019; defocus values within a EMPIAR dataset is not accounted for by majority of the particle picking methods. This defocus variation causes the same particles to appear differently, altering the noise statistics of each micrograph. This makes it challenging to create thresholds to select high quality particles. Figure&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref> shows an example how different defocus values impact the appearance and quality of Cryo-EM images in the same EMPIAR dataset. Therefore, during manually picking the particles, we included a wide variety of defocus levels and CTF fit.</p><p id="Par61">We recorded the correlation between defocus levels and the pick scores/the power scores (shown in Fig.&#x000a0;<xref rid="Fig11" ref-type="fig">11</xref> for EMPIAR<bold>-</bold>10590<sup><xref ref-type="bibr" rid="CR51">51</xref></sup>) to assess the shape and density of a particle candidate independently. After calibration, the scores of each particle are recorded relative to the calibration line, and these values are used to define thresholds on the parameters.<fig id="Fig11"><label>Fig. 11</label><caption><p>NCC and Power calibration plots for EMPIAR- 10590 (Endogenous Human BAF Complex). (<bold>A</bold>) Calibrating Median NCC scores vs defocus. (<bold>B</bold>) Calibrating Power scores vs Defocus. There is a strong trend that higher defocus correlates with higher NCC scores and same with Power score.</p></caption><graphic xlink:href="41597_2023_2280_Fig11_HTML" id="d32e2337"/></fig></p></sec></sec><sec id="Sec24"><title>Reliability of ground truth annotations</title><sec id="Sec25"><title>Legitimacy of importing micrographs and motion correction data</title><p id="Par62">All the input parameters used to prepare for loading micrographs into the CryoSPARC system were gathered from the appropriate literature. We adhered to the standards in the publications including data acquisition and imaging settings such as the microscope used, defocus range, spherical aberration, pixel spacing, acceleration voltage, electron dose and the correct usage of motion correction. Based on the microscope settings during the imaging process, we applied appropriate geometrical transformations. The defect files and the motion-correction files were flipped left-to-right or top-to-bottom and also rotated by specific degrees in clockwise/anti-clockwise direction as required. All these factors were thoroughly investigated and used during the data loading process in CryoSPARC.</p></sec><sec id="Sec26"><title>Inspection of picked protein particles</title><p id="Par63">The picked particles were inspected using a 2D colored histogram, as shown in Fig.&#x000a0;<xref rid="Fig12" ref-type="fig">12</xref>. A particle of interest would have an intermediate local power score and a high template correlation (indicating its shape closely matches its template). Low local power scores indicate empty ice patches, even though it might resemble the template. Additionally, very high local power scores indicate carbon edges, aggregates, contaminants, and other objects with excessive densities that resemble particles.<fig id="Fig12"><label>Fig. 12</label><caption><p>Particle inspection and filtration by adjusting normalized cross correlation (NCC) score (X axis) and local power (Y axis) for EMPIAR 10017. (<bold>A</bold>) Intermediate picked particles (green circles) from template-based picking step. The false positives, represented by red crossed particles inside the yellow box, are eliminated in step B. (<bold>B</bold>) Selected high quality true protein particles through adjustment of NCC and power score values.</p></caption><graphic xlink:href="41597_2023_2280_Fig12_HTML" id="d32e2368"/></fig></p><p id="Par64">As shown in Fig.&#x000a0;<xref rid="Fig12" ref-type="fig">12</xref> (<bold>B,</bold>
<bold>bottom</bold>), we interactively specified the upper and lower thresholds for both the Power score and NCC score for each dataset improving the accuracy in the manual particle picking.</p></sec><sec id="Sec27"><title>Cross-validation by two human experts</title><p id="Par65">The results of the particles picked by the two Cryo-EM experts were compared to each other to make sure they are consistent. For example, two EMPIAR IDs: EMPIAR-10028<sup><xref ref-type="bibr" rid="CR52">52</xref></sup> and EMPIAR-10081<sup><xref ref-type="bibr" rid="CR53">53</xref></sup> with 300 micrographs (total 600 Cryo-EM micrographs) were used in cross-validation. The results of the 2D classes were compared based on total number of particles in each class, relative resolution of particles in the class, and distinct views of the structure of particles. Similar 2D classes, as shown in Fig.&#x000a0;<xref rid="Fig13" ref-type="fig">13</xref>, achieved by two independent Cryo-EM specialists validate the accuracy of the manually labelled particles.<fig id="Fig13"><label>Fig. 13</label><caption><p>2D classification results of the picked particles of EMPIAR ID 10028 and 10081 (<bold>A</bold>) Results from Cryo-EM expert-1, (<bold>B</bold>) Results from Cryo-EM expert-2.</p></caption><graphic xlink:href="41597_2023_2280_Fig13_HTML" id="d32e2410"/></fig></p></sec><sec id="Sec28"><title>Comparison with existing state-of-the-art AI methods of particle picking</title><p id="Par66">We conducted a comparison between the 3D density maps reconstructed from the particles picked by us and by two AI methods, namely Topaz and crYOLO. We utilized them to predict particles for two datasets, EMPIAR 10081 and EMPIAR 10345, each containing 300 micrographs. Subsequently, the ab-initio density map reconstruction and homo-refinement were performed using the generated star files containing the picked particles. To avoid any bias in the comparison, we repeated the ab-initio 3D reconstruction trials with three different random seeds for each method. The GSFSC resolution results for the three trails for each method are presented in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>. CryoPPP outperforms both Topaz and crYOLO.<table-wrap id="Tab2"><label>Table 2</label><caption><p>Comparison of CryoPPP, Topaz, and crYOLO on two EMPIAR DATASETS.</p></caption><table frame="hsides" rules="groups"><thead><tr><th>EMPIAR ID</th><th>Metric</th><th colspan="3">Topaz</th><th colspan="5">crYOLO</th><th colspan="5">CryoPPP</th></tr></thead><tbody><tr><td rowspan="3">10081</td><td>Number of Particles Picked</td><td colspan="3">135,978</td><td colspan="5">17,550</td><td colspan="5">37,387</td></tr><tr><td rowspan="2">GSFSC Resolution (&#x000c5;)</td><td>Trial 1</td><td>Trial 2</td><td>Trial 3</td><td colspan="2">Trial 1</td><td colspan="2">Trial 2</td><td>Trial 3</td><td>Trial 1</td><td colspan="2">Trial 2</td><td colspan="2">Trial 3</td></tr><tr><td>6.90</td><td><bold>6.75</bold></td><td>6.96</td><td colspan="2">9.56</td><td colspan="2">9.61</td><td><bold>9.33</bold></td><td><bold>4</bold>.<bold>59</bold></td><td colspan="2">4.67</td><td colspan="2">4.63</td></tr><tr><td rowspan="3">10345</td><td>Number of Particles Picked</td><td colspan="3">40,324</td><td colspan="5">4,095</td><td colspan="5">15,894</td></tr><tr><td rowspan="2">GSFSC Resolution (&#x000c5;)</td><td>Trial 1</td><td>Trial 2</td><td>Trial 3</td><td>Trial 1</td><td colspan="2">Trial 2</td><td colspan="2">Trial 3</td><td colspan="2">Trial 1</td><td colspan="2">Trial 2</td><td>Trial 3</td></tr><tr><td>3.92</td><td><bold>3.85</bold></td><td>3.89</td><td>10.3</td><td colspan="2"><bold>9.29</bold></td><td colspan="2">10.26</td><td colspan="2"><bold>3</bold>.<bold>76</bold></td><td colspan="2">3.78</td><td>3.81</td></tr></tbody></table><table-wrap-foot><p>Bold font denotes the best resolution of the density map reconstructed from picked particles in the three trials.</p></table-wrap-foot></table-wrap></p><p id="Par67">For EMPIAR 10081, Topaz picked around 100,000 more particles than we. However, the best resolution of the density map reconstructed from CryoPPP picked particles in the three trials is 4.59&#x02009;&#x000c5;, substantially better than 6.75&#x02009;&#x000c5; of Topaz. This suggests that Topaz may have selected a substantial number of false positives (e.g., ice contaminations) or may have selected the same protein particles multiple times with slightly different center positions. CrYOLO, on the other hand, picked a significantly lower number of protein particles than us, which resulted in the worst resolution (9.33&#x02009;&#x000c5;) among the three. Similar results were obtained on EMPIAR 10345. The results confirm that the labeled particles in CryoPPP are of high quality and can be used to train/improve AI particle picking methods.</p></sec></sec><sec id="Sec29"><title>Cross validation with gold standard particles picked by the authors</title><p id="Par68">Gold standard particles are those particles that were originally picked by the Cryo-EM experts who generated the cryo-EM data. There are only a few EMPIAIR IDs deposited in EMPIAR that have both the micrographs and the gold standard particles. To validate the accuracy of our picked particles, we compared our results with the already-existing gold standard particles that are publicly available through the EMPIAR website. We carried out 2D and 3D validation for EMPIAR-10345<sup><xref ref-type="bibr" rid="CR54">54</xref></sup> and EMPIAR-10406<sup><xref ref-type="bibr" rid="CR55">55</xref></sup> to validate our particle labelling process as follows.</p><sec id="Sec30"><title>2D particle class validation with gold standard</title><p id="Par69">In order to get the gold standard 2D particles of the dataset, we downloaded the particle stack image files (.mrc) and.star file with the attributes of picked particles from EMPIAR. We used the particle stack and the star files to create the 2D classification results using CryoSPARC. Eventually, we compared our 2D class results with the gold standard. We performed the comparison based on the total number of classes, total number of picked particles, resolution, and visual orientation of the protein particle for each EMPIAR ID. Our results and the gold standard results exhibit strong correlations. It is worth noting that a high number of particles alone does not necessarily yield high resolution. Selecting a decent number of high-quality particles spanning a wide angular distribution is important for generating high 2D and 3D resolution.</p><p id="Par70">Figure&#x000a0;<xref rid="Fig14" ref-type="fig">14</xref> shows the visual illustration 2D classification results for EMPIAR ID 10345 and EMPIAR ID 10406 published by the authors of the cryo-EM data and generated by us. They are consistent.<fig id="Fig14"><label>Fig. 14</label><caption><p>2D classification comparison for EMPIAR- 10345 and EMPIAR-10406 (<bold>A</bold>) 2D classification published in EMPIAR. (<bold>B</bold>) 2D classification results of the particles by CryoPPP.</p></caption><graphic xlink:href="41597_2023_2280_Fig14_HTML" id="d32e2665"/></fig></p><p id="Par71">Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref> compares 2D classification results generated by original authors and by us. In both cases, (Fig.&#x000a0;<xref rid="Fig14" ref-type="fig">14A,B</xref>) the same 300 micrographs were used for comparison. On EMPIAR ID 10345, CryoPPP&#x02019;s results have substantially better resolution than the authors&#x02019; results for both N&#x02009;=&#x02009;50 and N&#x02009;=&#x02009;10 classes. On EMPIAR-10406, CryoPPP&#x02019;s results have better resolution for N&#x02009;=&#x02009;50 particle classes and comparable resolution for N&#x02009;=&#x02009;10 particle classes.<table-wrap id="Tab3"><label>Table 3</label><caption><p>2D classification result comparison for EMPIAR-10345 and EMPIAR-10406.</p></caption><table frame="hsides" rules="groups"><thead><tr><th>EMPIAR 10345</th><th>2D Particle Class Statistics (EMPIAR)</th><th>2D Particle Class Statistics (CryoPPP)</th></tr></thead><tbody><tr><td>Number of Picked Particles</td><td>17,838</td><td>15,894</td></tr><tr><td>Weighted Average Resolution of 2D classes (N&#x02009;=&#x02009;50)</td><td>18.63&#x02009;&#x000c5;</td><td>10.25&#x02009;&#x000c5;</td></tr><tr><td>Weighted Average Resolution of 2D classes (N&#x02009;=&#x02009;10)</td><td>20.52&#x02009;&#x000c5;</td><td>10.53&#x02009;&#x000c5;</td></tr><tr><td><bold>EMPAIR 10406</bold></td><td colspan="2"/></tr><tr><td>Number of Picked Particles</td><td>23, 450</td><td>24,703</td></tr><tr><td>Weighted Average Resolution of 2D classes (N&#x02009;=&#x02009;50)</td><td>8.47&#x02009;&#x000c5;</td><td>7.98&#x02009;&#x000c5;</td></tr><tr><td>Weighted Average Resolution of 2D classes (N&#x02009;=&#x02009;10)</td><td>15.53&#x02009;&#x000c5;</td><td>15.97&#x02009;&#x000c5;</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec31"><title>3D density map validation with gold standard</title><p id="Par72">The ab initio reconstruction of the 3D density map for EMPIAR 10345 and EMPIAR 10406 was carried out using the gold standard particles picked by the original authors and the ones picked by us, respectively. The coordinates of the particle picked by the original authors were downloaded from the EMPIAR website. Three different random seeds were used in the ab initio reconstruction to avoid random bias. The results of the 3D density maps, resolution, and direction of distribution obtained from the two kinds of particles are compared in Figs.&#x000a0;<xref rid="Fig15" ref-type="fig">15</xref>, <xref rid="Fig16" ref-type="fig">16</xref>.<fig id="Fig15"><label>Fig. 15</label><caption><p>The comparison of 3D density maps, resolution, and direction distribution on EMPIAR- 10345. (<bold>A</bold>) results from the particles published in EMPIAR. (<bold>B</bold>) results generated from the particles in CryoPPP.</p></caption><graphic xlink:href="41597_2023_2280_Fig15_HTML" id="d32e2788"/></fig><fig id="Fig16"><label>Fig. 16</label><caption><p>The comparison of 3D density maps, resolution, and direction distribution on EMPIAR- 10406. (<bold>A</bold>) results published in EMPIAR. (<bold>B</bold>) results generated from the particles in CryoPPP.</p></caption><graphic xlink:href="41597_2023_2280_Fig16_HTML" id="d32e2803"/></fig></p><p id="Par73">The detailed comparison results are reported in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>. The &#x02018;loose mask&#x02019; curve in the Fourier Shell Correlation (FSC) plots uses an automatically produced mask with a 15&#x02009;&#x000c5; falloff. The &#x02018;tight mask&#x02019; curve employs an auto-generated mask with a falloff of 6&#x02009;&#x000c5; for all FSC plots.<table-wrap id="Tab4"><label>Table 4</label><caption><p>The 3D density map result comparison on EMPIAR 10345 and EMPIAR 10406.</p></caption><table frame="hsides" rules="groups"><thead><tr><th>EMPIAR 10345</th><th colspan="3">3D Map Statistics (EMPIAR)</th><th colspan="3">3D Map Statistics (CryoPPP)</th></tr></thead><tbody><tr><td>Number of Picked Particles</td><td colspan="3">17,838</td><td colspan="3">15,894</td></tr><tr><td rowspan="2">GSFSC Resolution (&#x000c5;)</td><td>Trial 1</td><td>Trial 2</td><td>Trial 3</td><td>Trial 1</td><td>Trial 2</td><td>Trial 3</td></tr><tr><td><bold>4</bold>.<bold>86</bold></td><td>4.94</td><td>4.88</td><td><bold>3</bold>.<bold>76</bold></td><td>3.78</td><td>3.81</td></tr><tr><td>No Mask Resolution (&#x000c5;)</td><td><bold>10</bold></td><td>10</td><td>10</td><td><bold>6</bold>.<bold>6</bold></td><td>6.5</td><td>6.6</td></tr><tr><td>Loose Mask Resolution (&#x000c5;)</td><td><bold>7</bold>.<bold>3</bold></td><td>7.3</td><td>7.3</td><td><bold>4</bold>.<bold>9</bold></td><td>4.9</td><td>4.9</td></tr><tr><td>Tight Mask Resolution (&#x000c5;)</td><td><bold>4</bold>.<bold>9</bold></td><td>4.9</td><td>4.9</td><td><bold>3</bold>.<bold>9</bold></td><td>3.8</td><td>3.8</td></tr><tr><td>Corrected Mask Resolution (&#x000c5;)</td><td><bold>4</bold>.<bold>9</bold></td><td>4.9</td><td>4.9</td><td><bold>3</bold>.<bold>8</bold></td><td>3.9</td><td>3.9</td></tr><tr><td colspan="7"><bold>EMPAIR 10406</bold></td></tr><tr><td>Number of Picked Particles</td><td colspan="3">23, 450</td><td colspan="3">24,703</td></tr><tr><td rowspan="2">GSFSC Resolution (&#x000c5;)</td><td>Trial 1</td><td>Trial 2</td><td>Trial 3</td><td>Trial 1</td><td>Trial 2</td><td>Trial 3</td></tr><tr><td><bold>2</bold>.<bold>85</bold></td><td>2.89</td><td>2.86</td><td><bold>2</bold>.<bold>89</bold></td><td>2.9</td><td>2.93</td></tr><tr><td>No Mask Resolution (&#x000c5;)</td><td><bold>4</bold>.<bold>3</bold></td><td>4.3</td><td>4.3</td><td><bold>4</bold>.<bold>1</bold></td><td>4.1</td><td>4.2</td></tr><tr><td>Loose Mask Resolution (&#x000c5;)</td><td><bold>3</bold>.<bold>1</bold></td><td>3.2</td><td>3.1</td><td><bold>3</bold>.<bold>1</bold></td><td>3.1</td><td>3.2</td></tr><tr><td>Tight Mask Resolution (&#x000c5;)</td><td><bold>2</bold>.<bold>8</bold></td><td>2.9</td><td>2.9</td><td><bold>2</bold>.<bold>9</bold></td><td>2.9</td><td>2.9</td></tr><tr><td>Corrected Mask Resolution (&#x000c5;)</td><td><bold>2</bold>.<bold>9</bold></td><td>2.9</td><td>2.9</td><td><bold>2</bold>.<bold>9</bold></td><td>2.9</td><td>3.0</td></tr></tbody></table></table-wrap></p><p id="Par74">It is seen that CryoPPP outperforms the gold standard particles in terms of all the metrics on EMPIAR 10345 and achieves very similar results on EMPIAR 10406.</p><p id="Par75">These rigorous validations with the gold standard picked particles and the comparison with the existing state-of-the-art AI methods clearly demonstrate the high quality of the data in CryoPPP, which enable it to serve as either the benchmark dataset for compare AI and classical methods of particle picking or the training and test dataset to develop new methods in the field.</p></sec></sec></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec32"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="41597_2023_2280_MOESM1_ESM.docx"><caption><p>Figure S1</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="41597_2023_2280_MOESM2_ESM.xlsx"><caption><p>Table S1</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="41597_2023_2280_MOESM3_ESM.xlsx"><caption><p>Table S2</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="41597_2023_2280_MOESM4_ESM.xlsx"><caption><p>Table S3</p></caption></media></supplementary-material>
</p></sec></sec></body><back><fn-group><fn><p><bold>Publisher&#x02019;s note</bold> Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn><fn><p>These authors contributed equally: Ashwin Dhakal, Rajan Gyawali.</p></fn></fn-group><sec><title>Supplementary information</title><p>The online version contains supplementary material available at 10.1038/s41597-023-02280-2.</p></sec><ack><title>Acknowledgements</title><p>We thank the EMPIAR team for hosting the Cryo-EM data archive and the researchers who deposited theircryo-EM images into EMPIAR for public use. We are grateful to Dr. Filiz Bunyak and Dr. Michael Chapmanfor their insights on particle picking. Special thanks go to Ali Punjani and his team for developing CryoSPARC,which was extensively used in the curation of CryoPPP. This work was supported by National Institutes of Health(NIH) grant (grant #: R01GM146340) to JC and LW.</p></ack><notes notes-type="author-contribution"><title>Author contributions</title><p>J.C. conceived and conceptualized this research; J.C. and L.W. provided guidance on the research; A.D., R.G. and J.C. designed the methodology and experiment; A.D. and R.G. wrote the scripts and codes to preprocess dataset; A.D. and R.G. curated data; L.W. and J.C. conceptualized data validation; A.D. and R.G. drafted the manuscript; J.C. and L.W. revised manuscript; and all authors discussed the results, analyzed the data, and contributed to the final manuscript.</p></notes><notes notes-type="data-availability"><title>Code availability</title><p>The data analysis methods, software and associated parameters used in this study are described in the section of Methods. All the scripts associated with various steps of data curation are available at the GitHub repository: <ext-link ext-link-type="uri" xlink:href="https://github.com/BioinfoMachineLearning/cryoppp">https://github.com/BioinfoMachineLearning/cryoppp</ext-link>, which includes the instructions about how to download the data.</p></notes><notes id="FPar1" notes-type="COI-statement"><title>Competing interests</title><p id="Par76">The authors declare no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Glaeser</surname><given-names>RM</given-names></name></person-group><article-title>Stroboscopic imaging of macromolecular complexes</article-title><source>Nat. Methods</source><year>2013</year><volume>10</volume><fpage>475</fpage><lpage>476</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2486</pub-id><?supplied-pmid 23722205?><pub-id pub-id-type="pmid">23722205</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><mixed-citation publication-type="other">Pakhrin, S. C., Shrestha, B., Adhikari, B. &#x00026; Kc, D. B. Deep learning-based advances in protein structure prediction. <italic>Int. J. Mol. Sci</italic>. <bold>22</bold> (2021).</mixed-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Boadu</surname><given-names>F</given-names></name><name><surname>Cao</surname><given-names>H</given-names></name><name><surname>Cheng</surname><given-names>J</given-names></name></person-group><article-title>Combining protein sequences and structures with transformers and equivariant graph neural networks to predict protein function</article-title><source>bioRxiv</source><year>2023</year><pub-id pub-id-type="doi">10.1093/bioinformatics/xxxxx</pub-id><?supplied-pmid 36711471?><pub-id pub-id-type="pmid">36711471</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><mixed-citation publication-type="other">Dhakal, A., McKay, C., Tanner, J. J. &#x00026; Cheng, J. Artificial intelligence in the prediction of protein-ligand interactions: recent advances and future directions. <italic>Briefings in Bioinformatics</italic> 23 (2022).</mixed-citation></ref><ref id="CR5"><label>5.</label><mixed-citation publication-type="other">Giri, N. &#x00026; Cheng, J. Improving Protein&#x02013;Ligand Interaction Modeling with cryo-EM Data, Templates, and Deep Learning in 2021 Ligand Model Challenge. <italic>Biomolecules</italic><bold>13</bold> (2023).</mixed-citation></ref><ref id="CR6"><label>6.</label><mixed-citation publication-type="other">Mahmud, S., Soltanikazemi, E., Boadu, F., Dhakal, A. &#x00026; Cheng, J. Deep Learning Prediction of Severe Health Risks for Pediatric COVID-19 Patients with a Large Feature Set in 2021 BARDA Data Challenge. <italic>ArXiv</italic> (2022).</mixed-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grassucci</surname><given-names>RA</given-names></name><name><surname>Taylor</surname><given-names>DJ</given-names></name><name><surname>Frank</surname><given-names>J</given-names></name></person-group><article-title>Preparation of macromolecular complexes for cryo-electron microscopy</article-title><source>Nat. Protoc.</source><year>2007</year><volume>2</volume><fpage>3239</fpage><lpage>3246</lpage><pub-id pub-id-type="doi">10.1038/nprot.2007.452</pub-id><?supplied-pmid 18079724?><pub-id pub-id-type="pmid">18079724</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><mixed-citation publication-type="other">Shen, P., Iwasa, J. &#x00026; Brasch, J. <italic>Chapter 2: Cryo-EM grid preparation.</italic><ext-link ext-link-type="uri" xlink:href="https://cryoem101.org/chapter-2/">https://cryoem101.org/chapter-2/</ext-link> (2022).</mixed-citation></ref><ref id="CR9"><label>9.</label><mixed-citation publication-type="other">Shen, P., Iwasa, J. &#x00026; Brasch, J. <italic>Chapter 3: Grid Screening and Evaluation.</italic><ext-link ext-link-type="uri" xlink:href="https://cryoem101.org/chapter-3/">https://cryoem101.org/chapter-3/</ext-link> (2022).</mixed-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Carragher</surname><given-names>B</given-names></name><etal/></person-group><article-title>Current outcomes when optimizing &#x02018;standard&#x02019; sample preparation for single-particle cryo-EM</article-title><source>J. Microsc.</source><year>2019</year><volume>276</volume><fpage>39</fpage><lpage>45</lpage><pub-id pub-id-type="doi">10.1111/jmi.12834</pub-id><?supplied-pmid 31553060?><pub-id pub-id-type="pmid">31553060</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>S</given-names></name><etal/></person-group><article-title>High-resolution noise substitution to measure overfitting and validate resolution in 3D structure determination by single particle electron cryomicroscopy</article-title><source>Ultramicroscopy</source><year>2013</year><volume>135</volume><fpage>24</fpage><lpage>35</lpage><pub-id pub-id-type="doi">10.1016/j.ultramic.2013.06.004</pub-id><?supplied-pmid 23872039?><pub-id pub-id-type="pmid">23872039</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Downing</surname><given-names>KH</given-names></name><name><surname>Hendrickson</surname><given-names>FM</given-names></name></person-group><article-title>Performance of a 2k CCD camera designed for electron crystallography at 400&#x02009;kV</article-title><source>Ultramicroscopy</source><year>1999</year><volume>75</volume><fpage>215</fpage><lpage>233</lpage><pub-id pub-id-type="doi">10.1016/S0304-3991(98)00065-5</pub-id><?supplied-pmid 9919710?><pub-id pub-id-type="pmid">9919710</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>De Ruijter</surname><given-names>WJ</given-names></name></person-group><article-title>Imaging properties and applications of slow-scan charge-coupled device cameras suitable for electron microscopy</article-title><source>Micron</source><year>1995</year><volume>26</volume><fpage>247</fpage><lpage>275</lpage><pub-id pub-id-type="doi">10.1016/0968-4328(95)00054-8</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tang</surname><given-names>G</given-names></name><etal/></person-group><article-title>EMAN2: An extensible image processing suite for electron microscopy</article-title><source>J. Struct. Biol.</source><year>2007</year><volume>157</volume><fpage>38</fpage><lpage>46</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2006.05.009</pub-id><?supplied-pmid 16859925?><pub-id pub-id-type="pmid">16859925</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Scheres</surname><given-names>SHW</given-names></name></person-group><article-title>RELION: Implementation of a Bayesian approach to cryo-EM structure determination</article-title><source>J. Struct. Biol.</source><year>2012</year><volume>180</volume><fpage>519</fpage><lpage>530</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2012.09.006</pub-id><?supplied-pmid 23000701?><pub-id pub-id-type="pmid">23000701</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Punjani</surname><given-names>A</given-names></name><name><surname>Rubinstein</surname><given-names>JL</given-names></name><name><surname>Fleet</surname><given-names>DJ</given-names></name><name><surname>Brubaker</surname><given-names>MA</given-names></name></person-group><article-title>CryoSPARC: Algorithms for rapid unsupervised cryo-EM structure determination</article-title><source>Nat. Methods</source><year>2017</year><volume>14</volume><fpage>290</fpage><lpage>296</lpage><pub-id pub-id-type="doi">10.1038/nmeth.4169</pub-id><?supplied-pmid 28165473?><pub-id pub-id-type="pmid">28165473</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wagner</surname><given-names>T</given-names></name></person-group><source>Cinderella.</source><year>2019</year><pub-id pub-id-type="doi">10.5281/zenodo.3672421</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Giri</surname><given-names>N</given-names></name><name><surname>Roy</surname><given-names>RS</given-names></name><name><surname>Cheng</surname><given-names>J</given-names></name></person-group><article-title>Deep learning for reconstructing protein structures from cryo-EM density maps: recent advances and future directions</article-title><source>Curr. Opin. Struct. Biol.</source><year>2022</year><volume>79</volume><fpage>102536</fpage><pub-id pub-id-type="doi">10.1016/j.sbi.2023.102536</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Marabini</surname><given-names>R</given-names></name><etal/></person-group><article-title>Xmipp: An image processing package for electron microscopy</article-title><source>J. Struct. Biol.</source><year>1996</year><volume>116</volume><fpage>237</fpage><lpage>240</lpage><pub-id pub-id-type="doi">10.1006/jsbi.1996.0036</pub-id><?supplied-pmid 8812978?><pub-id pub-id-type="pmid">8812978</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Heimowitz</surname><given-names>A</given-names></name><name><surname>And&#x000e9;n</surname><given-names>J</given-names></name><name><surname>Singer</surname><given-names>A</given-names></name></person-group><article-title>APPLE picker: Automatic particle picking, a low-effort cryo-EM framework</article-title><source>J. Struct. Biol.</source><year>2018</year><volume>204</volume><fpage>215</fpage><lpage>227</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2018.08.012</pub-id><?supplied-pmid 30134153?><pub-id pub-id-type="pmid">30134153</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>F</given-names></name><etal/></person-group><article-title>DeepPicker: A deep learning approach for fully automated particle picking in cryo-EM</article-title><source>J. Struct. Biol.</source><year>2016</year><volume>195</volume><fpage>325</fpage><lpage>336</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2016.07.006</pub-id><?supplied-pmid 27424268?><pub-id pub-id-type="pmid">27424268</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>Y</given-names></name><name><surname>Ouyang</surname><given-names>Q</given-names></name><name><surname>Mao</surname><given-names>Y</given-names></name></person-group><article-title>A deep convolutional neural network approach to single-particle recognition in cryo-electron microscopy</article-title><source>BMC Bioinformatics</source><year>2017</year><volume>18</volume><fpage>1</fpage><lpage>10</lpage><pub-id pub-id-type="doi">10.1186/s12859-017-1757-y</pub-id><pub-id pub-id-type="pmid">28049414</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><mixed-citation publication-type="other">Xiao, Y. &#x00026; Yang, G. A fast method for particle picking in cryo-electron micrographs based on fast R-CNN. <italic>AIP Conf. Proc</italic>. <bold>1836</bold> (2017).</mixed-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wagner</surname><given-names>T</given-names></name><etal/></person-group><article-title>SPHIRE-crYOLO is a fast and accurate fully automated particle picker for cryo-EM</article-title><source>Commun. Biol.</source><year>2019</year><volume>2</volume><fpage>1</fpage><lpage>13</lpage><pub-id pub-id-type="doi">10.1038/s42003-019-0437-z</pub-id><pub-id pub-id-type="pmid">30740537</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>J</given-names></name><etal/></person-group><article-title>PIXER: An automated particle-selection method based on segmentation using a deep neural network</article-title><source>BMC Bioinformatics</source><year>2019</year><volume>20</volume><fpage>1</fpage><lpage>14</lpage><pub-id pub-id-type="pmid">30606105</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yao</surname><given-names>R</given-names></name><name><surname>Qian</surname><given-names>J</given-names></name><name><surname>Huang</surname><given-names>Q</given-names></name></person-group><article-title>Deep-learning with synthetic data enables automated picking of cryo-EM particle images of biological macromolecules</article-title><source>Bioinformatics</source><year>2020</year><volume>36</volume><fpage>1252</fpage><lpage>1259</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btz728</pub-id><?supplied-pmid 31584618?><pub-id pub-id-type="pmid">31584618</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tegunov</surname><given-names>D</given-names></name><name><surname>Cramer</surname><given-names>P</given-names></name></person-group><article-title>Real-time cryo-electron microscopy data preprocessing with Warp</article-title><source>Nat. Methods</source><year>2019</year><volume>16</volume><fpage>1146</fpage><lpage>1152</lpage><pub-id pub-id-type="doi">10.1038/s41592-019-0580-y</pub-id><?supplied-pmid 31591575?><pub-id pub-id-type="pmid">31591575</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bepler</surname><given-names>T</given-names></name><etal/></person-group><article-title>Positive-unlabeled convolutional neural networks for particle picking in cryo-electron micrographs</article-title><source>Nat. Methods</source><year>2019</year><volume>16</volume><fpage>1153</fpage><lpage>1160</lpage><pub-id pub-id-type="doi">10.1038/s41592-019-0575-8</pub-id><?supplied-pmid 31591578?><pub-id pub-id-type="pmid">31591578</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Al-Azzawi</surname><given-names>A</given-names></name><name><surname>Ouadou</surname><given-names>A</given-names></name><name><surname>Tanner</surname><given-names>JJ</given-names></name><name><surname>Cheng</surname><given-names>J</given-names></name></person-group><article-title>AutoCryoPicker: an unsupervised learning approach for fully automated single particle picking in Cryo-EM images</article-title><source>BMC Bioinformatics</source><year>2019</year><volume>20</volume><fpage>326</fpage><pub-id pub-id-type="doi">10.1186/s12859-019-2926-y</pub-id><?supplied-pmid 31195977?><pub-id pub-id-type="pmid">31195977</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Al-Azzawi</surname><given-names>A</given-names></name><etal/></person-group><article-title>DeepCryoPicker: fully automated deep neural network for single protein particle picking in cryo-EM</article-title><source>BMC Bioinformatics</source><year>2020</year><volume>21</volume><fpage>1</fpage><lpage>38</lpage><pub-id pub-id-type="doi">10.1186/s12859-020-03809-7</pub-id><pub-id pub-id-type="pmid">31898485</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Iudin</surname><given-names>A</given-names></name><etal/></person-group><article-title>EMPIAR: the Electron Microscopy Public Image Archive</article-title><source>Nucleic Acids Res.</source><year>2023</year><volume>51</volume><fpage>D1503</fpage><lpage>D1511</lpage><pub-id pub-id-type="doi">10.1093/nar/gkac1062</pub-id><?supplied-pmid 36440762?><pub-id pub-id-type="pmid">36440762</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><mixed-citation publication-type="other">Agard, D., Cheng, Y., Glaeser, R. M. &#x00026; Subramaniam, S. <italic>Single-particle cryo-electron microscopy (cryo-EM): Progress, challenges, and perspectives for further improvement</italic>. <italic>Advances in Imaging and Electron Physics</italic><bold>185</bold> (Elsevier, 2014).</mixed-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Langlois</surname><given-names>R</given-names></name><etal/></person-group><article-title>Automated particle picking for low-contrast macromolecules in cryo-electron microscopy</article-title><source>J. Struct. Biol.</source><year>2014</year><volume>186</volume><fpage>1</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2014.03.001</pub-id><?supplied-pmid 24607413?><pub-id pub-id-type="pmid">24607413</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Baldwin</surname><given-names>PR</given-names></name><name><surname>Penczek</surname><given-names>PA</given-names></name></person-group><article-title>The Transform Class in SPARX and EMAN2</article-title><source>J. Struct. Biol.</source><year>2007</year><volume>157</volume><fpage>250</fpage><lpage>261</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2006.06.002</pub-id><?supplied-pmid 16861004?><pub-id pub-id-type="pmid">16861004</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><mixed-citation publication-type="other">Zhang, C. <italic>et al</italic>. TransPicker: A Transformer-based Framework for Particle Picking in cryoEM Micrographs. <italic>Proc. - 2021 IEEE Int. Conf. Bioinforma. Biomed. BIBM 2021</italic> 1179&#x02013;1184, 10.1109/BIBM52615.2021.9669524 (2021).</mixed-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>George</surname><given-names>B</given-names></name><etal/></person-group><article-title>CASSPER is a semantic segmentation-based particle picking algorithm for single-particle cryo-electron microscopy</article-title><source>Commun. Biol.</source><year>2021</year><volume>4</volume><fpage>1</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1038/s42003-021-01721-1</pub-id><pub-id pub-id-type="pmid">33398033</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McSweeney</surname><given-names>DM</given-names></name><name><surname>McSweeney</surname><given-names>SM</given-names></name><name><surname>Liu</surname><given-names>Q</given-names></name></person-group><article-title>A self-supervised workflow for particle picking in cryo-EM</article-title><source>IUCrJ</source><year>2020</year><volume>7</volume><fpage>719</fpage><lpage>727</lpage><pub-id pub-id-type="doi">10.1107/S2052252520007241</pub-id><?supplied-pmid 32695418?><pub-id pub-id-type="pmid">32695418</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><mixed-citation publication-type="other">Azzawi, A. A, Ouadou, A., Tanner, J. J. &#x00026; Cheng, J. A super-clustering approach for fully automated single particle picking in cryo-em. <italic>Genes (Basel)</italic>. <bold>10</bold> (2019).</mixed-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mallick</surname><given-names>SP</given-names></name><name><surname>Zhu</surname><given-names>Y</given-names></name><name><surname>Kriegman</surname><given-names>D</given-names></name></person-group><article-title>Detecting particles in cryo-EM micrographs using learned features</article-title><source>J. Struct. Biol.</source><year>2004</year><volume>145</volume><fpage>52</fpage><lpage>62</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2003.11.005</pub-id><?supplied-pmid 15065673?><pub-id pub-id-type="pmid">15065673</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><mixed-citation publication-type="other">Hoang, T. V., Cavin, X., Schultz, P. &#x00026; Ritchie, D. W. GEMpicker: A highly parallel GPU-accelerated particle picking tool for cryo-electron microscopy. <italic>BMC Struct. Biol</italic>. <bold>13</bold> (2013).</mixed-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wagner</surname><given-names>T</given-names></name><name><surname>Raunser</surname><given-names>S</given-names></name></person-group><article-title>The evolution of SPHIRE-crYOLO particle picking and its application in automated cryo-EM processing workflows</article-title><source>Commun. Biol.</source><year>2020</year><volume>3</volume><fpage>1</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1038/s42003-020-0790-y</pub-id><pub-id pub-id-type="pmid">31925316</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><mixed-citation publication-type="other">Masoumzadeh, A. &#x00026; Brubaker, M. HydraPicker: Fully automated particle picking in cryo-em by utilizing dataset bias in single shot detection. <italic>30th Br. Mach. Vis. Conf. 2019, BMVC 2019</italic> (2020).</mixed-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="data"><name><surname>Dhakal</surname><given-names>A</given-names></name><name><surname>Gyawali</surname><given-names>R</given-names></name><name><surname>Wang</surname><given-names>L</given-names></name><name><surname>Cheng</surname><given-names>J</given-names></name><year>2023</year><data-title>CryoPPP</data-title><source>Zenodo</source><pub-id pub-id-type="doi">10.5281/zenodo.7934683</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Campbell</surname><given-names>MG</given-names></name><etal/></person-group><article-title>Movies of ice-embedded particles enhance resolution in electron cryo-microscopy</article-title><source>Structure</source><year>2012</year><volume>20</volume><fpage>1823</fpage><lpage>1828</lpage><pub-id pub-id-type="doi">10.1016/j.str.2012.08.026</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rawson</surname><given-names>S</given-names></name><name><surname>Iadanza</surname><given-names>MG</given-names></name><name><surname>Ranson</surname><given-names>NA</given-names></name><name><surname>Muench</surname><given-names>SP</given-names></name></person-group><article-title>Methods to account for movement and flexibility in cryo-EM data processing</article-title><source>Methods</source><year>2016</year><volume>100</volume><fpage>35</fpage><lpage>41</lpage><pub-id pub-id-type="doi">10.1016/j.ymeth.2016.03.011</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Noble</surname><given-names>AJ</given-names></name><etal/></person-group><article-title>Routine single particle CryoEM sample and grid characterization by tomography</article-title><source>Elife</source><year>2018</year><volume>7</volume><fpage>1</fpage><lpage>42</lpage><pub-id pub-id-type="doi">10.7554/eLife.34257</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><mixed-citation publication-type="other">Singer, A. &#x00026; Sigworth, F. J. Computational Methods for Single-Particle Cryo-EM. 1&#x02013;40 (2020).</mixed-citation></ref><ref id="CR48"><label>48.</label><mixed-citation publication-type="other">Li, J. <italic>et al</italic>. Cryo-EM structures of Escherichia coli cytochrome bo3 reveal bound phospholipids and ubiquinone-8 in a dynamic substrate binding site. <italic>Proc. Natl. Acad. Sci. USA</italic><bold>118</bold> (2021).</mixed-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Scheres</surname><given-names>SHW</given-names></name></person-group><article-title>Semi-automated selection of cryo-EM particles in RELION-1.3</article-title><source>J. Struct. Biol.</source><year>2015</year><volume>189</volume><fpage>114</fpage><lpage>122</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2014.11.010</pub-id><?supplied-pmid 25486611?><pub-id pub-id-type="pmid">25486611</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pettersen</surname><given-names>EF</given-names></name><etal/></person-group><article-title>UCSF Chimera - A visualization system for exploratory research and analysis</article-title><source>J. Comput. Chem.</source><year>2004</year><volume>25</volume><fpage>1605</fpage><lpage>1612</lpage><pub-id pub-id-type="doi">10.1002/jcc.20084</pub-id><?supplied-pmid 15264254?><pub-id pub-id-type="pmid">15264254</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pettersen</surname><given-names>EF</given-names></name><etal/></person-group><article-title>UCSF ChimeraX: Structure visualization for researchers, educators, and developers</article-title><source>Protein Sci.</source><year>2021</year><volume>30</volume><fpage>70</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1002/pro.3943</pub-id><?supplied-pmid 32881101?><pub-id pub-id-type="pmid">32881101</pub-id></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wong</surname><given-names>W</given-names></name><etal/></person-group><article-title>Cryo-EM structure of the Plasmodium falciparum 80&#x02009;S ribosome bound to the anti-protozoan drug emetine</article-title><source>Elife</source><year>2014</year><volume>2014</volume><fpage>1</fpage><lpage>20</lpage></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lee</surname><given-names>CH</given-names></name><name><surname>MacKinnon</surname><given-names>R</given-names></name></person-group><article-title>Structures of the Human HCN1 Hyperpolarization-Activated Channel</article-title><source>Cell</source><year>2017</year><volume>168</volume><fpage>111</fpage><lpage>120.e11</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2016.12.023</pub-id><?supplied-pmid 28086084?><pub-id pub-id-type="pmid">28086084</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Campbell</surname><given-names>MG</given-names></name><etal/></person-group><article-title>Cryo-EM Reveals Integrin-Mediated TGF- b Activation without Release from Latent TGF- b Article Cryo-EM Reveals Integrin-Mediated TGF- b Activation without Release from Latent TGF- b</article-title><source>Cell</source><year>2020</year><volume>180</volume><fpage>490</fpage><lpage>501.e16</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2019.12.030</pub-id><?supplied-pmid 31955848?><pub-id pub-id-type="pmid">31955848</pub-id></element-citation></ref><ref id="CR55"><label>55.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nicholson</surname><given-names>D</given-names></name><name><surname>Edwards</surname><given-names>TA</given-names></name><name><surname>O&#x02019;Neill</surname><given-names>AJ</given-names></name><name><surname>Ranson</surname><given-names>NA</given-names></name></person-group><article-title>Structure of the 70&#x02009;S Ribosome from the Human Pathogen Acinetobacter baumannii in Complex with Clinically Relevant Antibiotics</article-title><source>Structure</source><year>2020</year><volume>28</volume><fpage>1087</fpage><lpage>1100.e3</lpage><pub-id pub-id-type="doi">10.1016/j.str.2020.08.004</pub-id><?supplied-pmid 32857965?><pub-id pub-id-type="pmid">32857965</pub-id></element-citation></ref><ref id="CR56"><label>56.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Righetto</surname><given-names>RD</given-names></name><etal/></person-group><article-title>High-resolution cryo-EM structure of urease from the pathogen Yersinia enterocolitica</article-title><source>Nat. Commun.</source><year>2020</year><volume>11</volume><fpage>1</fpage><lpage>10</lpage><pub-id pub-id-type="pmid">31911652</pub-id></element-citation></ref><ref id="CR57"><label>57.</label><mixed-citation publication-type="other">Burendei, B. <italic>et al</italic>. Cryo-EM structures of undocked innexin-6 hemichannels in phospholipids. <italic>Sci. Adv</italic>. <bold>6</bold> (2020).</mixed-citation></ref><ref id="CR58"><label>58.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tanaka</surname><given-names>S</given-names></name><etal/></person-group><article-title>Structural Basis for Binding of Potassium-Competitive Acid Blockers to the Gastric Proton Pump</article-title><source>J. Med. Chem.</source><year>2022</year><volume>65</volume><fpage>7843</fpage><lpage>7853</lpage><pub-id pub-id-type="doi">10.1021/acs.jmedchem.2c00338</pub-id><?supplied-pmid 35604136?><pub-id pub-id-type="pmid">35604136</pub-id></element-citation></ref><ref id="CR59"><label>59.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Demura</surname><given-names>K</given-names></name><etal/></person-group><article-title>Cryo-EM structures of calcium homeostasis modulator channels in diverse oligomeric assemblies</article-title><source>Sci. Adv.</source><year>2020</year><volume>6</volume><fpage>1</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1126/sciadv.aba8105</pub-id></element-citation></ref><ref id="CR60"><label>60.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhou</surname><given-names>BR</given-names></name><etal/></person-group><article-title>Distinct Structures and Dynamics of Chromatosomes with Different Human Linker Histone Isoforms</article-title><source>Mol. Cell</source><year>2021</year><volume>81</volume><fpage>166</fpage><lpage>182.e6</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2020.10.038</pub-id><?supplied-pmid 33238161?><pub-id pub-id-type="pmid">33238161</pub-id></element-citation></ref><ref id="CR61"><label>61.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Oldham</surname><given-names>ML</given-names></name><name><surname>Grigorieff</surname><given-names>N</given-names></name><name><surname>Chen</surname><given-names>J</given-names></name></person-group><article-title>Structure of the transporter associated with antigen processing trapped by herpes simplex virus</article-title><source>Elife</source><year>2016</year><volume>5</volume><fpage>1</fpage><lpage>16</lpage><pub-id pub-id-type="doi">10.7554/eLife.21829</pub-id></element-citation></ref><ref id="CR62"><label>62.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>Q</given-names></name><etal/></person-group><article-title>Synthetic group A streptogramin antibiotics that overcome Vat resistance</article-title><source>Nature</source><year>2020</year><volume>586</volume><fpage>145</fpage><lpage>150</lpage><pub-id pub-id-type="doi">10.1038/s41586-020-2761-3</pub-id><?supplied-pmid 32968273?><pub-id pub-id-type="pmid">32968273</pub-id></element-citation></ref><ref id="CR63"><label>63.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Newing</surname><given-names>TP</given-names></name><etal/></person-group><article-title>Molecular basis for RNA polymerase-dependent transcription complex recycling by the helicase-like motor protein HelD</article-title><source>Nat. Commun.</source><year>2020</year><volume>11</volume><fpage>1</fpage><lpage>11</lpage><pub-id pub-id-type="doi">10.1038/s41467-020-20157-5</pub-id><pub-id pub-id-type="pmid">31911652</pub-id></element-citation></ref><ref id="CR64"><label>64.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kuzuya</surname><given-names>M</given-names></name><etal/></person-group><article-title>Structures of human pannexin-1 in nanodiscs reveal gating mediated by dynamic movement of the N terminus and phospholipids</article-title><source>Sci. Signal.</source><year>2022</year><volume>15</volume><fpage>1</fpage><lpage>11</lpage><pub-id pub-id-type="doi">10.1126/scisignal.abg6941</pub-id></element-citation></ref><ref id="CR65"><label>65.</label><mixed-citation publication-type="other">Liu, Y. <italic>et al</italic>. Ligand recognition and allosteric modulation of the human MRGPRX1 receptor. <italic>Nat. Chem. Biol</italic>. <bold>19</bold> (2022).</mixed-citation></ref><ref id="CR66"><label>66.</label><mixed-citation publication-type="other">Josephs, T. M. <italic>et al</italic>. Structure and dynamics of the CGRP receptor in apo and peptide-bound forms. <italic>Science (80-.)</italic>. <bold>372</bold> (2021).</mixed-citation></ref><ref id="CR67"><label>67.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dong</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Cryo-EM structures and dynamics of substrate-engaged human 26&#x02009;S proteasome</article-title><source>Nature</source><year>2019</year><volume>565</volume><fpage>49</fpage><lpage>55</lpage><pub-id pub-id-type="doi">10.1038/s41586-018-0736-4</pub-id><?supplied-pmid 30479383?><pub-id pub-id-type="pmid">30479383</pub-id></element-citation></ref><ref id="CR68"><label>68.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fischer</surname><given-names>N</given-names></name><etal/></person-group><article-title>The pathway to GTPase activation of elongation factor SelB on the ribosome</article-title><source>Nature</source><year>2016</year><volume>540</volume><fpage>80</fpage><lpage>85</lpage><pub-id pub-id-type="doi">10.1038/nature20560</pub-id><?supplied-pmid 27842381?><pub-id pub-id-type="pmid">27842381</pub-id></element-citation></ref><ref id="CR69"><label>69.</label><mixed-citation publication-type="other">Milne, J. L. S. &#x00026; Subramaniam, S. 2.2&#x02009;&#x000c5; resolution cryo-EM structure of &#x003b2;-galactosidase in complex with a cell-permeant inhibitor. <bold>348</bold>, 1147&#x02013;1152 (2015).</mixed-citation></ref><ref id="CR70"><label>70.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zi Tan</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Addressing preferred specimen orientation in single-particle cryo-EMthrough tilting</article-title><source>Nat. Methods</source><year>2017</year><volume>14</volume><fpage>793</fpage><lpage>796</lpage><pub-id pub-id-type="doi">10.1038/nmeth.4347</pub-id><pub-id pub-id-type="pmid">28671674</pub-id></element-citation></ref><ref id="CR71"><label>71.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Passos</surname><given-names>DO</given-names></name><etal/></person-group><article-title>Structural basis for strand-transfer inhibitor binding to HIV intasomes</article-title><source>Science (80-.).</source><year>2020</year><volume>367</volume><fpage>810</fpage><lpage>814</lpage><pub-id pub-id-type="doi">10.1126/science.aay8015</pub-id></element-citation></ref><ref id="CR72"><label>72.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Falzone</surname><given-names>ME</given-names></name><etal/></person-group><article-title>Structural basis of Ca2&#x02009;+&#x02009;-dependent activation and lipid transport by a TMEM16 scramblase</article-title><source>Elife</source><year>2019</year><volume>8</volume><fpage>1</fpage><lpage>25</lpage><pub-id pub-id-type="doi">10.7554/eLife.43229</pub-id></element-citation></ref><ref id="CR73"><label>73.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liao</surname><given-names>M</given-names></name><name><surname>Cao</surname><given-names>E</given-names></name><name><surname>Julius</surname><given-names>D</given-names></name><name><surname>Cheng</surname><given-names>Y</given-names></name></person-group><article-title>Structure of the TRPV1 ion channel determined by electron cryo-microscopy</article-title><source>Nature</source><year>2013</year><volume>504</volume><fpage>107</fpage><lpage>112</lpage><pub-id pub-id-type="doi">10.1038/nature12822</pub-id><?supplied-pmid 24305160?><pub-id pub-id-type="pmid">24305160</pub-id></element-citation></ref><ref id="CR74"><label>74.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Koning</surname><given-names>RI</given-names></name><etal/></person-group><article-title>Asymmetric cryo-EM reconstruction of phage MS2 reveals genome structure in situ</article-title><source>Nat. Commun.</source><year>2016</year><volume>7</volume><fpage>1</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1038/ncomms12524</pub-id></element-citation></ref><ref id="CR75"><label>75.</label><mixed-citation publication-type="other">Kim, L. Y. <italic>et al</italic>. Benchmarking cryo-EM single particle analysis workflow. <italic>Front. Mol. Biosci</italic>. <bold>5</bold> (2018).</mixed-citation></ref><ref id="CR76"><label>76.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jin</surname><given-names>P</given-names></name><etal/></person-group><article-title>Electron cryo-microscopy structure of the mechanotransduction channel NOMPC</article-title><source>Nature</source><year>2017</year><volume>547</volume><fpage>118</fpage><lpage>122</lpage><pub-id pub-id-type="doi">10.1038/nature22981</pub-id><?supplied-pmid 28658211?><pub-id pub-id-type="pmid">28658211</pub-id></element-citation></ref><ref id="CR77"><label>77.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Asami</surname><given-names>J</given-names></name><etal/></person-group><article-title>Structure of the bile acid transporter and HBV receptor NTCP</article-title><source>Nature</source><year>2022</year><volume>606</volume><fpage>1021</fpage><lpage>1026</lpage><pub-id pub-id-type="doi">10.1038/s41586-022-04845-4</pub-id><?supplied-pmid 35580629?><pub-id pub-id-type="pmid">35580629</pub-id></element-citation></ref><ref id="CR78"><label>78.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cao</surname><given-names>C</given-names></name><etal/></person-group><article-title>Structure, function and pharmacology of human itch GPCRs</article-title><source>Nature</source><year>2021</year><volume>600</volume><fpage>170</fpage><lpage>175</lpage><pub-id pub-id-type="doi">10.1038/s41586-021-04126-6</pub-id><?supplied-pmid 34789874?><pub-id pub-id-type="pmid">34789874</pub-id></element-citation></ref><ref id="CR79"><label>79.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ye</surname><given-names>G</given-names></name><name><surname>Liu</surname><given-names>B</given-names></name><name><surname>Li</surname><given-names>F</given-names></name></person-group><article-title>Cryo-EM structure of a SARS-CoV-2 omicron spike protein ectodomain</article-title><source>Nat. Commun.</source><year>2022</year><volume>13</volume><fpage>1</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1038/s41467-022-28882-9</pub-id><pub-id pub-id-type="pmid">34983933</pub-id></element-citation></ref></ref-list></back></article>