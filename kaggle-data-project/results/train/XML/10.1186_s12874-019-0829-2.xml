<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Med Res Methodol</journal-id><journal-id journal-id-type="iso-abbrev">BMC Med Res Methodol</journal-id><journal-title-group><journal-title>BMC Medical Research Methodology</journal-title></journal-title-group><issn pub-type="epub">1471-2288</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6805536</article-id><article-id pub-id-type="pmid">31640567</article-id><article-id pub-id-type="publisher-id">829</article-id><article-id pub-id-type="doi">10.1186/s12874-019-0829-2</article-id><article-categories><subj-group subj-group-type="heading"><subject>Software</subject></subj-group></article-categories><title-group><article-title>BUGSnet: an R package to facilitate the conduct and reporting of Bayesian network Meta-analyses</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-4124-2498</contrib-id><name><surname>B&#x000e9;liveau</surname><given-names>Audrey</given-names></name><address><phone>************</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Boyne</surname><given-names>Devon J.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Slater</surname><given-names>Justin</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Brenner</surname><given-names>Darren</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Arora</surname><given-names>Paul</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 8644 1405</institution-id><institution-id institution-id-type="GRID">grid.46078.3d</institution-id><institution>Department of Statistics and Actuarial Science, </institution><institution>University of Waterloo, </institution></institution-wrap>200 University Avenue West, Waterloo, Ontario N2L 3G1 Canada </aff><aff id="Aff2"><label>2</label>Division of Analytics, Lighthouse Outcomes, 1 University Avenue (3rd Floor), Toronto, Ontario M5J 2P1 Canada </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 7697</institution-id><institution-id institution-id-type="GRID">grid.22072.35</institution-id><institution>Department of Community Health Sciences, </institution><institution>University of Calgary, </institution></institution-wrap>2500 University Drive NW, Calgary, Alberta T2N 1N4 Canada </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 7697</institution-id><institution-id institution-id-type="GRID">grid.22072.35</institution-id><institution>Department of Oncology, </institution><institution>University of Calgary, </institution></institution-wrap>2500 University Drive NW, Calgary, Alberta T2N 1N4 Canada </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2157 2938</institution-id><institution-id institution-id-type="GRID">grid.17063.33</institution-id><institution>Dalla Lana School of Public Health, </institution><institution>University of Toronto, </institution></institution-wrap>Health Sciences Building, 155 College Street (6th Floor), Toronto, Ontario M5T 3M7 Canada </aff></contrib-group><pub-date pub-type="epub"><day>22</day><month>10</month><year>2019</year></pub-date><pub-date pub-type="pmc-release"><day>22</day><month>10</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>19</volume><elocation-id>196</elocation-id><history><date date-type="received"><day>26</day><month>4</month><year>2019</year></date><date date-type="accepted"><day>6</day><month>9</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2019</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Several reviews have noted shortcomings regarding the quality and reporting of network meta-analyses (NMAs). We suspect that this issue may be partially attributable to limitations in current NMA software which do not readily produce all of the output needed to satisfy current guidelines.</p></sec><sec><title>Results</title><p id="Par2">To better facilitate the conduct and reporting of NMAs, we have created an R package called &#x0201c;BUGSnet&#x0201d; (<bold>B</bold>ayesian inference <bold>U</bold>sing <bold>G</bold>ibbs <bold>S</bold>ampling to conduct a <bold>Net</bold>work meta-analysis). This R package relies upon Just Another Gibbs Sampler (JAGS) to conduct Bayesian NMA using a generalized linear model. BUGSnet contains a suite of functions that can be used to describe the evidence network, estimate a model and assess the model fit and convergence, assess the presence of heterogeneity and inconsistency, and output the results in a variety of formats including league tables and surface under the cumulative rank curve (SUCRA) plots. We provide a demonstration of the functions contained within BUGSnet by recreating a Bayesian NMA found in the second technical support document composed by the National Institute for Health and Care Excellence Decision Support Unit (NICE-DSU). We have also mapped these functions to checklist items within current reporting and best practice guidelines.</p></sec><sec><title>Conclusion</title><p id="Par3">BUGSnet is a new R package that can be used to conduct a Bayesian NMA and produce all of the necessary output needed to satisfy current scientific and regulatory standards. We hope that this software will help to improve the conduct and reporting of NMAs.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Network meta-analysis</kwd><kwd>Indirect treatment comparison</kwd><kwd>Systematic review</kwd><kwd>Bayesian inference</kwd><kwd>Knowledge synthesis</kwd><kwd>Health technology assessment</kwd><kwd>Clinical efficacy</kwd><kwd>R package</kwd><kwd>Reporting guidelines</kwd></kwd-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2019</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par9">Indirect treatment comparisons (ITC) and network meta-analysis (NMA) are approaches for quantitatively summarizing an evidence base in which there are more than two treatments of interest. Unlike traditional pairwise meta-analysis, ITC/NMA can incorporate indirect evidence that arises when a group of studies evaluating different treatments share a common comparator. The incorporation of such evidence within an NMA has several advantages over pairwise meta-analysis [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. Unlike pairwise meta-analysis, an NMA allows for the comparison of two or more treatments that have never been directly compared provided that the studies examining such treatments are linked via a common comparator (i.e. an indirect comparison) [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. Another important advantage of NMA over pairwise meta-analysis is that it may provide greater statistical precision through its incorporation of indirect evidence which is not taken into account within pairwise meta-analysis [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. Lastly, an NMA can be used to rank a set of treatments for a given disease indication with respect to their clinically efficacy or harm and can be used to quantify the uncertainty surrounding such which is useful when determining policies, guidelines, and costs surrounding the choice of treatment [<xref ref-type="bibr" rid="CR2">2</xref>].</p><p id="Par10">The number of publications using NMA has increased dramatically within the past decade [<xref ref-type="bibr" rid="CR3">3</xref>]. Despite this increase, several reviews have noted shortcomings with respect to the quality of the conduct and reporting of NMAs [<xref ref-type="bibr" rid="CR4">4</xref>&#x02013;<xref ref-type="bibr" rid="CR9">9</xref>]. In particular, several authors have noted that a considerable proportion of NMAs do not provide a descriptive overview of the network or its structure, fail to adequately describe the statistical methods employed and whether or not their underlying assumptions were assessed and met, and lack a comprehensive summary of the results including effect estimates and measures of uncertainty regarding treatment ranks [<xref ref-type="bibr" rid="CR4">4</xref>&#x02013;<xref ref-type="bibr" rid="CR9">9</xref>]. To improve the conduct, reporting, and appraisal of NMAs, a number of guidelines have been published which include the International Society of Pharmacoeconomics and Outcomes &#x02013; Academy of Managed Care Pharmacy &#x02013; National Pharmaceutical Council (ISPOR-AMCP-NPC) questionnaire for assessing the relevance and credibility of an NMA [<xref ref-type="bibr" rid="CR10">10</xref>], the Preferred Reporting Items for Systematic Reviews and Meta-Analyses (PRISMA) extension for reporting systematic reviews incorporating NMAs of health care interventions [<xref ref-type="bibr" rid="CR11">11</xref>], and the National Institute for Health and Care Excellence Decision Support Unit (NICE-DSU) reviewer&#x02019;s checklist for appraising the synthesis of evidence within a submission to a health technology assessment agency (technical support document 7) [<xref ref-type="bibr" rid="CR12">12</xref>].</p><p id="Par11">Although the dissemination and uptake of such guidelines will hopefully help to address some of the foregoing issues, we suspect that the such issues may, in part, be related to limitations in current user-friendly software and tools used to conduct NMA. As previously noted, current software packages do not readily produce all of the output necessary to satisfy current reporting guidelines in a format that is suitable for submission to a journal or health technology assessment agency [<xref ref-type="bibr" rid="CR13">13</xref>, <xref ref-type="bibr" rid="CR14">14</xref>]. Individuals must therefore rely upon multiple software packages, modify existing software, or generate code de novo in order to adhere to scientific and regulatory standards [<xref ref-type="bibr" rid="CR14">14</xref>]. The resulting increase in time, effort, and expertise has likely impacted the quality and reporting of NMAs done to date. Furthermore, we have found that the documentation and help files of current software packages sometimes suffer from a lack of clarity regarding their implementation and use. In addition, the current lack of approachable tutorials that demonstrate how to use current NMA software could be a hindrance to users with limited programming expertise. To address these limitations, we have developed an R package called &#x0201c;BUGSnet&#x0201d; (<bold>B</bold>ayesian inference <bold>U</bold>sing <bold>G</bold>ibbs <bold>S</bold>ampling to conduct a <bold>Net</bold>work meta-analysis) aimed at improving the reporting and conduct of NMA/ITC. BUGSnet improves over its two main competing software packages for conducting a contrast-based Bayesian NMA: GeMTC [<xref ref-type="bibr" rid="CR15">15</xref>] and NetMetaXL [<xref ref-type="bibr" rid="CR16">16</xref>]. While NetMetaXL does produce much of the output necessary to satisfy reporting guidelines, it is limited in the types of analyses it can carry out. Specifically, one cannot use NetMetaXL to analyze outcomes that are not dichotomous, to conduct meta-regression, or to analyzing evidence bases with more than 15 treatments [<xref ref-type="bibr" rid="CR16">16</xref>]. While GeMTC provides an enhanced suite of functions for conducting NMA relative to NetMetaXL, its reporting capabilities are limited. For example, GeMTC does not readily produce key reporting items for an NMA such as tabular overview of the evidence base or a SUCRA plot and league table of the NMA results on the original scale.</p><sec id="Sec2"><title>Implementation</title><p id="Par12">BUGSnet is a suite of functions that will carry out a Bayesian NMA while generating all items needed to satisfy the <italic>statistical</italic> components of the PRISMA, ISPOR-AMCP-NPC, and NICE-DSU checklists in a format that is suitable for publication or submission to a decision-making organization. These statistical components can be broadly categorized into: description of network (graphical and tabular), detection of heterogeneity, network meta-analysis (including meta-regression), model assessment, detection of inconsistency and reporting of the results. An overview of BUGSnet&#x02019;s functions and the corresponding checklist items that they address is presented in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>.
<table-wrap id="Tab1"><label>Table 1</label><caption><p>List of functions within the BUGSnet package and corresponding items on guidelines that they address</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2">Domain</th><th rowspan="2">Function</th><th rowspan="2">Brief description</th><th colspan="3">Checklist Item No.</th></tr><tr><th>PRISMA [<xref ref-type="bibr" rid="CR11">11</xref>]</th><th>ISPOR-AMPC-NCA [<xref ref-type="bibr" rid="CR10">10</xref>]</th><th>NICE-DSU [<xref ref-type="bibr" rid="CR12">12</xref>]</th></tr></thead><tbody><tr><td>Data preparation</td><td>data.prep()</td><td>Prepares data for further processing</td><td>N/A</td><td>N/A</td><td>N/A</td></tr><tr><td rowspan="2">Description of network</td><td>net.tab()</td><td>Descriptive statistics of evidence network</td><td>S4, 20</td><td>2, 14</td><td>A9.1, C3.1</td></tr><tr><td>net.plot()</td><td>Plot of evidence network</td><td>S3</td><td>2, 14</td><td><p>A9.1, C3.1,</p><p>C4.1</p></td></tr><tr><td rowspan="2">Homogeneity assessment</td><td>data.plot()</td><td>Graph of characteristics by study or treatment</td><td>18</td><td>5, 6</td><td><p>A7.2,</p><p>B2.5, C4.2</p></td></tr><tr><td>pma()</td><td>Heterogeneity statistics for all direct comparisons</td><td>-</td><td>5, 6</td><td><p>B2.1, B4.1,</p><p>C4.2</p></td></tr><tr><td rowspan="2">Network meta-analysis</td><td>nma.model()</td><td>Specifies the NMA model (including meta-regression)</td><td>23</td><td><p>7, 9</p><p>10, 12, 13, 19</p></td><td><p>A6.3,</p><p>A7.2, B2.3, B3.1, C2.1</p></td></tr><tr><td>nma.run()</td><td>Runs the NMA</td><td>N/A</td><td>N/A</td><td>N/A</td></tr><tr><td rowspan="2">Model assessment</td><td>nma.diag()</td><td>Trace plots and other convergence diagnostics</td><td>&#x02013;</td><td>&#x02013;</td><td>B1.1</td></tr><tr><td>nma.fit()</td><td>Leverage plots and deviance information criterion (DIC) values</td><td>23, S5</td><td>8, 11, 12</td><td>B2.2</td></tr><tr><td rowspan="5">Output results</td><td>nma.forest()</td><td>A forest plot of the NMA results</td><td>21</td><td>17</td><td>B4.1</td></tr><tr><td>nma.league()</td><td>League table of the NMA results</td><td>21</td><td>17</td><td>B4.1</td></tr><tr><td>nma.rank()</td><td>Tabular and graphical results for treatment ranking including Surface under the cumulative ranking curve (SUCRA) plot</td><td>21</td><td>18</td><td>B4.1</td></tr><tr><td>nma.regplot()</td><td>Meta-regression only. Plot of estimated relative treatment effects (on the linear scale) as a function of covariate values</td><td>23</td><td>10, 13, 19</td><td>A6.3, A7.2, B2.3</td></tr><tr><td>pma()</td><td>Results from pairwise comparisons</td><td>20</td><td>16</td><td>B4.1</td></tr><tr><td>Consistency assessment</td><td>nma.compare()</td><td>Comparison of consistency and inconsistency models [<xref ref-type="bibr" rid="CR17">17</xref>]</td><td>S5</td><td>8</td><td>C4.3</td></tr></tbody></table></table-wrap></p><p id="Par13">BUGSnet is implemented within R software. BUGSnet requires that the user have installed Just Another Gibbs Sampler (JAGS) on their computer [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR19">19</xref>]. Information as to how to install JAGS can be found at the program&#x02019;s sourceforge homepage: <ext-link ext-link-type="uri" xlink:href="http://mcmc-jags.sourceforge.net/">http://mcmc-jags.sourceforge.net/</ext-link>. BUGSnet is hosted and can be accessed at the following URL: <ext-link ext-link-type="uri" xlink:href="https://bugsnetsoftware.github.io/">https://bugsnetsoftware.github.io/</ext-link>. We encourage users to submit feedback on existing code and to provide suggestions for additional functions that should be added to BUGSnet at the aforementioned homepage. Detailed vignettes describing the step-by-step use of BUGSnet to conduct an NMA on various types of outcomes are currently available in the R package documentation and on the BUGSnet homepage and additional applied examples are forthcoming.</p></sec><sec id="Sec3"><title>Data preparation</title><p id="Par14">The first step to using BUGSnet is to process the data using the data.prep() function where the user specifies the name of the columns variables that correspond to the study IDs and treatment arms. This way, the user does not have to enter this information over and over in subsequent functions.</p></sec><sec id="Sec4"><title>Description of network</title><p id="Par15">Current guidelines recommend that authors report plot of the evidence network [<xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR12">12</xref>]. The net.plot() and the net.tab() functions allow the user to describe the network of studies in a graphical and tabular format respectively.</p><p id="Par16">With respect to the network graph, the size of the nodes and edges within the network plot are scaled such that they reflect the number of studies examining a specific treatment and the number of comparisons between any two given treatments respectively as per current recommendations. In addition, we have introduced an option that allows the user to highlight specific interventions of interest within the network graph and to label the edges with the names of the studies that have investigated these particular treatments. The colour, size, and layout of the network graph is highly customizable to ensure that the resulting figure will meet industry and journal standards.</p><p id="Par17">The net.tab() function produces descriptive tables that are based on the tables produced by NetMetaXL &#x02013; an excel-based software for conducting Bayesian NMAs [<xref ref-type="bibr" rid="CR16">16</xref>]. While the tables produced by NetMetaXl are excellent descriptors of the network geometry, this software is currently only capable of handling dichotomous outcomes and is limited to 15 treatments [<xref ref-type="bibr" rid="CR16">16</xref>]. We have expanded upon the tabular reporting of NetMetaXL by allowing such tables to summarize other types of outcomes including continuous, dichotomous, and count outcomes. An additional feature of our function is a report on whether the network is connected or not.</p></sec><sec id="Sec5"><title>Homogeneity</title><p id="Par18">Current guidelines recommend a careful exploration of heterogeneity within the network, typically prior to conducting the NMA [<xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR12">12</xref>]. Researchers should identify which characteristics are likely to be important modifiers of the treatment effects a priori using content expertise or a literature review [<xref ref-type="bibr" rid="CR20">20</xref>]. Once identified, one can use the data.plot() function within BUGSnet to assess the heterogeneity of these modifiers within an evidence network. Specifically, this function generates a graph that allows the user to display a characteristic of interest within each treatment arm, grouped by study ID or treatment.</p><p id="Par19">In addition, BUGSnet also provides an option within the pma() function to produce a table summarizing a Cochrane chi-square test, the tau-squared statistic, and the I-squared statistic for assessing between-study heterogeneity within each possible pairwise comparison within the network in which there is direct evidence [<xref ref-type="bibr" rid="CR21">21</xref>].</p></sec><sec id="Sec6"><title>Network meta-analysis</title><p id="Par20">BUGSnet implements a Bayesian contrast-based NMA using a generalised linear model as described in the NICE-DSU technical support document 2 [<xref ref-type="bibr" rid="CR17">17</xref>]. The BUGS code used to generate these models within the BUGSnet package borrows heavily from this source [<xref ref-type="bibr" rid="CR17">17</xref>]. Within BUGSnet, the nma.model() function is used to generate the BUGS model that one wishes to fit which includes aspects such as the link function and the likelihood distribution appropriate for the outcome of interest, the choice of using a fixed effects or a random effects model, and the inclusion of covariates if one wishes to conduct a meta-regression. After the NMA model has been generated, one can run a Bayesian network meta-analysis with the function nma.run(). In the nma.run() function, the user can specify the number of burn-ins, iterations, and adaptations for the Markov Chain Monte Carlo (MCMC) algorithm and which variables they wish to monitor.</p><sec id="Sec7"><title>Bayesian inference</title><p id="Par21">BUGSnet conducts NMA using Bayesian inference. There were several practical and theoretical reasons for choosing to implement the package within a Bayesian as opposed to a frequentist framework as noted by others: 1) Bayesian methods are more popular among researchers who conduct network meta-analyses; 2) Bayesian methods for network meta-analysis have been developed to a further degree; 3) Bayesian methods allow one to better handle data from trials with multiple arms and trials in which there are arms with zero events; 4) Bayesian methods are currently better suited for modeling uncertainty surrounding the heterogeneity between studies; 5) Bayesian methods present results as probabilities and are thus more suitable for ranking treatment efficacy and for incorporation into health-economic decision modeling [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR22">22</xref>].</p></sec><sec id="Sec8"><title>NMA models</title><p id="Par22">BUGSnet can handle continuous, dichotomous, and count data (with or without varying follow-up times) as well as data from studies with more than two treatment arms. In what follows, we describe the NMA models that are implemented within BUGSnet. Suppose that we have data from studies <italic>i</italic>&#x02009;=&#x02009;1, &#x02026;, <italic>M</italic>. In arm <italic>k</italic> of study <italic>i</italic>, treatment <italic>t</italic><sub><italic>ik</italic></sub>&#x02009;&#x02208;&#x02009;{1,&#x02009;&#x02026;,&#x02009;<italic>T</italic>} was used. The set {1,&#x02009;&#x02026;,&#x02009;<italic>T</italic>} represents the set of treatments that were assessed across the <italic>M</italic> studies, where treatment 1 is a reference treatment. Let <italic>a</italic><sub>1</sub>, &#x02026;, <italic>a</italic><sub><italic>M</italic></sub> represent the number of arms in studies 1, &#x02026;, <italic>M</italic>. Let <italic>R</italic><sub><italic>ik</italic></sub> be the measured aggregate response in arm <italic>k</italic> of study <italic>i</italic> (e.g. proportion of individuals who were alive at one-year, average blood pressure, etc.). Those responses are modeled as conditionally independent using an appropriate distribution <italic>F</italic> which is chosen based on the type of outcome at hand. For continuous outcomes, where the aggregate responses take the from of the sample mean and standard error in each arm, the distribution <italic>F</italic> is the normal distribution; <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {R}_{ik}\sim Normal\left({\varphi}_{ik},{se}_{ik}^2\ \right) $$\end{document}</tex-math><mml:math id="M2" display="inline"><mml:msub><mml:mi>R</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mo mathvariant="bold">~</mml:mo><mml:mtext mathvariant="italic">Normal</mml:mtext><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mrow><mml:msubsup><mml:mi mathvariant="italic">se</mml:mi><mml:mi mathvariant="italic">ik</mml:mi><mml:mn>2</mml:mn></mml:msubsup><mml:mspace width="0.25em"/></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq1.gif"/></alternatives></inline-formula>, where <italic>&#x003c6;</italic><sub><italic>ik</italic></sub> is the mean and <inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {se}_{ik}^2 $$\end{document}</tex-math><mml:math id="M4" display="inline"><mml:msubsup><mml:mi mathvariant="italic">se</mml:mi><mml:mi mathvariant="italic">ik</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq2.gif"/></alternatives></inline-formula> is the observed standard error of the responses in arm <italic>k</italic> of study <italic>i</italic>. When outcome is dichotomous, the distribution <italic>F</italic> is the binomial distribution; <italic>R</italic><sub><italic>ik</italic></sub>~<italic>Binomial</italic>(<italic>n</italic><sub><italic>ik</italic></sub>,&#x02009;<italic>&#x003c6;</italic><sub><italic>ik</italic></sub>&#x000a0;), where <italic>&#x003c6;</italic><sub><italic>ik</italic></sub> is the probability of experiencing the event and <italic>n</italic><sub><italic>ik</italic></sub> is the sample size in arm <italic>k</italic> of study <italic>i</italic>. When outcomes take the form of counts and the event rates can be assumed to be constant over the duration of follow-up, one can use the Poisson distribution; <italic>R</italic><sub><italic>ik</italic></sub><bold>~</bold><italic>Poisson</italic>(<italic>e</italic><sub><italic>ik</italic></sub><italic>&#x003c6;</italic><sub><italic>ik</italic></sub>&#x000a0;), where <italic>e</italic><sub><italic>ik</italic></sub> is the observed person-time at risk and <italic>&#x003c6;</italic><sub><italic>ik</italic></sub> is the event rate in arm <italic>k</italic> of study <italic>i</italic>. The latent parameters <italic>&#x003c6;</italic><sub><italic>ik</italic></sub> &#x02019;s are transformed using an appropriate link function <italic>g</italic>(&#x000b7;) so&#x000a0;<italic>g</italic>(<italic>&#x003c6;</italic><sub><italic>ik</italic></sub>)&#x02009;&#x02261;&#x02009;<italic>&#x003b8;</italic><sub><italic>ik</italic></sub> can be modeled with a linear model. Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref> summarizes the link functions <italic>g</italic>(&#x000b7;) and family distributions <italic>F</italic> implemented within BUGSnet based on the type of outcome data. Following the NICE-DSU technical support document 2 [<xref ref-type="bibr" rid="CR17">17</xref>], the linear model used is generally of the contrast-based form:
<disp-formula id="Equa"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\theta}_{ik}={\mu}_i+{\delta}_{ik}, $$\end{document}</tex-math><mml:math id="M6" display="block"><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mo>,</mml:mo></mml:math><graphic xlink:href="12874_2019_829_Article_Equa.gif" position="anchor"/></alternatives></disp-formula>where <italic>&#x003bc;</italic><sub><italic>i</italic></sub> represents the fixed effect of the treatment from arm 1 in study <italic>i</italic> (a control treatment) and <italic>&#x003b4;</italic><sub><italic>ik</italic></sub> represents the (fixed or random) effect of the treatment from arm <italic>k</italic> of study <italic>i</italic> relative to the treatment in arm 1 and <italic>&#x003b4;</italic><sub><italic>i</italic>1</sub>&#x02009;=&#x02009;0 for <italic>i</italic>&#x000a0;=&#x02009;1, &#x02026;,<italic>M.</italic> In BUGSnet, two exceptions to this model occur. First, when exploring a dichotomous outcome from studies with differing lengths of follow-up time, one can use a binomial family distribution with the complementary log-log link and the linear model includes the observed follow-up time <italic>f</italic><sub><italic>i</italic></sub> in trial <italic>i</italic>: <italic>&#x003b8;</italic><sub><italic>ik</italic></sub>&#x02009;=&#x02009;log(<italic>f</italic><sub><italic>i</italic></sub>)&#x02009;+&#x02009;<italic>&#x003bc;</italic><sub><italic>i</italic></sub>&#x02009;+&#x02009;<italic>&#x003b4;</italic><sub><italic>ik</italic></sub>&#x000a0;[<xref ref-type="bibr" rid="CR17">17</xref>]. Second, when exploring a dichotomous outcome with a binomial family distribution and a log link, the linear model takes the form <italic>&#x003b8;</italic><sub><italic>ik</italic></sub>&#x02009;=&#x02009;min(<italic>&#x003bc;</italic><sub><italic>i</italic></sub>&#x02009;+&#x02009;<italic>&#x003b4;</italic><sub><italic>ik</italic></sub>,&#x02009;&#x02212;10<sup>&#x02212;16</sup>) to ensure that <italic>&#x003b8;</italic><sub><italic>ik</italic></sub> is negative and the probabilities <italic>&#x003c6;</italic><sub><italic>ik</italic></sub> are between 0 and 1.
<table-wrap id="Tab2"><label>Table 2</label><caption><p>Types of outcomes and corresponding link functions and likelihood distributions available within BUGSnet</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Type of Outcome</th><th>Arm-Level<break/>Data Required</th><th>Distribution Family</th><th>Link function</th><th>Measure of effect</th><th>Assumption on follow-up time</th></tr></thead><tbody><tr><td>Continuous</td><td>Mean &#x00026; Standard Error</td><td>Normal</td><td>Identity</td><td>Mean Difference</td><td rowspan="3">Outcome is unrelated to follow-up time</td></tr><tr><td rowspan="3">Dichotomous</td><td rowspan="2"><p>Events &#x00026;</p><p>Sample Size</p></td><td rowspan="3">Binomial</td><td>Logit</td><td>Odds Ratio</td></tr><tr><td>Log</td><td>Risk Ratio</td></tr><tr><td><p>Events &#x00026;</p><p>Sample Size &#x00026;</p><p>Median</p><p>Follow-Up Time</p></td><td><p>Complementary</p><p>log-log</p></td><td>Hazard Ratio</td><td rowspan="2">Event rates are constant over the duration of follow-up</td></tr><tr><td>Count</td><td>Events &#x00026; Person-Time at Risk</td><td>Poisson</td><td>Log</td><td>Rate Ratio</td></tr></tbody></table></table-wrap></p><p id="Par23">In a random effect model, the <inline-formula id="IEq3"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\boldsymbol{\delta}}_i'\mathrm{s}={\left({\delta}_{i2},\dots, {\delta}_{i{a}_i}\right)}^{\top } $$\end{document}</tex-math><mml:math id="M8" display="inline"><mml:msub><mml:mi mathvariant="bold-italic">&#x003b4;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>&#x02019;</mml:mo><mml:mi mathvariant="normal">s</mml:mi><mml:mo>=</mml:mo><mml:msup><mml:mfenced close=")" open="(" separators=",,"><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02026;</mml:mo><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:msub><mml:mi>a</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub></mml:mfenced><mml:mo>&#x022a4;</mml:mo></mml:msup></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq3.gif"/></alternatives></inline-formula> are modeled as conditionally independent with distributions
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left[{\boldsymbol{\delta}}_i|{\mathbf{d}}_i,\varSigma \right]\sim MVNormal\left({\mathbf{d}}_i,\varSigma \right), $$\end{document}</tex-math><mml:math id="M10" display="block"><mml:mfenced close="]" open="[" separators="|"><mml:msub><mml:mi mathvariant="bold-italic">&#x003b4;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mrow><mml:msub><mml:mi mathvariant="bold">d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mi>&#x003a3;</mml:mi></mml:mrow></mml:mfenced><mml:mo>~</mml:mo><mml:mtext mathvariant="italic">MVNormal</mml:mtext><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi mathvariant="bold">d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mi>&#x003a3;</mml:mi></mml:mfenced><mml:mo>, </mml:mo></mml:math><graphic xlink:href="12874_2019_829_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula>where <inline-formula id="IEq4"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\mathbf{d}}_i={\left({d}_{\left({t}_{i1},{t}_{i2}\right)},\dots, {d}_{\left({t}_{i1},{t}_{i{a}_i}\right)}\right)}^{\top } $$\end{document}</tex-math><mml:math id="M12" display="inline"><mml:msub><mml:mi mathvariant="bold">d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msup><mml:mfenced close=")" open="(" separators=",,"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02026;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:msub><mml:mi>a</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub></mml:mfenced></mml:msub></mml:mfenced><mml:mo>&#x022a4;</mml:mo></mml:msup></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq4.gif"/></alternatives></inline-formula> and <inline-formula id="IEq5"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)}={d}_{\left(1,{t}_{ik}\right)}-{d}_{\left(1,{t}_{i1}\right)} $$\end{document}</tex-math><mml:math id="M14" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq5.gif"/></alternatives></inline-formula> is the difference in the treatment effect of treatments <italic>t</italic><sub><italic>i</italic>1</sub> and <italic>t</italic><sub><italic>ik</italic></sub> on the <italic>g</italic>(&#x000b7;) scale and&#x000a0;<italic>d</italic><sub>(1,&#x02009;1)</sub>&#x02009;=&#x02009;0. For <italic>&#x003a3;</italic> we adopt the usual compound symmetry structure described in (16), with variances <italic>&#x003c3;</italic><sup>2</sup> and covariances 0.5<italic>&#x003c3;</italic><sup>2</sup>, where <italic>&#x003c3;</italic><sup>2</sup> represents the between-trial variability in treatment effects (heterogeneity). Independent priors are used on <italic>&#x003c3;</italic>, <italic>d</italic><sub>(1,&#x02009;2)</sub>, &#x02026;. ,<italic>d</italic><sub>(1,&#x02009;<italic>T</italic>)</sub> and <italic>&#x003bc;</italic><sub>1</sub>, &#x02026;, <italic>&#x003bc;</italic><sub><italic>M</italic></sub>. For ease of implementation, in BUGSnet, the distribution (1) is decomposed into a series of conditional distributions [<xref ref-type="bibr" rid="CR17">17</xref>].
<disp-formula id="Equb"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left[{\delta}_{ik}|{\delta}_{i2},\dots, {\delta}_{ik-1},{\mathbf{d}}_i,\varSigma \right]\sim Normal\left({d}_{\left({t}_{i1},{t}_{ik}\right)}+\frac{1}{k-1}{\sum}_{j=1}^{k-1}\left[{\delta}_{\mathrm{ij}}-{d}_{\left({t}_{i1},{t}_{ik}\right)}\right],\frac{k}{2\left(k-1\right)}{\sigma}^2\right). $$\end{document}</tex-math><mml:math id="M16" display="block"><mml:mfenced close="]" open="[" separators="|"><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:msub><mml:mo mathvariant="bold">,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo mathvariant="bold">,</mml:mo><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mrow><mml:mi mathvariant="italic">ik</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo mathvariant="bold">,</mml:mo><mml:msub><mml:mi mathvariant="bold">d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:mi>&#x003a3;</mml:mi></mml:mrow></mml:mfenced><mml:mo>~</mml:mo><mml:mtext mathvariant="italic">Normal</mml:mtext><mml:mfenced close=")" open="(" separators=","><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>+</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi>k</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfrac><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msubsup><mml:mfenced close="]" open="["><mml:mrow><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi>ij</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub></mml:mrow></mml:mfenced></mml:mrow><mml:mrow><mml:mfrac><mml:mi>k</mml:mi><mml:mrow><mml:mn>2</mml:mn><mml:mfenced close=")" open="("><mml:mrow><mml:mi>k</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:mfenced></mml:mrow></mml:mfrac><mml:msup><mml:mi>&#x003c3;</mml:mi><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfenced><mml:mo>.</mml:mo></mml:math><graphic xlink:href="12874_2019_829_Article_Equb.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par24">In a fixed effect model, the <italic>&#x003b4;</italic><sub><italic>ik</italic></sub>&#x02009;&#x02019;&#x02009;s are treated as &#x0201c;fixed&#x0201d; (to use frequentist jargon) and are defined as <inline-formula id="IEq6"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\delta}_{ik}={d}_{\left({t}_{i1},{t}_{ik}\right)}={d}_{\left(1,{t}_{ik}\right)}-{d}_{\left(1,{t}_{i1}\right)} $$\end{document}</tex-math><mml:math id="M18" display="inline"><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq6.gif"/></alternatives></inline-formula> with <italic>d</italic><sub>(1,&#x02009;1)</sub>&#x02009;=&#x02009;0. Independent priors are used on <italic>d</italic><sub>(1,&#x02009;2)</sub>, &#x02026;. ,<italic>d</italic><sub>(1,&#x02009;<italic>T</italic>)</sub> and <italic>&#x003bc;</italic><sub>1</sub>, &#x02026;, <italic>&#x003bc;</italic><sub><italic>M</italic></sub>. In both the fixed and random-effects model, the posterior quantities of interest are all the mean treatment contrasts <inline-formula id="IEq7"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)} $$\end{document}</tex-math><mml:math id="M20" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq7.gif"/></alternatives></inline-formula> which can be determined from <italic>d</italic><sub>(1,&#x02009;2)</sub>, &#x02026;. ,<italic>d</italic><sub>(1,&#x02009;<italic>T</italic>)</sub> through the transitivity relation <inline-formula id="IEq8"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)}={d}_{\left(1,{t}_{ik}\right)}-{d}_{\left(1,{t}_{i1}\right)}. $$\end{document}</tex-math><mml:math id="M22" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub><mml:mo>.</mml:mo></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq8.gif"/></alternatives></inline-formula></p></sec><sec id="Sec9"><title>Meta-regression</title><p id="Par25">Let <italic>x</italic><sub><italic>ik</italic></sub> be a continuous covariate available in arms <italic>k</italic>&#x000a0;=&#x02009;1, &#x02026;, <italic>a</italic><sub><italic>i</italic></sub> of studies <italic>i</italic>&#x000a0;=&#x02009;1, &#x02026;, <italic>M</italic>. Network meta-regression is implemented in BUGSnet via the linear model
<disp-formula id="Equc"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\theta}_{ik}={\mu}_i+{\delta}_{ik}+{\beta}_{\left({t}_{i1},{t}_{ik}\right)}\left({x}_{ik}-\overline{x}\right), $$\end{document}</tex-math><mml:math id="M24" display="block"><mml:msub><mml:mi>&#x003b8;</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003bc;</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003b4;</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msub><mml:mi>x</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub><mml:mo>&#x02212;</mml:mo><mml:mover accent="true"><mml:mi>x</mml:mi><mml:mo stretchy="true">&#x000af;</mml:mo></mml:mover></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:math><graphic xlink:href="12874_2019_829_Article_Equc.gif" position="anchor"/></alternatives></disp-formula>where <inline-formula id="IEq9"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \overline{x} $$\end{document}</tex-math><mml:math id="M26" display="inline"><mml:mover accent="true"><mml:mi>x</mml:mi><mml:mo stretchy="true">&#x000af;</mml:mo></mml:mover></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq9.gif"/></alternatives></inline-formula> is the average of the <italic>x</italic><sub><italic>ik</italic></sub> &#x02019;s across studies and the <inline-formula id="IEq10"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\beta}_{\left({t}_{i1},{t}_{ik}\right)}={\beta}_{\left(1,{t}_{ik}\right)}-{\beta}_{\left(1,{t}_{i1}\right)} $$\end{document}</tex-math><mml:math id="M28" display="inline"><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq10.gif"/></alternatives></inline-formula> are regression coefficients for the effect of the covariate on the relative effect of treatments <italic>t</italic><sub><italic>i1</italic></sub> and <italic>t</italic><sub><italic>ik</italic></sub>, with <italic>&#x003b2;</italic><sub>(1,&#x02009;1)</sub>&#x02009;=&#x02009;&#x02026;&#x02009;=&#x02009;<italic>&#x003b2;</italic><sub>(<italic>T</italic>,&#x02009;<italic>T</italic>)</sub>&#x02009;=&#x02009;0. A prior is used on <italic>&#x003b2;</italic><sub>(1,&#x02009;2)</sub>, &#x02026;, <italic>&#x003b2;</italic><sub>(1,&#x02009;<italic>K</italic>)</sub>. When conducting a meta-regression analysis, the output plots and tables described in the Output section (league heat plot, league table, etc.) can also be produced but the user will need to specify a value for the covariate at which to produce treatment comparisons. Those treatment comparisons are calculated internally within BUGSnet by computing posterior quantities of interest at a specific covariate value <italic>x</italic><sup>0</sup> as <inline-formula id="IEq11"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)}+{\beta}_{\left({t}_{i1},{t}_{ik}\right)}\left({x}^0-\overline{x}\right), $$\end{document}</tex-math><mml:math id="M30" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mfenced close=")" open="("><mml:mrow><mml:msup><mml:mi>x</mml:mi><mml:mn>0</mml:mn></mml:msup><mml:mo>&#x02212;</mml:mo><mml:mover accent="true"><mml:mi>x</mml:mi><mml:mo stretchy="true">&#x000af;</mml:mo></mml:mover></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq11.gif"/></alternatives></inline-formula> and using the transitivity relations <inline-formula id="IEq12"><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)}={d}_{\left(1,{t}_{ik}\right)}-{d}_{\left(1,{t}_{i1}\right)} $$\end{document}</tex-math><mml:math id="M32" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq12.gif"/></alternatives></inline-formula> and <inline-formula id="IEq13"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\beta}_{\left({t}_{i1},{t}_{ik}\right)}={\beta}_{\left(1,{t}_{ik}\right)}-{\beta}_{\left(1,{t}_{i1}\right)}. $$\end{document}</tex-math><mml:math id="M34" display="inline"><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>&#x003b2;</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub><mml:mo>.</mml:mo></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq13.gif"/></alternatives></inline-formula></p></sec><sec id="Sec10"><title>Choice of priors</title><p id="Par26">By default, BUGSnet implements the vague priors described in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>. Our choice of priors was based on the justifications made by van Valkenhoef et al. (2012) [<xref ref-type="bibr" rid="CR15">15</xref>] which allow a prior variance to be easily calculated from the data without any user input. These priors are the same as the ones implemented in the GeMTC R package [<xref ref-type="bibr" rid="CR15">15</xref>]. The user also has the option within the nma.model() function to specify their own prior which is useful for conducting sensitivity analyses, namely for the comparison of prior distributions on the random effects standard deviation, <italic>&#x003c3;</italic>, to insure that they do not have a significant effect on the posterior estimates.
<table-wrap id="Tab3"><label>Table 3</label><caption><p>Priors implemented by default in BUGSnet</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th colspan="2">Consistency Model</th><th colspan="2">Inconsistency Model</th></tr><tr><th>Parameters</th><th>Random effect</th><th>Fixed effect</th><th>Random effect</th><th>Fixed effect</th></tr></thead><tbody><tr><td><italic>&#x003bc;</italic><sub>1</sub>, &#x02026;, <italic>&#x003bc;</italic><sub><italic>M</italic></sub></td><td colspan="4"><p>iid N(0,(15<italic>u</italic>)<sup>2</sup>)</p><p>Except when a log link is used with a binomial family, in which case</p><p><italic>&#x003bc;</italic><sub><italic>i</italic></sub>&#x02009;=&#x02009;log(<italic>p</italic><sub><italic>i</italic></sub>), <italic>p</italic><sub><italic>i</italic></sub>~ iid U(0,1) as per Warn et al. [<xref ref-type="bibr" rid="CR23">23</xref>]</p></td></tr><tr><td><italic>d</italic><sub>1, 2</sub>, &#x02026;. ,<italic>d</italic><sub>1, <italic>T</italic></sub></td><td colspan="2">iid N(0,(15<italic>u</italic>)<sup>2</sup>)</td><td colspan="2">NA</td></tr><tr><td><p><italic>d</italic><sub>1, 2</sub>, &#x02026;. ,<italic>d</italic><sub>1, <italic>T</italic></sub>, &#x02026;,</p><p>&#x000a0;<italic>d</italic><sub><italic>T</italic>&#x02009;&#x02212;&#x02009;2, <italic>T</italic>&#x02009;&#x02212;&#x02009;1</sub>, <italic>d</italic><sub><italic>T</italic>&#x02009;&#x02212;&#x02009;2, <italic>T</italic></sub>, <italic>d</italic><sub><italic>T</italic>&#x02009;&#x02212;&#x02009;1, <italic>T</italic></sub></p></td><td colspan="2">NA</td><td colspan="2">iid N(0,(15<italic>u</italic>)<sup>2</sup>)</td></tr><tr><td><italic>&#x003c3;</italic></td><td>U(0,<italic>u</italic>)</td><td>NA</td><td>U(0,<italic>u</italic>)</td><td>NA</td></tr><tr><td><p><italic>&#x003b2;</italic><sub>(1,&#x02009;2)</sub>, &#x02026;, <italic>&#x003b2;</italic><sub>(1,&#x02009;<italic>K</italic>)</sub></p><p>(meta-regression only)</p></td><td colspan="4"><p>Unrelated: iid t(0, <italic>u</italic><sup>2</sup>, df&#x02009;=&#x02009;1)</p><p>Exchangeable: iid N(<italic>b</italic>, <italic>&#x003b3;</italic><sup>2</sup>), <italic>b</italic>~ t(0, <italic>u</italic><sup>2</sup>, df&#x02009;=&#x02009;1), <italic>&#x003b3;</italic>~<italic>U</italic>(0,&#x02009;<italic>u</italic>)</p><p>Equal: <italic>&#x003b2;</italic><sub>2</sub>&#x02009;=&#x02009;&#x02026;&#x02009;=&#x02009;<italic>&#x003b2;</italic><sub><italic>T</italic></sub>&#x02009;=&#x02009;<italic>B</italic>, <italic>B</italic>~ t(0, <italic>u</italic><sup>2</sup>, df&#x02009;=&#x02009;1)</p></td></tr></tbody></table></table-wrap></p><p id="Par27">The variances 15<italic>u</italic> are taken from van Valkenhoef (2012) et al., where <italic>u</italic> is the largest maximum likelihood estimator of treatment differences on the linear scale in single trials [<xref ref-type="bibr" rid="CR15">15</xref>]. Note that t denotes Student&#x02019;s t distribution with parameters: location, variance and degrees of freedom.</p></sec></sec><sec id="Sec11"><title>Model assessment</title><p id="Par28">After the NMA model has been run, guidelines recommend that one assesses the convergence and fit of the model [<xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR12">12</xref>]. In BUGSnet, convergence can be assessed using trace plots and other convergence diagnostics produced by the nma.diag() function. Lastly, the fit of the model and the identification of potential outliers can be carried out using the nma.fit() function which will produce a plot of the leverage values and also display the corresponding effective number of parameters, total residual deviance, and deviance information criterion (DIC). These latter values can be used to help determine or justify model choice when considering two or more competing models (e.g. between a fixed- or random-effects model) and to help identify data points that contribute heavily to the DIC and/or that are influential.</p></sec><sec id="Sec12"><title>Consistency</title><p id="Par29">A fundamental assumption of an NMA is the assumption of transitivity [<xref ref-type="bibr" rid="CR2">2</xref>]. Under this assumption, one assumes that one can estimate the difference in the effect of two treatments by subtracting the difference in the effects of the two treatments relative to a common comparator as follows: <inline-formula id="IEq14"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)}={d}_{\left(1,{t}_{ik}\right)}-{d}_{\left(1,{t}_{i1}\right)} $$\end{document}</tex-math><mml:math id="M36" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub><mml:mspace width="0.25em"/></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq14.gif"/></alternatives></inline-formula>[<xref ref-type="bibr" rid="CR2">2</xref>]. Aside from exploring clinical heterogeneity of treatment definitions and modifiers within the network using the data.plot() function, one can also detect violations of the assumption of transitivity by examining statistical consistency within the network. Statistical consistency refers to the statistical agreement between indirect and direct evidence within an evidence network [<xref ref-type="bibr" rid="CR2">2</xref>]. Evidence of inconsistency would indicate a violation of the transitivity assumption. As noted by Efthimiou et al. (2015), statistical consistency can only be explored if there are closed loops within the network [<xref ref-type="bibr" rid="CR2">2</xref>]. A variety of methods have been proposed to assess consistency within a network meta-analysis [<xref ref-type="bibr" rid="CR2">2</xref>, <xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR25">25</xref>]. Such methods are often categorized as being &#x0201c;global&#x0201d; or &#x0201c;local&#x0201d; depending upon whether they examine inconsistency within the entire network or within particular segments thereof [<xref ref-type="bibr" rid="CR2">2</xref>]. BUGSnet currently implements the inconsistency model (or unrelated mean effects model) as described in the NICE-DSU TSD 4 [<xref ref-type="bibr" rid="CR26">26</xref>]. An inconsistency model is an NMA model similar to the consistency models described above but transitivity <inline-formula id="IEq15"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)}={d}_{\left(1,{t}_{ik}\right)}-{d}_{\left(1,{t}_{i1}\right)} $$\end{document}</tex-math><mml:math id="M38" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mn>1</mml:mn><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mfenced></mml:msub></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq15.gif"/></alternatives></inline-formula> is not assumed. Instead, independent priors are defined on each of the <inline-formula id="IEq16"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {d}_{\left({t}_{i1},{t}_{ik}\right)} $$\end{document}</tex-math><mml:math id="M40" display="inline"><mml:msub><mml:mi>d</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>t</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:msub><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ik</mml:mi></mml:msub></mml:mfenced></mml:msub></mml:math><inline-graphic xlink:href="12874_2019_829_Article_IEq16.gif"/></alternatives></inline-formula> &#x02019;s. Inconsistency models therefore have more parameters than consistency models, which needs to be weighted against how well they fit the data compared to the consistency model to determine if there is evidence of inconsistency. The inconsistency model can be specified using the type&#x02009;=&#x02009;"inconsistency" option in the nma.model(). To examine inconsistency at the global level, the fit of the inconsistency model can be compared against a model in which consistency is assumed using the nma.fit() function and comparing the DICs. Local inconsistency can be explored on the leverage plots produced by nma.fit() and also using the nma.compare() function which produces a plot comparing the posterior mean deviance of each data point between the consistency and the inconsistency model.</p><p id="Par30">We chose to implement the inconsistency model method for assessing inconsistency in BUGSnet because it easily handles different network structures and multi-arm trials, which is not the case with other methods for assessing inconsistency such as the Bucher method [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR27">27</xref>]. More options for assessing inconsistency at both the global and local levels will be considered in further BUGSnet releases.</p></sec><sec id="Sec13"><title>Output</title><p id="Par31">We provide several functions for displaying the results of the NMA in both graphical and tabular formats (league tables, league heat plots, SUCRA plots, SUCRA tables, rankograms and forest plots) to satisfy current guidelines. With respect to plotting the magnitude and uncertainty of the treatment effects, users can the use the nma.forest() function to graph the effect estimates from the NMA against a comparator specified by the user. The effect estimates can also be presented within a league table using the nma.league() function. An important presentation feature in BUGSnet, particularly for large league tables, is that the user can specify an option to colour and arrange the league table into a heatmap that highlights the magnitude of the effect estimates. Users can also graphically display the probability of the ranking of each treatment within a surface under the cumulative ranking curve (SUCRA) plot which can be specified within the nma.rank() function. This function can also be used to present treatment ranks in a tabular format, extract SUCRA values and produce a rankogram. All of the plots produced by these three reporting functions are produced with the ggplot2 package. As such, the user can easily customize the plots (e.g. change the background, add a title) by adding layers using the + command. Also, for reporting relative treatment effects the user can specify whether they want to plot the results on the the linear scale (log scale) or the original scale.</p><p id="Par32">When meta-regression is conducted, the nma.rank(), nma.forest() and nma.league() functions allow the user to specify for which value of the covariate they wish to present the results. Even though the covariate is centered for meta-regression, the user does not have to do any conversion and results are provided on the original non-centered scale. Another function, nma.regplot() outputs a plot of relative treatment effects on the linear scale across the range of covariate values used in the meta-regression, as in the NICE-DSU TSD 3 [<xref ref-type="bibr" rid="CR28">28</xref>].</p><p id="Par33">It is sometimes recommended that users present results from the direct evidence where available [<xref ref-type="bibr" rid="CR29">29</xref>]. To accommodate this, we have also incorporated the pma() function within BUGSnet which will perform pairwise meta-analysis using the meta package in R and automatically output the results into a tabular format [<xref ref-type="bibr" rid="CR30">30</xref>].</p></sec></sec><sec id="Sec14"><title>Results</title><p id="Par34">The following is a demonstration of some of the functions contained within BUGSnet (Table <xref rid="Tab1" ref-type="table">1</xref>) and some of the possible outputs. To accomplish this task, we have recreated an analysis of a dichotomous outcome where studies had variable follow-up times described in the NICE-DSU technical support document 2 (referred to as &#x0201c;Data Example 3&#x0201d;) [<xref ref-type="bibr" rid="CR17">17</xref>]. The BUGSnet code used to produce this analysis is available in the vignette titled <italic>survival</italic> in the BUGSnet documentation, and appended as a supplement to this article (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). Additional outputs are presented in the vignette as well as a more detailed description of how to conduct and report network meta-analysis, which is only presented here in brief.</p><p id="Par35">The evidence network used in this analysis consists of 22 randomized trials (including multi-arm trials) that examined the effects of six antihypertensive treatments on the risk of developing diabetes [<xref ref-type="bibr" rid="CR31">31</xref>]. The outcome for this data is the number of new diabetes cases observed during the trial period. The data is organized in the long format (i.e. one row per treatment arm), with variables indicating the study ID, the treatment ID, the number of patients, the number of events, and the mean age (and standard deviation) of participants for each treatment arm (see Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>). The results of our package are concordant with those reported in the TSD as well results obtained with GeMTC (code and outputs provided as supplement to this article (see Additional&#x000a0;files&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>, <xref rid="MOESM3" ref-type="media">3</xref>, <xref rid="MOESM4" ref-type="media">4</xref> and <xref rid="MOESM5" ref-type="media">5</xref>) and NetMetaXL.
<table-wrap id="Tab4"><label>Table 4</label><caption><p>Organization of diabetes dataset used to demonstrate the capabilities of BUGSnet</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Study ID</th><th>Treatment</th><th>Number of Participants</th><th>Number of Events</th><th>Age (Mean)</th><th>Age (SD)</th></tr></thead><tbody><tr><td>MRC-E</td><td>Diuretic</td><td>1081</td><td>43</td><td>60.7</td><td>14.3</td></tr><tr><td>MRC-E</td><td>Placebo</td><td>2213</td><td>34</td><td>59.2</td><td>13.1</td></tr><tr><td>MRC-E</td><td>blocker</td><td>1102</td><td>37</td><td>60.2</td><td>14.0</td></tr><tr><td>EWPH</td><td>Diuretic</td><td>416</td><td>29</td><td>59.0</td><td>15.2</td></tr><tr><td>EWPH</td><td>Placebo</td><td>424</td><td>20</td><td>57.0</td><td>14.8</td></tr><tr><td>.</td><td/><td/><td/><td/><td/></tr><tr><td>.</td><td/><td/><td/><td/><td/></tr><tr><td>.</td><td/><td/><td/><td/><td/></tr><tr><td>VALUE</td><td>CCB</td><td>5074</td><td>845</td><td>56.4</td><td>13.1</td></tr><tr><td>VALUE</td><td>ARB</td><td>5087</td><td>690</td><td>57.8</td><td>13.0</td></tr></tbody></table></table-wrap></p><sec id="Sec15"><title>Data preparation, description of network and homogeneity</title><p id="Par36">After the data was prepared using the data.prep() function, the net.plot() and the net.tab() functions were used to describe the network of studies in a graphical (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>) and tabular format respectively (Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref>). As previously discussed, the assumptions of network meta-analysis will be violated when an effect modifier is heterogeneously distributed throughout an evidence base [<xref ref-type="bibr" rid="CR20">20</xref>]. Prior to conducting the network meta-analysis, analysts can use the data.plot() function to examine the distribution of an effect modifier within the network. The determination of whether or not a variable is an effect modifier and whether or not the observed differences in its distribution are clinically meaningful is determined according to expert opinion and prior evidence. To demonstrate this function, we have simulated a patient characteristic that may modify the treatment effect (i.e. the age of participants). To mimic a lack of reporting, we have omitted the standard deviation for a few of the studies. As observed in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, the mean age of participants within each treatment arm (the individual points) is similar to the overall mean age of participants within the evidence base (the red dotted line). According to the standard deviation (the +/&#x02212; error bars), the variability of ages within each treatment arm appear to be similar as well (where available). Based on this analysis, one would conclude that there is no meaningful heterogeneity in the distribution of age. This analysis would be repeated for all potentially important effect modifiers identified a priori by clinical opinion and a review of previous studies. If no heterogeneity is detected, then one may proceed to conducting the network meta-analysis. If heterogeneity is detected, one can attempt to adjust for imbalances by using meta-regression (if there are an adequate number of studies) or by using alternative statistical techniques that leverage individual patient data (e.g. matching-adjusted indirect comparison or simulated treatment comparison) [<xref ref-type="bibr" rid="CR20">20</xref>].
<fig id="Fig1"><label>Fig. 1</label><caption><p>Network plots produced by the net.plot() Function in BUGSnet</p></caption><graphic xlink:href="12874_2019_829_Fig1_HTML" id="MO1"/></fig>
<table-wrap id="Tab5"><label>Table 5</label><caption><p>Network characteristics produced by the net.tab() function in BUGSnet</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Characteristic</th><th>Value</th></tr></thead><tbody><tr><td>Number of Interventions</td><td>6</td></tr><tr><td>Number of Studies</td><td>22</td></tr><tr><td>Total Number of Patients in Network</td><td>154,176</td></tr><tr><td>Total Possible Pairwise Comparisons</td><td>15</td></tr><tr><td>Total Number of Pairwise Comparisons With Direct Data</td><td>14</td></tr><tr><td>Is the network connected?</td><td>TRUE</td></tr><tr><td>Number of Two-arm Studies</td><td>18</td></tr><tr><td>Number of Multi-Arms Studies</td><td>4</td></tr><tr><td>Total Number of Events in Network</td><td>10,962</td></tr><tr><td>Number of Studies With No Zero Events</td><td>22</td></tr><tr><td>Number of Studies With At Least One Zero Event</td><td>0</td></tr><tr><td>Number of Studies with All Zero Events</td><td>0</td></tr><tr><td>Mean person follow up time</td><td>4.06</td></tr></tbody></table></table-wrap>
<fig id="Fig2"><label>Fig. 2</label><caption><p>Graph of patient characteristic by treatment using the data.plot() function in BUGSnet</p></caption><graphic xlink:href="12874_2019_829_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec16"><title>Network meta-analysis</title><p id="Par38">We conducted an NMA on the Diabetes dataset by fitting a generalized linear model with a complementary log-log link function and binomial likelihood function to account for the dichotomous outcome and differing follow-up times between studies, which was specified through the use of nma.model(). To be consistent with the NICE-DSU technical support document, we specified a burn-in of 50,000 iterations followed by 100,000 iterations with 10,000 adaptations in the nma.run() function. We compared the fit of both a fixed- and random-effects model. According to a visual examination of the leverage plots and comparison of the DIC values produced by the nma.fit(), the random effects model would be preferred over the fixed effects model for this particular dataset because the DIC value is lower and because there are fewer outliers in the leverage plot (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>).
<fig id="Fig3"><label>Fig. 3</label><caption><p>Leverage plots and fit statistics produced by the nma.fit() Function in BUGSnet</p></caption><graphic xlink:href="12874_2019_829_Fig3_HTML" id="MO3"/></fig></p></sec><sec id="Sec17"><title>Output</title><p id="Par39">We present results from the generalized linear model that we previously fit to the Diabetes dataset. As visualized in the SUCRA plot obtained from nma.rank(), the angiotensin-receptor blockers&#x02019; (ARB) curve is consistently above the other treatments&#x02019; curves suggesting that it is the most beneficial treatment with respect to the outcome among the treatments included in the Diabetes evidence network (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). The effect estimates and credible intervals produced by the foregoing model are displayed in a league heat plot (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>) obtained using nma.league(). In Fig. <xref rid="Fig5" ref-type="fig">5</xref>, one can see that the difference between ARB and other treatments are all statistically significant at the 95% level except for the ACE inhibitor and Placebo treatments.
<fig id="Fig4"><label>Fig. 4</label><caption><p>SUCRA plot produced by the nma.rank() Function in BUGSnet</p></caption><graphic xlink:href="12874_2019_829_Fig4_HTML" id="MO4"/></fig>
<fig id="Fig5"><label>Fig. 5</label><caption><p>League Table Heatmap Produced by the nma.league() Function in BUGSnet. Legend: The values in each cell represent the relative treatment effect (and 95% credible intervals) of the treatment on the top, compared to the treatment on the left. A double asterisk indicates statistical significance</p></caption><graphic xlink:href="12874_2019_829_Fig5_HTML" id="MO5"/></fig></p></sec><sec id="Sec18"><title>Consistency</title><p id="Par40">To assess the presence of inconsistency, we fit an NMA model similar to the one previously described but assuming inconsistency. We obtain leverage plots similar to Fig. <xref rid="Fig3" ref-type="fig">3</xref> using the nma.fit() function where we find that the DIC for the consistency model is marginally smaller than for the inconsistency mode. We also use the nma.compare() function to plot the individual data points&#x02019; posterior mean deviance contributions for the consistency model vs the inconsistency model (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>) as recommended in the NICE-DSU TSD 4 [<xref ref-type="bibr" rid="CR26">26</xref>]. Overall, we conclude that there is a lack of evidence to suggest inconsistency within the network.
<fig id="Fig6"><label>Fig. 6</label><caption><p>Posterior mean deviance comparison plot produced by the nma.compare() Function in BUGSnet.][Legend: Each data point represents a treatment arm&#x02019;s contribution to posterior mean deviance for the consistency model (horizontal axis) and the inconsistency model (vertical axis)</p></caption><graphic xlink:href="12874_2019_829_Fig6_HTML" id="MO6"/></fig></p></sec></sec><sec id="Sec19"><title>Discussion</title><p id="Par41">BUGSnet is intended to be used by researchers when assessing the clinical efficacy of multiple treatments within the context of a submission to a journal or a health technology assessment agency. For conducting a contrast-based Bayesian NMA, the two main competing software packages that one may consider are GeMTC [<xref ref-type="bibr" rid="CR15">15</xref>] and NetMetaXL [<xref ref-type="bibr" rid="CR16">16</xref>], for which we have discussed limitations in the introduction. With BUGSnet, we aimed to create a single tool that would compete with the reporting capabilities of NetMetaXL and the analytic capabilities of GeMTC. We have also aimed to provide users with enhanced reporting options not included in existing software such as a function to produce graphs that show the distribution of effect modifiers by trial or by treatment arm and an option to print study names and highlight certain treatment comparisons within the network plot. To help facilitate the use of BUGSnet among new users, we have provided three vignettes (with more vignettes forthcoming) in the R help files that walk users through conducting an NMA using BUGSnet by providing detailed R code and interpretations of the statistical output. Despite these benefits, there are limitations of BUGSnet. BUGSnet is currently limited to exclusively analyzing arm-level data. In contrast, GeMTC can be used to conduct an NMA using entirely arm-level or entirely contrast-level data [<xref ref-type="bibr" rid="CR22">22</xref>]. Relative to GeMTC, another limitation of BUGSnet is that GeMTC currently provides a broader range of methods of assessing inconsistency such as the node-splitting method and a broader range of meta-regression analyses such as subgroup meta-analysis. Since it is implemented within the R environment, some users may find BUGSnet more difficult to use relative to NetMetaXL, which is implemented within Microsoft Excel. At this point, arm-based models [<xref ref-type="bibr" rid="CR22">22</xref>] have not been implemented in BUGSnet; the R package pcnetmeta allows such analyses, although it does not readily provide a complete suite of outputs like BUGSnet. We plan to address these shortcomings in future iterations of BUGSnet and interested users should check the previously mentioned URL for updates.</p><p id="Par42">Network meta-analysis is a rapidly evolving area of research with new methods constantly being developed [<xref ref-type="bibr" rid="CR32">32</xref>]. While the work presented within this paper provides the essential tools required to conduct an NMA in accordance with current guidelines, we plan to implement additional functions and features within this package, based on user feedback, to provide enhanced flexibility and to ensure relevance. Some of the preliminary requests for short-term additions include: 1) additional functions for detecting inconsistency within the network such as the Bucher method [<xref ref-type="bibr" rid="CR27">27</xref>]; 2) an option to allow the user to conduct an NMA using study-level effect estimates; 3) allowing for the relaxation of the proportional hazards assumption when analyzing time-to-event outcomes; 4) allowing for sub-group meta-regression and the inclusion of more than one covariate into the meta-regression model; 5) a function that will automatically generate a report or slide deck presentation of the results that could be saved as a pdf, html or Word.</p><p id="Par43">As detailed in Table <xref rid="Tab1" ref-type="table">1</xref>, the functions contained within BUGSnet can be used to address the items within the PRISMA, ISPOR-AMCP-NPC, and NICE-DSU reporting guidelines that are related to the statistical analysis component of an NMA [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR29">29</xref>]. However, it should be emphasised that there are several non-statistical issues described within these guidelines that BUGSnet is not meant to address such as the identification of the research question, the specification of the study population and competing interventions, the development of the search strategy, and the assessment of the risk of bias within each study [<xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR12">12</xref>]. Researchers are urged to consult with these guidelines when planning their NMA to ensure that all aspects of the NMA, both statistical and non-statistical, adhere to current reporting and methodologic standards.</p></sec><sec id="Sec20"><title>Conclusions</title><p id="Par44">Here we present a new JAGS-based R package for conducting Bayesian NMA called BUGSnet. Relative to existing NMA software, BUGSnet provides an enhanced set of tools for conducting and reporting results according to published best-practice guidelines to help overcome the lack of quality identified within this body of literature. In addition to these features, we have attempted to provide ample documentation describing the use and implementation of BUGSnet to help promote the understanding and uptake of this software. Lastly, we plan to monitor the literature and to implement new features within BUGSnet based on the NMA analyst community to ensure that the package remains up-to-date with the latest advances in this rapidly developing area of research.</p></sec><sec id="Sec21"><title>Availability and requirements</title><p id="Par45">Project name: BUGSnet</p><p id="Par46">Project home page: <ext-link ext-link-type="uri" xlink:href="https://bugsnetsoftware.github.io/">https://bugsnetsoftware.github.io/</ext-link></p><p id="Par47">Operating system(s): Windows 10 v1809 and Mac OS 10.14 (may work on earlier versions but not tested)</p><p id="Par48">Programming language: R</p><p id="Par49">Other requirements: JAGS 4.3.0</p><p id="Par50">License: Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International</p><p id="Par51">Any restriction to use by non-academics: Contact authors for non-academic use.</p></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec22"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12874_2019_829_MOESM1_ESM.html"><caption><p><bold>Additional file 1.</bold> Vignette on network meta-analysis of survival data. A vignette detailing how to obtain the outputs in the Results section using BUGSnet version 1.0.2. The vignette includes all the necessary R code as well as additional outputs and explanations that were not presented in this manuscript for the sake of brevity.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12874_2019_829_MOESM2_ESM.r"><caption><p><bold>Additional file 2.</bold> R code to compare BUGSnet with GeMTC for the analysis of the diabetes data.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12874_2019_829_MOESM3_ESM.xlsx"><caption><p><bold>Additional file 3.</bold> Diabetes dataset to accompany the R code in Additional file <xref rid="MOESM2" ref-type="media">2</xref>.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12874_2019_829_MOESM4_ESM.csv"><caption><p><bold>Additional file 4.</bold> BUGSnet league table obtained from the R code in Additional file <xref rid="MOESM2" ref-type="media">2</xref>.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12874_2019_829_MOESM5_ESM.csv"><caption><p><bold>Additional file 5.</bold> GeMTC league table obtained from the R code in Additional file <xref rid="MOESM2" ref-type="media">2</xref>.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>ISPOR-AMCP-NPA</term><def><p id="Par4">International Society for Pharmacoeconomics and Outcomes Research - Academy of Managed Care Pharmacy - National Pharmaceutical Council</p></def></def-item><def-item><term>ITC</term><def><p id="Par58">Indirect Treatment Comparisons</p></def></def-item><def-item><term>JAGS</term><def><p id="Par5">Just Another Gibbs Sampler</p></def></def-item><def-item><term>NICE-DSU</term><def><p id="Par6">National Institute for Health and Care Excellence Decision Support Unit</p></def></def-item><def-item><term>NMA</term><def><p id="Par57">Network Meta-Analysis</p></def></def-item><def-item><term>PRISMA</term><def><p id="Par7">Preferred Reporting Items for Systematic Reviews and Meta-Analysis</p></def></def-item><def-item><term>SUCRA</term><def><p id="Par8">Surface Under the Cumulative Ranking Curve</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s12874-019-0829-2.</p></sec><ack><title>Acknowledgements</title><p>We would like to thank the two reviewers for their feedback which helped improve this manuscript. We would also like to thank the attendees of our 2019 CADTH Symposium workshop and UBC Network Meta-Analysis Workshop for providing valuable feedback regarding the implementation and ease of use of BUGSnet. AB acknowledges the support of a start-up grant from the University of Waterloo and DBo acknowledges the support of the Barrie I Strafford Doctoral Scholarship for Interdisciplinary Studies on Aging.</p></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>All five authors (AB, DBo, PA, JS and DBr) contributed to the conception and design of the work. AB designed the software architecture. AB, DBo and JS coded the software. DBo and AB drafted the first version of the manuscript and all five authors (AB, DBo, PA, JS and DBr) substantially revised it. All five authors (AB, DBo, PA, JS and DBr) have read and approved the manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>The author(s) received no financial support for the research, authorship, and/or publication of this article.</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>All of the datasets and material contained within the manuscript can be accessed within the BUGSnet package via the BUGSnet homepage: <ext-link ext-link-type="uri" xlink:href="https://bugsnetsoftware.github.io/">https://bugsnetsoftware.github.io/</ext-link></p></notes><notes><title>Ethics approval and consent to participate</title><p id="Par52">Not applicable</p></notes><notes><title>Consent for publication</title><p id="Par53">Not applicable</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p id="Par54">AB and DBo are consultants for Lighthouse Outcomes. JS is employed by Lighthouse Outcomes. PA and DBr are partners at Lighthouse Outcomes.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jansen</surname><given-names>JP</given-names></name><name><surname>Fleurence</surname><given-names>R</given-names></name><name><surname>Devine</surname><given-names>B</given-names></name><name><surname>Itzler</surname><given-names>R</given-names></name><name><surname>Barrett</surname><given-names>A</given-names></name><name><surname>Hawkins</surname><given-names>N</given-names></name><name><surname>Lee</surname><given-names>K</given-names></name><name><surname>Boersma</surname><given-names>C</given-names></name><name><surname>Annemans</surname><given-names>L</given-names></name><name><surname>Cappelleri</surname><given-names>JC</given-names></name></person-group><article-title>Interpreting indirect treatment comparisons and network meta-analysis for health-care decision making: report of the ISPOR task force on indirect treatment comparisons good research practices: part 1</article-title><source>Value Health</source><year>2011</year><volume>14</volume><issue>4</issue><fpage>417</fpage><lpage>428</lpage><pub-id pub-id-type="doi">10.1016/j.jval.2011.04.002</pub-id><pub-id pub-id-type="pmid">21669366</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Efthimiou</surname><given-names>O</given-names></name><name><surname>Debray</surname><given-names>TP</given-names></name><name><surname>van Valkenhoef</surname><given-names>G</given-names></name><name><surname>Trelle</surname><given-names>S</given-names></name><name><surname>Panayidou</surname><given-names>K</given-names></name><name><surname>Moons</surname><given-names>KG</given-names></name><name><surname>Reitsma</surname><given-names>JB</given-names></name><name><surname>Shang</surname><given-names>A</given-names></name><name><surname>Salanti</surname><given-names>G</given-names></name></person-group><article-title>GetReal in network meta-analysis: a review of the methodology</article-title><source>Res Synth Methods</source><year>2016</year><volume>7</volume><issue>3</issue><fpage>236</fpage><lpage>263</lpage><pub-id pub-id-type="doi">10.1002/jrsm.1195</pub-id><pub-id pub-id-type="pmid">26754852</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lee</surname><given-names>AW</given-names></name></person-group><article-title>Review of mixed treatment comparisons in published systematic reviews shows marked increase since 2009</article-title><source>J Clin Epidemiol</source><year>2014</year><volume>67</volume><issue>2</issue><fpage>138</fpage><lpage>143</lpage><pub-id pub-id-type="doi">10.1016/j.jclinepi.2013.07.014</pub-id><pub-id pub-id-type="pmid">24090930</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bafeta</surname><given-names>A</given-names></name><name><surname>Trinquart</surname><given-names>L</given-names></name><name><surname>Seror</surname><given-names>R</given-names></name><name><surname>Ravaud</surname><given-names>P</given-names></name></person-group><article-title>Reporting of results from network meta-analyses: methodological systematic review</article-title><source>BMJ</source><year>2014</year><volume>348</volume><fpage>g1741</fpage><pub-id pub-id-type="doi">10.1136/bmj.g1741</pub-id><pub-id pub-id-type="pmid">24618053</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hutton</surname><given-names>B</given-names></name><name><surname>Salanti</surname><given-names>G</given-names></name><name><surname>Chaimani</surname><given-names>A</given-names></name><name><surname>Caldwell</surname><given-names>DM</given-names></name><name><surname>Schmid</surname><given-names>C</given-names></name><name><surname>Thorlund</surname><given-names>K</given-names></name><name><surname>Mills</surname><given-names>E</given-names></name><name><surname>Catala-Lopez</surname><given-names>F</given-names></name><name><surname>Turner</surname><given-names>L</given-names></name><name><surname>Altman</surname><given-names>DG</given-names></name><etal/></person-group><article-title>The quality of reporting methods and results in network meta-analyses: an overview of reviews and suggestions for improvement</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><issue>3</issue><fpage>e92508</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0092508</pub-id><pub-id pub-id-type="pmid">24671099</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Song</surname><given-names>F</given-names></name><name><surname>Loke</surname><given-names>YK</given-names></name><name><surname>Walsh</surname><given-names>T</given-names></name><name><surname>Glenny</surname><given-names>AM</given-names></name><name><surname>Eastwood</surname><given-names>AJ</given-names></name><name><surname>Altman</surname><given-names>DG</given-names></name></person-group><article-title>Methodological problems in the use of indirect comparisons for evaluating healthcare interventions: survey of published systematic reviews</article-title><source>BMJ</source><year>2009</year><volume>338</volume><fpage>b1147</fpage><pub-id pub-id-type="doi">10.1136/bmj.b1147</pub-id><pub-id pub-id-type="pmid">19346285</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Donegan</surname><given-names>S</given-names></name><name><surname>Williamson</surname><given-names>P</given-names></name><name><surname>Gamble</surname><given-names>C</given-names></name><name><surname>Tudur-Smith</surname><given-names>C</given-names></name></person-group><article-title>Indirect comparisons: a review of reporting and methodological quality</article-title><source>PLoS One</source><year>2010</year><volume>5</volume><issue>11</issue><fpage>e11054</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0011054</pub-id><pub-id pub-id-type="pmid">21085712</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kovic</surname><given-names>B</given-names></name><name><surname>Zoratti</surname><given-names>MJ</given-names></name><name><surname>Michalopoulos</surname><given-names>S</given-names></name><name><surname>Silvestre</surname><given-names>C</given-names></name><name><surname>Thorlund</surname><given-names>K</given-names></name><name><surname>Thabane</surname><given-names>L</given-names></name></person-group><article-title>Deficiencies in addressing effect modification in network meta-analyses: a meta-epidemiological survey</article-title><source>J Clin Epidemiol</source><year>2017</year><volume>88</volume><fpage>47</fpage><lpage>56</lpage><pub-id pub-id-type="doi">10.1016/j.jclinepi.2017.06.004</pub-id><pub-id pub-id-type="pmid">28603010</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zarin</surname><given-names>W</given-names></name><name><surname>Veroniki</surname><given-names>AA</given-names></name><name><surname>Nincic</surname><given-names>V</given-names></name><name><surname>Vafaei</surname><given-names>A</given-names></name><name><surname>Reynen</surname><given-names>E</given-names></name><name><surname>Motiwala</surname><given-names>SS</given-names></name><name><surname>Antony</surname><given-names>J</given-names></name><name><surname>Sullivan</surname><given-names>SM</given-names></name><name><surname>Rios</surname><given-names>P</given-names></name><name><surname>Daly</surname><given-names>C</given-names></name><etal/></person-group><article-title>Characteristics and knowledge synthesis approach for 456 network meta-analyses: a scoping review</article-title><source>BMC Med</source><year>2017</year><volume>15</volume><issue>1</issue><fpage>3</fpage><pub-id pub-id-type="doi">10.1186/s12916-016-0764-6</pub-id><pub-id pub-id-type="pmid">28052774</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jansen</surname><given-names>JP</given-names></name><name><surname>Trikalinos</surname><given-names>T</given-names></name><name><surname>Cappelleri</surname><given-names>JC</given-names></name><name><surname>Daw</surname><given-names>J</given-names></name><name><surname>Andes</surname><given-names>S</given-names></name><name><surname>Eldessouki</surname><given-names>R</given-names></name><name><surname>Salanti</surname><given-names>G</given-names></name></person-group><article-title>Indirect treatment comparison/network meta-analysis study questionnaire to assess relevance and credibility to inform health care decision making: an ISPOR-AMCP-NPC good practice task force report</article-title><source>Value Health</source><year>2014</year><volume>17</volume><issue>2</issue><fpage>157</fpage><lpage>173</lpage><pub-id pub-id-type="doi">10.1016/j.jval.2014.01.004</pub-id><pub-id pub-id-type="pmid">24636374</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hutton</surname><given-names>B</given-names></name><name><surname>Salanti</surname><given-names>G</given-names></name><name><surname>Caldwell</surname><given-names>DM</given-names></name><name><surname>Chaimani</surname><given-names>A</given-names></name><name><surname>Schmid</surname><given-names>CH</given-names></name><name><surname>Cameron</surname><given-names>C</given-names></name><name><surname>Ioannidis</surname><given-names>JP</given-names></name><name><surname>Straus</surname><given-names>S</given-names></name><name><surname>Thorlund</surname><given-names>K</given-names></name><name><surname>Jansen</surname><given-names>JP</given-names></name><etal/></person-group><article-title>The PRISMA extension statement for reporting of systematic reviews incorporating network meta-analyses of health care interventions: checklist and explanations</article-title><source>Ann Intern Med</source><year>2015</year><volume>162</volume><issue>11</issue><fpage>777</fpage><lpage>784</lpage><pub-id pub-id-type="doi">10.7326/M14-2385</pub-id><pub-id pub-id-type="pmid">26030634</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><mixed-citation publication-type="other">Ades A, Caldwell DM, Reken S, Welton NJ, Sutton AJ, Dias S. NICE DSU technical support document 7: evidence synthesis of treatment efficacy in decision making: a reviewer&#x02019;s checklist. Decision Support Unit London UK. 2012;27905719.</mixed-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Neupane</surname><given-names>B</given-names></name><name><surname>Richer</surname><given-names>D</given-names></name><name><surname>Bonner</surname><given-names>AJ</given-names></name><name><surname>Kibret</surname><given-names>T</given-names></name><name><surname>Beyene</surname><given-names>J</given-names></name></person-group><article-title>Network meta-analysis using R: a review of currently available automated packages</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><issue>12</issue><fpage>e115065</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0115065</pub-id><pub-id pub-id-type="pmid">25541687</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>C</given-names></name><name><surname>Niu</surname><given-names>Y</given-names></name><name><surname>Wu</surname><given-names>J</given-names></name><name><surname>Gu</surname><given-names>H</given-names></name><name><surname>Zhang</surname><given-names>C</given-names></name></person-group><article-title>Software and package applicating for network meta-analysis: a usage-based comparative study</article-title><source>J Evid Based Med</source><year>2018</year><volume>11</volume><issue>3</issue><fpage>176</fpage><lpage>183</lpage><pub-id pub-id-type="doi">10.1111/jebm.12264</pub-id><pub-id pub-id-type="pmid">29266878</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>van Valkenhoef</surname><given-names>G</given-names></name><name><surname>Lu</surname><given-names>G</given-names></name><name><surname>de Brock</surname><given-names>B</given-names></name><name><surname>Hillege</surname><given-names>H</given-names></name><name><surname>Ades</surname><given-names>AE</given-names></name><name><surname>Welton</surname><given-names>NJ</given-names></name></person-group><article-title>Automating network meta-analysis</article-title><source>Res Synth Methods</source><year>2012</year><volume>3</volume><issue>4</issue><fpage>285</fpage><lpage>299</lpage><pub-id pub-id-type="doi">10.1002/jrsm.1054</pub-id><pub-id pub-id-type="pmid">26053422</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brown</surname><given-names>S</given-names></name><name><surname>Hutton</surname><given-names>B</given-names></name><name><surname>Clifford</surname><given-names>T</given-names></name><name><surname>Coyle</surname><given-names>D</given-names></name><name><surname>Grima</surname><given-names>D</given-names></name><name><surname>Wells</surname><given-names>G</given-names></name><name><surname>Cameron</surname><given-names>C</given-names></name></person-group><article-title>A Microsoft-excel-based tool for running and critically appraising network meta-analyses--an overview and application of NetMetaXL</article-title><source>Systematic Reviews</source><year>2014</year><volume>3</volume><fpage>110</fpage><pub-id pub-id-type="doi">10.1186/2046-4053-3-110</pub-id><pub-id pub-id-type="pmid">25267416</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Dias</surname><given-names>S</given-names></name><name><surname>Welton</surname><given-names>NJ</given-names></name><name><surname>Sutton</surname><given-names>AJ</given-names></name><name><surname>Ades</surname><given-names>A</given-names></name></person-group><source>NICE DSU technical support document 2: a generalised linear modelling framework for pairwise and network meta-analysis of randomised controlled trials</source><year>2011</year></element-citation></ref><ref id="CR18"><label>18.</label><mixed-citation publication-type="other">Plummer M: JAGS: a program for analysis of Bayesian graphical models using Gibbs sampling. In: Proceedings of the 3rd international workshop on distributed statistical computing<italic>:</italic> 2003: Vienna; 2003.</mixed-citation></ref><ref id="CR19"><label>19.</label><mixed-citation publication-type="other">Plummer M: JAGS version 4.3.0 user manual. <ext-link ext-link-type="uri" xlink:href="https://sourceforge.net/projects/mcmc-jags/files/Manuals/">https://sourceforge.net/projects/mcmc-jags/files/Manuals/</ext-link>. Accessed 30 Aug 2019.</mixed-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jansen</surname><given-names>JP</given-names></name><name><surname>Naci</surname><given-names>H</given-names></name></person-group><article-title>Is network meta-analysis as valid as standard pairwise meta-analysis? It all depends on the distribution of effect modifiers</article-title><source>BMC Med</source><year>2013</year><volume>11</volume><fpage>159</fpage><pub-id pub-id-type="doi">10.1186/1741-7015-11-159</pub-id><pub-id pub-id-type="pmid">23826681</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Higgins</surname><given-names>JP</given-names></name><name><surname>Thompson</surname><given-names>SG</given-names></name></person-group><article-title>Quantifying heterogeneity in a meta-analysis</article-title><source>Stat Med</source><year>2002</year><volume>21</volume><issue>11</issue><fpage>1539</fpage><lpage>1558</lpage><pub-id pub-id-type="doi">10.1002/sim.1186</pub-id><pub-id pub-id-type="pmid">12111919</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Dias</surname><given-names>Sofia</given-names></name><name><surname>Ades</surname><given-names>A. E.</given-names></name><name><surname>Welton</surname><given-names>Nicky J.</given-names></name><name><surname>Jansen</surname><given-names>Jeroen P.</given-names></name><name><surname>Sutton</surname><given-names>Alexander J.</given-names></name></person-group><source>Network Meta-Analysis for Decision Making</source><year>2018</year><publisher-loc>Chichester, UK</publisher-loc><publisher-name>John Wiley &#x00026; Sons, Ltd</publisher-name></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Warn</surname><given-names>DE</given-names></name><name><surname>Thompson</surname><given-names>SG</given-names></name><name><surname>Spiegelhalter</surname><given-names>DJ</given-names></name></person-group><article-title>Bayesian random effects meta-analysis of trials with binary outcomes: methods for the absolute risk difference and relative risk scales</article-title><source>Stat Med</source><year>2002</year><volume>21</volume><issue>11</issue><fpage>1601</fpage><lpage>1623</lpage><pub-id pub-id-type="doi">10.1002/sim.1189</pub-id><pub-id pub-id-type="pmid">12111922</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Veroniki</surname><given-names>AA</given-names></name><name><surname>Vasiliadis</surname><given-names>HS</given-names></name><name><surname>Higgins</surname><given-names>JP</given-names></name><name><surname>Salanti</surname><given-names>G</given-names></name></person-group><article-title>Evaluation of inconsistency in networks of interventions</article-title><source>Int J Epidemiol</source><year>2013</year><volume>42</volume><issue>1</issue><fpage>332</fpage><lpage>345</lpage><pub-id pub-id-type="doi">10.1093/ije/dys222</pub-id><pub-id pub-id-type="pmid">23508418</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Donegan</surname><given-names>S</given-names></name><name><surname>Williamson</surname><given-names>P</given-names></name><name><surname>D'Alessandro</surname><given-names>U</given-names></name><name><surname>Tudur Smith</surname><given-names>C</given-names></name></person-group><article-title>Assessing key assumptions of network meta-analysis: a review of methods</article-title><source>Res Synth Methods</source><year>2013</year><volume>4</volume><issue>4</issue><fpage>291</fpage><lpage>323</lpage><pub-id pub-id-type="doi">10.1002/jrsm.1085</pub-id><pub-id pub-id-type="pmid">26053945</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Dias</surname><given-names>S</given-names></name><name><surname>Welton</surname><given-names>NJ</given-names></name><name><surname>Sutton</surname><given-names>AJ</given-names></name><name><surname>Caldwell</surname><given-names>DM</given-names></name><name><surname>Lu</surname><given-names>G</given-names></name><name><surname>Ades</surname><given-names>A</given-names></name></person-group><source>NICE DSU technical support document 4: inconsistency in networks of evidence based on randomised controlled trials</source><year>2011</year></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bucher</surname><given-names>HC</given-names></name><name><surname>Guyatt</surname><given-names>GH</given-names></name><name><surname>Griffith</surname><given-names>LE</given-names></name><name><surname>Walter</surname><given-names>SD</given-names></name></person-group><article-title>The results of direct and indirect treatment comparisons in meta-analysis of randomized controlled trials</article-title><source>J Clin Epidemiol</source><year>1997</year><volume>50</volume><issue>6</issue><fpage>683</fpage><lpage>691</lpage><pub-id pub-id-type="doi">10.1016/S0895-4356(97)00049-8</pub-id><pub-id pub-id-type="pmid">9250266</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Dias</surname><given-names>S</given-names></name><name><surname>Sutton</surname><given-names>AJ</given-names></name><name><surname>Welton</surname><given-names>NJ</given-names></name><name><surname>Ades</surname><given-names>A</given-names></name></person-group><source>NICE DSU technical support document 3: heterogeneity: subgroups, meta-regression, bias and bias-adjustment</source><year>2011</year></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jaime Caro</surname><given-names>J</given-names></name><name><surname>Eddy</surname><given-names>DM</given-names></name><name><surname>Kan</surname><given-names>H</given-names></name><name><surname>Kaltz</surname><given-names>C</given-names></name><name><surname>Patel</surname><given-names>B</given-names></name><name><surname>Eldessouki</surname><given-names>R</given-names></name><name><surname>Briggs</surname><given-names>AH</given-names></name></person-group><article-title>Questionnaire to assess relevance and credibility of modeling studies for informing health care decision making: an ISPOR-AMCP-NPC good practice task force report</article-title><source>Value Health</source><year>2014</year><volume>17</volume><issue>2</issue><fpage>174</fpage><lpage>182</lpage><pub-id pub-id-type="doi">10.1016/j.jval.2014.01.003</pub-id><pub-id pub-id-type="pmid">24636375</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schwarzer</surname><given-names>G</given-names></name></person-group><article-title>Meta: an R package for meta-analysis</article-title><source>R News</source><year>2007</year><volume>7</volume><issue>3</issue><fpage>40</fpage><lpage>45</lpage></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Elliott</surname><given-names>WJ</given-names></name><name><surname>Meyer</surname><given-names>PM</given-names></name></person-group><article-title>Incident diabetes in clinical trials of antihypertensive drugs: a network meta-analysis</article-title><source>Lancet</source><year>2007</year><volume>369</volume><issue>9557</issue><fpage>201</fpage><lpage>207</lpage><pub-id pub-id-type="doi">10.1016/S0140-6736(07)60108-1</pub-id><pub-id pub-id-type="pmid">17240286</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nikolakopoulou</surname><given-names>A</given-names></name><name><surname>Mavridis</surname><given-names>D</given-names></name><name><surname>Furukawa</surname><given-names>TA</given-names></name><name><surname>Cipriani</surname><given-names>A</given-names></name><name><surname>Tricco</surname><given-names>AC</given-names></name><name><surname>Straus</surname><given-names>SE</given-names></name><name><surname>Siontis</surname><given-names>GCM</given-names></name><name><surname>Egger</surname><given-names>M</given-names></name><name><surname>Salanti</surname><given-names>G</given-names></name></person-group><article-title>Living network meta-analysis compared with pairwise meta-analysis in comparative effectiveness research: empirical study</article-title><source>BMJ</source><year>2018</year><volume>360</volume><fpage>k585</fpage><pub-id pub-id-type="doi">10.1136/bmj.k585</pub-id><pub-id pub-id-type="pmid">29490922</pub-id></element-citation></ref></ref-list></back></article>