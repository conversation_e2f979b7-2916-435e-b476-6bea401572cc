<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Genome Biol</journal-id><journal-id journal-id-type="iso-abbrev">Genome Biol</journal-id><journal-title-group><journal-title>Genome Biology</journal-title></journal-title-group><issn pub-type="ppub">1474-7596</issn><issn pub-type="epub">1474-760X</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6977301</article-id><article-id pub-id-type="publisher-id">1924</article-id><article-id pub-id-type="doi">10.1186/s13059-019-1924-8</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>A curated benchmark of enhancer-gene interactions for evaluating enhancer-target gene prediction methods</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Moore</surname><given-names>Jill E.</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Pratt</surname><given-names>Henry E.</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Purcaro</surname><given-names>Michael J.</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-3032-7966</contrib-id><name><surname>Weng</surname><given-names>Zhiping</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0742 0364</institution-id><institution-id institution-id-type="GRID">grid.168645.8</institution-id><institution>Program in Bioinformatics and Integrative Biology, </institution><institution>University of Massachusetts Medical School, </institution></institution-wrap>Worcester, MA 01605 USA </aff></contrib-group><pub-date pub-type="epub"><day>22</day><month>1</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>22</day><month>1</month><year>2020</year></pub-date><pub-date pub-type="collection"><year>2020</year></pub-date><volume>21</volume><elocation-id>17</elocation-id><history><date date-type="received"><day>5</day><month>9</month><year>2019</year></date><date date-type="accepted"><day>23</day><month>12</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2020</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold> This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Many genome-wide collections of candidate cis-regulatory elements (cCREs) have been defined using genomic and epigenomic data, but it remains a major challenge to connect these elements to their target genes.</p></sec><sec><title>Results</title><p id="Par2">To facilitate the development of computational methods for predicting target genes, we develop a Benchmark of candidate Enhancer-Gene Interactions (BENGI) by integrating the recently developed Registry of cCREs with experimentally derived genomic interactions. We use BENGI to test several published computational methods for linking enhancers with genes, including signal correlation and the TargetFinder and PEP supervised learning methods. We find that while TargetFinder is the best-performing method, it is only modestly better than a baseline distance method for most benchmark datasets when trained and tested with the same cell type and that TargetFinder often does not outperform the distance method when applied across cell types.</p></sec><sec><title>Conclusions</title><p id="Par3">Our results suggest that current computational methods need to be improved and that BENGI presents a useful framework for method development and testing.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s13059-019-1924-8) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Enhancer</kwd><kwd>Transcriptional regulation</kwd><kwd>Target gene</kwd><kwd>Benchmark</kwd><kwd>Machine learning</kwd><kwd>Genomic interactions</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000051</institution-id><institution>National Human Genome Research Institute</institution></institution-wrap></funding-source><award-id>1U24HG009446-01</award-id><award-id>5U41HG007000</award-id><principal-award-recipient><name><surname>Weng</surname><given-names>Zhiping</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par4">With the rapid increases in genomic and epigenomic data in recent years, our ability to annotate regulatory elements across the human genome and predict their activities in specific cell and tissue types has substantially improved. Widely used approaches integrate multiple epigenetic signals such as chromatin accessibility, histone marks, and transcribed RNAs [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR7">7</xref>] to define collections of regulatory elements that can be used to study regulatory programs in diverse cell types and dissect the genetic variations associated with human diseases [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR8">8</xref>&#x02013;<xref ref-type="bibr" rid="CR11">11</xref>].</p><p id="Par5">To maximize the utility of regulatory elements, one must know which genes they regulate. We recently developed the Registry of candidate cis-Regulatory elements (cCREs), a collection of candidate regulatory genomic regions in humans and mice, by integrating chromatin accessibility (DNase-seq) data and histone mark ChIP-seq data from hundreds of biosamples generated by the ENCODE Consortium (<ext-link ext-link-type="uri" xlink:href="http://screen.encodeproject.org/">http://screen.encodeproject.org</ext-link>). Over 75% of these cCREs have enhancer-like signatures (high chromatin accessibility as measured by a high DNase-seq signal and a high level of the enhancer-specific histone mark H3K27ac) and are located distal (&#x0003e;&#x02009;2&#x02009;kb) to an annotated transcription start site (TSS). For cCREs proximal to a TSS, it may be safe to assume that the TSS corresponds to the target gene, but to annotate the biological function of the TSS-distal cCREs and interpret the genetic variants that they harbor, we need to determine which genes they regulate.</p><p id="Par6">Assigning enhancers to target genes on a genome-wide scale remains a difficult task. While one could assign an enhancer to the closest gene using linear distance, there are many examples of enhancers skipping over nearby genes in favor of more distal targets [<xref ref-type="bibr" rid="CR12">12</xref>]. Experimental assays such as Hi-C and ChIA-PET survey physical interactions between genomic regions [<xref ref-type="bibr" rid="CR13">13</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>], and by overlapping the anchors of these interactions with annotated enhancers and promoters, we can infer regulatory connections. Approaches based on quantitative trait loci (QTL) associate genetic variants in intergenic regions with genes via the variation in their expression levels across multiple individuals in a human population [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR19">19</xref>]. Recently, a single-cell perturbation approach extended this idea [<xref ref-type="bibr" rid="CR20">20</xref>]. However, these assays are expensive to perform and have only been conducted at a high resolution in a small number of cell types. Therefore, we need to rely on computational methods to broadly predict enhancer-gene interactions.</p><p id="Par7">One popular computational method for identifying enhancer-gene interactions is to correlate genomic and epigenomic signals at enhancers and gene promoters across multiple biosamples. This method is based on the assumption that enhancers and genes tend to be active or inactive in the same cell types. The first study to utilize this method linked enhancers with genes by correlating active histone mark signals at enhancers with gene expression across nine cell types [<xref ref-type="bibr" rid="CR1">1</xref>]. Several groups subsequently used similar approaches to link enhancers and genes by correlating various combinations of DNase, histone mark, transcription factor, and gene expression data [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR21">21</xref>&#x02013;<xref ref-type="bibr" rid="CR23">23</xref>]. While these methods successfully identified a subset of biologically relevant interactions, their performance has yet to be systematically evaluated.</p><p id="Par8">Other groups have developed supervised machine-learning methods that train statistical models on sets of known enhancer-gene pairs. Most of these models use epigenomic signals (e.g., histone marks, TFs, DNase) at enhancers, promoters, or intervening windows as input features [<xref ref-type="bibr" rid="CR24">24</xref>&#x02013;<xref ref-type="bibr" rid="CR27">27</xref>]. PEP-motif, on the other hand, uses sequence-based features [<xref ref-type="bibr" rid="CR28">28</xref>]. The performance of these methods has not been systematically evaluated for several reasons. First, different methods use different definitions for enhancers ranging from EP300 peaks [<xref ref-type="bibr" rid="CR26">26</xref>] to chromatin segments [<xref ref-type="bibr" rid="CR27">27</xref>]. Second, these methods use different datasets to define their gold standards, such as ChIA-PET interactions [<xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR26">26</xref>] or Hi-C loops [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR27">27</xref>], along with different methods for generating negative pairs. Finally, many of these methods use a traditional randomized cross-validation scheme, which results in severe overfitting of some supervised models due to overlapping features [<xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR30">30</xref>].</p><p id="Par9">To facilitate the development of target gene prediction methods, we developed a collection of benchmark datasets by integrating the Registry of cCREs with experimentally derived genomic interactions. We then tested several published methods for linking enhancers with genes, including signal correlation and the supervised learning methods TargetFinder and PEP [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. Overall, we found that while TargetFinder was the best-performing method, it was only modestly better than a baseline distance method for most benchmark datasets when trained and tested on the same cell type, and Target Finder often did not outperform the distance method when applied across cell types. Our results suggest that current computational methods need to be improved and that our benchmark presents a useful framework for method development and testing.</p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>A Benchmark of candidate Enhancer-Gene Interactions (BENGI)</title><p id="Par10">To effectively evaluate target gene prediction methods, we curated a Benchmark of candidate Enhancer-Gene Interactions (BENGI) by integrating our predicted enhancers, cCREs with enhancer-like signatures (cCREs-ELS), with 3D chromatin interactions, genetic interactions, and CRISPR/dCAS9 perturbations in a total of 21 datasets across 13 biosamples (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Tables S1 and Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2a). For 3D chromatin interactions, which include ChIA-PET, Hi-C, and CHi-C interactions, we selected all links with one anchor overlapping a distal cCRE-ELS and the other anchor falling within 2&#x02009;kb of a GENCODE-annotated TSS (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b, see &#x0201c;<xref rid="Sec11" ref-type="sec">Methods</xref>&#x0201d;). For approximately three quarters of the total interactions, the anchor of the 3D chromatin interaction overlaps the proximal region of more than one gene, making the assignment of the exact gene target ambiguous. To assess the impact of these potentially ambiguous assignments, we created two versions of each 3D interaction benchmark dataset. In the first, we retained all cCRE-gene links; in the second, we removed links with ends within 2&#x02009;kb of the TSSs of multiple genes (i.e., ambiguous pairs). For genetic interactions (cis-eQTLs) and CRISPR/dCas9 perturbations (crisprQTLs), we paired a cCRE-ELS with a gene if the cCRE overlapped the reported SNP or targeted region (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b). In total, we curated over 162,000 unique cCRE-gene pairs across the 13 biosamples. Because these experimental datasets capture different aspects of enhancer-gene interactions (see statistical analyses in the next section), we retained the cCRE-gene pairs as separate datasets in BENGI.
<fig id="Fig1"><label>Fig. 1</label><caption><p>A benchmark of candidate enhancer-gene interactions (BENGI). <bold>a</bold> Experimental datasets used to curate BENGI interactions categorized by 3D chromatin interactions, genetic interactions, and CRISPR/Cas9 perturbations. <bold>b</bold> Methods of generating cCRE-gene pairs (dashed straight lines in green, shaded green, or red) from experimentally determined interactions or perturbation links (dashed, shaded arcs in red, pink, or gold). Each cCRE-gene pair derived from 3D chromatin interactions (top panel) has a cCRE-ELS (yellow box) intersecting one anchor of a link, and the pair is classified depending on the other anchor of the link: for a positive pair (dashed green line), the other anchor overlaps one or more TSSs of just one gene; for an ambiguous pair (dashed line with gray shading), the other anchor overlaps the TSSs of multiple genes; for a negative pair (dashed red line), the other anchor does not overlap with a TSS. Each cCRE-gene pair derived from genetic interactions or perturbation links (middle and bottom panels) has a cCRE-ELS (yellow box) intersecting an eQTL SNP or a CRISPR-targeted region, and the pair is classified as positive (dashed green line) if the gene is an eQTL or crisprQTL gene, while all the pairs that this cCRE forms with non-eQTL genes that have a TSS within the distance cutoff are considered negative pairs (dashed red line). <bold>c</bold> To reduce potential false positives obtained from 3D interaction data, we implemented a filtering step to remove ambiguous pairs (gray box in <bold>b</bold>) that link cCREs-ELS to more than one gene. This filtering step was not required for assays that explicitly listed the linked gene (eQTLs and crisprQTLs). Additionally, for comparisons between BENGI datasets, we also curated matching sets of interactions with a fixed positive-to-negative ratio. Therefore, a total of four BENGI datasets were curated for each 3D chromatin experiment (A, B, C, D), and two were curated for each genetic interaction and CRISPR/Cas-9 perturbation experiment (A, B). <bold>d</bold> To avoid overfitting of machine-learning algorithms, all cCRE-gene pairs were assigned to cross-validation (CV) groups based on their chromosomal locations. Positive and negative pairs on the same chromosome were assigned to the same CV group, and chromosomes with complementary sizes were assigned to the same CV group so that the groups contained approximately the same number of pairs</p></caption><graphic xlink:href="13059_2019_1924_Fig1_HTML" id="MO1"/></fig></p><p id="Par11">To complement the positive cCRE-gene pairs in each BENGI dataset, we generated negative pairs for each cCRE-ELS by selecting all unpaired genes whose TSS was located within (either upstream or downstream) the 95th percentile distance from all positive cCRE-gene pairs in the dataset (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2a, see &#x0201c;<xref rid="Sec11" ref-type="sec">Methods</xref>&#x0201d;). These distance cutoffs ranged from 120&#x02009;kb (RNAPII ChIA-PET in HeLa) to 1.83&#x02009;Mb (Hi-C in K562). The percentages of positive pairs also varied from 1.8% (Hi-C in K562) to 23.5% (CHi-C in GM12878), and datasets with greater class imbalance (i.e., a smaller percentage of positive pairs) are inherently more challenging for a computational algorithm. To enable the comparison of algorithm performance across datasets, we further created datasets with a fixed ratio of one positive to four negatives for each BENGI dataset by randomly discarding the excess negatives. This strategy, along with the previously mentioned removal of ambiguous 3D chromatin interaction pairs, resulted in four BENGI datasets per ChIA-PET, Hi-C, or CHi-C experiment and two BENGI datasets per eQTL or crisprQTL experiment (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>c, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2a). All pairs with a natural positive-negative ratio were used in our analyses unless otherwise noted.</p><p id="Par12">To facilitate the training and testing of supervised machine-learning algorithms, we then assigned both positive and negative pairs to 12 cross-validation (CV) groups by chromosome such that pairs within the same chromosome were always assigned to the same CV group, while similar sizes were maintained for different CV groups by pairing one large chromosome with one small chromosome (chromCV, see &#x0201c;<xref rid="Sec11" ref-type="sec">Methods</xref>&#x0201d;, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>d). Because GM12878 and other lymphoblastoid cell lines (LCLs) had the most BENGI datasets and have been extensively surveyed by the ENCODE and 1000 Genomes Consortia, we will highlight our analyses on the BENGI datasets from LCLs.</p></sec><sec id="Sec4"><title>Summary statistics of BENGI datasets</title><p id="Par13">We asked whether the various types of chromatin, genetic, and CRISPR experiments might capture different types of enhancer-gene interactions. To answer this question, we carried out several statistical analyses across the BENGI datasets. First, we performed hierarchical clustering of the six BENGI datasets in GM12878/LCLs by the overlap coefficient&#x02014;the number of positive cCRE-gene pairs shared between two datasets divided by the number of positives in the smaller dataset. We obtained two clusters: one comprising the two eQTL datasets and the other comprising the four chromatin interaction datasets (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a). This overall grouping of the datasets was consistent with the characteristics of the experimental techniques (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). Beyond the overall grouping, the two eQTL datasets exhibited higher overlap coefficients with the RNAPII ChIA-PET and CHi-C datasets (0.20&#x02013;0.36) than with the Hi-C and CTCF ChIA-PET datasets (0.01&#x02013;0.05). This reflects the promoter emphasis of the first four techniques, enriching for promoter-proximal interactions. In contrast, Hi-C identifies significantly more distant interactions than the other techniques (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S1a, Wilcoxon rank-sum test <italic>p</italic> value&#x02009;=&#x02009;1.1E&#x02212;223). Additionally, we note that the eQTL and crisprQTL interactions all have maximum distances of 1&#x02009;Mb (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S1a) because the original studies only tested SNPs within 1&#x02009;Mb of each gene.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Characteristics of BENGI datasets. Six datasets in GM12878 or other LCLs were evaluated: RNAPII ChIA-PET (red), CTCF ChIA-PET (orange), Hi-C (green), CHi-C (blue), GEUVADIS eQTLs (purple), and GTEx eQTLs (pink), and the same color scheme is used for all panels. <bold>a</bold> Heatmap depicting the overlap coefficients between positive cCRE-gene pairs in each BENGI dataset. The datasets were clustered using the hclust algorithm, and the clustered datasets are outlined in black. <bold>b</bold> Violin plots depicting the distance distributions of positive cCRE-gene pairs for each BENGI dataset. The 95th percentile of each distribution is indicated by a star and presented above each plot. <bold>c</bold> Violin plots depicting the expression levels of genes in positive cCRE-gene pairs (in transcripts per million, TPM). <bold>d</bold> Violin plots depicting CTCF signal levels at cCREs-ELSs in positive cCRE-gene pairs. A dashed box indicates cCREs-ELS with a signal &#x0003e;&#x02009;5. <bold>e</bold> Distributions of the number of genes positively linked with a cCRE-ELS across datasets</p></caption><graphic xlink:href="13059_2019_1924_Fig2_HTML" id="MO2"/></fig>
<table-wrap id="Tab1"><label>Table 1</label><caption><p>Genomic interaction dataset</p></caption><table frame="hsides" rules="groups"><thead><tr><th colspan="2">Assay</th><th>Reference</th></tr></thead><tbody><tr><td colspan="3">3D chromatin interactions</td></tr><tr><td>&#x02003;Hi-C</td><td>High-resolution in situ Hi-C: identifies chromatin loops anchored by convergent CTCF binding sites</td><td>[<xref ref-type="bibr" rid="CR14">14</xref>]</td></tr><tr><td>&#x02003;RNAPII ChIA-PET</td><td>Chromatin interaction analysis by paired-end tag sequencing targeting RNAPII: identifies chromatin interactions enriched for RNAPII binding</td><td>[<xref ref-type="bibr" rid="CR16">16</xref>]</td></tr><tr><td>&#x02003;CTCF ChIA-PET</td><td>Chromatin interaction analysis by paired-end tag sequencing targeting CTCF: identifies chromatin interactions enriched for CTCF binding</td><td>[<xref ref-type="bibr" rid="CR16">16</xref>]</td></tr><tr><td>&#x02003;CHi-C</td><td>Promoter capture Hi-C: identifies chromatin interactions between promoters and other loci</td><td>[<xref ref-type="bibr" rid="CR17">17</xref>]</td></tr><tr><td colspan="3">Genetic interactions</td></tr><tr><td>&#x02003;eQTLs</td><td>Expression quantitative trait loci: identifies genetic variants correlated with changes of gene expression of individuals in a human population</td><td>[<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR19">19</xref>]</td></tr><tr><td colspan="3">CRISPR/Cas9 perturbations</td></tr><tr><td>&#x02003;crisprQTLs</td><td>Identifies loci that when targeted with CRISPR/Cas9 correlate with changes in gene expression measured in single cells</td><td>[<xref ref-type="bibr" rid="CR20">20</xref>]</td></tr></tbody></table></table-wrap></p><p id="Par14">We then compared the gene expression of the positive pairs among the six GM12878/LCL datasets (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>c). Overall, the genes in the GEUVADIS eQTL pairs exhibited the highest median expression (median&#x02009;=&#x02009;10.9 transcripts per million sequenced reads, or TPM; Wilcoxon rank-sum test <italic>p</italic>&#x02009;=&#x02009;1E&#x02212;3), while the genes in the CHi-C pairs presented the lowest median expression levels (median&#x02009;=&#x02009;0.24 TPM, <italic>p</italic>&#x02009;=&#x02009;7E&#x02212;&#x02009;39). When we removed ambiguous pairs, gene expression increased significantly for all four chromatin interaction datasets (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S1b), suggesting that some of the ambiguous pairs were false positives. We observed similar increases in gene expression upon the removal of ambiguous pairs in other cell types for which we had RNA-seq data (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S1c-e). Without the ambiguous pairs, the RNAPII ChIA-PET pairs showed comparable expression to the GEUVADIS eQTL pairs. The enrichment for RNAPII in the ChIA-PET protocol may preferentially identify interactions that involve higher RNAPII activity and higher gene expression. The K562 crisprQTL pairs presented the highest overall median expression of 26.4 TPM. We expected to observe high expression for the eQTL and crisprQTL datasets because these interactions can only be detected for genes that are expressed in the respective biosamples.</p><p id="Par15">We also observed significant differences in the CTCF ChIP-seq signals at cCREs-ELS between the BENGI datasets: cCREs-ELS in CTCF ChIA-PET pairs and Hi-C pairs showed significantly higher CTCF signals than cCREs-ELS in the other datasets (Wilcoxon rank-sum test <italic>p</italic>&#x02009;&#x0003c;&#x02009;3.7E&#x02212;&#x02009;9, Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>d, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2b). Similarly, these pairs were enriched for components of the cohesin complex such as RAD21 and SMC3 (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2b). This enrichment for CTCF was biologically consistent, as CTCF was the target in the ChIA-PET experiment, and Hi-C loops are enriched for convergent CTCF binding sites [<xref ref-type="bibr" rid="CR14">14</xref>].</p><p id="Par16">Finally, we tallied the number of linked genes for each cCRE-ELS. Across all BENGI datasets, the majority of cCREs-ELS were linked to just one target gene (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>e, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2c). As expected, this trend was more pronounced for 3D chromatin datasets without ambiguous pairs (on average, 84% of cCREs-ELS were paired with only one gene, <italic>p</italic>&#x02009;&#x0003c;&#x02009;3.3E&#x02212;5). With or without ambiguous pairs, a lower percentage of cCREs-ELS in CHi-C pairs was paired with just one gene (19% of all pairs and 55% of unambiguous pairs) than in the other BENGI datasets (<italic>p</italic>&#x02009;&#x0003c;&#x02009;3.1E&#x02212;&#x02009;75). This observation, along with the lower average expression of the linked genes (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>c), suggests that some of the CHi-C pairs were either false positives or captured interactions between cCREs-ELS and genes that are yet to be expressed.</p><p id="Par17">These analyses suggested that the various experimental techniques whose results formed the basis of the BENGI datasets capture different classes of genomic interactions. Because we do not have a complete understanding of which experimental techniques are best able to capture bona fide enhancer-gene interactions, we propose that computational methods (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>) should be evaluated on the entire collection of these BENGI datasets to provide a comprehensive understanding of their performance.
<table-wrap id="Tab2"><label>Table 2</label><caption><p>Computational methods for target gene prediction</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Method</th><th>Description</th><th>Reference</th></tr></thead><tbody><tr><td colspan="3">Unsupervised methods</td></tr><tr><td>&#x02003;Distance</td><td>Ranks pairs by inverse linear distance</td><td/></tr><tr><td>&#x02003;DNase-DNase</td><td>Calculates the Pearson correlation coefficient between the DNase signals at enhancers and promoters across 32 cell-type categories.</td><td>[<xref ref-type="bibr" rid="CR22">22</xref>]</td></tr><tr><td>&#x02003;DNase-expression</td><td>Calculates the Pearson correlation coefficient between the normalized DNase signals at enhancers and normalized gene expression levels measured by microarray across 112 cell types.</td><td>[<xref ref-type="bibr" rid="CR23">23</xref>]</td></tr><tr><td>&#x02003;GeneHancer</td><td>Cell-type agnostic predictions based on co-expression correlations, CHi-C interactions, eQTLs, and genomic distance</td><td>[<xref ref-type="bibr" rid="CR31">31</xref>]</td></tr><tr><td>&#x02003;Average-rank</td><td>Combines the distance and DNase-expression methods by averaging the rank of for each prediction between the two methods</td><td/></tr><tr><td colspan="3">Supervised methods</td></tr><tr><td rowspan="2">&#x02003;PEP-motif</td><td><italic>Features:</italic> frequency of motif instances at enhancers and promoters</td><td rowspan="2">[<xref ref-type="bibr" rid="CR28">28</xref>]</td></tr><tr><td><italic>Classifier:</italic> Gradient boosting (XGB package)</td></tr><tr><td rowspan="2">&#x02003;TargetFinder</td><td><italic>Features:</italic> Cell-type-specific epigenomic signals (ChIP-seq, DNase, CAGE, etc.) at enhancers, promoters, and the intervening window between enhancers and promoters.</td><td rowspan="2">[<xref ref-type="bibr" rid="CR27">27</xref>]</td></tr><tr><td><italic>Classifier:</italic> Gradient boosting (scikit learn)</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec5"><title>A baseline method of target gene prediction using genomic distance</title><p id="Par18">Using the BENGI datasets, we evaluated a simple <italic>closest gene</italic> method for target gene prediction: a cCRE-ELS was assigned to its closest gene in terms of linear distance, computed by subtracting the genomic coordinates of the cCRE and the nearest TSS. All BENGI datasets, despite interaction type, had highly similar ELS-gene distance distributions (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S1f). We tested this method using two gene sets, consisting of all genes or all protein-coding genes annotated by GENCODE V19, by evaluating precision and recall on the basis of each BENGI dataset. The use of protein-coding genes invariably resulted in better performance than the use of all genes (50% better on average over all 21 datasets across cell types; Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2d); thus, we used protein-coding genes for all subsequent analyses with this method.</p><p id="Par19">The <italic>closest gene</italic> method worked best for crisprQTL pairs (precision&#x02009;=&#x02009;0.67 and recall&#x02009;=&#x02009;0.60), followed by ChIA-PET RNAPII pairs (precision&#x02009;=&#x02009;0.66 and recall&#x02009;=&#x02009;0.31 averaged across cell lines). The method performed worst for Hi-C pairs, with an average precision of 0.19 and an average recall of 0.12. These results are consistent with our statistical analyses described above, which revealed that crisprQTL and RNAPII ChIA-PET pairs were enriched in gene-proximal interactions, while Hi-C pairs tended to identify more distal interactions.</p><p id="Par20">For comparison with other enhancer-gene prediction methods, we adapted the <italic>closest gene</italic> method to a quantitative ranking scheme where we ordered cCRE-gene pairs by the distance between the cCRE-ELS and the gene&#x02019;s closest TSS. For each BENGI dataset, we evaluated the overall performance of the resulting <italic>distance</italic> method by calculating the area under the precision-recall curve (AUPR). Accordingly, the <italic>distance</italic> method exhibited the highest AUPR (0.41) for RNAPII ChIA-PET pairs and the lowest AUPR (0.06) for Hi-C pairs (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a,b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S2b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>: Table S3). Since the distance method is cell-type independent and does not require any experimental data, we considered it as the baseline method for comparing all enhancer-gene prediction methods.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Evaluation of unsupervised methods for predicting cCRE-gene pairs. <bold>a</bold> Precision-recall (PR) curves for four unsupervised methods evaluated on RNAPII ChIA-PET pairs in GM12878: distance between cCREs-ELS and genes (gray), DNase-DNase correlation by Thurman et al. (green), DNase-expression correlation by Sheffield et al. (purple), and the average rank of the distance and the DNase-expression method (black). The areas under the PR curve (AUPRs) for the four methods are listed in the legend. The AUPR for a random method is indicated with a dashed line at 0.15. <bold>b</bold> The AUPRs for the four unsupervised methods are computed for each of the six benchmark datasets from LCLs. <bold>c</bold> Genome browser view (chr6:88,382,922-88,515,031) of epigenomic signals and positive BENGI links (RNAPII ChIA-PET in red, Hi-C in green, CHi-C in blue, and GEUVADIS eQTL in pink) connecting the EH37E0853090 cCRE (star) to the <italic>AKIRIN2</italic> gene. <bold>d</bold> Scatter plot of normalized <italic>AKIRIN2</italic> expression vs. the normalized DNase signal at EH37E0853090 as calculated by Sheffield et al. (Pearson correlation coefficient&#x02009;=&#x02009;0.16). Although <italic>AKIRIN2</italic> is highly expressed across many tissues, EH37E0853090 presents high DNase signals primarily in lymphoblastoid cell lines (purple triangles), resulting in a low correlation</p></caption><graphic xlink:href="13059_2019_1924_Fig3_HTML" id="MO3"/></fig></p></sec><sec id="Sec6"><title>Correlation-based approaches perform worse than the distance method</title><p id="Par21">We next evaluated the performance of two correlation-based methods with the BENGI datasets: a method based on correlating the DNase signals at predicted enhancers with the DNase signals at TSSs across a panel of biosamples [<xref ref-type="bibr" rid="CR22">22</xref>] and a method based on correlating DNase signals with gene expression [<xref ref-type="bibr" rid="CR23">23</xref>]. Both the DNase-DNase and DNase-expression methods outperformed random predictions for all 21 BENGI datasets, with average AUPR values of 0.10 and 0.12 vs. 0.07, respectively, but the differences were modest (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S2; Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>: Table S3). As previously demonstrated [<xref ref-type="bibr" rid="CR22">22</xref>], positive pairs presented significantly higher correlations under both methods than negative pairs in all datasets (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S2); however, the relative rankings of these correlations were mixed and did not completely segregate positive from negative pairs. The DNase-expression method significantly outperformed the DNase-DNase method for all but two BENGI datasets (Wilcoxon signed-rank test <italic>p</italic>&#x02009;=&#x02009;6.7E&#x02212;5), with an average AUPR increase of 29% (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2).</p><p id="Par22">We then evaluated the performance of the GeneHancer prediction model, via an integration of four types of enhancer annotations, including an earlier version of our cCREs, to generate a collection of candidate enhancers [<xref ref-type="bibr" rid="CR31">31</xref>]. These candidate enhancers were then linked to genes by integrating co-expression correlations, eQTLs, CHi-C data, and genomic distance. Because the authors used eQTLs and CHi-C from the same data sources as those in BENGI to build the GeneHancer model, we only evaluated the performance of the model on the ChIA-PET, Hi-C, and crisprQTL pairs. While the GeneHancer predictions were better than random predictions, the differences were extremely modest (average improvement of 0.01 in AUPR). The GeneHancer predictions also had a much lower overall recall than the correlations methods (on average 8% compared to 100% and 76% for DNase-DNase and DNase-expression respectively). Even for these limited sets of predictions, GeneHancer never outperformed the DNase-expression model and only outperformed the DNase-DNase model for crisprQTLs (Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S3).</p><p id="Par23">Ultimately, the distance method substantially outperformed the two correlation-based methods and the GeneHancer predictions: distance was better than DNase-DNase for all 21 datasets (average AUPR increase of 127%; <italic>p</italic>&#x02009;=&#x02009;1.9E&#x02212;6; Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2), better than DNase-expression for 17 datasets (average AUPR increase of 77%; <italic>p</italic>&#x02009;=&#x02009;1.6E&#x02212;4), and better than GeneHancer predictions for all datasets (average AUPR increase of 256%; <italic>p</italic>&#x02009;=&#x02009;9.5E&#x02212;7). The PR curves of the distance method and the two correlation-based methods for the RNAPII ChIA-PET pairs are shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a. For the first 25&#x02009;k predictions, the distance method presented a similar precision to the DNase-DNase method and lower precision than the DNase-expression method, but when more predictions were made, the distance method substantially outperformed both correlation-based methods and achieved a much higher AUPR (0.41 vs. 0.28 and 0.26). We observed this crossover of PR curves in other non-QTL datasets as well (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S2); thus, we integrated the distance and DNase-expression methods by averaging their ranks for the same prediction. Notably, this average-rank method showed high precision for its top-ranked predictions (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a) and achieved higher AUPRs than the other methods for all 13 datasets except for GTEx eQTL pairs, with an average AUPR increase of 17% over the distance method for these datasets (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2). For the eight GTEx eQTL datasets, the distance method remained the best approach, showing an 18% higher AUPR on average than the second-best method, average rank (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2).</p><p id="Par24">We asked why correlation-based methods performed poorly for predicting enhancer-gene pairs. One particular example is highlighted in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> c, d. cCRE-ELS EH37E0853090 was paired with the <italic>AKIRIN2</italic> gene by RNAPII ChIA-PET, Hi-C, CHi-C, and a GEUVADIS eQTL (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>c). However, this pair was poorly ranked by both correlation-based methods (correlation coefficients: <italic>r</italic>&#x02009;=&#x02009;0.03 and 0.16 for DNase-DNase and DNase-expression, respectively). <italic>AKIRIN2</italic> was highly expressed in most surveyed cell types (median normalized expression of 8.5 vs. background of 4.7 RPKM, Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S4a), and its promoter exhibited a high DNase signal (signal &#x02265;&#x02009;50) for each of the DNase-seq groups (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S4b). However, EH37E0853090 only presented high DNase signals in four cell types, which were all lymphoblastoid cell lines, suggesting that this enhancer was primarily active in the B cell lineage. The ubiquitous expression of <italic>AKIRIN2</italic> and the cell-type-specific activity of EH37E0853091 resulted in a low correlation (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>d, Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S4b). In general, TSS-overlapping cCREs (cCREs-TSS) are active in many more biosamples than distal cCREs-ELS (median of 92 vs. 46 biosamples, <italic>p</italic>&#x02009;=&#x02009;3.6E&#x02212;&#x02009;264, Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S4c-d). In summary, because the epigenomic signals at cCREs-ELS are far more cell type specific than the epigenomic signals at TSSs and gene expression profiles, correlation across biosamples is a poor method for detecting enhancer-gene pairs.</p></sec><sec id="Sec7"><title>Supervised methods outperform baseline methods upon cross-validation</title><p id="Par25">We tested two supervised machine-learning methods that were reported to perform well in the original publications on the methods: TargetFinder, which uses epigenomic signals such as histone mark ChIP-seq, TF ChIP-seq, DNase-seq in the corresponding cell types as input features, and PEP-motif, which uses the occurrence of TF sequence motifs as features. Xi et al. subsequently revealed that the original implementation of cross-validation (CV) by TargetFinder and PEP-motif allowed the assignment of enhancer-gene pairs from the same genomic loci to different CV groups, which led to sharing of training and testing data, overfitting of their models, and inflated performance [<xref ref-type="bibr" rid="CR29">29</xref>]. Thus, we implemented the chromCV method to ensure that pairs from the same chromosome were always assigned to the same CV group (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>e; &#x0201c;<xref rid="Sec11" ref-type="sec">Methods</xref>&#x0201d;).</p><p id="Par26">We first tested these two supervised methods on the six BENGI datasets in GM12878 because there were a large number of epigenomic datasets for this cell type that could be used as features to train the methods. Although PEP-motif performed better than random, it underperformed the distance method for all GM12878 pairs and was far worse than the average-rank method pairs (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, b; Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2b). In contrast, TargetFinder outperformed the average-rank method for all six datasets, with an average AUPR improvement of 66% (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, b; Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2), but the AUPRs were still low, especially for the Hi-C (0.17) and eQTL datasets (0.19 and 0.26).
<fig id="Fig4"><label>Fig. 4</label><caption><p>Evaluation of supervised learning methods for predicting cCRE-gene pairs. <bold>a</bold> PR curves for three supervised methods evaluated using RNAPII ChIA-PET pairs in GM12878: PEP-motif (green) and two versions of TargetFinder (full model in darker blue and core model in lighter blue). For comparison, two unsupervised methods presented in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> (the distance (gray) and average-rank (black) methods) are also shown along with the AUPR for a random method (dashed line at 0.15). The AUPRs for the methods are listed in the legend. <bold>b</bold> AUPRs for the three supervised methods, two unsupervised methods, and a random approach, colored as in <bold>a</bold>, for each of the six BENGI datasets from LCLs. <bold>c</bold> Scatter plot of AUPRs for TargetFinder (triangles) and PEP-motif (circles) across the BENGI datasets evaluated using 12-fold random CV (<italic>X</italic>-axis) vs. chromosome-based CV (<italic>Y</italic>-axis). The diagonal dashed line indicates <italic>X</italic>&#x000a0;=&#x02009;<italic>Y</italic>. <bold>d</bold> Schematic diagram for the full and core4 TargetFinder models</p></caption><graphic xlink:href="13059_2019_1924_Fig4_HTML" id="MO4"/></fig></p><p id="Par27">Because the results of TargetFinder and PEP-motif upon our chromCV implementation were worse than the original published results for these methods, we also implemented a randomized 12-fold CV method as described in the original publications to test whether we could reproduce their results. Indeed, we observed large performance decreases for the chromCV method with respect to the original CV method (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>c), suggesting that overfitting was a source of inflated performance. PEP-motif presented a more substantial decrease in performance (average AUPR decrease of 80%) than TargetFinder (average AUPR decrease of 51%), likely because PEP-motif added 4&#x02009;kb of padding on both sides of each enhancer, increasing the chance of overlapping training and testing data. Although PEP-motif and TargetFinder used Hi-C loops as the gold standard in their original analyses, both methods showed the largest performance decreases for the BENGI GM12878 Hi-C pairs (AUPR decrease of 95% for PEP-motif and 80% for TargetFinder). This analysis further highlights the utility of a carefully designed benchmark to prevent overfitting of supervised models.</p><p id="Par28">Our implementation of TargetFinder in GM12878 cells involved 101 epigenomic datasets, including ChIP-seq data for 88 TFs, resulting in a total of 303 input features (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>d). However, such extensive TF ChIP-seq data were not available for other biosamples; thus, we also trained TargetFinder models using only distance and four epigenomic features: DNase, H3K4me3, H3K27ac, and CTCF data, which we refer to as the core4 TargetFinder models. While the core4 models exhibited an average AUPR reduction of 23% compared with the respective full models across the 13 BENGI datasets (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, b; Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>: Table S3), they still outperformed the distance and average-rank methods for all datasets. Of particular note were the IMR-90 Hi-C pairs, which presented the greatest decrease in performance between the full and core4 TargetFinder models, with an AUPR reduction of 0.29 (81%). We observed similar large decreases in performance across all four variations of the IMR-90 Hi-C pairs. We also trained core3 models for the biosamples without CTCF data, and they showed an average AUPR reduction of 34% compared with the respective full models across the 13 BENGI datasets. For the seven GTEx eQTL datasets from tissues, these core3 models did not outperform the distance or average-rank models.</p><p id="Par29">Overall, TargetFinder&#x02019;s performance on the RNAPII and CTCF ChIA-PET pairs was markedly higher than its performance on other BENGI datasets. These datasets were the only two benchmarks of 3D chromatin interactions mediated by specific TFs. When we analyzed the feature-importance scores (i.e., Gini importance) from TargetFinder&#x02019;s GBM model, we found that RNAPII and CTCF ChIP-seq signals at promoters had the highest importance in the respective models. To further dissect the features contributed to TargetFinder&#x02019;s performance, we ran the algorithm on a subset of positive and negative pairs (1:2 ratio of positives to negatives) and three selections of positive and negative pairs that were matched for (i) only promoter inclusion, (ii) only distance, and (iii) promoter inclusion and distance (for promoter distance, see &#x0201c;<xref rid="Sec11" ref-type="sec">Methods</xref>&#x0201d;). For all four subsets, the full TargetFinder still outperformed all other methods (Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>: Table S4e); however, compared to the 1:2 ratio set (average AUPR&#x02009;=&#x02009;0.86), performance was lower for the distance-matched and promoter-matched sets (average AUPR&#x02009;=&#x02009;0.74 and 0.69) and was the lowest for the promoter-distance-matched sets (average AUPR&#x02009;=&#x02009;0.61). We observed similar patterns with the TargetFinder core4 and core3 though the relative drop in performances was much larger&#x02014;average decreases in AUPR of 0.25 for full model, 0.28 for core4 model, and 0.32 for core-3 model. Particularly, for the core3 CTCF ChIA-PET promoter-distance model, which does not include CTCF as a feature, we observed an AUPR of 0.43, a 0.30 reduction in AUPR compared to the 1:2 ratio pairs, and only a 0.03 improvement in AUPR over the DNase-DNase correlation method. These results suggest that differences in RNAPII/CTCF ChIP-seq signal and distance between positive and negative pairs contribute to TargetFinder&#x02019;s ability to successfully predict cCRE-ELS-gene pairs.</p></sec><sec id="Sec8"><title>TargetFinder exhibits moderate performance across different cell types</title><p id="Par30">The most desirable application of a supervised method is to train the model in a biosample with 3D chromatin or genetic interaction data and then use the model to make predictions in another biosample without such data. Thus, we tested the TargetFinder core4 and core3 models for such application to the ChIA-PET, Hi-C, CHi-C, and GTEx eQTL datasets, readjusting our chromCV method to prevent overfitting [<xref ref-type="bibr" rid="CR32">32</xref>] (see &#x0201c;<xref rid="Sec11" ref-type="sec">Methods</xref>&#x0201d;).</p><p id="Par31">As expected, the cross-cell-type models performed worse than the same-cell-type models, but their performance varied compared with the unsupervised distance and average-rank methods. For the CHi-C and RNAPII ChIA-PET datasets, all tested cross-cell-type TargetFinder models outperformed the distance and average-rank methods for both tested cell types (GM12878 vs. HeLa and GM12878 vs. CD34+), with average AUPR increases of 32% and 12%, respectively (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>a,b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Table S5). For CTCF ChIA-PET, the core3 model trained on HeLa cells did not outperform the unsupervised methods for predicting GM12878 pairs (AUPR&#x02009;=&#x02009;0.15 vs 0.21), but the models trained on GM12878 and the core4 model trained on HeLa did slightly outperform the unsupervised methods for predicting HeLa pairs and GM12878 pairs respectively (average AUPR increase of 7% Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>c, Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Table S5). The results for the Hi-C datasets were mixed. Among the 60 cross-cell-type models tested, 12 outperformed the distance and average-rank methods. Specifically, the model trained on GM12878 only outperformed the distance and average-rank methods for predicting HeLa or NHEK pairs (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>d, Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Table S5), with an average 50% increase in performance. The model trained on IMR-90 never outperformed the distance and average-rank methods, and for the prediction of HMEC, IMR-90, and K562 pairs, none of the cross-cell-type models outperformed the distance or average-rank methods (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Table S5). These results were consistent across the fixed ratio pairs as well. Finally, none of the cross-cell-type models outperformed the distance method for the GTEx datasets; the distance method was the highest-performing model for all GTEx datasets (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Table S5).
<fig id="Fig5"><label>Fig. 5</label><caption><p>Evaluation of supervised learning methods trained in one cell type and tested in another cell type. AUPRs for the distance (gray), average-rank (black), and TargetFinder core4 (purple) methods across <bold>a</bold> RNAPII ChIA-PET, <bold>b</bold> CTCF ChIA-PET, <bold>c</bold> CHi-C, <bold>d</bold> Hi-C, and <bold>e</bold> GTEx eQTL pairs. The cell type used for training is indicated in the panel title, and the cell type used for testing is indicated on the <italic>X</italic>-axis. The best-performing method for each dataset is indicated by a star, and random performance is indicated with a dashed line</p></caption><graphic xlink:href="13059_2019_1924_Fig5_HTML" id="MO5"/></fig></p></sec></sec><sec id="Sec9"><title>Discussion</title><p id="Par32">Here, we have presented BENGI, a benchmark comprising cCRE-ELS-gene pairs, curated through the integration of the Registry of cCREs and genomic interaction datasets. We used BENGI to evaluate four published computational methods for target gene prediction that represent most of the widely used approaches in the field while surveying orthogonal dimensions: correlation methods survey across the biosample dimension, while supervised machine-learning methods such as TargetFinder survey across the assay dimension. We found that the two correlation-based, unsupervised methods significantly underperformed the baseline distance method, while one of the two supervised methods examined, TargetFinder, significantly outperformed the distance method when trained and tested within the same cell type by cross-validation. Although TargetFinder outperformed the distance method for all BENGI datasets, the AUPRs of the TargetFinder models were generally still low (0.07&#x02013;0.72). In particular, TargetFinder performed best on ChIA-PET pairs; however, the performance substantially decreased when the positive and negative pairs were matched for their distributions of RNAPII/CTCF ChIP-seq signals at promoters and cCRE-ELS-gene distances. Thus, these features are the main contributors to TargetFinder&#x02019;s higher performance on ChIA-PET datasets than other BENGI datasets. The other supervised method, PEP-motif, significantly underperformed the distance method, suggesting that the frequencies of TF motifs at enhancers and promoters are not sufficiently predictive of genomic interactions. When trained and tested in different cell types, TargetFinder performed better than the distance method for some BENGI datasets, albeit by a much smaller amount. Overall, there is much room for improvement for all of these methods, indicating that target gene prediction remains a challenging problem. BENGI datasets can be used by the community to tackle this problem while avoiding overfitting issues such as those identified for TargetFinder and PEP post publication [<xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR30">30</xref>].</p><p id="Par33">Our analyses highlight the differences between the genomic interactions identified by various experimental techniques (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). For the same biosample (e.g., LCLs), the BENGI datasets generated by the same technique shared ~&#x02009;40% of their pairs (e.g., between RNAPII and CTCF ChIA-PET and between GEUVADIS and GTEx eQTLs), but the overlap between the datasets generated by different techniques were typically lower than 25% and could be as low as 1% (e.g., between eQTL and Hi-C). The BENGI datasets also differed significantly in terms of enhancer-gene distance and the enrichment of epigenomic signals at enhancers and TSSs. Thus, we still do not have a comprehensive understanding of the factors that regulate enhancer-gene interactions, and these different experimental techniques may capture different subsets of interactions.</p><p id="Par34">Overall, all computational methods evaluated presented difficulty in predicting Hi-C pairs; even for the fixed ratio datasets, the Hi-C pairs consistently exhibited the lowest overall performance. This could be due to the technical challenges of calling Hi-C loops or the biological roles of these loops. For example, it has been noted that the detection of Hi-C loops requires care, and different loop-calling methods can produce markedly different results [<xref ref-type="bibr" rid="CR33">33</xref>]. Additionally, recent results from the Aiden lab demonstrated that gene expression did not change upon loop disruption via knocking out the key protein CTCF using a degron system [<xref ref-type="bibr" rid="CR34">34</xref>]. This finding may suggest that these CTCF Hi-C loops may play specific biological roles and may only represent a small subset of enhancer-gene interactions that have different properties compared to the other interactions.</p><p id="Par35">Although the correlation-based methods did not outperform the distance method, the DNase-expression method did augment the distance method when combined with it. Furthermore, because correlation-based methods and supervised machine-learning methods survey orthogonal dimensions (biosample vs. assay), one promising future direction will be to combine these two types of approaches. For such future work to be fruitful, it will be beneficial to understand the differences in performance between the two correlation-based methods because the DNase-expression correlation method consistently outperformed the DNase-DNase correlation method. Several factors could contribute to this increased performance. First, gene expression may be a better readout for enhancer-gene interactions than a promoter&#x02019;s chromatin accessibility, although these two features are correlated (average Pearson correlation <italic>r</italic>&#x02009;=&#x02009;0.68). Second, for the DNase-expression method, Sheffield et al. generated normalized, batch-corrected matrices for the DNase-seq and gene expression data, while the DNase-DNase method used a read depth-normalized signal without any additional processing. To avoid imprecision in reimplementation, we downloaded these exact input datasets from the original publications (i.e., the exact normalized matrices for the DNase-expression method and the ENCODE2-processed DNase-seq bigWigs for the DNase-DNase method). The Sheffield et al. normalization technique may correct for outliers and batch effects, which would otherwise lead to spurious correlations impacting performance. Third, the DNase-DNase method merged 79 cell types into 32 groups based on cell type similarity. While this grouping may correct an uneven survey of the biosample space, it may lead to lower overall correlations for cell-type-specific interactions. We highlighted one such case involving the LCL-specific EH37E0853090-AKIRIN2 interaction, where the DNase-DNase method reported a correlation of 0.03, and the DNase-expression method reported a correlation of 0.12. The low correlation calculated by the DNase-DNase method was due to the combination of the four LCLs in one group, reducing the statistical power (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S4b). These possible explanations should be carefully considered when designing future correlation-based and combined methods. Additionally, although these correlation-based methods did not perform well on the BENGI datasets, they may present better predictive power when used on curated sets of biosamples such as those obtained across embryonic development or cell differentiation. As we expand the number of cell types and tissues covered by BENGI, we hope to test these methods to evaluate their performance systematically.</p><p id="Par36">Finally, we developed BENGI using an enhancer-centric model, as we were motivated by the Registry of cCREs. We hope to expand upon this approach to include a gene-centric model (i.e., for a given gene, determine the interacting enhancers) for future developments. Additionally, though BENGI datasets currently span 13 biosamples, the majority of the gene-ELS pairs derived from GM12878 or LCLs because these cells have been extensively profiled. Therefore, users of the benchmark should be cognizant that not all biosamples are profiled equally. Furthermore, the remaining BENGI datasets all derived from cell lines or heterogeneous tissues, none from primary cells. We will increase the representation of primary cells in our benchmark as soon as 3D chromatin and genetic interaction data on primary cells become available. We also plan to expand BENGI to include more functionally tested datasets such as the crisprQTLs as these results are published.</p></sec><sec id="Sec10"><title>Conclusions</title><p id="Par37">Precise and accurate identification of enhancer-gene links in a cell-type-specific manner remains a major challenge. Systematic comparisons using the BENGI datasets enabled us to identify the pitfalls in the current repertoire of computational methods, such as correlation-based approaches and the more complex, tree-based supervised algorithms. BENGI will aid the development of future enhancer-gene prediction models and improve our understanding of how regulatory elements control gene expression and ultimately the role that regulatory elements play in human diseases.</p></sec><sec id="Sec11"><title>Methods</title><sec id="Sec12"><title>Data acquisition</title><sec id="Sec13"><title>ChIA-PET</title><p id="Par38">We downloaded the following ChIA-PET clusters generated by the Ruan lab [<xref ref-type="bibr" rid="CR16">16</xref>] from the NCBI Gene Expression Omnibus (GEO) under accession number GSE72816.<disp-quote><p id="Par39">GSM1872886_GM12878_CTCF_PET_clusters.txt</p><p id="Par40">GSM1872887_GM12878_RNAPII_PET_clusters.txt</p><p id="Par41">GSM1872888_HeLa_CTCF_PET_clusters.txt</p><p id="Par42">GSM1872889_HeLa_RNAPII_PET_clusters.txt</p></disp-quote></p><p id="Par43">We filtered each set of clusters by selecting ChIA-PET links that were supported by at least four reads (column 7&#x02009;&#x02265;&#x02009;4).</p></sec><sec id="Sec14"><title>Hi-C loops</title><p id="Par44">We downloaded the following Hi-C loops generated by the Aiden lab [<xref ref-type="bibr" rid="CR14">14</xref>] from GEO under accession number GSE63525.<disp-quote><p id="Par45">GSE63525_GM12878_primary+replicate_HiCCUPS_looplist.txt</p><p id="Par46">GSE63525_HMEC_HiCCUPS_looplist.txt.gz</p><p id="Par47">GSE63525_HeLa_HiCCUPS_looplist.txt.gz</p><p id="Par48">GSE63525_IMR90_HiCCUPS_looplist.txt.gz</p><p id="Par49">GSE63525_K562_HiCCUPS_looplist.txt.gz</p><p id="Par50">GSE63525_NHEK_HiCCUPS_looplist.txt.gz</p></disp-quote></p><p id="Par51">We did not perform any additional filtering on these loops.</p></sec><sec id="Sec15"><title>CHi-C</title><p id="Par52">We downloaded the following CHi-C interactions generated by the Osborne lab [<xref ref-type="bibr" rid="CR17">17</xref>] from ArrayExpress under accession number E-MTAB-2323.<disp-quote><p id="Par53">TS5_GM12878_promoter-other_significant_interactions.txt</p><p id="Par54">TS5_CD34_promoter-other_significant_interactions.txt</p></disp-quote></p><p id="Par55">We filtered each set of interactions selecting CHi-C links by requiring a log(observed/expected) value greater than ten (column 11&#x02009;&#x0003e;&#x02009;10).</p></sec><sec id="Sec16"><title>eQTLs</title><p id="Par56">We downloaded cis-eQTLs from the GEUVADIS project:</p><p id="Par57">
<ext-link ext-link-type="uri" xlink:href="ftp://ftp.ebi.ac.uk/pub/databases/microarray/data/experiment/GEUV/E-GEUV-1/analysis_results/">ftp://ftp.ebi.ac.uk/pub/databases/microarray/data/experiment/GEUV/E-GEUV-1/analysis_results/</ext-link>
</p><p id="Par58">EUR373.gene.cis.FDR5.all.rs137.txt</p><p id="Par59">We downloaded single-tissue cis-eQTLs (GTEx_Analysis_v7_eQTL.tar.gz) from the GTEx Portal <ext-link ext-link-type="uri" xlink:href="https://gtexportal.org/home/<USER>">https://gtexportal.org/home/<USER>/ext-link>. We used the following files:</p><p id="Par60">
<disp-quote><p id="Par61">Cells_EBV-transformed_lymphocytes.v7.signif_variant_gene_pairs.txt</p><p id="Par62">Colon_Sigmoid.v7.signif_variant_gene_pairs.txt</p><p id="Par63">Liver.v7.signif_variant_gene_pairs.txt</p><p id="Par64">Ovary.v7.signif_variant_gene_pairs.txt</p><p id="Par65">Pancreas.v7.signif_variant_gene_pairs.txt</p><p id="Par66">Stomach.v7.signif_variant_gene_pairs.txt</p><p id="Par67">Thyroid.v7.signif_variant_gene_pairs.txt</p></disp-quote>
</p></sec><sec id="Sec17"><title>CRISPR perturbations</title><p id="Par68">We downloaded crisprQTL data from Gasperini et al. [<xref ref-type="bibr" rid="CR20">20</xref>] and mapped the reported genes to those annotated in GENCODE V19 and intersected the reported enhancer coordinates with cCREs-ELS in K562. A total of 4937 of the tested enhancers (85%) overlapped a K562 cCRE-ELS.</p></sec></sec><sec id="Sec18"><title>Defining cCREs-ELS</title><p id="Par69">We used cCREs-ELS from V1 of the ENCODE Registry of cCREs available on the ENCODE portal found under the accessions provided in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S1a. We selected all cCREs-ELS (RGB color code 255,205,0) that were distal (i.e., greater than 2&#x02009;kb from an annotated TSS, GENCODE v19).</p></sec><sec id="Sec19"><title>Defining cCRE-gene pairs</title><p id="Par70">We created cCRE-gene pairs using the <italic>Generate-Benchmark.sh</italic>. script, which is available on GitHub [<xref ref-type="bibr" rid="CR35">35</xref>].</p><sec id="Sec20"><title>3D chromatin interactions (ChIA-PET, Hi-C, and CHi-C)</title><p id="Par71">Using bedtools intersect (v2.27.1), we intersected the anchors of the filtered links (see above) with cCREs-ELS that were active in the same biosample. We retained all links with an anchor that overlapped at least one cCREs-ELS and with the other anchor within &#x000b1;&#x02009;2&#x02009;kb of a GENCODE V19 TSS. We tagged all links with an anchor within &#x000b1;&#x02009;2&#x02009;kb of the TSSs of multiple genes as ambiguous pairs and created a separate version of each dataset with these links removed.</p></sec><sec id="Sec21"><title>Genetic interactions (eQTLs)</title><p id="Par72">For eQTLs, we retrieved the location of each reported SNP from the eQTL file and intersected these loci with cCREs-ELS that were active in the same tissue type using bedtools intersect. We then paired the cCRE-ELS with the gene linked to the SNP. We only considered SNPs that were directly reported in each of the studies; we did not expand our set using linkage disequilibrium due to the mixed populations surveyed by GTEx.</p></sec><sec id="Sec22"><title>CRISPR/dCas-9 (crisprQTLs)</title><p id="Par73">For crisprQTLs, we intersected the reported positive enhancers with cCREs in K562 using bedtools intersect. We then paired the cCRE-ELS with the gene linked to the reported enhancer.</p></sec><sec id="Sec23"><title>Generation of negative pairs</title><p id="Par74">To generate negative pairs, we calculated the 95th percentile of the distances of positive cCRE-gene pairs for each dataset, with distance defined as the linear distance between the cCRE-ELS and the closest TSS of the gene using bedtools closest. For each cCRE-ELS among the positive cCRE-gene pairs that fell within this 95th percentile, we considered all other genes within the 95th percentile distance cutoff as negatives. Because our model is enhancer-centric, the same promoter may belong to both positive and negative sets, paired with different enhancers. For datasets with ambiguous links removed (ChIA-PET, Hi-C, and CHi-C), we also excluded genes in these ambiguous pairs as negatives. For the fixed ratio datasets, we also excluded genes that were in the positive pairs for the cCREs-ELS in other BENGI datasets before randomly selecting the negatives. If a cCRE-ELS exhibited fewer than four negative pairs, then it was excluded from this fixed ratio set.</p></sec><sec id="Sec24"><title>Assignment of chromosome CV</title><p id="Par75">For each BENGI dataset, we calculated the number of cCRE-gene pairs on each chromosome and assigned chromCV groups accordingly. The chromosome with the most pairs (often chr1) was assigned its own group. Then, we iteratively took the chromosome with the most and fewest pairs and combined them to create one CV group. In total, the 23 chromosomes (1&#x02013;22, X) were assigned to 12 CV groups.</p></sec></sec><sec id="Sec25"><title>Characterization of BENGI datasets</title><sec id="Sec26"><title>Clustering of dataset overlap</title><p id="Par76">For each pairwise combination of the GM12878/LCL BENGI datasets, we calculated the overlap coefficient of positive cCRE-gene pairs. Then, using <italic>hclust</italic>, we performed hierarchical clustering with default parameters.</p></sec><sec id="Sec27"><title>Gene expression</title><p id="Par77">For biosamples with matching RNA-seq data, we downloaded corresponding RNA-seq data from the ENCODE portal (accessions provided in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S1b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>: Figure S1). For each gene, we calculated the average TPM between the two experimental replicates. To test whether there was a significant difference between BENGI datasets with or without ambiguous pairs, we used a Wilcoxon test.</p></sec><sec id="Sec28"><title>ChIP-seq signals</title><p id="Par78">For cCREs-ELS in each positive pair across the GM12878 and LCL BENGI datasets, we calculated the average ChIP-seq signal for 140 transcription factors and DNA-binding proteins. We downloaded the ChIP-seq signal from the ENCODE portal (accession available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Table S2b) and used UCSC&#x02019;s <italic>bigWigAverageOverBed</italic> to calculate the average signal across each cCRE. For each BENGI dataset, we then reported the average signal for all cCREs.</p></sec></sec><sec id="Sec29"><title>Implementation of cCRE-gene prediction methods</title><sec id="Sec30"><title>Closest-gene method</title><p id="Par79">We identified the closest TSS to each cCRE-ELS using bedtools closest and GENCODE V19 TSS annotations. We compared two options: use of the full set of GENCODE TSSs (with problematic annotations removed) or use of only protein-coding GENCODE TSSs. To evaluate performance, we calculated the overall precision and recall for each BENGI dataset (Script: Closest-Gene-Method.sh).</p></sec><sec id="Sec31"><title>Distance method</title><p id="Par80">For each cCRE-gene pair, we calculated the linear distance between the cCRE-ELS and the gene&#x02019;s nearest TSS. To rank these pairs, we took the inverse (1/distance) and calculated the area under the precision-recall curve (AUPR) using a custom R script that uses the PROCR library (Script: Run-Distance-Method.sh).</p></sec><sec id="Sec32"><title><italic>DNase-DNase</italic> correlation method</title><p id="Par81">We used the same DNase-seq datasets as Thurman et al. employed for their DNase-DNase method. We downloaded these legacy datasets generated during ENCODE Phase 2 from the UCSC genome browser. For each cCRE-gene pair, we curated a set of cCREs-TSS by determining the closest cCRE for each TSS of the gene. We then calculated the average DNase signal across the nucleotide positions in the cCRE-ELS and cCRE-TSS for each DNase dataset. For similar cell types, as determined by Thurman et al., we averaged the DNase signal among these similar cell types in each of the 32 groups to generate 32 values for each cCRE-ELS and cCRE-TSS. We then calculated the Pearson correlation coefficient (PCC) for each cCRE-ELS and cCRE-TSS pair. If a gene was annotated with multiple TSSs, we selected the highest PCC among all the cCRE-ELS and cCRE-TSS comparisons. We ranked the predictions by their PCC and calculated the AUPR using the PROCR library (Script: Run-Thurman.sh).</p></sec><sec id="Sec33"><title><italic>DNase-expression</italic> correlation method</title><p id="Par82">To match the legacy data and normalization methods originally used by previous investigators [<xref ref-type="bibr" rid="CR23">23</xref>], we downloaded normalized counts across 112 cell types for DNase-hypersensitive sites or DHSs (<italic>dhs112_v3.bed</italic>) and genes (<italic>exp112.bed</italic>) from <ext-link ext-link-type="uri" xlink:href="http://big.databio.org/papers/RED/supplement/">http://big.databio.org/papers/RED/supplement/</ext-link>. We intersected each cCRE-ELS with the DHSs previously curated [<xref ref-type="bibr" rid="CR23">23</xref>]. If a cCRE overlapped with more than one DHS, we selected the DHS with the strongest signal for the cell type in question (i.e., the DHS with the strongest signal in GM12878 for GM12878 cCREs-ELS). For each cCRE-gene pair, we then calculated the Pearson correlation coefficient using the 112 normalized values provided in each matrix. cCRE-gene pairs that did not overlap with a DHS or did not have a matching gene in the expression matrix were assigned a score of &#x02212;&#x02009;100. (Script: Run-Sheffield.sh).</p></sec><sec id="Sec34"><title>PEP-motif</title><p id="Par83">We reimplemented PEP-motif to run on our cCRE-gene pairs with chromCV. Similar to Yang et al., we calculated motif frequency using FIMO [<xref ref-type="bibr" rid="CR36">36</xref>] and the HOCOMOCO database (v11 core, [<xref ref-type="bibr" rid="CR37">37</xref>]). We also added &#x000b1;&#x02009;4&#x02009;kb of padding to each cCRE-ELS as originally described. We concatenated cross-validation predictions and calculated AUPR values using PROCR (Script: Run-PEPMotif.sh).</p></sec><sec id="Sec35"><title>TargetFinder</title><p id="Par84">We reimplemented TargetFinder to run on our cCRE-gene pairs with chromCV. For features, we used the identical datasets described by Whalen et al. for each cell type. We concatenated the cross-validation predictions and calculated AUPR values using PROCR (Script: Run-TargetFinder-Full.sh).</p><p id="Par85">To dissect features contributing to TargetFinder&#x02019;s high performance on ChIA-PET pairs, we created four subsets of pairs for the GM12878 RNAPII and CTCF ChIA-PET datasets.
<list list-type="order"><list-item><p id="Par86">A subset with a 1:2 ratio of positives to negatives which was created by subsampling 1 positive link for each cCREs and 2 negative links for each cCRE. This was analogous to the 1:4 fixed ratio method described above.</p></list-item><list-item><p id="Par87">A &#x0201c;promoter-matched&#x0201d; subset that only includes pairs from promoters that are in at least one positive and one negative pair. We then subsample to achieve a fixed 1:2 ratio of positives to negatives.</p></list-item><list-item><p id="Par88">A &#x0201c;distance-matched subset for which we define 5 distance quantiles based on the distribution of positive pairs and sample equally from each bin maintaining a 1:2 ratio of positives to negatives.</p></list-item><list-item><p id="Par89">A &#x0201c;promoter-distance-matched&#x0201d; subset for which we match for promoter use as described in (2) and distance as described in (3). Once again, we maintained a 1:2 ratio of positives to negatives.</p></list-item></list></p></sec><sec id="Sec36"><title>Cross-cell-type performance</title><p id="Par90">To test the cross-cell-type performance of TargetFinder, we generated core4 and core3 models for each cell type and then evaluated the models in other cell types. To prevent any overfitting, we assigned the chromCV of the test sets to match those of the training sets.</p></sec></sec></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec37"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13059_2019_1924_MOESM1_ESM.xlsx"><caption><p><bold>Additional file 1: Table S1.</bold> Accessions for relevant ENCODE cCREs and RNA-seq experiments.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13059_2019_1924_MOESM2_ESM.xlsx"><caption><p><bold>Additional file 2: Table S2.</bold> Characteristics of BENGI datasets.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13059_2019_1924_MOESM3_ESM.pdf"><caption><p><bold>Additional file 3: Figure S1.</bold> Expression levels of genes in BENGI pairs. <bold>Figure S2.</bold> PR curves for unsupervised models. <bold>Figure S3.</bold> Correlation between BENGI pairs. <bold>Figure S4.</bold> Correlation methods perform poorly due to the ubiquity of promoters. <bold>Figure S5.</bold> PR curves of the supervised methods evaluated with BENGI datasets.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13059_2019_1924_MOESM4_ESM.xlsx"><caption><p><bold>Additional file 4: Table S3.</bold> AUPRs for unsupervised methods.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13059_2019_1924_MOESM5_ESM.xlsx"><caption><p><bold>Additional file 5: Table S4.</bold> AUPRs for supervised methods.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="13059_2019_1924_MOESM6_ESM.xlsx"><caption><p><bold>Additional file 6: Table S5.</bold> AUPRs for cross-cell type trained supervised methods.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="13059_2019_1924_MOESM7_ESM.docx"><caption><p><bold>Additional file 7.</bold> Review history.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s13059-019-1924-8.</p></sec><ack><title>Acknowledgements</title><p>The authors thank the Weng lab for helpful feedback and discussions.</p><sec id="FPar1"><title>Peer review information</title><p id="Par91">Yixin Yao was the primary editor on this article and managed its editorial process and peer review in collaboration with the rest of the editorial team.</p></sec><sec id="FPar2"><title>Review history</title><p id="Par92">The review history is available as Additional&#x000a0;file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>This project was developed and designed by JEM and ZW. JEM performed the analyses. JEM, HEP, and MJP jointly contributed to the creation of the Registry of cCREs. All authors read and approved the final version of the manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>This work was funded by grants from the National Institutes of Health (5U41HG007000, 5U24HG009446) to Z.W.</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>All processed datasets and corresponding scripts are available and version controlled at <ext-link ext-link-type="uri" xlink:href="https://github.com/weng-lab/BENGI">https://github.com/weng-lab/BENGI</ext-link> [<xref ref-type="bibr" rid="CR35">35</xref>]. cCREs are available at the ENCODE portal (<ext-link ext-link-type="uri" xlink:href="http://www.encodeproject.org">www.encodeproject.org</ext-link>) under the following accessions: ENCSR480YCS, ENCSR451UAK, ENCSR502KJC, ENCSR981LWT, ENCSR593JKE, ENCSR376OQT, ENCSR970YPW, ENCSR282MAC, ENCSR551OAQ, ENCSR529CSY, ENCSR117DKP, ENCSR117AJU, and ENCSR935NWB. ChIA-PET data is available at GEO under accession GSE72816. Hi-C data is available at GEO under accession GSE63525. CHi-C data is available from ArrayExpress under accession E-MTAB-2323. Cis-eQTLs from the GEUVADIS project are available at <ext-link ext-link-type="uri" xlink:href="ftp://ftp.ebi.ac.uk/pub/databases/microarray/data/experiment/GEUV/E-GEUV-1/analysis_results/EUR373.gene.cis.FDR5.all.rs137.txt">ftp://ftp.ebi.ac.uk/pub/databases/microarray/data/experiment/GEUV/E-GEUV-1/analysis_results/EUR373.gene.cis.FDR5.all.rs137.txt</ext-link>. GTEx single-tissue cis-eQTLs are available in GTEx_Analysis_v7_eQTL.tar.gz from <ext-link ext-link-type="uri" xlink:href="https://gtexportal.org/home/<USER>">https://gtexportal.org/home/<USER>/ext-link>. crisprQTL data is available from the supplemental tables in Gasperini et al. [<xref ref-type="bibr" rid="CR20">20</xref>]. ENCODE RNA-seq experiments are available at the ENCODE portal under the following accessions: ENCSR000COQ, ENCSR000CPR, ENCSR000AEM, ENCSR000CTQ, ENCSR000CPL, ENCSR830HIN, ENCSR860HAA.</p></notes><notes><title>Ethics approval and consent to participate</title><p id="Par95">Not applicable.</p></notes><notes><title>Consent for publication</title><p id="Par96">Not applicable.</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p id="Par97">Z. Weng is a cofounder of Rgenta Therapeutics and she serves on its scientific advisory board.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ernst</surname><given-names>J</given-names></name><name><surname>Kheradpour</surname><given-names>P</given-names></name><name><surname>Mikkelsen</surname><given-names>TS</given-names></name><name><surname>Shoresh</surname><given-names>N</given-names></name><name><surname>Ward</surname><given-names>LD</given-names></name><name><surname>Epstein</surname><given-names>CB</given-names></name><etal/></person-group><article-title>Mapping and analysis of chromatin state dynamics in nine human cell types</article-title><source>Nature.</source><year>2011</year><volume>473</volume><fpage>43</fpage><lpage>49</lpage><pub-id pub-id-type="doi">10.1038/nature09906</pub-id><pub-id pub-id-type="pmid">21441907</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>ENCODE Project Consortium</collab><name><surname>Bernstein</surname><given-names>BE</given-names></name><name><surname>Birney</surname><given-names>E</given-names></name><name><surname>Dunham</surname><given-names>I</given-names></name><name><surname>Green</surname><given-names>ED</given-names></name><name><surname>Gunter</surname><given-names>C</given-names></name><etal/></person-group><article-title>An integrated encyclopedia of DNA elements in the human genome</article-title><source>Nature</source><year>2012</year><volume>489</volume><fpage>57</fpage><lpage>74</lpage><pub-id pub-id-type="doi">10.1038/nature11247</pub-id><pub-id pub-id-type="pmid">22955616</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hoffman</surname><given-names>MM</given-names></name><name><surname>Buske</surname><given-names>OJ</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Weng</surname><given-names>Z</given-names></name><name><surname>Bilmes</surname><given-names>JA</given-names></name><name><surname>Noble</surname><given-names>WS</given-names></name></person-group><article-title>Unsupervised pattern discovery in human chromatin structure through genomic segmentation</article-title><source>Nat Methods</source><year>2012</year><volume>9</volume><fpage>473</fpage><lpage>476</lpage><pub-id pub-id-type="doi">10.1038/nmeth.1937</pub-id><pub-id pub-id-type="pmid">22426492</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rajagopal</surname><given-names>N</given-names></name><name><surname>Xie</surname><given-names>W</given-names></name><name><surname>Li</surname><given-names>Y</given-names></name><name><surname>Wagner</surname><given-names>U</given-names></name><name><surname>Wang</surname><given-names>W</given-names></name><name><surname>Stamatoyannopoulos</surname><given-names>J</given-names></name><etal/></person-group><article-title>RFECS: A Random-Forest Based Algorithm for Enhancer Identification from Chromatin State. Singh M, editor</article-title><source>PLoS Comput Biol Public Libr Sci</source><year>2013</year><volume>9</volume><fpage>e1002968</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1002968</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>Roadmap Epigenomics Consortium</collab><name><surname>Kundaje</surname><given-names>A</given-names></name><name><surname>Meuleman</surname><given-names>W</given-names></name><name><surname>Ernst</surname><given-names>J</given-names></name><name><surname>Bilenky</surname><given-names>M</given-names></name><name><surname>Yen</surname><given-names>A</given-names></name><etal/></person-group><article-title>Integrative analysis of 111 reference human epigenomes</article-title><source>Nature</source><year>2015</year><volume>518</volume><fpage>317</fpage><lpage>330</lpage><pub-id pub-id-type="doi">10.1038/nature14248</pub-id><pub-id pub-id-type="pmid">25693563</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>He</surname><given-names>Y</given-names></name><name><surname>Gorkin</surname><given-names>DU</given-names></name><name><surname>Dickel</surname><given-names>DE</given-names></name><name><surname>Nery</surname><given-names>JR</given-names></name><name><surname>Castanon</surname><given-names>RG</given-names></name><name><surname>Lee</surname><given-names>AY</given-names></name><etal/></person-group><article-title>Improved regulatory element prediction based on tissue-specific local epigenomic signatures</article-title><source>Proc Natl Acad Sci U S A</source><year>2017</year><volume>114</volume><fpage>E1633</fpage><lpage>E1640</lpage><pub-id pub-id-type="doi">10.1073/pnas.1618353114</pub-id><pub-id pub-id-type="pmid">28193886</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>D</given-names></name><name><surname>Liu</surname><given-names>S</given-names></name><name><surname>Warrell</surname><given-names>J</given-names></name><name><surname>Won</surname><given-names>H</given-names></name><name><surname>Shi</surname><given-names>X</given-names></name><name><surname>Navarro</surname><given-names>FCP</given-names></name><etal/></person-group><article-title>Comprehensive functional genomic resource and integrative model for the human brain</article-title><source>Science</source><year>2018</year><volume>362</volume><fpage>eaat8464</fpage><pub-id pub-id-type="doi">10.1126/science.aat8464</pub-id><pub-id pub-id-type="pmid">30545857</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maurano</surname><given-names>MT</given-names></name><name><surname>Humbert</surname><given-names>R</given-names></name><name><surname>Rynes</surname><given-names>E</given-names></name><name><surname>Thurman</surname><given-names>RE</given-names></name><name><surname>Haugen</surname><given-names>E</given-names></name><name><surname>Wang</surname><given-names>H</given-names></name><etal/></person-group><article-title>Systematic localization of common disease-associated variation in regulatory DNA</article-title><source>Science</source><year>2012</year><volume>337</volume><fpage>1190</fpage><lpage>1195</lpage><pub-id pub-id-type="doi">10.1126/science.1222794</pub-id><pub-id pub-id-type="pmid">22955828</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schaub</surname><given-names>MA</given-names></name><name><surname>Boyle</surname><given-names>AP</given-names></name><name><surname>Kundaje</surname><given-names>A</given-names></name><name><surname>Batzoglou</surname><given-names>S</given-names></name><name><surname>Snyder</surname><given-names>M</given-names></name></person-group><article-title>Linking disease associations with regulatory information in the human genome</article-title><source>Genome Res</source><year>2012</year><volume>22</volume><fpage>1748</fpage><lpage>1759</lpage><pub-id pub-id-type="doi">10.1101/gr.136127.111</pub-id><pub-id pub-id-type="pmid">22955986</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Farh</surname><given-names>KK-H</given-names></name><name><surname>Marson</surname><given-names>A</given-names></name><name><surname>Zhu</surname><given-names>J</given-names></name><name><surname>Kleinewietfeld</surname><given-names>M</given-names></name><name><surname>Housley</surname><given-names>WJ</given-names></name><name><surname>Beik</surname><given-names>S</given-names></name><etal/></person-group><article-title>Genetic and epigenetic fine mapping of causal autoimmune disease variants</article-title><source>Nature.</source><year>2014</year><volume>518</volume><fpage>337</fpage><lpage>343</lpage><pub-id pub-id-type="doi">10.1038/nature13835</pub-id><pub-id pub-id-type="pmid">25363779</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Finucane</surname><given-names>HK</given-names></name><name><surname>Bulik-Sullivan</surname><given-names>B</given-names></name><name><surname>Gusev</surname><given-names>A</given-names></name><name><surname>Trynka</surname><given-names>G</given-names></name><name><surname>Reshef</surname><given-names>Y</given-names></name><name><surname>Loh</surname><given-names>P-R</given-names></name><etal/></person-group><article-title>Partitioning heritability by functional annotation using genome-wide association summary statistics</article-title><source>Nature Genet</source><year>2015</year><volume>47</volume><fpage>1228</fpage><lpage>1235</lpage><pub-id pub-id-type="doi">10.1038/ng.3404</pub-id><pub-id pub-id-type="pmid">26414678</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lettice</surname><given-names>LA</given-names></name><name><surname>Heaney</surname><given-names>SJH</given-names></name><name><surname>Purdie</surname><given-names>LA</given-names></name><name><surname>Li</surname><given-names>L</given-names></name><name><surname>de Beer</surname><given-names>P</given-names></name><name><surname>Oostra</surname><given-names>BA</given-names></name><etal/></person-group><article-title>A long-range Shh enhancer regulates expression in the developing limb and fin and is associated with preaxial polydactyly</article-title><source>Hum Mol Genet</source><year>2003</year><volume>12</volume><fpage>1725</fpage><lpage>1735</lpage><pub-id pub-id-type="doi">10.1093/hmg/ddg180</pub-id><pub-id pub-id-type="pmid">12837695</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lieberman-Aiden</surname><given-names>E</given-names></name><name><surname>van Berkum</surname><given-names>NL</given-names></name><name><surname>Williams</surname><given-names>L</given-names></name><name><surname>Imakaev</surname><given-names>M</given-names></name><name><surname>Ragoczy</surname><given-names>T</given-names></name><name><surname>Telling</surname><given-names>A</given-names></name><etal/></person-group><article-title>Comprehensive mapping of long-range interactions reveals folding principles of the human genome</article-title><source>Sci Am Assoc Adv Sci</source><year>2009</year><volume>326</volume><fpage>289</fpage><lpage>293</lpage></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rao</surname><given-names>SSP</given-names></name><name><surname>Huntley</surname><given-names>MH</given-names></name><name><surname>Durand</surname><given-names>NC</given-names></name><name><surname>Stamenova</surname><given-names>EK</given-names></name><name><surname>Bochkov</surname><given-names>ID</given-names></name><name><surname>Robinson</surname><given-names>JT</given-names></name><etal/></person-group><article-title>A 3D map of the human genome at kilobase resolution reveals principles of chromatin looping</article-title><source>Cell.</source><year>2014</year><volume>159</volume><fpage>1665</fpage><lpage>1680</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2014.11.021</pub-id><pub-id pub-id-type="pmid">25497547</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>G</given-names></name><name><surname>Ruan</surname><given-names>X</given-names></name><name><surname>Auerbach</surname><given-names>RK</given-names></name><name><surname>Sandhu</surname><given-names>KS</given-names></name><name><surname>Zheng</surname><given-names>M</given-names></name><name><surname>Wang</surname><given-names>P</given-names></name><etal/></person-group><article-title>Extensive promoter-centered chromatin interactions provide a topological basis for transcription regulation</article-title><source>Cell.</source><year>2012</year><volume>148</volume><fpage>84</fpage><lpage>98</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2011.12.014</pub-id><pub-id pub-id-type="pmid">22265404</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tang</surname><given-names>Z</given-names></name><name><surname>Luo</surname><given-names>OJ</given-names></name><name><surname>Li</surname><given-names>X</given-names></name><name><surname>Zheng</surname><given-names>M</given-names></name><name><surname>Zhu</surname><given-names>JJ</given-names></name><name><surname>Szalaj</surname><given-names>P</given-names></name><etal/></person-group><article-title>CTCF-mediated human 3D genome architecture reveals chromatin topology for transcription</article-title><source>Cell.</source><year>2015</year><volume>163</volume><fpage>1611</fpage><lpage>1627</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2015.11.024</pub-id><pub-id pub-id-type="pmid">26686651</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mifsud</surname><given-names>B</given-names></name><name><surname>Tavares-Cadete</surname><given-names>F</given-names></name><name><surname>Young</surname><given-names>AN</given-names></name><name><surname>Sugar</surname><given-names>R</given-names></name><name><surname>Schoenfelder</surname><given-names>S</given-names></name><name><surname>Ferreira</surname><given-names>L</given-names></name><etal/></person-group><article-title>Mapping long-range promoter contacts in human cells with high-resolution capture hi-C</article-title><source>Nat Genet</source><year>2015</year><volume>47</volume><fpage>598</fpage><lpage>606</lpage><pub-id pub-id-type="doi">10.1038/ng.3286</pub-id><pub-id pub-id-type="pmid">25938943</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lappalainen</surname><given-names>T</given-names></name><name><surname>Sammeth</surname><given-names>M</given-names></name><name><surname>Friedl&#x000e4;nder</surname><given-names>MR</given-names></name><name><surname>PAC</surname><given-names>'tH</given-names></name><name><surname>Monlong</surname><given-names>J</given-names></name><name><surname>Rivas</surname><given-names>MA</given-names></name><etal/></person-group><article-title>Transcriptome and genome sequencing uncovers functional variation in humans</article-title><source>Nature.</source><year>2013</year><volume>501</volume><fpage>506</fpage><lpage>511</lpage><pub-id pub-id-type="doi">10.1038/nature12531</pub-id><pub-id pub-id-type="pmid">24037378</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>GTEx Consortium, Laboratory, Data Analysis &#x00026;Coordinating Center (LDACC)&#x02014;Analysis Working Group, Statistical Methods groups&#x02014;Analysis Working Group, Enhancing GTEx (eGTEx) groups, NIH Common Fund, NIH/NCI</collab><etal/></person-group><article-title>Genetic effects on gene expression across human tissues</article-title><source>Nature</source><year>2017</year><volume>550</volume><fpage>204</fpage><lpage>213</lpage><pub-id pub-id-type="doi">10.1038/nature24277</pub-id><pub-id pub-id-type="pmid">29022597</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gasperini</surname><given-names>M</given-names></name><name><surname>Hill</surname><given-names>AJ</given-names></name><name><surname>McFaline-Figueroa</surname><given-names>JL</given-names></name><name><surname>Martin</surname><given-names>B</given-names></name><name><surname>Kim</surname><given-names>S</given-names></name><name><surname>Zhang</surname><given-names>MD</given-names></name><etal/></person-group><article-title>A Genome-wide Framework for Mapping Gene Regulation via Cellular Genetic Screens</article-title><source>Cell</source><year>2019</year><volume>176</volume><fpage>377</fpage><lpage>390.e19</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2018.11.029</pub-id><pub-id pub-id-type="pmid">30612741</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shen</surname><given-names>Y</given-names></name><name><surname>Yue</surname><given-names>F</given-names></name><name><surname>McCleary</surname><given-names>DF</given-names></name><name><surname>Ye</surname><given-names>Z</given-names></name><name><surname>Edsall</surname><given-names>L</given-names></name><name><surname>Kuan</surname><given-names>S</given-names></name><etal/></person-group><article-title>A map of the cis-regulatory sequences in the mouse genome</article-title><source>Nature.</source><year>2012</year><volume>488</volume><fpage>116</fpage><lpage>120</lpage><pub-id pub-id-type="doi">10.1038/nature11243</pub-id><pub-id pub-id-type="pmid">22763441</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thurman</surname><given-names>RE</given-names></name><name><surname>Rynes</surname><given-names>E</given-names></name><name><surname>Humbert</surname><given-names>R</given-names></name><name><surname>Vierstra</surname><given-names>J</given-names></name><name><surname>Maurano</surname><given-names>MT</given-names></name><name><surname>Haugen</surname><given-names>E</given-names></name><etal/></person-group><article-title>The accessible chromatin landscape of the human genome</article-title><source>Nature</source><year>2012</year><volume>489</volume><fpage>75</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1038/nature11232</pub-id><pub-id pub-id-type="pmid">22955617</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sheffield</surname><given-names>NC</given-names></name><name><surname>Thurman</surname><given-names>RE</given-names></name><name><surname>Song</surname><given-names>L</given-names></name><name><surname>Safi</surname><given-names>A</given-names></name><name><surname>Stamatoyannopoulos</surname><given-names>JA</given-names></name><name><surname>Lenhard</surname><given-names>B</given-names></name><etal/></person-group><article-title>Patterns of regulatory activity across diverse human cell types predict tissue identity, transcription factor binding, and long-range interactions</article-title><source>Genome Res</source><year>2013</year><volume>23</volume><fpage>777</fpage><lpage>788</lpage><pub-id pub-id-type="doi">10.1101/gr.152140.112</pub-id><pub-id pub-id-type="pmid">23482648</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>He</surname><given-names>B</given-names></name><name><surname>Chen</surname><given-names>C</given-names></name><name><surname>Teng</surname><given-names>L</given-names></name><name><surname>Tan</surname><given-names>K</given-names></name></person-group><article-title>Global view of enhancer-promoter interactome in human cells</article-title><source>Proc Natl Acad Sci USA</source><year>2014</year><volume>111</volume><fpage>E2191</fpage><lpage>E2199</lpage><pub-id pub-id-type="doi">10.1073/pnas.1320308111</pub-id><pub-id pub-id-type="pmid">24821768</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Roy</surname><given-names>S</given-names></name><name><surname>Siahpirani</surname><given-names>AF</given-names></name><name><surname>Chasman</surname><given-names>D</given-names></name><name><surname>Knaack</surname><given-names>S</given-names></name><name><surname>Ay</surname><given-names>F</given-names></name><name><surname>Stewart</surname><given-names>R</given-names></name><etal/></person-group><article-title>A predictive modeling approach for cell line-specific long-range regulatory interactions</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><fpage>8694</fpage><lpage>8712</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv865</pub-id><pub-id pub-id-type="pmid">26338778</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhao</surname><given-names>C</given-names></name><name><surname>Li</surname><given-names>X</given-names></name><name><surname>Hu</surname><given-names>H</given-names></name></person-group><article-title>PETModule: a motif module based approach for enhancer target gene prediction</article-title><source>Sci Rep</source><year>2016</year><volume>6</volume><fpage>30043</fpage><pub-id pub-id-type="doi">10.1038/srep30043</pub-id><pub-id pub-id-type="pmid">27436110</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Whalen</surname><given-names>S</given-names></name><name><surname>Truty</surname><given-names>RM</given-names></name><name><surname>Pollard</surname><given-names>KS</given-names></name></person-group><article-title>Enhancer-promoter interactions are encoded by complex genomic signatures on looping chromatin</article-title><source>Nat Genet Nat Res</source><year>2016</year><volume>48</volume><fpage>488</fpage><lpage>496</lpage><pub-id pub-id-type="doi">10.1038/ng.3539</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yang</surname><given-names>Y</given-names></name><name><surname>Zhang</surname><given-names>R</given-names></name><name><surname>Singh</surname><given-names>S</given-names></name><name><surname>Ma</surname><given-names>J</given-names></name></person-group><article-title>Exploiting sequence-based features for predicting enhancer&#x02013;promoter interactions</article-title><source>Bioinformatics</source><year>2017</year><volume>33</volume><fpage>i252</fpage><lpage>i260</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btx257</pub-id><pub-id pub-id-type="pmid">28881991</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xi</surname><given-names>W</given-names></name><name><surname>Beer</surname><given-names>MA</given-names></name></person-group><article-title>Local epigenomic state cannot discriminate interacting and non-interacting enhancer-promoter pairs with high accuracy. Noble WS, editor</article-title><source>PLoS Comput Biol</source><year>2018</year><volume>14</volume><fpage>e1006625</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1006625</pub-id><pub-id pub-id-type="pmid">30562350</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cao</surname><given-names>F</given-names></name><name><surname>Fullwood</surname><given-names>MJ</given-names></name></person-group><article-title>Inflated performance measures in enhancer-promoter interaction-prediction methods</article-title><source>Nature Genetics</source><year>2019</year><volume>326</volume><fpage>289</fpage><lpage>1198</lpage></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fishilevich</surname><given-names>S</given-names></name><name><surname>Nudel</surname><given-names>R</given-names></name><name><surname>Rappaport</surname><given-names>N</given-names></name><name><surname>Hadar</surname><given-names>R</given-names></name><name><surname>Plaschkes</surname><given-names>I</given-names></name><name><surname>Iny Stein</surname><given-names>T</given-names></name><etal/></person-group><article-title>GeneHancer: genome-wide integration of enhancers and target genes in GeneCards</article-title><source>Database (Oxford)</source><year>2017</year><volume>2017</volume><fpage>1217</fpage><pub-id pub-id-type="doi">10.1093/database/bax028</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><mixed-citation publication-type="other">Schreiber J, Singh R, Bilmes J, bioRxiv WN, 2019. A pitfall for machine learning methods aiming to predict across cell types. <ext-link ext-link-type="uri" xlink:href="http://biorxiv.org">biorxiv.org</ext-link>. Accessed 1 June 2019.</mixed-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Forcato</surname><given-names>M</given-names></name><name><surname>Nicoletti</surname><given-names>C</given-names></name><name><surname>Pal</surname><given-names>K</given-names></name><name><surname>Livi</surname><given-names>CM</given-names></name><name><surname>Ferrari</surname><given-names>F</given-names></name><name><surname>Bicciato</surname><given-names>S</given-names></name></person-group><article-title>Comparison of computational methods for hi-C data analysis</article-title><source>Nat Methods</source><year>2017</year><volume>14</volume><fpage>679</fpage><lpage>685</lpage><pub-id pub-id-type="doi">10.1038/nmeth.4325</pub-id><pub-id pub-id-type="pmid">28604721</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rao</surname><given-names>SSP</given-names></name><name><surname>Huang</surname><given-names>S-C</given-names></name><name><surname>Glenn St Hilaire</surname><given-names>B</given-names></name><name><surname>Engreitz</surname><given-names>JM</given-names></name><name><surname>Perez</surname><given-names>EM</given-names></name><name><surname>Kieffer-Kwon</surname><given-names>K-R</given-names></name><etal/></person-group><article-title>Cohesin loss eliminates all loop domains</article-title><source>Cell.</source><year>2017</year><volume>171</volume><fpage>305</fpage><lpage>324</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2017.09.026</pub-id><pub-id pub-id-type="pmid">28985562</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><mixed-citation publication-type="other">Moore JE, Pratt HE, Purcaro MJ, Weng Z. A curated benchmark of enhancer-gene interactions for evaluating enhancer-target gene prediction methods. Github. 2019. <ext-link ext-link-type="uri" xlink:href="https://github.com/weng-lab/BENGI">https://github.com/weng-lab/BENGI</ext-link>. Accessed 1 Dec 2019.</mixed-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grant</surname><given-names>CE</given-names></name><name><surname>Bailey</surname><given-names>TL</given-names></name><name><surname>Noble</surname><given-names>WS</given-names></name></person-group><article-title>FIMO: scanning for occurrences of a given motif</article-title><source>Bioinformatics.</source><year>2011</year><volume>27</volume><fpage>1017</fpage><lpage>1018</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btr064</pub-id><pub-id pub-id-type="pmid">21330290</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kulakovskiy</surname><given-names>IV</given-names></name><name><surname>Vorontsov</surname><given-names>IE</given-names></name><name><surname>Yevshin</surname><given-names>IS</given-names></name><name><surname>Sharipov</surname><given-names>RN</given-names></name><name><surname>Fedorova</surname><given-names>AD</given-names></name><name><surname>Rumynskiy</surname><given-names>EI</given-names></name><etal/></person-group><article-title>HOCOMOCO: towards a complete collection of transcription factor binding models for human and mouse via large-scale ChIP-Seq analysis</article-title><source>Nucleic Acids Res</source><year>2018</year><volume>46</volume><fpage>D252</fpage><lpage>D259</lpage><pub-id pub-id-type="doi">10.1093/nar/gkx1106</pub-id><pub-id pub-id-type="pmid">29140464</pub-id></element-citation></ref></ref-list></back></article>