<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.1 20151215//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1.dtd?><?SourceDTD.Version 1.1?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">IUCrJ</journal-id><journal-id journal-id-type="iso-abbrev">IUCrJ</journal-id><journal-id journal-id-type="publisher-id">IUCrJ</journal-id><journal-title-group><journal-title>IUCrJ</journal-title></journal-title-group><issn pub-type="epub">2052-2525</issn><publisher><publisher-name>International Union of Crystallography</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7642787</article-id><article-id pub-id-type="publisher-id">lt5030</article-id><article-id pub-id-type="doi">10.1107/S2052252520013603</article-id><article-id pub-id-type="coden">IUCRAJ</article-id><article-id pub-id-type="pii">S2052252520013603</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Papers</subject></subj-group></article-categories><title-group><article-title>Hirshfeld atom like refinement with alternative electron density partitions</article-title><alt-title>HAR with alternative electron density partitions</alt-title></title-group><contrib-group><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0003-4448-445X</contrib-id><name><surname>Chodkiewicz</surname><given-names>Micha&#x00142; Leszek</given-names></name><xref ref-type="aff" rid="a">a</xref><xref ref-type="corresp" rid="cor">*</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0002-9956-5356</contrib-id><name><surname>Woi&#x00144;ska</surname><given-names>Magdalena</given-names></name><xref ref-type="aff" rid="a">a</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid" authenticated="true">https://orcid.org/0000-0002-0277-294X</contrib-id><name><surname>Wo&#x0017a;niak</surname><given-names>Krzysztof</given-names></name><xref ref-type="aff" rid="a">a</xref></contrib><aff id="a">
<label>a</label>Biological and Chemical Research Centre, Department of Chemistry, <institution>University of Warsaw</institution>, &#x0017b;wirki i Wigury 101, Warszawa, 02-089 Warszawa, <country>Poland</country>
</aff></contrib-group><author-notes><corresp id="cor">Correspondence e-mail: <email><EMAIL></email></corresp></author-notes><pub-date pub-type="collection"><day>01</day><month>11</month><year>2020</year></pub-date><pub-date pub-type="epub"><day>29</day><month>10</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>29</day><month>10</month><year>2020</year></pub-date><!-- PMC Release delay is 0 months and 0 days and was based on the <pub-date pub-type="epub"/>. --><volume>7</volume><issue>Pt 6</issue><issue-id pub-id-type="publisher-id">m200600</issue-id><fpage>1199</fpage><lpage>1215</lpage><history><date date-type="received"><day>26</day><month>6</month><year>2020</year></date><date date-type="accepted"><day>12</day><month>10</month><year>2020</year></date></history><permissions><copyright-statement>&#x000a9; Chodkiewicz et al. 2020</copyright-statement><copyright-year>2020</copyright-year><license license-type="open-access" xlink:href="http://creativecommons.org/licenses/by/4.0/"><license-p>This is an open-access article distributed under the terms of the Creative Commons Attribution (CC-BY) Licence, which permits unrestricted
use, distribution, and reproduction in any medium, provided the original authors and source are cited.</license-p><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/">http://creativecommons.org/licenses/by/4.0/</ali:license_ref></license></permissions><self-uri xlink:href="https://doi.org/10.1107/S2052252520013603">A full version of this article is available from Crystallography Journals Online.</self-uri><abstract abstract-type="toc"><p>In this work, various models of atomic electron density were applied in a generalized version of the Hirshfeld atom refinement to three organic structures.</p></abstract><abstract><p>Hirshfeld atom refinement is one of the most successful methods for the accurate determination of structural parameters for hydrogen atoms from X-ray diffraction data. This work introduces a generalization of the method [generalized atom refinement (GAR)], consisting of the application of various methods of partitioning electron density into atomic contributions. These were tested on three organic structures using the following partitions: Hirshfeld, iterative Hirshfeld, iterative stockholder, minimal basis iterative stockholder and Becke. The effects of partition choice were also compared with those caused by other factors such as quantum chemical methodology, basis set, representation of the crystal field and a combination of these factors. The differences between the partitions were small in terms of <italic>R</italic> factor (<italic>e.g.</italic> much smaller than for refinements with different quantum chemistry methods, <italic>i.e.</italic> Hartree&#x02013;Fock and coupled cluster) and therefore no single partition was clearly the best in terms of experimental data reconstruction. In the case of structural parameters the differences between the partitions are comparable to those related to the choice of other factors. We have observed the systematic effects of the partition choice on bond lengths and ADP values of polar hydrogen atoms. The bond lengths were also systematically influenced by the choice of electron density calculation methodology. This suggests that GAR-derived structural parameters could be systematically improved by selecting an optimal combination of the partition and quantum chemistry method. The results of the refinements were compared with those of neutron diffraction experiments. This allowed a selection of the most promising partition methods for further optimization of GAR settings, namely the Hirshfeld, iterative stockholder and minimal basis iterative stockholder.</p></abstract><kwd-group><kwd>Hirshfeld atom refinement</kwd><kwd>electron density partition</kwd><kwd>generalized atom refinement</kwd><kwd>GAR</kwd><kwd>HAR</kwd></kwd-group><funding-group><award-group><funding-source>Narodowym Centrum Nauki</funding-source><award-id>OPUS grant number 2018/31/B/ST4/02142</award-id></award-group><award-group><funding-source>Wroclaw Centre for Networking and Supercomputing</funding-source><award-id>115</award-id></award-group><award-group><funding-source>European Regional Development Fund under the Operational Programme Innovative Economy</funding-source><award-id>years 2007 &#x02013; 2013</award-id></award-group><award-group><funding-source>Foundation for Polish Science</funding-source><award-id>TEAM TECH Core Facility for crystallographic and biophysical research to support the development of medicinal products</award-id></award-group><funding-statement>This work was funded by <funding-source>Narodowym Centrum Nauki</funding-source> grant <award-id>OPUS grant number 2018/31/B/ST4/02142</award-id>. <funding-source>Wroclaw Centre for Networking and Supercomputing</funding-source> grant <award-id>115</award-id>. <funding-source>European Regional Development Fund under the Operational Programme Innovative Economy</funding-source> grant <award-id>years 2007 &#x02013; 2013</award-id>. <funding-source>Foundation for Polish Science</funding-source> grant <award-id>TEAM TECH Core Facility for crystallographic and biophysical research to support the development of medicinal products</award-id>. </funding-statement></funding-group><counts><page-count count="17"/></counts></article-meta></front><body><sec sec-type="introduction" id="sec1"><label>1.</label><title>Introduction &#x000a0; </title><p>Ongoing progress in experimental technique development in X-ray crystallography makes this method an excellent tool to observe aspherical electron density deformations that can be attributed to bond formation and other interactions. However, the simplest and most popular approach, in fact, the only one practically available for many decades is the independent atom model (IAM), which treats the crystal as a set of spherical atomic densities centred on the atomic nuclei. However, it does not take into account the aspherical nature of atomic electron densities. For this reason, IAM fails to correctly describe those aspects of molecular geometry which are influenced by aspherical electron density deformations, such as the positions and anisotropic displacement parameters of hydrogen atoms. Consequently, the bond lengths formed by hydrogen atoms are on average shorter by 0.1&#x02005;&#x000c5; compared with their benchmark values as reported by neutron diffraction experiments and anisotropic refinements of hydrogen atom thermal motions resulting in non-positive definite ADP values. Therefore, methods introducing the various models of aspherical atomic scattering factors were developed (Weiss, 1964<xref ref-type="bibr" rid="bb64"> &#x025b8;</xref>; DeMarco &#x00026; Weiss, 1965<xref ref-type="bibr" rid="bb15"> &#x025b8;</xref>; Kurki-Suonio, 1968<xref ref-type="bibr" rid="bb39"> &#x025b8;</xref>; Stewart, 1969<xref ref-type="bibr" rid="bb57"> &#x025b8;</xref>, 1973<xref ref-type="bibr" rid="bb58"> &#x025b8;</xref>; Hirshfeld, 1971<xref ref-type="bibr" rid="bb23"> &#x025b8;</xref>; Hansen &#x00026; Coppens, 1978<xref ref-type="bibr" rid="bb22"> &#x025b8;</xref>). Unfortunately the most successful and popular of them, the multipole model proposed by Hansen &#x00026; Coppens (1978<xref ref-type="bibr" rid="bb22"> &#x025b8;</xref>), does not allow for free refinement of hydrogen atom positions and ADP values (Hoser <italic>et al.</italic>, 2009<xref ref-type="bibr" rid="bb25"> &#x025b8;</xref>), except in certain special cases of high-resolution good-quality data (Zhurov <italic>et al.</italic>, 2011<xref ref-type="bibr" rid="bb70"> &#x025b8;</xref>; Woinska <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb68"> &#x025b8;</xref>). This problem was overcome by use of the transferable aspherical atom model (TAAM) which takes advantage of the fact that the parameters of the multipole model are similar to those of atoms in similar chemical environments (Pichon-Pesme <italic>et al.</italic>, 1995<xref ref-type="bibr" rid="bb52"> &#x025b8;</xref>) and uses predefined sets of such parameters for refinement. This allows for free refinement of hydrogen positions and leads to more accurate <italic>X</italic>&#x02014;H bond lengths, as shown previously for a number of databanks of multipole model parameters (B&#x00105;k <italic>et al.</italic>, 2011<xref ref-type="bibr" rid="bb3"> &#x025b8;</xref>).</p><p>Performing Hirshfeld atom refinement (HAR) (Jayatilaka &#x00026; Dittrich, 2008<xref ref-type="bibr" rid="bb29"> &#x025b8;</xref>; Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>) which utilizes stockholder partitioning (Hirshfeld, 1977<xref ref-type="bibr" rid="bb24"> &#x025b8;</xref>) of the electron density for a molecule in the crystal turned out to be an even more promising method as it avoids the atomic density transferability assumption used in TAAM and the limitations of the electron density model used in multipole formalism (Koritsanszky <italic>et al.</italic>, 2011<xref ref-type="bibr" rid="bb35"> &#x025b8;</xref>). This implements the atomic aspherical structure factors obtained from the Hirshfeld-partitioned electron density of the asymmetric unit/molecule/cluster in the crystal calculated by an iterative procedure with the effects of the crystal environment included via surrounding the central unit by a cluster of electric multipoles. The improved model of the aspherical atomic structure factor resulted in more accurate and precise refinement of the hydrogen atom positions and considerable progress in the refinement of hydrogen ADP values (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>; Woi&#x00144;ska <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb66"> &#x025b8;</xref>, 2017<xref ref-type="bibr" rid="bb67"> &#x025b8;</xref>; Malaspina <italic>et al.</italic>, 2017<xref ref-type="bibr" rid="bb44"> &#x025b8;</xref>; Orben &#x00026; Dittrich, 2014<xref ref-type="bibr" rid="bb51"> &#x025b8;</xref>; Dittrich <italic>et al.</italic>, 2017<xref ref-type="bibr" rid="bb16"> &#x025b8;</xref>; Sanjuan-Szklarz <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb53"> &#x025b8;</xref>), even for X-ray data of standard resolution. Therefore HAR was added to a group of methods for deriving ADP values for hydrogen atoms, including the &#x02018;TLS+ONIOM&#x02019; approach based on <italic>ab initio</italic> calculations (Whitten &#x00026; Spackman), refinement with the TAAM model (Dittrich <italic>et al.</italic>, 2008<xref ref-type="bibr" rid="bb17"> &#x025b8;</xref>), TLS-based analysis available via the web service <italic>SHADE</italic> (Munshi <italic>et al.</italic>, 2008<xref ref-type="bibr" rid="bb49"> &#x025b8;</xref>) and lattice dynamical models (Hoser &#x00026; Madsen, 2016<xref ref-type="bibr" rid="bb26"> &#x025b8;</xref>; 2017<xref ref-type="bibr" rid="bb27"> &#x025b8;</xref>).</p><p>HAR was applied to refinement of anharmonic thermal motions (Woinska <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb68"> &#x025b8;</xref>; Orben &#x00026; Dittrich, 2014<xref ref-type="bibr" rid="bb51"> &#x025b8;</xref>), refinement of compounds that contain transition metals (Woi&#x00144;ska <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb66"> &#x025b8;</xref>; Bu&#x0010d;insk&#x000fd; <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb9"> &#x025b8;</xref>, 2019<xref ref-type="bibr" rid="bb10"> &#x025b8;</xref>; Malaspina <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb45"> &#x025b8;</xref>) and refinements including relativistic effects (Bu&#x0010d;insk&#x000fd; <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb9"> &#x025b8;</xref>, 2019<xref ref-type="bibr" rid="bb10"> &#x025b8;</xref>; Malaspina <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb45"> &#x025b8;</xref>). Initial work aimed at optimizing HAR for the refinement of macromolecules is also available: HAR-ELMO (Malaspina <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb45"> &#x025b8;</xref>) and fragHAR (Bergmann <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb5"> &#x025b8;</xref>). A recent study of TAAM refinement (K. Jha <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb31"> &#x025b8;</xref>) using the same set of test systems, as in an analogous study of HAR (Woi&#x00144;ska <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb66"> &#x025b8;</xref>) revealed that HAR produced bond lengths slightly closer to those obtained from neutron diffraction than TAAM. The average bond length underestimation was 0.020&#x02005;&#x000c5; for TAAM and 0.014&#x02005;&#x000c5; for HAR. It should be noted that such results apply for specific TAAM parameterizations and specific HAR methodologies (defined by the quantum chemical method, basis set and representation of crystal field).</p><p>Nevertheless, HAR is still not a fully mature method since there are still many areas with potential for improvement including long computational times required for repeated quantum mechanical calculation; quality of hydrogen ADP values refined with HAR; refinement of structures other than those of molecular crystals (network structures, ionic crystals) may be suboptimal; refinement of disorder is not yet properly handled; refinement of structures containing heavy metals is difficult due to the limited choice of available basis sets and challenges related to application of relativistic methods; lack of a well established optimal combination of settings (including the quantum chemistry method, basis set and representation of crystal field).</p><p>It must also be stressed that increasing the applicability of HAR, adapting the method to perform more challenging tasks such as refinement of macromolecular structures and increasing its popularity among users requires the creation of new software tools and/or the incorporation of the method in existing, commonly used programs dedicated to the processing of crystallographic data. A step in this direction was its implementation in a popular program for chemical crystallography <italic>OLEX2</italic> (Dolomanov <italic>et al.</italic>, 2009<xref ref-type="bibr" rid="bb18"> &#x025b8;</xref>) in the pre-installed <italic>HARt</italic> interface, enabling simple access to the basic functionalities of HAR (Fugel <italic>et al.</italic>, 2018<xref ref-type="bibr" rid="bb21"> &#x025b8;</xref>). Refinement with <italic>HARt</italic>, similar to the classical version of HAR (Jayatilaka &#x00026; Dittrich, 2008<xref ref-type="bibr" rid="bb29"> &#x025b8;</xref>; Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>) can be carried out against <italic>F</italic>, unlike IAM in <italic>OLEX2</italic>, which is based on <italic>F</italic>
<sup>2</sup>. As far as treatment of macromolecular structures is concerned, the development of HAR-based methods is proceeding in two directions: database-related methods and fragmentation techniques. The first involves the HAR-ELMO method (Malaspina <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb45"> &#x025b8;</xref>) which combines HAR with libraries of extremely localized molecular orbitals (Meyer &#x00026; Genoni, 2018<xref ref-type="bibr" rid="bb47"> &#x025b8;</xref>). This method was tested on a few small-molecule structures and proved capable of locating hydrogen atoms (in terms of bond lengths and ADP values) as accurately and precisely as traditional HAR at significantly lower computational costs. It was also successfully applied in the refinement of two polypeptides and the crystal structure of crambin (for two X-ray datasets collected at different subatomic resolutions). The other fragmentation-related method was first implemented in the fragHAR method (Bergmann <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb5"> &#x025b8;</xref>) using molecular fractionation with the conjugate caps method of fragmentation (Zhang &#x00026; Zhang, 2003<xref ref-type="bibr" rid="bb69"> &#x025b8;</xref>) in order to divide the molecule of interest into smaller fragments for which a wavefunction can be calculated with quantum mechanical methods. This method was implemented in the <italic>TONTO</italic> program (Jayatilaka &#x00026; Grimwood, 2003<xref ref-type="bibr" rid="bb30"> &#x025b8;</xref>) and tested on three oligopeptide crystal structures. It yields hydrogen positions and ADP values in statistical agreement with HAR; however, any interactions that involve hydrogen atoms must be given special attention during the fragment-selection process.</p><p>HAR is based on a model defined by computational chemistry methods (<italic>e.g.</italic> Hartree&#x02013;Fock or density functional theory with BLYP functional) as well as the basis set and representation of the molecular environment in the crystal. Although there are recommendations to guide the choice of model settings (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>; Fugel <italic>et al.</italic>, 2018<xref ref-type="bibr" rid="bb21"> &#x025b8;</xref>), these are not yet well established. Most HAR refinements have been performed with the Hartree&#x02013;Fock method or a density functional approach with BLYP functional, which are usually not considered methods of choice for accurate quantum mechanical calculation for molecular systems. Very recently, an application of more accurate quantum chemistry methods [second-order M&#x000f8;ller&#x02013;Plesset perturbation theory (MP2) and coupled cluster singles and doubles (CCSD)] have been tested (Wieduwilt <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb101"> &#x025b8;</xref>); however, conclusions about the advantages of these methods over computationally cheaper methods were not discussed. It was observed that an agreement between experimental and HAR-derived structure factors for <sc>l</sc>-alanine improves in the order HF, BLYP, MP2 &#x02243; B3LYP, CCSD.</p><p>In this study we examine additional options for choosing the model. We test the effects of the choice of electron density partitioning on atomic contributions. Since we are no longer limited to the partition proposed by Hirshfeld, the resulting method can be thought of as a generalization of HAR; to distinguish this from HAR it will be referred to as generalized atom refinement (GAR).</p><p>If the effects of partition choice on refinement accuracy are comparably important as those of the other settings in GAR and the newly introduced partitions are not clearly inferior compared with Hirshfeld partition then we can conclude that there is an important new dimension in the search for an optimal HAR-like model. In this work we aim to identify promising directions for such a search.</p><p>Partitioning electron density into atomic contributions is closely related to popular chemical concepts such as net atomic charge and atoms in molecules. Many partition methods have been proposed: those related to Bader&#x02019;s quantum theory of atoms in molecules (Bader, 1990<xref ref-type="bibr" rid="bb2"> &#x025b8;</xref>), to the Mulliken (1955<xref ref-type="bibr" rid="bb48"> &#x025b8;</xref>) and L&#x000f6;wdin (1955<xref ref-type="bibr" rid="bb42"> &#x025b8;</xref>) population analyses (among others), stockholder partitioning as proposed by Hirshfeld (1977<xref ref-type="bibr" rid="bb24"> &#x025b8;</xref>), and related methods such as the iterative Hirshfeld method (Bultinck <italic>et al.</italic>, 2007<xref ref-type="bibr" rid="bb11"> &#x025b8;</xref>), the iterative stockholder partitioning method (Lillestolen &#x00026; Wheatley, 2008<xref ref-type="bibr" rid="bb41"> &#x025b8;</xref>), the minimal basis iterative stockholder (Lillestolen &#x00026; Wheatley, 2008<xref ref-type="bibr" rid="bb41"> &#x025b8;</xref>) and the DDEC6 method (Manz &#x00026; Limas, 2016<xref ref-type="bibr" rid="bb46"> &#x025b8;</xref>). Electron density partitions are also used for computational purposes [<italic>e.g.</italic> in the Becke scheme for numerical integration (Becke, 1988<xref ref-type="bibr" rid="bb4"> &#x025b8;</xref>)]. In this preliminary test of the effect of electron density partitioning on HAR-like refinement, we tested partitions implemented in the quantum chemistry program <italic>HORTON</italic> (Verstraelen <italic>et al.</italic>, 2017<xref ref-type="bibr" rid="bb61"> &#x025b8;</xref>) which mainly includes partitions inspired by the one introduced by Hirshfeld (1977<xref ref-type="bibr" rid="bb24"> &#x025b8;</xref>).</p></sec><sec sec-type="methods" id="sec2"><label>2.</label><title>Methodology &#x000a0; </title><sec id="sec2.1"><label>2.1.</label><title>Investigated structures &#x000a0; </title><p>In this work, three organic systems were selected for testing: urea, oxalic acid dihydrate and 9,10-bis-di&#x000ad;phenyl&#x000ad;thio&#x000ad;phospho&#x000ad;ranylanthracene&#x000b7;toluene (SPAnPS). Urea and oxalic acid dihydrate have been used in many studies on accurate refinements with aspherical atom models, and for electron density distributions in crystals (Stevens <italic>et al.</italic>, 1979<xref ref-type="bibr" rid="bb55"> &#x025b8;</xref>; Stevens &#x00026; Coppens, 1980<xref ref-type="bibr" rid="bb56"> &#x025b8;</xref>; Zobel <italic>et al.</italic>, 1993<xref ref-type="bibr" rid="bb71"> &#x025b8;</xref>; Krijn <italic>et al.</italic>, 1988<xref ref-type="bibr" rid="bb36"> &#x025b8;</xref>; Swaminathan <italic>et al.</italic>, 1984<xref ref-type="bibr" rid="bb59"> &#x025b8;</xref>; Gatti <italic>et al.</italic>, 1994<xref ref-type="bibr" rid="bb106"> &#x025b8;</xref>; Birkedal <italic>et al.</italic>, 2004<xref ref-type="bibr" rid="bb6"> &#x025b8;</xref>; Jayatilaka &#x00026; Dittrich, 2008<xref ref-type="bibr" rid="bb29"> &#x025b8;</xref>; Pisani <italic>et al.</italic>, 2011<xref ref-type="bibr" rid="bb107"> &#x025b8;</xref>; Wall, 2016<xref ref-type="bibr" rid="bb105"> &#x025b8;</xref>). Urea and oxalic acid are small polar organic molecules, which makes testing them relatively easy with, in general, computationally demanding methods such as GAR. The polarity of these systems is an advantage when examining the differences between the Hirshfeld and iterative Hirshfeld partitions as they are expected to be more pronounced than in non-polar systems. Ionic systems would probably be even more appropriate for such comparison, but HAR methodology for these kinds of systems is not yet well established so was not examined. The third system, SPAnPS, contains substantially larger molecules with no polar hydrogen atoms. Each X-ray dataset has known neutron measurements at the same temperature that we measured. We used the following sources of datasets/structures: urea &#x02013; neutron structure (Swaminathan <italic>et al.</italic>, 1984<xref ref-type="bibr" rid="bb59"> &#x025b8;</xref>) and X-ray data (Birkedal <italic>et al.</italic>, 2004<xref ref-type="bibr" rid="bb6"> &#x025b8;</xref>) both at 123&#x02005;K, oxalic acid (Kami&#x00144;ski <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb33"> &#x025b8;</xref>), and SPAnPS (K&#x000f6;hler <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb34"> &#x025b8;</xref>). ADP values for oxalic acid neutron measurement were scaled isotropically because we noticed systematic differences in ADP values of non-hydrogen atoms. The scale factors were derived by the method of least squares (Blessing, 1995<xref ref-type="bibr" rid="bb7"> &#x025b8;</xref>). One of possible explanations is a difference in true temperatures of diffraction experiments.</p></sec><sec id="sec2.2"><label>2.2.</label><title>Partitions &#x000a0; </title><p>The following partitions were examined in this study: the original stockholder partition proposed by Hirshfeld (H) (Hirshfeld, 1977<xref ref-type="bibr" rid="bb24"> &#x025b8;</xref>) which was used in HAR, the iterative Hirshfeld (IH) (Bultinck <italic>et al.</italic>, 2007<xref ref-type="bibr" rid="bb11"> &#x025b8;</xref>), iterative stockholder (IS) (Lillestolen &#x00026; Wheatley, 2008<xref ref-type="bibr" rid="bb41"> &#x025b8;</xref>), the minimal basis iterative stockholder (MBIS) (Verstraelen <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb62"> &#x025b8;</xref>) and the partition proposed by Becke (B) (Becke, 1988<xref ref-type="bibr" rid="bb4"> &#x025b8;</xref>). Most of the tested methods are based on the stockholder partition of the electron density, which expresses the electron density &#x003c1; of an atom <italic>a</italic> at point <italic>r</italic> as<disp-formula id="fd1"><graphic xlink:href="m-07-01199-efd1.jpg" mimetype="image" mime-subtype="gif" position="float" orientation="portrait"/></disp-formula>with a summation over all atoms in the system (indexed with subscript <italic>k</italic>), <italic>w<sub>k</sub></italic>(<italic>r</italic>) is the spherical weighting function for the <italic>k</italic>th atom and <italic>R<sub>k</sub></italic> is the position of the <italic>k</italic>th atom. Such methods can be viewed as an extension of the original stockholder method proposed by Hirshfeld.</p><p>In the original Hirshfeld partition the function <italic>w<sub>k</sub></italic>(<italic>r</italic>) corresponds to the spherically averaged atomic densities of the isolated atoms. Hirshfeld partitioning leads to relatively low partial charges (Davidson &#x00026; Chakravorty, 1992<xref ref-type="bibr" rid="bb14"> &#x025b8;</xref>), which has been considered to be a deficiency in the method (Bultinck <italic>et al.</italic>, 2007<xref ref-type="bibr" rid="bb11"> &#x025b8;</xref>). These low partial charges are not surprising since they are maximally similar to the isolated atoms under an information theory framework (Nalewajski &#x00026; Parr, 2000<xref ref-type="bibr" rid="bb50"> &#x025b8;</xref>).</p><p>The iterative Hirshfeld method is similar to Hirshfeld partitioning but also takes the atomic charges into account for the weighting function. The weighting function for a given atom is a combination of the electron densities of the isolated neutral atom and an isolated ion(s) of a given element. This combination is chosen in such a way that it reproduces the charge of the corresponding atom. This method produces considerably higher partial charges than Hirshfeld partitioning which can lead to problems [<italic>e.g.</italic> in the case of one highly polar oxide, calculation of the electron density of a nonexistant oxygen dianion was required (Verstraelen <italic>et al.</italic>, 2013<xref ref-type="bibr" rid="bb60"> &#x025b8;</xref>)].</p><p>The iterative stockholder method does not require supplementary atomic density gas-phase calculations. It uses spherically averaged atomic densities for the atoms as weight functions with the initial weight functions normalized to <italic>w<sub>k</sub></italic>(<italic>r</italic>) = 1 for all atoms. While this method avoids some problems which appear in the Hirshfeld and iterative Hirshfeld methods, it has been shown (Bultinck <italic>et al.</italic>, 2009<xref ref-type="bibr" rid="bb12"> &#x025b8;</xref>) that the spherically averaged atomic densities resulting from this method can be not monotonically decaying, a counterintuitive result.</p><p>In the case of the minimal basis iterative stockholder method, the weighting function for a given atom is expressed using a minimal basis set of spherical Slater-type functions. This method is similar to the iterative stockholder method.</p><p>The partition proposed by Becke was designed to deal with three-dimensional integration in molecular systems and therefore it is not expected that the resulting atomic charges will correspond to chemical intuition. This partition is not based on stockholder-type partitions. Instead it is similar to Voronoi tessellation with adjustments for atomic sizes and &#x02018;softened&#x02019; boundaries (atomic densities are continuous).</p></sec><sec id="sec2.3"><label>2.3.</label><title>Implementation &#x000a0; </title><p>A locally modified version of <italic>OLEX2</italic> (Dolomanov <italic>et al.</italic>, 2009<xref ref-type="bibr" rid="bb18"> &#x025b8;</xref>) was used in the refinements. It incorporated a development version of the <italic>DiSCaMB</italic> library (Chodkiewicz <italic>et al.</italic>, 2018<xref ref-type="bibr" rid="bb108"> &#x025b8;</xref>) into the <italic>olex2.refine</italic> module which allows for the application of form factors corresponding to aspherical atomic densities.</p><p>In general, GAR implementation is quite similar to HAR implementation (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>; Fugel <italic>et al.</italic>, 2018<xref ref-type="bibr" rid="bb21"> &#x025b8;</xref>). The information necessary for electron density calculation for a non-periodic molecular system representing a given crystal structure is generated by computational chemistry software. This information incorporates either the set of molecular orbitals or the first-order reduced density matrix. The molecular system was built from molecule(s) comprising the asymmetric unit but this can be increased in order to better represent the molecular environment in the crystal. The effect of the crystal field can be modelled by surrounding the studied system with a set of atomic electric multipole moments. Atomic electron densities corresponding to a chosen partition of molecular electron density are calculated and used in the computation of atomic form factors. The form factors are then used in least squares refinement. Since the refinement leads to a new geometry, new quantum chemical calculations are run and a new least squares refinement is performed. This procedure consisting of quantum chemical calculations followed by least squares refinement is repeated until convergence criteria are met.</p><p>In practice, our implementation differs not only by allowing more electron density partitions, but also on many other points, among them the most important is probably refinement against <inline-formula><inline-graphic xlink:href="m-07-01199-efi1.jpg" mimetype="image" mime-subtype="gif"/></inline-formula>. Quantum chemical calculations are performed with <italic>GAUSSIAN16</italic> (Frisch <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb20"> &#x025b8;</xref>). The atomic multipole moments needed to represent the effect of the crystal field are calculated using the Hirshfeld partition, even if the atomic form factors are calculated for the other partitions. In this way the equal treatment of crystal field effects for refinements based on different partitions is preserved. Atomic multipoles are calculated in a self-consistent embedding scheme in which a newly calculated electron density is the source for new multipole moments which generate new representations of the crystal field, giving rise to new electron density results. Cycles of such calculations are performed until the differences between the components of the multipole moments are smaller than 0.003&#x02005;a.u. Only point charges and dipoles are used. Charges representing dipoles are separated by 0.02&#x02005;&#x000c5;.</p><p>The electron densites at molecular integration grid points were calculated with <italic>HORTON</italic> (Verstraelen <italic>et al.</italic>, 2017<xref ref-type="bibr" rid="bb61"> &#x025b8;</xref>), which reads in the first-order reduced density matrix from a Gaussian formatted check-point file. It also performs molecular electron density partitioning and prints out the atomic electron densities and the details of the molecular integration grid. This is implemented as a part of the <italic>DiSCaMB</italic> library which is responsible for the calculation of atomic multipoles and atomic form factors.</p><p>A Becke-type multicenter integration scheme for molecular integrals (Becke, 1988<xref ref-type="bibr" rid="bb4"> &#x025b8;</xref>) is used with pruned grids as defined in <italic>HORTON</italic> (Verstraelen <italic>et al.</italic>, 2017<xref ref-type="bibr" rid="bb61"> &#x025b8;</xref>). A radial grid is generated using the power transform (<italic>r</italic> = <italic>ax</italic>
<sup><italic>p</italic></sup>) and a Lebedev&#x02013;Laikov grid (Lebedev &#x00026; Laikov, 1999<xref ref-type="bibr" rid="bb40"> &#x025b8;</xref>) is used for angular integration. Form factor calculation involves the largest predefined grid in <italic>HORTON</italic>. Parameters of the grid are element specific, <italic>e.g.</italic> for carbon atoms 148 radial points are used and up to 1730 angular points.</p><p>Least squares refinement is performed against <inline-formula><inline-graphic xlink:href="m-07-01199-efi1.jpg" mimetype="image" mime-subtype="gif"/></inline-formula> as implemented in <italic>olex2.refine</italic> (Bourhis <italic>et al.</italic>, 2015<xref ref-type="bibr" rid="bb8"> &#x025b8;</xref>) with no use of additional <italic>SHELX</italic>-type parameters in the weighting scheme, <italic>i.e.</italic> the weights are defined as <inline-formula><inline-graphic xlink:href="m-07-01199-efi3.jpg" mimetype="image" mime-subtype="gif"/></inline-formula>, where <inline-formula><inline-graphic xlink:href="m-07-01199-efi4.jpg" mimetype="image" mime-subtype="gif"/></inline-formula> is the variance of the observed intensity. Absence of additional parameters in the weighting scheme allows for direct comparison of the discrepancy in <italic>R</italic> factors. The whole GAR procedure is finished when the difference in geometry after the least squares refinement and the one used in the quantum chemical calculations is less than 0.001&#x02005;&#x000c5; for both the atomic positions and the covalent bond lengths.</p></sec><sec id="sec2.4"><label>2.4.</label><title>Reported statistics &#x000a0; </title><p>In order to statistically assess the results of GAR refinements, we have compared discrepancy <italic>R</italic> factor values, the lengths of covalent bonds to hydrogen atoms and anisotropic displacement parameters for the hydrogen atoms. The difference between the values obtained from X-ray and neutron measurements are referred to as &#x00394;<italic>R</italic> for bond lengths and &#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub> for ADP tensor components. Their average absolute values are calculated (&#x02329;|&#x00394;<italic>R</italic>|&#x0232a; and &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a;, respectively) and also averaged (&#x02329;&#x00394;<italic>R</italic>&#x0232a;) in the case of &#x00394;<italic>R</italic>. We also calculated the average ratio of the square of the difference to its variance (we reported the root of that value) referred to as the weighted root mean square difference for the bond lengths, wRSMD(&#x00394;<italic>R</italic>), and for the ADP values, wRSMD(&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>). The statistics are defined as follows,<disp-formula id="fd2"><graphic xlink:href="m-07-01199-efd2.jpg" mimetype="image" mime-subtype="gif" position="float" orientation="portrait"/></disp-formula>and<disp-formula id="fd3"><graphic xlink:href="m-07-01199-efd3.jpg" mimetype="image" mime-subtype="gif" position="float" orientation="portrait"/></disp-formula>where the subscript X indicates X-ray values, N the neutron values while the angle brackets (chevrons) denote the average value of the expression in the brackets (averaged over atoms). It should be noted that the lower value of wRSMD is not an indicator that one method is better than another since it can happen that a method with higher accuracy and precision corresponds also to higher wRSMD, an example is provided further in the text where the effects of electron density partition choice on ADP values are discussed. We also used the average values of the <italic>S</italic>
<sub>12</sub> similarity index as introduced by Whitten &#x00026; Spackman (2006<xref ref-type="bibr" rid="bb65"> &#x025b8;</xref>). It is defined as <italic>S</italic>
<sub>12</sub> = 100(1 &#x02212; <italic>R</italic>
<sub>12</sub>), where <italic>R</italic>
<sub>12</sub> describes the overlap between the density distribution functions (<italic>p</italic>
<sub>1</sub>, <italic>p</italic>
<sub>2</sub>) for nuclei defined by two ADP tensors:<disp-formula id="fd4"><graphic xlink:href="m-07-01199-efd4.jpg" mimetype="image" mime-subtype="gif" position="float" orientation="portrait"/></disp-formula>In order to identify patterns in bond lengths, the average ratio of the X-ray to neutron bond lengths &#x02329;<italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub>&#x0232a; was calculated. In order to identify patterns in the ADP values, we compared the averaged ratios of the volumes of &#x02018;vibrational&#x02019; ellipsoids &#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a; [<italic>e.g.</italic> known from <italic>ORTEP</italic> (Johnson, 1965<xref ref-type="bibr" rid="bb32"> &#x025b8;</xref>)]. This ratio was calculated by taking into account: (1) the volume of the ellipsoid is proportional to the product of the lengths of its semi-axes, (2) the semi-axes of the thermal ellipsoids are proportional to the eigenvalues of the ADP tensor in Cartesian coordinates and (3) the product of the matrix eigenvalues is equal to its determinant. Taking (1)&#x02013;(3) together gives<disp-formula id="fd5"><graphic xlink:href="m-07-01199-efd5.jpg" mimetype="image" mime-subtype="gif" position="float" orientation="portrait"/></disp-formula>where <inline-formula><inline-graphic xlink:href="m-07-01199-efi5.jpg" mimetype="image" mime-subtype="gif"/></inline-formula> and <inline-formula><inline-graphic xlink:href="m-07-01199-efi6.jpg" mimetype="image" mime-subtype="gif"/></inline-formula> are the X-ray and neutron measurement-based ADP tensors in the Cartesian coordinates, respectively.</p><p>When the average over the atoms or bonds is reported, the uncertainty indicated in brackets is given as the population standard deviation, defined as<disp-formula id="fd6"><graphic xlink:href="m-07-01199-efd6.jpg" mimetype="image" mime-subtype="gif" position="float" orientation="portrait"/></disp-formula>
</p></sec></sec><sec sec-type="results" id="sec3"><label>3.</label><title>Results &#x000a0; </title><p>In order to compare the results of the GAR refinements with the different partitioning schemes, we focused on the structural parameters related to the hydrogen atoms, specifically the lengths of bonds involving hydrogen atoms and hydrogen ADP values, since accurate determination of these with X-ray refinement is more challenging than for heavier atoms. Unless stated otherwise, all results refer to those parameters.</p><p>In order to put the analyzed differences between the experimentally derived X-ray and neutron bond lengths (&#x00394;<italic>R</italic>) into context here are some related values. For example, the standard deviations for bond lengths from neutron measurements referenced in this work for N&#x02014;H and O&#x02014;H bonds lie in the ranges 2&#x02013;3 and 2.5&#x02013;6.3&#x02005;m&#x000c5; for HAR refinement (for B3LYP/cc-pVTZ), whereas in the case of C&#x02014;H bonds in SPAnPS those numbers are 1&#x02013;5&#x02005;m&#x000c5; for neutron data and 3.4&#x02013;5.7&#x02005;m&#x000c5; for HAR. For example, <italic>X</italic>&#x02014;H bond lengths from neutron diffraction data (Allen &#x00026; Bruno, 2010<xref ref-type="bibr" rid="bb1"> &#x025b8;</xref>) for functional groups are C(<italic>sp</italic>
<sup>3</sup>)&#x02014;O&#x02014;H 0.970&#x02005;(12), C(aryl)&#x02014;O&#x02014;H 0.992&#x02005;(17) and O&#x02014;C(<italic>sp</italic>
<sup>2</sup>)&#x02014;O&#x02014;H 1.018&#x02005;(22)&#x02005;&#x000c5;, and the differences between those values are 22, 26 and 48&#x02005;m&#x000c5;. The analogous values for HAR (Woi&#x00144;ska <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb66"> &#x025b8;</xref>; HAR with BLYP/cc-pVDZ) are (data for maximum resolution) 0.953&#x02005;(28), 0.965&#x02005;(32) and 0.983&#x02005;(35)&#x02005;&#x000c5; and the differences between the HAR and the neutron values (&#x00394;<italic>R</italic>) for maximum available resolution are &#x02212;17, &#x02212;27 and &#x02212;35&#x02005;m&#x000c5; (however, they are smaller for 0.8&#x02005;&#x000c5; resolution: &#x02212;8, &#x02212;8, &#x02212;32&#x02005;m&#x000c5;). In the case of IAM the differences are much larger: &#x02212;122, &#x02212;147 and &#x02212;132&#x02005;m&#x000c5;. Although the average distances for HAR are much closer to neutron ones than those from IAM, there are still considerable differences, comparable to the differences in O&#x02014;H bond lengths for different functional groups. This can be partially explained by the fact that different sets of molecules were used for evaluation of the averages for HAR and neutron data. On the other hand, the average HAR bond lengths reported by Woi&#x00144;ska <italic>et al.</italic> (2016<xref ref-type="bibr" rid="bb66"> &#x025b8;</xref>) seem to be systematically shorter than neutron bond lengths in the case of polar hydrogen atoms. We should however remember that these are shorter for HAR using DFT with the BLYP functional and cc-pVDZ basis set. HAR with Hartree&#x02013;Fock produces longer bonds for polar hydrogens [see the results in the work by Capelli <italic>et al.</italic> (2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>) or Wieduwilt <italic>et al.</italic> (2020<xref ref-type="bibr" rid="bb101"> &#x025b8;</xref>)] as well as higher <italic>R</italic> factors. The problem of the choice of optimal settings for HAR is still far from exhaustively explored.</p><sec id="sec3.1"><label>3.1.</label><title>Testing factors other than electron density partition &#x000a0; </title><p>Before discussing the effects of the electron density partitioning method, we will describe the results of tests involving other components of the model including the quantum chemistry method, the representation of the crystal field and the basis set, thereby allowing a comparison between the effects of the electron density partition and the effects related to the other settings (which were not tested in HAR against <inline-formula><inline-graphic xlink:href="m-07-01199-efi1.jpg" mimetype="image" mime-subtype="gif"/></inline-formula>). The tests of these factors were performed on urea and oxalic acid since, as relatively small systems, they are well suited for testing computationally demanding settings such as post-Hartree&#x02013;Fock methods or quantum mechanical representation of molecular surrounding.</p><p>These factors were tested with the Hirshfeld partition only. Unless stated otherwise, electron density was calculated using B3LYP, the cc-pVTZ basis set, and the crystal field was represented by atomic point charges and dipoles located at the atoms in molecules with at least one atom within 8&#x02005;&#x000c5; of any atom of the molecule for which the wavefunction is calculated. Those settings were selected on the basis of results from the initial phase of refinements for this section. Ideally when testing one of the components of the model, one would use the optimal setting for the other components. This is not possible in practice and not always necessary. At some point, higher levels of theory ceased producing improvements in the results. Little gain has been observed upon switching from the cc-pVTZ to the cc-pVQZ basis set, suggesting that the cc-pVTZ set is more than adequate. In the case of crystal field representation, a model with point charges seemed to produce results of similar quality to those from the more expensive model. B3LYP was selected as the quantum chemical method for the tests since it belonged to the set of methods (B3LYP, MP2, CCSD) which gave the best <italic>R</italic> factors in the initial tests and the two other methods are much more computationally demanding.</p><p>The results of the tests are presented in Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref>. Urea and oxalic acid dehydrate structures have five unique hydrogen atoms (see Fig. 1<xref ref-type="fig" rid="fig1"> &#x025b8;</xref>), all bonded to electronegative atoms (N and O), referred to throughout the text as polar hydrogen atoms. Values related to the structural descriptors in Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref> are given as an average over those atoms. Values of the descriptors are given separately for urea and oxalic acid in Table S1 of the <ext-link ext-link-type="uri" xlink:href="#suppinfoanchor">supporting information</ext-link>. Individual values of the structural parameters are shown in Figs. 2&#x02013;5.</p><sec id="sec3.1.1"><label>3.1.1.</label><title>Basis set &#x000a0; </title><p>The effect of the choice of basis set on HAR was tested with the family of correlation-consistent basis sets developed by Dunning and coworkers (Dunning, 1989<xref ref-type="bibr" rid="bb104"> &#x025b8;</xref>; Kendall <italic>et al.</italic>, 1992<xref ref-type="bibr" rid="bb103"> &#x025b8;</xref>; Woon &#x00026; Dunning, 1993<xref ref-type="bibr" rid="bb102"> &#x025b8;</xref>), from the smallest to the largest: cc-pVDZ, cc-pVTZ, cc-pVQZ. Each of the listed basis sets roughly doubles the number of functions of the previous one, making wavefunction calculations considerably slower since computational time formally scales as <italic>N</italic>
<sup>4</sup> (where <italic>N</italic> is the number of base functions) in the case of the least computationally expensive methods used.</p><p>Similar to the earlier tests of HAR (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>), it appears that the cc-pVTZ basis set is sufficient since switching to cc-pVQZ brings only a small reduction in <italic>wR</italic>
<sub>2</sub> [&#x02264;0.02 p.p. (percent point)] and relatively small changes in the structural parameters [see Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref> and Figs. 2<xref ref-type="fig" rid="fig2"> &#x025b8;</xref>(<italic>a</italic>)&#x02013;(<italic>c</italic>)]. Switching from cc-pVDZ to cc-pVTZ leads to a much larger reduction in <italic>wR</italic>
<sub>2</sub> (&#x02264;0.3 p.p.). The cc-pVTZ basis set is also visibly better than the cc-pVDZ basis set in terms of the similarity between the X-ray and neutron determined ADP values in the case of oxalic acid [see Figs. 2<xref ref-type="fig" rid="fig2"> &#x025b8;</xref>(<italic>b</italic>)&#x02013;2(<italic>c</italic>)], but not in the case of urea. Some patterns can be observed for volumes of thermal ellipsoids: for all five hydrogen atoms those derived with cc-pVDZ are smaller than those obtained with cc-VTZ [Fig. 2<xref ref-type="fig" rid="fig2"> &#x025b8;</xref>(<italic>d</italic>)]. Discrepancies in bond lengths were especially visible for O1&#x02014;H1 bond in oxalic acid [&#x00394;<italic>R</italic> 8(6)&#x02005;m&#x000c5; for cc-pVTZ and 24&#x02005;(6)&#x02005;m&#x000c5; for cc-pVDZ], which is involved in a very strong hydrogen bond (O1&#x02014;H1&#x022ef;O3, H1 to O3 distance 1.423&#x02005;&#x000c5;). Results for the other bonds alone do not suggest that cc-pVTZ is superior to cc-pVDZ for estimating bond lengths. Also the results in the work by Capelli <italic>et al.</italic> (2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>) do not show that the use of a larger basis set (cc-pVTZ) leads to better bond lengths (for HAR with Hartree&#x02013;Fock and for DFT with the BLYP functional). In that work it was also observed that cc-pVTZ leads to better ADP values. In summary, switching from cc-pVDZ to the larger basis set cc-pVTZ seems to improve the ADP values. In the case of one of the bonds in this study it also significantly improved the bond length, but it was unclear if this was an isolated case or if such improvement can be expected in certain situations.</p></sec><sec id="sec3.1.2"><label>3.1.2.</label><title>Quantum chemistry method &#x000a0; </title><p>The following methods were compared: DFT with B3LYP and the BLYP functional, Hartree&#x02013;Fock(HF), and the post-Hartree&#x02013;Fock methods: MP2 and CCSD. The same set of quantum chemistry methods was tested in refinements for <sc>l</sc>-alanine (Wieduwilt <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb101"> &#x025b8;</xref>); however, effects of the crystal field were not taken into account in that work. We can roughly order the accuracy of the methods for calculating the energies and corresponding properties in the following way: HF &#x0003c; MP2 &#x02264; CCSD and BLYP &#x0003c; B3LYP, also MP2 &#x02243; B3LYP. A general perception of the accuracy of the quantum chemistry method is reflected in the values of the discrepancy factor <italic>wR</italic>
<sub>2</sub>, where HF gave the highest values, greater than B3LYP by &#x02264;0.51 p.p. Similar trends were observed in the work by Wieduwilt <italic>et al.</italic> (2020<xref ref-type="bibr" rid="bb101"> &#x025b8;</xref>); however, in the current work MP2, CCSD and B3LYP gave similar agreement factors (measured as <italic>wR</italic>
<sub>2</sub>) whereas in the other study CCSD gave superior results. The higher <italic>wR</italic>
<sub>2</sub> values do not automatically translate into higher discrepancies between X-ray and neutron structural parameters (<italic>e.g.</italic> HF produces relatively good bond lengths in this work).</p><p>It has been observed that the choice of quantum chemistry method in HAR has a systematic effect on the lengths of bonds to hydrogen. For example, Hartree&#x02013;Fock of Gly-<sc>l</sc>-Ala produces systematically too long and BLYP too-short bonds (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>). Too-short N&#x02014;H bonds (up to 36&#x02005;m&#x000c5;) for refinement with BLYP were also reported for carbamazepine (Sovago <italic>et al.</italic>, 2016<xref ref-type="bibr" rid="bb54"> &#x025b8;</xref>). We have also observed some trends in bond lengths. For all bonds, they can be ordered in the following way (average X-ray value minus the neutron value in m&#x000c5; given in parentheses): BLYP (&#x02212;12.5) &#x0003c; B3LYP (&#x02212;8) &#x0003c; CCSD (&#x02212;3.6) &#x02264; HF (4.2). The same ordering was also observed for <sc>l</sc>-alanine (Wieduwilt <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb101"> &#x025b8;</xref>) for all polar bonds to hydrogen (in the &#x02013;NH<sub>3</sub>
<sup>+</sup> group). Although the differences are sometimes within experimental uncertainty, the fact that the order of the bond lengths can be observed for all bonds [Fig. 3<xref ref-type="fig" rid="fig3"> &#x025b8;</xref>(<italic>a</italic>)] suggests that this is not an artefact but a real trend in the bond length values. Some of the differences are quite substantial, the largest one, between BLYP and HF, takes, on average, a value of 17&#x02005;m&#x000c5;.</p><p>In terms of bond length accuracy (see &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; in Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref>), BLYP produces the largest discrepancies in bond lengths [12&#x02005;(6)&#x02005;m&#x000c5; on average], B3LYP smaller [8(5)&#x02005;m&#x000c5;] and Hartree&#x02013;Fock and post-Hartree&#x02013;Fock methods the smallest (3.5&#x02013;4.6&#x02005;m&#x000c5;). Different conclusions on relative accuracy could be drawn from results for Gly-<sc>l</sc>-Ala (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>), where the discrepancies for polar bonds for BLYP and Hartree&#x02013;Fock were quite similar (on average 11 versus 14&#x02005;m&#x000c5;). However, those results are not in contrast with the observation that post-Hartree&#x02013;Fock methods produce relatively good bond lengths and that B3LYP produces better bond lengths than BLYP. Certainly more tests are required to establish relative accuracy of bond length estimation with various quantum chemistry methods, but it can already be concluded that BLYP leads to too-short bond lengths.</p><p>Clear assessment of relative accuracy is also not possible for ADP determination. Some of the worst values of ADP accuracy descriptors are associated with the HF method [Figs. 3<xref ref-type="fig" rid="fig3"> &#x025b8;</xref>(<italic>b</italic>) and 3(<italic>c</italic>)], which also gave the worst ADP in terms of &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; and &#x02329;<italic>S</italic>
<sub>12</sub>&#x0232a; (Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref>). Also for Gly-<sc>l</sc>-Ala (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>) HF-derived ADPs were worse than those from BLYP in terms of &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a;. However, the evidence for the inferiority of the ADP values from HF calculations in the current work is not strong, hence it is probably not possible to draw such a conclusion on the basis of visual inspection of &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; for individual atoms [Fig. 3<xref ref-type="fig" rid="fig3"> &#x025b8;</xref>(<italic>b</italic>)]. In the case of <italic>S</italic>
<sub>12</sub> [Fig. 3<xref ref-type="fig" rid="fig3"> &#x025b8;</xref>(<italic>c</italic>)] there is one atom with a much higher <italic>S</italic>
<sub>12</sub> value for HF than for other methods (<italic>S</italic>
<sub>12</sub> = 8.7 versus &#x0003c;3) &#x02013; in oxalic acid, H1 which participates in a very strong hydrogen bond. Interestingly, the large &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; in urea from HF (0.0078 for HF versus 0.0044&#x02005;&#x000c5;<sup>2</sup> for MP2) does not translate into a visibly larger wRMSD (1.9 versus 1.7, see Table S1). This is caused by the fact that standard deviations for HF-derived <italic>U</italic>
<sub><italic>ij</italic></sub> are about 50% larger than those for MP2 (although they are quite similar in the case of oxalic acid). No trends in the volumes of thermal ellipsoids have been observed [Fig. 3<xref ref-type="fig" rid="fig3"> &#x025b8;</xref>(<italic>d</italic>)].</p></sec><sec id="sec3.1.3"><label>3.1.3.</label><title>Representation of molecular environment &#x000a0; </title><p>The most common approach applied in HAR uses point multipoles. It is clear that the lack of such representation leads to an inferior structural model in terms of the averaged discrepancies in both bond lengths and ADP values, and a larger <italic>wR</italic>
<sub>2</sub> for the tested systems (see Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref>). For all hydrogen atoms, ADP value agreement factors &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; and <italic>S</italic>
<sub>12</sub> for models with crystal field representations (CFR) were close to or better than those for models with no such representation [Figs. 4<xref ref-type="fig" rid="fig4"> &#x025b8;</xref>(<italic>b</italic>) and 4(<italic>c</italic>)]. Volumes of thermal ellipsoids were larger for models with no CFR [Fig. 4<xref ref-type="fig" rid="fig4"> &#x025b8;</xref>(<italic>d</italic>)]. For bond lengths, the largest discrepancies were also generated with models with no CFR [Fig. 4<xref ref-type="fig" rid="fig4"> &#x025b8;</xref>(<italic>a</italic>)]. Significant effects for neglecting strong intermolecular interactions were also observed in HAR refinements with fragmentation (fragHAR) for polypeptides (Bergmann <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb5"> &#x025b8;</xref>).</p><p>Typically in HAR only the molecules/ions constituting the asymmetric unit (hereafter referred as the &#x02018;central part&#x02019;) are treated at a quantum mechanical level. It was reported (Fugel <italic>et al.</italic>, 2018<xref ref-type="bibr" rid="bb21"> &#x025b8;</xref>) that the accuracy of HAR may improve when molecules/ions surrounding the central part are treated at the quantum mechanical level. We have tested two variants of this approach. In one, quantum mechanical calculations were performed on a cluster of molecules including the central part (urea or oxalic acid with two neighbouring water molecules) and molecules involved in hydrogen bonds with the central part, which lead to a cluster of 56 atoms in the case of urea, and 58 atoms in the case of oxalic acid. This variant is referred to as &#x02018;qm cluster smaller&#x02019; in Fig. 4<xref ref-type="fig" rid="fig4"> &#x025b8;</xref>. Another variant involved those molecules which are up to 3.5&#x02005;&#x000c5; from the central part. Corresponding clusters included 88 atoms in the case of urea and 136 atoms for oxalic acid. This variant is referred to as &#x02018;qm cluster larger&#x02019; in Fig. 4<xref ref-type="fig" rid="fig4"> &#x025b8;</xref>. The clusters were also surrounded by point multipoles (using an 8&#x02005;&#x000c5; threshold). Replacing the point-charge values and dipoles with explicit quantum mechanical representations of the surrounding molecules did not generate a visible improvement, but significantly increased the computational cost of refinement. It was suggested (Capelli <italic>et al.</italic>, 2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>) that such a representation could improve the accuracy of polar N&#x02014;H bond lengths and its lack could be a reason why that accuracy was lower than for C&#x02014;H bonds. Our results do not support such a supposition; however, it seems quite probable that an explicit quantum mechanical representation of neighbouring molecules would be advantageous for systems with very strong interactions. Oxalic acid dihydrate has a very strong hydrogen bond (O1&#x02014;H1&#x022ef;O3), but it was always treated at the quantum mechanical level in this work. This is related to the technical aspects of the implementation (all components of the asymmetric unit have to be represented in the same quantum mechanical calculations).</p></sec><sec id="sec3.1.4"><label>3.1.4.</label><title>Combination of less expensive HAR settings &#x000a0; </title><p>In this study, we have also examined a combination of settings which are computationally less expensive than B3LYP/cc-pVTZ with surrounding multipoles, which is used as a reference model in this paragraph. We have also performed TAAM refinement with the <italic>UBDB</italic> data bank (Volkov <italic>et al.</italic>, 2007<xref ref-type="bibr" rid="bb63"> &#x025b8;</xref>; Dominiak <italic>et al.</italic>, 2007<xref ref-type="bibr" rid="bb19"> &#x025b8;</xref>; Jarzembska &#x00026; Dominiak, 2012<xref ref-type="bibr" rid="bb28"> &#x025b8;</xref>; Kumar <italic>et al.</italic>, 2019<xref ref-type="bibr" rid="bb38"> &#x025b8;</xref>) using a locally modified version of <italic>OLEX2</italic>. The reference model clearly gave a better agreement factor than the computationally less expensive models (<italic>wR</italic>
<sub>2</sub>, see Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref>). It also outperformed them in terms of accuracy for ADP values in terms of &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; and <italic>S</italic>
<sub>12</sub> (see Table 1<xref ref-type="table" rid="table1"> &#x025b8;</xref>), those values are also consistently relatively low for the method for all hydrogen atoms [see Figs. 5<xref ref-type="fig" rid="fig5"> &#x025b8;</xref>(<italic>b</italic>) and 5(<italic>c</italic>)].</p><p>Interestingly the combination of the quantum chemistry method which led to the highest <italic>wR</italic>
<sub>2</sub> &#x02013; Hartree&#x02013;Fock &#x02013; with the smallest basis set tested &#x02013; cc-pVDZ &#x02013; led to the best bond lengths. This seems to be the result of two systematic effects: the Hartree&#x02013;Fock method giving slightly too-long bonds combined with a smaller basis set which, in the case of Hartree&#x02013;Fock, leads to shortening of the bond (which can be observed for all bonds with polar hydrogen atoms, see Fig. S2 of the <ext-link ext-link-type="uri" xlink:href="#suppinfoanchor">supporting information</ext-link>). Similarly in the work by Capelli <italic>et al.</italic> (2014<xref ref-type="bibr" rid="bb13"> &#x025b8;</xref>) it was observed that the smaller basis set does not lead to inferior bond lengths compared with the larger one for the Hartree&#x02013;Fock method and average bond lengths are smaller for this basis set.</p><p>In the case of urea, lower quality HAR models gave results comparable to TAAM(<italic>UBDB</italic>) [worse in the case of HAR with HF/cc-pVDZ(&#x02212;), see Table S1]. In the case of oxalic acid dehydrate, with very strong hydrogen bonds, TAAM(<italic>UBDB</italic>) gave clearly worse results [see Table S1 and Fig. 5<xref ref-type="fig" rid="fig5"> &#x025b8;</xref>(<italic>a</italic>)]. We have also included structural models based on the standardized neutron bond lengths in the comparison. They are in relatively good agreement with those from neutron experiments except for the H1 atom in oxalic acid involved in a very strong hydrogen bond, for which the discrepancy reaches 54&#x02005;m&#x000c5; (which is still less than 73&#x02005;m&#x000c5; in the case of TAAM).</p></sec></sec><sec id="sec3.2"><label>3.2.</label><title>Electron density partition &#x000a0; </title><p>In terms of <italic>wR</italic>
<sub>2</sub> statistics, the differences between the partitions are quite small (see Table 2<xref ref-type="table" rid="table2"> &#x025b8;</xref>), maximally 0.07 p.p., which was much less than between models using the cc-pVTZ and cc-pVDZ basis sets (maximum 0.3 p.p.) or between the HF and B3LYP methods (maximum 0.51 p.p.) or &#x02013; to a lesser extent &#x02013; between the models with and without surrounding multipoles (up to 0.15 p.p.). The differences were relatively small despite quite large differences in the atomic charge values (see Table 3<xref ref-type="table" rid="table3"> &#x025b8;</xref>), <italic>e.g.</italic> IH partition gave a charge of 0.53 on the oxalic acid H1 atom while the H partition gave 0.12, which means that for H partition the atoms carry almost twice as many electrons as for IH partition (0.88&#x02005;e versus 0.47&#x02005;e). Usually the absolute values of the charge for IH, IS and MBIS are significantly larger than those for H and B in the case of polar hydrogen atoms.</p><p>Standard uncertainties (SU) for bond lengths (&#x003c3;<sub>bond</sub>) and ADP values (&#x003c3;<sub>ADP</sub>) varied significantly between partitions (see Table 4<xref ref-type="table" rid="table4"> &#x025b8;</xref>). The highest values of &#x003c3;<sub>bond</sub> for the covalent bonds to hydrogen were observed for refinements with iterative Hirshfeld partition. A similar situation was found for the hydrogen ADP values. The smallest SU values were obtained for the B partition. Those differences were more pronounced for the polar hydrogen atoms (<italic>i.e.</italic> in urea and oxalic acid), <italic>e.g.</italic> the average &#x003c3;<sub>bond</sub> in oxalic acid is 8.6&#x02005;m&#x000c5; for IH and 2.7&#x02005;m&#x000c5; for B. For SPAnPS (the non-polar hydrogens), those values were 3.6 and 2.6&#x02005;m&#x000c5;, respectively.</p><p>In the case of SPAnPS, larger discrepancies were observed for atoms with larger ADP values and/or those bonded to carbon atoms with larger ADP values and/or with higher contributions from anharmonic terms in their atomic displacement descriptions. This effect is shown in Table 5<xref ref-type="table" rid="table5"> &#x025b8;</xref> for Hirshfeld partitions (see Table S2 for data for all partitions) in which the statistics for four groups of hydrogen atoms in SPAnPS are presented (see Fig. 6<xref ref-type="fig" rid="fig6"> &#x025b8;</xref>): (1) bonded to the carbon atoms for which no anharmonic motion was refined in the work by K&#x000f6;hler <italic>et al.</italic> (2019<xref ref-type="bibr" rid="bb34"> &#x025b8;</xref>), (2) other atoms in the larger molecule, (3) aryl hydrogens in toluene and (4) methyl hydrogens in toluene. There was a very clear increase in &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; for subsequent groups; when hydrogen atoms and their bonding partners had larger ADP values and the anharmonic displacement effects were more pronounced, &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; was larger. Similar patterns could be observed for &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a;, which was similar for the two groups of hydrogen atoms present in the larger molecule and much larger for the hydrogen atoms in toluene, especially in the methyl group. The larger absolute error in ADP values and bond lengths for atoms with larger ADP values was an expected observation. The more interesting one, however, is a clear increase of the ratio of X-ray to neutron bond lengths (see <italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub> in Table 5<xref ref-type="table" rid="table5"> &#x025b8;</xref>), which rise in the following way: 0.995, 0.998, 1.000, 1.025. One of the possible reasons could be an effect introduced by convolution approximation which is specific to X-ray models. The SPAnPS example suggests that, in general, the discrepancies resulting from large ADP values and anharmonic effects could be much larger than those related to electron density partitions.</p><p>Only atoms of group (1) were used for further analysis in SPAnPS (see Table 6<xref ref-type="table" rid="table6"> &#x025b8;</xref>) in order to avoid additional discrepancies between X-ray and neutron results which can be expected for atoms with possibly higher contributions from anharmonic effects and/or with large ADP values. Standard uncertainties for the bond lengths for this group are 2.8&#x02013;4.6&#x02005;m&#x000c5; for X-ray and 1&#x02013;1.5&#x02005;m&#x000c5; for neutron data. In this case, &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; for all partitions are very similar, about 5&#x02005;m&#x000c5;, and the differences in &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; between the partitions did not exceed 1&#x02005;m&#x000c5; and were smaller than the population standard deviations (&#x0223c;3&#x02005;m&#x000c5;), indicating that all partitions gave C&#x02014;H bond lengths of similar accuracy for that structure. While wRMSD values in the 1.2&#x02013;1.56 range did not give a clear indication that there was a statistical difference between the X-ray and neutron bond lengths, all GAR-derived bonds were shorter than the neutron ones suggesting that this is a systematic effect.</p><p>The situation is quite different in the case of urea and oxalic acid, which have only polar hydrogen atoms (see Tables 2<xref ref-type="table" rid="table2"> &#x025b8;</xref> and 7<xref ref-type="table" rid="table7"> &#x025b8;</xref>). Systematic differences between the partitions could be observed. In all cases the bond lengths can be ordered in the following way: B &#x0003c; H &#x0003c; IS, MBIS, IH [see Fig. 7(<italic>a</italic>)<xref ref-type="fig" rid="fig7"> &#x025b8;</xref>]. IS produced similar bond lengths to MBIS. For N&#x02014;H bonds (urea) HI also gave similar bond lengths except for O&#x02014;H (oxalic acid), which were all longer by about 5&#x02005;m&#x000c5; than for IS. The differences in the average bond length between the X-ray and neutron measurements for those bonds were B &#x02212;18.2, H &#x02212;8.3, IH 1.7, IS &#x02212;3.1 and MBIS &#x02212;3.6&#x02005;m&#x000c5; (see Table 7<xref ref-type="table" rid="table7"> &#x025b8;</xref>).</p><p>In terms of bond length, the accuracy for polar hydrogen atoms in MBIS, IS and IH are very similar [4.4&#x02005;(13), 5.0&#x02005;(33) and 5.5&#x02005;(37)&#x02005;m&#x000c5;], H is slightly worse [8(5)&#x02005;m&#x000c5;]. The differences between MBIS, IS and IH appear to be too small relative to the spread of the results (measured as population standard deviations, see Table 7<xref ref-type="table" rid="table7"> &#x025b8;</xref>) to conclude on the superiority of any of the methods in bond length estimation. H partition probably produces worse results, but more research is needed to justify this statement since the difference between the methods is not large compared with the error spread for each method (see Table 7<xref ref-type="table" rid="table7"> &#x025b8;</xref>) and standard uncertainties of bond lengths. We can clearly point to B as an inferior partition in terms of &#x00394;<italic>R</italic> (see Fig. S3 and Table 7<xref ref-type="table" rid="table7"> &#x025b8;</xref>). Interestingly, it is comparable to other methods in the case of urea but much worse in the case of oxalic acid.</p><p>Reported values of wRMSD provide information on the differences relative to its standard deviations. The value for the Hirshfeld partition (2.1) borders the value for which we can conclude that the bond lengths for this partition are statistically different from those obtained from neutron diffraction, in this case they are shorter.</p><p>In the case of ADP values, a clear trend could be observed for the volumes of thermal ellipsoids (see the &#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a; statistics in Table 2<xref ref-type="table" rid="table2"> &#x025b8;</xref>). For all individual hydrogen atoms in all of the tested systems, the volumes can be ordered in the following way: B &#x0003c; H, IS, MBIS &#x0003c; IH. This regular difference is especially striking in the case of the polar hydrogen atoms [see Fig. 7<xref ref-type="fig" rid="fig7"> &#x025b8;</xref>(<italic>d</italic>)]. Iterative Hirshfeld refinement produces worse ADP values then the other methods, in terms of both &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; and <italic>S</italic>
<sub>12</sub> statistics [see Figs. 7<xref ref-type="fig" rid="fig7"> &#x025b8;</xref>(<italic>b</italic>) and 7(<italic>c</italic>)]. ADP values from IH are too large, whereas B ADP values tend to be too small (see &#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a; values in Table 2<xref ref-type="table" rid="table2"> &#x025b8;</xref>). Data for IH and H are good examples of the situation when values with higher accuracy and precision, in this case for H in terms of |&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>| (Table 2<xref ref-type="table" rid="table2"> &#x025b8;</xref> and 7) and &#x003c3;<sub><italic>U</italic><sub><italic>ij</italic></sub></sub> (Table 4<xref ref-type="table" rid="table4"> &#x025b8;</xref>), can at the same time correspond to higher wRMSD values (Table 2<xref ref-type="table" rid="table2"> &#x025b8;</xref>).</p><p>With B producing inferior bond lengths, IH inferior ADP values and H probably overly short bond lengths, MBIS and IS seem to be the most promising methods. When comparing the partition methods we should bear in mind that the values are reported for a particular method of computational chemistry &#x02013; DFT with B3LYP functional &#x02013; and could differ for other methods. Recommendations for GAR settings should be given for a set of settings (<italic>e.g.</italic> IH with B3LYP/cc-pVTZ and point multipoles for crystal field representation) rather than for each setting separately (<italic>e.g.</italic> recommendation for IH without specifying the other settings). In principle, finding such an optimal set of settings would require an exhaustive search over all combinations of the settings. In practice we can try to identify the most promising combinations without exhaustive search. For example, IH produces too-large ADP values for B3LYP and replacing B3LYP with other quantum chemistry methods probably cannot solve this problem since for these methods no systematic trends were observed for thermal ellipsoids volumes. Similarly in the case of B partition. The most promising partitions are therefore H, IS and MBIS. In the case of quantum chemistry methods we can probably eliminate BLYP as it seems to produce too-short bonds and there is probably no partition among the &#x02018;promising&#x02019; ones that can correct for that. Also Hartree&#x02013;Fock would not be a good first choice since it produces clearly inferior agreement factors and this cannot be changed with a different partition, since partitions have a very small effect on the agreement factors. Therefore in the case of quantum chemistry methods B3LYP and post-Hartree&#x02013;Fock methods seem to be the most promising. Hartree&#x02013;Fock is also certainly worth further testing as it gave relatively accurate bond lengths in this work despite high <italic>wR</italic>
<sub>2</sub> agreement factor values.</p><p>From a practical point of view, refinements with IH partitions turned out to be the slowest to converge in terms of the number of wavefunction calculations required. For SPAnPS and urea, we observed an oscillatory behaviour of some structural parameters when comparing the results of consecutive least square refinements. To account for this, we averaged the structural parameters from the last two least squares refinements and averaged the atomic form factors from the last two calculations of the wavefunction, which led to significantly improved convergence (see Fig. S5).</p></sec></sec><sec sec-type="conclusions" id="sec4"><label>4.</label><title>Conclusions &#x000a0; </title><p>HAR is a refinement technique that allows for the determination of accurate structural parameters for hydrogen atoms from X-ray diffraction data. A generalization of HAR (GAR) was introduced in this work. Aside from the Hirshfeld electron density partition used in the original version of HAR, other partitions were applied: Becke, Hirshfeld, iterative Hirshfeld, iterative stockholder and the minimal basis iterative stockholder. The effects of the electron density partitioning choice on GAR-like refinement were tested on the structures of two small polar organic molecules (urea and oxalic acid dihydrate) and a larger one (SPAnPS) with no polar hydrogen atoms. The effects of partition choice were also compared with those caused by other settings of GAR such as (1) the quantum chemistry method (Hartree&#x02013;Fock, DFT with BLYP and DFT with B3LYP functional, CCSD, MP2), (2) the basis set, (3) representation of the crystal field and (4) some combination of these factors. Since the set of tested structures is rather small, the results should be treated as suggestive of the most promising HAR-like refinement settings as well as an indication of the most promising directions for a further search for the optimal choice of such settings, and not as a concluding recommendation. In all of the GAR refinements the hydrogen atom positions and anisotropic displacement parameters were refined freely and these refinements led to positively defined ADP tensors. The discrepancies between GAR and neutron-derived bond lengths to hydrogen atoms were much smaller than those which can be expected for the classical model (IAM) with spherical electron densities &#x02013; the maximal average difference for GAR refinement was 26&#x02005;m&#x000c5; and minimal 3.3&#x02005;m&#x000c5;, compared with about 100&#x02005;m&#x000c5; which can be expected for IAM. In terms of the <italic>wR</italic>
<sub>2</sub> agreement factor, the differences caused by the electron density partitioning scheme were much smaller than those introduced by the choice of basis set (<italic>e.g.</italic> cc-pVDZ versus cc-pVTZ), the method of wavefunction calculation (<italic>e.g.</italic> HF versus B3LYP), or the difference between the refinements with and without crystal field representation (in the case of polar molecules). Therefore none of the partitions were clearly the best in terms of experimental data reconstruction. In the case of the structural parameters, the differences between the partitions were comparable to those caused by changing the other settings. Since the refinement results depend on a combination of GAR settings, it is possible to asses such a combination and not an individual component of the GAR model. In principle, finding such an optimal combination of the settings would require an exhaustive search over all the possible combinations of the settings.</p><p>Our analysis does not clearly show which combinations of setting should be recommended for GAR. Among the tested partitions the most promising ones for the further search of optimal settings are Hirshfeld partition, iterative stockholder and minimal basis iterative stockholder. Among the quantum chemistry methods DFT with BLYP functional seems to be the least likely to be used in an optimal combination of settings.</p><p>We have analyzed structural parameters related to hydrogen atoms. Many systematic effects related to choice of GAR settings were observed, especially in the case of polar hydrogen atoms. For example, when comparing the results of the refinements using different partitions, in all of the cases bond lengths can be ordered in the following way: Becke &#x0003c; Hirshfeld &#x0003c; other partitions. A clear trend can be also observed in the thermal ellipsoid volumes &#x02013; Becke partition, for all of the atoms in all of the tested structures, produces the lowest values whereas the iterative Hirshfeld produces the largest values. The effects of a particular partition on the refinement could be summarized as follows (when used with the B3LYP functional and cc-pVTZ basis set):</p><p>&#x000b7; The Becke partition was expected to produce inferior results as it was designed purely for the purpose of numerical integration used in density functional theory calculations. It indeed gave the worst results in terms of <italic>wR</italic>
<sub>2</sub> (still a relatively small difference) and seemed to be more prone to produce inferior results than the other partitions. For polar hydrogens, it led to the shortest bonds and the smallest ADP values. However, it gave the smallest standard uncertainties for the hydrogen parameters.</p><p>&#x000b7; The Hirshfeld partition gave relatively good ADP values, <italic>wR</italic>
<sub>2</sub>, and slightly too-short bond lengths (8.3&#x02005;m&#x000c5; on average)</p><p>&#x000b7; Iterative Hirshfeld &#x02013;gave the largest bond lengths for the polar hydrogens, on average 1.7&#x02005;m&#x000c5; longer than those from neutron measurement. It also gave the largest ADP values, which led to the largest discrepancies in terms of absolute difference when compared with the neutron diffraction results. It also led to the highest standard uncertainties and was more prone to problems with convergence of refinement.</p><p>&#x000b7; The iterative stockholder and minimal basis iterative stockholder gave similar results. Bonds to polar hydrogen atoms were on average longer than those from the Hirshfeld partition and slightly more similar to those from the neutron diffraction measurements and the ADP values were relatively good. They seem to produce a slightly better result than other partitions but more research is needed to verify this observation.</p><p>Tests of the other factors influencing the HAR-like refinement performed on the crystal structures of urea and oxalic acid showed also some systematic trends (all refinements were performed with Hirshfeld partition of the electron density):</p><p>&#x000b7; Similarly to the previous publications regarding the classical HAR approach, the cc-pVDZ basis set was clearly inferior to cc-pVTZ in terms of <italic>wR</italic>
<sub>2</sub> but extending the basis set further to cc-pVQZ did not give a clear improvement. The cc-pVTZ basis set gave systematically larger volumes for thermal ellipsoids than cc-pVDZ. For oxalic acid it also led to clearly better structural parameters.</p><p>&#x000b7; Bonds to hydrogen atoms derived using GAR with the Hartree&#x02013;Fock method were systematically shorter when the cc-pVDZ basis set was used instead of cc-pVTZ (by 6&#x02005;m&#x000c5; on averge).</p><p>&#x000b7; Systematic differences in bond lengths calculated with different quantum chemistry methods have been observed for polar hydrogen atoms. They can be ordered in the following way for averaged X-ray&#x02013;neutron values (given in parentheses in m&#x000c5;): BLYP(&#x02212;12) &#x0003c; B3LYP(&#x02212;8) &#x0003c; CCSD(&#x02212;4) &#x0003c; HF(4).</p><p>&#x000b7; Hartree&#x02013;Fock was clearly an inferior method for wavefunction calculation in terms of <italic>wR</italic>
<sub>2</sub>. B3LYP seemed to be a better choice than BLYP in terms of bond length accuracy and agreement factors, and the application of the most expensive methods (MP2, CCSD) gave similar agreement factors as B3LYP but with better bond lengths. Yet when B3LYP was paired with other electron density partitions &#x02013; <italic>i.e.</italic> the iterative stockholder &#x02013; the bond length accuracy improved and was similar to the accuracy of MP2 and CCSD with the Hirshfeld partition.</p><p>&#x000b7; While there is a clear advantage in representing the crystal field with multipoles, we did not observe further improvement when treating the surrounding molecules quantum mechanically (such an improvement was expected for compounds with very strong intermolecular interactions). Refinement with no crystal field representation led to the largest volumes of thermal ellipsoids in the case of polar hydrogen atoms.</p><p>&#x000b7; TAAM, which is based on the use of fixed, predefined parameters derived from the Hansen&#x02013;Coppens multipole model, performed roughly similarly to HAR with B3LYP/cc-pVDZ and no crystal field representation with point multipoles or with HF/cc-pVDZ and point multipoles in the case of urea. It was however clearly worse than any of the HAR approaches in the case of oxalic acid, which is a system with very strong hydrogen bonds.</p><p>Those results may differ when other partition methods are used rather than the applied Hirshfeld, especially in the case of quantum chemistry methods which also exhibit systematic differences in bond lengths.</p><p>While it is becoming clear that GAR (including HAR) is probably the most accurate method for deriving structural parameters for hydrogen atoms from X-ray refinement, it is still unclear what the optimal method settings are. With this work we add another dimension to the methodology. The next step will be to test various combinations of the settings using a larger set of test structures and a wider choice of quantum chemistry methods and electron density partitions. Further improvement towards ultra-accurate X-ray refinements may also require a more advanced model of atomic displacements and an examination of the effects of the convolution approximation.</p></sec><sec sec-type="supplementary-material"><title>Supplementary Material</title><supplementary-material content-type="local-data"><p>Supporting figures and tables. DOI: <ext-link ext-link-type="uri" xlink:href="https://doi.org/10.1107/S2052252520013603/lt5030sup1.pdf">10.1107/S2052252520013603/lt5030sup1.pdf</ext-link>
</p><media mimetype="application" mime-subtype="pdf" xlink:href="m-07-01199-sup1.pdf" orientation="portrait" id="d38e117" position="anchor"/></supplementary-material><supplementary-material content-type="local-data"><media xlink:href="m-07-01199-sup2.zip"><caption><p>Click here for additional data file.</p></caption></media><p>Supporting cifs. DOI: <ext-link ext-link-type="uri" xlink:href="https://doi.org/10.1107/S2052252520013603/lt5030sup2.zip">10.1107/S2052252520013603/lt5030sup2.zip</ext-link>
</p></supplementary-material><supplementary-material content-type="local-data"><p>CCDC reference: <ext-link ext-link-type="uri" xlink:href="https://scripts.iucr.org/cgi-bin/cr.cgi?rm=csd&#x00026;csdid=2040066">2040066</ext-link>
</p></supplementary-material></sec></body><back><ack><p>The authors declare that they have no competing interests.</p></ack><ref-list><title>References</title><ref id="bb1"><mixed-citation publication-type="other">Allen, F. H. &#x00026; Bruno, I. J. (2010). <italic>Acta Cryst.</italic> B<bold>66</bold>, 380&#x02013;386.</mixed-citation></ref><ref id="bb2"><mixed-citation publication-type="other">Bader, R. F. W. (1990). <italic>Atoms in Molecules: a Quantum Theory</italic>. Oxford University Press.</mixed-citation></ref><ref id="bb3"><mixed-citation publication-type="other">B&#x00105;k, J. M., Domaga&#x00142;a, S., H&#x000fc;bschle, C., Jelsch, C., Dittrich, B. &#x00026; Dominiak, P. M. (2011). <italic>Acta Cryst.</italic> A<bold>67</bold>, 141&#x02013;153.</mixed-citation></ref><ref id="bb4"><mixed-citation publication-type="other">Becke, A. D. (1988). <italic>J. Chem. Phys.</italic>
<bold>88</bold>, 2547&#x02013;2553.</mixed-citation></ref><ref id="bb5"><mixed-citation publication-type="other">Bergmann, J., Davidson, M., Oksanen, E., Ryde, U. &#x00026; Jayatilaka, D. (2020). <italic>IUCrJ</italic>, <bold>7</bold>, 158&#x02013;165.</mixed-citation></ref><ref id="bb6"><mixed-citation publication-type="other">Birkedal, H., Madsen, D., Mathiesen, R. H., Knudsen, K., Weber, H.-P., Pattison, P. &#x00026; Schwarzenbach, D. (2004). <italic>Acta Cryst.</italic> A<bold>60</bold>, 371&#x02013;381.</mixed-citation></ref><ref id="bb7"><mixed-citation publication-type="other">Blessing, R. H. (1995). <italic>Acta Cryst.</italic> B<bold>51</bold>, 816&#x02013;823.</mixed-citation></ref><ref id="bb8"><mixed-citation publication-type="other">Bourhis, L. J., Dolomanov, O. V., Gildea, R. J., Howard, J. A. K. &#x00026; Puschmann, H. (2015). <italic>Acta Cryst.</italic> A<bold>71</bold>, 59&#x02013;75.</mixed-citation></ref><ref id="bb9"><mixed-citation publication-type="other">Bu&#x0010d;insk&#x000fd;, L., Jayatilaka, D. &#x00026; Grabowsky, S. (2016). <italic>J. Phys. Chem. A</italic>, <bold>120</bold>, 6650&#x02013;6669.</mixed-citation></ref><ref id="bb10"><mixed-citation publication-type="other">Bu&#x0010d;insk&#x000fd;, L., Jayatilaka, D. &#x00026; Grabowsky, S. (2019). <italic>Acta Cryst.</italic> A<bold>75</bold>, 705&#x02013;717.</mixed-citation></ref><ref id="bb12"><mixed-citation publication-type="other">Bultinck, P., Cooper, D. L. &#x00026; Van Neck, D. (2009). <italic>Phys. Chem. Chem. Phys.</italic>
<bold>11</bold>, 3424&#x02013;3429.</mixed-citation></ref><ref id="bb11"><mixed-citation publication-type="other">Bultinck, P., Van Alsenoy, C., Ayers, P. W. &#x00026; Carb&#x000f3;-Dorca, R. (2007). <italic>J. Chem. Phys.</italic>
<bold>126</bold>, 144111.</mixed-citation></ref><ref id="bb13"><mixed-citation publication-type="other">Capelli, S. C., B&#x000fc;rgi, H.-B., Dittrich, B., Grabowsky, S. &#x00026; Jayatilaka, D. (2014). <italic>IUCrJ</italic>, <bold>1</bold>, 361&#x02013;379.</mixed-citation></ref><ref id="bb108"><mixed-citation publication-type="other">Chodkiewicz, M. L., Migacz, S., Rudnicki, W., Makal, A., Kalinowski, J. A., Moriarty, N. W., Grosse-Kunstleve, R. W., Afonine, P. V., Adams, P. D. &#x00026; Dominiak, P. M. (2018). <italic>J. Appl. Cryst.</italic>
<bold>51</bold>, 193&#x02013;199. [Reference added OK?]</mixed-citation></ref><ref id="bb14"><mixed-citation publication-type="other">Davidson, E. R. &#x00026; Chakravorty, S. (1992). <italic>Theor. Chim. Acta</italic>, <bold>83</bold>, 319&#x02013;330.</mixed-citation></ref><ref id="bb15"><mixed-citation publication-type="other">DeMarco, J. J. &#x00026; Weiss, R. J. (1965). <italic>Phys. Rev.</italic>
<bold>137</bold>, A1869&#x02013;A1871.</mixed-citation></ref><ref id="bb16"><mixed-citation publication-type="other">Dittrich, B., L&#x000fc;bben, J., Mebs, S., Wagner, A., Luger, P. &#x00026; Flaig, R. (2017). <italic>Chem. Eur. J.</italic>
<bold>23</bold>, 4605&#x02013;4614.</mixed-citation></ref><ref id="bb17"><mixed-citation publication-type="other">Dittrich, B., McKinnon, J. J. &#x00026; Warren, J. E. (2008). <italic>Acta Cryst.</italic> B<bold>64</bold>, 750&#x02013;759.</mixed-citation></ref><ref id="bb18"><mixed-citation publication-type="other">Dolomanov, O. V., Bourhis, L. J., Gildea, R. J., Howard, J. A. K. &#x00026; Puschmann, H. (2009). <italic>J. Appl. Cryst.</italic>
<bold>42</bold>, 339&#x02013;341.</mixed-citation></ref><ref id="bb19"><mixed-citation publication-type="other">Dominiak, P. M., Volkov, A., Li, X., Messerschmidt, M. &#x00026; Coppens, P. (2007). <italic>J. Chem. Theory Comput.</italic>
<bold>3</bold>, 232&#x02013;247.</mixed-citation></ref><ref id="bb104"><mixed-citation publication-type="other">Dunning, T. H. (1989). <italic>J. Chem. Phys.</italic>
<bold>90</bold>, 1007&#x02013;1023.</mixed-citation></ref><ref id="bb20"><mixed-citation publication-type="other">Frisch, M. J., Trucks, G. W., Schlegel, H. B., Scuseria, G. E., Robb, M. A., Cheeseman, J. R., Scalmani, G., Barone, V., Petersson, G. A., Nakatsuji, H., Li, X., Caricato, M., Marenich, A. V., Bloino, J., Janesko, B. G., Gomperts, R., Mennucci, B., Hratchian, H. P., Ortiz, J. V., Izmaylov, A. F., Sonnenberg, J. L., Williams-Young, D., Ding, F., Lipparini, F., Egidi, F., Goings, J., Peng, B., Petrone, A., Henderson, T., Ranasinghe, D., Zakrzewski, V. G., Gao, J., Rega, N., Zheng, G., Liang, W., Hada, M., Ehara, M., Toyota, K., Fukuda, R., Hasegawa, J., Ishida, M., Nakajima, T., Honda, Y., Kitao, O., Nakai, H., Vreven, T., Throssell, K., Montgomery, J. A. Jr, Peralta, J. E., Ogliaro, F., Bearpark, M. J., Heyd, J. J., Brothers, E. N., Kudin, K. N., Staroverov, V. N., Keith, T. A., Kobayashi, R., Normand, J., Raghavachari, K., Rendell, A. P., Burant, J. C., Iyengar, S. S., Tomasi, J., Cossi, M., Millam, J. M., Klene, M., Adamo, C., Cammi, R., Ochterski, J. W., Martin, R. L., Morokuma, K., Farkas, O., Foresman, J. B. &#x00026; Fox, D. J. (2016). <italic>Gaussian16</italic>, Revision C. 01. Gaussian, Inc., Wallingford, Connecticut, USA.</mixed-citation></ref><ref id="bb21"><mixed-citation publication-type="other">Fugel, M., Jayatilaka, D., Hupf, E., Overgaard, J., Hathwar, V. R., Macchi, P., Turner, M. J., Howard, J. A. K., Dolomanov, O. V., Puschmann, H., Iversen, B. B., B&#x000fc;rgi, H.-B. &#x00026; Grabowsky, S. (2018). <italic>IUCrJ</italic>, <bold>5</bold>, 32&#x02013;44.</mixed-citation></ref><ref id="bb106"><mixed-citation publication-type="other">Gatti, C., Saunders, V. R. &#x00026; Roetti, C. (1994). <italic>J. Chem. Phys.</italic>
<bold>101</bold>, 10686&#x02013;10696.</mixed-citation></ref><ref id="bb22"><mixed-citation publication-type="other">Hansen, N. K. &#x00026; Coppens, P. (1978). <italic>Acta Cryst.</italic> A<bold>34</bold>, 909&#x02013;921.</mixed-citation></ref><ref id="bb23"><mixed-citation publication-type="other">Hirshfeld, F. L. (1971). <italic>Acta Cryst.</italic> B<bold>27</bold>, 769&#x02013;781.</mixed-citation></ref><ref id="bb24"><mixed-citation publication-type="other">Hirshfeld, F. L. (1977). <italic>Theor. Chim. Acta</italic>, <bold>44</bold>, 129&#x02013;138.</mixed-citation></ref><ref id="bb25"><mixed-citation publication-type="other">Hoser, A. A., Dominiak, P. M. &#x00026; Wo&#x0017a;niak, K. (2009). <italic>Acta Cryst.</italic> A<bold>65</bold>, 300&#x02013;311.</mixed-citation></ref><ref id="bb26"><mixed-citation publication-type="other">Hoser, A. A. &#x00026; Madsen, A. &#x000d8;. (2016). <italic>Acta Cryst.</italic> A<bold>72</bold>, 206&#x02013;214.</mixed-citation></ref><ref id="bb27"><mixed-citation publication-type="other">Hoser, A. A. &#x00026; Madsen, A. &#x000d8;. (2017). <italic>Acta Cryst.</italic> A<bold>73</bold>, 102&#x02013;114.</mixed-citation></ref><ref id="bb28"><mixed-citation publication-type="other">Jarzembska, K. N. &#x00026; Dominiak, P. M. (2012). <italic>Acta Cryst.</italic> A<bold>68</bold>, 139&#x02013;147.</mixed-citation></ref><ref id="bb29"><mixed-citation publication-type="other">Jayatilaka, D. &#x00026; Dittrich, B. (2008). <italic>Acta Cryst.</italic> A<bold>64</bold>, 383&#x02013;393.</mixed-citation></ref><ref id="bb30"><mixed-citation publication-type="other">Jayatilaka, D. &#x00026; Grimwood, D. J. (2003). <italic>Comput. Sci.</italic>
<bold>4</bold>, 142&#x02013;151.</mixed-citation></ref><ref id="bb31"><mixed-citation publication-type="other">Jha, K. K., Gruza, B., Kumar, P., Chodkiewicz, M. L. &#x00026; Dominiak, P. M. (2020). <italic>Acta Cryst.</italic> B<bold>76</bold>, 296&#x02013;306.</mixed-citation></ref><ref id="bb32"><mixed-citation publication-type="other">Johnson, C. K. (1965). <italic>ORTEP</italic>. Report ORNL-3794. Oak Ridge National Laboratory, Tennessee, USA.</mixed-citation></ref><ref id="bb33"><mixed-citation publication-type="other">Kami&#x00144;ski, R., Domaga&#x00142;a, S., Jarzembska, K. N., Hoser, A. A., Sanjuan-Szklarz, W. F., Gutmann, M. J., Makal, A., Mali&#x00144;ska, M., B&#x00105;k, J. M. &#x00026; Wo&#x0017a;niak, K. (2014). <italic>Acta Cryst.</italic> A<bold>70</bold>, 72&#x02013;91.</mixed-citation></ref><ref id="bb103"><mixed-citation publication-type="other">Kendall, R. A., Dunning, T. H. &#x00026; Harrison, R. J. (1992). <italic>J. Chem. Phys.</italic>
<bold>96</bold>, 6796&#x02013;6806.</mixed-citation></ref><ref id="bb34"><mixed-citation publication-type="other">K&#x000f6;hler, C., L&#x000fc;bben, J., Krause, L., Hoffmann, C., Herbst-Irmer, R. &#x00026; Stalke, D. (2019). <italic>Acta Cryst.</italic> B<bold>75</bold>, 434&#x02013;441.</mixed-citation></ref><ref id="bb35"><mixed-citation publication-type="other">Koritsanszky, T., Volkov, A. &#x00026; Chodkiewicz, M. (2011). <italic>New Directions in Pseudoatom-Based X-ray Charge Density Analysis</italic> In <italic>Electron Density and Chemical Bonding II. Structure and Bonding</italic>, vol 147. Springer, Berlin, Heidelberg.</mixed-citation></ref><ref id="bb36"><mixed-citation publication-type="other">Krijn, M. P. C. M., Graafsma, H. &#x00026; Feil, D. (1988). <italic>Acta Cryst.</italic> B<bold>44</bold>, 609&#x02013;616.</mixed-citation></ref><ref id="bb38"><mixed-citation publication-type="other">Kumar, P., Gruza, B., Bojarowski, S. A. &#x00026; Dominiak, P. M. (2019). <italic>Acta Cryst.</italic> A<bold>75</bold>, 398&#x02013;408.</mixed-citation></ref><ref id="bb39"><mixed-citation publication-type="other">Kurki-Suonio, K. (1968). <italic>Acta Cryst.</italic> A<bold>24</bold>, 379&#x02013;390.</mixed-citation></ref><ref id="bb40"><mixed-citation publication-type="other">Lebedev, V. I. &#x00026; Laikov, D. N. (1999). <italic>Dokl. Math.</italic>
<bold>59</bold>, 477&#x02013;481.</mixed-citation></ref><ref id="bb41"><mixed-citation publication-type="other">Lillestolen, T. C. &#x00026; Wheatley, R. J. (2008). <italic>Chem. Commun.</italic>
<bold>45</bold>, 5909.</mixed-citation></ref><ref id="bb42"><mixed-citation publication-type="other">L&#x000f6;wdin, P. (1955). <italic>J. Chem. Phys.</italic>
<bold>21</bold>, 374&#x02013;375.</mixed-citation></ref><ref id="bb43"><mixed-citation publication-type="other">Macrae, C. F., Sovago, I., Cottrell, S. J., Galek, P. T. A., McCabe, P., Pidcock, E., Platings, M., Shields, G. P., Stevens, J. S., Towler, M. &#x00026; Wood, P. A. (2020). <italic>J. Appl. Cryst.</italic>
<bold>53</bold>, 226&#x02013;235.</mixed-citation></ref><ref id="bb44"><mixed-citation publication-type="other">Malaspina, L. A., Edwards, A. J., Woi&#x00144;ska, M., Jayatilaka, D., Turner, M. J., Price, J. R., Herbst-Irmer, R., Sugimoto, K., Nishibori, E. &#x00026; Grabowsky, S. (2017). <italic>Cryst. Growth Des.</italic>
<bold>17</bold>, 3812&#x02013;3825.</mixed-citation></ref><ref id="bb45"><mixed-citation publication-type="other">Malaspina, L. A., Wieduwilt, E. K., Bergmann, J., Kleemiss, F., Meyer, B., Ruiz-L&#x000f3;pez, M. F., Pal, R., Hupf, E., Beckmann, J., Piltz, R. O., Edwards, A. J., Grabowsky, S. &#x00026; Genoni, A. (2019). <italic>J. Phys. Chem. Lett.</italic>
<bold>10</bold>, 6973&#x02013;6982.</mixed-citation></ref><ref id="bb46"><mixed-citation publication-type="other">Manz, T. A. &#x00026; Limas, N. G. (2016). <italic>RSC Adv.</italic>
<bold>6</bold>, 47771&#x02013;47801.</mixed-citation></ref><ref id="bb47"><mixed-citation publication-type="other">Meyer, B. &#x00026; Genoni, A. (2018). <italic>J. Phys. Chem. A</italic>, <bold>122</bold>, 8965&#x02013;8981.</mixed-citation></ref><ref id="bb48"><mixed-citation publication-type="other">Mulliken, R. S. (1955). <italic>J. Chem. Phys.</italic>
<bold>23</bold>, 1833&#x02013;1840.</mixed-citation></ref><ref id="bb49"><mixed-citation publication-type="other">Munshi, P., Madsen, A. &#x000d8;., Spackman, M. A., Larsen, S. &#x00026; Destro, R. (2008). <italic>Acta Cryst.</italic> A<bold>64</bold>, 465&#x02013;475.</mixed-citation></ref><ref id="bb50"><mixed-citation publication-type="other">Nalewajski, R. F. &#x00026; Parr, R. G. (2000). <italic>Proc. Natl Acad. Sci. USA</italic>, <bold>97</bold>, 8879&#x02013;8882.</mixed-citation></ref><ref id="bb51"><mixed-citation publication-type="other">Orben, C. M. &#x00026; Dittrich, B. (2014). <italic>Acta Cryst.</italic> C<bold>70</bold>, 580&#x02013;583.</mixed-citation></ref><ref id="bb52"><mixed-citation publication-type="other">Pichon-Pesme, V., Lecomte, C. &#x00026; Lachekar, H. (1995). <italic>J. Phys. Chem.</italic>
<bold>99</bold>, 6242&#x02013;6250.</mixed-citation></ref><ref id="bb107"><mixed-citation publication-type="other">Pisani, C., Dovesi, R., Erba, A. &#x00026; Giannozzi, P. (2011). <italic>Modern Charge-Density Analysis</italic>, edited by C. Gatti &#x00026; P. Macchi, pp. 79-132. Dordrecht: Springer.</mixed-citation></ref><ref id="bb53"><mixed-citation publication-type="other">Sanjuan-Szklarz, W. F., Woi&#x00144;ska, M., Domaga&#x00142;a, S., Dominiak, P. M., Grabowsky, S., Jayatilaka, D., Gutmann, M. &#x00026; Wo&#x0017a;niak, K. (2020). <italic>IUCrJ</italic>, <bold>7</bold>, 920&#x02013;933.</mixed-citation></ref><ref id="bb54"><mixed-citation publication-type="other">Sovago, I., Gutmann, M. J., Senn, H. M., Thomas, L. H., Wilson, C. C. &#x00026; Farrugia, L. J. (2016). <italic>Acta Cryst.</italic> B<bold>72</bold>, 39&#x02013;50.</mixed-citation></ref><ref id="bb55"><mixed-citation publication-type="other">Stevens, E., Coppens, P., Feld, R. &#x00026; Lehmann, M. (1979). <italic>Chem. Phys. Lett.</italic>
<bold>67</bold>, 541&#x02013;543.</mixed-citation></ref><ref id="bb56"><mixed-citation publication-type="other">Stevens, E. D. &#x00026; Coppens, P. (1980). <italic>Acta Cryst.</italic> B<bold>36</bold>, 1864&#x02013;1876.</mixed-citation></ref><ref id="bb57"><mixed-citation publication-type="other">Stewart, R. F. (1969). <italic>J. Chem. Phys.</italic>
<bold>51</bold>, 4569&#x02013;4577.</mixed-citation></ref><ref id="bb58"><mixed-citation publication-type="other">Stewart, R. F. (1973). <italic>J. Chem. Phys.</italic>
<bold>58</bold>, 1668&#x02013;1676.</mixed-citation></ref><ref id="bb59"><mixed-citation publication-type="other">Swaminathan, S., Craven, B. M. &#x00026; McMullan, R. K. (1984). <italic>Acta Cryst.</italic> B<bold>40</bold>, 300&#x02013;306.</mixed-citation></ref><ref id="bb60"><mixed-citation publication-type="other">Verstraelen, T., Ayers, P. W., Van Speybroeck, V. &#x00026; Waroquier, V. (2013). <italic>J. Chem. Theory Comput.</italic>
<bold>9</bold>, 2221&#x02013;2225.</mixed-citation></ref><ref id="bb61"><mixed-citation publication-type="other">Verstraelen, T., Tecmer, P., Heidar-Zadeh, F., Gonz&#x000e1;lez-Espinoza, C. E., Chan, M., Kim, T. D., Boguslawski, K., Fias, S., Vandenbrande, S., Berrocal, D. &#x00026; Ayers, P. W. (2017). <italic>HORTON 2.1.1</italic>, http://theochem.github.com/horton/.</mixed-citation></ref><ref id="bb62"><mixed-citation publication-type="other">Verstraelen, T., Vandenbrande, S., Heidar-Zadeh, F., Vanduyfhuys, L., Van Speybroeck, V., Waroquier, M. &#x00026; Ayers, P. W. (2016). <italic>J. Chem. Theory Comput.</italic>
<bold>12</bold>, 3894&#x02013;3912.</mixed-citation></ref><ref id="bb63"><mixed-citation publication-type="other">Volkov, A., Messerschmidt, M. &#x00026; Coppens, P. (2007). <italic>Acta Cryst.</italic> D<bold>63</bold>, 160&#x02013;170.</mixed-citation></ref><ref id="bb105"><mixed-citation publication-type="other">Wall, M. E. (2016). <italic>IUCrJ</italic>, <bold>3</bold>, 237&#x02013;246.</mixed-citation></ref><ref id="bb64"><mixed-citation publication-type="other">Weiss, R. J. (1964). <italic>Phys. Lett.</italic>
<bold>12</bold>, 293&#x02013;295.</mixed-citation></ref><ref id="bb65"><mixed-citation publication-type="other">Whitten, A. E. &#x00026; Spackman, M. A. (2006). <italic>Acta Cryst.</italic> B<bold>62</bold>, 875&#x02013;888.</mixed-citation></ref><ref id="bb101"><mixed-citation publication-type="other">Wieduwilt, E. K., Macetti, G., Malaspina, L. A., Jayatilaka, D., Grabowsky, S. &#x00026; Genoni, A. (2020). <italic>J. Mol. Struct.</italic>
<bold>1209</bold>, 127934.</mixed-citation></ref><ref id="bb66"><mixed-citation publication-type="other">Woi&#x00144;ska, M., Grabowsky, S., Dominiak, P. M., Wo&#x0017a;niak, K. &#x00026; Jayatilaka, D. (2016). <italic>Sci. Adv.</italic>
<bold>2</bold>, e1600192.</mixed-citation></ref><ref id="bb102"><mixed-citation publication-type="other">Woon &#x00026; Dunning (1993). <italic>J. Chem. Phys.</italic>
<bold>98</bold>, 1358&#x02013;1371.</mixed-citation></ref><ref id="bb67"><mixed-citation publication-type="other">Woi&#x00144;ska, M., Jayatilaka, D., Dittrich, B., Flaig, R., Luger, P., Wo&#x0017a;niak, K., Dominiak, P. M. &#x00026; Grabowsky, S. (2017). <italic>ChemPhysChem</italic>, <bold>18</bold>, 3334&#x02013;3351.</mixed-citation></ref><ref id="bb68"><mixed-citation publication-type="other">Woinska, M., Wanat, M., Taciak, P., Pawinski, T., Minor, W. &#x00026; Wozniak, K. (2019). <italic>IUCrJ</italic>, <bold>6</bold>, 868&#x02013;883.</mixed-citation></ref><ref id="bb69"><mixed-citation publication-type="other">Zhang, D. W. &#x00026; Zhang, J. Z. H. (2003). <italic>J. Chem. Phys.</italic>
<bold>119</bold>, 3599&#x02013;3605.</mixed-citation></ref><ref id="bb70"><mixed-citation publication-type="other">Zhurov, V. V., Zhurova, E. A., Stash, A. I. &#x00026; Pinkerton, A. A. (2011). <italic>Acta Cryst.</italic> A<bold>67</bold>, 160&#x02013;173.</mixed-citation></ref><ref id="bb71"><mixed-citation publication-type="other">Zobel, D., Luger, P. &#x00026; Drei&#x000df;ig, W. (1993). <italic>Z. Naturforsch. A</italic>, <bold>48</bold>, 53&#x02013;54.</mixed-citation></ref></ref-list></back><floats-group><fig id="fig1" orientation="portrait" position="float"><label>Figure 1</label><caption><p>Hydrogen atom labelling schemes for the studied structures (<italic>a</italic>) urea and (<italic>b</italic>) oxalic acid dihydrate, produced using the iterative stockholder partition refinement with B3LYP/cc-pVTZ theory level and surrounding multipoles cluster. ADP values are shown at the 50% probability level (Mercury, Macrae <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb43"> &#x025b8;</xref>).</p></caption><graphic xlink:href="m-07-01199-fig1"/></fig><fig id="fig2" orientation="portrait" position="float"><label>Figure 2</label><caption><p>Comparison of neutron and X-ray parameters of polar hydrogen atoms for refinements with various basis sets: (<italic>a</italic>) &#x00394;<italic>R</italic> &#x02013; the difference between X-ray and neutron measured bond lengths (error bars correspond to the X-ray bond length uncertainties), (<italic>b</italic>) &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; the average absolute difference of the ADP tensor components, (<italic>c</italic>) ADP similarity index <italic>S</italic>
<sub>12</sub>, (<italic>d</italic>) <italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub> ratio of X-ray and neutron thermal ellipsoids.</p></caption><graphic xlink:href="m-07-01199-fig2"/></fig><fig id="fig3" orientation="portrait" position="float"><label>Figure 3</label><caption><p>Comparison of the neutron and X-ray parameters of polar hydrogen atoms for refinements with various quantum chemistry methods: (<italic>a</italic>) &#x00394;<italic>R</italic> &#x02013; the difference between X-ray and neutron measured bond length (error bars correspond to the X-ray bond length uncertainties), (<italic>b</italic>) &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; average absolute difference of ADP tensor components, (<italic>c</italic>) ADP similarity index <italic>S</italic>
<sub>12</sub>, (<italic>d</italic>) <italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub> ratio of X-ray and neutron thermal ellipsoids.</p></caption><graphic xlink:href="m-07-01199-fig3"/></fig><fig id="fig4" orientation="portrait" position="float"><label>Figure 4</label><caption><p>Comparison of the neutron and X-ray parameters of polar hydrogen atoms for refinements with various representations of the crystal field (see text): (<italic>a</italic>) &#x00394;<italic>R</italic> &#x02013; the difference between X-ray and neutron measured bond lengths (error bars correspond to the X-ray bond length uncertainties), (<italic>b</italic>) &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; the average absolute difference of ADP tensor components, (<italic>c</italic>) ADP similarity index <italic>S</italic>
<sub>12</sub>, (<italic>d</italic>) <italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub> ratio of X-ray and neutron thermal ellipsoids.</p></caption><graphic xlink:href="m-07-01199-fig4"/></fig><fig id="fig5" orientation="portrait" position="float"><label>Figure 5</label><caption><p>Comparison of neutron and X-ray parameters of polar hydrogen atoms for refinements with HAR, TAAM and a model with standardized neutron bond lengths: (<italic>a</italic>) &#x00394;<italic>R</italic> &#x02013; the difference between X-ray and neutron measured bond lengths in m&#x000c5; (error bars correspond to the X-ray bond length uncertainties), (<italic>b</italic>) &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; the average absolute difference of ADP tensor components, (<italic>c</italic>) ADP similarity index <italic>S</italic>
<sub>12</sub>, (<italic>d</italic>) as in (<italic>a</italic>) but the least accurate methods were omitted to improve readability.</p></caption><graphic xlink:href="m-07-01199-fig5"/></fig><fig id="fig6" orientation="portrait" position="float"><label>Figure 6</label><caption><p>Hydrogen atoms in SPAnPS coloured according to the group (see text): (1) &#x02013; green, (2) &#x02013; white, (3) &#x02013; dark red and (4) &#x02013; cyan. Iterative Hirshfeld partition refinement with B3LYP/cc-pVTZ theory level and surrounding multipoles cluster. ADP values are shown at the 50% probability level (Mercury, Macrae <italic>et al.</italic>, 2020<xref ref-type="bibr" rid="bb43"> &#x025b8;</xref>).</p></caption><graphic xlink:href="m-07-01199-fig6"/></fig><fig id="fig7" orientation="portrait" position="float"><label>Figure 7</label><caption><p>Comparison of the neutron and X-ray parameters of polar hydrogen atoms for refinement with various electron density partitions: (<italic>a</italic>) &#x00394;<italic>R</italic> (m&#x000c5;) &#x02013; the difference between X-ray and neutron measured bond lengths (error bars correspond to the X-ray bond length uncertainties), Fig. S2 also includes B partition, (<italic>b</italic>) &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; average absolute difference of ADP tensor components, (<italic>c</italic>) ADP similarity index <italic>S</italic>
<sub>12</sub>, (<italic>d</italic>) <italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub> ratio of X-ray and neutron thermal ellipsoids.</p></caption><graphic xlink:href="m-07-01199-fig7"/></fig><table-wrap id="table1" orientation="portrait" position="float"><label>Table 1</label><caption><title>
<italic>wR</italic>
<sub>2</sub> statistics and the comparison of structural indicators related to hydrogen atoms (based on comparison with neutron data) for the various settings of HAR, for TAAM and for structures with standardized bond lengths (see text), the averages for five polar hydrogen atoms (in urea and oxalic acid)</title><p>The symbols (+) and (&#x02212;) indicate a model with and without point multipoles, respectively. &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; &#x02013; the average absolute difference of bond lengths, &#x02329;&#x00394;<italic>R</italic>&#x0232a; &#x02013; the average difference of the bond lengths, wRMSD(&#x00394;<italic>R</italic>) &#x02013; the weighted root mean squared deviation for bond lengths [equation (2)<xref ref-type="disp-formula" rid="fd2"/>], <italic>S</italic>
<sub>12</sub> &#x02013; the average ADP similarity index <italic>S</italic>
<sub>12</sub> [equation (4<xref ref-type="disp-formula" rid="fd4"/>)], &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; the average absolute difference of ADP tensor components, wRMSD(&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>) &#x02013; the weighted root mean squared deviation for the components of the ADP tensor [equation (3)<xref ref-type="disp-formula" rid="fd3"/>]. Values in brackets correspond to population standard deviations.</p></caption><table frame="hsides" rules="groups"><thead valign="bottom"><tr><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x000a0;</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="2" align="left" char="." charoff="6" valign="bottom">
<italic>wR</italic>
<sub>2</sub> oxalic urea acid</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" char="." charoff="51" valign="bottom">&#x02329;|&#x00394;<italic>R</italic>|&#x0232a; (m&#x000c5;)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" char="." charoff="65" valign="bottom">&#x02329;&#x00394;<italic>R</italic>&#x0232a; (m&#x000c5;)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" char="." charoff="9" valign="bottom">wRMSD(&#x00394;<italic>R</italic>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" char="." charoff="16" valign="bottom">
<italic>S</italic>
<sub>12</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" char="." charoff="22" valign="bottom">&#x02329;|&#x00394;<italic>U</italic>
<sub>ij</sub>|&#x0232a; &#x000d7; 10<sup>4</sup>(&#x000c5;<sup>2</sup>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" char="." charoff="3" valign="bottom">wRMSD (&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>)</th></tr></thead><tbody valign="top"><tr><td rowspan="1" colspan="1" align="left" valign="top">Basis set</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">&#x000a0;</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">cc-pVDZ</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">4.01</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.98</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">11.2&#x02005;(78)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;11.2&#x02005;(78)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">2.13</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">2.9&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">58&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.61</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">cc-pVTZ</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.73</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.68</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.94</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.75&#x02005;(74)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">52&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.58</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">cc-pVQZ</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.71</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.69</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">7.2&#x02005;(49)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;7.2(4.9)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.76</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.7&#x02005;(6)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">49&#x02005;(12)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.55</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">Method</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">&#x000a0;</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">HF</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">4.01</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">2.19</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">4.6&#x02005;(36)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">4.2&#x02005;(41)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.06</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">3.4&#x02005;(27)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">61&#x02005;(20)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.65</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">BLYP</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.78</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.78</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">12.5&#x02005;(57)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;12.5&#x02005;(57)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">2.88</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">2.0&#x02005;(8)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">57&#x02005;(16)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.62</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">B3LYP</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.73</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.68</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.94</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.75&#x02005;(74)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">52&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.58</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">MP2</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.72</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.66</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">3.5&#x02005;(28)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;1.9&#x02005;(40)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">0.91</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.8&#x02005;(8)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">49&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.53</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">CCSD</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.73</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.71</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">3.8&#x02005;(38)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;3.6&#x02005;(39)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.00</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.8&#x02005;(9)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">50&#x02005;(13)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.57</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">Environment</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">&#x000a0;</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">(&#x02212;)</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.87</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">2.07</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">12.0&#x02005;(9)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;11.0&#x02005;(10)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">2.77</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">2.6&#x02005;(15)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">69&#x02005;(26)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.74</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">(+)</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.73</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.68</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.94</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.75&#x02005;(74)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">52&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.58</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">Cluster hydrogen-bond</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.71</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.65</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">8.8&#x02005;(47)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;8.8&#x02005;(47)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.85</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.9&#x02005;(7)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">54&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.60</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">Cluster 3.5&#x02005;&#x000c5;</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.70</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.70</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">8.2</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;7.5&#x02005;(60)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.92</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.8&#x02005;(6)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">52&#x02005;(13)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.59</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">Mix of less accurate settings</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">&#x000a0;</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">B3LYP/cc-pVTZ(+)</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">3.73</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">1.68</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;8.0&#x02005;(46)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">1.94</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">1.75&#x02005;(74)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">52&#x02005;(14)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.58</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">B3LYP/cc-pVDZ(&#x02212;)</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">4.16</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">2.18</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">17.0&#x02005;(7)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;15.0&#x02005;(11)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">3.09</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">3.4&#x02005;(10)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">70&#x02005;(25)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.72</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">HF/cc-pVDZ(+)</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">4.17</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">2.44</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">3.3&#x02005;(16)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;1.5&#x02005;(33)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">0.71</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">5.0&#x02005;(3)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">68&#x02005;(17)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.71</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">HF/cc-pVDZ(&#x02212;)</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">4.35</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">2.51</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">15.0&#x02005;(5)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;6.6&#x02005;(140)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">2.75</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">5.0&#x02005;(2)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">82&#x02005;(33)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.77</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">TAAM (UBDB)</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">5.15</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">2.13</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">38.0&#x02005;(27)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;38.0&#x02005;(27)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">5.52</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">7.6&#x02005;(36)</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">106&#x02005;(48)</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">1.95</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">Standard bond distance</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x02013;</td><td rowspan="1" colspan="1" align="char" char="." charoff="6" valign="top">&#x02013;</td><td rowspan="1" colspan="1" align="char" char="." charoff="51" valign="top">18.0&#x02005;(18)</td><td rowspan="1" colspan="1" align="char" char="." charoff="65" valign="top">&#x02212;13.0&#x02005;(22)</td><td rowspan="1" colspan="1" align="char" char="." charoff="9" valign="top">&#x02013;</td><td rowspan="1" colspan="1" align="char" char="." charoff="16" valign="top">&#x02013;</td><td rowspan="1" colspan="1" align="char" char="." charoff="22" valign="top">&#x02013;</td><td rowspan="1" colspan="1" align="char" char="." charoff="3" valign="top">&#x000a0;</td></tr></tbody></table></table-wrap><table-wrap id="table2" orientation="portrait" position="float"><label>Table 2</label><caption><title>
<italic>R</italic> factors (<italic>R</italic>1 and <italic>wR</italic>
<sub>2</sub>) and structural indicators related to hydrogen atoms (based on comparison to neutron data) for various electron density partitions in GAR</title><p>B &#x02013; Becke, H &#x02013; Hirshfeld, IH &#x02013; iterative Hirshfeld, IS &#x02013; iterative stockholder, MBIS &#x02013; minimal basis iterative stockholder. &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; &#x02013; average absolute difference of bond lengths, wRMSD(&#x00394;<italic>R</italic>) - weighted room mean square deviation for bond lengths [equation (2)<xref ref-type="disp-formula" rid="fd2"/>], <italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub> &#x02013; average ratio of X-ray to neutron bond length, &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; average absolute difference of ADP tensor components, wRMSD(&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>) &#x02013; weighted room mean square deviation for components of ADP tensor [equation (3)<xref ref-type="disp-formula" rid="fd3"/>], <italic>S</italic>
<sub>12</sub> &#x02013; average ADP similarity index <italic>S</italic>
<sub>12</sub> [equation (4<xref ref-type="disp-formula" rid="fd4"/>)], &#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a; &#x02013; average ratio of X-ray to neutron volumes of thermal ellipsoids.</p></caption><table frame="hsides" rules="groups"><thead valign="bottom"><tr><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x000a0;</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>R</italic>1</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>wR</italic>2</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>R</italic>|&#x0232a; (m&#x000c5;)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>R</italic>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x000d7; 10<sup>4</sup>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>S</italic>
<sub>12</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a;</th></tr></thead><tbody valign="top"><tr><td rowspan="1" colspan="1" align="left" valign="top">Oxalic acid</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">B</td><td rowspan="1" colspan="1" align="left" valign="top">1.38</td><td rowspan="1" colspan="1" align="left" valign="top">3.79</td><td rowspan="1" colspan="1" align="left" valign="top">26.8</td><td rowspan="1" colspan="1" align="left" valign="top">6.8</td><td rowspan="1" colspan="1" align="left" valign="top">0.973</td><td rowspan="1" colspan="1" align="left" valign="top">58</td><td rowspan="1" colspan="1" align="left" valign="top">1.8</td><td rowspan="1" colspan="1" align="left" valign="top">3.13</td><td rowspan="1" colspan="1" align="left" valign="top">0.46</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">H</td><td rowspan="1" colspan="1" align="left" valign="top">1.36</td><td rowspan="1" colspan="1" align="left" valign="top">3.73</td><td rowspan="1" colspan="1" align="left" valign="top">9.2</td><td rowspan="1" colspan="1" align="left" valign="top">1.8</td><td rowspan="1" colspan="1" align="left" valign="top">0.990</td><td rowspan="1" colspan="1" align="left" valign="top">52</td><td rowspan="1" colspan="1" align="left" valign="top">1.4</td><td rowspan="1" colspan="1" align="left" valign="top">1.90</td><td rowspan="1" colspan="1" align="left" valign="top">0.95</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IH</td><td rowspan="1" colspan="1" align="left" valign="top">1.37</td><td rowspan="1" colspan="1" align="left" valign="top">3.73</td><td rowspan="1" colspan="1" align="left" valign="top">6</td><td rowspan="1" colspan="1" align="left" valign="top">0.8</td><td rowspan="1" colspan="1" align="left" valign="top">1.003</td><td rowspan="1" colspan="1" align="left" valign="top">77</td><td rowspan="1" colspan="1" align="left" valign="top">1.4</td><td rowspan="1" colspan="1" align="left" valign="top">3.26</td><td rowspan="1" colspan="1" align="left" valign="top">1.64</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IS</td><td rowspan="1" colspan="1" align="left" valign="top">1.36</td><td rowspan="1" colspan="1" align="left" valign="top">3.72</td><td rowspan="1" colspan="1" align="left" valign="top">4.7</td><td rowspan="1" colspan="1" align="left" valign="top">1.1</td><td rowspan="1" colspan="1" align="left" valign="top">0.996</td><td rowspan="1" colspan="1" align="left" valign="top">53</td><td rowspan="1" colspan="1" align="left" valign="top">1.4</td><td rowspan="1" colspan="1" align="left" valign="top">2.09</td><td rowspan="1" colspan="1" align="left" valign="top">0.90</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">MBIS</td><td rowspan="1" colspan="1" align="left" valign="top">1.36</td><td rowspan="1" colspan="1" align="left" valign="top">3.73</td><td rowspan="1" colspan="1" align="left" valign="top">4.9</td><td rowspan="1" colspan="1" align="left" valign="top">1.1</td><td rowspan="1" colspan="1" align="left" valign="top">0.995</td><td rowspan="1" colspan="1" align="left" valign="top">53</td><td rowspan="1" colspan="1" align="left" valign="top">1.5</td><td rowspan="1" colspan="1" align="left" valign="top">2.18</td><td rowspan="1" colspan="1" align="left" valign="top">0.83</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">Urea</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">B</td><td rowspan="1" colspan="1" align="left" valign="top">1.37</td><td rowspan="1" colspan="1" align="left" valign="top">1.70</td><td rowspan="1" colspan="1" align="left" valign="top">5.6</td><td rowspan="1" colspan="1" align="left" valign="top">2.8</td><td rowspan="1" colspan="1" align="left" valign="top">0.995</td><td rowspan="1" colspan="1" align="left" valign="top">26</td><td rowspan="1" colspan="1" align="left" valign="top">1.42</td><td rowspan="1" colspan="1" align="left" valign="top">0.65</td><td rowspan="1" colspan="1" align="left" valign="top">0.89</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">H</td><td rowspan="1" colspan="1" align="left" valign="top">1.36</td><td rowspan="1" colspan="1" align="left" valign="top">1.68</td><td rowspan="1" colspan="1" align="left" valign="top">6.2</td><td rowspan="1" colspan="1" align="left" valign="top">2.1</td><td rowspan="1" colspan="1" align="left" valign="top">0.994</td><td rowspan="1" colspan="1" align="left" valign="top">52</td><td rowspan="1" colspan="1" align="left" valign="top">1.83</td><td rowspan="1" colspan="1" align="left" valign="top">1.52</td><td rowspan="1" colspan="1" align="left" valign="top">1.49</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IH</td><td rowspan="1" colspan="1" align="left" valign="top">1.37</td><td rowspan="1" colspan="1" align="left" valign="top">1.72</td><td rowspan="1" colspan="1" align="left" valign="top">4.7</td><td rowspan="1" colspan="1" align="left" valign="top">1.1</td><td rowspan="1" colspan="1" align="left" valign="top">0.999</td><td rowspan="1" colspan="1" align="left" valign="top">96</td><td rowspan="1" colspan="1" align="left" valign="top">2.06</td><td rowspan="1" colspan="1" align="left" valign="top">3.04</td><td rowspan="1" colspan="1" align="left" valign="top">2.34</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IS</td><td rowspan="1" colspan="1" align="left" valign="top">1.37</td><td rowspan="1" colspan="1" align="left" valign="top">1.72</td><td rowspan="1" colspan="1" align="left" valign="top">5.5</td><td rowspan="1" colspan="1" align="left" valign="top">1.6</td><td rowspan="1" colspan="1" align="left" valign="top">0.999</td><td rowspan="1" colspan="1" align="left" valign="top">45</td><td rowspan="1" colspan="1" align="left" valign="top">1.69</td><td rowspan="1" colspan="1" align="left" valign="top">1.30</td><td rowspan="1" colspan="1" align="left" valign="top">1.41</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">MBIS</td><td rowspan="1" colspan="1" align="left" valign="top">1.38</td><td rowspan="1" colspan="1" align="left" valign="top">1.73</td><td rowspan="1" colspan="1" align="left" valign="top">5.2</td><td rowspan="1" colspan="1" align="left" valign="top">1.5</td><td rowspan="1" colspan="1" align="left" valign="top">0.999</td><td rowspan="1" colspan="1" align="left" valign="top">43</td><td rowspan="1" colspan="1" align="left" valign="top">1.67</td><td rowspan="1" colspan="1" align="left" valign="top">1.26</td><td rowspan="1" colspan="1" align="left" valign="top">1.36</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">SPAnPS</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">B</td><td rowspan="1" colspan="1" align="left" valign="top">2.22</td><td rowspan="1" colspan="1" align="left" valign="top">2.54</td><td rowspan="1" colspan="1" align="left" valign="top">13.0</td><td rowspan="1" colspan="1" align="left" valign="top">4.76</td><td rowspan="1" colspan="1" align="left" valign="top">1.006</td><td rowspan="1" colspan="1" align="left" valign="top">103</td><td rowspan="1" colspan="1" align="left" valign="top">3.74</td><td rowspan="1" colspan="1" align="left" valign="top">3.20</td><td rowspan="1" colspan="1" align="left" valign="top">0.78</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">H</td><td rowspan="1" colspan="1" align="left" valign="top">2.19</td><td rowspan="1" colspan="1" align="left" valign="top">2.47</td><td rowspan="1" colspan="1" align="left" valign="top">12.1</td><td rowspan="1" colspan="1" align="left" valign="top">3.71</td><td rowspan="1" colspan="1" align="left" valign="top">1.002</td><td rowspan="1" colspan="1" align="left" valign="top">139</td><td rowspan="1" colspan="1" align="left" valign="top">3.72</td><td rowspan="1" colspan="1" align="left" valign="top">4.48</td><td rowspan="1" colspan="1" align="left" valign="top">1.03</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IH</td><td rowspan="1" colspan="1" align="left" valign="top">2.20</td><td rowspan="1" colspan="1" align="left" valign="top">2.49</td><td rowspan="1" colspan="1" align="left" valign="top">11.4</td><td rowspan="1" colspan="1" align="left" valign="top">3.41</td><td rowspan="1" colspan="1" align="left" valign="top">1.002</td><td rowspan="1" colspan="1" align="left" valign="top">154</td><td rowspan="1" colspan="1" align="left" valign="top">3.71</td><td rowspan="1" colspan="1" align="left" valign="top">4.86</td><td rowspan="1" colspan="1" align="left" valign="top">1.07</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IS</td><td rowspan="1" colspan="1" align="left" valign="top">2.19</td><td rowspan="1" colspan="1" align="left" valign="top">2.48</td><td rowspan="1" colspan="1" align="left" valign="top">12.2</td><td rowspan="1" colspan="1" align="left" valign="top">3.97</td><td rowspan="1" colspan="1" align="left" valign="top">1.004</td><td rowspan="1" colspan="1" align="left" valign="top">123</td><td rowspan="1" colspan="1" align="left" valign="top">3.56</td><td rowspan="1" colspan="1" align="left" valign="top">4.02</td><td rowspan="1" colspan="1" align="left" valign="top">0.95</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">MBIS</td><td rowspan="1" colspan="1" align="left" valign="top">2.19</td><td rowspan="1" colspan="1" align="left" valign="top">2.48</td><td rowspan="1" colspan="1" align="left" valign="top">12.4</td><td rowspan="1" colspan="1" align="left" valign="top">4.04</td><td rowspan="1" colspan="1" align="left" valign="top">1.003</td><td rowspan="1" colspan="1" align="left" valign="top">120</td><td rowspan="1" colspan="1" align="left" valign="top">3.58</td><td rowspan="1" colspan="1" align="left" valign="top">4.07</td><td rowspan="1" colspan="1" align="left" valign="top">0.90</td></tr></tbody></table></table-wrap><table-wrap id="table3" orientation="portrait" position="float"><label>Table 3</label><caption><title>Atomic charges of hydrogen atoms in oxalic acid and urea for various electron density partitions</title><p>B &#x02013; Becke, H &#x02013; Hirshfeld, IH &#x02013; iterative Hirshfeld, IS &#x02013; iterative stockholder, MBIS &#x02013; minimal basis iterative stockholder. For the atom labelling scheme, see Fig. 1<xref ref-type="fig" rid="fig1"> &#x025b8;</xref>.</p></caption><table frame="hsides" rules="groups"><thead valign="bottom"><tr><th rowspan="1" colspan="1" align="left" valign="bottom">&#x000a0;</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="3" align="left" valign="bottom">Oxalic Acid</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="2" align="left" valign="bottom">Urea</th></tr><tr><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="top">&#x000a0;</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="top">H1</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="top">H2</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="top">H3</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="top">H1</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="top">H2</th></tr></thead><tbody valign="top"><tr><td rowspan="1" colspan="1" align="left" valign="top">B</td><td rowspan="1" colspan="1" align="left" valign="top">0.171</td><td rowspan="1" colspan="1" align="left" valign="top">0.167</td><td rowspan="1" colspan="1" align="left" valign="top">0.148</td><td rowspan="1" colspan="1" align="left" valign="top">0.167</td><td rowspan="1" colspan="1" align="left" valign="top">0.170</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">H</td><td rowspan="1" colspan="1" align="left" valign="top">0.119</td><td rowspan="1" colspan="1" align="left" valign="top">0.232</td><td rowspan="1" colspan="1" align="left" valign="top">0.217</td><td rowspan="1" colspan="1" align="left" valign="top">0.153</td><td rowspan="1" colspan="1" align="left" valign="top">0.163</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IH</td><td rowspan="1" colspan="1" align="left" valign="top">0.503</td><td rowspan="1" colspan="1" align="left" valign="top">0.589</td><td rowspan="1" colspan="1" align="left" valign="top">0.572</td><td rowspan="1" colspan="1" align="left" valign="top">0.492</td><td rowspan="1" colspan="1" align="left" valign="top">0.504</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IS</td><td rowspan="1" colspan="1" align="left" valign="top">0.529</td><td rowspan="1" colspan="1" align="left" valign="top">0.541</td><td rowspan="1" colspan="1" align="left" valign="top">0.523</td><td rowspan="1" colspan="1" align="left" valign="top">0.458</td><td rowspan="1" colspan="1" align="left" valign="top">0.460</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">MBIS</td><td rowspan="1" colspan="1" align="left" valign="top">0.535</td><td rowspan="1" colspan="1" align="left" valign="top">0.554</td><td rowspan="1" colspan="1" align="left" valign="top">0.537</td><td rowspan="1" colspan="1" align="left" valign="top">0.464</td><td rowspan="1" colspan="1" align="left" valign="top">0.465</td></tr></tbody></table></table-wrap><table-wrap id="table4" orientation="portrait" position="float"><label>Table 4</label><caption><title>Average standard deviations for bond lengths to hydrogen (&#x000d7;10<sup>&#x02212;4</sup> &#x000c5;) and hydrogen <italic>U</italic>
<sub><italic>ij</italic></sub> (&#x000d7;10<sup>&#x02212;4</sup> &#x000c5;<sup>2</sup>) for various electron density partitions</title><p>B &#x02013; Becke, H &#x02013; Hirshfeld, IH &#x02013; iterative Hirshfeld, IS &#x02013; iterative stockholder, MBIS &#x02013; minimal basis iterative stockholder) taken from B3LYP/cc-pVTZ refinements with surrounding charges and dipoles. Statistics for SPAnPS based on group 1 hydrogen atoms</p></caption><table frame="hsides" rules="groups"><thead valign="bottom"><tr><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x000a0;</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">Molecule</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">B</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">H</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">HI</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">IS</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">MBIS</th></tr></thead><tbody valign="top"><tr><td rowspan="3" colspan="1" align="left" valign="top">&#x02329;&#x003c3;<sub>bonds</sub>&#x0232a;</td><td rowspan="1" colspan="1" align="left" valign="top">Urea</td><td rowspan="1" colspan="1" align="left" valign="top">17</td><td rowspan="1" colspan="1" align="left" valign="top">27</td><td rowspan="1" colspan="1" align="left" valign="top">43</td><td rowspan="1" colspan="1" align="left" valign="top">30</td><td rowspan="1" colspan="1" align="left" valign="top">30</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">OXZDH</td><td rowspan="1" colspan="1" align="left" valign="top">27</td><td rowspan="1" colspan="1" align="left" valign="top">54</td><td rowspan="1" colspan="1" align="left" valign="top">86</td><td rowspan="1" colspan="1" align="left" valign="top">54</td><td rowspan="1" colspan="1" align="left" valign="top">54</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">SPAnPS</td><td rowspan="1" colspan="1" align="left" valign="top">26</td><td rowspan="1" colspan="1" align="left" valign="top">35</td><td rowspan="1" colspan="1" align="left" valign="top">36</td><td rowspan="1" colspan="1" align="left" valign="top">33</td><td rowspan="1" colspan="1" align="left" valign="top">32</td></tr><tr><td rowspan="1" colspan="1" align="left" charoff="50" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" charoff="50" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" charoff="50" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" charoff="50" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" charoff="50" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" charoff="50" valign="top">&#x000a0;</td><td rowspan="1" colspan="1" align="left" charoff="50" valign="top">&#x000a0;</td></tr><tr><td rowspan="3" colspan="1" align="left" valign="top">&#x02329;&#x003c3;<sub><italic>U</italic><sub><italic>ij</italic></sub></sub>&#x0232a;</td><td rowspan="1" colspan="1" align="left" valign="top">Urea</td><td rowspan="1" colspan="1" align="left" valign="top">10</td><td rowspan="1" colspan="1" align="left" valign="top">15</td><td rowspan="1" colspan="1" align="left" valign="top">23</td><td rowspan="1" colspan="1" align="left" valign="top">16</td><td rowspan="1" colspan="1" align="left" valign="top">15</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">OXADH</td><td rowspan="1" colspan="1" align="left" valign="top">16</td><td rowspan="1" colspan="1" align="left" valign="top">27</td><td rowspan="1" colspan="1" align="left" valign="top">43</td><td rowspan="1" colspan="1" align="left" valign="top">26</td><td rowspan="1" colspan="1" align="left" valign="top">25</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">SPAnPS</td><td rowspan="1" colspan="1" align="left" valign="top">20</td><td rowspan="1" colspan="1" align="left" valign="top">25</td><td rowspan="1" colspan="1" align="left" valign="top">26</td><td rowspan="1" colspan="1" align="left" valign="top">23</td><td rowspan="1" colspan="1" align="left" valign="top">22</td></tr></tbody></table></table-wrap><table-wrap id="table5" orientation="portrait" position="float"><label>Table 5</label><caption><title>Comparison of the structural indicators related to hydrogen atoms with neutron measurements for SPAnPS</title><p>Hydrogen atoms were divided into groups (see text); results for HAR. &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; &#x02013; average absolute difference of bond lengths, wRMSD(&#x00394;<italic>R</italic>) &#x02013; weighted room mean square deviation for bond lengths [equation (2)<xref ref-type="disp-formula" rid="fd2"/>], <italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub> &#x02013; average ratio of X-ray to neutron bond length, &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; average absolute difference of ADP tensor components, wRMSD(&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>) &#x02013; weighted room mean square deviation for components of ADP tensor [equation (3)<xref ref-type="disp-formula" rid="fd3"/>], <italic>S</italic>
<sub>12</sub> &#x02013; average ADP similarity index <italic>S</italic>
<sub>12</sub> [equation (4<xref ref-type="disp-formula" rid="fd4"/>)], &#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a; &#x02013; average ratio of X-ray to neutron volumes of thermal ellipsoids.</p></caption><table frame="hsides" rules="groups"><thead valign="bottom"><tr><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">Hydrogen atoms group</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>R</italic>|&#x0232a; (m&#x000c5;)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>R</italic>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>U<sub>ij</sub></italic>|&#x0232a; &#x000d7;&#x02009;10<sup>4</sup>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>S</italic>
<sub>12</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a;</th></tr></thead><tbody valign="top"><tr><td rowspan="1" colspan="1" align="left" valign="top">1</td><td rowspan="1" colspan="1" align="left" valign="top">5.1&#x02005;(37)</td><td rowspan="1" colspan="1" align="left" valign="top">1.33</td><td rowspan="1" colspan="1" align="left" valign="top">0.995</td><td rowspan="1" colspan="1" align="left" valign="top">49&#x02005;(9)</td><td rowspan="1" colspan="1" align="left" valign="top">2.19</td><td rowspan="1" colspan="1" align="left" valign="top">1.26&#x02005;(50)</td><td rowspan="1" colspan="1" align="left" valign="top">1.04</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">2</td><td rowspan="1" colspan="1" align="left" valign="top">7.9</td><td rowspan="1" colspan="1" align="left" valign="top">2.17</td><td rowspan="1" colspan="1" align="left" valign="top">0.998</td><td rowspan="1" colspan="1" align="left" valign="top">45</td><td rowspan="1" colspan="1" align="left" valign="top">1.36</td><td rowspan="1" colspan="1" align="left" valign="top">1.05</td><td rowspan="1" colspan="1" align="left" valign="top">0.94</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">3</td><td rowspan="1" colspan="1" align="left" valign="top">15.2</td><td rowspan="1" colspan="1" align="left" valign="top">4.27</td><td rowspan="1" colspan="1" align="left" valign="top">1.000</td><td rowspan="1" colspan="1" align="left" valign="top">163</td><td rowspan="1" colspan="1" align="left" valign="top">2.22</td><td rowspan="1" colspan="1" align="left" valign="top">3.97</td><td rowspan="1" colspan="1" align="left" valign="top">1.11</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">4</td><td rowspan="1" colspan="1" align="left" valign="top">31.4</td><td rowspan="1" colspan="1" align="left" valign="top">5.68</td><td rowspan="1" colspan="1" align="left" valign="top">1.025</td><td rowspan="1" colspan="1" align="left" valign="top">530</td><td rowspan="1" colspan="1" align="left" valign="top">3.15</td><td rowspan="1" colspan="1" align="left" valign="top">21.01</td><td rowspan="1" colspan="1" align="left" valign="top">1.14</td></tr></tbody></table></table-wrap><table-wrap id="table6" orientation="portrait" position="float"><label>Table 6</label><caption><title>Comparison of the structural parameters related to hydrogen atoms with neutron measurements of SPAnPS for group 1 (see text)</title><p>&#x02329;|&#x00394;<italic>R</italic>|&#x0232a; &#x02013; average absolute difference of bond lengths, wRMSD(&#x00394;<italic>R</italic>) &#x02013; weighted room mean square deviation for bond lengths [equation (2)<xref ref-type="disp-formula" rid="fd2"/>], &#x02329;<italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub>&#x0232a; &#x02013; average ratio of X-ray to neutron bond lengths, &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; average absolute difference of ADP tensor components, wRMSD(&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>) &#x02013; weighted room mean square deviation for components of ADP tensor [equation (3)<xref ref-type="disp-formula" rid="fd3"/>], <italic>S</italic>
<sub>12</sub> &#x02013; average ADP similarity index <italic>S</italic>
<sub>12</sub> [equation (4<xref ref-type="disp-formula" rid="fd4"/>)], &#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a; &#x02013; average ratio of X-ray to neutron volumes of thermal ellipsoids.</p></caption><table frame="hsides" rules="groups"><thead valign="bottom"><tr><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x000a0;</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>R</italic>|&#x0232a; (m&#x000c5;)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>R</italic>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>U<sub>ij</sub></italic>|&#x0232a; &#x000d7;&#x02009;10<sup>4</sup>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>S</italic>
<sub>12</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a;</th></tr></thead><tbody valign="top"><tr><td rowspan="1" colspan="1" align="left" valign="top">B</td><td rowspan="1" colspan="1" align="left" valign="top">4.3&#x02005;(33)</td><td rowspan="1" colspan="1" align="left" valign="top">1.49</td><td rowspan="1" colspan="1" align="left" valign="top">0.996</td><td rowspan="1" colspan="1" align="left" valign="top">36&#x02005;(4)</td><td rowspan="1" colspan="1" align="left" valign="top">2.21</td><td rowspan="1" colspan="1" align="left" valign="top">0.97&#x02005;(33)</td><td rowspan="1" colspan="1" align="left" valign="top">0.80</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">H</td><td rowspan="1" colspan="1" align="left" valign="top">5.1&#x02005;(37)</td><td rowspan="1" colspan="1" align="left" valign="top">1.33</td><td rowspan="1" colspan="1" align="left" valign="top">0.995</td><td rowspan="1" colspan="1" align="left" valign="top">49&#x02005;(9)</td><td rowspan="1" colspan="1" align="left" valign="top">2.19</td><td rowspan="1" colspan="1" align="left" valign="top">1.26&#x02005;(50)</td><td rowspan="1" colspan="1" align="left" valign="top">1.04</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IH</td><td rowspan="1" colspan="1" align="left" valign="top">4.5&#x02005;(36)</td><td rowspan="1" colspan="1" align="left" valign="top">1.20</td><td rowspan="1" colspan="1" align="left" valign="top">0.996</td><td rowspan="1" colspan="1" align="left" valign="top">52&#x02005;(10)</td><td rowspan="1" colspan="1" align="left" valign="top">2.22</td><td rowspan="1" colspan="1" align="left" valign="top">1.31&#x02005;(51)</td><td rowspan="1" colspan="1" align="left" valign="top">1.07</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IS</td><td rowspan="1" colspan="1" align="left" valign="top">5.0&#x02005;(32)</td><td rowspan="1" colspan="1" align="left" valign="top">1.48</td><td rowspan="1" colspan="1" align="left" valign="top">0.995</td><td rowspan="1" colspan="1" align="left" valign="top">41&#x02005;(7)</td><td rowspan="1" colspan="1" align="left" valign="top">2.11</td><td rowspan="1" colspan="1" align="left" valign="top">1.05&#x02005;(45)</td><td rowspan="1" colspan="1" align="left" valign="top">0.96</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">MBIS</td><td rowspan="1" colspan="1" align="left" valign="top">5.2&#x02005;(27)</td><td rowspan="1" colspan="1" align="left" valign="top">1.56</td><td rowspan="1" colspan="1" align="left" valign="top">0.995</td><td rowspan="1" colspan="1" align="left" valign="top">38&#x02005;(6)</td><td rowspan="1" colspan="1" align="left" valign="top">2.04</td><td rowspan="1" colspan="1" align="left" valign="top">0.97&#x02005;(44)</td><td rowspan="1" colspan="1" align="left" valign="top">0.92</td></tr></tbody></table></table-wrap><table-wrap id="table7" orientation="portrait" position="float"><label>Table 7</label><caption><title>Comparison of structural parameters related to hydrogen atoms with neutron measurements for various electron density partitions (electron density form B3LYP/cc-pVTZ with crystal field represented with point multipoles): average values for five polar hydrogen atoms (in urea and oxalic acid)</title><p>&#x02329;&#x00394;<italic>R</italic>&#x0232a; &#x02013; average difference of bond lengths, &#x02329;|&#x00394;<italic>R</italic>|&#x0232a; &#x02013; average absolute difference of bond lengths, wRMSD(&#x00394;<italic>R</italic>) &#x02013; weighted room mean square deviation for bond lengths [equation (2)<xref ref-type="disp-formula" rid="fd2"/>], <italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub> &#x02013; average ratio of X-ray to neutron bond lengths, &#x02329;|&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>|&#x0232a; &#x02013; average absolute difference of ADP tensor components, wRMSD(&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>) &#x02013; weighted room mean square deviation for components of ADP tensor [equation (3)<xref ref-type="disp-formula" rid="fd3"/>], <italic>S</italic>
<sub>12</sub> &#x02013; average ADP similarity index <italic>S</italic>
<sub>12</sub> [equation (4<xref ref-type="disp-formula" rid="fd4"/>)], &#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a; &#x02013; average ratio of X-ray to neutron volumes of thermal ellipsoids. Partition acronyms: B &#x02013; Becke, H &#x02013; Hirshfeld, IH &#x02013; iterative Hirshfeld, IS &#x02013; iterative stockholder, MBIS &#x02013; minimal basis iterative stockholder.</p></caption><table frame="hsides" rules="groups"><thead valign="bottom"><tr><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x000a0;</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;&#x00394;<italic>R</italic>&#x0232a; (m&#x000c5;)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>R</italic>|&#x0232a; (m&#x000c5;)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>R</italic>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>R</italic>
<sub>X</sub>/<italic>R</italic>
<sub>N</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;|&#x00394;<italic>U</italic>
<sub>ij</sub>|&#x0232a; &#x000d7; 10<sup>4</sup>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">wRSMD (&#x00394;<italic>U</italic>
<sub><italic>ij</italic></sub>)</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">
<italic>S</italic>
<sub>12</sub>
</th><th style="border-bottom:1px solid black;" rowspan="1" colspan="1" align="left" valign="bottom">&#x02329;<italic>V</italic>
<sub>X</sub>/<italic>V</italic>
<sub>N</sub>&#x0232a;</th></tr></thead><tbody valign="top"><tr><td rowspan="1" colspan="1" align="left" valign="top">B</td><td rowspan="1" colspan="1" align="left" valign="top">-18&#x02005;(13)</td><td rowspan="1" colspan="1" align="left" valign="top">18&#x02005;(13)</td><td rowspan="1" colspan="1" align="left" valign="top">5.7</td><td rowspan="1" colspan="1" align="left" valign="top">0.982&#x02005;(13)</td><td rowspan="1" colspan="1" align="left" valign="top">45&#x02005;(19)</td><td rowspan="1" colspan="1" align="left" valign="top">1.90</td><td rowspan="1" colspan="1" align="left" valign="top">2.2&#x02005;(14)</td><td rowspan="1" colspan="1" align="left" valign="top">0.6&#x02005;(3)</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">H</td><td rowspan="1" colspan="1" align="left" valign="top">-8(5)</td><td rowspan="1" colspan="1" align="left" valign="top">8(5)</td><td rowspan="1" colspan="1" align="left" valign="top">2.1</td><td rowspan="1" colspan="1" align="left" valign="top">0.992&#x02005;(5)</td><td rowspan="1" colspan="1" align="left" valign="top">52&#x02005;(15)</td><td rowspan="1" colspan="1" align="left" valign="top">1.55</td><td rowspan="1" colspan="1" align="left" valign="top">1.7&#x02005;(8)</td><td rowspan="1" colspan="1" align="left" valign="top">1.2&#x02005;(5)</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IH</td><td rowspan="1" colspan="1" align="left" valign="top">2(7)</td><td rowspan="1" colspan="1" align="left" valign="top">5.5&#x02005;(37)</td><td rowspan="1" colspan="1" align="left" valign="top">1.1</td><td rowspan="1" colspan="1" align="left" valign="top">1.001&#x02005;(7)</td><td rowspan="1" colspan="1" align="left" valign="top">85&#x02005;(28)</td><td rowspan="1" colspan="1" align="left" valign="top">1.53</td><td rowspan="1" colspan="1" align="left" valign="top">3.2&#x02005;(16)</td><td rowspan="1" colspan="1" align="left" valign="top">1.9&#x02005;(7)</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">IS</td><td rowspan="1" colspan="1" align="left" valign="top">-3(6)</td><td rowspan="1" colspan="1" align="left" valign="top">5.0&#x02005;(33)</td><td rowspan="1" colspan="1" align="left" valign="top">1.5</td><td rowspan="1" colspan="1" align="left" valign="top">0.997&#x02005;(6)</td><td rowspan="1" colspan="1" align="left" valign="top">50&#x02005;(17)</td><td rowspan="1" colspan="1" align="left" valign="top">1.55</td><td rowspan="1" colspan="1" align="left" valign="top">1.8&#x02005;(9)</td><td rowspan="1" colspan="1" align="left" valign="top">1.1&#x02005;(5)</td></tr><tr><td rowspan="1" colspan="1" align="left" valign="top">MBIS</td><td rowspan="1" colspan="1" align="left" valign="top">-3(6)</td><td rowspan="1" colspan="1" align="left" valign="top">4.4&#x02005;(13)</td><td rowspan="1" colspan="1" align="left" valign="top">1.3</td><td rowspan="1" colspan="1" align="left" valign="top">0.997&#x02005;(6)</td><td rowspan="1" colspan="1" align="left" valign="top">49&#x02005;(19)</td><td rowspan="1" colspan="1" align="left" valign="top">1.42</td><td rowspan="1" colspan="1" align="left" valign="top">1.8&#x02005;(9)</td><td rowspan="1" colspan="1" align="left" valign="top">1.0&#x02005;(5)</td></tr></tbody></table></table-wrap></floats-group></article>