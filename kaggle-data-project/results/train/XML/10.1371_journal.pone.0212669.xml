<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.1d3 20150301//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1.dtd?><?SourceDTD.Version 39.96?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">PLoS One</journal-id><journal-id journal-id-type="iso-abbrev">PLoS ONE</journal-id><journal-id journal-id-type="publisher-id">plos</journal-id><journal-id journal-id-type="pmc">plosone</journal-id><journal-title-group><journal-title>PLoS ONE</journal-title></journal-title-group><issn pub-type="epub">1932-6203</issn><publisher><publisher-name>Public Library of Science</publisher-name><publisher-loc>San Francisco, CA USA</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6386283</article-id><article-id pub-id-type="doi">10.1371/journal.pone.0212669</article-id><article-id pub-id-type="publisher-id">PONE-D-18-19943</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Research and Analysis Methods</subject><subj-group><subject>Mathematical and Statistical Techniques</subject><subj-group><subject>Statistical Methods</subject><subj-group><subject>Forecasting</subject></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Physical Sciences</subject><subj-group><subject>Mathematics</subject><subj-group><subject>Statistics</subject><subj-group><subject>Statistical Methods</subject><subj-group><subject>Forecasting</subject></subj-group></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Computer and Information Sciences</subject><subj-group><subject>Data Acquisition</subject></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Genetics</subject><subj-group><subject>Gene Expression</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Biology and Life Sciences</subject><subj-group><subject>Biochemistry</subject><subj-group><subject>Biomarkers</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Physical Sciences</subject><subj-group><subject>Mathematics</subject><subj-group><subject>Algebra</subject><subj-group><subject>Linear Algebra</subject><subj-group><subject>Singular Value Decomposition</subject></subj-group></subj-group></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Science Policy</subject><subj-group><subject>Open Science</subject><subj-group><subject>Open Data</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Research and Analysis Methods</subject><subj-group><subject>Research Design</subject><subj-group><subject>Experimental Design</subject></subj-group></subj-group></subj-group><subj-group subj-group-type="Discipline-v3"><subject>Computer and Information Sciences</subject><subj-group><subject>Artificial Intelligence</subject><subj-group><subject>Machine Learning</subject><subj-group><subject>Support Vector Machines</subject></subj-group></subj-group></subj-group></subj-group></article-categories><title-group><article-title>Batch adjustment by reference alignment (BARA): Improved prediction performance in biological test sets with batch effects</article-title><alt-title alt-title-type="running-head">Batch adjustment method improves prediction performance in biological test sets with batch effects</alt-title></title-group><contrib-group><contrib contrib-type="author"><contrib-id authenticated="true" contrib-id-type="orcid">http://orcid.org/0000-0002-3339-1269</contrib-id><name><surname>Gradin</surname><given-names>Robin</given-names></name><role content-type="http://credit.casrai.org/">Conceptualization</role><role content-type="http://credit.casrai.org/">Data curation</role><role content-type="http://credit.casrai.org/">Formal analysis</role><role content-type="http://credit.casrai.org/">Investigation</role><role content-type="http://credit.casrai.org/">Methodology</role><role content-type="http://credit.casrai.org/">Software</role><role content-type="http://credit.casrai.org/">Validation</role><role content-type="http://credit.casrai.org/">Writing &#x02013; original draft</role><role content-type="http://credit.casrai.org/">Writing &#x02013; review &#x00026; editing</role><xref ref-type="aff" rid="aff001"><sup>1</sup></xref><xref ref-type="corresp" rid="cor001">*</xref></contrib><contrib contrib-type="author"><name><surname>Lindstedt</surname><given-names>Malin</given-names></name><role content-type="http://credit.casrai.org/">Funding acquisition</role><role content-type="http://credit.casrai.org/">Methodology</role><role content-type="http://credit.casrai.org/">Project administration</role><role content-type="http://credit.casrai.org/">Resources</role><role content-type="http://credit.casrai.org/">Supervision</role><role content-type="http://credit.casrai.org/">Writing &#x02013; review &#x00026; editing</role><xref ref-type="aff" rid="aff002"><sup>2</sup></xref></contrib><contrib contrib-type="author"><name><surname>Johansson</surname><given-names>Henrik</given-names></name><role content-type="http://credit.casrai.org/">Conceptualization</role><role content-type="http://credit.casrai.org/">Formal analysis</role><role content-type="http://credit.casrai.org/">Investigation</role><role content-type="http://credit.casrai.org/">Methodology</role><role content-type="http://credit.casrai.org/">Resources</role><role content-type="http://credit.casrai.org/">Supervision</role><role content-type="http://credit.casrai.org/">Visualization</role><role content-type="http://credit.casrai.org/">Writing &#x02013; review &#x00026; editing</role><xref ref-type="aff" rid="aff001"><sup>1</sup></xref></contrib></contrib-group><aff id="aff001"><label>1</label>
<addr-line>SenzaGen AB, Lund, Sweden</addr-line></aff><aff id="aff002"><label>2</label>
<addr-line>Department of Immunotechnology, Lund University, Lund, Sweden</addr-line></aff><contrib-group><contrib contrib-type="editor"><name><surname>Pfeifer</surname><given-names>Susanne P.</given-names></name><role>Editor</role><xref ref-type="aff" rid="edit1"/></contrib></contrib-group><aff id="edit1"><addr-line>Arizona State University, UNITED STATES</addr-line></aff><author-notes><fn fn-type="COI-statement" id="coi001"><p><bold>Competing Interests: </bold>Authors Robin Gradin and Henrik Johansson are employees of Senzagen AB. This does not alter our adherence to PLOS ONE policies on sharing data and materials.</p></fn><corresp id="cor001">* E-mail: <email><EMAIL></email></corresp></author-notes><pub-date pub-type="epub"><day>22</day><month>2</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>14</volume><issue>2</issue><elocation-id>e0212669</elocation-id><history><date date-type="received"><day>5</day><month>7</month><year>2018</year></date><date date-type="accepted"><day>7</day><month>2</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; 2019 Gradin et al</copyright-statement><copyright-year>2019</copyright-year><copyright-holder>Gradin et al</copyright-holder><license xlink:href="http://creativecommons.org/licenses/by/4.0/"><license-p>This is an open access article distributed under the terms of the <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution License</ext-link>, which permits unrestricted use, distribution, and reproduction in any medium, provided the original author and source are credited.</license-p></license></permissions><self-uri content-type="pdf" xlink:href="pone.0212669.pdf"/><abstract><p>Many biological data acquisition platforms suffer from inadvertent inclusion of biologically irrelevant variance in analyzed data, collectively termed batch effects. Batch effects can lead to difficulties in downstream analysis by lowering the power to detect biologically interesting differences and can in certain instances lead to false discoveries. They are especially troublesome in predictive modelling where samples in training sets and test sets are often completely correlated with batches. In this article, we present BARA, a normalization method for adjusting batch effects in predictive modelling. BARA utilizes a few reference samples to adjust for batch effects in a compressed data space spanned by the training set. We evaluate BARA using a collection of publicly available datasets and three different prediction models, and compare its performance to already existing methods developed for similar purposes. The results show that data normalized with BARA generates high and consistent prediction performances. Further, they suggest that BARA produces reliable performances independent of the examined classifiers. We therefore conclude that BARA has great potential to facilitate the development of predictive assays where test sets and training sets are correlated with batch.</p></abstract><funding-group><funding-statement>The authors received no specific funding for this work. SenzaGen AB provided support in the form of salaries for authors R.G and H.J, but did not have any additional role in the study design, data collection and analysis, decision to publish, or preparation of the manuscript. The specific roles of these authors are articulated in the &#x02018;author contributions&#x02019; section.</funding-statement></funding-group><counts><fig-count count="5"/><table-count count="2"/><page-count count="15"/></counts><custom-meta-group><custom-meta id="data-availability"><meta-name>Data Availability</meta-name><meta-value>The R-code used to perform the analysis is available at <ext-link ext-link-type="uri" xlink:href="https://github.com/gradinetal2018/BARA">https://github.com/gradinetal2018/BARA</ext-link>. The data underlying the results presented in the study are available from ArrayExpress (<ext-link ext-link-type="uri" xlink:href="https://www.ebi.ac.uk/arrayexpress/">https://www.ebi.ac.uk/arrayexpress/</ext-link>) with the following accession numbers; E-GEOD-19722, E-GEOD-28654, E-GEOD-29623, E-GEOD-39084, E-GEOD-45216, E-GEOD-45670, E-GEOD-46474, E-GEOD-48278, E-GEOD-48350, E-GEOD-48780, E-GEOD-49243, E-GEOD-50774, E-GEOD-53224, E-GEOD-53890, E-GEOD-54543, E-GEOD-54837, E-GEOD-58697, E-GEOD-59312, E-GEOD-60028, E-GEOD-61804, E-GEOD-63626, E-GEOD-64415, E-GEOD-64857, E-GEOD-67851, E-GEOD-68720.</meta-value></custom-meta></custom-meta-group></article-meta><notes><title>Data Availability</title><p>The R-code used to perform the analysis is available at <ext-link ext-link-type="uri" xlink:href="https://github.com/gradinetal2018/BARA">https://github.com/gradinetal2018/BARA</ext-link>. The data underlying the results presented in the study are available from ArrayExpress (<ext-link ext-link-type="uri" xlink:href="https://www.ebi.ac.uk/arrayexpress/">https://www.ebi.ac.uk/arrayexpress/</ext-link>) with the following accession numbers; E-GEOD-19722, E-GEOD-28654, E-GEOD-29623, E-GEOD-39084, E-GEOD-45216, E-GEOD-45670, E-GEOD-46474, E-GEOD-48278, E-GEOD-48350, E-GEOD-48780, E-GEOD-49243, E-GEOD-50774, E-GEOD-53224, E-GEOD-53890, E-GEOD-54543, E-GEOD-54837, E-GEOD-58697, E-GEOD-59312, E-GEOD-60028, E-GEOD-61804, E-GEOD-63626, E-GEOD-64415, E-GEOD-64857, E-GEOD-67851, E-GEOD-68720.</p></notes></front><body><sec sec-type="intro" id="sec001"><title>Introduction</title><p>Data acquisition techniques designed to quantify biological signals from gene- or protein expression are often associated with batch effects. The problem with batch effects is that it leads to the inclusion of biologically irrelevant variance in the obtained data, which can lower the power of subsequent analyses or lead to false discoveries [<xref rid="pone.0212669.ref001" ref-type="bibr">1</xref>&#x02013;<xref rid="pone.0212669.ref004" ref-type="bibr">4</xref>]. The variance may be due to a variety of different experimental parameters, including analysis date, sample processing or reagent quality [<xref rid="pone.0212669.ref005" ref-type="bibr">5</xref>]. Further, batch effects are not exclusive to high throughput acquisition methods but are also observed in data from lower throughput methods such as qPCR or NanoString nCounter technologies [<xref rid="pone.0212669.ref006" ref-type="bibr">6</xref>, <xref rid="pone.0212669.ref007" ref-type="bibr">7</xref>]. The high incidence of batch effects in multiple biological platforms is a contributing factor to the relatively small number of diagnostic and prognostic biomarker signatures that have been implemented in clinical settings [<xref rid="pone.0212669.ref006" ref-type="bibr">6</xref>, <xref rid="pone.0212669.ref008" ref-type="bibr">8</xref>].</p><p>Some actions have been shown to reduce the impact of batch effects. One such action is to carefully design experiments to minimize the correlation between possible sources of technical variance and known biological factors. However, this action is not possible for all types of experiments. For predictive modelling, for example, the correlation between biological factors and batches cannot be eliminated. This is due to the inherent nature of these experiments, where fixed training sets are often used to infer parameter values used to predict subsequently acquired test sets. This leads to total confoundment between batches and samples in the test sets, which can result in poor predictive performances on test sets [<xref rid="pone.0212669.ref009" ref-type="bibr">9</xref>]. Another option to reduce the impact of batch effects is to apply analytical methods on already obtained data. Many such methods have been designed, but most require prior knowledge of the biological factors of interest and low confoundment between batches and the biological groups [<xref rid="pone.0212669.ref010" ref-type="bibr">10</xref>&#x02013;<xref rid="pone.0212669.ref012" ref-type="bibr">12</xref>]. Examples of such methods are ComBat and surrogate variable analysis (SVA). ComBat is a supervised batch correction method that requires that the sources of batch effects are known. It is a location and scale method that uses the empirical Bayes method to moderate the batch effect estimates, making it better equipped to handle smaller datasets [<xref rid="pone.0212669.ref010" ref-type="bibr">10</xref>]. In contrast to ComBat and other supervised batch effect adjustment methods, SVA does not require that the sources of batch effects are known. Instead, the biological sources of interest should be known and specified in the model. The initial step of SVA estimates and removes the variance associated with the known biological information. Latent structures are then identified in the residual matrix, which can either be removed to generate a cleaned dataset or be incorporated in subsequent significant analyses. Identified latent structures can contain information linked to batch effects, but they can also contain other sources of expression heterogeneity, such as biological factors not included in the initial modeling [<xref rid="pone.0212669.ref011" ref-type="bibr">11</xref>]. Both SVA and ComBat were originally developed for datasets in discovery studies, where biological sources of interest and possible sources of batch effects are known. Because of this, they and other methods developed for similar purposes are not directly applicable to datasets generated in predictive settings. However, by making certain assumptions, the algorithms can be modified to be used in predictive modelling. For ComBat, one must assume that the composition of test sets is similar to that in the training set. But this assumption can be violated when, for example, the size of the test set decreases, as shown in [<xref rid="pone.0212669.ref013" ref-type="bibr">13</xref>]. For SVA, one can assume that latent structures identified in the training set can also be identified in test sets. This assumption was used to develop the frozen SVA algorithm [<xref rid="pone.0212669.ref014" ref-type="bibr">14</xref>]. However, this assumption is not valid if latent structures associated with batch effects are different in the training set and test sets. This can lead to poor predictive performances as shown in [<xref rid="pone.0212669.ref013" ref-type="bibr">13</xref>]. In general, for a normalization method to be applicable in a wide range of prediction problems, it should allow for training sets and test sets to be correlated with batch. Further, the training set should not be altered when normalizing with different test sets. Finally, it should ideally allow test sets to be acquired without the need to include a large amount of reference samples.</p><p>In this paper, we introduce <italic>Batch Adjustment by Reference Alignment</italic> (BARA) to adjust for batch effects in predictive modelling. The method has the advantage that only a few reference samples are necessary to perform batch corrections. Also, rather than attempting to clean the data by removing batch effects from both training set and test sets, BARA aims to transform the test set to make it more similar to the training set. BARA performs the adjustment in a compressed data space spanned by the training set, thereby alleviating the number of necessary batch estimates that needs to be performed. We test the BARA method on a collection of 25 publicly available datasets and show that BARA consistently aids the classifier to achieve high prediction performances. We further show that the performance of BARA is better or comparable to the performance of existing methods on the examined datasets. By reducing the negative impact of batch effects, the prediction performances observed with BARA can facilitate the development of predictive assays.</p></sec><sec sec-type="materials|methods" id="sec002"><title>Materials and methods</title><p>The R software environment was used to perform the analyses presented in this paper [<xref rid="pone.0212669.ref015" ref-type="bibr">15</xref>]. Figures were created with the R-package ggplot2 [<xref rid="pone.0212669.ref016" ref-type="bibr">16</xref>]. In addition, the following R-packages were used; reshape2, dplyr, stringr, data.table, magrittr, foreach, doParallel, e1071, randomForest, class and bapred [<xref rid="pone.0212669.ref017" ref-type="bibr">17</xref>&#x02013;<xref rid="pone.0212669.ref027" ref-type="bibr">27</xref>]. The scripts used to generate the results, including the BARA algorithm, are available at: <ext-link ext-link-type="uri" xlink:href="https://github.com/gradinetal2018/BARA">https://github.com/gradinetal2018/BARA</ext-link>.</p><sec id="sec003"><title>Cross-study datasets</title><p>25 datasets compiled by Hornung et. al. [<xref rid="pone.0212669.ref013" ref-type="bibr">13</xref>] were downloaded from ArrayExpress [<xref rid="pone.0212669.ref028" ref-type="bibr">28</xref>], see <xref rid="pone.0212669.t001" ref-type="table">Table 1</xref>. The gene expression levels of all datasets were quantified with Affymetrix GeneChip Human Genome U133 Plus 2.0. The raw data files (CEL files) were normalized using single channel array normalization [<xref rid="pone.0212669.ref029" ref-type="bibr">29</xref>]. For each dataset, duplicated samples were removed, and only samples with existing annotations of gender were retained. All samples were annotated by <italic>gender/sex</italic>.</p><table-wrap id="pone.0212669.t001" orientation="portrait" position="float"><object-id pub-id-type="doi">10.1371/journal.pone.0212669.t001</object-id><label>Table 1</label><caption><title>Datasets used in the cross-study analysis.</title></caption><alternatives><graphic id="pone.0212669.t001g" xlink:href="pone.0212669.t001"/><table frame="hsides" rules="groups"><colgroup span="1"><col align="left" valign="middle" span="1"/><col align="left" valign="middle" span="1"/><col align="left" valign="middle" span="1"/></colgroup><thead><tr><th align="center" rowspan="1" colspan="1">Accession Number</th><th align="center" rowspan="1" colspan="1">Sample Size</th><th align="center" rowspan="1" colspan="1">Reference</th></tr></thead><tbody><tr><td align="center" rowspan="1" colspan="1">E-GEOD-19722</td><td align="center" rowspan="1" colspan="1">46</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref030" ref-type="bibr">30</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-28654</td><td align="center" rowspan="1" colspan="1">112</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref031" ref-type="bibr">31</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-29623</td><td align="center" rowspan="1" colspan="1">65</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref032" ref-type="bibr">32</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-39084</td><td align="center" rowspan="1" colspan="1">70</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref033" ref-type="bibr">33</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-45216</td><td align="center" rowspan="1" colspan="1">31</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref034" ref-type="bibr">34</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-45670</td><td align="center" rowspan="1" colspan="1">38</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref035" ref-type="bibr">35</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-46474</td><td align="center" rowspan="1" colspan="1">40</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref036" ref-type="bibr">36</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-48278</td><td align="center" rowspan="1" colspan="1">57</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref037" ref-type="bibr">37</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-48350</td><td align="center" rowspan="1" colspan="1">83</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref038" ref-type="bibr">38</xref>, <xref rid="pone.0212669.ref039" ref-type="bibr">39</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-48780</td><td align="center" rowspan="1" colspan="1">49</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref040" ref-type="bibr">40</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-49243</td><td align="center" rowspan="1" colspan="1">73</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref041" ref-type="bibr">41</xref>, <xref rid="pone.0212669.ref042" ref-type="bibr">42</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-50774</td><td align="center" rowspan="1" colspan="1">21</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref043" ref-type="bibr">43</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-53224</td><td align="center" rowspan="1" colspan="1">53</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref044" ref-type="bibr">44</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-53890</td><td align="center" rowspan="1" colspan="1">41</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref045" ref-type="bibr">45</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-54543</td><td align="center" rowspan="1" colspan="1">30</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref046" ref-type="bibr">46</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-54837</td><td align="center" rowspan="1" colspan="1">226</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref047" ref-type="bibr">47</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-58697</td><td align="center" rowspan="1" colspan="1">124</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref048" ref-type="bibr">48</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-59312</td><td align="center" rowspan="1" colspan="1">79</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref049" ref-type="bibr">49</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-60028</td><td align="center" rowspan="1" colspan="1">24</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref050" ref-type="bibr">50</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-61804</td><td align="center" rowspan="1" colspan="1">325</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref051" ref-type="bibr">51</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-63626</td><td align="center" rowspan="1" colspan="1">63</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref052" ref-type="bibr">52</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-64415</td><td align="center" rowspan="1" colspan="1">209</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref053" ref-type="bibr">53</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-64857</td><td align="center" rowspan="1" colspan="1">81</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref054" ref-type="bibr">54</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-67851</td><td align="center" rowspan="1" colspan="1">31</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref055" ref-type="bibr">55</xref>]</td></tr><tr><td align="center" rowspan="1" colspan="1">E-GEOD-68720</td><td align="center" rowspan="1" colspan="1">97</td><td align="center" rowspan="1" colspan="1">[<xref rid="pone.0212669.ref056" ref-type="bibr">56</xref>]</td></tr></tbody></table></alternatives><table-wrap-foot><fn id="t001fn001"><p>The table describes each dataset&#x02019;s accession number and the number of samples extracted from it.</p></fn></table-wrap-foot></table-wrap></sec><sec id="sec004"><title>Cross-study prediction evaluation</title><p>Cross-study prediction performances were used to evaluate the performance of BARA and to compare it to existing normalization methods. The normalization methods included in this analysis were; BARA, ComBat [<xref rid="pone.0212669.ref010" ref-type="bibr">10</xref>], FABatch [<xref rid="pone.0212669.ref021" ref-type="bibr">21</xref>], fSVA exact [<xref rid="pone.0212669.ref014" ref-type="bibr">14</xref>], mean centering, ratio A, reference centering, reference ratio A and standardization. The reference centering method subtracts the reference samples&#x02019; mean expression of each gene from all samples in the training set and the test set respectively. Similarly, the reference ratio A method scales each gene and sample in the training set and the test set by their respective reference samples mean expression. To examine the predictive performance on normalized data, each of the 25 datasets was iteratively used as a temporary training set. First, 3 samples from the same biological group were randomly selected as reference samples in the training set. Next, using all samples in the training set, the 500 most significant differentially expressed genes, comparing males to females, were identified using limma [<xref rid="pone.0212669.ref057" ref-type="bibr">57</xref>, <xref rid="pone.0212669.ref058" ref-type="bibr">58</xref>]. Because the normalization methods had all been adjusted to be used in predictive modelling, as implemented in the bapred package [<xref rid="pone.0212669.ref021" ref-type="bibr">21</xref>] or through implementations in R, each method was first applied to the training set. Next, the transformed training set was used to define the prediction models. Three different prediction models were examined; k-nearest neighbors (kNN), random forest, and support vector machines (SVM). The prediction models were tuned using repeated cross-validation on the training set, with 3 repeats and 10 folds. The parameters resulting in the highest mean prediction performance, evaluated using Mathews Correlation Coefficient (MCC), were selected to establish the final prediction model. To allow for variation among the samples acting as reference sample, the test set normalization and prediction procedure was repeated 10 times for each test set, using a different selection of reference samples in each iteration. More specifically, when classifying the samples in each test set, 3 samples from the same group as the reference samples in the training set were randomly selected from the test set. The normalization methods that did not rely on reference samples used all samples in the test set, including the 3 reference samples, while the reference-based normalization methods only used the reference samples to normalize the data. Because information about the group of the reference samples could be considered being leaked during the normalization procedure, the reference samples were removed from the test sets before the predictions were made. The final prediction performance for each test set was calculated as the median MCC from the 10 iterations. To obtain an overall prediction performance for each training set, the MCCs of the 24 test sets were averaged. A summarized prediction score for each normalization and prediction model was calculated as the mean MCC from all the training sets.</p></sec><sec id="sec005"><title>Assessment of BARA&#x02019;s dependence on the number of reference samples</title><p>To assess the performance of the BARA algorithm as the number of reference samples was varied, the cross-study prediction approach described above was repeated. The performance estimation was repeated 6 times, where the number of utilized reference samples was varied from 1 sample to 6 samples. The predictive performances were summarized as described above.</p></sec><sec id="sec006"><title>The BARA algorithm</title><p>The BARA algorithm was created specifically for predictive modelling, where a fixed training set is used to classify test samples possibly affected by batch effects. The training set is used to identify a set of directions that captures the largest part of the variance in the data, using singular value decomposition (SVD). This step allows the data to be compressed into a lower dimensional space, which reduces the number of necessary batch estimates, and simultaneously decreases the complexity of the data.</p><p>The training dataset, X, contains m samples in rows and n variables in columns. Each variable in X is centered by its mean value, and the matrix is decomposed using SVD to identify the directions where the batch adjustment is performed.
<disp-formula id="pone.0212669.e001"><alternatives><graphic xlink:href="pone.0212669.e001.jpg" id="pone.0212669.e001g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M1"><mml:mrow><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>m</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>x</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:math></alternatives><label>1</label></disp-formula>
<disp-formula id="pone.0212669.e002"><alternatives><graphic xlink:href="pone.0212669.e002.jpg" id="pone.0212669.e002g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M2"><mml:mrow><mml:msup><mml:mrow><mml:mi>U</mml:mi><mml:mi>S</mml:mi><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mi>T</mml:mi></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:mi>S</mml:mi><mml:mi>V</mml:mi><mml:mi>D</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mrow><mml:mi>x</mml:mi></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math></alternatives><label>2</label></disp-formula>
where <inline-formula id="pone.0212669.e003"><alternatives><graphic xlink:href="pone.0212669.e003.jpg" id="pone.0212669.e003g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M3"><mml:mover accent="true"><mml:mrow><mml:mi mathvariant="normal">x</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:math></alternatives></inline-formula> represents a 1*n dimensional vector containing the column means of X, U is the left singular vectors, S the singular values, and V the right singular vectors. The number of dimensions retained, <italic>k</italic>, is an adjustable parameter that can be set by using a predetermined value, estimated with for example cross-validation, or determined by setting an acceptable loss of variance in the training set, for example 10%. The centered training data is then multiplied by the first <italic>k</italic> columns of the right singular vector to obtain a transformed training set.</p><disp-formula id="pone.0212669.e004"><alternatives><graphic xlink:href="pone.0212669.e004.jpg" id="pone.0212669.e004g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M4"><mml:mrow><mml:msup><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mo>*</mml:mo><mml:msub><mml:mrow><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mo>,</mml:mo><mml:mn>1</mml:mn><mml:mo>:</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></alternatives><label>3</label></disp-formula><p>The test dataset, Z, contains p samples in rows and n variables. The variables are first adjusted by subtracting the mean values of the training data, and is then projected onto the identified directions.</p><disp-formula id="pone.0212669.e005"><alternatives><graphic xlink:href="pone.0212669.e005.jpg" id="pone.0212669.e005g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M5"><mml:mrow><mml:msub><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>p</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:msub><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>x</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:math></alternatives><label>4</label></disp-formula><disp-formula id="pone.0212669.e006"><alternatives><graphic xlink:href="pone.0212669.e006.jpg" id="pone.0212669.e006g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M6"><mml:mrow><mml:msup><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mn>0</mml:mn></mml:mrow></mml:msub><mml:mo>*</mml:mo><mml:msub><mml:mrow><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mo>,</mml:mo><mml:mn>1</mml:mn><mml:mo>:</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></alternatives><label>5</label></disp-formula><p>A batch adjustment factor, a<sub>j</sub>, is estimated for all retained dimensions, using the reference samples present in both the training set and the test set. For example, the adjustment factor of dimension <italic>j</italic> is estimated by comparing the mean value of the reference samples in the training set for dimension <italic>j</italic>, to the mean value of the reference samples in the test set for dimension <italic>j</italic>.</p><disp-formula id="pone.0212669.e007"><alternatives><graphic xlink:href="pone.0212669.e007.jpg" id="pone.0212669.e007g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M7"><mml:mrow><mml:msub><mml:mrow><mml:mi>a</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msubsup><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>z</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msubsup><mml:mo>&#x02212;</mml:mo><mml:msubsup><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>x</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>f</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msubsup></mml:mrow></mml:math></alternatives><label>6</label></disp-formula><p>Where <inline-formula id="pone.0212669.e008"><alternatives><graphic xlink:href="pone.0212669.e008.jpg" id="pone.0212669.e008g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M8"><mml:mrow><mml:msub><mml:mover accent="true"><mml:mi mathvariant="normal">z</mml:mi><mml:mo>&#x000af;</mml:mo></mml:mover><mml:mrow><mml:mi mathvariant="normal">ref</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></alternatives></inline-formula> and <inline-formula id="pone.0212669.e009"><alternatives><graphic xlink:href="pone.0212669.e009.jpg" id="pone.0212669.e009g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M9"><mml:mrow><mml:msub><mml:mover accent="true"><mml:mi mathvariant="normal">x</mml:mi><mml:mo>&#x000af;</mml:mo></mml:mover><mml:mrow><mml:mi mathvariant="normal">ref</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math></alternatives></inline-formula> are 1*k dimensional vectors containing the variable means for the reference samples in the transformed test set and the transformed training set respectively. The transformed test data is then adjusted by the adjustment factors and both datasets are reconstructed to the original data space.</p><disp-formula id="pone.0212669.e010"><alternatives><graphic xlink:href="pone.0212669.e010.jpg" id="pone.0212669.e010g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M10"><mml:mrow><mml:msup><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>p</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:msubsup><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msubsup></mml:mrow></mml:mrow><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mi>a</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:mrow></mml:math></alternatives><label>7</label></disp-formula><disp-formula id="pone.0212669.e011"><alternatives><graphic xlink:href="pone.0212669.e011.jpg" id="pone.0212669.e011g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M11"><mml:mrow><mml:msup><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:msup><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msup><mml:mo>*</mml:mo><mml:msubsup><mml:mrow><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mo>,</mml:mo><mml:mn>1</mml:mn><mml:mo>:</mml:mo><mml:mi>k</mml:mi></mml:mrow><mml:mrow><mml:mi>T</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:math></alternatives><label>8</label></disp-formula><disp-formula id="pone.0212669.e012"><alternatives><graphic xlink:href="pone.0212669.e012.jpg" id="pone.0212669.e012g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M12"><mml:mrow><mml:msup><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:msup><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02032;</mml:mo></mml:mrow></mml:msup><mml:mo>*</mml:mo><mml:msubsup><mml:mrow><mml:mi>V</mml:mi></mml:mrow><mml:mrow><mml:mo>,</mml:mo><mml:mn>1</mml:mn><mml:mo>:</mml:mo><mml:mi>k</mml:mi></mml:mrow><mml:mrow><mml:mi>T</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:math></alternatives><label>9</label></disp-formula><p>To achieve a level of expression to what was originally observed, the mean values estimated from the training set are finally added to the reconstructed data.</p><disp-formula id="pone.0212669.e013"><alternatives><graphic xlink:href="pone.0212669.e013.jpg" id="pone.0212669.e013g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M13"><mml:mrow><mml:msup><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>m</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:msubsup><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msubsup><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>x</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:math></alternatives><label>10</label></disp-formula><disp-formula id="pone.0212669.e014"><alternatives><graphic xlink:href="pone.0212669.e014.jpg" id="pone.0212669.e014g" mimetype="image" position="anchor" orientation="portrait"/><mml:math id="M14"><mml:mrow><mml:msup><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>p</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:mrow><mml:mstyle displaystyle="false"><mml:msubsup><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:msubsup></mml:mstyle><mml:mrow><mml:msubsup><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msubsup><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>x</mml:mi></mml:mrow><mml:mo>&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:mrow></mml:math></alternatives><label>11</label></disp-formula><p>The predictions are then performed as normal, where the model is built from the compressed training dataset, X<sup>k</sup>, and the predictions are made on the adjusted test set, Z<sup>k</sup>.</p></sec><sec id="sec007"><title>Normalization methods and parameter settings</title><p>The following methods were used through their implementations in the R-package bapred; ComBat, FAbatch, fSVA exact, mean centering, ratio A and standardization. For FAbatch, the default values of the parameters were used, i.e. the number of factors to estimate for each batch was left unspecified, the preliminary probabilities were estimated using leave-one-out cross-validation, maximum number of iterations were 100, and the maximum number of factors were 12. For fSVA exact, the algorithm parameter was changed to correspond to the exact algorithm instead of the fast, while the default values were used for the remaining parameters. For the other methods implemented in the bapred package, no additional parameter values could be specified.</p><p>The two reference-based methods, reference mean centering and reference ratio A, where implemented in R. Reference mean centering subtracted each batch&#x02019;s genes by the mean expression of its reference samples, and reference ratio A scaled each batch&#x02019;s genes expression by the mean expression of the reference samples.</p><p>BARA was implemented by specifying the loss parameter as a criterion for selecting the number of dimensions to retain. The loss parameter was set to 10%. Thus, at most 10% of the variance in the training data was lost in the normalization.</p></sec><sec id="sec008"><title>Prediction models</title><p>Three types of prediction models were used to assess the performance of the normalization methods in the cross-study analysis. The prediction models were; random forest, kNN and SVM with linear kernel. The prediction models were implemented using the R-packages randomForest, class and e1071 [<xref rid="pone.0212669.ref018" ref-type="bibr">18</xref>, <xref rid="pone.0212669.ref022" ref-type="bibr">22</xref>, <xref rid="pone.0212669.ref026" ref-type="bibr">26</xref>]. The prediction models were selected to include both linear and non-linear classifiers. For every training set, the prediction models were tuned to maximize the MCC using repeated cross-validation with 3 repeats and 10 folds. For the respective prediction model, the following parameters and parameter values were tuned:</p><list list-type="bullet"><list-item><p>kNN
<list list-type="simple"><list-item><label>&#x025cb;</label><p>Number of nearest neighbors: 1, 2, 3, 4, 5, 6, 7, 8, 9</p></list-item></list></p></list-item><list-item><p>Random Forest
<list list-type="simple"><list-item><label>&#x025cb;</label><p>Mtry: 5, 7, 9, 10, 11, 13, 15, 17</p></list-item><list-item><label>&#x025cb;</label><p>Ntree: 500, 1000, 1500, 2000, 2500, 3000</p></list-item></list></p></list-item><list-item><p>SVM
<list list-type="simple"><list-item><label>&#x025cb;</label><p>Cost: 0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10, 50</p></list-item></list></p></list-item></list></sec></sec><sec sec-type="results" id="sec009"><title>Results</title><p>To assess the performance of BARA and to compare it to existing normalization methods, cross-study predictions were examined. Because the acquired datasets originated from separate studies, the biological annotation used for classification was sex/gender. <xref ref-type="fig" rid="pone.0212669.g001">Fig 1</xref> shows a PCA plot of the 25 datasets, which shows clear signs of batch effects.</p><fig id="pone.0212669.g001" orientation="portrait" position="float"><object-id pub-id-type="doi">10.1371/journal.pone.0212669.g001</object-id><label>Fig 1</label><caption><title>PCA plot of the datasets.</title><p>The figure shows the first two principal components after merging the acquired datasets. The samples were colored by the datasets from where they originated.</p></caption><graphic xlink:href="pone.0212669.g001"/></fig><p>To assess the different normalization methods, each dataset was iteratively used as training set to define a prediction model. Batch effects between the training sets and the test sets were adjusted with the examined normalization methods. Normalized test sets were classified with the trained prediction models and MCCs were calculated to estimate the prediction performances. The resulting MCCs for the normalized data and for the unnormalized data on the 3 different prediction models are shown in Figs <xref ref-type="fig" rid="pone.0212669.g002">2</xref>&#x02013;<xref ref-type="fig" rid="pone.0212669.g004">4</xref>. Further, the mean performance for each normalization method and prediction model can be seen in <xref rid="pone.0212669.t002" ref-type="table">Table 2</xref>. Figs <xref ref-type="fig" rid="pone.0212669.g002">2</xref>&#x02013;<xref ref-type="fig" rid="pone.0212669.g004">4</xref> show that data normalized with BARA seems to generate consistent performances independent of the examined prediction model. Further, the estimated MCCs are high with low variance. In fact, considering the calculated performance scores in <xref rid="pone.0212669.t002" ref-type="table">Table 2</xref>, BARA achieves the highest mean MCC compared to the other examined normalization methods. Lastly, BARA also shows an improved prediction performance compared to the unnormalized data.</p><fig id="pone.0212669.g002" orientation="portrait" position="float"><object-id pub-id-type="doi">10.1371/journal.pone.0212669.g002</object-id><label>Fig 2</label><caption><title>Prediction performance, kNN.</title><p>The plot shows the predictive performances for the different methods when normalized data were classified with kNN models. The boxes represent the 25 MCCs obtained in the iterative exercise where each dataset was used as training set to classify the remaining datasets.</p></caption><graphic xlink:href="pone.0212669.g002"/></fig><fig id="pone.0212669.g003" orientation="portrait" position="float"><object-id pub-id-type="doi">10.1371/journal.pone.0212669.g003</object-id><label>Fig 3</label><caption><title>Prediction performance, random forest.</title><p>The plot shows the predictive performances for the different methods when normalized data were classified with random forest models. The boxes represent the 25 MCCs obtained in the iterative exercise where each dataset was used as training set to classify the remaining datasets.</p></caption><graphic xlink:href="pone.0212669.g003"/></fig><fig id="pone.0212669.g004" orientation="portrait" position="float"><object-id pub-id-type="doi">10.1371/journal.pone.0212669.g004</object-id><label>Fig 4</label><caption><title>Prediction performance, SVM.</title><p>The plot shows the predictive performances for the different methods when normalized data were classified with SVMs. The boxes represent the 25 MCCs obtained in the iterative exercise where each dataset was used as training set to classify the remaining datasets.</p></caption><graphic xlink:href="pone.0212669.g004"/></fig><table-wrap id="pone.0212669.t002" orientation="portrait" position="float"><object-id pub-id-type="doi">10.1371/journal.pone.0212669.t002</object-id><label>Table 2</label><caption><title>Prediction performances.</title></caption><alternatives><graphic id="pone.0212669.t002g" xlink:href="pone.0212669.t002"/><table frame="hsides" rules="groups"><colgroup span="1"><col align="left" valign="middle" span="1"/><col align="left" valign="middle" span="1"/><col align="left" valign="middle" span="1"/><col align="left" valign="middle" span="1"/></colgroup><thead><tr><th align="left" rowspan="1" colspan="1">Normalization Method</th><th align="right" rowspan="1" colspan="1">kNN</th><th align="right" rowspan="1" colspan="1">Random Forest</th><th align="right" rowspan="1" colspan="1">SVM</th></tr></thead><tbody><tr><td align="left" rowspan="1" colspan="1">BARA</td><td align="right" rowspan="1" colspan="1"><bold>0.88 &#x000b1; 0.20</bold></td><td align="right" rowspan="1" colspan="1"><bold>0.80 &#x000b1; 0.31</bold></td><td align="right" rowspan="1" colspan="1"><bold>0.78 &#x000b1; 0.28</bold></td></tr><tr><td align="left" rowspan="1" colspan="1">ComBat</td><td align="right" rowspan="1" colspan="1">0.80 &#x000b1; 0.30</td><td align="right" rowspan="1" colspan="1">0.60 &#x000b1; 0.37</td><td align="right" rowspan="1" colspan="1">0.57 &#x000b1; 0.36</td></tr><tr><td align="left" rowspan="1" colspan="1">FAbatch</td><td align="right" rowspan="1" colspan="1">0.65 &#x000b1; 0.24</td><td align="right" rowspan="1" colspan="1">0.55 &#x000b1; 0.30</td><td align="right" rowspan="1" colspan="1">0.54 &#x000b1; 0.27</td></tr><tr><td align="left" rowspan="1" colspan="1">fSVA Exact</td><td align="right" rowspan="1" colspan="1">0.82 &#x000b1; 0.27</td><td align="right" rowspan="1" colspan="1">0.74 &#x000b1; 0.35</td><td align="right" rowspan="1" colspan="1">0.39 &#x000b1; 0.29</td></tr><tr><td align="left" rowspan="1" colspan="1">Mean Centered</td><td align="right" rowspan="1" colspan="1">0.82 &#x000b1; 0.26</td><td align="right" rowspan="1" colspan="1">0.63 &#x000b1; 0.34</td><td align="right" rowspan="1" colspan="1">0.60 &#x000b1; 0.31</td></tr><tr><td align="left" rowspan="1" colspan="1">None</td><td align="right" rowspan="1" colspan="1">0.81 &#x000b1; 0.30</td><td align="right" rowspan="1" colspan="1">0.76 &#x000b1; 0.34</td><td align="right" rowspan="1" colspan="1">0.43 &#x000b1; 0.29</td></tr><tr><td align="left" rowspan="1" colspan="1">Ratio A</td><td align="right" rowspan="1" colspan="1">0.04 &#x000b1; 0.07</td><td align="right" rowspan="1" colspan="1">0.60 &#x000b1; 0.39</td><td align="right" rowspan="1" colspan="1">0.20 &#x000b1; 0.10</td></tr><tr><td align="left" rowspan="1" colspan="1">Reference Centered</td><td align="right" rowspan="1" colspan="1">0.86 &#x000b1; 0.21</td><td align="right" rowspan="1" colspan="1">0.70 &#x000b1; 0.32</td><td align="right" rowspan="1" colspan="1">0.51 &#x000b1; 0.31</td></tr><tr><td align="left" rowspan="1" colspan="1">Reference Ratio A</td><td align="right" rowspan="1" colspan="1">0.20 &#x000b1; 0.26</td><td align="right" rowspan="1" colspan="1">0.71 &#x000b1; 0.34</td><td align="right" rowspan="1" colspan="1">0.37 &#x000b1; 0.16</td></tr><tr><td align="left" rowspan="1" colspan="1">Standardized</td><td align="right" rowspan="1" colspan="1">0.60 &#x000b1; 0.30</td><td align="right" rowspan="1" colspan="1">0.66 &#x000b1; 0.36</td><td align="right" rowspan="1" colspan="1">0.59 &#x000b1; 0.35</td></tr></tbody></table></alternatives><table-wrap-foot><fn id="t002fn001"><p>Prediction performances for each normalization and prediction model. Each performance is given as the mean MCC &#x000b1; the standard deviation.</p></fn></table-wrap-foot></table-wrap><p>Because all information used to adjust for batch effects in the BARA method is estimated from the reference samples, the performance of the method as the number of reference samples was varied was examined. The number of reference samples was varied from 1 sample to 6 samples, and the cross-study prediction approach described above was repeated. Again, the performances were summarized by calculating the mean MCC for each run. The performance for each repeat can be seen in <xref ref-type="fig" rid="pone.0212669.g005">Fig 5</xref>. The plot suggests that the performance of BARA in the examined datasets is robust to the number of reference samples utilized. However, it is also evident that the run with a single reference sample achieve the lowest performance metric.</p><fig id="pone.0212669.g005" orientation="portrait" position="float"><object-id pub-id-type="doi">10.1371/journal.pone.0212669.g005</object-id><label>Fig 5</label><caption><title>Performance of BARA as the number of reference samples is varied.</title><p>The points represent the mean MCCs as each of the 25 datasets was iteratively used as training set to classify the remaining samples. The bars represent the standard deviation of the MCC.</p></caption><graphic xlink:href="pone.0212669.g005"/></fig></sec><sec sec-type="conclusions" id="sec010"><title>Discussion</title><p>Batch effects are a widespread problem that exist in most biological data acquisition platforms and hinder the development and implementation of promising biomarker signatures [<xref rid="pone.0212669.ref001" ref-type="bibr">1</xref>&#x02013;<xref rid="pone.0212669.ref005" ref-type="bibr">5</xref>]. Batch effects are especially difficult to account for in predictive modelling where the biological factors in the test set are completely confounded with batch. In this paper, we have introduced BARA, a normalization method for the adjustment of batch effects in predictive modelling, and compared it to already existing methods. The aim of normalization methods applied in settings of predictive modelling is to make prediction models and inferred information transferable to test sets affected by batch effects. Common strategies suggest using reference samples to estimate the perturbation induced by batch effects [<xref rid="pone.0212669.ref009" ref-type="bibr">9</xref>]. However, including multiple additional samples in each batch of test samples can be both time consuming and add analytical costs. Therefore, normalization methods should ideally require none or few reference samples.</p><p>Only a few reference samples are required by BARA; three were used in the comparison described above and BARA was also found to achieve robust performance metrics when only a single reference sample was used. However, the optimal number of reference samples could generally be assumed to be based on a trade-off between costs and accuracy. Because the reference samples are used to estimate mean adjustments for every batch in a compressed data space, additional reference samples should provide more certain estimates of the mean values. This was also observed when the number of reference samples used by BARA was varied, see <xref ref-type="fig" rid="pone.0212669.g005">Fig 5</xref>. Because mean values are estimated, it is also expected that the ideal number of reference samples will not be the same for different data acquisition techniques and datasets but will depend on, for example, the variation between replicates. Further, even though random assignment of reference samples does not represent an ideal selection strategy, the performance of BARA could be considered stable as indicated by the low variance in the performance metrics, see Figs <xref ref-type="fig" rid="pone.0212669.g002">2</xref>&#x02013;<xref ref-type="fig" rid="pone.0212669.g004">4</xref>. In an ideal scenario, the reference samples should represent a standardized sample where the major difference compared to other reference samples are batch effects. This could lead to better estimates with a lower number of required reference samples.</p><p>BARA estimates the batch adjustments in a compressed data space spanned by the training set. The compression is calculated with SVD and is thus a linear combination of the original variables along the directions of maximum variance. SVD was chosen because we hypothesized that it would be suitable for many datasets associated with predictive modelling where biomarker signatures have been identified to optimize prediction performances, which suggests that low-dimensional representations of the data exist that captures large fractions of the important variance. Further, SVD is a well-known operation that offers a convenient way of compressing data, performing batch adjustment and reconstructing the original variables. Because SVD is used in BARA, multiple levels of compression can be selected for a dataset depending on the number of directions that are retained. In the cross-study analyses described above, a maximum of 10% of the variance in the training set was considered an acceptable loss, and the smallest number of dimensions satisfying this condition was selected in the normalization steps. However, other approaches for selecting an optimal number of dimensions to retain in a specific dataset could be pursued. For example, cross-validation performances could be compared as the number of dimensions retained are varied, or an external validation set affected by batch effects could aid the selection by better mimicking an actual prediction scenario.</p><p>The ability of BARA to restore the predictive performances in datasets suffering from batch effects was assessed by cross-study predictions, and the obtained performances were compared to those obtained using existing normalization methods or no normalization. Due to the difficulty in procuring public datasets containing training sets suitable for predictive modelling where external test sets affected by batch effects exist, a collection of datasets previously compiled [<xref rid="pone.0212669.ref013" ref-type="bibr">13</xref>] were used, where sex/gender was used as classification label. Successively, each of the 25 datasets in the collection were designated as a training set. From the training set, the 500 most significant genes after comparing the gene expression of females to males were identified, and prediction models were defined using kNN, random forest and SVM. Batch effects between the training sets and the test sets were removed with the examined normalization methods, and the samples in the test set were classified. Figs <xref ref-type="fig" rid="pone.0212669.g002">2</xref>&#x02013;<xref ref-type="fig" rid="pone.0212669.g004">4</xref> shows the predictive performances after applying the examined normalization methods. The figures show that BARA produces high and consistent performances for the examined datasets independent of prediction model. The results further suggest, that BARA outperforms the other examined normalization methods on the examined datasets, by reaching the highest mean MCC values and consistently show low variation in performance. Further, the performance is also improved compared to using no normalization, which indicates that the BARA algorithm mitigates some of the negative effects caused by batch effects in the studied datasets. It is also worth noting that genes associated with sex/gender are strong predictors, and the performance of the unnormalized data was often higher than those obtained by some of the normalization methods. In fact, BARA was the only normalization method that consistently resulted in improved mean prediction performance compared to the unnormalized data in all three prediction models.</p><p>In conclusion, we have introduced a novel method to adjust for batch effects in predictive modelling and compared it to already existing methods. We show that BARA improves the prediction performances in the examined datasets compared to applying no normalization. Further, the BARA-normalized datasets achieved higher or comparable prediction performances compared to datasets normalized with the other examined methods. These results suggest that BARA can be considered a useful tool to reduce the negative impact of batch effect in predictive modelling.</p></sec></body><back><ack><p>We would like to thank Dr. Lene Nordrum for excellent support in writing the article.</p></ack><ref-list><title>References</title><ref id="pone.0212669.ref001"><label>1</label><mixed-citation publication-type="journal"><name><surname>Lambert</surname><given-names>CG</given-names></name>, <name><surname>Black</surname><given-names>LJ</given-names></name>. <article-title>Learning from our GWAS mistakes: from experimental design to scientific method</article-title>. <source>Biostatistics (Oxford, England)</source>. <year>2012</year>;<volume>13</volume>(<issue>2</issue>):<fpage>195</fpage>&#x02013;<lpage>203</lpage>. <pub-id pub-id-type="doi">10.1093/biostatistics/kxr055</pub-id> PMC3297828. <?supplied-pmid 22285994?><pub-id pub-id-type="pmid">22285994</pub-id></mixed-citation></ref><ref id="pone.0212669.ref002"><label>2</label><mixed-citation publication-type="journal"><name><surname>Leek</surname><given-names>JT</given-names></name>, <name><surname>Scharpf</surname><given-names>RB</given-names></name>, <name><surname>Bravo</surname><given-names>HC</given-names></name>, <name><surname>Simcha</surname><given-names>D</given-names></name>, <name><surname>Langmead</surname><given-names>B</given-names></name>, <name><surname>Johnson</surname><given-names>WE</given-names></name>, <etal>et al</etal>
<article-title>Tackling the widespread and critical impact of batch effects in high-throughput data</article-title>. <source>Nature Reviews Genetics</source>. <year>2010</year>;<volume>11</volume>:<fpage>733</fpage>
<pub-id pub-id-type="doi">10.1038/nrg2825</pub-id>
<ext-link ext-link-type="uri" xlink:href="https://www.nature.com/articles/nrg2825#supplementary-information">https://www.nature.com/articles/nrg2825#supplementary-information</ext-link>. <?supplied-pmid 20838408?><pub-id pub-id-type="pmid">20838408</pub-id></mixed-citation></ref><ref id="pone.0212669.ref003"><label>3</label><mixed-citation publication-type="journal"><name><surname>McLerran</surname><given-names>D</given-names></name>, <name><surname>Grizzle</surname><given-names>WE</given-names></name>, <name><surname>Feng</surname><given-names>Z</given-names></name>, <name><surname>Thompson</surname><given-names>IM</given-names></name>, <name><surname>Bigbee</surname><given-names>WL</given-names></name>, <name><surname>Cazares</surname><given-names>LH</given-names></name>, <etal>et al</etal>
<article-title>SELDI-TOF MS Whole Serum Proteomic Profiling with IMAC Surface Does Not Reliably Detect Prostate Cancer</article-title>. <source>Clinical chemistry</source>. <year>2008</year>;<volume>54</volume>(<issue>1</issue>):<fpage>53</fpage>&#x02013;<lpage>60</lpage>. <pub-id pub-id-type="doi">10.1373/clinchem.2007.091496</pub-id> PMC4332515. <?supplied-pmid 18024530?><pub-id pub-id-type="pmid">18024530</pub-id></mixed-citation></ref><ref id="pone.0212669.ref004"><label>4</label><mixed-citation publication-type="journal"><name><surname>Tung</surname><given-names>P-Y</given-names></name>, <name><surname>Blischak</surname><given-names>JD</given-names></name>, <name><surname>Hsiao</surname><given-names>CJ</given-names></name>, <name><surname>Knowles</surname><given-names>DA</given-names></name>, <name><surname>Burnett</surname><given-names>JE</given-names></name>, <name><surname>Pritchard</surname><given-names>JK</given-names></name>, <etal>et al</etal>
<article-title>Batch effects and the effective design of single-cell gene expression studies</article-title>. <source>Scientific reports</source>. <year>2017</year>;<volume>7</volume>:<fpage>39921</fpage>
<pub-id pub-id-type="doi">10.1038/srep39921</pub-id>
<ext-link ext-link-type="uri" xlink:href="https://www.nature.com/articles/srep39921#supplementary-information">https://www.nature.com/articles/srep39921#supplementary-information</ext-link>. <?supplied-pmid 28045081?><pub-id pub-id-type="pmid">28045081</pub-id></mixed-citation></ref><ref id="pone.0212669.ref005"><label>5</label><mixed-citation publication-type="book"><name><surname>Scherer</surname><given-names>A.</given-names></name>
<source>Batch effects and noise in microarray experiments: sources and solutions</source>: <publisher-name>John Wiley &#x00026; Sons</publisher-name>; <year>2009</year>.</mixed-citation></ref><ref id="pone.0212669.ref006"><label>6</label><mixed-citation publication-type="journal"><name><surname>Goh</surname><given-names>WWB</given-names></name>, <name><surname>Wang</surname><given-names>W</given-names></name>, <name><surname>Wong</surname><given-names>L</given-names></name>. <article-title>Why Batch Effects Matter in Omics Data, and How to Avoid Them</article-title>. <source>Trends in Biotechnology</source>. <volume>35</volume>(<issue>6</issue>):<fpage>498</fpage>&#x02013;<lpage>507</lpage>. <pub-id pub-id-type="doi">10.1016/j.tibtech.2017.02.012</pub-id>
<?supplied-pmid 28351613?><pub-id pub-id-type="pmid">28351613</pub-id></mixed-citation></ref><ref id="pone.0212669.ref007"><label>7</label><mixed-citation publication-type="journal"><name><surname>Talhouk</surname><given-names>A</given-names></name>, <name><surname>Kommoss</surname><given-names>S</given-names></name>, <name><surname>Mackenzie</surname><given-names>R</given-names></name>, <name><surname>Cheung</surname><given-names>M</given-names></name>, <name><surname>Leung</surname><given-names>S</given-names></name>, <name><surname>Chiu</surname><given-names>DS</given-names></name>, <etal>et al</etal>
<article-title>Single-Patient Molecular Testing with NanoString nCounter Data Using a Reference-Based Strategy for Batch Effect Correction</article-title>. <source>PLOS ONE</source>. <year>2016</year>;<volume>11</volume>(<issue>4</issue>):<fpage>e0153844</fpage>
<pub-id pub-id-type="doi">10.1371/journal.pone.0153844</pub-id>
<?supplied-pmid 27096160?><pub-id pub-id-type="pmid">27096160</pub-id></mixed-citation></ref><ref id="pone.0212669.ref008"><label>8</label><mixed-citation publication-type="journal"><name><surname>Diamandis</surname><given-names>EP</given-names></name>. <article-title>Cancer Biomarkers: Can We Turn Recent Failures into Success?</article-title>
<source>JNCI Journal of the National Cancer Institute</source>. <year>2010</year>;<volume>102</volume>(<issue>19</issue>):<fpage>1462</fpage>&#x02013;<lpage>7</lpage>. <pub-id pub-id-type="doi">10.1093/jnci/djq306</pub-id> PMC2950166. <?supplied-pmid 20705936?><pub-id pub-id-type="pmid">20705936</pub-id></mixed-citation></ref><ref id="pone.0212669.ref009"><label>9</label><mixed-citation publication-type="journal"><name><surname>Luo</surname><given-names>J</given-names></name>, <name><surname>Schumacher</surname><given-names>M</given-names></name>, <name><surname>Scherer</surname><given-names>A</given-names></name>, <name><surname>Sanoudou</surname><given-names>D</given-names></name>, <name><surname>Megherbi</surname><given-names>D</given-names></name>, <name><surname>Davison</surname><given-names>T</given-names></name>, <etal>et al</etal>
<article-title>A comparison of batch effect removal methods for enhancement of prediction performance using MAQC-II microarray gene expression data</article-title>. <source>The Pharmacogenomics Journal</source>. <year>2010</year>;<volume>10</volume>(<issue>4</issue>):<fpage>278</fpage>&#x02013;<lpage>91</lpage>. <pub-id pub-id-type="doi">10.1038/tpj.2010.57</pub-id> PMC2920074. <?supplied-pmid 20676067?><pub-id pub-id-type="pmid">20676067</pub-id></mixed-citation></ref><ref id="pone.0212669.ref010"><label>10</label><mixed-citation publication-type="journal"><name><surname>Johnson</surname><given-names>WE</given-names></name>, <name><surname>Li</surname><given-names>C</given-names></name>, <name><surname>Rabinovic</surname><given-names>A</given-names></name>. <article-title>Adjusting batch effects in microarray expression data using empirical Bayes methods</article-title>. <source>Biostatistics</source>. <year>2007</year>;<volume>8</volume>(<issue>1</issue>):<fpage>118</fpage>&#x02013;<lpage>27</lpage>. <pub-id pub-id-type="doi">10.1093/biostatistics/kxj037</pub-id>
<?supplied-pmid 16632515?><pub-id pub-id-type="pmid">16632515</pub-id></mixed-citation></ref><ref id="pone.0212669.ref011"><label>11</label><mixed-citation publication-type="journal"><name><surname>Leek</surname><given-names>JT</given-names></name>, <name><surname>Storey</surname><given-names>JD</given-names></name>. <article-title>Capturing Heterogeneity in Gene Expression Studies by Surrogate Variable Analysis</article-title>. <source>PLOS Genetics</source>. <year>2007</year>;<volume>3</volume>(<issue>9</issue>):<fpage>e161</fpage>
<pub-id pub-id-type="doi">10.1371/journal.pgen.0030161</pub-id>
<?supplied-pmid 17907809?><pub-id pub-id-type="pmid">17907809</pub-id></mixed-citation></ref><ref id="pone.0212669.ref012"><label>12</label><mixed-citation publication-type="journal"><name><surname>Oytam</surname><given-names>Y</given-names></name>, <name><surname>Sobhanmanesh</surname><given-names>F</given-names></name>, <name><surname>Duesing</surname><given-names>K</given-names></name>, <name><surname>Bowden</surname><given-names>JC</given-names></name>, <name><surname>Osmond-McLeod</surname><given-names>M</given-names></name>, <name><surname>Ross</surname><given-names>J</given-names></name>. <article-title>Risk-conscious correction of batch effects: maximising information extraction from high-throughput genomic datasets</article-title>. <source>BMC Bioinformatics</source>. <year>2016</year>;<volume>17</volume>(<issue>1</issue>):<fpage>332</fpage>
<pub-id pub-id-type="doi">10.1186/s12859-016-1212-5</pub-id>
<?supplied-pmid 27585881?><pub-id pub-id-type="pmid">27585881</pub-id></mixed-citation></ref><ref id="pone.0212669.ref013"><label>13</label><mixed-citation publication-type="journal"><name><surname>Hornung</surname><given-names>R</given-names></name>, <name><surname>Causeur</surname><given-names>D</given-names></name>, <name><surname>Bernau</surname><given-names>C</given-names></name>, <name><surname>Boulesteix</surname><given-names>AL</given-names></name>. <article-title>Improving cross-study prediction through addon batch effect adjustment or addon normalization</article-title>. <source>Bioinformatics (Oxford, England)</source>. <year>2017</year>;<volume>33</volume>(<issue>3</issue>):<fpage>397</fpage>&#x02013;<lpage>404</lpage>. Epub 2016/11/01. <pub-id pub-id-type="doi">10.1093/bioinformatics/btw650</pub-id> .<?supplied-pmid 27797760?><pub-id pub-id-type="pmid">27797760</pub-id></mixed-citation></ref><ref id="pone.0212669.ref014"><label>14</label><mixed-citation publication-type="journal"><name><surname>Parker</surname><given-names>HS</given-names></name>, <name><surname>Corrada Bravo</surname><given-names>H</given-names></name>, <name><surname>Leek</surname><given-names>JT</given-names></name>. <article-title>Removing batch effects for prediction problems with frozen surrogate variable analysis</article-title>. <source>PeerJ</source>. <year>2014</year>;<volume>2</volume>:<fpage>e561</fpage> Epub 2014/10/22. <pub-id pub-id-type="doi">10.7717/peerj.561</pub-id>
<?supplied-pmid 25332844?><pub-id pub-id-type="pmid">25332844</pub-id></mixed-citation></ref><ref id="pone.0212669.ref015"><label>15</label><mixed-citation publication-type="other">Team RC. R: A Language and Environment for Statistical Computing. 2018.</mixed-citation></ref><ref id="pone.0212669.ref016"><label>16</label><mixed-citation publication-type="book"><name><surname>Wickham</surname><given-names>H.</given-names></name>
<source>ggplot2: Elegant Graphics for Data Analysis</source>: <publisher-name>Springer-Verlag</publisher-name>
<publisher-loc>New York</publisher-loc>; <year>2009</year>.</mixed-citation></ref><ref id="pone.0212669.ref017"><label>17</label><mixed-citation publication-type="other">Corporation M, Weston S. doParallel: Foreach Parallel Adaptor for the 'parallel' Package. 2017.</mixed-citation></ref><ref id="pone.0212669.ref018"><label>18</label><mixed-citation publication-type="other">David M, Evgenia D, Kurt H, Andreas W, Friedrich L. e1071: Misc Functions of the Department of Statistics, Probability Theory Group (Formerly: E1071), TU Wien. 2018.</mixed-citation></ref><ref id="pone.0212669.ref019"><label>19</label><mixed-citation publication-type="other">Hadley W. stringr: Simple, Consistent Wrappers for Common String Operations. 2018.</mixed-citation></ref><ref id="pone.0212669.ref020"><label>20</label><mixed-citation publication-type="other">Hadley W, Romain F, Lionel H, Kirill M. dplyr: A Grammar of Data Manipulation. 2017.</mixed-citation></ref><ref id="pone.0212669.ref021"><label>21</label><mixed-citation publication-type="journal"><name><surname>Hornung</surname><given-names>R</given-names></name>, <name><surname>Boulesteix</surname><given-names>A-L</given-names></name>, <name><surname>Causeur</surname><given-names>D</given-names></name>. <article-title>Combining location-and-scale batch effect adjustment with data cleaning by latent factor adjustment</article-title>. <source>BMC Bioinformatics</source>. <year>2016</year>;<volume>17</volume>(<issue>1</issue>):<fpage>27</fpage>
<pub-id pub-id-type="doi">10.1186/s12859-015-0870-z</pub-id>
<?supplied-pmid 26753519?><pub-id pub-id-type="pmid">26753519</pub-id></mixed-citation></ref><ref id="pone.0212669.ref022"><label>22</label><mixed-citation publication-type="journal"><name><surname>Liaw</surname><given-names>A</given-names></name>, <name><surname>Wiener</surname><given-names>M</given-names></name>. <article-title>Classification and Regression by randomForest</article-title>. <source>R news</source>. <year>2002</year>;<volume>2</volume>(<issue>3</issue>):<fpage>18</fpage>&#x02013;<lpage>22</lpage>.</mixed-citation></ref><ref id="pone.0212669.ref023"><label>23</label><mixed-citation publication-type="other">Matt D, Arun S. data.table: Extension of `data.frame. 2017.</mixed-citation></ref><ref id="pone.0212669.ref024"><label>24</label><mixed-citation publication-type="other">Microsoft, Steve W. foreach: Provides Foreach Looping Construct for R. 2017.</mixed-citation></ref><ref id="pone.0212669.ref025"><label>25</label><mixed-citation publication-type="other">Stefan MB, Hadley W. magrittr: A Forward-Pipe Operator for R. 2014.</mixed-citation></ref><ref id="pone.0212669.ref026"><label>26</label><mixed-citation publication-type="book"><name><surname>Venables</surname><given-names>WN</given-names></name>, <name><surname>Ripley</surname><given-names>BD</given-names></name>. <source>Modern Applied Statistics with S</source>. <edition>Fourth ed</edition>: <publisher-name>Springer</publisher-name>; <year>2002</year>.</mixed-citation></ref><ref id="pone.0212669.ref027"><label>27</label><mixed-citation publication-type="journal"><name><surname>Wickham</surname><given-names>H.</given-names></name>
<article-title>Reshaping data with the reshape package</article-title>. <source>Journal of statistical software</source>. <year>2007</year>;<volume>21</volume>(<issue>12</issue>).</mixed-citation></ref><ref id="pone.0212669.ref028"><label>28</label><mixed-citation publication-type="journal"><name><surname>Kolesnikov</surname><given-names>N</given-names></name>, <name><surname>Hastings</surname><given-names>E</given-names></name>, <name><surname>Keays</surname><given-names>M</given-names></name>, <name><surname>Melnichuk</surname><given-names>O</given-names></name>, <name><surname>Tang</surname><given-names>YA</given-names></name>, <name><surname>Williams</surname><given-names>E</given-names></name>, <etal>et al</etal>
<article-title>ArrayExpress update&#x02014;simplifying data submissions</article-title>. <source>Nucleic acids research</source>. <year>2015</year>;<volume>43</volume>(Database issue):<fpage>D1113</fpage>&#x02013;<lpage>6</lpage>. Epub 2014/11/02. <pub-id pub-id-type="doi">10.1093/nar/gku1057</pub-id>
<?supplied-pmid 25361974?><pub-id pub-id-type="pmid">25361974</pub-id></mixed-citation></ref><ref id="pone.0212669.ref029"><label>29</label><mixed-citation publication-type="journal"><name><surname>Piccolo</surname><given-names>SR</given-names></name>, <name><surname>Sun</surname><given-names>Y</given-names></name>, <name><surname>Campbell</surname><given-names>JD</given-names></name>, <name><surname>Lenburg</surname><given-names>ME</given-names></name>, <name><surname>Bild</surname><given-names>AH</given-names></name>, <name><surname>Johnson</surname><given-names>WE</given-names></name>. <article-title>A single-sample microarray normalization method to facilitate personalized-medicine workflows</article-title>. <source>Genomics</source>. <year>2012</year>;<volume>100</volume>(<issue>6</issue>):<fpage>337</fpage>&#x02013;<lpage>44</lpage>. Epub 2012/09/11. <pub-id pub-id-type="doi">10.1016/j.ygeno.2012.08.003</pub-id> ; PubMed Central PMCID: PMCPmc3508193.<?supplied-pmid 22959562?><pub-id pub-id-type="pmid">22959562</pub-id></mixed-citation></ref><ref id="pone.0212669.ref030"><label>30</label><mixed-citation publication-type="journal"><name><surname>Shaykhiev</surname><given-names>R</given-names></name>, <name><surname>Wang</surname><given-names>R</given-names></name>, <name><surname>Zwick</surname><given-names>RK</given-names></name>, <name><surname>Hackett</surname><given-names>NR</given-names></name>, <name><surname>Leung</surname><given-names>R</given-names></name>, <name><surname>Moore</surname><given-names>MAS</given-names></name>, <etal>et al</etal>
<article-title>Airway basal cells of healthy smokers express an embryonic stem cell signature relevant to lung cancer</article-title>. <source>Stem Cells</source>. <year>2013</year>;<volume>31</volume>(<issue>9</issue>):<fpage>1992</fpage>&#x02013;<lpage>2002</lpage>. <pub-id pub-id-type="doi">10.1002/stem.1459</pub-id> .<?supplied-pmid 23857717?><pub-id pub-id-type="pmid">23857717</pub-id></mixed-citation></ref><ref id="pone.0212669.ref031"><label>31</label><mixed-citation publication-type="journal"><name><surname>Trojani</surname><given-names>A</given-names></name>, <name><surname>Di Camillo</surname><given-names>B</given-names></name>, <name><surname>Tedeschi</surname><given-names>A</given-names></name>, <name><surname>Lodola</surname><given-names>M</given-names></name>, <name><surname>Montesano</surname><given-names>S</given-names></name>, <name><surname>Ricci</surname><given-names>F</given-names></name>, <etal>et al</etal>
<article-title>Gene expression profiling identifies ARSD as a new marker of disease progression and the sphingolipid metabolism as a potential novel metabolism in chronic lymphocytic leukemia</article-title>. <source>Cancer biomarkers: section A of Disease markers</source>. <year>2011</year>;<volume>11</volume>(<issue>1</issue>):<fpage>15</fpage>&#x02013;<lpage>28</lpage>. Epub 2011/01/01. <pub-id pub-id-type="doi">10.3233/CBM-2012-0259</pub-id> .<?supplied-pmid 22820137?><pub-id pub-id-type="pmid">22820137</pub-id></mixed-citation></ref><ref id="pone.0212669.ref032"><label>32</label><mixed-citation publication-type="journal"><name><surname>Chen</surname><given-names>DT</given-names></name>, <name><surname>Hernandez</surname><given-names>JM</given-names></name>, <name><surname>Shibata</surname><given-names>D</given-names></name>, <name><surname>McCarthy</surname><given-names>SM</given-names></name>, <name><surname>Humphries</surname><given-names>LA</given-names></name>, <name><surname>Clark</surname><given-names>W</given-names></name>, <etal>et al</etal>
<article-title>Complementary strand microRNAs mediate acquisition of metastatic potential in colonic adenocarcinoma</article-title>. <source>Journal of gastrointestinal surgery: official journal of the Society for Surgery of the Alimentary Tract</source>. <year>2012</year>;<volume>16</volume>(<issue>5</issue>):<fpage>905</fpage>&#x02013;<lpage>12</lpage>; discussion 12&#x02013;3. Epub 2012/03/01. <pub-id pub-id-type="doi">10.1007/s11605-011-1815-0</pub-id> .<?supplied-pmid 22362069?><pub-id pub-id-type="pmid">22362069</pub-id></mixed-citation></ref><ref id="pone.0212669.ref033"><label>33</label><mixed-citation publication-type="journal"><name><surname>Kirzin</surname><given-names>S</given-names></name>, <name><surname>Marisa</surname><given-names>L</given-names></name>, <name><surname>Guimbaud</surname><given-names>R</given-names></name>, <name><surname>De Reynies</surname><given-names>A</given-names></name>, <name><surname>Legrain</surname><given-names>M</given-names></name>, <name><surname>Laurent-Puig</surname><given-names>P</given-names></name>, <etal>et al</etal>
<article-title>Sporadic early-onset colorectal cancer is a specific sub-type of cancer: a morphological, molecular and genetics study</article-title>. <source>PLoS One</source>. <year>2014</year>;<volume>9</volume>(<issue>8</issue>):<fpage>e103159</fpage> Epub 2014/08/02. <pub-id pub-id-type="doi">10.1371/journal.pone.0103159</pub-id>
<?supplied-pmid 25083765?><pub-id pub-id-type="pmid">25083765</pub-id></mixed-citation></ref><ref id="pone.0212669.ref034"><label>34</label><mixed-citation publication-type="journal"><name><surname>Lambert</surname><given-names>SR</given-names></name>, <name><surname>Mladkova</surname><given-names>N</given-names></name>, <name><surname>Gulati</surname><given-names>A</given-names></name>, <name><surname>Hamoudi</surname><given-names>R</given-names></name>, <name><surname>Purdie</surname><given-names>K</given-names></name>, <name><surname>Cerio</surname><given-names>R</given-names></name>, <etal>et al</etal>
<article-title>Key differences identified between actinic keratosis and cutaneous squamous cell carcinoma by transcriptome profiling</article-title>. <source>British journal of cancer</source>. <year>2014</year>;<volume>110</volume>(<issue>2</issue>):<fpage>520</fpage>&#x02013;<lpage>9</lpage>. <pub-id pub-id-type="doi">10.1038/bjc.2013.760</pub-id> .<?supplied-pmid 24335922?><pub-id pub-id-type="pmid">24335922</pub-id></mixed-citation></ref><ref id="pone.0212669.ref035"><label>35</label><mixed-citation publication-type="journal"><name><surname>Wen</surname><given-names>J</given-names></name>, <name><surname>Yang</surname><given-names>H</given-names></name>, <name><surname>Liu</surname><given-names>MZ</given-names></name>, <name><surname>Luo</surname><given-names>KJ</given-names></name>, <name><surname>Liu</surname><given-names>H</given-names></name>, <name><surname>Hu</surname><given-names>Y</given-names></name>, <etal>et al</etal>
<article-title>Gene expression analysis of pretreatment biopsies predicts the pathological response of esophageal squamous cell carcinomas to neo-chemoradiotherapy</article-title>. <source>Annals of oncology: official journal of the European Society for Medical Oncology</source>. <year>2014</year>;<volume>25</volume>(<issue>9</issue>):<fpage>1769</fpage>&#x02013;<lpage>74</lpage>. Epub 2014/06/08. <pub-id pub-id-type="doi">10.1093/annonc/mdu201</pub-id> .<?supplied-pmid 24907633?><pub-id pub-id-type="pmid">24907633</pub-id></mixed-citation></ref><ref id="pone.0212669.ref036"><label>36</label><mixed-citation publication-type="journal"><name><surname>Gunther</surname><given-names>OP</given-names></name>, <name><surname>Shin</surname><given-names>H</given-names></name>, <name><surname>Ng</surname><given-names>RT</given-names></name>, <name><surname>McMaster</surname><given-names>WR</given-names></name>, <name><surname>McManus</surname><given-names>BM</given-names></name>, <name><surname>Keown</surname><given-names>PA</given-names></name>, <etal>et al</etal>
<article-title>Novel multivariate methods for integration of genomics and proteomics data: applications in a kidney transplant rejection study</article-title>. <source>Omics: a journal of integrative biology</source>. <year>2014</year>;<volume>18</volume>(<issue>11</issue>):<fpage>682</fpage>&#x02013;<lpage>95</lpage>. Epub 2014/11/12. <pub-id pub-id-type="doi">10.1089/omi.2014.0062</pub-id>
<?supplied-pmid 25387159?><pub-id pub-id-type="pmid">25387159</pub-id></mixed-citation></ref><ref id="pone.0212669.ref037"><label>37</label><mixed-citation publication-type="journal"><name><surname>Huffman</surname><given-names>KM</given-names></name>, <name><surname>Koves</surname><given-names>TR</given-names></name>, <name><surname>Hubal</surname><given-names>MJ</given-names></name>, <name><surname>Abouassi</surname><given-names>H</given-names></name>, <name><surname>Beri</surname><given-names>N</given-names></name>, <name><surname>Bateman</surname><given-names>LA</given-names></name>, <etal>et al</etal>
<article-title>Metabolite signatures of exercise training in human skeletal muscle relate to mitochondrial remodelling and cardiometabolic fitness</article-title>. <source>Diabetologia</source>. <year>2014</year>;<volume>57</volume>(<issue>11</issue>):<fpage>2282</fpage>&#x02013;<lpage>95</lpage>. Epub 2014/08/06. <pub-id pub-id-type="doi">10.1007/s00125-014-3343-4</pub-id>
<?supplied-pmid 25091629?><pub-id pub-id-type="pmid">25091629</pub-id></mixed-citation></ref><ref id="pone.0212669.ref038"><label>38</label><mixed-citation publication-type="journal"><name><surname>Berchtold</surname><given-names>NC</given-names></name>, <name><surname>Coleman</surname><given-names>PD</given-names></name>, <name><surname>Cribbs</surname><given-names>DH</given-names></name>, <name><surname>Rogers</surname><given-names>J</given-names></name>, <name><surname>Gillen</surname><given-names>DL</given-names></name>, <name><surname>Cotman</surname><given-names>CW</given-names></name>. <article-title>Synaptic genes are extensively downregulated across multiple brain regions in normal human aging and Alzheimer's disease</article-title>. <source>Neurobiology of aging</source>. <year>2013</year>;<volume>34</volume>(<issue>6</issue>):<fpage>1653</fpage>&#x02013;<lpage>61</lpage>. Epub 2013/01/01. <pub-id pub-id-type="doi">10.1016/j.neurobiolaging.2012.11.024</pub-id>
<?supplied-pmid 23273601?><pub-id pub-id-type="pmid">23273601</pub-id></mixed-citation></ref><ref id="pone.0212669.ref039"><label>39</label><mixed-citation publication-type="journal"><name><surname>Blair</surname><given-names>LJ</given-names></name>, <name><surname>Nordhues</surname><given-names>BA</given-names></name>, <name><surname>Hill</surname><given-names>SE</given-names></name>, <name><surname>Scaglione</surname><given-names>KM</given-names></name>, <name><surname>O'Leary</surname><given-names>JC</given-names><suffix>3rd</suffix></name>, <name><surname>Fontaine</surname><given-names>SN</given-names></name>, <etal>et al</etal>
<article-title>Accelerated neurodegeneration through chaperone-mediated oligomerization of tau</article-title>. <source>The Journal of clinical investigation</source>. <year>2013</year>;<volume>123</volume>(<issue>10</issue>):<fpage>4158</fpage>&#x02013;<lpage>69</lpage>. Epub 2013/09/04. <pub-id pub-id-type="doi">10.1172/JCI69003</pub-id>
<?supplied-pmid 23999428?><pub-id pub-id-type="pmid">23999428</pub-id></mixed-citation></ref><ref id="pone.0212669.ref040"><label>40</label><mixed-citation publication-type="journal"><name><surname>Sun</surname><given-names>Y</given-names></name>, <name><surname>Caplazi</surname><given-names>P</given-names></name>, <name><surname>Zhang</surname><given-names>J</given-names></name>, <name><surname>Mazloom</surname><given-names>A</given-names></name>, <name><surname>Kummerfeld</surname><given-names>S</given-names></name>, <name><surname>Quinones</surname><given-names>G</given-names></name>, <etal>et al</etal>
<article-title>PILRalpha negatively regulates mouse inflammatory arthritis</article-title>. <source>Journal of immunology (Baltimore, Md: 1950)</source>. <year>2014</year>;<volume>193</volume>(<issue>2</issue>):<fpage>860</fpage>&#x02013;<lpage>70</lpage>. Epub 2014/06/18. <pub-id pub-id-type="doi">10.4049/jimmunol.1400045</pub-id> .<?supplied-pmid 24935926?><pub-id pub-id-type="pmid">24935926</pub-id></mixed-citation></ref><ref id="pone.0212669.ref041"><label>41</label><mixed-citation publication-type="journal"><name><surname>Kool</surname><given-names>M</given-names></name>, <name><surname>Jones</surname><given-names>DT</given-names></name>, <name><surname>Jager</surname><given-names>N</given-names></name>, <name><surname>Northcott</surname><given-names>PA</given-names></name>, <name><surname>Pugh</surname><given-names>TJ</given-names></name>, <name><surname>Hovestadt</surname><given-names>V</given-names></name>, <etal>et al</etal>
<article-title>Genome sequencing of SHH medulloblastoma predicts genotype-related response to smoothened inhibition</article-title>. <source>Cancer cell</source>. <year>2014</year>;<volume>25</volume>(<issue>3</issue>):<fpage>393</fpage>&#x02013;<lpage>405</lpage>. Epub 2014/03/22. <pub-id pub-id-type="doi">10.1016/j.ccr.2014.02.004</pub-id>
<?supplied-pmid 24651015?><pub-id pub-id-type="pmid">24651015</pub-id></mixed-citation></ref><ref id="pone.0212669.ref042"><label>42</label><mixed-citation publication-type="journal"><name><surname>Poschl</surname><given-names>J</given-names></name>, <name><surname>Stark</surname><given-names>S</given-names></name>, <name><surname>Neumann</surname><given-names>P</given-names></name>, <name><surname>Grobner</surname><given-names>S</given-names></name>, <name><surname>Kawauchi</surname><given-names>D</given-names></name>, <name><surname>Jones</surname><given-names>DT</given-names></name>, <etal>et al</etal>
<article-title>Genomic and transcriptomic analyses match medulloblastoma mouse models to their human counterparts</article-title>. <source>Acta neuropathologica</source>. <year>2014</year>;<volume>128</volume>(<issue>1</issue>):<fpage>123</fpage>&#x02013;<lpage>36</lpage>. Epub 2014/05/30. <pub-id pub-id-type="doi">10.1007/s00401-014-1297-8</pub-id> .<?supplied-pmid 24871706?><pub-id pub-id-type="pmid">24871706</pub-id></mixed-citation></ref><ref id="pone.0212669.ref043"><label>43</label><mixed-citation publication-type="journal"><name><surname>Zhang</surname><given-names>L</given-names></name>, <name><surname>Chen</surname><given-names>LH</given-names></name>, <name><surname>Wan</surname><given-names>H</given-names></name>, <name><surname>Yang</surname><given-names>R</given-names></name>, <name><surname>Wang</surname><given-names>Z</given-names></name>, <name><surname>Feng</surname><given-names>J</given-names></name>, <etal>et al</etal>
<article-title>Exome sequencing identifies somatic gain-of-function PPM1D mutations in brainstem gliomas</article-title>. <source>Nat Genet</source>. <year>2014</year>;<volume>46</volume>(<issue>7</issue>):<fpage>726</fpage>&#x02013;<lpage>30</lpage>. Epub 2014/06/02. <pub-id pub-id-type="doi">10.1038/ng.2995</pub-id>
<?supplied-pmid 24880341?><pub-id pub-id-type="pmid">24880341</pub-id></mixed-citation></ref><ref id="pone.0212669.ref044"><label>44</label><mixed-citation publication-type="journal"><name><surname>Wegert</surname><given-names>J</given-names></name>, <name><surname>Ishaque</surname><given-names>N</given-names></name>, <name><surname>Vardapour</surname><given-names>R</given-names></name>, <name><surname>Georg</surname><given-names>C</given-names></name>, <name><surname>Gu</surname><given-names>Z</given-names></name>, <name><surname>Bieg</surname><given-names>M</given-names></name>, <etal>et al</etal>
<article-title>Mutations in the SIX1/2 pathway and the DROSHA/DGCR8 miRNA microprocessor complex underlie high-risk blastemal type Wilms tumors</article-title>. <source>Cancer cell</source>. <year>2015</year>;<volume>27</volume>(<issue>2</issue>):<fpage>298</fpage>&#x02013;<lpage>311</lpage>. Epub 2015/02/12. <pub-id pub-id-type="doi">10.1016/j.ccell.2015.01.002</pub-id> .<?supplied-pmid 25670083?><pub-id pub-id-type="pmid">25670083</pub-id></mixed-citation></ref><ref id="pone.0212669.ref045"><label>45</label><mixed-citation publication-type="journal"><name><surname>Lu</surname><given-names>T</given-names></name>, <name><surname>Aron</surname><given-names>L</given-names></name>, <name><surname>Zullo</surname><given-names>J</given-names></name>, <name><surname>Pan</surname><given-names>Y</given-names></name>, <name><surname>Kim</surname><given-names>H</given-names></name>, <name><surname>Chen</surname><given-names>Y</given-names></name>, <etal>et al</etal>
<article-title>REST and stress resistance in ageing and Alzheimer's disease</article-title>. <source>Nature</source>. <year>2014</year>;<volume>507</volume>(<issue>7493</issue>):<fpage>448</fpage>&#x02013;<lpage>54</lpage>. Epub 2014/03/29. <pub-id pub-id-type="doi">10.1038/nature13163</pub-id>
<?supplied-pmid 24670762?><pub-id pub-id-type="pmid">24670762</pub-id></mixed-citation></ref><ref id="pone.0212669.ref046"><label>46</label><mixed-citation publication-type="journal"><name><surname>Tsay</surname><given-names>JC</given-names></name>, <name><surname>Li</surname><given-names>Z</given-names></name>, <name><surname>Yie</surname><given-names>TA</given-names></name>, <name><surname>Wu</surname><given-names>F</given-names></name>, <name><surname>Segal</surname><given-names>L</given-names></name>, <name><surname>Greenberg</surname><given-names>AK</given-names></name>, <etal>et al</etal>
<article-title>Molecular characterization of the peripheral airway field of cancerization in lung adenocarcinoma</article-title>. <source>PLoS One</source>. <year>2015</year>;<volume>10</volume>(<issue>2</issue>):<fpage>e0118132</fpage> Epub 2015/02/24. <pub-id pub-id-type="doi">10.1371/journal.pone.0118132</pub-id>
<?supplied-pmid 25705890?><pub-id pub-id-type="pmid">25705890</pub-id></mixed-citation></ref><ref id="pone.0212669.ref047"><label>47</label><mixed-citation publication-type="journal"><name><surname>Singh</surname><given-names>D</given-names></name>, <name><surname>Fox</surname><given-names>SM</given-names></name>, <name><surname>Tal-Singer</surname><given-names>R</given-names></name>, <name><surname>Bates</surname><given-names>S</given-names></name>, <name><surname>Riley</surname><given-names>JH</given-names></name>, <name><surname>Celli</surname><given-names>B</given-names></name>. <article-title>Altered gene expression in blood and sputum in COPD frequent exacerbators in the ECLIPSE cohort</article-title>. <source>PLoS One</source>. <year>2014</year>;<volume>9</volume>(<issue>9</issue>):<fpage>e107381</fpage> Epub 2014/09/30. <pub-id pub-id-type="doi">10.1371/journal.pone.0107381</pub-id>
<?supplied-pmid 25265030?><pub-id pub-id-type="pmid">25265030</pub-id></mixed-citation></ref><ref id="pone.0212669.ref048"><label>48</label><mixed-citation publication-type="journal"><name><surname>Salas</surname><given-names>S</given-names></name>, <name><surname>Brulard</surname><given-names>C</given-names></name>, <name><surname>Terrier</surname><given-names>P</given-names></name>, <name><surname>Ranchere-Vince</surname><given-names>D</given-names></name>, <name><surname>Neuville</surname><given-names>A</given-names></name>, <name><surname>Guillou</surname><given-names>L</given-names></name>, <etal>et al</etal>
<article-title>Gene Expression Profiling of Desmoid Tumors by cDNA Microarrays and Correlation with Progression-Free Survival</article-title>. <source>Clinical cancer research: an official journal of the American Association for Cancer Research</source>. <year>2015</year>;<volume>21</volume>(<issue>18</issue>):<fpage>4194</fpage>&#x02013;<lpage>200</lpage>. Epub 2015/04/17. <pub-id pub-id-type="doi">10.1158/1078-0432.ccr-14-2910</pub-id> .<?supplied-pmid 25878329?><pub-id pub-id-type="pmid">25878329</pub-id></mixed-citation></ref><ref id="pone.0212669.ref049"><label>49</label><mixed-citation publication-type="journal"><name><surname>Hou</surname><given-names>J</given-names></name>, <name><surname>van Oord</surname><given-names>G</given-names></name>, <name><surname>Groothuismink</surname><given-names>ZM</given-names></name>, <name><surname>Claassen</surname><given-names>MA</given-names></name>, <name><surname>Kreefft</surname><given-names>K</given-names></name>, <name><surname>Zaaraoui-Boutahar</surname><given-names>F</given-names></name>, <etal>et al</etal>
<article-title>Gene expression profiling to predict and assess the consequences of therapy-induced virus eradication in chronic hepatitis C virus infection</article-title>. <source>Journal of virology</source>. <year>2014</year>;<volume>88</volume>(<issue>21</issue>):<fpage>12254</fpage>&#x02013;<lpage>64</lpage>. Epub 2014/08/08. <pub-id pub-id-type="doi">10.1128/JVI.00775-14</pub-id>
<?supplied-pmid 25100847?><pub-id pub-id-type="pmid">25100847</pub-id></mixed-citation></ref><ref id="pone.0212669.ref050"><label>50</label><mixed-citation publication-type="journal"><name><surname>Dhingra</surname><given-names>N</given-names></name>, <name><surname>Shemer</surname><given-names>A</given-names></name>, <name><surname>Correa da Rosa</surname><given-names>J</given-names></name>, <name><surname>Rozenblit</surname><given-names>M</given-names></name>, <name><surname>Fuentes-Duculan</surname><given-names>J</given-names></name>, <name><surname>Gittler</surname><given-names>JK</given-names></name>, <etal>et al</etal>
<article-title>Molecular profiling of contact dermatitis skin identifies allergen-dependent differences in immune response</article-title>. <source>The Journal of allergy and clinical immunology</source>. <year>2014</year>;<volume>134</volume>(<issue>2</issue>):<fpage>362</fpage>&#x02013;<lpage>72</lpage>. Epub 2014/04/29. <pub-id pub-id-type="doi">10.1016/j.jaci.2014.03.009</pub-id> .<?supplied-pmid 24768652?><pub-id pub-id-type="pmid">24768652</pub-id></mixed-citation></ref><ref id="pone.0212669.ref051"><label>51</label><mixed-citation publication-type="journal"><name><surname>Metzelder</surname><given-names>SK</given-names></name>, <name><surname>Michel</surname><given-names>C</given-names></name>, <name><surname>von Bonin</surname><given-names>M</given-names></name>, <name><surname>Rehberger</surname><given-names>M</given-names></name>, <name><surname>Hessmann</surname><given-names>E</given-names></name>, <name><surname>Inselmann</surname><given-names>S</given-names></name>, <etal>et al</etal>
<article-title>NFATc1 as a therapeutic target in FLT3-ITD-positive AML</article-title>. <source>Leukemia</source>. <year>2015</year>;<volume>29</volume>(<issue>7</issue>):<fpage>1470</fpage>&#x02013;<lpage>7</lpage>. Epub 2015/05/16. <pub-id pub-id-type="doi">10.1038/leu.2015.95</pub-id> .<?supplied-pmid 25976987?><pub-id pub-id-type="pmid">25976987</pub-id></mixed-citation></ref><ref id="pone.0212669.ref052"><label>52</label><mixed-citation publication-type="journal"><name><surname>Higuchi</surname><given-names>Y</given-names></name>, <name><surname>Kojima</surname><given-names>M</given-names></name>, <name><surname>Ishii</surname><given-names>G</given-names></name>, <name><surname>Aoyagi</surname><given-names>K</given-names></name>, <name><surname>Sasaki</surname><given-names>H</given-names></name>, <name><surname>Ochiai</surname><given-names>A</given-names></name>. <article-title>Gastrointestinal Fibroblasts Have Specialized, Diverse Transcriptional Phenotypes: A Comprehensive Gene Expression Analysis of Human Fibroblasts</article-title>. <source>PLoS One</source>. <year>2015</year>;<volume>10</volume>(<issue>6</issue>):<fpage>e0129241</fpage> Epub 2015/06/06. <pub-id pub-id-type="doi">10.1371/journal.pone.0129241</pub-id>
<?supplied-pmid 26046848?><pub-id pub-id-type="pmid">26046848</pub-id></mixed-citation></ref><ref id="pone.0212669.ref053"><label>53</label><mixed-citation publication-type="journal"><name><surname>Pajtler</surname><given-names>KW</given-names></name>, <name><surname>Witt</surname><given-names>H</given-names></name>, <name><surname>Sill</surname><given-names>M</given-names></name>, <name><surname>Jones</surname><given-names>DT</given-names></name>, <name><surname>Hovestadt</surname><given-names>V</given-names></name>, <name><surname>Kratochwil</surname><given-names>F</given-names></name>, <etal>et al</etal>
<article-title>Molecular Classification of Ependymal Tumors across All CNS Compartments, Histopathological Grades, and Age Groups</article-title>. <source>Cancer cell</source>. <year>2015</year>;<volume>27</volume>(<issue>5</issue>):<fpage>728</fpage>&#x02013;<lpage>43</lpage>. Epub 2015/05/13. <pub-id pub-id-type="doi">10.1016/j.ccell.2015.04.002</pub-id>
<?supplied-pmid 25965575?><pub-id pub-id-type="pmid">25965575</pub-id></mixed-citation></ref><ref id="pone.0212669.ref054"><label>54</label><mixed-citation publication-type="journal"><name><surname>Wang</surname><given-names>L</given-names></name>, <name><surname>Shen</surname><given-names>X</given-names></name>, <name><surname>Wang</surname><given-names>Z</given-names></name>, <name><surname>Xiao</surname><given-names>X</given-names></name>, <name><surname>Wei</surname><given-names>P</given-names></name>, <name><surname>Wang</surname><given-names>Q</given-names></name>, <etal>et al</etal>
<article-title>A molecular signature for the prediction of recurrence in colorectal cancer</article-title>. <source>Molecular cancer</source>. <year>2015</year>;<volume>14</volume>:<fpage>22</fpage> Epub 2015/02/04. <pub-id pub-id-type="doi">10.1186/s12943-015-0296-2</pub-id>
<?supplied-pmid 25645394?><pub-id pub-id-type="pmid">25645394</pub-id></mixed-citation></ref><ref id="pone.0212669.ref055"><label>55</label><mixed-citation publication-type="journal"><name><surname>Ho</surname><given-names>DM</given-names></name>, <name><surname>Shih</surname><given-names>CC</given-names></name>, <name><surname>Liang</surname><given-names>ML</given-names></name>, <name><surname>Tsai</surname><given-names>CY</given-names></name>, <name><surname>Hsieh</surname><given-names>TH</given-names></name>, <name><surname>Tsai</surname><given-names>CH</given-names></name>, <etal>et al</etal>
<article-title>Integrated genomics has identified a new AT/RT-like yet INI1-positive brain tumor subtype among primary pediatric embryonal tumors</article-title>. <source>BMC medical genomics</source>. <year>2015</year>;<volume>8</volume>:<fpage>32</fpage> Epub 2015/06/26. <pub-id pub-id-type="doi">10.1186/s12920-015-0103-3</pub-id>
<?supplied-pmid 26109171?><pub-id pub-id-type="pmid">26109171</pub-id></mixed-citation></ref><ref id="pone.0212669.ref056"><label>56</label><mixed-citation publication-type="journal"><name><surname>Kang</surname><given-names>H</given-names></name>, <name><surname>Wilson</surname><given-names>CS</given-names></name>, <name><surname>Harvey</surname><given-names>RC</given-names></name>, <name><surname>Chen</surname><given-names>IM</given-names></name>, <name><surname>Murphy</surname><given-names>MH</given-names></name>, <name><surname>Atlas</surname><given-names>SR</given-names></name>, <etal>et al</etal>
<article-title>Gene expression profiles predictive of outcome and age in infant acute lymphoblastic leukemia: a Children's Oncology Group study</article-title>. <source>Blood</source>. <year>2012</year>;<volume>119</volume>(<issue>8</issue>):<fpage>1872</fpage>&#x02013;<lpage>81</lpage>. Epub 2012/01/03. <pub-id pub-id-type="doi">10.1182/blood-2011-10-382861</pub-id>
<?supplied-pmid 22210879?><pub-id pub-id-type="pmid">22210879</pub-id></mixed-citation></ref><ref id="pone.0212669.ref057"><label>57</label><mixed-citation publication-type="journal"><name><surname>Phipson</surname><given-names>B</given-names></name>, <name><surname>Lee</surname><given-names>S</given-names></name>, <name><surname>Majewski</surname><given-names>IJ</given-names></name>, <name><surname>Alexander</surname><given-names>WS</given-names></name>, <name><surname>Smyth</surname><given-names>GK</given-names></name>. <article-title>ROBUST HYPERPARAMETER ESTIMATION PROTECTS AGAINST HYPERVARIABLE GENES AND IMPROVES POWER TO DETECT DIFFERENTIAL EXPRESSION</article-title>. <source>The annals of applied statistics</source>. <year>2016</year>;<volume>10</volume>(<issue>2</issue>):<fpage>946</fpage>&#x02013;<lpage>63</lpage>. Epub 2017/04/04. <pub-id pub-id-type="doi">10.1214/16-AOAS920</pub-id>
<?supplied-pmid 28367255?><pub-id pub-id-type="pmid">28367255</pub-id></mixed-citation></ref><ref id="pone.0212669.ref058"><label>58</label><mixed-citation publication-type="journal"><name><surname>Ritchie</surname><given-names>ME</given-names></name>, <name><surname>Phipson</surname><given-names>B</given-names></name>, <name><surname>Wu</surname><given-names>D</given-names></name>, <name><surname>Hu</surname><given-names>Y</given-names></name>, <name><surname>Law</surname><given-names>CW</given-names></name>, <name><surname>Shi</surname><given-names>W</given-names></name>, <etal>et al</etal>
<article-title>limma powers differential expression analyses for RNA-sequencing and microarray studies</article-title>. <source>Nucleic acids research</source>. <year>2015</year>;<volume>43</volume>(<issue>7</issue>):<fpage>e47</fpage> Epub 2015/01/22. <pub-id pub-id-type="doi">10.1093/nar/gkv007</pub-id> ; PubMed Central PMCID: PMCPmc4402510.<?supplied-pmid 25605792?><pub-id pub-id-type="pmid">25605792</pub-id></mixed-citation></ref></ref-list></back></article>