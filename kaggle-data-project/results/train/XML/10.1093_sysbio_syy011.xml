<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.1 20151215//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1.dtd?><?SourceDTD.Version 1.1?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Syst Biol</journal-id><journal-id journal-id-type="iso-abbrev">Syst. Biol</journal-id><journal-id journal-id-type="publisher-id">sysbio</journal-id><journal-title-group><journal-title>Systematic Biology</journal-title></journal-title-group><issn pub-type="ppub">1063-5157</issn><issn pub-type="epub">1076-836X</issn><publisher><publisher-name>Oxford University Press</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6101526</article-id><article-id pub-id-type="doi">10.1093/sysbio/syy011</article-id><article-id pub-id-type="publisher-id">syy011</article-id><article-categories><subj-group subj-group-type="heading"><subject>Regular Articles</subject></subj-group></article-categories><title-group><article-title>Comparison of Methods for Molecular Species Delimitation Across a Range of Speciation Scenarios</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Luo</surname><given-names>Arong</given-names></name><xref ref-type="aff" rid="AFF1">1</xref><xref ref-type="aff" rid="AFF2">2</xref><xref ref-type="corresp" rid="COR1"/><!--<email><EMAIL></email>--></contrib><contrib contrib-type="author"><name><surname>Ling</surname><given-names>Cheng</given-names></name><xref ref-type="aff" rid="AFF3">3</xref></contrib><contrib contrib-type="author"><name><surname>Ho</surname><given-names>Simon Y W</given-names></name><xref ref-type="aff" rid="AFF2">2</xref></contrib><contrib contrib-type="author"><name><surname>Zhu</surname><given-names>Chao-Dong</given-names></name><xref ref-type="aff" rid="AFF1">1</xref><xref ref-type="aff" rid="AFF4">4</xref></contrib></contrib-group><contrib-group><contrib contrib-type="editor"><name><surname>Mueller</surname><given-names>Rachel</given-names></name><role>Associate Editor</role></contrib></contrib-group><aff id="AFF1"><label>1</label>Key Laboratory of Zoological Systematics and Evolution, Institute of Zoology, Chinese Academy of Sciences, Beijing 100101, China</aff><aff id="AFF2"><label>2</label>School of Life and Environmental Sciences, University of Sydney, Sydney, New South Wales 2006, Australia</aff><aff id="AFF3"><label>3</label>Department of Computer Science and Technology, College of Information Science and Technology, Beijing University of Chemical Technology, Beijing 100029, China</aff><aff id="AFF4"><label>4</label>College of Life Sciences, University of Chinese Academy of Sciences, Beijing 100049, China</aff><author-notes><corresp id="COR1">Correspondence to be sent to: Key Laboratory of Zoological Systematics and Evolution, Institute of Zoology, Chinese Academy of Sciences, Beijing 100101, China; E-mail: <email><EMAIL></email></corresp><fn id="syy011-FM2"><p>Simon Y. W. Ho and Chao-Dong Zhu contributed equally to this article</p></fn></author-notes><pub-date pub-type="ppub"><month>9</month><year>2018</year></pub-date><pub-date pub-type="epub" iso-8601-date="2018-02-15"><day>15</day><month>2</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>15</day><month>2</month><year>2018</year></pub-date><!-- PMC Release delay is 0 months and 0 days and was based on the <pub-date pub-type="epub"/>. --><volume>67</volume><issue>5</issue><fpage>830</fpage><lpage>846</lpage><history><date date-type="received"><day>25</day><month>6</month><year>2017</year></date><date date-type="rev-recd"><day>31</day><month>8</month><year>2017</year></date><date date-type="accepted"><day>10</day><month>2</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2018. Published by Oxford University Press, on behalf of the Society of Systematic Biologists.</copyright-statement><copyright-year>2018</copyright-year><license license-type="cc-by-nc" xlink:href="http://creativecommons.org/licenses/by-nc/4.0/"><license-p>This is an Open Access article distributed under the terms of the Creative Commons Attribution Non-Commercial License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by-nc/4.0/">http://creativecommons.org/licenses/by-nc/4.0/</ext-link>), which permits non-commercial reuse, distribution, and reproduction in any medium, provided the original work is properly cited. For Permissions, please email: <email><EMAIL></email></license-p></license></permissions><self-uri xlink:href="syy011.pdf"/><abstract><title>Abstract</title><p>Species are fundamental units in biological research and can be defined on the basis of various operational criteria. There has been growing use of molecular approaches for species delimitation. Among the most widely used methods, the generalized mixed Yule-coalescent (GMYC) and Poisson tree processes (PTP) were designed for the analysis of single-locus data but are often applied to concatenations of multilocus data. In contrast, the Bayesian multispecies coalescent approach in the software Bayesian Phylogenetics and Phylogeography (BPP) explicitly models the evolution of multilocus data. In this study, we compare the performance of GMYC, PTP, and BPP using synthetic data generated by simulation under various speciation scenarios. We show that in the absence of gene flow, the main factor influencing the performance of these methods is the ratio of population size to divergence time, while number of loci and sample size per species have smaller effects. Given appropriate priors and correct guide trees, BPP shows lower rates of species overestimation and underestimation, and is generally robust to various potential confounding factors except high levels of gene flow. The single-threshold GMYC and the best strategy that we identified in PTP generally perform well for scenarios involving more than a single putative species when gene flow is absent, but PTP outperforms GMYC when fewer species are involved. Both methods are more sensitive than BPP to the effects of gene flow and potential confounding factors. Case studies of bears and bees further validate some of the findings from our simulation study, and reveal the importance of using an informed starting point for molecular species delimitation. Our results highlight the key factors affecting the performance of molecular species delimitation, with potential benefits for using these methods within an integrative taxonomic framework.</p></abstract><kwd-group kwd-group-type="author"><kwd>Molecular species delimitation</kwd><kwd>speciation</kwd><kwd>multispecies coalescent</kwd><kwd>simulation</kwd><kwd>generalized mixed Yule-coalescent</kwd><kwd>Poisson tree processes</kwd><kwd>Bayesian phylogenetics</kwd></kwd-group><funding-group><award-group award-type="grant"><funding-source><named-content content-type="funder-name">National Natural Science Foundation of China</named-content><named-content content-type="funder-identifier">10.13039/501100001809</named-content></funding-source><award-id>31201701</award-id></award-group><award-group award-type="grant"><funding-source><named-content content-type="funder-name">Youth Innovation Promotion Association of the Chinese Academy of Sciences</named-content><named-content content-type="funder-identifier">10.13039/501100004739</named-content></funding-source><award-id>2017118</award-id></award-group></funding-group><counts><page-count count="17"/></counts></article-meta></front><body><p>Species identification is critical to a wide range of biological research, including studies of evolution, conservation, and biodiversity. However, various operational criteria are used for species identification, depending on the species concept that is being invoked (<xref rid="B14" ref-type="bibr">de Queiroz 2007</xref>). Among the most widely used are the biological species concept, which is based on reproductive isolation (<xref rid="B48" ref-type="bibr">Mayr 1942</xref>; <xref rid="B18" ref-type="bibr">Dobzhansky 1950</xref>), and the phylogenetic species concept, which is based on reciprocal monophyly (<xref rid="B66" ref-type="bibr">Rosen 1979</xref>; <xref rid="B4" ref-type="bibr">Baum and Shaw 1995</xref>). In contrast, morphology-based taxonomy usually appeals to the phenetic species concept (<xref rid="B49" ref-type="bibr">Michener 1970</xref>; <xref rid="B72" ref-type="bibr">Sokal and Crovello 1970</xref>), which remains a key framework for species identification in practice.</p><p>The last decade has witnessed the growing availability of genetic methods for species identification, providing a valuable complement to morphological taxonomy. Some of the widely used approaches for validating putative species are based on comparison of intra- and interspecific genetic distances (<xref rid="B29" ref-type="bibr">Hebert et al. 2003</xref>, <xref rid="B30" ref-type="bibr">2004</xref>; <xref rid="B45" ref-type="bibr">Mallo and Posada 2016</xref>). These methods are contentious, however, partly because they do not appeal to an explicit species concept (<xref rid="B69" ref-type="bibr">Rubinoff et al. 2006a</xref>, <xref rid="B70" ref-type="bibr">2006b</xref>; <xref rid="B81" ref-type="bibr">Waugh 2007</xref>). By contrast, the goal of molecular species delimitation is to build a taxonomic scheme for a set of samples and to infer a <italic>de novo</italic> delimitation of operational taxonomic units (OTUs) (<xref rid="B78" ref-type="bibr">Tautz et al. 2003</xref>; <xref rid="B79" ref-type="bibr">Vogler and Monaghan 2007</xref>; <xref rid="B45" ref-type="bibr">Mallo and Posada 2016</xref>). Within this burgeoning field, most methods appeal to the phylogenetic species concept and identify minimal phylogenetic units as the OTUs (<xref rid="B25" ref-type="bibr">Goldstein et al. 2000</xref>). These methods include the generalized mixed Yule-coalescent (GMYC) model (<xref rid="B57" ref-type="bibr">Pons et al. 2006</xref>; <xref rid="B23" ref-type="bibr">Fujisawa and Barraclough 2013</xref>), Poisson tree processes (PTP) model (<xref rid="B88" ref-type="bibr">Zhang et al. 2013</xref>; <xref rid="B39" ref-type="bibr">Kapli et al. 2017</xref>), Bayes factor delimitation (<xref rid="B26" ref-type="bibr">Grummer et al. 2014</xref>; <xref rid="B42" ref-type="bibr">Leach&#x000e9; et al. 2014</xref>), Bayesian coalescent method in the software Bayesian Phylogenetics and Phylogeography (BPP) (<xref rid="B83" ref-type="bibr">Yang 2015</xref>), and phylogeographic inference using approximate likelihoods (<xref rid="B37" ref-type="bibr">Jackson et al. 2017</xref>). Molecular species delimitation has been employed either as a stand-alone method or as part of an integrative taxonomic approach to species identification (e.g., <xref rid="B5" ref-type="bibr">Bond and Stockman 2008</xref>; <xref rid="B35" ref-type="bibr">Hotaling et al. 2016</xref>; <xref rid="B47" ref-type="bibr">Mason et al. 2016</xref>).</p><p>Methods of molecular species delimitation differ from each other in a number of respects. Among the widely used methods, Automatic Barcode Gap Discovery (ABGD) is one of the most computationally efficient. It requires the <italic>a priori</italic> specification of an intraspecific distance threshold, and the method is based on genetic distances computed from a single locus rather than an explicit species concept (<xref rid="B60" ref-type="bibr">Puillandre et al. 2012</xref>). The GMYC method also analyzes data from a single locus, but requires an ultrametric estimate of the gene tree. Studies have found that its performance is affected primarily by the ratio of population sizes to species divergence times, but also by varying population sizes, number of species involved, and number of sampling singletons (<xref rid="B23" ref-type="bibr">Fujisawa and Barraclough 2013</xref>; <xref rid="B17" ref-type="bibr">Dellicour and Flot 2015</xref>; <xref rid="B1" ref-type="bibr">Ahrens et al. 2016</xref>). Empirical studies have shown that ABGD and GMYC tend to under- and oversplit species, respectively (e.g., <xref rid="B55" ref-type="bibr">Paz and Crawford 2012</xref>; <xref rid="B56" ref-type="bibr">Pentinsaari et al. 2017</xref>; <xref rid="B65" ref-type="bibr">Renner et al. 2017</xref>). As with GMYC, PTP requires an estimate of the gene tree, but with branch lengths proportional to the amount of genetic change rather than to time. It tends to outperform GMYC when interspecific distances are small (<xref rid="B88" ref-type="bibr">Zhang et al. 2013</xref>), though the two methods often produce similar estimates of species limits (e.g., <xref rid="B40" ref-type="bibr">Lang et al. 2015</xref>; <xref rid="B2" ref-type="bibr">Arrigoni et al. 2016</xref>; <xref rid="B80" ref-type="bibr">Wang et al. 2016</xref>). GMYC and PTP were originally designed for the analysis of single-locus data, but are often applied to concatenated multilocus data by postulating a shared genealogical history (e.g., <xref rid="B2" ref-type="bibr">Arrigoni et al. 2016</xref>; <xref rid="B52" ref-type="bibr">Nieto-Montes de Oca et al. 2017</xref>; <xref rid="B65" ref-type="bibr">Renner et al. 2017</xref>).</p><p>In contrast with the methods described above, the Bayesian method in BPP was designed to analyze multiple loci but is much more computationally intensive (<xref rid="B83" ref-type="bibr">Yang 2015</xref>). It performs well when appropriate priors are chosen, with low rates of false positives and false negatives under most evolutionary scenarios (<xref rid="B84" ref-type="bibr">Yang and Rannala 2010</xref>, <xref rid="B85" ref-type="bibr">2014</xref>; <xref rid="B89" ref-type="bibr">Zhang et al. 2011</xref>, <xref rid="B88" ref-type="bibr">2014</xref>). Although the biological species concept provides the motivation for assuming limited gene flow between species, BPP appears to be robust to low levels of gene flow (<xref rid="B89" ref-type="bibr">Zhang et al. 2011</xref>). Studies with both simulated and empirical data have shown that BPP is more accurate than other multilocus coalescent methods (such as the information-theoretic and approximate Bayesian frameworks), while being somewhat sensitive to the number of loci and to the information content (<xref rid="B20" ref-type="bibr">Ence and Carstens 2011</xref>; <xref rid="B7" ref-type="bibr">Camargo et al. 2012</xref>; <xref rid="B33" ref-type="bibr">Hime et al. 2016</xref>). Empirical studies have also shown that BPP can produce delimitations that are consistent with those from other widely used methods such as GMYC and PTP (e.g., <xref rid="B34" ref-type="bibr">Hoppeler et al. 2016</xref>; <xref rid="B59" ref-type="bibr">Previ&#x00161;i&#x00107; et al. 2016</xref>; <xref rid="B52" ref-type="bibr">Nieto-Montes de Oca et al. 2017</xref>). Species delimitations by BPP are used widely not only for explicit taxon identification, but also as an important procedure in analyses of taxon evolution and divergence (e.g., <xref rid="B68" ref-type="bibr">Ruane et al. 2014</xref>; <xref rid="B51" ref-type="bibr">Moritz et al. 2018</xref>).</p><p>Most genetic methods for species identification, especially those based on the phylogenetic species concept, do not explicitly account for the mode of speciation. There are three main modes of speciation that differ in terms of the assumed degree of gene flow: allopatric, parapatric, and sympatric speciation (<xref rid="B24" ref-type="bibr">Gavrilets 2003</xref>). In each case, the formation of incipient species is related to a reduction in gene flow, which is at the core of the biological species concept. Some view speciation as a gradual and protracted process independent of any species concept (<xref rid="B67" ref-type="bibr">Rosindell et al. 2010</xref>; <xref rid="B21" ref-type="bibr">Etienne et al. 2014</xref>). For these reasons, there appears to be a gap between what we would consider to be &#x0201c;good&#x0201d; species and the taxonomic units inferred by coalescent-based methods of molecular species delimitation. This can be addressed by examining the congruence between genetic divergence and speciation. Some methods, such as GMYC and PTP, assume that gene trees accurately reflect the diversification of species, whereas BPP acknowledges the possibility of discordance between the two (<xref rid="B84" ref-type="bibr">Yang and Rannala 2010</xref>). This discordance is presumed to be caused primarily by among-gene differences in lineage sorting, but it can also be due to systematic error (e.g., model misspecification) and stochastic error (e.g., sampling scheme) (<xref rid="B44" ref-type="bibr">Mallo et al. 2014</xref>; <xref rid="B45" ref-type="bibr">Mallo and Posada 2016</xref>).</p><p>In this study, we compare the performance of three widely used methods of species delimitation, GMYC, PTP, and BPP, using both single-locus and multilocus sequence data generated by simulation under various speciation scenarios. We characterize the behavior of these methods, their delimitation efficacy, and their sensitivity to potential confounding factors. In addition, we validate some of the features of these methods in case studies involving sequence data from bears and bees. Our results provide practical guidelines for using molecular methods of species delimitation.</p><sec sec-type="materials|methods" id="SEC1"><title>Materials and Methods</title><sec id="SEC1.1"><title>Models and Assumptions</title><p>To examine the performance of species delimitation using GMYC, PTP, and BPP, we simulated the evolution of sequence data under five speciation scenarios (<xref ref-type="fig" rid="F1">Fig. 1</xref>): 1) no speciation; 2) speciation into two species with cessation of gene flow; 3) speciation into two species with ongoing gene flow; 4) speciation into five species with cessation of gene flow; and 5) speciation into four species with ongoing gene flow. In each case, we assumed Wright&#x02013;Fisher panmixia within each species. Scenario I is treated as the null case in this study. Scenario II can represent either allopatric or peripatric speciation, depending on the combination of population sizes between the two species. Scenario III involves reduced but ongoing gene flow, so it can be taken to represent either parapatric or sympatric speciation. Scenarios IV and V are extensions of Scenarios II and III, respectively; we chose to model the evolution of five and four species in these scenarios, to allow variation in the tree topology and for practical convenience. In these scenarios, speciation can also be regarded as the formation of separate populations, and migration as being interchangeable with other forms of gene flow (e.g., introgression). We assumed that all speciation events were bifurcating, and that all genes evolved neutrally without gene conversion, gene duplication, or horizontal transfer.</p><fig id="F1" orientation="portrait" position="float"><label>Figure 1.</label><caption><p>Five speciation models used for simulations in this study. a) Scenario I: a single species without population structure. b) Scenario II: speciation into two species, with cessation of gene flow. c) Scenario III: speciation into two species, with ongoing gene flow indicated by arrows. d) Scenario IV: speciation into five species, with cessation of gene flow. e) Scenario V: speciation into four species, with ongoing gene flow between adjacent species indicated by arrows.</p></caption><graphic xlink:href="syy011f1"/></fig></sec><sec id="SEC1.2"><title>Evolutionary Simulations</title><p>Nucleotide sequence evolution was simulated under each of the five scenarios (<xref rid="T1" ref-type="table">Table 1</xref>), with assumptions of a constant rate of 10<inline-formula><tex-math id="M1">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{-8}}$\end{document}</tex-math></inline-formula> mutations per site per generation and a generation time of 1 year. Owing to its convenience and versatility, we preferred to use SimPhy (<xref rid="B45" ref-type="bibr">Mallo et al. 2016</xref>) to generate the trees, where possible. The species tree was simulated first, and then we simulated the evolution of 100 independent gene trees conditioned on the species tree. For simulations that could not be performed using SimPhy, we used makesamples (<xref rid="B36" ref-type="bibr">Hudson 2002</xref>) to simulate the evolution of 100 gene trees based on each species tree that we specified.</p><p>
<table-wrap id="T1" orientation="portrait" position="float"><label>Table 1.</label><caption><p>Genealogical simulation software and parameter settings for Scenarios I&#x02013;V</p></caption><table frame="hsides" rules="groups"><thead align="left"><tr><th align="left" rowspan="1" colspan="1">Scenario</th><th align="center" rowspan="1" colspan="1">Software</th><th align="center" rowspan="1" colspan="1">Population size <inline-formula><tex-math id="M2">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula></th><th align="center" rowspan="1" colspan="1">Crown age <inline-formula><tex-math id="M3">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> (years)</th><th align="center" rowspan="1" colspan="1">Number of loci <inline-formula><tex-math id="M4">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula></th><th align="center" rowspan="1" colspan="1">Sample size <inline-formula><tex-math id="M5">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula></th><th align="center" rowspan="1" colspan="1">Migration rate <inline-formula><tex-math id="M6">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij}$\end{document}</tex-math></inline-formula> (individuals per generation)</th><th align="center" rowspan="1" colspan="1">Speciation rate <inline-formula><tex-math id="M7">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r$\end{document}</tex-math></inline-formula> (speciation events per generation)</th></tr></thead><tbody><tr><td align="left" rowspan="1" colspan="1">I</td><td align="center" rowspan="1" colspan="1">makesamples</td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M8">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M9">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M10">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">1, 2, 5, 10, 20</td><td align="center" rowspan="1" colspan="1">2, 4, 10, 20, 40</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">II</td><td align="center" rowspan="1" colspan="1">SimPhy</td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M11">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M12">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M13">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M14">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M15">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M16">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">1, 2, 5, 10, 20</td><td align="center" rowspan="1" colspan="1">1, 2, 5, 10, 20</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">III</td><td align="center" rowspan="1" colspan="1">makesamples</td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M17">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{4}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M18">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">1, 2, 5, 10, 20</td><td align="center" rowspan="1" colspan="1">10</td><td align="center" rowspan="1" colspan="1">0.1, 1, 10, 10<inline-formula><tex-math id="M19">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{2}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M20">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{3}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">IV</td><td align="center" rowspan="1" colspan="1">SimPhy</td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M21">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{4}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M22">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">1, 2, 5, 10, 20</td><td align="center" rowspan="1" colspan="1">10</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M23">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{-5}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M24">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{-6}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M25">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{-7}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M26">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{-8}$\end{document}</tex-math></inline-formula></td></tr><tr><td align="left" rowspan="1" colspan="1">V</td><td align="center" rowspan="1" colspan="1">makesamples</td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M27">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{4}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">10<inline-formula><tex-math id="M28">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">1, 2, 5, 10, 20</td><td align="center" rowspan="1" colspan="1">10</td><td align="center" rowspan="1" colspan="1">0.1, 1, 10, 10<inline-formula><tex-math id="M29">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{2}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M30">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{3}}$\end{document}</tex-math></inline-formula></td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr></tbody></table></table-wrap>
</p><p>Under each set of simulation conditions (see below; <xref rid="T1" ref-type="table">Table 1</xref>), we randomly subsampled varying numbers of gene trees (<inline-formula><tex-math id="M31">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 1$\end{document}</tex-math></inline-formula>, 2, 5, 10, and 20) from the 100 generated by SimPhy or makesamples. These correspond to varying numbers of loci, because each gene tree corresponds to the evolutionary history of a single locus. We performed the jackknifing procedure 10 times for each number of loci. For each sampled gene tree, we then used Seq-Gen v1.3.2 (<xref rid="B62" ref-type="bibr">Rambaut and Grassly 1997</xref>) to simulate the evolution of a sequence alignment of length 1000 bp, using the Jukes&#x02013;Cantor model of nucleotide substitution (<xref rid="B38" ref-type="bibr">Jukes and Cantor 1969</xref>). An outgroup sequence was added to the sequence alignment for each of the five scenarios during simulation, but was removed for the species-delimitation analyses. All data generated by our simulations are available in <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Material</ext-link> of this article available on Dryad at <ext-link ext-link-type="uri" xlink:href="http://dx.doi.org/10.5061/dryad.739bs">http://dx.doi.org/10.5061/dryad.739bs</ext-link>, Appendix S1.</p><sec id="SEC1.2.1"><title>Scenario I</title><p>We began simulations with the null scenario of a single, unstructured population or species (<xref ref-type="fig" rid="F1">Fig. 1a</xref>), under the classical Wright&#x02013;Fisher model in makesamples. We set 15 combinations of sample sizes (i.e., the number of sampled individuals per species) and population sizes. Sample sizes (<inline-formula><tex-math id="M32">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 2$\end{document}</tex-math></inline-formula>, 4, 10, 20, and 40) were double those used in Scenario II (as appropriate for some methods of species delimitation), whereas population sizes (<inline-formula><tex-math id="M33">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M34">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}}$\end{document}</tex-math></inline-formula>, and 10<inline-formula><tex-math id="M35">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}})$\end{document}</tex-math></inline-formula> were consistent with the general settings used in Scenario II. After producing 10 replicates for each number of loci (<inline-formula><tex-math id="M36">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 1$\end{document}</tex-math></inline-formula>, 2, 5, 10, and 20), we had a total of 750 datasets.</p></sec><sec id="SEC1.2.2"><title>Scenario II</title><p>This scenario involves two reproductively isolated species with equal population sizes and with equal numbers of sampled individuals (<xref ref-type="fig" rid="F1">Fig. 1b</xref>). In SimPhy, we chose: <inline-formula><tex-math id="M37">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 1$\end{document}</tex-math></inline-formula>, 2, 5, 10, and 20 samples per species (as appropriate for the relevant delimitation methods; <xref rid="B43" ref-type="bibr">Luo et al. 2015</xref>); population sizes of <inline-formula><tex-math id="M38">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M39">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}}$\end{document}</tex-math></inline-formula>, and 10<inline-formula><tex-math id="M40">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula> for each species; and divergence times of <inline-formula><tex-math id="M41">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{7}}, 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula>, and 10<inline-formula><tex-math id="M42">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}}$\end{document}</tex-math></inline-formula> years between the two species. These yielded a total of 45 combinations of parameters. Larger population sizes and divergence times lead to greater genetic variation within species and between species, respectively; the ranges of values investigated in our study are generally consistent with the features of a broad range of eukaryotic species (<xref rid="B89" ref-type="bibr">Zhang et al. 2011</xref>; <xref rid="B23" ref-type="bibr">Fujisawa and Barraclough 2013</xref>). Taking into account the variation in the number of loci, our simulations produced a total of 2250 datasets. Based on the results from our analyses of these datasets, we chose the basic settings for the remaining simulations (including supplementary settings for BPP and variations of Scenario II). These results, together with those from Scenario I, provided benchmarks for interpreting the results from the other Scenarios and also informed the best strategy in PTP for analyzing our remaining data.</p><p>For species delimitation using BPP in particular, supplementary settings included: extreme population sizes for each species (<inline-formula><tex-math id="M43">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{3}}$\end{document}</tex-math></inline-formula> and 10<inline-formula><tex-math id="M44">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}})$\end{document}</tex-math></inline-formula>, with species divergence time <inline-formula><tex-math id="M45">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M46">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 1$\end{document}</tex-math></inline-formula>, 2, 5, 10, and 20 samples per species; and larger sample size <inline-formula><tex-math id="M47">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 50$\end{document}</tex-math></inline-formula> with <inline-formula><tex-math id="M48">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M49">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{5}}$\end{document}</tex-math></inline-formula>. Unless otherwise noted, the simulations for Scenario II described below exclude the supplementary settings used for BPP.</p><p>In addition, we considered a series of variations of Scenario II, involving potential confounding factors that might influence species delimitations inferred by the three methods. These included: simulating sequence evolution using makesamples vs. SimPhy for the core settings in Scenario II, to evaluate the consistency of our methods; differing vs. equal population sizes for the two species; exponentially growing vs. constant-size populations; uneven sampling vs. equal sample sizes from the two species; and substitution rate heterogeneity across species, across loci, or across lineages (see <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S2 available on Dryad for details).</p></sec><sec id="SEC1.2.3"><title>Scenario III</title><p>This scenario involves two sister species with ongoing gene flow (<xref ref-type="fig" rid="F1">Fig. 1c</xref>). Gene flow is specified by the migration rate <inline-formula><tex-math id="M50">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij}$\end{document}</tex-math></inline-formula>. In makesamples, <inline-formula><tex-math id="M51">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 4N_{i}m_{ij}$\end{document}</tex-math></inline-formula> (<inline-formula><tex-math id="M52">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$i$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M53">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$j  = 1, \ldots,$\end{document}</tex-math></inline-formula> number of populations), where <inline-formula><tex-math id="M54">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$m_{ij}$\end{document}</tex-math></inline-formula> is the fraction of population <inline-formula><tex-math id="M55">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$i$\end{document}</tex-math></inline-formula> that is made up of migrants from population <inline-formula><tex-math id="M56">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$j$\end{document}</tex-math></inline-formula> each generation, and <inline-formula><tex-math id="M57">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N_{i}$\end{document}</tex-math></inline-formula> is the size of population <inline-formula><tex-math id="M58">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$i$\end{document}</tex-math></inline-formula>. To test the effect of ongoing gene flow on species delimitation, we set <inline-formula><tex-math id="M59">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 0.1$\end{document}</tex-math></inline-formula>, 1, 10, 10<inline-formula><tex-math id="M60">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{2}}$\end{document}</tex-math></inline-formula>, and 10<inline-formula><tex-math id="M61">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{3}}$\end{document}</tex-math></inline-formula>, and assumed equal amounts of reciprocal gene flow. In light of the results from Scenario II and general applicability to most empirical studies, we set the population size of each species to <inline-formula><tex-math id="M62">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, the divergence time of the two species to <inline-formula><tex-math id="M63">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula>, and the sample size per species to <inline-formula><tex-math id="M64">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 10$\end{document}</tex-math></inline-formula>. These settings were combined with variation in the number of loci, as described above for Scenarios I and II.</p></sec><sec id="SEC1.2.4"><title>Scenario IV</title><p>We examined a five-species case in which speciation followed a Yule process (i.e., birth&#x02013;death process with extinction rate zero; <xref rid="B87" ref-type="bibr">Yule 1924</xref>) (<xref ref-type="fig" rid="F1">Fig. 1d</xref>), and considered a range of speciation rates (<inline-formula><tex-math id="M65">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r = 10^{\mathrm{-5}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M66">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{-6}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M67">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{-7}}$\end{document}</tex-math></inline-formula>, and 10<inline-formula><tex-math id="M68">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{-8 }}$\end{document}</tex-math></inline-formula> speciation events per year) for five species with the relationship ((A,B),(D,(C,E))). The most recent common ancestor of these species was set to 10<inline-formula><tex-math id="M69">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}}$\end{document}</tex-math></inline-formula> years before present, with each species having a population size <inline-formula><tex-math id="M70">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula> and with <inline-formula><tex-math id="M71">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 10$\end{document}</tex-math></inline-formula> samples per species. We used SimPhy to simulate speciation with a pure-birth process, conditioned on the crown age and the number of extant species (<xref rid="B28" ref-type="bibr">Hartmann et al. 2010</xref>). Under these constraints, higher speciation rates (e.g., <inline-formula><tex-math id="M72">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r = 10^{\mathrm{-5}})$\end{document}</tex-math></inline-formula> tended to produce trees with internal nodes closer to the tips (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S3 available on Dryad). Based on these settings, we performed simulations with various numbers of loci.</p></sec><sec id="SEC1.2.5"><title>Scenario V</title><p>We examined a four-species case in which gene flow is conditioned on the geographic proximity of the species (<xref ref-type="fig" rid="F1">Fig. 1e</xref>). The species have the relationship ((A,B),(C,D)), and gene flow occurs reciprocally between A and B, between B and C, and between C and D. Rates of gene flow match those in Scenario III. In makesamples, we assumed that divergences between sister species A and B and between C and D occurred 10<inline-formula><tex-math id="M73">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula> years ago, and that the four species had their most recent common ancestor 10<inline-formula><tex-math id="M74">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}}$\end{document}</tex-math></inline-formula> years ago. Each species was assumed to have a population size of <inline-formula><tex-math id="M75">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, with <inline-formula><tex-math id="M76">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 10$\end{document}</tex-math></inline-formula> samples per species. Under each set of conditions, we performed simulations with various numbers of loci. In addition to the complete datasets, sequences of species pairs (A, B), (A, C), (A, D), and (B, C) were extracted separately so that we could explicitly test the effect of geographic distance on species delimitation.</p></sec></sec><sec id="SEC1.3"><title>Species Delimitation</title><p>We performed species delimitation using three different methods: GMYC, PTP, and BPP. With the assumption that species are reciprocally monophyletic, GMYC uses a likelihood approach to identify the boundary between a Yule speciation process and intraspecific coalescence. This is done with reference to the relative node times in an ultrametric tree. To obtain these trees, we first inferred phylograms with maximum likelihood in RAxML v8.2.9 (<xref rid="B73" ref-type="bibr">Stamatakis 2014</xref>). We pruned non-unique sequences prior to phylogenetic inference, and conducted rapid full analyses with the Jukes&#x02013;Cantor substitution model and 1000 bootstrap replicates. The phylograms were then made ultrametric through computing relative evolutionary times using the penalized-likelihood method in r8s v1.7 (<xref rid="B71" ref-type="bibr">Sanderson 2003</xref>), with a smoothing parameter of 10. Although a multiple-threshold version of GMYC has been developed (<xref rid="B50" ref-type="bibr">Monaghan et al. 2009</xref>), we only used the single-threshold version of GMYC because it has been shown to outperform the multiple-threshold version (<xref rid="B23" ref-type="bibr">Fujisawa and Barraclough 2013</xref>; <xref rid="B76" ref-type="bibr">Talavera et al. 2013</xref>). Species delimitation was done using the package splits v1.0-19 (<xref rid="B22" ref-type="bibr">Ezard et al. 2009</xref>) in R (<xref rid="B61" ref-type="bibr">R Core Team 2016</xref>).</p><p>PTP identifies the transition points between inter- and intraspecific branching events. The method postulates that the number of substitutions between species is significantly higher than that within species, with any individual substitution having a low probability of causing speciation. The mean numbers of substitutions until speciation events and until coalescent events are expected to follow exponential distributions, forming two independent Poisson processes on the tree. Since PTP does not require an ultrametric tree, we directly used the phylograms inferred in RAxML as described above. We employed three strategies in PTP: PTP heuristic (PTP-h) v2.2, Bayesian PTP maximum likelihood (bPTP-ML) v0.51, and Bayesian PTP heuristic (bPTP-h) v0.51, but did not consider the most recently proposed strategy, multi-rate PTP (<xref rid="B39" ref-type="bibr">Kapli et al. 2017</xref>; but see Discussion). For bPTP-ML and bPTP-h, we carried out two independent Bayesian Markov chain Monte Carlo (MCMC) analyses, with each chain having a length of 10<inline-formula><tex-math id="M77">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula> steps and the first 25% discarded as burn-in. After checking for convergence between chains, we reported the results from one of the two chains. For PTP-h, the minimal branch length was set to 10<inline-formula><tex-math id="M78">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{-6}}$\end{document}</tex-math></inline-formula>, with other parameters left at their default values.</p><p>BPP delimits species in the multispecies coalescent framework, which assumes that the gene trees evolve within the constraints of the species tree (<xref rid="B63" ref-type="bibr">Rannala and Yang 2003</xref>). It uses reversible-jump MCMC to move between delimitation models while calculating posterior probabilities. We used BPP v3.3a to analyze our simulated single- and multilocus datasets based on guide trees that matched those used for simulation. Algorithms 0 and 1 (<xref rid="B84" ref-type="bibr">Yang and Rannala 2010</xref>) were used in two independent runs with default parameters, with each having 10<inline-formula><tex-math id="M79">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula> MCMC iterations following a discarded burn-in of 10,000 iterations. We checked for convergence between the two runs and combined the MCMC samples to produce estimates of the posterior probabilities of various delimitation models. For all datasets, we placed diffuse gamma priors <inline-formula><tex-math id="M80">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$G$\end{document}</tex-math></inline-formula>(1,500) on all <inline-formula><tex-math id="M81">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$\theta$\end{document}</tex-math></inline-formula> parameters and <inline-formula><tex-math id="M82">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$G$\end{document}</tex-math></inline-formula>(1,100) on the root age <inline-formula><tex-math id="M83">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$\tau_{0}$\end{document}</tex-math></inline-formula> of species trees. These values are generally consistent with the settings in our simulations. For datasets from Scenario I, we randomly assigned equal numbers of sequences to each of two arbitrary species. For datasets with rate heterogeneity among loci, species delimitation was performed with a model of variable rates among loci, specified using a Dirichlet distribution with <inline-formula><tex-math id="M84">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$\alpha = 2$\end{document}</tex-math></inline-formula> (<xref rid="B6" ref-type="bibr">Burgess and Yang 2008</xref>; <xref rid="B89" ref-type="bibr">Zhang et al. 2011</xref>).</p></sec><sec id="SEC1.4"><title>Evaluation of Performance</title><p>The three methods examined in this study produce species delimitations in different forms. For species delimitation using BPP, we recorded the posterior probabilities of different delimitation models (e.g., <inline-formula><tex-math id="M85">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{1}$\end{document}</tex-math></inline-formula> for the one-species model and <inline-formula><tex-math id="M86">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> for the two-species model), and considered the rates of false positives and false negatives. For the results from GMYC and PTP, we based our evaluations on the most well supported species delimitations to capture the broad patterns. However, we did not take into account the support values for delimited entities, which should be considered when the focus of the investigation is on particular taxa. We calculated the number of delimited OTUs for each simulated species where delimitations were available, although this measure lacks the capacity to distinguish between certain outcomes (e.g., it cannot differentiate between the situations depicted in <xref ref-type="fig" rid="F2">Fig. 2a</xref>,<xref ref-type="fig" rid="F2">b</xref>, as illustrated below).</p><fig id="F2" orientation="portrait" position="float"><label>Figure 2.</label><caption><p>An illustration of four categories used to classify the results of our species delimitations, for every species pair, by the GMYC and the PTP methods. Boxes with <inline-formula><tex-math id="M87">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$a$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M88">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$b$\end{document}</tex-math></inline-formula> represent individuals of simulated species A and B, respectively, with additional individuals implied by the ellipses. Black bars above the boxes denote delimitation results; each bar indicates an OTU. Two illustrative examples each are given for false positives and for complex false positives.</p></caption><graphic xlink:href="syy011f2"/></fig><p>To enable the results to be described in finer detail, we used five categories to summarize the delimitations of GMYC and PTP for every species pair (<xref ref-type="fig" rid="F2">Fig. 2</xref>). Because our focus is on the overall quality of delimitation rather than the number of delimited OTUs, these categories are largely intended to evaluate the performance of GMYC and PTP. First, two species might be correctly identified as two distinct OTUs (&#x0201c;correct delimitation&#x0201d;). Second, two species might be delimited to be a single OTU (&#x0201c;false negative&#x0201d;). Third, at least one of the two species might be inferred to be two or more OTUs that are also distinct from the OTU(s) identified for the other species (&#x0201c;false positive&#x0201d;). Fourth, at least one of the two species is inferred to be two or more OTUs, but with partial or total overlap with the OTU(s) identified for the other species (&#x0201c;complex false positive&#x0201d;). These four categories are similar, but not identical, to those defined by <xref rid="B64" ref-type="bibr">Ratnasingham and Hebert (2013)</xref>. A fifth category comprises the cases in which we were unable to obtain a species delimitation (&#x0201c;not available&#x0201d; or &#x0201c;NA&#x0201d;).</p><p>GMYC and PTP can fail to yield a species delimitation under various circumstances. Because the trees were pruned so that they only contained unique sequences prior to phylogenetic inference, maximum-likelihood trees were unavailable for some datasets. This was problematic for both GMYC and PTP. In addition, PTP-h can fail to yield a definite species delimitation due to the failure of the likelihood-ratio test, because we used the default cut-off of <inline-formula><tex-math id="M89">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P = 0.001$\end{document}</tex-math></inline-formula> (<xref rid="B88" ref-type="bibr">Zhang 2013</xref>). In some cases, GMYC encountered computing errors, particularly for datasets comprising fewer sequences. Trees with zero branch lengths are actually compatible with PTP, but for convenience we preferred to include NAs for bPTP-h and bPTP-ML. For delimitations inferred for data from Scenario I, which involves a single species, we did not consider false negatives and complex false positives.</p></sec><sec id="SEC1.5"><title>Case Studies</title><p>Our simulation study was designed to provide an insight into the performance of species-delimitation methods with data generated under known conditions. However, the idealized settings of our simulations might not adequately reflect the complex conditions under which real sequences have evolved. Therefore, based on the results from our simulation study, we carried out additional comparisons of species-delimitation methods using empirical datasets of bears and bees. These datasets comprise sequences of mitochondrial genes, which are expected to have congruent gene trees because of the absence of recombination in the mitochondrial genome.</p><sec id="SEC1.5.1"><title>Bears</title><p>The genus <italic>Ursus</italic> (Carnivora: Ursidae) comprises both extant and extinct species of bears, for which the taxonomy is relatively uncontroversial. For this group, we obtained a total of 172 complete or partial mitochondrial genomes from GenBank (retrieved on 20 February, 2017) and extracted the 12 protein-coding genes (excluding <italic>ND6</italic>). The sloth bear (<italic>Melursus ursinus</italic>) was added as an outgroup to allow estimation of a rooted tree. After removing duplicate sequences and/or sequences containing large proportions of missing data, we aligned the sequences while maintaining the reading frames. Our dataset then comprised concatenated sequences of the 12 protein-coding genes from 89 taxa (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S4 available on Dryad).</p><p>We inferred the gene tree using maximum likelihood in RAxML, with a separate GTR<inline-formula><tex-math id="M90">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$+$\end{document}</tex-math></inline-formula>G<inline-formula><tex-math id="M91">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$+$\end{document}</tex-math></inline-formula>I substitution model applied to each gene. We ran rapid full analyses with 1000 bootstrap replicates. After rooting the tree and removing the outgroup sequence from the sloth bear, we used the inferred tree for species delimitation by GMYC, bPTP-h, bPTP-ML, and PTP-h. For BPP, the maximum-likelihood tree was simply treated as the guide tree for species delimitation with diffuse gamma priors <inline-formula><tex-math id="M92">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$\theta$\end{document}</tex-math></inline-formula>s <inline-formula><tex-math id="M93">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$\sim$\end{document}</tex-math></inline-formula><inline-formula><tex-math id="M94">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$G$\end{document}</tex-math></inline-formula>(1,500) and <inline-formula><tex-math id="M95">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$\tau_{0} \sim G$\end{document}</tex-math></inline-formula>(1,100).</p></sec><sec id="SEC1.5.2"><title>Bees</title><p>We tested the effects of locus number and sample size on species delimitation using sequence data from bees. This is a group of insects with important pollination services, but for which species diversity is relatively unclear. To test the impact of the number of loci, we downloaded a total of 38 complete or partial mitochondrial genomes of apid bees (Apidae; Hymenoptera: Apoidea) from GenBank (retrieved on 23 May, 2017). After removing duplicates and aligning the sequences, we used three datasets comprising 20 sequences of <italic>COI</italic>, 20 concatenated sequences of <italic>COI</italic> and <italic>CYTB</italic>, and 18 concatenated sequences of <italic>ATP6</italic>, <italic>COI</italic>, <italic>COII</italic>, <italic>COIII</italic>, <italic>CYTB</italic>, and <italic>ND1</italic>. Data are available in <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S5 available on Dryad.</p><p>To test the impact of sample size, we downloaded sequences of the canonical barcode region (i.e., the 5&#x02019; terminus of the <italic>COI</italic> gene) from the mason bees of the genus <italic>Osmia</italic> (Apoidea: Megachilidae). After deleting sequences containing large proportions of missing data, pruning non-unique sequences, aligning the sequences, and removing species represented by fewer than five sequences, we were left with 69 sequences. In terms of species annotations, we randomly deleted some sequences to obtain two additional datasets: one comprising two sequences per species, and another comprising a single sequence per species (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S6 available on Dryad).</p><p>To allow the position of the root to be inferred, corresponding genes or regions from <italic>Megachile sculpturalis</italic> (GenBank accession NC_028017) and/or <italic>Megachile strupigera</italic> (GenBank accession KT346366) were used as the outgroup for both apid bees and mason bees (<xref rid="B31" ref-type="bibr">Hedtke et al. 2013</xref>). Species delimitations with GMYC, bPTP-ML, and BPP were implemented in a similar manner to our analyses of bear sequences. For our BPP analyses, the maximum-likelihood tree inferred from the 69 sequences was used as the guide tree for the other two datasets of mason bees. To achieve MCMC convergence, we drew samples from <inline-formula><tex-math id="M96">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$5 \times 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula> MCMC steps rather than the 10<inline-formula><tex-math id="M97">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula> steps used in our other BPP analyses.</p></sec></sec></sec><sec sec-type="results" id="SEC2"><title>Results</title><sec id="SEC2.1"><title>Simulation Scenario I: One Species</title><p>For the null case in which sequences were sampled from a single species, BPP generally yielded the correct delimitation with high posterior probabilities (median <inline-formula><tex-math id="M98">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 0.99$\end{document}</tex-math></inline-formula> and mean <inline-formula><tex-math id="M99">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 0.93$\end{document}</tex-math></inline-formula>) for the one-species model (<inline-formula><tex-math id="M100">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{1})$\end{document}</tex-math></inline-formula>, across various parameter combinations (<xref rid="T2" ref-type="table">Table 2</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S7 available on Dryad). In some instances, however, <inline-formula><tex-math id="M101">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{1}$\end{document}</tex-math></inline-formula> has extremely low values (min. <inline-formula><tex-math id="M102">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 0.00$\end{document}</tex-math></inline-formula>), whereas posterior probabilities for the two-species model (<inline-formula><tex-math id="M103">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2})$\end{document}</tex-math></inline-formula> are relatively high. To avoid species inflation (<xref rid="B8" ref-type="bibr">Carstens et al. 2013</xref>), we consider that BPP supports the existence of two species only if <inline-formula><tex-math id="M104">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2} &#x0003e; 0.95$\end{document}</tex-math></inline-formula> (<xref rid="B89" ref-type="bibr">Zhang et al. 2011</xref>). Therefore, the false-positive error rate is 1.73% for BPP, but is high for both GMYC and PTP (<xref rid="T2" ref-type="table">Tables 2</xref> and <xref rid="T3" ref-type="table">3</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S7 available on Dryad).</p><p>
<table-wrap id="T2" orientation="portrait" position="float"><label>Table 2.</label><caption><p>Descriptive statistics of the evaluation indices for delimitation results from Scenarios I to V</p></caption><table frame="hsides" rules="groups"><thead align="center"><tr><th align="left" rowspan="1" colspan="1">Scenario</th><th align="center" rowspan="1" colspan="1">Statistics</th><th align="center" rowspan="1" colspan="1">BPP</th><th align="center" colspan="4" rowspan="1">No. of delimited OTUs</th></tr><tr><th align="left" rowspan="1" colspan="1"/><th align="center" rowspan="1" colspan="1"/><th align="center" rowspan="1" colspan="1">PP</th><th align="center" rowspan="1" colspan="1">GMYC</th><th align="center" rowspan="1" colspan="1">bPTP-h</th><th align="center" rowspan="1" colspan="1">bPTP-ML</th><th align="center" rowspan="1" colspan="1">PTP-h</th></tr></thead><tbody><tr><td align="left" rowspan="1" colspan="1">I</td><td align="center" rowspan="1" colspan="1">Median</td><td align="center" rowspan="1" colspan="1">0.99</td><td align="center" rowspan="1" colspan="1">2</td><td align="center" rowspan="1" colspan="1">8</td><td align="center" rowspan="1" colspan="1">5</td><td align="center" rowspan="1" colspan="1">11</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Mean</td><td align="center" rowspan="1" colspan="1">0.93</td><td align="center" rowspan="1" colspan="1">3.38</td><td align="center" rowspan="1" colspan="1">11.14</td><td align="center" rowspan="1" colspan="1">8.44</td><td align="center" rowspan="1" colspan="1">12.78</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Sd.</td><td align="center" rowspan="1" colspan="1">0.19</td><td align="center" rowspan="1" colspan="1">2.73</td><td align="center" rowspan="1" colspan="1">9.49</td><td align="center" rowspan="1" colspan="1">8.12</td><td align="center" rowspan="1" colspan="1">7.99</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Min.</td><td align="center" rowspan="1" colspan="1">0.00</td><td align="center" rowspan="1" colspan="1">2</td><td align="center" rowspan="1" colspan="1">2</td><td align="center" rowspan="1" colspan="1">2</td><td align="center" rowspan="1" colspan="1">2</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Max.</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">32</td><td align="center" rowspan="1" colspan="1">37</td><td align="center" rowspan="1" colspan="1">39</td><td align="center" rowspan="1" colspan="1">34</td></tr><tr><td align="left" rowspan="1" colspan="1">II</td><td align="center" rowspan="1" colspan="1">Median</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">1</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Mean</td><td align="center" rowspan="1" colspan="1">0.91</td><td align="center" rowspan="1" colspan="1">1.71</td><td align="center" rowspan="1" colspan="1">3.18</td><td align="center" rowspan="1" colspan="1">2.39</td><td align="center" rowspan="1" colspan="1">1.49</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Sd.</td><td align="center" rowspan="1" colspan="1">0.24</td><td align="center" rowspan="1" colspan="1">1.08</td><td align="center" rowspan="1" colspan="1">4.00</td><td align="center" rowspan="1" colspan="1">3.17</td><td align="center" rowspan="1" colspan="1">1.91</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Min.</td><td align="center" rowspan="1" colspan="1">0.00</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">1</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Max.</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">13</td><td align="center" rowspan="1" colspan="1">20</td><td align="center" rowspan="1" colspan="1">20</td><td align="center" rowspan="1" colspan="1">18</td></tr><tr><td align="left" rowspan="1" colspan="1">III</td><td align="center" rowspan="1" colspan="1">Median</td><td align="center" rowspan="1" colspan="1">0.02</td><td align="center" rowspan="1" colspan="1">2</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">3</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Mean</td><td align="center" rowspan="1" colspan="1">0.38</td><td align="center" rowspan="1" colspan="1">2.14</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">4.38</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Sd.</td><td align="center" rowspan="1" colspan="1">0.46</td><td align="center" rowspan="1" colspan="1">1.50</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">3.09</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Min.</td><td align="center" rowspan="1" colspan="1">0.00</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Max.</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">10</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">10</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">IV</td><td align="center" rowspan="1" colspan="1">Median</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Mean</td><td align="center" rowspan="1" colspan="1">0.97</td><td align="center" rowspan="1" colspan="1">1.04</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Sd.</td><td align="center" rowspan="1" colspan="1">0.15</td><td align="center" rowspan="1" colspan="1">0.30</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Min.</td><td align="center" rowspan="1" colspan="1">0.01</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Max.</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">7</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">V</td><td align="center" rowspan="1" colspan="1">Median</td><td align="center" rowspan="1" colspan="1">0.25</td><td align="center" rowspan="1" colspan="1">2</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">5</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Mean</td><td align="center" rowspan="1" colspan="1">0.42</td><td align="center" rowspan="1" colspan="1">2.14</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">5.06</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Sd.</td><td align="center" rowspan="1" colspan="1">0.44</td><td align="center" rowspan="1" colspan="1">1.74</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">3.40</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Min.</td><td align="center" rowspan="1" colspan="1">0.00</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">1</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">Max.</td><td align="center" rowspan="1" colspan="1">1.00</td><td align="center" rowspan="1" colspan="1">10</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">10</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr></tbody></table><table-wrap-foot><fn id="tblfn1"><p>Note: Posterior probabilities for correct delimitation models are given for the results from BPP: <inline-formula><tex-math id="M105">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{1}$\end{document}</tex-math></inline-formula> of the one-species model for Scenario I; <inline-formula><tex-math id="M106">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> of the two-species model for Scenarios II and III; <inline-formula><tex-math id="M107">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{7}$\end{document}</tex-math></inline-formula> of the five-species model for Scenario IV; and <inline-formula><tex-math id="M108">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{5}$\end{document}</tex-math></inline-formula> of the four-species model for Scenario V. The numbers of delimited OTUs for each simulated species are reported for both GMYC and PTP.</p><p>BPP <inline-formula><tex-math id="M109">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> Bayesian Phylogenetics and Phylogeography; GMYC <inline-formula><tex-math id="M110">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> generalized mixed Yule-coalescent; PTP <inline-formula><tex-math id="M111">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> Poisson tree processes; bPTP-h <inline-formula><tex-math id="M112">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> Bayesian PTP heuristic; bPTP-ML <inline-formula><tex-math id="M113">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> Bayesian PTP maximum likelihood; PTP-h <inline-formula><tex-math id="M114">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> PTP heuristic; PP <inline-formula><tex-math id="M115">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> posterior probability; OTU <inline-formula><tex-math id="M116">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> operational taxonomic unit.</p></fn></table-wrap-foot></table-wrap>
</p><p>
<table-wrap id="T3" orientation="portrait" position="float"><label>Table 3.</label><caption><p>Percentages of categories with delimitation results from Scenarios I to V by GMYC and PTP</p></caption><table frame="hsides" rules="groups"><thead align="center"><tr><th align="left" rowspan="1" colspan="1">Scenario</th><th align="center" rowspan="1" colspan="1">Category</th><th align="center" colspan="4" rowspan="1">Percent (%)</th></tr><tr><th align="left" rowspan="1" colspan="1"/><th align="center" rowspan="1" colspan="1"/><th align="center" rowspan="1" colspan="1">GMYC</th><th align="center" rowspan="1" colspan="1">bPTP-h</th><th align="center" rowspan="1" colspan="1">bPTP-ML</th><th align="center" rowspan="1" colspan="1">PTP-h</th></tr></thead><tbody><tr><td align="left" rowspan="1" colspan="1">I</td><td align="center" rowspan="1" colspan="1">CD</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">0</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FP</td><td align="center" rowspan="1" colspan="1">76.50</td><td align="center" rowspan="1" colspan="1">91.00</td><td align="center" rowspan="1" colspan="1">91.00</td><td align="center" rowspan="1" colspan="1">8.33</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">NA</td><td align="center" rowspan="1" colspan="1">23.50</td><td align="center" rowspan="1" colspan="1">9.00</td><td align="center" rowspan="1" colspan="1">9.00</td><td align="center" rowspan="1" colspan="1">91.67</td></tr><tr><td align="left" rowspan="1" colspan="1">II</td><td align="center" rowspan="1" colspan="1">CD</td><td align="center" rowspan="1" colspan="1">21.89</td><td align="center" rowspan="1" colspan="1">48.89</td><td align="center" rowspan="1" colspan="1">60.33</td><td align="center" rowspan="1" colspan="1">39.83</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FN</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">0</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FP</td><td align="center" rowspan="1" colspan="1">52.61</td><td align="center" rowspan="1" colspan="1">36.39</td><td align="center" rowspan="1" colspan="1">21.67</td><td align="center" rowspan="1" colspan="1">4.22</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">CFP</td><td align="center" rowspan="1" colspan="1">13.06</td><td align="center" rowspan="1" colspan="1">9.28</td><td align="center" rowspan="1" colspan="1">12.56</td><td align="center" rowspan="1" colspan="1">1.89</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">NA</td><td align="center" rowspan="1" colspan="1">12.44</td><td align="center" rowspan="1" colspan="1">5.44</td><td align="center" rowspan="1" colspan="1">5.44</td><td align="center" rowspan="1" colspan="1">54.06</td></tr><tr><td align="left" rowspan="1" colspan="1">III</td><td align="center" rowspan="1" colspan="1">CD</td><td align="center" rowspan="1" colspan="1">5.60</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">10.80</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FN</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FP</td><td align="center" rowspan="1" colspan="1">19.20</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">24.80</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">CFP</td><td align="center" rowspan="1" colspan="1">45.60</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">56.00</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">NA</td><td align="center" rowspan="1" colspan="1">29.60</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">8.40</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">IV</td><td align="center" rowspan="1" colspan="1">CD</td><td align="center" rowspan="1" colspan="1">88.10</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">87.55</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FN</td><td align="center" rowspan="1" colspan="1">5.85</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">11.70</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FP</td><td align="center" rowspan="1" colspan="1">4.70</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">CFP</td><td align="center" rowspan="1" colspan="1">0.10</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">0</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">NA</td><td align="center" rowspan="1" colspan="1">1.25</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">0.75</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">V</td><td align="center" rowspan="1" colspan="1">CD</td><td align="center" rowspan="1" colspan="1">7.53</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">6.67</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FN</td><td align="center" rowspan="1" colspan="1">10.00</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">4.60</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">FP</td><td align="center" rowspan="1" colspan="1">16.47</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">33.00</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">CFP</td><td align="center" rowspan="1" colspan="1">40.53</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">48.20</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr><tr><td align="left" rowspan="1" colspan="1">&#x000a0;</td><td align="center" rowspan="1" colspan="1">NA</td><td align="center" rowspan="1" colspan="1">25.47</td><td align="center" rowspan="1" colspan="1">&#x02014;</td><td align="center" rowspan="1" colspan="1">7.53</td><td align="center" rowspan="1" colspan="1">&#x02014;</td></tr></tbody></table><table-wrap-foot><fn id="tblfn2"><p>Note: Values are given based on single species in Scenario I, one species pair in Scenarios II and III, ten species pairs in Scenario IV, and six species pairs in Scenario V.</p><p>CD <inline-formula><tex-math id="M117">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> correct delimitation; FP <inline-formula><tex-math id="M118">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> false positive; NA <inline-formula><tex-math id="M119">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> not available; FN <inline-formula><tex-math id="M120">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> false negative; CFP <inline-formula><tex-math id="M121">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$=$\end{document}</tex-math></inline-formula> complex false positive.</p></fn></table-wrap-foot></table-wrap>
</p><p>After excluding NAs, all of the delimitations by GMYC and PTP yielded false positives, indicating that both oversplit species under our null simulation scenario. However, they delimited varying numbers of OTUs. Given the available delimitations, GMYC inferred fewer OTUs (median <inline-formula><tex-math id="M122">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 2$\end{document}</tex-math></inline-formula> and mean <inline-formula><tex-math id="M123">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 3.38$\end{document}</tex-math></inline-formula>) than PTP across all of the relevant simulation conditions. Among the three strategies of PTP, bPTP-h and PTP-h gave rise to smaller maximum numbers of delimited OTUs than did bPTP-ML, but bPTP-ML yielded fewer OTUs overall (median <inline-formula><tex-math id="M124">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 5$\end{document}</tex-math></inline-formula> and mean <inline-formula><tex-math id="M125">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 8.44$\end{document}</tex-math></inline-formula>).</p></sec><sec id="SEC2.2"><title>Simulation Scenario II: Two Species</title><p>With diffuse priors in BPP, <inline-formula><tex-math id="M126">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> for the two-species delimitation model approaches 1.00 under most conditions, yielding an average false-negative error rate of 14.40% (<xref ref-type="fig" rid="F3">Fig. 3</xref> and <xref rid="T2" ref-type="table">Table 2</xref>). With large divergence times (<inline-formula><tex-math id="M127">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula> or 10<inline-formula><tex-math id="M128">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}})$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M129">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> is greater than 0.95 for almost all population sizes. When <inline-formula><tex-math id="M130">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{5}}$\end{document}</tex-math></inline-formula>, the <inline-formula><tex-math id="M131">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> values show complex patterns: with smaller population sizes (<inline-formula><tex-math id="M132">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula> and 10<inline-formula><tex-math id="M133">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}})$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M134">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> is below 0.95 especially for smaller sample sizes (<inline-formula><tex-math id="M135">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 1$\end{document}</tex-math></inline-formula> and 2), but tends to increase with number of loci <inline-formula><tex-math id="M136">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula>; with a larger population size (<inline-formula><tex-math id="M137">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{6}})$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M138">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> first decreases, then approaches 1.00 with larger values of <inline-formula><tex-math id="M139">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M140">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula>.</p><fig id="F3" orientation="portrait" position="float"><label>Figure 3.</label><caption><p>Species delimitations estimated by the Bayesian coalescent method in BPP. Boxplots are shown for posterior probabilities of the two-species delimitation model (<inline-formula><tex-math id="M141">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2})$\end{document}</tex-math></inline-formula>, across every 10 replicates under each set of conditions for Scenario II. Nine combinations (<inline-formula><tex-math id="M142">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M143">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t)$\end{document}</tex-math></inline-formula> of population size <inline-formula><tex-math id="M144">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula> and divergence time <inline-formula><tex-math id="M145">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> are shown along the top, together with five values of sample size <inline-formula><tex-math id="M146">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> on the right. The <inline-formula><tex-math id="M147">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$x$\end{document}</tex-math></inline-formula>-axis represents the number of loci, while probabilities are given on the <inline-formula><tex-math id="M148">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$y$\end{document}</tex-math></inline-formula>-axis.</p></caption><graphic xlink:href="syy011f3"/></fig><p>The results from our BPP analyses of supplementary (<inline-formula><tex-math id="M149">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M150">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t)$\end{document}</tex-math></inline-formula> combinations (10<inline-formula><tex-math id="M151">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{3}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M152">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}})$\end{document}</tex-math></inline-formula> are similar to those from (10<inline-formula><tex-math id="M153">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M154">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}})$\end{document}</tex-math></inline-formula>. Results from (10<inline-formula><tex-math id="M155">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{7}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M156">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}})$\end{document}</tex-math></inline-formula> are broadly consistent with those from (10<inline-formula><tex-math id="M157">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M158">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}})$\end{document}</tex-math></inline-formula>, except for large values of <inline-formula><tex-math id="M159">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M160">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and some failures in MCMC convergence. Given that MCMC convergence was generally good in our BPP delimitations, here we only note the cases in which MCMC convergence was not achieved. With sample size <inline-formula><tex-math id="M161">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 50$\end{document}</tex-math></inline-formula> and (<inline-formula><tex-math id="M162">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M163">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t)$\end{document}</tex-math></inline-formula> as (10<inline-formula><tex-math id="M164">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M165">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}})$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M166">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> values are 1.00 across different values of <inline-formula><tex-math id="M167">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S8 available on Dryad).</p><p>The number of delimited OTUs for each simulated species (median <inline-formula><tex-math id="M168">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 1$\end{document}</tex-math></inline-formula> and mean <inline-formula><tex-math id="M169">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$= 1.71$\end{document}</tex-math></inline-formula>) gives the impression that GMYC might correctly delimit species under this scenario (<xref rid="T2" ref-type="table">Table 2</xref>). However, GMYC actually produced a low rate of correct delimitations (21.89%) with no false negatives. False positives (52.61%) and complex false positives (13.06%) occurred quite often, with the latter limited to certain combinations of (<inline-formula><tex-math id="M170">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M171">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t)$\end{document}</tex-math></inline-formula> (<xref ref-type="fig" rid="F4">Fig. 4</xref> and <xref rid="T3" ref-type="table">Table 3</xref>). GMYC failed to yield a species delimitation with small sample size <inline-formula><tex-math id="M172">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> and/or number of loci <inline-formula><tex-math id="M173">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula>, and encountered computing errors with some (<inline-formula><tex-math id="M174">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M175">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t)$\end{document}</tex-math></inline-formula> combinations. Although correct delimitations generally accompany false positives, the percentage of the latter is significantly higher than that of the former (<inline-formula><tex-math id="M176">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P&#x0003c;10^{\mathrm{-3}}$\end{document}</tex-math></inline-formula>, non-parametric paired Wilcoxon test). The test also reveals that larger numbers of loci did not improve the chance of obtaining correct delimitations (<inline-formula><tex-math id="M177">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P&#x0003e;$\end{document}</tex-math></inline-formula>0.05 for <inline-formula><tex-math id="M178">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> comparisons of 1&#x0223c;2, 1&#x0223c;5, 1&#x0223c;10, and 1&#x0223c;20), but larger samples sizes did (<inline-formula><tex-math id="M179">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P&#x0003c;10^{\mathrm{-3}}$\end{document}</tex-math></inline-formula> for <inline-formula><tex-math id="M180">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> comparisons 2&#x0223c;5, 2&#x0223c;10, and 2&#x0223c;20). In contrast, analyzing larger numbers of loci resulted in more false-positive errors, whereas sample size did not have a significant influence on this error rate.</p><fig id="F4" orientation="portrait" position="float"><label>Figure 4.</label><caption><p>Species delimitations estimated by GMYC for datasets from Scenario II. Panels show nine combinations of population size <inline-formula><tex-math id="M181">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula> and divergence time <inline-formula><tex-math id="M182">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> along the top, and four values of sample size <inline-formula><tex-math id="M183">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> on the right. The <inline-formula><tex-math id="M184">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$x$\end{document}</tex-math></inline-formula>-axis represents the number of loci. The <inline-formula><tex-math id="M185">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$y$\end{document}</tex-math></inline-formula>-axis represents the number of cases classified by correct delimitation (CD), false positive (FP), complex false positive (CFP), and not available (NA) among the 10 replicates under each set of conditions, which are denoted by different shades according to the legend in the bottom-right. No false negatives occurred in the results from Scenario II.</p></caption><graphic xlink:href="syy011f4"/></fig><p>PTP-h produced many NA results (54.06%), with most due to unclear species delimitations. However, correct delimitations dominate the remaining available results (<xref rid="T3" ref-type="table">Table 3</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S9 available on Dryad). The other two methods, bPTP-h and bPTP-ML, performed similarly to each other, with far fewer NA results. However, bPTP-h oversplit species more frequently (<xref rid="T3" ref-type="table">Table 3</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S10 available on Dryad). Because we penalize species overestimation more greatly than underestimation, we only provide the detailed results from bPTP-ML here (<xref ref-type="fig" rid="F5">Fig. 5</xref>). This method produced correct species delimitations in the majority of cases (60.33%; <xref rid="T3" ref-type="table">Table 3</xref>). However, it had variable rates of success under different conditions, as also shown by the larger standard deviation (3.17) of the numbers of delimited OTUs for each simulated species (<xref rid="T2" ref-type="table">Table 2</xref>). Under some conditions (e.g., the combination of <inline-formula><tex-math id="M186">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M187">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{7}})$\end{document}</tex-math></inline-formula>, the performance of bPTP-ML generally increases with number of loci <inline-formula><tex-math id="M188">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and sample size <inline-formula><tex-math id="M189">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula>. It did not produce any false negatives, whereas false positives mainly occur in the (<inline-formula><tex-math id="M190">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M191">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t)$\end{document}</tex-math></inline-formula> combinations of (10<inline-formula><tex-math id="M192">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M193">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{5}})$\end{document}</tex-math></inline-formula> and (10<inline-formula><tex-math id="M194">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}}$\end{document}</tex-math></inline-formula>, 10<inline-formula><tex-math id="M195">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$^{\mathrm{6}})$\end{document}</tex-math></inline-formula>. Complex false positives tend to dominate the results when <inline-formula><tex-math id="M196">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M197">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{5}}$\end{document}</tex-math></inline-formula>.</p><fig id="F5" orientation="portrait" position="float"><label>Figure 5.</label><caption><p>Species delimitations estimated by the Bayesian PTP maximum likelihood (bPTP-ML) for datasets from Scenario II. Panels show nine combinations of population size <inline-formula><tex-math id="M198">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula> and divergence time <inline-formula><tex-math id="M199">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> along the top, and four values of sample size <inline-formula><tex-math id="M200">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> on the right. The <inline-formula><tex-math id="M201">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$x$\end{document}</tex-math></inline-formula>-axis represents the number of loci. The <inline-formula><tex-math id="M202">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$y$\end{document}</tex-math></inline-formula>-axis represents the number of cases classified by correct delimitation (CD), false positive (FP), complex false positive (CFP), and not available (NA) among the 10 replicates under each set of conditions, which are denoted by different shades according to the legend in the bottom-right. No false negatives occurred in the results from Scenario II.</p></caption><graphic xlink:href="syy011f5"/></fig><p>We found that species delimitations are not significantly different whether we performed simulations using makesamples or SimPhy. Population growth also had no significant influence on the performance of the species-delimitation methods. However, having one species of population size larger than that of the other species (<inline-formula><tex-math id="M203">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}})$\end{document}</tex-math></inline-formula> led to false positives dominating the delimitations by GMYC and bPTP-ML. Both unbalanced sampling and mutation rate heterogeneity at different levels were found to have small effects on the performance of these methods (see <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S2 available on Dryad for details).</p></sec><sec id="SEC2.3"><title>Simulation Scenario III: Two Species with Ongoing Gene Flow</title><p>With the basic settings informed by Scenario II (population size <inline-formula><tex-math id="M204">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N = 10^{\mathrm{4}}$\end{document}</tex-math></inline-formula>, divergence time <inline-formula><tex-math id="M205">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t = 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula>, and sample size <inline-formula><tex-math id="M206">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 10$\end{document}</tex-math></inline-formula>) and with reference to delimitation results under these conditions in Scenario II, we found that varying degrees of gene flow did influence the performance of these methods. BPP identified the correct species delimitation when the migration rate was very low (<inline-formula><tex-math id="M207">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 0.1$\end{document}</tex-math></inline-formula>) (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S11 available on Dryad). When <inline-formula><tex-math id="M208">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 1, P_{2}$\end{document}</tex-math></inline-formula> approaches 1.00 only with larger numbers of loci. For higher migration rates, nearly all <inline-formula><tex-math id="M209">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> values approach 0.00, indicating the presence of false negatives.</p><p>Compared with BPP, both GMYC and bPTP-ML appear to be more sensitive to the presence of gene flow. A large proportion of false positives was produced by GMYC when <inline-formula><tex-math id="M210">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 0.1$\end{document}</tex-math></inline-formula>, and complex false positives dominate the available results of GMYC when <inline-formula><tex-math id="M211">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} \geqslant 1$\end{document}</tex-math></inline-formula>. Of the different methods in PTP, we only consider bPTP-ML here because of its superior performance in Scenarios I and II. It produced a variety of results, including correct delimitations, when <inline-formula><tex-math id="M212">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 0.1$\end{document}</tex-math></inline-formula> and 1. For higher migration rates, however, bPTP-ML tended to produce complex false positives. Neither GMYC nor bPTP-ML yielded false negatives under this scenario. The numbers of delimited OTUs for each simulated species in this scenario are also higher than those in Scenario II for both GMYC and bPTP-ML (<xref rid="T2" ref-type="table">Table 2</xref>).</p></sec><sec id="SEC2.4"><title>Simulation Scenario IV: Five Species</title><p>We use the values of the speciation rates, which controlled the relative depths of the internal nodes in our simulated trees, to refer to the five chronograms in this scenario (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S3 available on Dryad). When speciation rate <inline-formula><tex-math id="M213">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r \leq 10^{-6}$\end{document}</tex-math></inline-formula> (i.e., relatively deep speciation events), BPP generally obtained the correct species delimitation model with posterior probabilities (<inline-formula><tex-math id="M214">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{7})$\end{document}</tex-math></inline-formula> approaching 1.00 (<xref ref-type="fig" rid="F6">Fig. 6a</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S12 available on Dryad). This model is denoted as &#x0201c;1111&#x0201d;, where the numbers &#x0201c;1&#x0201d; indicate the correct resolutions of (A,B) from (D,(C,E)), A from B, D from (C,E), and C from E, respectively. Failure to identify each of these distinctions is denoted by &#x0201c;0&#x0201d;. When <inline-formula><tex-math id="M215">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r = 10^{\mathrm{-5 }}$\end{document}</tex-math></inline-formula> (i.e., shallow speciation events), posterior probabilities are spread across all delimitation models except 0000, given small numbers of loci (i.e., <inline-formula><tex-math id="M216">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 1$\end{document}</tex-math></inline-formula> or 2); for larger <inline-formula><tex-math id="M217">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula>, however, <inline-formula><tex-math id="M218">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{7}$\end{document}</tex-math></inline-formula> always approaches 1.00.</p><fig id="F6" orientation="portrait" position="float"><label>Figure 6.</label><caption><p>a) Posterior probabilities of the correct species delimitation model 1111 (<inline-formula><tex-math id="M219">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{7})$\end{document}</tex-math></inline-formula> by BPP in Scenario IV. Probabilities are along the <inline-formula><tex-math id="M220">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$y$\end{document}</tex-math></inline-formula>-axis for every 10 replicates of each number of loci (<inline-formula><tex-math id="M221">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$x$\end{document}</tex-math></inline-formula>-axis) combined with the speciation rates (indicated by text above the boxplots). b) Symbol plots of correct delimitations by GMYC in Scenario IV. Yellow (light grey in printed version) circles represent results from the four species pairs along the two basal branches, while blue (dark grey in printed version) circles represent results from the six species pairs across the two basal branches. Relative areas of circles correspond to the percentages of correct delimitations in the respective full delimitations (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S12 available on Dryad), with the maximum area indicating 100%. Symbols in (c) have the same meaning as in (b), but show correct delimitations by bPTP-ML in Scenario IV. (d) Correlogram of posterior probabilities inferred by BPP across 250 datasets in Scenario V. Diagonal lines running from top-left to bottom-right in the red panels below the diagonal and red pies above the diagonal denote negative correlation, whereas diagonal lines running from bottom-left to top-right in the blue panels below the diagonal and blue pies above the diagonal denote positive correlation (the blue and red colors are only shown in online version). Darker colors indicate stronger relationships. Delimitation models are denoted by the character &#x0201c;m&#x0201d; and numbers along the diagonal, such as &#x0201c;m111&#x0201d; for delimitation model 111.</p></caption><graphic xlink:href="syy011f6"/></fig><p>GMYC yielded the correct delimitation under some circumstances in the five-species scenario (<xref ref-type="fig" rid="F6">Fig. 6b</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S12 available on Dryad). When <inline-formula><tex-math id="M222">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r \leqslant  10^{-6}$\end{document}</tex-math></inline-formula>, GMYC correctly delimited all of the species pairs, though with a few false negatives and false positives. When <inline-formula><tex-math id="M223">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r = 10^{\mathrm{-5 }}$\end{document}</tex-math></inline-formula> and with smaller <inline-formula><tex-math id="M224">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula>, it mainly produced false negatives for the four species pairs along the two basal branches, but correct delimitations for the other six pairs. For <inline-formula><tex-math id="M225">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r = 10^{\mathrm{-5 }}$\end{document}</tex-math></inline-formula> and large <inline-formula><tex-math id="M226">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula>, some false positives appear together with correct delimitations for all ten species pairs, making GMYC delimitations complicated. Conversely, bPTP-ML produced results with some clear patterns (<xref ref-type="fig" rid="F6">Fig. 6c</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S12 available on Dryad). When <inline-formula><tex-math id="M227">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r = 10^{\mathrm{-5}}$\end{document}</tex-math></inline-formula>, it typically yielded false negatives and correct delimitations for the above four species pairs and the six species pairs, respectively. When <inline-formula><tex-math id="M228">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$r = 10^{\mathrm{-6}}$\end{document}</tex-math></inline-formula> together with <inline-formula><tex-math id="M229">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 1$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M230">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 2$\end{document}</tex-math></inline-formula>, some false negatives accompany correct delimitations. Under the remaining conditions, bPTP-ML almost always yielded correct delimitations. Numbers of delimited OTUs for GMYC and bPTP-ML have lower mean and median values (<xref rid="T2" ref-type="table">Table 2</xref>), but these can mask the presence of false negatives.</p></sec><sec id="SEC2.5"><title>Simulation Scenario V: Four Species with Ongoing Gene Flow between Adjacent Species</title><p>For our scenario with four species experiencing ongoing gene flow between geographically adjacent species, BPP yielded posterior probabilities similar to those from Scenario III (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S13 available on Dryad). Only with a low migration rate (<inline-formula><tex-math id="M231">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} \leqslant 1$\end{document}</tex-math></inline-formula>; and <inline-formula><tex-math id="M232">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 1$\end{document}</tex-math></inline-formula> with larger <inline-formula><tex-math id="M233">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l)$\end{document}</tex-math></inline-formula> were high probabilities obtained for the correct delimitation model. This model is referred to as &#x0201c;111&#x0201d;, where the numbers &#x0201c;1&#x0201d; denote correct resolutions of (A,B) from (C,D), A from B, and C from D, respectively. Failure to identify each of these distinctions is denoted by &#x0201c;0&#x0201d;. Otherwise, the model 000 has a high posterior probability (<xref ref-type="fig" rid="F6">Fig. 6d</xref>), though it is relatively low when <inline-formula><tex-math id="M234">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 10$\end{document}</tex-math></inline-formula>.</p><p>As with Scenario III, GMYC and bPTP-ML show stronger sensitivity to the presence of gene flow, but they both yielded a few false negatives with every level of gene flow (<xref rid="T2" ref-type="table">Tables 2</xref> and <xref rid="T3" ref-type="table">3</xref>; <ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S13 available on Dryad). A variety of results containing some correct delimitations (mainly with smaller numbers of loci) and many false positives were obtained for GMYC when <inline-formula><tex-math id="M235">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 0.1$\end{document}</tex-math></inline-formula> and for bPTP-ML when <inline-formula><tex-math id="M236">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} \leqslant 1$\end{document}</tex-math></inline-formula>. Otherwise, complex false positives dominate their delimitations.</p><p>Separate analyses of the four species pairs from Scenario V (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S14 available on Dryad) show that when <inline-formula><tex-math id="M237">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 1$\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id="M238">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> values from BPP for (A, C) and (A, D) are higher than those for (A, B) and (B, C). Even with a very high migration rate (<inline-formula><tex-math id="M239">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 10$\end{document}</tex-math></inline-formula>), <inline-formula><tex-math id="M240">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$P_{2}$\end{document}</tex-math></inline-formula> for (A, D) approaches 1.00 for large numbers of loci <inline-formula><tex-math id="M241">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 10$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M242">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 20$\end{document}</tex-math></inline-formula>. For bPTP-ML, correct delimitations for (A, C) and (A, D) were obtained more frequently than those for (A, B) and (B, C) when <inline-formula><tex-math id="M243">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 0.1$\end{document}</tex-math></inline-formula>. For GMYC, which produced limited correct delimitations, there are no clear differences in the numbers of correct delimitations among the four species pairs. However, for <inline-formula><tex-math id="M244">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} = 1$\end{document}</tex-math></inline-formula>, more false positives were produced for (A, C) and (A, D), whereas complex false positives dominate the results from (A, B) and (B, C). For all three species-delimitation methods, delimitation differences among the species pairs, indicating the effects of geographic distance, become negligible when the migration rate is very small or very large (as appropriate for each method).</p></sec><sec id="SEC2.6"><title>Species Delimitation in Bears</title><p>The maximum-likelihood estimate of the mitochondrial tree of <italic>Ursus</italic> shows that the brown bear (<italic>U. arctos</italic>) is paraphyletic with respect to the polar bear (<italic>U. maritimus</italic>) (<xref ref-type="fig" rid="F7">Fig. 7</xref>). The sequences from the cave bear (<italic>U. spelaeus</italic>) are also not monophyletic, but this is corrected with the recent designation of sequence NC_011112 as belonging to <italic>U. ingressus</italic> (<xref rid="B74" ref-type="bibr">Stiller et al. 2014</xref>). With the species annotation and reciprocal monophyly as references, a guide tree comprising 10 taxa (<xref ref-type="fig" rid="F7">Fig. 7</xref>) aided BPP in identifying 9 OTUs with a posterior probability of 0.84. The three strategies in PTP, bPTP-ML, bPTP-h, and PTP-h, estimated 17, 22, and 20 OTUs, respectively. GMYC analysis using a single threshold identified the presence of 20 OTUs, matching the result obtained using PTP-h.</p><fig id="F7" orientation="portrait" position="float"><label>Figure 7.</label><caption><p>Species delimitations estimated for a dataset comprising 89 sequences from bears (genus <italic>Ursus</italic>). The maximum-likelihood tree is shown on the left. The vertical bars, from left to right, indicate the OTUs inferred by BPP, bPTP-ML, bPTP-h, PTP-h, and GMYC, respectively. Clades (of different colors in online version) in the tree indicate the 10 taxa in the guide tree for BPP delimitation, and a collapsed clade at the bottom with the label &#x0201c;HQ6859.._ <italic>Ursus</italic>_ <italic>arctos</italic>&#x0201d; represents 34 sequences of <italic>Ursus arctos</italic> with accession numbers beginning with &#x0201c;HQ6859&#x0201d; (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S4 available on Dryad).</p></caption><graphic xlink:href="syy011f7"/></fig></sec><sec id="SEC2.7"><title>Species Delimitation in Bees</title><p>Species included in our datasets account for a small portion of the described species of apid bees and mason bees, so we do not focus on the inferred relationships here. We tested the effects of the number of loci <inline-formula><tex-math id="M245">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and sample size <inline-formula><tex-math id="M246">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> on the performance of BPP, GMYC, and bPTP-ML with the available species annotations. For Apidae (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S15 available on Dryad), our duplicate BPP analyses with <inline-formula><tex-math id="M247">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$5\times 10^{\mathrm{6}}$\end{document}</tex-math></inline-formula> MCMC steps failed to converge when <inline-formula><tex-math id="M248">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 6$\end{document}</tex-math></inline-formula>, resulting in posterior probabilities approaching 1.00 for two different delimitation models. One of these models, along with the species delimitations with the highest posterior probabilities when <inline-formula><tex-math id="M249">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 1$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M250">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 2$\end{document}</tex-math></inline-formula>, are consistent with the species annotations. Both GMYC and bPTP-ML produced estimates congruent with the species annotations, except that GMYC delineated two subspecies of <italic>Apis mellifera</italic> from other subspecies when <inline-formula><tex-math id="M251">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l = 2$\end{document}</tex-math></inline-formula>.</p><p>For <italic>Osmia</italic> bees (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S16 available on Dryad), when <inline-formula><tex-math id="M252">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 1$\end{document}</tex-math></inline-formula>, GMYC and bPTP-ML mixed the annotated species to varying degrees. When <inline-formula><tex-math id="M253">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 2$\end{document}</tex-math></inline-formula>, the two methods generally identified the annotated species, but with one more OTU for some species. When <inline-formula><tex-math id="M254">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n\geqslant 5$\end{document}</tex-math></inline-formula>, the methods delimited more OTUs for the annotated species. Delimitations from our BPP analyses are consistent with the 10 <italic>Osmia</italic> species whenever <inline-formula><tex-math id="M255">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n = 1$\end{document}</tex-math></inline-formula>, 2, or <inline-formula><tex-math id="M256">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$\geqslant 5$\end{document}</tex-math></inline-formula>.</p></sec></sec><sec sec-type="discussion" id="SEC3"><title>Discussion</title><sec id="SEC3.1"><title>Performance of Species-Delimitation Methods</title><p>We have presented a comprehensive comparison of the performance of three widely used molecular species-delimitation methods, based on five different simulation scenarios. The Bayesian coalescent method in BPP, designed for multiple loci, was found to yield high posterior probabilities for correct species delimitations under a variety of conditions. It was relatively robust to the influence of unequal population sizes, population growth, unbalanced sampling, and mutation rate heterogeneity. Some of these findings are consistent with those of previous studies (e.g., <xref rid="B89" ref-type="bibr">Zhang et al. 2011</xref>; <xref rid="B3" ref-type="bibr">Barley et al. 2018</xref>). However, we note that our use of BPP was carried out under favorable conditions. For example, we used diffuse gamma priors that were compatible with the population sizes and divergence times in our simulations (<xref rid="B41" ref-type="bibr">Leach&#x000e9; and Fujita 2010</xref>; <xref rid="B83" ref-type="bibr">Yang 2015</xref>). We also specified the true species tree as the guide tree, although the species tree can be jointly estimated with species delimitation by BPP or independently inferred by BPP or other software such as *BEAST (<xref rid="B32" ref-type="bibr">Heled and Drummond 2010</xref>; <xref rid="B85" ref-type="bibr">Yang and Rannala 2014</xref>; <xref rid="B10" ref-type="bibr">Caviedes-Solis et al. 2015</xref>; <xref rid="B83" ref-type="bibr">Yang 2015</xref>).</p><p>We confirmed that BPP encountered problems when the migration rate between species was relatively high (<inline-formula><tex-math id="M257">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$M_{ij} &#x0003e; 1$\end{document}</tex-math></inline-formula>), and that its delimitation efficacy was somewhat affected by geographic distance (<xref rid="B89" ref-type="bibr">Zhang et al. 2011</xref>). These results are not surprising, given the assumption of BPP that no gene flow exists between species. We also found that when the ratio of population size to divergence time (<inline-formula><tex-math id="M258">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M259">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t)$\end{document}</tex-math></inline-formula> was relatively high, BPP had a high probability of underestimating the number of species, although this could potentially be overcome by analyzing larger numbers of loci and/or using larger samples. Therefore, recently diverged species (as shown by our Scenarios II and IV) pose a challenge to BPP, especially when they have larger population sizes. This outcome is consistent with a previous finding that more loci are needed when analyzing species that have a shallow evolutionary history (<xref rid="B33" ref-type="bibr">Hime et al. 2016</xref>).</p><p>We obtained different species delimitations across the three PTP strategies and even the newly developed multi-rate PTP method, which did not perform better than bPTP-ML according to our evaluation criteria (results not shown). However, our focus is on comparison of GMYC, PTP, and BPP. In contrast with BPP, the first two methods aim to delimit species efficiently with data from a single locus, but are increasingly being applied to multilocus datasets. However, our results highlight some differences between the methods. First, unlike the single-threshold GMYC, the best PTP strategy bPTP-ML did not encounter computing errors. Second, bPTP-ML correctly delimited species in Scenario II more frequently than did GMYC, contributing to the better overall performance of bPTP-ML compared with GMYC. Third, larger <inline-formula><tex-math id="M260">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M261">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> generally enhanced correct delimitations in bPTP-ML, whereas the effect of the former was more modest for GMYC. Fourth, where species were not delimited correctly, the results often differed between GMYC and bPTP-ML. Last, the numbers of delimited OTUs for simulated species indicate that GMYC and PTP can infer different numbers of OTUs in practice.</p><p>There are also considerable similarities in the performance of GMYC and bPTP-ML. Both methods were sensitive to the ratio <inline-formula><tex-math id="M262">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M263">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula>. Large values of <inline-formula><tex-math id="M264">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M265">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> led to complex false positives involving polyphyletic species and false positives involving oversplitting (e.g., Scenario II). Both GMYC and bPTP-ML were also very sensitive to ongoing gene flow, with negative impacts seen even with very low levels of gene flow. Further, both were generally robust to the effects of potential confounding factors (e.g., unbalanced sampling and mutation rate heterogeneity), but to a lesser extent than BPP overall (especially to differing population sizes; Supplementary Appendix S2 available on Dryad). Additionally, our results support the suggestion that GMYC should not be used when the dataset consists of very few putative species, and we extend this suggestion to include bPTP-ML. This is because of the imbalance between speciation and coalescence (<xref rid="B76" ref-type="bibr">Talavera et al. 2013</xref>; <xref rid="B17" ref-type="bibr">Dellicour and Flot 2015</xref>), which poses a challenge to identifying transition points between inter- and intraspecific processes. In our results, problems appeared in the forms of no correct delimitations (Scenario I), no false negatives (Scenario II, Scenario III, and individual pairs from Scenario V), and even computing errors in GMYC.</p><p>On the whole, our results indicate that both GMYC and bPTP-ML are able to perform well in the absence of gene flow between species; the latter method tends to perform better overall, although in some cases it produced larger numbers of inferred OTUs. Nevertheless, GMYC and bPTP-ML have a number of important shortcomings. First, they need gene trees to be specified and they treat these as being equivalent to species trees. This assumption is problematic when there is strong discordance between the gene trees and the species tree (<xref rid="B15" ref-type="bibr">Degnan and Rosenberg 2006</xref>, <xref rid="B16" ref-type="bibr">2009</xref>). Consequently, concatenation of multiple loci requires caution when using methods designed to analyze single-locus data. Second, when <inline-formula><tex-math id="M266">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M267">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> was relatively high, the performance of both GMYC and bPTP-ML was poor regardless of the number of loci or the sample size. Third, GMYC and bPTP-ML rely on the accuracy of the input trees, and upstream errors can result in misleading species delimitations (<xref rid="B77" ref-type="bibr">Tang et al. 2014</xref>). GMYC seems to be generally robust to the choice of method used to infer the ultrametric input tree (<xref rid="B76" ref-type="bibr">Talavera et al. 2013</xref>; <xref rid="B11" ref-type="bibr">da Cruz and Weksler 2018</xref>), but this result is in contrast with the known sensitivity of node-time estimates to the choice of clock model and tree prior (e.g., <xref rid="B19" ref-type="bibr">Duch&#x000ea;ne et al. 2014</xref>).</p></sec><sec id="SEC3.2"><title>Factors Affecting Molecular Species Delimitation</title><p>The combination of population size <inline-formula><tex-math id="M268">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula> and divergence time <inline-formula><tex-math id="M269">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> has differing impacts on the three species-delimitation methods. When the ratio <inline-formula><tex-math id="M270">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M271">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> was relatively high for two species, BPP tended to produce false-negative errors. To obtain a correct species delimitation with a small number of loci and small sample size (e.g., 2 for both), the maximum of the ratio <inline-formula><tex-math id="M272">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M273">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> for BPP analyses should be around 1 (<xref ref-type="fig" rid="F3">Fig. 3</xref>). With higher <inline-formula><tex-math id="M274">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M275">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula>, GMYC and bPTP-ML increasingly produced false positives and complex false positives in Scenario II. With shallow speciation events in Scenario IV, GMYC mainly yielded false negatives with false positives for closely related species, whereas bPTP-ML increased the number of false negatives. These results demonstrate the important influence of incomplete lineage sorting on species delimitation, which becomes more probable with higher <inline-formula><tex-math id="M276">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M277">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula>.</p><p>There is growing use of multi-locus datasets for molecular species delimitation, but this often involves a trade-off between the number of loci and sample size for each species (<xref rid="B23" ref-type="bibr">Fujisawa and Barraclough 2013</xref>; <xref rid="B33" ref-type="bibr">Hime et al. 2016</xref>). We investigated the possible effects of this trade-off, particularly in Scenario II of our simulations, finding that the effects were outweighed by those of the population size and divergence time. This was previously demonstrated for GMYC (<xref rid="B23" ref-type="bibr">Fujisawa and Barraclough 2013</xref>), but we have found that it also holds true for BPP and bPTP-ML. However, the effects of the number of loci <inline-formula><tex-math id="M278">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and sample size <inline-formula><tex-math id="M279">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> cannot be negligible. Although both <inline-formula><tex-math id="M280">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M281">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> appeared to have no impact with lower <inline-formula><tex-math id="M282">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M283">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> (somewhat consistent with the results of <xref rid="B86" ref-type="bibr">Yang and Rannala 2017</xref>), increasing both <inline-formula><tex-math id="M284">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id="M285">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> improved correct delimitations of BPP with higher <inline-formula><tex-math id="M286">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M287">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula> (<xref ref-type="fig" rid="F3">Figs. 3</xref> and <xref ref-type="fig" rid="F6">6a</xref>). Under some conditions in Scenario II, increasing <inline-formula><tex-math id="M288">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$l$\end{document}</tex-math></inline-formula> and/or <inline-formula><tex-math id="M289">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$n$\end{document}</tex-math></inline-formula> improved the performance of bPTP-ML and GMYC.</p><p>Our concatenations of larger numbers of loci generally did not have negative effects on the performance of bPTP-ML and GMYC, indicating that the impacts of violating the assumption of gene-tree discordance are modest. However, the situation might be considerably more complex for real data. The performance of BPP with concatenations of 20 loci was equal to or worse than that with single locus (results not shown), suggesting that concatenating independent loci can have a negative impact on methods that have been designed to analyze multiple loci.</p><p>The presence of gene flow had negative impacts on the three methods examined in our study, particularly on GMYC and bPTP-ML. These two methods were also substantially affected by differing population sizes resulting in higher <inline-formula><tex-math id="M290">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$N$\end{document}</tex-math></inline-formula>/<inline-formula><tex-math id="M291">\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym} 
\usepackage{amsfonts} 
\usepackage{amssymb} 
\usepackage{amsbsy}
\usepackage{upgreek}
\usepackage{mathrsfs}
\setlength{\oddsidemargin}{-69pt}
\begin{document}
}{}$t$\end{document}</tex-math></inline-formula>, along with the number of putative species. In contrast, unbalanced sampling, population growth, and mutation rate heterogeneity appeared to have limited impacts on species delimitation using these methods (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S2 available on Dryad).</p></sec><sec id="SEC3.3"><title>Species Delimitation, Population Delimitation, or a Mosaic?</title><p>Our simulation study has shed light on the performance of species-delimitation methods across a range of speciation scenarios, but its implications apply equally to studies of highly structured populations (<xref rid="B75" ref-type="bibr">Sukumaran and Knowles 2017</xref>). For example, BPP has been variously used to delimit populations (e.g., <xref rid="B58" ref-type="bibr">Potter et al. 2016</xref>), to delimit species (e.g., <xref rid="B47" ref-type="bibr">Mason et al. 2016</xref>), to delimit populations with the potential to elevate them to species (e.g., <xref rid="B53" ref-type="bibr">Oliveira et al. 2015</xref>), and to delimit evolving lineages within and across species (e.g., <xref rid="B51" ref-type="bibr">Moritz et al. 2018</xref>). Our speciation scenarios can be interpreted as being analogous to specific models of population structure. For example, two species with unequal population sizes (<ext-link ext-link-type="uri" xlink:href="https://academic.oup.com/sysbio/article-lookup/doi/10.1093/sysbio/syy011#supplementary-data">Supplementary Appendix</ext-link> S2 available on Dryad) can be treated as a simple case of the continent-island model without migration. Our simulation Scenario V is a one-dimensional stepping-stone model of population structure.</p><p>The delimited OTUs can represent species, populations, or even a mosaic of species and populations. In this study, we have simply treated speciation events as being instantaneous, while assuming Wright&#x02013;Fisher panmixia for each species. In contrast, both the protracted speciation model and the viewpoint that a species is a separately evolving metapopulation lineage (<xref rid="B14" ref-type="bibr">de Queiroz 2007</xref>) treat speciation as an extended process. However, these two treatments are not strictly contradictory. Our modelling of speciation as an instantaneous event can be interpreted as the initiation of speciation (i.e., lineage separation), with the delimited OTUs then representing populations or a mosaic of species and populations. If the extended process is relatively short and the newly formed species do not have pronounced structure, then species divergence is effectively an instantaneous event. If there is any continuation of gene flow, the effects are captured in our Scenarios III and V.</p><p>The results of molecular species delimitation should be interpreted alongside other lines of evidence, such as comparative morphology, population genetics, and ecology (<xref rid="B75" ref-type="bibr">Sukumaran and Knowles 2017</xref>). The importance of using such an integrative approach to taxonomy is underscored by our finding that PTP and GMYC yielded high rates of false positives and complex false positives in some circumstances. If interpreted naively, the results of these analyses would lead to an artificial inflation in the number of species.</p></sec><sec id="SEC3.4"><title>Implications of Case Studies</title><p>Our case studies, based on species of bears and bees, confirmed some of the findings from our simulation study. These included differences in the performance of the three species-delimitation methods, along with the modest effects of increasing the number of loci and sample size. To interpret these results, we referred to the species annotations accompanying the sequence data from bears and bees, most of which are attributed to traditional morphological taxonomy.</p><p>Like the species annotations in our cases, additional information from morphological characters, behavioral traits, and geographic distributions would be needed to provide informed staring points for species delimitation. Accordingly, molecular species delimitation is either implicitly or explicitly carried out as part of an integrative taxonomy approach (<xref rid="B13" ref-type="bibr">Dayrat 2005</xref>; <xref rid="B82" ref-type="bibr">Will et al. 2005</xref>). In terms of other lines of evidence as above, the informed starting points provide important background for interpreting the presence of population structure.</p><p>Our analysis of mitochondrial genomes from bears enhances our understanding of these vulnerable, endangered, or extinct animals. For example, our results point to various delimitations of the brown bear, a species that has been the subject of numerous mitochondrial studies (<xref rid="B12" ref-type="bibr">Davison et al. 2011</xref>). Its mitochondrial paraphyly with respect to the polar bear has been recognized as an instance of introgression due to past hybridization between the two species (<xref rid="B27" ref-type="bibr">Hailer et al. 2012</xref>).</p><p>Compared with bears, many bee species remain undescribed, despite their ecological and economic importance. Currently, pollinator bees fundamental to agricultural productivity are declining towards extinction (<xref rid="B54" ref-type="bibr">Ollerton et al. 2014</xref>; <xref rid="B9" ref-type="bibr">Carswell 2015</xref>). Our delimitation results are broadly consistent with the species annotations, indicating that molecular species delimitation complements rather than replaces the traditional taxonomy. With molecular species delimitation, the first step to protecting species diversity of bees can be accelerated.</p></sec></sec><sec sec-type="conclusions" id="SEC4"><title>Conclusions</title><p>We have compared the performance of three widely used methods of species-delimitation across a range of simulation scenarios and evolutionary parameters. Our results have drawn attention to the better accuracy and robustness of the Bayesian coalescent method in BPP, along with the performance of the GMYC model and the PTP model under a range of conditions. All three methods are negatively influenced by gene flow and are sensitive to the ratio of population size to divergence time, reflecting the important impact of incomplete lineage sorting on species delimitation. Unexpectedly, we found only a modest benefit in increasing the number of loci and the sample size per species. Future studies of molecular species delimitation, particularly focusing on a range of empirical datasets, will provide further insights into the relative impacts of different confounding factors. With a greater understanding of the behavior of molecular species-delimitation methods, genetic data will increasingly contribute to integrative taxonomy and other areas of biological research.</p></sec></body><back><ack><title>Acknowledgments</title><p>We thank the reviewers and the editors for their constructive comments, and Dr Qingsong Zhou, Huanxi Cao, and Dr Pengfei Gao for advice on software. We thank those who generated, edited, and submitted the sequences analyzed in our case studies. We acknowledge the University of Sydney HPC service for providing computing resources for some of our analyses.</p></ack><sec id="SEC5" sec-type="materials"><title>Supplementary Material</title><p>Data available from the Dryad Digital Repository: <ext-link ext-link-type="uri" xlink:href="http://dx.doi.org/10.5061/dryad.739bs">http://dx.doi.org/10.5061/dryad.739bs</ext-link>.</p></sec><sec><title>Funding</title><p>This work was supported by the National Natural Science Foundation of China [grant number 31201701; the Youth Innovation Promotion Association of the Chinese Academy of Sciences [2017118]; and the Ministry of Environmental Protection of China [grant number 2111101]. A.L. was funded by a visiting scholarship from the Chinese Academy of Sciences to carry out research at the University of Sydney. S.Y.W.H. was supported by a Future Fellowship from the Australian Research Council. We acknowledge the support of the National Science Fund for Distinguished Young Scholars [grant number 31625024 to C.Z.].</p></sec><ref-list><title>References</title><ref id="B1"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Ahrens</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Fujisawa</surname><given-names>T.,</given-names></name><name name-style="western"><surname>Krammer</surname><given-names>H.,</given-names></name><name name-style="western"><surname>Eberle</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Fabrizi</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Vogler</surname><given-names>A.P.</given-names></name></person-group>
<year>2016</year>
<article-title>Rarity and incomplete sampling in DNA-based species delimitation.</article-title><source>Syst. Biol.</source><volume>65</volume>:<fpage>478</fpage>&#x02013;<lpage>494</lpage>.<pub-id pub-id-type="pmid">26797695</pub-id></mixed-citation></ref><ref id="B2"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Arrigoni</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Berumen</surname><given-names>M.L.,</given-names></name><name name-style="western"><surname>Chen</surname><given-names>C.A.,</given-names></name><name name-style="western"><surname>Terraneo</surname><given-names>T.I.,</given-names></name><name name-style="western"><surname>Baird</surname><given-names>A.H.,</given-names></name><name name-style="western"><surname>Payri</surname><given-names>C.,</given-names></name><name name-style="western"><surname>Benzoni</surname><given-names>F.</given-names></name></person-group>
<year>2016</year>
<article-title>Species delimitation in the reef coral genera <italic>Echinophyllia</italic> and <italic>Oxypora</italic> (Scleractinia, Lobophylliidae) with a description of two new species.</article-title><source>Mol. Phylogenet. Evol.</source><volume>105</volume>:<fpage>146</fpage>&#x02013;<lpage>159</lpage>.<pub-id pub-id-type="pmid">27593164</pub-id></mixed-citation></ref><ref id="B3"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Barley</surname><given-names>A.J.,</given-names></name><name name-style="western"><surname>Brown</surname><given-names>J.M.,</given-names></name><name name-style="western"><surname>Thomson</surname><given-names>R.C.</given-names></name></person-group>
<year>2018</year>
<article-title>Impact of model violations on the inference of species boundaries under the multispecies coalescent.</article-title><source>Syst. Biol.</source><volume>67</volume>:<fpage>269</fpage>&#x02013;<lpage>284</lpage>.<pub-id pub-id-type="pmid">28945903</pub-id></mixed-citation></ref><ref id="B4"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name name-style="western"><surname>Baum</surname><given-names>D.A.,</given-names></name><name name-style="western"><surname>Shaw</surname><given-names>K.L.</given-names></name></person-group>
<year>1995</year>
<article-title>Genealogical perspectives on the species problem.</article-title> In: <person-group person-group-type="editor"><name name-style="western"><surname>Hoch</surname><given-names>P.C.,</given-names></name><name name-style="western"><surname>Stephenson</surname><given-names>A.G.,</given-names></name></person-group> editors. <comment>Experimental and molecular approaches to plant biosystematics.</comment><publisher-loc>St. Louis</publisher-loc>: <publisher-name>Missouri Botanical Garden</publisher-name> p. <fpage>289</fpage>&#x02013;<lpage>303</lpage>.</mixed-citation></ref><ref id="B5"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Bond</surname><given-names>J.E.,</given-names></name><name name-style="western"><surname>Stockman</surname><given-names>A.K.</given-names></name></person-group>
<year>2008</year>
<article-title>An integrative method for delimiting cohesion species: finding the population-species interface in a group of Californian trapdoor spiders with extreme genetic divergence and geographic structuring.</article-title><source>Syst. Biol.</source><volume>57</volume>:<fpage>628</fpage>&#x02013;<lpage>646</lpage>.<pub-id pub-id-type="pmid">18686196</pub-id></mixed-citation></ref><ref id="B6"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Burgess</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Yang</surname><given-names>Z.</given-names></name></person-group>
<year>2008</year>
<article-title>Estimation of hominoid ancestral population sizes under Bayesian coalescent models incorporating mutation rate variation and sequencing errors.</article-title><source>Mol. Biol. Evol.</source><volume>25</volume>:<fpage>1979</fpage>&#x02013;<lpage>1994</lpage>.<pub-id pub-id-type="pmid">18603620</pub-id></mixed-citation></ref><ref id="B7"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Camargo</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Morando</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Avila</surname><given-names>L.J.,</given-names></name><name name-style="western"><surname>Sites</surname><given-names>J.W.</given-names><suffix>Jr.</suffix></name></person-group>
<year>2012</year>
<article-title>Species delimitation with ABC and other coalescent-based methods: a test of accuracy with simulations and an empirical example with lizards of the <italic>Liolaemus darwinii</italic> complex (Squamata: Liolaemidae).</article-title><source>Evolution</source><volume>66</volume>:<fpage>2834</fpage>&#x02013;<lpage>2849</lpage>.<pub-id pub-id-type="pmid">22946806</pub-id></mixed-citation></ref><ref id="B8"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Carstens</surname><given-names>B.C.,</given-names></name><name name-style="western"><surname>Pelletier</surname><given-names>T.A.,</given-names></name><name name-style="western"><surname>Reid</surname><given-names>N.M.,</given-names></name><name name-style="western"><surname>Satler</surname><given-names>J.D.</given-names></name></person-group>
<year>2013</year>
<article-title>How to fail at species delimitation?</article-title><source>Mol. Ecol.</source><volume>22</volume>:<fpage>4369</fpage>&#x02013;<lpage>4383</lpage>.<pub-id pub-id-type="pmid">23855767</pub-id></mixed-citation></ref><ref id="B9"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Carswell</surname><given-names>C.</given-names></name></person-group>
<year>2015</year>
<article-title>Bumblebees aren&#x02019;t keeping up with a warming planet.</article-title><source>Science</source><volume>349</volume>:<fpage>126</fpage>&#x02013;<lpage>127</lpage>.<pub-id pub-id-type="pmid">26160921</pub-id></mixed-citation></ref><ref id="B10"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Caviedes-Solis</surname><given-names>I.W.,</given-names></name><name name-style="western"><surname>Bouzid</surname><given-names>N.M.,</given-names></name><name name-style="western"><surname>Banbury</surname><given-names>B.L.,</given-names></name><name name-style="western"><surname>Leach&#x000e9;</surname><given-names>A.D.</given-names></name></person-group>
<year>2015</year>
<article-title>Uprooting phylogenetic uncertainty in coalescent species delimitation: a meta-analysis of empirical studies.</article-title><source>Curr. Zool.</source><volume>61</volume>:<fpage>866</fpage>&#x02013;<lpage>873</lpage>.</mixed-citation></ref><ref id="B11"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>da Cruz</surname><given-names>M.D.,</given-names></name><name name-style="western"><surname>Weksler</surname><given-names>M.</given-names></name></person-group>
<year>2018</year>
<article-title>Impact of tree priors in species delimitation and phylogenetics of the genus <italic>Oligoryzomys</italic> (Rodentia: Cricetidae).</article-title><source>Mol. Phylogenet. Evol.</source><volume>119</volume>:<fpage>1</fpage>&#x02013;<lpage>12</lpage>.<pub-id pub-id-type="pmid">29107618</pub-id></mixed-citation></ref><ref id="B12"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Davison</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Ho</surname><given-names>S.Y.W.,</given-names></name><name name-style="western"><surname>Bray</surname><given-names>S.C.,</given-names></name><name name-style="western"><surname>Korsten</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Tammeleht</surname><given-names>E.,</given-names></name><name name-style="western"><surname>Hindrikson</surname><given-names>M.,</given-names></name><name name-style="western"><surname>&#x000d8;stbye</surname><given-names>K.,</given-names></name><name name-style="western"><surname>&#x000d8;stbye</surname><given-names>E.,</given-names></name><name name-style="western"><surname>Lauritzen</surname><given-names>S-E.,</given-names></name><name name-style="western"><surname>Austin</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Cooper</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Saarma</surname><given-names>U.</given-names></name></person-group>
<year>2011</year>
<article-title>Late-Quaternary biogeographic scenarios for the brown bear (<italic>Ursus arctos</italic>), a wild mammal model species.</article-title><source>Quat. Sci. Rev.</source><volume>30</volume>:<fpage>418</fpage>&#x02013;<lpage>430</lpage>.</mixed-citation></ref><ref id="B13"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Dayrat</surname><given-names>B.</given-names></name></person-group>
<year>2005</year>
<article-title>Towards integrative taxonomy.</article-title><source>Biol. J. Linn. Soc.</source><volume>85</volume>:<fpage>407</fpage>&#x02013;<lpage>415</lpage>.</mixed-citation></ref><ref id="B14"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>de Queiroz</surname><given-names>K.</given-names></name></person-group>
<year>2007</year>
<article-title>Species concepts and species delimitation.</article-title><source>Syst. Biol.</source><volume>56</volume>:<fpage>879</fpage>&#x02013;<lpage>886</lpage>.<pub-id pub-id-type="pmid">18027281</pub-id></mixed-citation></ref><ref id="B15"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Degnan</surname><given-names>J.H.,</given-names></name><name name-style="western"><surname>Rosenberg</surname><given-names>N.A.</given-names></name></person-group>
<year>2006</year>
<article-title>Discordance of species trees with their most likely gene trees.</article-title><source>PLOS Genet.</source><volume>2</volume>:<fpage>e68</fpage>.<pub-id pub-id-type="pmid">16733550</pub-id></mixed-citation></ref><ref id="B16"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Degnan</surname><given-names>J.H.,</given-names></name><name name-style="western"><surname>Rosenberg</surname><given-names>N.A.</given-names></name></person-group>
<year>2009</year>
<article-title>Gene tree discordance, phylogenetic inference and the multispecies coalescent.</article-title><source>Trends Ecol. Evol.</source><volume>24</volume>:<fpage>332</fpage>&#x02013;<lpage>340</lpage>.<pub-id pub-id-type="pmid">19307040</pub-id></mixed-citation></ref><ref id="B17"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Dellicour</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Flot</surname><given-names>J.</given-names></name></person-group>
<year>2015</year>
<article-title>Delimiting species-poor data sets using single molecular markers: a study of Barcode Gaps, Haplowebs and GMYC.</article-title><source>Syst. Biol.</source><volume>64</volume>:<fpage>900</fpage>&#x02013;<lpage>908</lpage>.<pub-id pub-id-type="pmid">25601944</pub-id></mixed-citation></ref><ref id="B18"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Dobzhansky</surname><given-names>T.</given-names></name></person-group>
<year>1950</year>
<article-title>Mendelian populations and their evolution.</article-title><source>Am. Nat.</source><volume>84</volume>:<fpage>401</fpage>&#x02013;<lpage>418</lpage>.</mixed-citation></ref><ref id="B19"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Duch&#x000ea;ne</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Lanfear</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Ho</surname><given-names>S.Y.W.</given-names></name></person-group>
<year>2014</year>
<article-title>The impact of calibration and clock-model choice on molecular estimates of divergence times.</article-title><source>Mol. Phylogenet. Evol.</source><volume>78</volume>:<fpage>277</fpage>&#x02013;<lpage>289</lpage>.<pub-id pub-id-type="pmid">24910154</pub-id></mixed-citation></ref><ref id="B20"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Ence</surname><given-names>D.D.,</given-names></name><name name-style="western"><surname>Carstens</surname><given-names>B.C.</given-names></name></person-group>
<year>2011</year>
<article-title>SpedeSTEM: a rapid and accurate method for species delimitation.</article-title><source>Mol. Ecol. Resour.</source><volume>11</volume>:<fpage>473</fpage>&#x02013;<lpage>480</lpage>.<pub-id pub-id-type="pmid">21481205</pub-id></mixed-citation></ref><ref id="B21"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Etienne</surname><given-names>R.S.,</given-names></name><name name-style="western"><surname>Morlon</surname><given-names>H.,</given-names></name><name name-style="western"><surname>Lambert</surname><given-names>A.</given-names></name></person-group>
<year>2014</year>
<article-title>Estimating the duration of speciation from phylogenies.</article-title><source>Evolution</source><volume>68</volume>:<fpage>2430</fpage>&#x02013;<lpage>2440</lpage>.<pub-id pub-id-type="pmid">24758256</pub-id></mixed-citation></ref><ref id="B22"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Ezard</surname><given-names>T.,</given-names></name><name name-style="western"><surname>Fujisawa</surname><given-names>T.,</given-names></name><name name-style="western"><surname>Barraclough</surname><given-names>T.G.</given-names></name></person-group>
<year>2009</year>
<article-title>Splits: species limits by threshold statistics (version 1.0-19).</article-title> Available from: URL <ext-link ext-link-type="uri" xlink:href="http://R-Forge.R-project.org/projects/splits/">http://R-Forge.R-project.org/projects/splits/</ext-link>.</mixed-citation></ref><ref id="B23"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Fujisawa</surname><given-names>T.,</given-names></name><name name-style="western"><surname>Barraclough</surname><given-names>T.G.</given-names></name></person-group>
<year>2013</year>
<article-title>Delimiting species using single-locus data and the generalized mixed Yule coalescent approach: a revised method and evaluation on simulated data sets.</article-title><source>Syst. Biol.</source><volume>62</volume>:<fpage>707</fpage>&#x02013;<lpage>724</lpage>.<pub-id pub-id-type="pmid">23681854</pub-id></mixed-citation></ref><ref id="B24"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Gavrilets</surname><given-names>S.</given-names></name></person-group>
<year>2003</year>
<article-title>Models of speciation: what have we learned in 40 years?</article-title><source>Evolution</source><volume>57</volume>:<fpage>2197</fpage>&#x02013;<lpage>2215</lpage>.<pub-id pub-id-type="pmid">14628909</pub-id></mixed-citation></ref><ref id="B25"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Goldstein</surname><given-names>P.Z.,</given-names></name><name name-style="western"><surname>DeSalle</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Amato</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Vogler</surname><given-names>A.P.</given-names></name></person-group>
<year>2000</year>
<article-title>Conservation genetics at the species boundary.</article-title><source>Conserv. Biol.</source><volume>14</volume>:<fpage>120</fpage>&#x02013;<lpage>131</lpage>.</mixed-citation></ref><ref id="B26"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Grummer</surname><given-names>J.A.,</given-names></name><name name-style="western"><surname>Bryson</surname><given-names>R.W.</given-names><suffix>Jr.,</suffix></name><name name-style="western"><surname>Reeder</surname><given-names>T.W.</given-names></name></person-group>
<year>2014</year>
<article-title>Species delimitation using Bayes factors: simulations and application to the <italic>Sceloporus scalaris</italic> species group (Squamata: Phrynosomatidae).</article-title><source>Syst. Biol.</source><volume>63</volume>:<fpage>119</fpage>&#x02013;<lpage>133</lpage>.<pub-id pub-id-type="pmid">24262383</pub-id></mixed-citation></ref><ref id="B27"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hailer</surname><given-names>F.,</given-names></name><name name-style="western"><surname>Kutschera</surname><given-names>V.E.,</given-names></name><name name-style="western"><surname>Hallstr&#x000f6;m</surname><given-names>B.M.,</given-names></name><name name-style="western"><surname>Klassert</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Fain</surname><given-names>S.R.,</given-names></name><name name-style="western"><surname>Leonard</surname><given-names>J.A.,</given-names></name><name name-style="western"><surname>Arnason</surname><given-names>U.,</given-names></name><name name-style="western"><surname>Janke</surname><given-names>A.</given-names></name></person-group>
<year>2012</year>
<article-title>Nuclear genomic sequences reveal that polar bears are an old and distinct bear lineage.</article-title><source>Science</source><volume>336</volume>:<fpage>344</fpage>&#x02013;<lpage>347</lpage>.<pub-id pub-id-type="pmid">22517859</pub-id></mixed-citation></ref><ref id="B28"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hartmann</surname><given-names>K.,</given-names></name><name name-style="western"><surname>Wong</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Stadler</surname><given-names>T.</given-names></name></person-group>
<year>2010</year>
<article-title>Sampling trees from evolutionary models.</article-title><source>Syst. Biol.</source><volume>59</volume>:<fpage>465</fpage>&#x02013;<lpage>476</lpage>.<pub-id pub-id-type="pmid">20547782</pub-id></mixed-citation></ref><ref id="B29"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hebert</surname><given-names>P.D.,</given-names></name><name name-style="western"><surname>Cywinska</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Ball</surname><given-names>S.L.,</given-names></name><name name-style="western"><surname>deWaard</surname><given-names>J.R.</given-names></name></person-group>
<year>2003</year>
<article-title>Biological identifications through DNA barcodes.</article-title><source>Proc. Biol. Sci.</source><volume>270</volume>:<fpage>313</fpage>&#x02013;<lpage>321</lpage>.<pub-id pub-id-type="pmid">12614582</pub-id></mixed-citation></ref><ref id="B30"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hebert</surname><given-names>P.D.,</given-names></name><name name-style="western"><surname>Penton</surname><given-names>E.H.,</given-names></name><name name-style="western"><surname>Burns</surname><given-names>J.M.,</given-names></name><name name-style="western"><surname>Janzen</surname><given-names>D.H.,</given-names></name><name name-style="western"><surname>Hallwachs</surname><given-names>W.</given-names></name></person-group>
<year>2004</year>
<article-title>Ten species in one: DNA barcoding reveals cryptic species in the neotropical skipper butterfly <italic>Astraptes fulgerator</italic>.</article-title><source>Proc. Natl. Acad. Sci. USA</source><volume>101</volume>:<fpage>14812</fpage>&#x02013;<lpage>14817</lpage>.<pub-id pub-id-type="pmid">15465915</pub-id></mixed-citation></ref><ref id="B31"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hedtke</surname><given-names>S.M.,</given-names></name><name name-style="western"><surname>Patiny</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Danforth</surname><given-names>B.N.</given-names></name></person-group>
<year>2013</year>
<article-title>The bee tree of life: a supermatrix approach to apoid phylogeny and biogeography.</article-title><source>BMC Evol. Biol.</source><volume>13</volume>:<fpage>138</fpage>.<pub-id pub-id-type="pmid">23822725</pub-id></mixed-citation></ref><ref id="B32"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Heled</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Drummond</surname><given-names>A.J.</given-names></name></person-group>
<year>2010</year>
<article-title>Bayesian inference of species trees from multilocus data.</article-title><source>Mol. Biol. Evol.</source><volume>27</volume>:<fpage>570</fpage>&#x02013;<lpage>580</lpage>.<pub-id pub-id-type="pmid">19906793</pub-id></mixed-citation></ref><ref id="B33"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hime</surname><given-names>P.M.,</given-names></name><name name-style="western"><surname>Hotaling</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Grewelle</surname><given-names>R.E.,</given-names></name><name name-style="western"><surname>O&#x02019;Neill</surname><given-names>E.M.,</given-names></name><name name-style="western"><surname>Voss</surname><given-names>S.R.,</given-names></name><name name-style="western"><surname>Shaffer</surname><given-names>H.B.,</given-names></name><name name-style="western"><surname>Weisrock</surname><given-names>D.W.</given-names></name></person-group>
<year>2016</year>
<article-title>The influence of locus number and information content on species delimitation: an empirical test case in an endangered Mexican salamander.</article-title><source>Mol. Ecol.</source><volume>25</volume>:<fpage>5959</fpage>&#x02013;<lpage>5974</lpage>.<pub-id pub-id-type="pmid">27748559</pub-id></mixed-citation></ref><ref id="B34"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hoppeler</surname><given-names>F.,</given-names></name><name name-style="western"><surname>Shah</surname><given-names>R.D.T.,</given-names></name><name name-style="western"><surname>Shah</surname><given-names>D.N,</given-names></name><name name-style="western"><surname>J&#x000e4;hnig</surname><given-names>S.C.,</given-names></name><name name-style="western"><surname>Tonkin</surname><given-names>J.D.,</given-names></name><name name-style="western"><surname>Sharma</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Pauls</surname><given-names>S.U.</given-names></name></person-group>
<year>2016</year>
<article-title>Environmental and spatial characterization of an unknown fauna using DNA sequencing&#x02014;an example with Himalayan Hydropsychidae (Insecta: Trichoptera).</article-title><source>Freshw. Biol.</source><volume>61</volume>:<fpage>1905</fpage>&#x02013;<lpage>1920</lpage>.</mixed-citation></ref><ref id="B35"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hotaling</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Foley</surname><given-names>M.E.,</given-names></name><name name-style="western"><surname>Lawrence</surname><given-names>N.M.,</given-names></name><name name-style="western"><surname>Bocanegra</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Blanco</surname><given-names>M.B.,</given-names></name><name name-style="western"><surname>Rasoloarison</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Kappeler</surname><given-names>P.M.,</given-names></name><name name-style="western"><surname>Barrett</surname><given-names>M.A.,</given-names></name><name name-style="western"><surname>Yoder</surname><given-names>A.D.,</given-names></name><name name-style="western"><surname>Weisrock</surname><given-names>D.W.</given-names></name></person-group>
<year>2016</year>
<article-title>Species discovery and validation in a cryptic radiation of endangered primates: coalescent-based species delimitation in Madagascar&#x02019;s mouse lemurs.</article-title><source>Mol. Ecol.</source><volume>25</volume>:<fpage>2029</fpage>&#x02013;<lpage>2045</lpage>.<pub-id pub-id-type="pmid">26946180</pub-id></mixed-citation></ref><ref id="B36"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Hudson</surname><given-names>R.R.</given-names></name></person-group>
<year>2002</year>
<article-title>Generating samples under a Wright-Fisher neutral model of genetic variation.</article-title><source>Bioinformatics</source><volume>18</volume>:<fpage>337</fpage>&#x02013;<lpage>338</lpage>.<pub-id pub-id-type="pmid">11847089</pub-id></mixed-citation></ref><ref id="B37"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Jackson</surname><given-names>N.D.,</given-names></name><name name-style="western"><surname>Carstens</surname><given-names>B.C.,</given-names></name><name name-style="western"><surname>Morales</surname><given-names>A.E.,</given-names></name><name name-style="western"><surname>O&#x02019;Meara</surname><given-names>B.C.</given-names></name></person-group>
<year>2017</year>
<article-title>Species delimitation with gene flow.</article-title><source>Syst. Biol.</source><volume>66</volume>:<fpage>799</fpage>&#x02013;<lpage>812</lpage>.<pub-id pub-id-type="pmid">28003535</pub-id></mixed-citation></ref><ref id="B38"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name name-style="western"><surname>Jukes</surname><given-names>T.H.,</given-names></name><name name-style="western"><surname>Cantor</surname><given-names>C.R.</given-names></name></person-group>
<year>1969</year>
<article-title>Evolution of protein molecules.</article-title> In: <person-group person-group-type="editor"><name name-style="western"><surname>Munro</surname><given-names>H.N.,</given-names></name></person-group> editor. <comment>Mammalian protein metabolism</comment>
<publisher-loc>New York</publisher-loc>: <publisher-name>Academic Press</publisher-name> p. <fpage>21</fpage>&#x02013;<lpage>123</lpage>.</mixed-citation></ref><ref id="B39"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Kapli</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Lutteropp</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Zhang</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Kobert</surname><given-names>K.,</given-names></name><name name-style="western"><surname>Pavlidis</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Stamatakis</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Flouri</surname><given-names>T.</given-names></name></person-group>
<year>2017</year>
<article-title>Multi-rate Poisson tree processes for single-locus species delimitation under maximum likelihood and Markov chain Monte Carlo.</article-title><source>Bioinformatics</source><volume>33</volume>:<fpage>1630</fpage>&#x02013;<lpage>1638</lpage>.<pub-id pub-id-type="pmid">28108445</pub-id></mixed-citation></ref><ref id="B40"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Lang</surname><given-names>A.S.,</given-names></name><name name-style="western"><surname>Bocksberger</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Stech</surname><given-names>M.</given-names></name></person-group>
<year>2015</year>
<article-title>Phylogeny and species delimitations in European <italic>Dicranum</italic> (Dicranaceae, Bryophyta) inferred from nuclear and plastid DNA.</article-title><source>Mol. Phylogenet. Evol.</source><volume>92</volume>:<fpage>217</fpage>&#x02013;<lpage>225</lpage>.<pub-id pub-id-type="pmid">26149758</pub-id></mixed-citation></ref><ref id="B41"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Leach&#x000e9;</surname><given-names>A.D.,</given-names></name><name name-style="western"><surname>Fujita</surname><given-names>M.K.</given-names></name></person-group>
<year>2010</year>
<article-title>Bayesian species delimitation in West African forest geckos (<italic>Hemidactylus fasciatus</italic>).</article-title><source>Proc. Biol. Sci.</source><volume>277</volume>:<fpage>3071</fpage>&#x02013;<lpage>3077</lpage>.<pub-id pub-id-type="pmid">20519219</pub-id></mixed-citation></ref><ref id="B42"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Leach&#x000e9;</surname><given-names>A.D.,</given-names></name><name name-style="western"><surname>Fujita</surname><given-names>M.K.,</given-names></name><name name-style="western"><surname>Minin</surname><given-names>V.N.,</given-names></name><name name-style="western"><surname>Bouckaert</surname><given-names>R.R.</given-names></name></person-group>
<year>2014</year>
<article-title>Species delimitation using genome-wide SNP data.</article-title><source>Syst. Biol.</source><volume>63</volume>:<fpage>534</fpage>&#x02013;<lpage>542</lpage>.<pub-id pub-id-type="pmid">24627183</pub-id></mixed-citation></ref><ref id="B43"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Luo</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Lan</surname><given-names>H.,</given-names></name><name name-style="western"><surname>Ling</surname><given-names>C.,</given-names></name><name name-style="western"><surname>Zhang</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Shi</surname><given-names>L.,</given-names></name><name name-style="western"><surname>Ho</surname><given-names>S.Y.W.,</given-names></name><name name-style="western"><surname>Zhu</surname><given-names>C.</given-names></name></person-group>
<year>2015</year>
<article-title>A simulation study of sample size for DNA barcoding.</article-title><source>Ecol. Evol.</source><volume>5</volume>:<fpage>5869</fpage>&#x02013;<lpage>5879</lpage>.<pub-id pub-id-type="pmid">26811761</pub-id></mixed-citation></ref><ref id="B44"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Mallo</surname><given-names>D.,</given-names></name><name name-style="western"><surname>De Oliveira Martins</surname><given-names>L.,</given-names></name><name name-style="western"><surname>Posada</surname><given-names>D.</given-names></name></person-group>
<year>2014</year>
<article-title>Unsorted homology within locus and species trees.</article-title><source>Syst. Biol.</source><volume>63</volume>:<fpage>988</fpage>&#x02013;<lpage>992</lpage>.<pub-id pub-id-type="pmid">25077514</pub-id></mixed-citation></ref><ref id="B45"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Mallo</surname><given-names>D.,</given-names></name><name name-style="western"><surname>De Oliveira Martins</surname><given-names>L.,</given-names></name><name name-style="western"><surname>Posada</surname><given-names>D.</given-names></name></person-group>
<year>2016</year>
<article-title>SimPhy: phylogenomic simulation of gene, locus, and species trees.</article-title><source>Syst. Biol.</source><volume>65</volume>:<fpage>334</fpage>&#x02013;<lpage>344</lpage>.<pub-id pub-id-type="pmid">26526427</pub-id></mixed-citation></ref><ref id="B46"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Mallo</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Posada</surname><given-names>D.</given-names></name></person-group>
<year>2016</year>
<article-title>Multilocus inference of species trees and DNA barcoding.</article-title><source>Phil. Trans. R. Soc. Lond B Biol. Sci.</source><volume>371</volume>:<fpage>20150335</fpage>.<pub-id pub-id-type="pmid">27481787</pub-id></mixed-citation></ref><ref id="B47"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Mason</surname><given-names>V.C.,</given-names></name><name name-style="western"><surname>Li</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Minx</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Schmitz</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Churakov</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Doronina</surname><given-names>L.,</given-names></name><name name-style="western"><surname>Melin</surname><given-names>A.D.,</given-names></name><name name-style="western"><surname>Dominy</surname><given-names>N.J.,</given-names></name><name name-style="western"><surname>Lim</surname><given-names>N.T,</given-names></name><name name-style="western"><surname>Springer</surname><given-names>M.S.,</given-names></name><name name-style="western"><surname>Wilson</surname><given-names>R.K.,</given-names></name><name name-style="western"><surname>Warren</surname><given-names>W.C.,</given-names></name><name name-style="western"><surname>Helgen</surname><given-names>K.M.,</given-names></name><name name-style="western"><surname>Murphy</surname><given-names>W.J.</given-names></name></person-group>
<year>2016</year>
<article-title>Genomic analysis reveals hidden biodiversity within colugos, the sister group to primates.</article-title><source>Science</source><volume>2</volume>:<fpage>e1600633</fpage>.</mixed-citation></ref><ref id="B48"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name name-style="western"><surname>Mayr</surname><given-names>E.</given-names></name></person-group>
<year>1942</year>
<article-title>Systematics and the origin of species.</article-title><publisher-loc>New York</publisher-loc>: <publisher-name>Columbia University Press</publisher-name>.</mixed-citation></ref><ref id="B49"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Michener</surname><given-names>C.D.</given-names></name></person-group>
<year>1970</year>
<article-title>Diverse approaches to systematics.</article-title><source>Evol. Biol.</source><volume>4</volume>:<fpage>1</fpage>&#x02013;<lpage>38</lpage>.</mixed-citation></ref><ref id="B50"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Monaghan</surname><given-names>M.T.,</given-names></name><name name-style="western"><surname>Wild</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Elliot</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Fujisawa</surname><given-names>T.,</given-names></name><name name-style="western"><surname>Balke</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Inward</surname><given-names>D.J.G.,</given-names></name><name name-style="western"><surname>Lees</surname><given-names>D.C.,</given-names></name><name name-style="western"><surname>Ranaivosolo</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Eggleton</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Barraclough</surname><given-names>T.G.,</given-names></name><name name-style="western"><surname>Vogler</surname><given-names>A.P.</given-names></name></person-group>
<year>2009</year>
<article-title>Accelerated species inventory on Madagascar using coalescent-based models of species delineation.</article-title><source>Syst. Biol.</source><volume>58</volume>:<fpage>298</fpage>&#x02013;<lpage>311</lpage>.<pub-id pub-id-type="pmid">********</pub-id></mixed-citation></ref><ref id="B51"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Moritz</surname><given-names>C.,</given-names></name><name name-style="western"><surname>Pratt</surname><given-names>R.C.,</given-names></name><name name-style="western"><surname>Bank</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Bourke</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Bragg</surname><given-names>J.G.,</given-names></name><name name-style="western"><surname>Doughty</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Keogh</surname><given-names>J.S.,</given-names></name><name name-style="western"><surname>Laver</surname><given-names>R.J.,</given-names></name><name name-style="western"><surname>Potter</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Teasdale</surname><given-names>L.C.,</given-names></name><name name-style="western"><surname>Tedeschi</surname><given-names>L.G.,</given-names></name><name name-style="western"><surname>Oliver</surname><given-names>P.M.</given-names></name></person-group>
<year>2018</year>
<article-title>Cryptic lineage diversity, body size divergence and sympatry in a species complex of Australian lizards (<italic>Gehyra</italic>).</article-title><source>Evolution</source><volume>72</volume>:<fpage>54</fpage>&#x02013;<lpage>66</lpage>.<pub-id pub-id-type="pmid">29067680</pub-id></mixed-citation></ref><ref id="B52"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Nieto-Montes de Oca</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Barley</surname><given-names>A.J.,</given-names></name><name name-style="western"><surname>Meza-L&#x000e1;zaro</surname><given-names>R.N.,</given-names></name><name name-style="western"><surname>Garc&#x000ed;a-V&#x000e1;zquez</surname><given-names>U.O.,</given-names></name><name name-style="western"><surname>Zamora-Abrego</surname><given-names>J.G.,</given-names></name><name name-style="western"><surname>Thomson</surname><given-names>R.C.,</given-names></name><name name-style="western"><surname>Leach&#x000e9;</surname><given-names>A.D.</given-names></name></person-group>
<year>2017</year>
<article-title>Phylogenomics and species delimitation in the knob-scaled lizards of the genus <italic>Xenosaurus</italic> (Squamata: Xenosauridae) using ddRADseq data reveal a substantial underestimation of diversity.</article-title><source>Mol. Phylogenet. Evol.</source><volume>106</volume>:<fpage>241</fpage>&#x02013;<lpage>253</lpage>.<pub-id pub-id-type="pmid">27720785</pub-id></mixed-citation></ref><ref id="B53"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Oliveira</surname><given-names>E.F.,</given-names></name><name name-style="western"><surname>Gehara</surname><given-names>M.,</given-names></name><name name-style="western"><surname>S&#x000e3;o-Pedro</surname><given-names>V.A.,</given-names></name><name name-style="western"><surname>Chen</surname><given-names>X.,</given-names></name><name name-style="western"><surname>Myers</surname><given-names>E.A.,</given-names></name><name name-style="western"><surname>Burbrink</surname><given-names>F.T.,</given-names></name><name name-style="western"><surname>Mesquita</surname><given-names>D.O.,</given-names></name><name name-style="western"><surname>Garda</surname><given-names>A.A.,</given-names></name><name name-style="western"><surname>Colli</surname><given-names>G.R.,</given-names></name><name name-style="western"><surname>Rodrigues</surname><given-names>M.T.,</given-names></name><name name-style="western"><surname>Arias</surname><given-names>F.J.,</given-names></name><name name-style="western"><surname>Zaher</surname><given-names>H.,</given-names></name><name name-style="western"><surname>Santos</surname><given-names>R.M.L.,</given-names></name><name name-style="western"><surname>Costa</surname><given-names>G.C.</given-names></name></person-group>
<year>2015</year>
<article-title>Speciation with gene flow in whiptail lizards from a Neotropical xeric biome.</article-title><source>Mol. Ecol.</source><volume>24</volume>:<fpage>5957</fpage>&#x02013;<lpage>5975</lpage>.<pub-id pub-id-type="pmid">26502084</pub-id></mixed-citation></ref><ref id="B54"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Ollerton</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Erenler</surname><given-names>H.,</given-names></name><name name-style="western"><surname>Edwards</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Crockett</surname><given-names>R.</given-names></name></person-group>
<year>2014</year>
<article-title>Extinctions of aculeate pollinators in Britain and the role of large-scale agricultural changes.</article-title><source>Science</source><volume>346</volume>:<fpage>1360</fpage>&#x02013;<lpage>1362</lpage>.<pub-id pub-id-type="pmid">25504719</pub-id></mixed-citation></ref><ref id="B55"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Paz</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Crawford</surname><given-names>A.J.</given-names></name></person-group>
<year>2012</year>
<article-title>Molecular-based rapid inventories of sympatric diversity: a comparison of DNA barcode clustering methods applied to geography-based vs clade-based sampling of amphibians.</article-title><source>J. Biosci.</source><volume>37</volume>:<fpage>887</fpage>&#x02013;<lpage>896</lpage>.<pub-id pub-id-type="pmid">23107924</pub-id></mixed-citation></ref><ref id="B56"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Pentinsaari</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Vos</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Mutanen</surname><given-names>M.</given-names></name></person-group>
<year>2017</year>
<article-title>Algorithmic single-locus species delimitation: effects of sampling effort, variation and nonmonophyly in four methods and 1870 species of beetles.</article-title><source>Mol. Ecol. Resour.</source><volume>17</volume>:<fpage>393</fpage>&#x02013;<lpage>404</lpage>.<pub-id pub-id-type="pmid">27292571</pub-id></mixed-citation></ref><ref id="B57"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Pons</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Barraclough</surname><given-names>T.G.,</given-names></name><name name-style="western"><surname>Gomes-Zurita</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Cardoso</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Duran</surname><given-names>D.P.,</given-names></name><name name-style="western"><surname>Hazell</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Kamoun</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Sumlin</surname><given-names>W.D.,</given-names></name><name name-style="western"><surname>Vogler</surname><given-names>A.P.</given-names></name></person-group>
<year>2006</year>
<article-title>Sequence-based species delimitation for the DNA taxonomy of undescribed insects.</article-title><source>Syst. Biol.</source><volume>55</volume>:<fpage>595</fpage>&#x02013;<lpage>609</lpage>.<pub-id pub-id-type="pmid">16967577</pub-id></mixed-citation></ref><ref id="B58"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Potter</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Bragg</surname><given-names>J.G.,</given-names></name><name name-style="western"><surname>Peter</surname><given-names>B.M.,</given-names></name><name name-style="western"><surname>Bi</surname><given-names>K.,</given-names></name><name name-style="western"><surname>Moritz</surname><given-names>C.</given-names></name></person-group>
<year>2016</year>
<article-title>Phylogenomics at the tips: inferring lineages and their demographic history in a tropical lizard, <italic>Carlia amax</italic>.</article-title><source>Mol. Ecol.</source><volume>25</volume>:<fpage>1367</fpage>&#x02013;<lpage>1380</lpage>.<pub-id pub-id-type="pmid">26818481</pub-id></mixed-citation></ref><ref id="B59"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Previ&#x00161;i&#x00107;</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Gelemanovi&#x00107;</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Urbani&#x0010d;</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Ternjej</surname><given-names>I.</given-names></name></person-group>
<year>2016</year>
<article-title>Cryptic diversity in the Western Balkan endemic copepod: four species in one?</article-title><source>Mol. Phylogenet. Evol.</source><volume>100</volume>:<fpage>124</fpage>&#x02013;<lpage>134</lpage>.<pub-id pub-id-type="pmid">27063254</pub-id></mixed-citation></ref><ref id="B60"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Puillandre</surname><given-names>N.,</given-names></name><name name-style="western"><surname>Lambert</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Brouillet</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Achaz</surname><given-names>G.</given-names></name></person-group>
<year>2012</year>
<article-title>ABGD, automatic barcode gap discovery for primary species delimitation.</article-title><source>Mol. Ecol.</source><volume>21</volume>:<fpage>1864</fpage>&#x02013;<lpage>1877</lpage>.<pub-id pub-id-type="pmid">21883587</pub-id></mixed-citation></ref><ref id="B61"><mixed-citation publication-type="book">
<collab>R Core Team</collab>. <year>2016</year>
<article-title>R: a language and environment for statistical computing.</article-title><publisher-loc>Vienna, Austria</publisher-loc>: <publisher-name>R Foundation for Statistical Computing.</publisher-name> v3.3.2. Available from: URL <ext-link ext-link-type="uri" xlink:href="https://www.R-project.org/">https://www.R-project.org/</ext-link>.</mixed-citation></ref><ref id="B62"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Rambaut</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Grassly</surname><given-names>N.C.</given-names></name></person-group>
<year>1997</year>
<article-title>Seq-Gen: an application for the Monte Carlo simulation of DNA sequence evolution along phylogenetic trees.</article-title><source>Comput. Appl. Biosci.</source><volume>13</volume>:<fpage>235</fpage>&#x02013;<lpage>238</lpage>.<pub-id pub-id-type="pmid">9183526</pub-id></mixed-citation></ref><ref id="B63"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Rannala</surname><given-names>B.,</given-names></name><name name-style="western"><surname>Yang</surname><given-names>Z.</given-names></name></person-group>
<year>2003</year>
<article-title>Bayes estimation of species divergence times and ancestral population sizes using DNA sequences from multiple loci.</article-title><source>Genetics</source><volume>164</volume>:<fpage>1645</fpage>&#x02013;<lpage>1656</lpage>.<pub-id pub-id-type="pmid">12930768</pub-id></mixed-citation></ref><ref id="B64"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Ratnasingham</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Hebert</surname><given-names>P.D.N.</given-names></name></person-group>
<year>2013</year>
<article-title>A DNA-based registry for all animal species: the barcode index number (BIN) system.</article-title><source>PLOS ONE</source><volume>8</volume>:<fpage>e66213</fpage>.<pub-id pub-id-type="pmid">23861743</pub-id></mixed-citation></ref><ref id="B65"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Renner</surname><given-names>M.A.,</given-names></name><name name-style="western"><surname>Heslewood</surname><given-names>M.M.,</given-names></name><name name-style="western"><surname>Patzak</surname><given-names>S.D.,</given-names></name><name name-style="western"><surname>Sch&#x000e4;fer-Verwimp</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Heinrichs</surname><given-names>J.</given-names></name></person-group>
<year>2017</year>
<article-title>By how much do we underestimate species diversity of liverworts using morphological evidence? An example from Australasian <italic>Plagiochila</italic> (Plagiochilaceae: Jungermanniopsida).</article-title><source>Mol. Phylogenet. Evol.</source><volume>107</volume>:<fpage>576</fpage>&#x02013;<lpage>593</lpage>.<pub-id pub-id-type="pmid">28007566</pub-id></mixed-citation></ref><ref id="B66"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Rosen</surname><given-names>D.E.</given-names></name></person-group>
<year>1979</year>
<article-title>Fishes from the uplands and intermontane basins of Guatemala: revisionary studies and comparative geography.</article-title><source>Bull. Am. Mus. Nat. Hist.</source><volume>162</volume>:<fpage>267</fpage>&#x02013;<lpage>376</lpage>.</mixed-citation></ref><ref id="B67"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Rosindell</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Cornell</surname><given-names>S.J.,</given-names></name><name name-style="western"><surname>Hubbell</surname><given-names>S.P.,</given-names></name><name name-style="western"><surname>Etienne</surname><given-names>R.S.</given-names></name></person-group>
<year>2010</year>
<article-title>Protracted speciation revitalizes the neutral theory of biodiversity.</article-title><source>Ecol. Lett.</source><volume>13</volume>:<fpage>716</fpage>&#x02013;<lpage>727</lpage>.<pub-id pub-id-type="pmid">20584169</pub-id></mixed-citation></ref><ref id="B68"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Ruane</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Bryson</surname><given-names>R.W.</given-names><suffix>Jr.,</suffix></name><name name-style="western"><surname>Pyron</surname><given-names>R.A.,</given-names></name><name name-style="western"><surname>Burbrink</surname><given-names>F.T.</given-names></name></person-group>
<year>2014</year>
<article-title>Coalescent species delimitation in milksnakes (Genus <italic>Lampropeltis</italic>) and impacts on phylogenetic comparative analyses.</article-title><source>Syst. Biol.</source><volume>63</volume>:<fpage>231</fpage>&#x02013;<lpage>250</lpage>.<pub-id pub-id-type="pmid">24335429</pub-id></mixed-citation></ref><ref id="B69"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Rubinoff</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Cameron</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Will</surname><given-names>K.</given-names></name></person-group>
<year>2006a</year>
<article-title>A genomic perspective on the shortcomings of mitochondrial DNA for &#x0201c;barcoding&#x0201d; identification.</article-title><source>J. Hered.</source><volume>97</volume>:<fpage>581</fpage>&#x02013;<lpage>594</lpage>.<pub-id pub-id-type="pmid">17135463</pub-id></mixed-citation></ref><ref id="B70"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Rubinoff</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Cameron</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Will</surname><given-names>K.</given-names></name></person-group>
<year>2006b</year>
<article-title>Are plant DNA barcodes a search for the Holy Grail?</article-title><source>Trends Ecol. Evol.</source><volume>21</volume>:<fpage>1</fpage>&#x02013;<lpage>2</lpage>.<pub-id pub-id-type="pmid">16701459</pub-id></mixed-citation></ref><ref id="B71"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Sanderson</surname><given-names>M.J.</given-names></name></person-group>
<year>2003</year>
<article-title>r8s: inferring absolute rates of molecular evolution and divergence times in the absence of a molecular clock.</article-title><source>Bioinformatics</source><volume>19</volume>:<fpage>301</fpage>&#x02013;<lpage>302</lpage>.<pub-id pub-id-type="pmid">12538260</pub-id></mixed-citation></ref><ref id="B72"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Sokal</surname><given-names>R.R.,</given-names></name><name name-style="western"><surname>Crovello</surname><given-names>T.J.</given-names></name></person-group>
<year>1970</year>
<article-title>The biological species concept: a critical evaluation.</article-title><source>Am. Nat.</source><volume>104</volume>:<fpage>127</fpage>&#x02013;<lpage>153</lpage>.</mixed-citation></ref><ref id="B73"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Stamatakis</surname><given-names>A.</given-names></name></person-group>
<year>2014</year>
<article-title>RAxML version 8: a tool for phylogenetic analysis and post-analysis of large phylogenies.</article-title><source>Bioinformatics</source><volume>30</volume>:<fpage>1312</fpage>&#x02013;<lpage>1313</lpage>.<pub-id pub-id-type="pmid">24451623</pub-id></mixed-citation></ref><ref id="B74"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Stiller</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Molak</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Prost</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Rabeder</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Baryshnikov</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Rosendahl</surname><given-names>W.,</given-names></name><name name-style="western"><surname>M&#x000fc;nzel</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Bocherens</surname><given-names>H.,</given-names></name><name name-style="western"><surname>Grandal-d&#x02019;Anglade</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Hilpert</surname><given-names>B.,</given-names></name><name name-style="western"><surname>Germonpr&#x000e9;</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Stasyk</surname><given-names>O.,</given-names></name><name name-style="western"><surname>Pinhasi</surname><given-names>R.,</given-names></name><name name-style="western"><surname>Tintori</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Rohland</surname><given-names>N.,</given-names></name><name name-style="western"><surname>Mohandesan</surname><given-names>E.,</given-names></name><name name-style="western"><surname>Ho</surname><given-names>S.Y.W.,</given-names></name><name name-style="western"><surname>Hofreiter</surname><given-names>M.,</given-names></name><name name-style="western"><surname>Knapp</surname><given-names>M.</given-names></name></person-group>
<year>2014</year>
<source>Quat. Int.</source><volume>339&#x02013;340</volume>:<fpage>224</fpage>&#x02013;<lpage>231</lpage>.</mixed-citation></ref><ref id="B75"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Sukumaran</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Knowles</surname><given-names>L.L.</given-names></name></person-group>
<year>2017</year>
<article-title>Multispecies coalescent delimits structure, not species.</article-title><source>Proc. Natl. Acad. Sci. USA</source><volume>114</volume>:<fpage>1607</fpage>&#x02013;<lpage>1612</lpage>.<pub-id pub-id-type="pmid">28137871</pub-id></mixed-citation></ref><ref id="B76"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Talavera</surname><given-names>G.,</given-names></name><name name-style="western"><surname>Dinc&#x00103;</surname><given-names>V.,</given-names></name><name name-style="western"><surname>Vila</surname><given-names>R.</given-names></name></person-group>
<year>2013</year>
<article-title>Factors affecting species delimitations with the GMYC model: insights from a butterfly survey.</article-title><source>Methods Ecol. Evol.</source><volume>4</volume>:<fpage>1101</fpage>&#x02013;<lpage>1110</lpage>.</mixed-citation></ref><ref id="B77"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Tang</surname><given-names>C.Q.,</given-names></name><name name-style="western"><surname>Humphreys</surname><given-names>A.M.,</given-names></name><name name-style="western"><surname>Fontaneto</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Barraclough</surname><given-names>T.G.</given-names></name></person-group>
<year>2014</year>
<article-title>Effects of phylogenetic reconstruction method on the robustness of species delimitation using single-locus data.</article-title><source>Methods Ecol. Evol.</source><volume>5</volume>:<fpage>1086</fpage>&#x02013;<lpage>1094</lpage>.<pub-id pub-id-type="pmid">25821577</pub-id></mixed-citation></ref><ref id="B78"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Tautz</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Arctander</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Minelli</surname><given-names>A.,</given-names></name><name name-style="western"><surname>Thomas</surname><given-names>R.H.,</given-names></name><name name-style="western"><surname>Vogler</surname><given-names>A.P.</given-names></name></person-group>
<year>2003</year>
<article-title>A plea for DNA taxonomy.</article-title><source>Trends Ecol. Evol.</source><volume>18</volume>:<fpage>70</fpage>&#x02013;<lpage>74</lpage>.</mixed-citation></ref><ref id="B79"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Vogler</surname><given-names>A.P.,</given-names></name><name name-style="western"><surname>Monaghan</surname><given-names>M.T.</given-names></name></person-group>
<year>2007</year>
<article-title>Recent advances in DNA taxonomy.</article-title><source>J. Zool. Syst. Evol. Res.</source><volume>45</volume>:<fpage>1</fpage>&#x02013;<lpage>10</lpage>.</mixed-citation></ref><ref id="B80"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Wang</surname><given-names>C.,</given-names></name><name name-style="western"><surname>Agrawal</surname><given-names>S.,</given-names></name><name name-style="western"><surname>Laudien</surname><given-names>J.,</given-names></name><name name-style="western"><surname>H&#x000e4;ussermann</surname><given-names>V.,</given-names></name><name name-style="western"><surname>Held</surname><given-names>C.</given-names></name></person-group>
<year>2016</year>
<article-title>Discrete phenotypes are not underpinned by genome-wide genetic differentiation in the squat lobster <italic>Munida gregaria</italic> (Crustacea: Decapoda: Munididae): a multi-marker study covering the Patagonian shelf.</article-title><source>BMC Evol. Biol.</source><volume>16</volume>:<fpage>258</fpage>.<pub-id pub-id-type="pmid">27903261</pub-id></mixed-citation></ref><ref id="B81"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Waugh</surname><given-names>J.</given-names></name></person-group>
<year>2007</year>
<article-title>DNA barcoding in animal species: progress, potential and pitfalls.</article-title><source>BioEssays</source><volume>29</volume>:<fpage>188</fpage>&#x02013;<lpage>197</lpage>.<pub-id pub-id-type="pmid">17226815</pub-id></mixed-citation></ref><ref id="B82"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Will</surname><given-names>K.W.,</given-names></name><name name-style="western"><surname>Mishler</surname><given-names>B.D.,</given-names></name><name name-style="western"><surname>Wheeler</surname><given-names>Q.D.</given-names></name></person-group>
<year>2005</year>
<article-title>The perils of DNA barcoding and the need for integrative taxonomy.</article-title><source>Syst. Biol.</source><volume>54</volume>:<fpage>844</fpage>&#x02013;<lpage>851</lpage>.<pub-id pub-id-type="pmid">16243769</pub-id></mixed-citation></ref><ref id="B83"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Yang</surname><given-names>Z.</given-names></name></person-group>
<year>2015</year>
<article-title>The BPP program for species tree estimation and species delimitation.</article-title><source>Curr. Zool.</source><volume>61</volume>:<fpage>854</fpage>&#x02013;<lpage>865</lpage>.</mixed-citation></ref><ref id="B84"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Yang</surname><given-names>Z.,</given-names></name><name name-style="western"><surname>Rannala</surname><given-names>B.</given-names></name></person-group>
<year>2010</year>
<article-title>Bayesian species delimitation using multilocus sequence data.</article-title><source>Proc. Natl. Acad. Sci. USA</source><volume>107</volume>:<fpage>9264</fpage>&#x02013;<lpage>9269</lpage>.<pub-id pub-id-type="pmid">20439743</pub-id></mixed-citation></ref><ref id="B85"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Yang</surname><given-names>Z.,</given-names></name><name name-style="western"><surname>Rannala</surname><given-names>B.</given-names></name></person-group>
<year>2014</year>
<article-title>Unguided species delimitation using DNA sequence data from multiple loci.</article-title><source>Mol. Biol. Evol.</source><volume>31</volume>:<fpage>3125</fpage>&#x02013;<lpage>3135</lpage>.<pub-id pub-id-type="pmid">25274273</pub-id></mixed-citation></ref><ref id="B86"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Yang</surname><given-names>Z.,</given-names></name><name name-style="western"><surname>Rannala</surname><given-names>B.</given-names></name></person-group>
<year>2017</year>
<article-title>Bayesian species identification under the multispecies coalescent provides significant improvements to DNA barcoding analyses.</article-title><source>Mol. Ecol.</source><volume>26</volume>:<fpage>3028</fpage>&#x02013;<lpage>3036</lpage>.<pub-id pub-id-type="pmid">28281309</pub-id></mixed-citation></ref><ref id="B87"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Yule</surname><given-names>G.U.</given-names></name></person-group>
<year>1924</year>
<article-title>A mathematical theory of evolution, based on the conclusions</article-title> of <comment>Dr. J. C. Willis, F.R.S. Phil.</comment><source>Trans. R. Soc. Lond. B.</source><volume>213</volume>:<fpage>21</fpage>&#x02013;<lpage>87</lpage>.</mixed-citation></ref><ref id="B88"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Zhang</surname><given-names>C.,</given-names></name><name name-style="western"><surname>Rannala</surname><given-names>B.,</given-names></name><name name-style="western"><surname>Yang</surname><given-names>Z.</given-names></name></person-group>
<year>2014</year>
<article-title>Bayesian species delimitation can be robust to guide-tree inference errors.</article-title><source>Syst. Biol.</source><volume>63</volume>:<fpage>993</fpage>&#x02013;<lpage>1004</lpage>.<pub-id pub-id-type="pmid">25096853</pub-id></mixed-citation></ref><ref id="B89"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Zhang</surname><given-names>C.,</given-names></name><name name-style="western"><surname>Zhang</surname><given-names>D.,</given-names></name><name name-style="western"><surname>Zhu</surname><given-names>T.,</given-names></name><name name-style="western"><surname>Yang</surname><given-names>Z.</given-names></name></person-group>
<year>2011</year>
<article-title>Evaluation of a Bayesian coalescent method of species delimitation.</article-title><source>Syst. Biol.</source><volume>60</volume>:<fpage>747</fpage>&#x02013;<lpage>761</lpage>.<pub-id pub-id-type="pmid">21876212</pub-id></mixed-citation></ref><ref id="B90"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name name-style="western"><surname>Zhang</surname><given-names>J.,</given-names></name><name name-style="western"><surname>Kapli</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Pavlidis</surname><given-names>P.,</given-names></name><name name-style="western"><surname>Stamatakis</surname><given-names>A.</given-names></name></person-group>
<year>2013</year>
<article-title>A general species delimitation method with applications to phylogenetic placements.</article-title><source>Bioinformatics</source><volume>29</volume>:<fpage>2869</fpage>&#x02013;<lpage>2876</lpage>.<pub-id pub-id-type="pmid">23990417</pub-id></mixed-citation></ref></ref-list></back></article>