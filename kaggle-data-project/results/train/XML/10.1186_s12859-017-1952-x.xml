<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5719527</article-id><article-id pub-id-type="publisher-id">1952</article-id><article-id pub-id-type="doi">10.1186/s12859-017-1952-x</article-id><article-categories><subj-group subj-group-type="heading"><subject>Methodology Article</subject></subj-group></article-categories><title-group><article-title>CONSTAX: a tool for improved taxonomic resolution of environmental fungal ITS sequences</article-title></title-group><contrib-group><contrib contrib-type="author" equal-contrib="yes"><name><surname>Gdanetz</surname><given-names>Kristi</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes" equal-contrib="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-1589-947X</contrib-id><name><surname>Benucci</surname><given-names>Gian Maria Niccol&#x000f2;</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Vande Pol</surname><given-names>Natalie</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Bonito</surname><given-names>Gregory</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2150 1785</institution-id><institution-id institution-id-type="GRID">grid.17088.36</institution-id><institution>Department of Plant Biology, </institution><institution>Michigan State University, </institution></institution-wrap>East Lansing, Michigan 48824 USA </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2150 1785</institution-id><institution-id institution-id-type="GRID">grid.17088.36</institution-id><institution>Department of Plant, Soil, &#x00026; Microbial Sciences, </institution><institution>Michigan State University, </institution></institution-wrap>East Lansing, Michigan 48824 USA </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2150 1785</institution-id><institution-id institution-id-type="GRID">grid.17088.36</institution-id><institution>Department of Microbiology &#x00026; Molecular Genetics, </institution><institution>Michigan State University, </institution></institution-wrap>East Lansing, Michigan 48824 USA </aff></contrib-group><pub-date pub-type="epub"><day>6</day><month>12</month><year>2017</year></pub-date><pub-date pub-type="pmc-release"><day>6</day><month>12</month><year>2017</year></pub-date><pub-date pub-type="collection"><year>2017</year></pub-date><volume>18</volume><elocation-id>538</elocation-id><history><date date-type="received"><day>27</day><month>8</month><year>2017</year></date><date date-type="accepted"><day>22</day><month>11</month><year>2017</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2017</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">One of the most crucial steps in high-throughput sequence-based microbiome studies is the taxonomic assignment of sequences belonging to operational taxonomic units (OTUs). Without taxonomic classification, functional and biological information of microbial communities cannot be inferred or interpreted. The internal transcribed spacer (ITS) region of the ribosomal DNA is the conventional marker region for fungal community studies. While bioinformatics pipelines that cluster reads into OTUs have received much attention in the literature, less attention has been given to the taxonomic classification of these sequences, upon which biological inference is dependent.</p></sec><sec><title>Results</title><p id="Par2">Here we compare how three common fungal OTU taxonomic assignment tools (RDP Classifier, UTAX, and SINTAX) handle ITS fungal sequence data. The classification power, defined as the proportion of assigned OTUs at a given taxonomic rank, varied among the classifiers. Classifiers were generally consistent (assignment of the same taxonomy to a given OTU) across datasets and ranks; a small number of OTUs were assigned unique classifications across programs. We developed CONSTAX (CONSensus TAXonomy), a Python tool that compares taxonomic classifications of the three programs and merges them into an improved consensus taxonomy. This tool also produces summary classification outputs that are useful for downstream analyses.</p></sec><sec><title>Conclusions</title><p id="Par3">Our results demonstrate that independent taxonomy assignment tools classify unique members of the fungal community, and greater classification power is realized by generating consensus taxonomy of available classifiers with CONSTAX.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12859-017-1952-x) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>taxonomy classifiers</kwd><kwd>RDP</kwd><kwd>SINTAX</kwd><kwd>UPARSE</kwd><kwd>UNOISE</kwd><kwd>ITS</kwd><kwd>mycobiome</kwd><kwd>fungal microbiome</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/*********</institution-id><institution>AgBioResearch, Michigan State University</institution></institution-wrap></funding-source><award-id>GR-16-043</award-id><principal-award-recipient><name><surname>Bonito</surname><given-names>Gregory</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/*********</institution-id><institution>National Science Foundation</institution></institution-wrap></funding-source><award-id>988</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2017</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par11">Next-generation sequencing technologies and high-performance computers define the culture-independent era of microbial ecology. High-throughput sequencing of DNA barcode marker regions, namely the bacterial 16S rRNA gene or fungal internal transcribed spacer (ITS) ribosomal regions, have allowed researchers to characterize complex microbial communities at a depth not previously possible with culture-based methods. Hypervariable regions of the 16S rRNA gene have been extensively studied and adopted by researchers to describe prokaryotic microbial communities, and a mix of ribosomal markers have been used to describe fungal communities [<xref ref-type="bibr" rid="CR1">1</xref>] over the past 25&#x000a0;years [<xref ref-type="bibr" rid="CR2">2</xref>]. The ITS region, comprising the ITS1, 5.8S, and ITS2 segments, was recently selected as the formal DNA barcode for fungi [<xref ref-type="bibr" rid="CR3">3</xref>&#x02013;<xref ref-type="bibr" rid="CR5">5</xref>], although there is a lack of consensus regarding which ITS (ITS1 or ITS2) to utilize as a barcode [<xref ref-type="bibr" rid="CR6">6</xref>&#x02013;<xref ref-type="bibr" rid="CR8">8</xref>]. It remains unclear which of the ITS primer sets has the best resolution for fungal diversity, and papers targeting either ITS segment have been published at near equal frequencies [<xref ref-type="bibr" rid="CR8">8</xref>&#x02013;<xref ref-type="bibr" rid="CR10">10</xref>].</p><p id="Par12">Pipelines for processing fungal ITS amplicon datasets such as CLOTU [<xref ref-type="bibr" rid="CR11">11</xref>], CloVR-ITS [<xref ref-type="bibr" rid="CR12">12</xref>], PIPITS [<xref ref-type="bibr" rid="CR1">1</xref>], and others [<xref ref-type="bibr" rid="CR13">13</xref>] are available in the literature, but most of the tool-development effort has been towards generating nearly automated pipelines for filtering, trimming, and clustering of amplicon reads into operational taxonomic units (OTUs). Less emphasis has been placed on assigning taxonomy to representative OTU sequences in a dataset. Linnaean taxonomy provides a controlled vocabulary that communicates ecological, biological or geographic information. Linking OTUs to functionally meaningful names, which typically depends upon species-level resolution, is key to addressing biological and ecological hypotheses. Processing sequencing reads, in addition to taxonomy assignment of sequences, can be completed using various bioinformatics pipeline tools. The most popular are Mothur [<xref ref-type="bibr" rid="CR14">14</xref>], QIIME [<xref ref-type="bibr" rid="CR15">15</xref>], and USEARCH [<xref ref-type="bibr" rid="CR16">16</xref>]. There are a variety of algorithms to use for the taxonomy assignment step, which include: BLAST [<xref ref-type="bibr" rid="CR17">17</xref>], Ribosomal Database Project (RDP) Na&#x000ef;ve Bayesian Classifier [<xref ref-type="bibr" rid="CR18">18</xref>], UTAX [<xref ref-type="bibr" rid="CR19">19</xref>], and SINTAX [<xref ref-type="bibr" rid="CR20">20</xref>]. The RDP Classifier (RDPC) uses Bayesian statistics to find 8-mers that have higher probability of belonging to a given genus. Based on these conditions, RDPC estimates the probability that an unknown query DNA sequence belongs to the genus [<xref ref-type="bibr" rid="CR18">18</xref>]. The UTAX algorithm looks for k-mer words in common between a query sequence and a known reference sequence, and calculates a score of word counts. The score is used to estimate confidence values for each of the taxonomic levels, which are then trained on the reference database to give an estimate of error rates [<xref ref-type="bibr" rid="CR19">19</xref>]. The SINTAX algorithm predicts taxonomy by using k-mer similarity to identify the top hit in a reference database, and provides bootstrap confidence for all ranks in the prediction [<xref ref-type="bibr" rid="CR20">20</xref>]. Local alignment, most commonly implemented in BLAST [<xref ref-type="bibr" rid="CR17">17</xref>], is still occasionally used for taxonomy assignment of high-throughput sequence datasets. However use of BLAST to identify OTUs in amplicon-based microbiome datasets has low accuracy as demonstrated previously [<xref ref-type="bibr" rid="CR20">20</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>], and discussed by Wang et al. [<xref ref-type="bibr" rid="CR18">18</xref>].</p><p id="Par13">The UNITE reference database is a curated database of all International Nucleotide Sequence Database Collaboration (INSDC) fungal sequences, and is the most commonly used reference database for fungal amplicon analyses [<xref ref-type="bibr" rid="CR23">23</xref>&#x02013;<xref ref-type="bibr" rid="CR25">25</xref>]. Recently the Ribosomal Database Project released the Warcup Fungal Database [<xref ref-type="bibr" rid="CR26">26</xref>], a curated version of UNITE and INSDC. Apart from previously published database comparisons which showed the accuracy of UNITE [<xref ref-type="bibr" rid="CR23">23</xref>] and Warcup fungal databases [<xref ref-type="bibr" rid="CR26">26</xref>], all comparative studies of taxonomy classifiers of which we are aware, have analyzed only prokaryotic organisms [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. Since only a small fraction of microbial species estimated to be on the planet have been described, taxonomic classification is not a trivial task and no algorithm is 100% precise. Several types of classification errors are possible, as highlighted in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>. The RDPC, UTAX, and SINTAX classifiers report a confidence value for the classification given to an OTU so that the user can set a cutoff value below which no name is given. Even though a number of databases and tools have been developed to enable high-throughput analyses of environmental sequences, researchers still need to solve the problems caused by misidentified or insufficiently identified sequences [<xref ref-type="bibr" rid="CR5">5</xref>]. Further, some poorly sampled fungal lineages reduce the ability of a classifier to confidently assign OTUs to the correct fungal lineage regardless of the classification algorithm used.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Types of classifications</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Present in the database?</th><th>Taxon name given?</th><th>Correct name given?</th><th>Result</th><th>Error Type</th></tr></thead><tbody><tr><td>Yes</td><td>Yes</td><td>Yes</td><td>Good assignment</td><td>True positive</td></tr><tr><td>Yes</td><td>Yes</td><td>No</td><td>Misclassification</td><td>False positive</td></tr><tr><td>Yes</td><td>No</td><td>No</td><td>Underclassification</td><td>False negative</td></tr><tr><td>No</td><td>Yes</td><td>No</td><td>Overclassification</td><td>False negative</td></tr><tr><td>No</td><td>No</td><td>No</td><td>Good assignment</td><td>True negative</td></tr></tbody></table></table-wrap>
</p><p id="Par14">This study tested whether established taxonomic classifiers for fungal ITS DNA sequences generate similar profiles of the fungal community. Specifically, we compared the power (proportion of assigned OTUs at a given&#x000a0;level) and consistency (agreement of OTU assignment across classifiers) of the RDPC, UTAX, and SINTAX classification algorithms. Power and consistency were compared across i) ITS1 or ITS2 regions, ii) OTU-clustering approaches, and iii) merged or single stranded reads. Further, we created a Python tool that functions independently of OTU-picking method to merge taxonomy assignments from multiple classifier programs into an improved consensus taxonomy, and generates several output files that can be used for subsequent community analysis.</p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Data accessibility</title><p id="Par15">Sample origins, barcode regions, and accession numbers for all datasets used in the current study can be found in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>. Implementation of the tool presented in this paper requires users to download and install the following software: RDPC [<ext-link ext-link-type="uri" xlink:href="https://github.com/rdpstaff/classifier">https://github.com/rdpstaff/classifier</ext-link>], USEARCH version 8 for UTAX, and USEARCH version 9 or later for SINTAX [<ext-link ext-link-type="uri" xlink:href="http://drive5.com/usearch/download.html">http://drive5.com/usearch/download.html</ext-link>], R v2.15.1 or&#x000a0;later [<ext-link ext-link-type="uri" xlink:href="https://www.r-project.org">https://www.r-project.org</ext-link>], Python version 2.7 [<ext-link ext-link-type="uri" xlink:href="https://www.python.org">https://www.python.org</ext-link>]. Detailed installation and analysis instructions, including all custom scripts used in the analysis and a test dataset are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>, or for download from GitHub: [<ext-link ext-link-type="uri" xlink:href="https://github.com/natalie-vandepol/compare_taxonomy"><underline>https://github.com/natalie-vandepol/compare_taxonomy</underline></ext-link>]. All of the custom Python scripts described in the methods section can be downloaded from the <italic>CONSTAX.tar.gz</italic> file (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). All the steps described in the methods section are automated through the <italic>constax.sh</italic> script, but are included as independent scripts in <italic>CONSTAX.tar.gz</italic> so they can be easily modified to suit the user&#x02019;s needs. An overview of the data analysis workflow is available in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>.<table-wrap id="Tab2"><label>Table 2</label><caption><p>Sample origins, barcode regions, and accession numbers for datasets</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Dataset</th><th>Gene Region</th><th>Read Type</th><th>Sample Origin</th><th>Data Availability</th><th>Reference</th></tr></thead><tbody><tr><td>ITS1-Soil</td><td>ITS1</td><td>2&#x02009;&#x000d7;&#x02009;250&#x000a0;bp</td><td>North American soil</td><td>NCBI SRA SRP035367</td><td>Smith &#x00026; Peay [<xref ref-type="bibr" rid="CR36">36</xref>]</td></tr><tr><td>ITS2-Soil</td><td>ITS2</td><td>2&#x02009;&#x000d7;&#x02009;250&#x000a0;bp</td><td>North American soil</td><td>NCBI SRA SRR1508275</td><td>Oliver et al. [<xref ref-type="bibr" rid="CR37">37</xref>]</td></tr><tr><td>ITS1-Plant</td><td>ITS1</td><td>2&#x02009;&#x000d7;&#x02009;250&#x000a0;bp</td><td>European plants</td><td>MG-RAST 13322</td><td>Agler et al. [<xref ref-type="bibr" rid="CR10">10</xref>]</td></tr><tr><td>ITS2-Plant</td><td>ITS2</td><td>2&#x02009;&#x000d7;&#x02009;250&#x000a0;bp</td><td>European plants</td><td>MG-RAST 13322</td><td>Agler et al. [<xref ref-type="bibr" rid="CR10">10</xref>]</td></tr><tr><td>ITS1-BC<sup>a</sup>
</td><td>ITS1</td><td>1&#x02009;&#x000d7;&#x02009;300&#x000a0;bp</td><td>North American soil</td><td>NCBI SRA SRP079401</td><td>Benucci et al., unpublished</td></tr><tr><td>ITS2-BC<sup>a</sup>
</td><td>ITS2</td><td>1&#x02009;&#x000d7;&#x02009;300&#x000a0;bp</td><td>North American soil</td><td>NCBI SRA SRP079401</td><td>Benucci et al., unpublished</td></tr><tr><td>ITS1-UN<sup>b</sup>
</td><td>ITS1</td><td>1&#x02009;&#x000d7;&#x02009;300&#x000a0;bp</td><td>North American soil</td><td>NCBI SRA SRP079401</td><td>Benucci et al., unpublished</td></tr><tr><td>ITS2-UN<sup>b</sup>
</td><td>ITS2</td><td>1&#x02009;&#x000d7;&#x02009;300&#x000a0;bp</td><td>North American soil</td><td>NCBI SRA SRP079401</td><td>Benucci et al., unpublished</td></tr></tbody></table><table-wrap-foot><p>
<sup>a</sup>
data processed with UPARSE algorithm, OTUs generated with clustering
</p><p>
<sup>b</sup>
data processed with UNOISE&#x000a0;algorithm, ESVs generated with splitting
</p></table-wrap-foot></table-wrap>
<fig id="Fig1"><label>Fig. 1</label><caption><p>Overview of CONSTAX workflow. Bubbles highlighted by gray box are automated through <italic>constax.sh</italic>
</p></caption><graphic xlink:href="12859_2017_1952_Fig1_HTML" id="MO1"/></fig>
</p></sec><sec id="Sec4"><title>Generation of operational taxonomic units</title><p id="Par16">For the ITS1-soil and ITS2-soil datasets&#x000a0;(Table <xref rid="Tab2" ref-type="table">2</xref>), forward and reverse reads were merged with PEAR version 0.9.8 [<xref ref-type="bibr" rid="CR29">29</xref>]. Merged reads were randomly sampled to one million reads to reduce computational time. Reads were quality-filtered, trimmed, dereplicated, clustered at 97% similarity (the standard sequence similarity value), and OTU-calling was performed using USEARCH version 8.1.1831 [<xref ref-type="bibr" rid="CR16">16</xref>]. Analysis of plant datasets (ITS1-plant and ITS2-plant) began with the processed 97% similarity OTUs provided by the authors [<xref ref-type="bibr" rid="CR10">10</xref>].</p><p id="Par166">For the ITS1/2-BC and ITS1/2-UN datasets, reads were quality-filtered as above, but differed in OTU-generation method. First, a clustering algorithm that generated OTUs using the UPARSE [<xref ref-type="bibr" rid="CR19">19</xref>] algorithm was used to call OTUs for ITS1-BC and ITS2-BC. Second, the UNOISE2&#x000a0;algorithm [<xref ref-type="bibr" rid="CR30">30</xref>]&#x000a0;that performed denoising and generated exact sequence variants (ESVs)&#x000a0;[<xref ref-type="bibr" rid="CR31">31</xref>] was used&#x000a0;for ITS1-UN and ITS2-UN. Each set of OTUs and ESVs were randomly sampled to 500 for the comparative taxonomic analysis described in the next section. Sample and abundance data were not used in this study.&#x000a0;The code for the&#x000a0;OTU-picking pipeline described above is available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>.</p></sec><sec id="Sec5"><title>Database formatting and classifier training</title><p id="Par17">The UNITE fungal database [<xref ref-type="bibr" rid="CR23">23</xref>], release 31&#x02013;01-2016, containing 23,264 sequences was used in the current study. A custom script (<italic>FormatRefDB.py</italic>) was developed in Python 2.7 to format the database, starting from the general fasta release, for each classifier to ensure training was completed with identical databases. For RDPC training, custom Python scripts (<italic>subscript_lineage2taxonomyTrain.py</italic>, <italic>subscript_fasta_addFullLineage.py</italic>) were used to give each Species Hypothesis a unique name and remove special text characters. Prior to UTAX training and SINTAX classification, custom Python scripts were used to make minor changes to header lines of the <italic>fasta</italic> file. After formatting, these versions of the UNITE database were used to train classifiers. All the formatting and training scripts above are automated through the <italic>constax.sh</italic> script, users need only specify the location of the reference database.</p></sec><sec id="Sec6"><title>Taxonomy assignment</title><p id="Par18">Taxonomy was assigned to the OTUs with RDPC version 11.5 [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR32">32</xref>], UTAX from USEARCH version 8.1.1831 [<xref ref-type="bibr" rid="CR19">19</xref>, <xref ref-type="bibr" rid="CR33">33</xref>], and SINTAX from USEARCH version 9.2 [<xref ref-type="bibr" rid="CR16">16</xref>]. This step generated three tables (one from each classifier) with a taxonomic assignment at each of the seven ranks of the hierarchy (Kingdom, Phylum, Class, Order, Family, Genus, Species). We used the default settings, a 0.8 cut-off, to serve as a baseline for comparison. Researchers may choose to use less stringent cut-offs, depending on the goals of their studies. The cut-off can be specified in the <italic>config</italic> file contained in <italic>CONSTAX.tar.gz</italic> (Additional file <xref rid="MOESM2" ref-type="media">2</xref>).</p></sec><sec id="Sec7"><title>Post-taxonomy data processing</title><p id="Par19">A custom Python script (<italic>CombineTaxonomy.py</italic>) was developed to standardize the taxonomy table formats, filter the output files at the recommended quality score, and create the consensus taxonomy. Additionally,&#x000a0;the script produces a combined and improved (higher power) taxonomy table by concatenating the information contained in the taxonomy tables from RDPC, UTAX, and SINTAX. Rules developed to merge the taxonomy assignments implemented in the Python script are detailed in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>. Briefly, a majority rule (two out of three OTUs classified) was used when classifiers did not assign the same name to a representative sequence. When there was not a clear majority rule, the name with the highest quality score was chosen. The <italic>CombineTaxonomy.py</italic> script is also automated through the <italic>constax.sh</italic> script<italic>.</italic> All analyses downstream of the consensus OTU assignments were completed in R version 3.3.2 [<xref ref-type="bibr" rid="CR34">34</xref>] and graphs were generated with the R package &#x02018;<italic>ggplot2&#x02019;</italic> [<xref ref-type="bibr" rid="CR35">35</xref>]. R code used to generate the graphs is also available in the <italic>CONSTAX.tar.gz,</italic> and automated through <italic>constax.sh</italic> script.<table-wrap id="Tab3"><label>Table 3</label><caption><p>Rules adopted to generate the combined taxonomy table</p></caption><table frame="hsides" rules="groups"><thead><tr><th>RDP</th><th>UTAX</th><th>SINTAX</th><th>CONSENSUS</th></tr></thead><tbody><tr><td colspan="4">3 taxonomy assignments</td></tr><tr><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon A</italic>
</td></tr><tr><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon B</italic>
</td><td>
<italic>Taxon A</italic>
</td></tr><tr><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon B</italic>
</td><td>
<italic>Taxon C</italic>
</td><td>Use score</td></tr><tr><td colspan="4">2 taxonomy assignments +1 unidentified</td></tr><tr><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon A</italic>
</td><td>Unidentified</td><td>
<italic>Taxon A</italic>
</td></tr><tr><td>
<italic>Taxon A</italic>
</td><td>
<italic>Taxon B</italic>
</td><td>Unidentified</td><td>Use score</td></tr><tr><td colspan="4">1 taxonomy assignment +2 unidentified</td></tr><tr><td>
<italic>Taxon A</italic>
</td><td>Unidentified</td><td>Unidentified</td><td>Taxon A</td></tr></tbody></table></table-wrap>
</p></sec></sec><sec id="Sec8"><title>Results</title><sec id="Sec9"><title>Power of classifiers</title><p id="Par20">Classification power differed across RDPC, UTAX, and SINTAX (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). Also, the total number of assigned OTUs varied across datasets, ITS region, and OTU-generation approach. In general, the highest number of assignments at each level of the taxonomic hierarchy was observed in the ITS1-soil dataset shown in Fig. <xref rid="Fig2" ref-type="fig">2a</xref> [<xref ref-type="bibr" rid="CR36">36</xref>]. Classifications for the ITS2-soil dataset [<xref ref-type="bibr" rid="CR37">37</xref>] follow the same general pattern as ITS1-soil, but overall had lower power (Fig. <xref rid="Fig2" ref-type="fig">2b</xref>). Although, UTAX had higher classification power for some ITS1 datasets at Kingdom level (Fig. <xref rid="Fig2" ref-type="fig">2c</xref>, Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>: Figure S1A), generally, SINTAX had the highest classification power (Fig. <xref rid="Fig2" ref-type="fig">2a-b, d</xref>). ITS1-plant (Fig. <xref rid="Fig2" ref-type="fig">2c</xref>) and ITS2-plant (Fig. <xref rid="Fig2" ref-type="fig">2d</xref>) [<xref ref-type="bibr" rid="CR10">10</xref>] datasets generated a greater number of unidentified OTUs by all three of the classifiers when compared with the soil datasets (Fig. <xref rid="Fig2" ref-type="fig">2</xref>, Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S1). A larger number of identified OTUs were detected for the ITS1-BC and ITS2-BC datasets when OTUs were generated by denoising (Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S1A-B) instead of clustering (Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S1C-D), at all levels except Species. Moreover, a similar pattern was observed with the ITS1-BC and ITS2-BC datasets, more assigned OTUs were observed for ITS2-BC in comparison to ITS1-BC, but not at every rank level (Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S1).<fig id="Fig2"><label>Fig. 2</label><caption><p>Power of classifiers. Distribution of classified and unclassified OTUs for each classifier and across taxonomic level. <bold>a</bold> ITS1-soil dataset from Smith &#x00026; Peay [<xref ref-type="bibr" rid="CR36">36</xref>]. <bold>b</bold> ITS2-soil dataset from Oliver et al. [<xref ref-type="bibr" rid="CR37">37</xref>]. <bold>c</bold> ITS1-plant and <bold>d</bold> ITS2-plant datasets from Angler et al. [<xref ref-type="bibr" rid="CR10">10</xref>]</p></caption><graphic xlink:href="12859_2017_1952_Fig2_HTML" id="MO2"/></fig>
</p><p id="Par21">Depending on the dataset, the number of unidentified OTUs gradually, or sharply, increased at other ranks higher than Kingdom level. Percent improvement of the consensus taxonomy assignments were calculated from maximum and minimum numbers of classifications obtained at any given rank (Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>). With CONSTAX, there was ~1% mean improvement at Kingdom level when the consensus taxonomy was compared with an individual classifier program. At other rank levels, there was 7&#x02013;35% mean improvement. For ITS2 datasets, there was a 1&#x02013;61% percent improvement at Family level (Table <xref rid="Tab4" ref-type="table">4</xref>). For ITS1 datasets there was a 1 to 59% improvement at Family level (Table <xref rid="Tab4" ref-type="table">4</xref>). At Species level there was a 35% mean improvement across all datasets (Table <xref rid="Tab4" ref-type="table">4</xref>). The higher end of these ranges is due to poor classification of OTUs, especially ITS2 OTUs, using UTAX. If the percent improvement is recalculated without UTAX the maximum percent improvement drops from 98% to 52% (Table <xref rid="Tab4" ref-type="table">4</xref>).<table-wrap id="Tab4"><label>Table 4</label><caption><p>Range of percent improvement using&#x000a0;CONSTAX</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Taxnomic Rank<sup>a</sup>
</th><th>Percent Increase<sup>b</sup>
</th><th>ITS1-Soil</th><th>ITS2-Soil</th><th>ITS1-Plant</th><th>ITS2-Plant</th><th>ITS1-BC<sup>c</sup>
</th><th>ITS2-BC<sup>c</sup>
</th><th>ITS1-UN<sup>c,d</sup>
</th><th>ITS2-UN<sup>c,d</sup>
</th><th>Mean Increase</th></tr></thead><tbody><tr><td rowspan="2">Kingdom</td><td>max.</td><td>0.00</td><td>0.00 (1.60)</td><td>0.00 (0.20)</td><td>0.00</td><td>0.00 (1.00)</td><td>0.00 (0.20)</td><td>0.00 (2.20)</td><td>0.00</td><td char="(" align="char" rowspan="2">0.81 (1.14)</td></tr><tr><td>min.</td><td>0.40</td><td>3.00</td><td>1.40</td><td>0.20</td><td>3.60</td><td>1.40</td><td>2.60</td><td>0.40</td></tr><tr><td rowspan="2">Phylum</td><td>max.</td><td>0.47</td><td>1.29</td><td>4.46 (5.25)</td><td>2.84</td><td>6.05</td><td>1.70</td><td>5.57 (7.62)</td><td>2.76</td><td char="(" align="char" rowspan="2">6.83 (6.50)</td></tr><tr><td>min.</td><td>5.21 (4.03)</td><td>13.18 (5.43)</td><td>17.06</td><td>18.01</td><td>11.46</td><td>12.24</td><td>11.73</td><td>8.90</td></tr><tr><td rowspan="2">Class</td><td>max.</td><td>1.58</td><td>1.83</td><td>0.93</td><td>4.89</td><td>3.04</td><td>0.84</td><td>3.98</td><td>3.70</td><td char="(" align="char" rowspan="2">8.56 (5.36)</td></tr><tr><td>min.</td><td>9.23 (2.11)</td><td>24.77 (7.65)</td><td>21.98 (18.27)</td><td>26.63 (22.28)</td><td>18.26 (8.70)</td><td>9.28 (5.49)</td><td>13.94 (5.98)</td><td>9.26 (5.19)</td></tr><tr><td rowspan="2">Order</td><td>max.</td><td>1.69</td><td>2.47</td><td>0.33</td><td>2.58</td><td>2.48</td><td>1.40</td><td>5.75</td><td>3.23</td><td char="(" align="char" rowspan="2">10.94 (5.73)</td></tr><tr><td>min.</td><td>11.83 (4.79)</td><td>27.21 (6.71)</td><td>37.42 (20.86)</td><td>42.58 (24.52)</td><td>19.31 (7.92)</td><td>7.44 (5.58)</td><td>19.91 (8.85)</td><td>11.29 (4.03)</td></tr><tr><td rowspan="2">Family</td><td>max.</td><td>1.36</td><td>3.54</td><td>2.47</td><td>3.16</td><td>1.86</td><td>1.10</td><td>6.88</td><td>6.13</td><td char="(" align="char" rowspan="2">15.72 (6.43)</td></tr><tr><td>min.</td><td>13.9 (1.69)</td><td>30.81 (6.57)</td><td>58.02 (22.63)</td><td>61.05 (26.32)</td><td>20.50 (9.94)</td><td>11.60 (6.08)</td><td>32.80 (8.99)</td><td>27.83 (7.08)</td></tr><tr><td rowspan="2">Genus</td><td>max.</td><td>2.56</td><td>6.25</td><td>2.51</td><td>5.33</td><td>2.40</td><td>1.89</td><td>9.15</td><td>9.03</td><td char="(" align="char" rowspan="2">27.06 (9.04)</td></tr><tr><td>min.</td><td>28.21 (3.85)</td><td>53.13 (8.59)</td><td>88.94 (37.69)</td><td>85.33 (36.00)</td><td>31.20 (7.20)</td><td>35.22 (10.69)</td><td>63.38 (10.56)</td><td>62.58 (9.03)</td></tr><tr><td rowspan="2">Species</td><td>max.</td><td>5.65</td><td>8.51</td><td>2.70</td><td>1.92</td><td>3.19</td><td>1.83</td><td>9.28</td><td>13.68</td><td char="(" align="char" rowspan="2">34.65 (13.20)</td></tr><tr><td>min.</td><td>52.42 (16.13)</td><td>65.96 (14.89)</td><td>98.65 (47.97)</td><td>96.15 (51.92)</td><td>41.49 (9.57)</td><td>51.38 (21.10)</td><td>81.44 (11.34)</td><td>89.47 (17.89)</td></tr></tbody></table><table-wrap-foot><p>
<sup>a</sup>Percent improvement calculated with RDP, SINTAX, and UTAX outputs (numbers in paranthesis calculated without including UTAX, only differing values displayed). Ranges represent minimum and maximum improvement when compared to all three classifiers at a given level</p><p>
<sup>b</sup>Equation to calculate percent increase, where <italic>N</italic>&#x02009;=&#x02009;assigned OTUs. <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \frac{\max\ or\ \min\ N}{consensus\ N}\times 100 $$\end{document}</tex-math><mml:math id="M2" display="inline"><mml:mfrac><mml:mrow><mml:mo>max</mml:mo><mml:mspace width="0.25em"/><mml:mtext mathvariant="italic">or</mml:mtext><mml:mspace width="0.25em"/><mml:mo>min</mml:mo><mml:mspace width="0.25em"/><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">consensus</mml:mtext><mml:mspace width="0.25em"/><mml:mi>N</mml:mi></mml:mrow></mml:mfrac><mml:mo>&#x000d7;</mml:mo><mml:mn>100</mml:mn></mml:math><inline-graphic xlink:href="12859_2017_1952_Article_IEq1.gif"/></alternatives></inline-formula>
</p><p>
<sup>c</sup>Reads are forward (ITS1) or reverse (ITS2), not merged read pairs</p><p>
<sup>d</sup>Dataset was processed with denoising instead of clustering</p></table-wrap-foot></table-wrap>
</p></sec><sec id="Sec10"><title>Consistency of classifiers</title><p id="Par22">Generally, all the classifiers were consistent in OTU assignments. Based on the consensus taxonomy tables, no bias was observed toward a fungal lineage from any of the classifiers. Nearly all OTUs were identified at Kingdom level (Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref>, Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>: Table S1). There were few examples across the datasets where a single OTU was placed into a unique lineage by one or more of the classifiers. Only 1.24%&#x000a0;&#x02213;&#x000a0;0.006 (st. dev.) of OTUs were differentially assigned across the datasets. This differential assignment phenomenon was most frequently observed at Kingdom level where OTUs were placed with low confidence into either Kingdom Fungi or Protista (Table <xref rid="Tab5" ref-type="table">5</xref>). These OTUs were rarely assigned at a higher level after Kingdom, and never higher than Class; they may be novel sequences, PCR, or sequencing errors. Across all datasets used in the present study (4000 OTUs/ESVs), there were two examples of OTUs assigned to unique fungal lineages. These were found only in ITS1-BC and ITS2-BC datasets (Table <xref rid="Tab5" ref-type="table">5</xref>). The ITS1-BC OTU diverged at Class; the OTU was assigned to Eurotiomycetes and Sordariomycetes by RDPC and UTAX, respectively, and unidentified by SINTAX. This OTU did not have an assignment lower than family. The assignment of the ITS2-BC OTU diverged at Phylum; RDPC and SINTAX placed the ITS2-BC OTU into the Basidiomycota, and UTAX placed this OTU in the Ascomycota. The assignment diverged again at Class, where it was placed into the Pucciniomycetes by RDPC, and the Agaricomycetes by SINTAX.<table-wrap id="Tab5"><label>Table 5</label><caption><p>Distribution of identically classified, uniquely classified, and unidentified OTUs across all taxonomic ranks for data presented in Fig. <xref rid="Fig2" ref-type="fig">2</xref>
</p></caption><table frame="hsides" rules="groups"><tbody><tr><td>ITS1-Soil</td><td>Kingdom</td><td>Phylum</td><td>Class</td><td>Order</td><td>Family</td><td>Genus</td><td>Species</td></tr><tr><td>&#x02003;3 classified, identical</td><td>498</td><td>393</td><td>339</td><td>306</td><td>253</td><td>167</td><td>57</td></tr><tr><td>&#x02003;3 classified, 1 unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;3 classidied, 3 unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;2 classified, identical</td><td>2</td><td>17</td><td>31</td><td>33</td><td>34</td><td>53</td><td>42</td></tr><tr><td>&#x02003;2 classified, unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;1 classified</td><td>0</td><td>12</td><td>9</td><td>16</td><td>8</td><td>14</td><td>25</td></tr><tr><td>&#x02003;&#x02003;RDP</td><td>0</td><td>0</td><td>1</td><td>1</td><td>5</td><td>9</td><td>7</td></tr><tr><td>&#x02003;&#x02003;SINTAX</td><td>0</td><td>11</td><td>5</td><td>12</td><td>3</td><td>5</td><td>18</td></tr><tr><td>&#x02003;&#x02003;UTAX</td><td>0</td><td>1</td><td>3</td><td>3</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;Unidentified</td><td>0</td><td>78</td><td>121</td><td>145</td><td>205</td><td>266</td><td>376</td></tr><tr><td>ITS2-Soil</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;3 classified, identical</td><td>481</td><td>332</td><td>242</td><td>203</td><td>135</td><td>60</td><td>16</td></tr><tr><td>&#x02003;3 classified, 1 unique</td><td>3</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;3 classified, 3 unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;2 classified, identical</td><td>2</td><td>33</td><td>58</td><td>57</td><td>45</td><td>49</td><td>20</td></tr><tr><td>&#x02003;2 classified, unique</td><td>7</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;1 classified</td><td>7</td><td>22</td><td>27</td><td>23</td><td>18</td><td>19</td><td>11</td></tr><tr><td>&#x02003;&#x02003;RDP</td><td>0</td><td>3</td><td>4</td><td>4</td><td>6</td><td>8</td><td>4</td></tr><tr><td>&#x02003;&#x02003;SINTAX</td><td>7</td><td>17</td><td>1</td><td>17</td><td>11</td><td>11</td><td>7</td></tr><tr><td>&#x02003;&#x02003;UTAX</td><td>0</td><td>2</td><td>22</td><td>2</td><td>1</td><td>0</td><td>0</td></tr><tr><td>&#x02003;Unidentified</td><td>0</td><td>113</td><td>173</td><td>217</td><td>302</td><td>372</td><td>453</td></tr><tr><td>ITS1-Plant</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;3 classified, identical</td><td>490</td><td>304</td><td>234</td><td>181</td><td>98</td><td>22</td><td>2</td></tr><tr><td>&#x02003;3 classified, 1 unique</td><td>2</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;3 classified, 3 unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;2 classified, identical</td><td>4</td><td>52</td><td>45</td><td>65</td><td>88</td><td>97</td><td>71</td></tr><tr><td>&#x02003;2 classified, unique</td><td>4</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;1 classified</td><td>0</td><td>25</td><td>44</td><td>56</td><td>57</td><td>80</td><td>75</td></tr><tr><td>&#x02003;&#x02003;RDP</td><td>0</td><td>2</td><td>1</td><td>0</td><td>5</td><td>5</td><td>4</td></tr><tr><td>&#x02003;&#x02003;SINTAX</td><td>0</td><td>9</td><td>41</td><td>0</td><td>51</td><td>75</td><td>71</td></tr><tr><td>&#x02003;&#x02003;UTAX</td><td>0</td><td>14</td><td>2</td><td>1</td><td>1</td><td>0</td><td>0</td></tr><tr><td>&#x02003;Unidentified</td><td>0</td><td>119</td><td>177</td><td>198</td><td>257</td><td>301</td><td>352</td></tr><tr><td>ITS2-Plant</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02003;3 classified, identical</td><td>499</td><td>166</td><td>120</td><td>83</td><td>36</td><td>11</td><td>2</td></tr><tr><td>&#x02003;3 classified, 1 unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;3 classified, 3 unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;2 classified, identical</td><td>1</td><td>20</td><td>29</td><td>36</td><td>32</td><td>33</td><td>22</td></tr><tr><td>&#x02003;2 classified, unique</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>&#x02003;1 classified</td><td>0</td><td>25</td><td>35</td><td>36</td><td>27</td><td>31</td><td>28</td></tr><tr><td>&#x02003;&#x02003;RDP</td><td>0</td><td>1</td><td>2</td><td>2</td><td>3</td><td>4</td><td>1</td></tr><tr><td>&#x02003;&#x02003;SINTAX</td><td>0</td><td>3</td><td>31</td><td>32</td><td>0</td><td>28</td><td>27</td></tr><tr><td>&#x02003;&#x02003;UTAX</td><td>0</td><td>21</td><td>2</td><td>2</td><td>24</td><td>0</td><td>0</td></tr><tr><td>&#x02003;Unidentified</td><td>0</td><td>289</td><td>316</td><td>345</td><td>405</td><td>425</td><td>448</td></tr></tbody></table></table-wrap>
</p></sec><sec id="Sec11"><title>Python tool outputs</title><p id="Par23">CONSTAX is implemented in Python and provided as a Bourne Shell executable, <italic>constax.sh</italic>. After installation of the required dependencies, the user must modify paths&#x000a0;and parameters in <italic>constax.sh</italic> and the <italic>config</italic> file, both of which can be found in <italic>CONSTAX.tar.gz</italic> (Additional file <xref rid="MOESM2" ref-type="media">2</xref>). The Python scripts called by <italic>constax.sh</italic> are provided independently and can be easily modified for use with other classifiers or reference databases. After implementation of <italic>constax.sh</italic>, filtered versions of all taxonomy tables for the given cutoff are generated, alongside the four main output files: i) <italic>consensus_taxonomy.txt</italic>, the final higher power taxonomy table; ii) <italic>combined_taxonomy.txt</italic>, which is a large table of all three taxonomy tables side-by-side in addition to the consensus taxonomy; iii) <italic>otu_taxonomy_CountClassified.txt</italic>, which details assigned and unidentified OTUs at each rank level; and iv) <italic>Classification_Summary.txt</italic>, which lists the total counts of all unique taxa at a given rank level.</p></sec></sec><sec id="Sec12"><title>Discussion</title><p id="Par24">Factors that influence the composition and structure of microbial communities are mainly confined to three different groups: sample origin (e.g., soil or water), laboratory methods (e.g., primer selection, PCR conditions, library preparation), and post-sequencing bioinformatic analysis. Since there are sample or methodological challenges at several steps of microbial community studies that can ultimately influence taxonomic classification; we standardize and improve the taxonomic classification step of fungal microbiome studies with CONSTAX. CONSTAX improves taxonomy assignment of fungal OTUs regardless of the strategies researchers choose to reduce the sample or methodological challenges. Linking OTUs to functionally informative names, which largely requires genus- or species-level resolution, is key to addressing biological and ecological hypotheses in fungal community studies. Considerable time should be invested into choosing optimal tools for taxonomic analysis. In this study, eight fungal amplicon datasets were assigned taxonomy using the same reference database [<xref ref-type="bibr" rid="CR23">23</xref>] and three taxonomy assignment programs were compared: RDPC [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR32">32</xref>], UTAX [<xref ref-type="bibr" rid="CR19">19</xref>, <xref ref-type="bibr" rid="CR33">33</xref>], and SINTAX [<xref ref-type="bibr" rid="CR20">20</xref>]. The taxonomic classification step is arguably one of the most delicate steps of the pipeline for amplicon-based microbial ecology studies, because taxon names are largely the basis by which scientists attach biological interpretation to the data. Our results showed minor differences across taxonomic classification approaches using thresholds chosen a priori.</p><p id="Par25">The UTAX classifier generated greater numbers of unidentified OTUs compared with RDPC and SINTAX, a pattern that is pronounced in the ITS2 dataset. We also found more non-fungal OTUs were recovered from the ITS2 sequences; indicating primers for this region may be less fungi-specific than those used for amplifying the ITS1 region. The ITS1 region has been shown to be more conserved in sequence and length for most fungal lineages compared with ITS2 [<xref ref-type="bibr" rid="CR38">38</xref>&#x02013;<xref ref-type="bibr" rid="CR40">40</xref>]. Whether the ITS1 or ITS2 region provides the best taxonomic resolution has been investigated previously with Sanger sequence data [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR37">37</xref>] and pyrosequence data [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR41">41</xref>]. Apart from the small bias of ITS1 against early diverging fungi, these regions yield similar profiles of fungal communities and either region is considered suitable for community studies. Regardless of primer choice, we showed that use of multiple taxonomy assignment algorithms resulted in consistent classifications when an appropriate OTU-clustering threshold level is used.</p><p id="Par26">Our tool, CONSTAX, implements the following best practice tips for taxonomy assignment of ITS datasets: i) Use more than one classifier program, as not one is clearly superior to others; ii) Obtain a consensus taxonomy after running multiple classifiers; iii) Use the most recent release of software. The classifier programs tested here differ slightly in power, so performing taxonomic classifications with multiple programs, and combining the results will result in a stronger assignment with higher resolution.</p><p id="Par27">When designing experiments, it behooves researchers to carefully consider their target organisms when choosing the ITS barcode region and selecting primers. When investigating broad patterns of fungi, use of ITS alone should be sufficient, but if there is interest in a specific group of fungi, additional markers for those lineages (such as 18S rRNA gene for arbuscular mycorrhizal fungi) may be needed [<xref ref-type="bibr" rid="CR42">42</xref>]. Further, there are limitations in making functional inferences from fungal ITS amplicon data. If the research questions are aimed at specific species or functions, metagenomics may be a more appropriate approach than amplicon-based community analyses.</p></sec><sec id="Sec13"><title>Conclusion</title><p id="Par28">We provide a tool, CONSTAX, for generating consensus taxonomy of targeted amplicon sequence data, and demonstrate that it improves taxonomy assignments of environmental OTUs. Taxonomic assignment will improve as database completeness improves, especially the RDPC, since that algorithm functions best when there are multiple representatives for a group (genus or species). The mycological community should continue to generate high quality ITS reference sequences for their research organisms and from Herbarium specimens, which will further enhance the performance of taxonomy assignment algorithms.</p></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec14"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12859_2017_1952_MOESM1_ESM.pdf"><label>Additional file 1:</label><caption><p>CONSTAX tutorial. Implementation of code and scripts for database formatting and trimming, taxonomy assignment, and post-taxonomy assignment filtering. (PDF 699&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12859_2017_1952_MOESM2_ESM.gz"><label>Additional file 2:</label><caption><p>CONSTAX.tar.gz compressed directory. Contains test datasets, Python, Shell, and R scripts to use the tool. (GZ 175&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12859_2017_1952_MOESM3_ESM.sh"><label>Additional file 3:</label><caption><p>otu_processing.sh pipeline. Contains code for sequence quality control and OTU-picking. (SH 2&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12859_2017_1952_MOESM4_ESM.pdf"><label>Additional file 4: Figure S1.</label><caption><p>Power of taxonomy classifiers. Distribution of classified and unclassified OTUs for each classifier and across taxonomic level. (A) ITS1-UN and (B) ITS2-UN data analyzed using UNOISE. (C) ITS1-BC and (D) ITS2-BC data analyzed with UPARSE. (PDF 304&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12859_2017_1952_MOESM5_ESM.xlsx"><label>Additional file 5: Table S1.</label><caption><p>Distribution of identically classified, uniquely classified, and unidentified OTUs across all taxonomic ranks for data presented in Additional file <xref rid="MOESM4" ref-type="media">4</xref>: Figure S1 (Benucci et al., unpublished). (XLSX 47&#x000a0;kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>ESV</term><def><p id="Par4">exact sequence variant</p></def></def-item><def-item><term>ITS</term><def><p id="Par5">internal transcribed spacer region of the ribosomal DNA</p></def></def-item><def-item><term>OTU</term><def><p id="Par6">operational taxonomic unit</p></def></def-item><def-item><term>PCR</term><def><p id="Par7">polymerase chain reaction</p></def></def-item><def-item><term>RDP</term><def><p id="Par8">Ribosomal Database Project</p></def></def-item><def-item><term>RDPC</term><def><p id="Par9">Ribosomal Database Project classifier</p></def></def-item><def-item><term>rRNA</term><def><p id="Par10">ribosomal RNA</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Electronic supplementary material</bold></p><p>The online version of this article (10.1186/s12859-017-1952-x) contains supplementary material, which is available to authorized users.</p></fn></fn-group><ack><p>We thank Matthew Agler for providing processed OTUs for our analysis, and Benli Chai for assistance with RDPC training.</p><sec id="FPar1"><title>Funding</title><p id="Par29">KG was supported by MSU Plant Science Fellowship, and the Michigan Wheat Program. GNMB was supported by AgBioResearch (Project GREEEN GR-16-043). NVP was supported by NSF BEACON (Project #988). GB acknowledges support from the US National Science Foundation (NSF) DEB 1737898.</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p id="Par30">Sample origins, barcode regions, and accession numbers for all datasets used in the current study can be found in Table <xref rid="Tab2" ref-type="table">2</xref>. All custom scripts used in the analysis are available in Additional files <xref rid="MOESM1" ref-type="media">1</xref> and <xref rid="MOESM2" ref-type="media">2</xref> or for download from GitHub: [<ext-link ext-link-type="uri" xlink:href="https://github.com/natalie-vandepol/compare_taxonomy"><underline>https://github.com/natalie-vandepol/compare_taxonomy</underline></ext-link>].</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>KG and GMNB conceived the idea, conducted the analysis, wrote the manuscript. KG and GMNB contributed equally to this research and can be considered co-first authors. NVP developed the python scripts. GB provided scientific support and assistance with writing. All authors read and approved the final version of the manuscript.</p></notes><notes notes-type="COI-statement"><sec id="FPar3"><title>Ethics approval and consent to participate</title><p id="Par31">This work did not involve human or animal subjects or protected species.</p></sec><sec id="FPar4"><title>Consent for publication</title><p id="Par32">Not applicable.</p></sec><sec id="FPar5"><title>Competing interests</title><p id="Par33">The authors declare that they have no competing interests.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gweon</surname><given-names>HS</given-names></name><name><surname>Oliver</surname><given-names>A</given-names></name><name><surname>Taylor</surname><given-names>J</given-names></name><name><surname>Booth</surname><given-names>T</given-names></name><name><surname>Gibbs</surname><given-names>M</given-names></name><name><surname>Read</surname><given-names>DS</given-names></name><name><surname>Griffiths</surname><given-names>RI</given-names></name><name><surname>Schonrogge</surname><given-names>K</given-names></name></person-group><article-title>PIPITS: an automated pipeline for analyses of fungal internal transcribed spacer sequences from the Illumina sequencing platform</article-title><source>Methods Ecol Evol</source><year>2015</year><volume>6</volume><issue>8</issue><fpage>973</fpage><lpage>980</lpage><pub-id pub-id-type="doi">10.1111/2041-210X.12399</pub-id><?supplied-pmid 27570615?><pub-id pub-id-type="pmid">27570615</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>White</surname><given-names>TJ</given-names></name><name><surname>Bruns</surname><given-names>T</given-names></name><name><surname>Lee</surname><given-names>S</given-names></name><name><surname>Taylor</surname><given-names>JW</given-names></name></person-group><person-group person-group-type="editor"><name><surname>Innis</surname><given-names>MA</given-names></name><name><surname>Gelfand</surname><given-names>DH</given-names></name><name><surname>Sninsky</surname><given-names>JJ</given-names></name><name><surname>White</surname><given-names>TJ</given-names></name></person-group><article-title>Amplification and direct sequencing of fungal ribosomal RNA genes for phylogenetics</article-title><source>PCR protocols: a guide to methods and applications</source><year>1990</year><publisher-loc>New York</publisher-loc><publisher-name>Elsevier</publisher-name><fpage>315</fpage><lpage>322</lpage></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bellemain</surname><given-names>E</given-names></name><name><surname>Carlsen</surname><given-names>T</given-names></name><name><surname>Brochmann</surname><given-names>C</given-names></name><name><surname>Coissac</surname><given-names>E</given-names></name><name><surname>Taberlet</surname><given-names>P</given-names></name><name><surname>Kauserud</surname><given-names>H</given-names></name></person-group><article-title>ITS as an environmental DNA barcode for fungi: an in silico approach reveals potential PCR biases</article-title><source>BMC Microbiol</source><year>2010</year><volume>10</volume><issue>1</issue><fpage>189</fpage><pub-id pub-id-type="doi">10.1186/1471-2180-10-189</pub-id><?supplied-pmid 20618939?><pub-id pub-id-type="pmid">20618939</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schoch</surname><given-names>CL</given-names></name><name><surname>Seifert</surname><given-names>KA</given-names></name><name><surname>Huhndorf</surname><given-names>S</given-names></name></person-group><article-title>Nuclear ribosomal internal transcribed spacer (ITS) region as a universal DNA barcode marker for fungi</article-title><source>Proc Natl Acad Sci</source><year>2012</year><volume>109</volume><issue>16</issue><fpage>6241</fpage><lpage>6246</lpage><pub-id pub-id-type="doi">10.1073/pnas.1117018109</pub-id><?supplied-pmid 22454494?><pub-id pub-id-type="pmid">22454494</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hibbett</surname><given-names>D</given-names></name><name><surname>Abarenkov</surname><given-names>K</given-names></name><name><surname>Koljalg</surname><given-names>U</given-names></name><name><surname>&#x000d6;pik</surname><given-names>M</given-names></name><name><surname>Chai</surname><given-names>B</given-names></name><name><surname>Cole</surname><given-names>JR</given-names></name><etal/></person-group><article-title>Sequence-based classification and identification of fungi</article-title><source>Mycologia</source><year>2016</year><volume>108</volume><issue>6</issue><fpage>1049</fpage><lpage>1068</lpage><?supplied-pmid 27760854?><pub-id pub-id-type="pmid">27760854</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dentinger</surname><given-names>BT</given-names></name><name><surname>Didukh</surname><given-names>MY</given-names></name><name><surname>Moncalvo</surname><given-names>JM</given-names></name></person-group><article-title>Comparing COI and ITS as DNA barcode markers for mushrooms and allies (Agaricomycotina)</article-title><source>PLoS One</source><year>2011</year><volume>6</volume><issue>9</issue><fpage>e25081</fpage><lpage>e25088</lpage><pub-id pub-id-type="doi">10.1371/journal.pone.0025081</pub-id><?supplied-pmid 21966418?><pub-id pub-id-type="pmid">21966418</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bazzicalupo</surname><given-names>AL</given-names></name><name><surname>B&#x000e1;lint</surname><given-names>M</given-names></name><name><surname>Schmitt</surname><given-names>I</given-names></name></person-group><article-title>Comparison of ITS1 and ITS2 rDNA in 454 sequencing of hyperdiverse fungal communities</article-title><source>Fungal Ecol</source><year>2013</year><volume>6</volume><issue>1</issue><fpage>102</fpage><lpage>109</lpage><pub-id pub-id-type="doi">10.1016/j.funeco.2012.09.003</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Blaalid</surname><given-names>R</given-names></name><name><surname>Kumar</surname><given-names>S</given-names></name><name><surname>Nilsson</surname><given-names>RH</given-names></name><name><surname>Abarenkov</surname><given-names>K</given-names></name><name><surname>Kirk</surname><given-names>PM</given-names></name><name><surname>Kauserud</surname><given-names>H</given-names></name></person-group><article-title>ITS1 versus ITS2 as DNA metabarcodes for fungi</article-title><source>Mol Ecol Resour</source><year>2013</year><volume>13</volume><issue>2</issue><fpage>218</fpage><lpage>224</lpage><pub-id pub-id-type="doi">10.1111/1755-0998.12065</pub-id><?supplied-pmid 23350562?><pub-id pub-id-type="pmid">23350562</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mello</surname><given-names>A</given-names></name><name><surname>Napoli</surname><given-names>C</given-names></name><name><surname>Murat</surname><given-names>C</given-names></name><name><surname>Morin</surname><given-names>E</given-names></name><name><surname>Marceddu</surname><given-names>G</given-names></name><name><surname>Bonfante</surname><given-names>P</given-names></name></person-group><article-title>ITS-1 versus ITS-2 pyrosequencing: a comparison of fungal populations in truffle grounds</article-title><source>Mycologia</source><year>2011</year><volume>103</volume><issue>6</issue><fpage>1184</fpage><lpage>1193</lpage><pub-id pub-id-type="doi">10.3852/11-027</pub-id><?supplied-pmid 21700633?><pub-id pub-id-type="pmid">21700633</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Agler</surname><given-names>MT</given-names></name><name><surname>Ruhe</surname><given-names>J</given-names></name><name><surname>Kroll</surname><given-names>S</given-names></name><name><surname>Morhenn</surname><given-names>C</given-names></name><name><surname>Kim</surname><given-names>S-T</given-names></name><name><surname>Weigel</surname><given-names>D</given-names></name><etal/></person-group><article-title>Microbial hub taxa link host and abiotic factors to plant microbiome variation</article-title><source>PLoS Biol</source><year>2016</year><volume>14</volume><issue>1</issue><fpage>e1002352</fpage><lpage>e1002331</lpage><pub-id pub-id-type="doi">10.1371/journal.pbio.1002352</pub-id><?supplied-pmid 26788878?><pub-id pub-id-type="pmid">26788878</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kumar</surname><given-names>S</given-names></name><name><surname>Carlsen</surname><given-names>T</given-names></name><name><surname>Mevik</surname><given-names>B-H</given-names></name><name><surname>Enger</surname><given-names>P</given-names></name><name><surname>Blaalid</surname><given-names>R</given-names></name><name><surname>Shalchian-Tabrizi</surname><given-names>K</given-names></name><etal/></person-group><article-title>CLOTU: an online pipeline for processing and clustering of 454 amplicon reads into OTUs followed by taxonomic annotation</article-title><source>BMC Bioinformatics</source><year>2011</year><volume>12</volume><issue>2</issue><fpage>182</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-12-182</pub-id><?supplied-pmid 21599929?><pub-id pub-id-type="pmid">21599929</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>White</surname><given-names>JR</given-names></name><name><surname>Maddox</surname><given-names>C</given-names></name><name><surname>White</surname><given-names>O</given-names></name><name><surname>Angiuoli</surname><given-names>SV</given-names></name><name><surname>Fricke</surname><given-names>WF</given-names></name></person-group><article-title>CloVR-ITS: automated internal transcribed spacer amplicon sequence analysis pipeline for the characterization of fungal microbiota</article-title><source>Microbiome</source><year>2013</year><volume>1</volume><issue>1</issue><fpage>6</fpage><pub-id pub-id-type="doi">10.1186/2049-2618-1-6</pub-id><?supplied-pmid 24451270?><pub-id pub-id-type="pmid">24451270</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>B&#x000e1;lint</surname><given-names>M</given-names></name><name><surname>Schmidt</surname><given-names>P-A</given-names></name><name><surname>Sharma</surname><given-names>R</given-names></name><name><surname>Thines</surname><given-names>M</given-names></name><name><surname>Schmitt</surname><given-names>I</given-names></name></person-group><article-title>An Illumina metabarcoding pipeline for fungi</article-title><source>Ecol Evol</source><year>2014</year><volume>4</volume><issue>13</issue><fpage>2642</fpage><lpage>2653</lpage><pub-id pub-id-type="doi">10.1002/ece3.1107</pub-id><?supplied-pmid 25077016?><pub-id pub-id-type="pmid">25077016</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schloss</surname><given-names>PD</given-names></name><name><surname>Westcott</surname><given-names>SL</given-names></name><name><surname>Ryabin</surname><given-names>T</given-names></name><name><surname>Hall</surname><given-names>JR</given-names></name><name><surname>Hartmann</surname><given-names>M</given-names></name><name><surname>Hollister</surname><given-names>EB</given-names></name><etal/></person-group><article-title>Introducing mothur: open-source, platform-independent, community-supported software for describing and comparing microbial communities</article-title><source>Appl Environ Microbiol</source><year>2009</year><volume>75</volume><issue>23</issue><fpage>7537</fpage><lpage>7541</lpage><pub-id pub-id-type="doi">10.1128/AEM.01541-09</pub-id><?supplied-pmid 19801464?><pub-id pub-id-type="pmid">19801464</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Caporaso</surname><given-names>JG</given-names></name><name><surname>Kuczynski</surname><given-names>J</given-names></name><name><surname>Stombaugh</surname><given-names>J</given-names></name><name><surname>Bittinger</surname><given-names>K</given-names></name><name><surname>Bushman</surname><given-names>FD</given-names></name><name><surname>Costello</surname><given-names>EK</given-names></name><etal/></person-group><article-title>QIIME allows analysis of high-throughput community sequencing data</article-title><source>Nat Methods</source><year>2010</year><volume>7</volume><issue>5</issue><fpage>335</fpage><lpage>336</lpage><pub-id pub-id-type="doi">10.1038/nmeth.f.303</pub-id><?supplied-pmid 20383131?><pub-id pub-id-type="pmid">20383131</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edgar</surname><given-names>RC</given-names></name></person-group><article-title>Search and clustering orders of magnitude faster than BLAST</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><issue>19</issue><fpage>2460</fpage><lpage>2461</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btq461</pub-id><?supplied-pmid 20709691?><pub-id pub-id-type="pmid">20709691</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Altschul</surname><given-names>SF</given-names></name><name><surname>Gish</surname><given-names>W</given-names></name><name><surname>Miller</surname><given-names>W</given-names></name><name><surname>Myers</surname><given-names>EW</given-names></name></person-group><article-title>Basic local alignment search tool</article-title><source>J Mol Biol</source><year>1990</year><volume>215</volume><issue>3</issue><fpage>403</fpage><lpage>410</lpage><pub-id pub-id-type="doi">10.1016/S0022-2836(05)80360-2</pub-id><?supplied-pmid 2231712?><pub-id pub-id-type="pmid">2231712</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Q</given-names></name><name><surname>Garrity</surname><given-names>GM</given-names></name><name><surname>Tiedje</surname><given-names>JM</given-names></name><name><surname>Cole</surname><given-names>JR</given-names></name></person-group><article-title>Naive Bayesian classifier for rapid assignment of rRNA sequences into the new bacterial taxonomy</article-title><source>Appl Environ Microbiol</source><year>2007</year><volume>73</volume><issue>16</issue><fpage>5261</fpage><lpage>5267</lpage><pub-id pub-id-type="doi">10.1128/AEM.00062-07</pub-id><?supplied-pmid 17586664?><pub-id pub-id-type="pmid">17586664</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edgar</surname><given-names>RC</given-names></name></person-group><article-title>UPARSE: highly accurate OTU sequences from microbial amplicon reads</article-title><source>Nat Methods</source><year>2013</year><volume>10</volume><issue>1</issue><fpage>996</fpage><lpage>998</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2604</pub-id><?supplied-pmid 23955772?><pub-id pub-id-type="pmid">23955772</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><mixed-citation publication-type="other">Edgar R. SINTAX: a simple non-Bayesian taxonomy classifier for 16S and ITS sequences. bioRxiv. 2016;074161 doi:10.1101/074161.</mixed-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cole</surname><given-names>JR</given-names></name><name><surname>Chai</surname><given-names>B</given-names></name><name><surname>Farris</surname><given-names>RJ</given-names></name><name><surname>Wang</surname><given-names>Q</given-names></name><name><surname>Kulam</surname><given-names>AS</given-names></name><name><surname>McGarrell</surname><given-names>DM</given-names></name><etal/></person-group><article-title>The ribosomal database project (RDP-II): sequences and tools for high-throughput rRNA analysis</article-title><source>Nucleic Acids Res</source><year>2005</year><volume>33</volume><fpage>D294</fpage><lpage>D296</lpage><pub-id pub-id-type="doi">10.1093/nar/gki038</pub-id><?supplied-pmid 15608200?><pub-id pub-id-type="pmid">15608200</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>Z</given-names></name><name><surname>DeSantis</surname><given-names>TZ</given-names></name><name><surname>Andersen</surname><given-names>GL</given-names></name><name><surname>Knight</surname><given-names>R</given-names></name></person-group><article-title>Accurate taxonomy assignments from 16S rRNA sequences produced by highly parallel pyrosequencers</article-title><source>Nucleic Acids Res</source><year>2008</year><volume>36</volume><fpage>e120</fpage><pub-id pub-id-type="doi">10.1093/nar/gkn491</pub-id><?supplied-pmid 18723574?><pub-id pub-id-type="pmid">18723574</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abarenkov</surname><given-names>K</given-names></name><name><surname>Nilsson</surname><given-names>RH</given-names></name><name><surname>Larsson</surname><given-names>K-H</given-names></name><name><surname>Alexander</surname><given-names>IJ</given-names></name><name><surname>Eberhardt</surname><given-names>U</given-names></name><name><surname>Erland</surname><given-names>S</given-names></name><etal/></person-group><article-title>The UNITE database for molecular identification of fungi &#x02013; recent updates and future perspectives</article-title><source>New Phytol</source><year>2010</year><volume>186</volume><issue>2</issue><fpage>281</fpage><lpage>285</lpage><pub-id pub-id-type="doi">10.1111/j.1469-8137.2009.03160.x</pub-id><?supplied-pmid 20409185?><pub-id pub-id-type="pmid">20409185</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>K&#x000f5;ljalg</surname><given-names>U</given-names></name><name><surname>Nilsson</surname><given-names>RH</given-names></name><name><surname>Abarenkov</surname><given-names>K</given-names></name><name><surname>Tedersoo</surname><given-names>L</given-names></name><name><surname>Taylor</surname><given-names>AFS</given-names></name><name><surname>Bahram</surname><given-names>M</given-names></name><etal/></person-group><article-title>Towards a unified paradigm for sequence-based identification of fungi</article-title><source>Mol Ecol</source><year>2013</year><volume>22</volume><issue>21</issue><fpage>5271</fpage><lpage>5277</lpage><pub-id pub-id-type="doi">10.1111/mec.12481</pub-id><?supplied-pmid 24112409?><pub-id pub-id-type="pmid">24112409</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>K&#x000f5;ljalg</surname><given-names>U</given-names></name><name><surname>Larsson</surname><given-names>K-H</given-names></name><name><surname>Abarenkov</surname><given-names>K</given-names></name><name><surname>Nilsson</surname><given-names>RH</given-names></name><name><surname>Alexander</surname><given-names>IJ</given-names></name><name><surname>Eberhardt</surname><given-names>U</given-names></name><etal/></person-group><article-title>UNITE: a database providing web-based methods for the molecular identification of ectomycorrhizal fungi</article-title><source>New Phytol</source><year>2005</year><volume>166</volume><fpage>1063</fpage><lpage>1068</lpage><pub-id pub-id-type="doi">10.1111/j.1469-8137.2005.01376.x</pub-id><?supplied-pmid 15869663?><pub-id pub-id-type="pmid">15869663</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Deshpande</surname><given-names>V</given-names></name><name><surname>Wang</surname><given-names>Q</given-names></name><name><surname>Greenfield</surname><given-names>P</given-names></name><name><surname>Charleston</surname><given-names>M</given-names></name><name><surname>Porras-Alfaro</surname><given-names>A</given-names></name><name><surname>Kuske</surname><given-names>CR</given-names></name><etal/></person-group><article-title>Fungal identification using a Bayesian classifier and the Warcup training set of internal transcribed spacer sequences</article-title><source>Mycologia</source><year>2016</year><volume>108</volume><issue>1</issue><fpage>1</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.3852/14-293</pub-id><?supplied-pmid 26553774?><pub-id pub-id-type="pmid">26553774</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sun</surname><given-names>Y</given-names></name><name><surname>Cai</surname><given-names>Y</given-names></name><name><surname>Huse</surname><given-names>SM</given-names></name><name><surname>Knight</surname><given-names>R</given-names></name><name><surname>Farmerie</surname><given-names>WG</given-names></name><name><surname>Wang</surname><given-names>X</given-names></name><etal/></person-group><article-title>A large-scale benchmark study of existing algorithms for taxonomy-independent microbial community analysis</article-title><source>Brief Bioinform</source><year>2012</year><volume>13</volume><issue>1</issue><fpage>107</fpage><lpage>121</lpage><pub-id pub-id-type="doi">10.1093/bib/bbr009</pub-id><?supplied-pmid 21525143?><pub-id pub-id-type="pmid">21525143</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Plummer</surname><given-names>E</given-names></name><name><surname>Twin</surname><given-names>J</given-names></name></person-group><article-title>A comparison of three bioinformatics pipelines for the analysis of preterm gut microbiota using 16S rRNA gene sequencing data</article-title><source>J Proteomics Bioinformatics</source><year>2015</year><volume>8</volume><issue>12</issue><fpage>1</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.4172/jpb.1000381</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>J</given-names></name><name><surname>Kobert</surname><given-names>K</given-names></name><name><surname>Flouri</surname><given-names>T</given-names></name><name><surname>Stamatakis</surname><given-names>A</given-names></name></person-group><article-title>PEAR: a fast and accurate Illumina paired-end reAd mergeR</article-title><source>Bioinformatics</source><year>2014</year><volume>30</volume><issue>5</issue><fpage>614</fpage><lpage>620</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btt593</pub-id><?supplied-pmid 24142950?><pub-id pub-id-type="pmid">24142950</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><mixed-citation publication-type="other">Callahan BJ, McMurdie PJ, Holmes SP. Exact sequence variants should replace operational taxonomic units in marker gene data analysis. bioRxiv. 2017;113597 doi:10.1101/113597.</mixed-citation></ref><ref id="CR31"><label>31.</label><mixed-citation publication-type="other">Edgar RC. UNOISE2: improved error-correction for Illumina 16S and ITS amplicon sequencing. bioRxiv. 2016;081257 doi:10.1101/081257.</mixed-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cole</surname><given-names>JR</given-names></name><name><surname>Wang</surname><given-names>Q</given-names></name><name><surname>Fish</surname><given-names>JA</given-names></name><name><surname>Chai</surname><given-names>B</given-names></name><name><surname>McGarrell</surname><given-names>DM</given-names></name><name><surname>Sun</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Ribosomal database project: data and tools for high throughput rRNA analysis</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>42</volume><fpage>D633</fpage><lpage>D642</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt1244</pub-id><?supplied-pmid 24288368?><pub-id pub-id-type="pmid">24288368</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edgar</surname><given-names>RC</given-names></name><name><surname>Haas</surname><given-names>BJ</given-names></name><name><surname>Clemente</surname><given-names>JC</given-names></name><name><surname>Quince</surname><given-names>C</given-names></name><name><surname>Knight</surname><given-names>R</given-names></name></person-group><article-title>UCHIME improves sensitivity and speed of chimera detection</article-title><source>Bioinformatics</source><year>2011</year><volume>27</volume><issue>16</issue><fpage>2194</fpage><lpage>2200</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btr381</pub-id><?supplied-pmid 21700674?><pub-id pub-id-type="pmid">21700674</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="book"><person-group person-group-type="author"><collab>R Core Team</collab></person-group><source>R: a language and environment for statistical computing</source><year>2016</year><publisher-loc>Vienna</publisher-loc><publisher-name>R Foundation for Statistical Computing</publisher-name></element-citation></ref><ref id="CR35"><label>35.</label><mixed-citation publication-type="other">Wickham H. ggplot2: Elegant Graphics for Data Analysis. Springer-Verlag New York, 2009. <ext-link ext-link-type="uri" xlink:href="http://ggplot2.org">http://ggplot2.org</ext-link>.</mixed-citation></ref><ref id="CR36"><label>36.</label><mixed-citation publication-type="other">Smith DP, Peay KG. Sequence depth, not PCR replication, improves ecological inference from next generation DNA sequencing. PLoS One. 2014;9(2):e90234&#x02013;12.</mixed-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Oliver</surname><given-names>AK</given-names></name><name><surname>Mac</surname><given-names>A</given-names></name><name><surname>Jr</surname><given-names>C</given-names></name><name><surname>Jumpponen</surname><given-names>A</given-names></name></person-group><article-title>Soil fungal communities respond compositionally to recurring frequent prescribed burning in a managed southeastern US forest ecosystem</article-title><source>For Ecol Manag</source><year>2015</year><volume>345</volume><fpage>1</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1016/j.foreco.2015.02.020</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nilsson</surname><given-names>RH</given-names></name><name><surname>Kristiansson</surname><given-names>E</given-names></name><name><surname>Ryberg</surname><given-names>M</given-names></name></person-group><article-title>Intraspecific ITS variability in the kingdom fungi as expressed in the international sequence databases and its implications for molecular species identification</article-title><source>Evol Bioinformatics Online</source><year>2008</year><volume>4</volume><fpage>193</fpage><lpage>201</lpage></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ryberg</surname><given-names>M</given-names></name><name><surname>Nilsson</surname><given-names>RH</given-names></name><name><surname>Kristiansson</surname><given-names>E</given-names></name><name><surname>T&#x000f6;pel</surname><given-names>M</given-names></name><name><surname>Jacobsson</surname><given-names>S</given-names></name><name><surname>Larsson</surname><given-names>E</given-names></name></person-group><article-title>Mining metadata from unidentified ITS sequences in GenBank: a case study in <italic>Inocybe</italic> (Basidiomycota)</article-title><source>BMC Evol Biol</source><year>2008</year><volume>8</volume><issue>1</issue><fpage>50</fpage><lpage>14</lpage><pub-id pub-id-type="doi">10.1186/1471-2148-8-50</pub-id><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mullineux</surname><given-names>T</given-names></name><name><surname>Hausner</surname><given-names>G</given-names></name></person-group><article-title>Evolution of rDNA ITS1 and ITS2 sequences and RNA secondary structures within members of the fungal genera <italic>Grosmannia</italic> and <italic>Leptographium</italic></article-title><source>Fungal Genet Biol</source><year>2009</year><volume>46</volume><issue>11</issue><fpage>855</fpage><lpage>867</lpage><pub-id pub-id-type="doi">10.1016/j.fgb.2009.08.001</pub-id><?supplied-pmid 19665572?><pub-id pub-id-type="pmid">19665572</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Monard</surname><given-names>C</given-names></name><name><surname>Gantner</surname><given-names>S</given-names></name><name><surname>Stenlid</surname><given-names>J</given-names></name></person-group><article-title>Utilizing ITS1 and ITS2 to study environmental fungal diversity using pyrosequencing</article-title><source>FEMS Microbiol Ecol</source><year>2013</year><volume>84</volume><issue>1</issue><fpage>165</fpage><lpage>175</lpage><pub-id pub-id-type="doi">10.1111/1574-6941.12046</pub-id><?supplied-pmid 23176677?><pub-id pub-id-type="pmid">23176677</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>&#x000d6;pik</surname><given-names>M</given-names></name><name><surname>Davison</surname><given-names>J</given-names></name><name><surname>Moora</surname><given-names>M</given-names></name><name><surname>Zobel</surname><given-names>M</given-names></name></person-group><article-title>DNA-based detection and identification of Glomeromycota: the virtual taxonomy of environmental sequences</article-title><source>Botany</source><year>2014</year><volume>92</volume><issue>2</issue><fpage>135</fpage><lpage>147</lpage><pub-id pub-id-type="doi">10.1139/cjb-2013-0110</pub-id></element-citation></ref></ref-list></back></article>