<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Genomics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Genomics</journal-id><journal-title-group><journal-title>BMC Genomics</journal-title></journal-title-group><issn pub-type="epub">1471-2164</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6792250</article-id><article-id pub-id-type="publisher-id">6131</article-id><article-id pub-id-type="doi">10.1186/s12864-019-6131-1</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Sequence properties of certain GC rich avian genes, their origins and absence from genome assemblies: case studies</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Beauclair</surname><given-names>Linda</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Ram&#x000e9;</surname><given-names>Christelle</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Arensburger</surname><given-names>Peter</given-names></name><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Pi&#x000e9;gu</surname><given-names>Beno&#x000ee;t</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Guillou</surname><given-names>Florian</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Dupont</surname><given-names>Jo&#x000eb;lle</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0001-9476-0710</contrib-id><name><surname>Bigot</surname><given-names>Yves</given-names></name><address><phone>+33 2 47 42 75 66</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0385 4036</institution-id><institution-id institution-id-type="GRID">grid.464126.3</institution-id><institution>PRC, UMR INRA0085, CNRS 7247, Centre INRA Val de Loire, </institution></institution-wrap>37380 Nouzilly, France </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2234 9391</institution-id><institution-id institution-id-type="GRID">grid.155203.0</institution-id><institution>Biological Sciences Department, </institution><institution>California State Polytechnic University, </institution></institution-wrap>Pomona, CA 91768 USA </aff></contrib-group><pub-date pub-type="epub"><day>14</day><month>10</month><year>2019</year></pub-date><pub-date pub-type="pmc-release"><day>14</day><month>10</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>20</volume><elocation-id>734</elocation-id><history><date date-type="received"><day>11</day><month>6</month><year>2019</year></date><date date-type="accepted"><day>23</day><month>9</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2019</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">More and more eukaryotic genomes are sequenced and assembled, most of them presented as a complete model in which missing chromosomal regions are filled by Ns and where a few chromosomes may be lacking. Avian genomes often contain sequences with high GC content, which has been hypothesized to be at the origin of many missing sequences in these genomes. We investigated features of these missing sequences to discover why some may not have been integrated into genomic libraries and/or sequenced.</p></sec><sec><title>Results</title><p id="Par2">The sequences of five red jungle fowl cDNA models with high GC content were used as queries to search publicly available datasets of Illumina and Pacbio sequencing reads. These were used to reconstruct the leptin, TNF&#x003b1;, MRPL52, PCP2 and PET100 genes, all of which are absent from the red jungle fowl genome model. These gene sequences displayed elevated GC contents, had intron sizes that were sometimes larger than non-avian orthologues, and had non-coding regions that contained numerous tandem and inverted repeat sequences with motifs able to assemble into stable G-quadruplexes and intrastrand dyadic structures. Our results suggest that Illumina technology was unable to sequence the non-coding regions of these genes. On the other hand, PacBio technology was able to sequence these regions, but with dramatically lower efficiency than would typically be expected.</p></sec><sec><title>Conclusions</title><p id="Par3">High GC content was not the principal reason why numerous GC-rich regions of avian genomes are missing from genome assembly models. Instead, it is the presence of tandem repeats containing motifs capable of assembling into very stable secondary structures that is likely responsible.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>G-quadruplex/genome/Illumina/ PacBio/repeats</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100009469</institution-id><institution>Conseil R&#x000e9;gional du Centre-Val de Loire</institution></institution-wrap></funding-source><award-id>AVIGES</award-id><principal-award-recipient><name><surname>Bigot</surname><given-names>Yves</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2019</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par30">The red jungle fowl (RJF) is the ancestor of all domestic chicken breeds and lines, and was the third vertebrate to have its genome sequenced and assembled [<xref ref-type="bibr" rid="CR1">1</xref>]. In the last decade the increased output and reliability of second and third generation high throughput sequencing technologies have led to the release of five RJF genome model updates, the galGal5 model is currently the most commonly used.</p><p id="Par31">The galGal5 model [<xref ref-type="bibr" rid="CR2">2</xref>] is organized into 34 chromosomes that are split into 9 macro-autosomes and 23 micro-autosomes based on their size in caryology, and 2 sexual chromosomes, the W and Z micro and macro heterosomes respectively. Together, these chromosome models are approximately 1 Giga base pairs (Gbp) in size. An additional 0.23 Gbp of sequences is also present in the model either as unplaced scaffolds or as scaffolds assigned to a chromosome but not placed within the chromosome. Models for microchromosomes 29, 30, 34, 35, 36, 37 and 38 are not available, perhaps because many of their sequences are found in scaffolds without a precise location in the genome. The current galGal5 model size (total size, 1.23 Gbp) is smaller than the size estimated by cytometry and DNA reassociation kinetics (ranging from 1.28&#x02013;1.3 Gbp see [<xref ref-type="bibr" rid="CR3">3</xref>]). This suggests that at least 3.4 to 5.4% of the RJF genome remains to be sequenced and assembled, including some subtelomeric regions (see [<xref ref-type="bibr" rid="CR4">4</xref>]). During the preparation of this manuscript the first files of the galGal6 model were released (March 27th 2018) as well its annotation at NCBI (May 18th 2018) and Ensembl (March 11th 2019). galGal6 has a similar total genome size as galGal5, but includes a longer chromosome 16 (we would note that this is probably the most ellusive avian chromosome due to its repeat content), a chromosome 30, and fewer unplaced scaffolds than galGal5. Because the galGal6 genome model was only very recently released, the galGal5 was used as the cornerstone of our study while galGal6 was used as a complementary information source.</p><p id="Par32">In addition to the genome completeness questions discussed above, there has been significant controversy in the literature regarding at least 2454 genes in this animal [<xref ref-type="bibr" rid="CR5">5</xref>]. These genes are present in other vertebrates but have been either lost during the evolution of the bird lineage [<xref ref-type="bibr" rid="CR6">6</xref>&#x02013;<xref ref-type="bibr" rid="CR9">9</xref>], or are &#x0201c;invisible&#x0201d; to sequencing and assembling technologies because of their elevated GC content. One of the best examples concerns the sequence of leptin gene in RJF and other avian genomes. This has been the subject of several controversial publications during the last two decades (for reviews see [<xref ref-type="bibr" rid="CR10">10</xref>, <xref ref-type="bibr" rid="CR11">11</xref>]). In 2016 the controversy was resolved with the publication of one partial model and one complete sequence model of the leptin open reading frames (ORFs) in two Galloanserae species. These models (accession numbers LN794246 and LN794245) were reconstructed from nine 454 reads from the RJF (partial model) and from 20 Illumina reads from the Pekin duck <italic>Anas platyrhynchos domesticus</italic> (complete model) [<xref ref-type="bibr" rid="CR10">10</xref>]. These sequences confirmed that the coding exons of leptin genes of both species had elevated GC content (67.7 and 73.3%, respectively). This observation likely explains why these genes had long been difficult to amplify by polymerase chain reactions (PCR) or to sequence (the difficulty of using these techniques with GC rich sequences is discussed in [<xref ref-type="bibr" rid="CR12">12</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>]). Sequencing confirmed that there were 2 coding exons in both species; which had previously been observed in a dove, two falcons, a tit and a zebra finch species [<xref ref-type="bibr" rid="CR9">9</xref>]. Recently, the locus containing the leptin gene was mapped on the galGal5 RJF genome model to the distal tip of the p arm on chromosome 1, a subtelomeric region known to be GC-rich [<xref ref-type="bibr" rid="CR4">4</xref>]. A second publication in 2016 released the full-length sequence of the RJF leptin ORF (accession number KT970642). This sequence has been verified by sequencing of three tilled RT-PCR products obtained under non-routine amplification conditions [<xref ref-type="bibr" rid="CR18">18</xref>]. In the last 3&#x000a0;years several sets of avian cDNA corresponding to &#x0201c;lost or missing GC-rich genes&#x0201d; have been published ([<xref ref-type="bibr" rid="CR19">19</xref>], 14 cDNAs [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR20">20</xref>];, 2132 cDNAs [<xref ref-type="bibr" rid="CR21">21</xref>, <xref ref-type="bibr" rid="CR22">22</xref>];, 1 cDNA). These publications support the hypothesis that the current sequencing and assembly technologies are inefficient in regions with elevated GC content (&#x0003e;&#x02009;60%).</p><p id="Par33">In addition to the issues raised above, there are at least four categories of genes that were at one point considered to be absent from avian genomes. First are genes where public RNA-seq data from chickens allowed for reconstruction of cDNAs, but for which no complete or partial genomic gene sequences are available. The RJF leptin and tumor necrosis factor &#x003b1; (TNF&#x003b1;) genes [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR22">22</xref>] belong to this category. In the second category are genes that have yet to be detected by searching public datasets of RJF RNA-seq or by genome resequencing, but for which cDNA or genomic copies are available or can be easily characterized within sequence data of other avian species [<xref ref-type="bibr" rid="CR5">5</xref>]. An example is the omentin gene (so-called intellectin 1, ITLN1). Indeed, an ITLN1 cDNA is available in the transcriptome sequence of the tinamou (<italic>Tinamus guttatus</italic>, accession XP_010211902.1). Furthermore, this has allowed for characterization of an orthologous gene in the kiwi (<italic>Apteryx australis mantelli</italic>, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). A third category of genes that were thought be absent in birds are genes for which no trace has been found in public sequencing datasets. Examples of such genes are those encoding the kiss peptides 1 and 3 and their related receptors [<xref ref-type="bibr" rid="CR23">23</xref>], as well as the piwi 3 and piwi 4 proteins [<xref ref-type="bibr" rid="CR24">24</xref>]. It is likely this category includes some true losses in the avian lineage, but some genes are likely difficult to sequence and assemble using current technologies. The last category is simply those genes that have already been sequenced, assembled, and mapped to scaffolds but are not properly annotated. Examples include genes coding for the carnitine O-palmitoyltransferase 1 (CPT1C) and insulin-like peptide (ISNL5) (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). A final note, genes in the first three categories may be absent at least in part, due to technical difficulties such as preserving their sequences following genome fragmentation, difficulty amplifying them by PCR, or difficulty sequencing them in RNA-seq and/or genomic libraries, or a combination of these.</p><p id="Par34">Because PacBio technology is deemed to be more efficient at sequencing GC-rich fragments [<xref ref-type="bibr" rid="CR25">25</xref>] than Illumina technology, we have searched public PacBio datasets for sequences containing the coding exons of some &#x0201c;lost or missing GC-rich genes&#x0201d; [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR19">19</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>] in order to identify sequence properties that might explain their absence from genome models. We first evaluated the quality of sequences obtained with PacBio technology for GC rich gene sequences. We then verified whether these genes were still absent from the two most recent galGal genome models. Following this, we searched public datasets in order to construct a genomic model of the leptin gene, an important contribution to avian physiologists. This model was extremely GC-rich, had an intron composed of short tandem repeats, and contained numerous stretches of G-motifs in both coding and non-coding regions. We found that these motifs would be favorable for the assembly of intrastrand G-quadruplexes (G4&#x02009;s) [<xref ref-type="bibr" rid="CR26">26</xref>&#x02013;<xref ref-type="bibr" rid="CR30">30</xref>]. These are nucleotidic structures that are extremely stable and reluctant to RNA and DNA-template dependent DNA replication. Because of the leptin gene&#x02019;s unusual properties we identified it in other avian species and studied its expression. Finally, in order compare the leptin gene properties to other genes that are difficult to sequence, we extended our observations to four other genes encoding the tumor necrosis factor alpha (TNF&#x003b1;), the mitochondrial ribosomal protein L52 (MRPL52), the Purkinje cell protein 2 (PCP2) and the protein homolog to the yeast mitochondrial PET100 protein (PET100), for which reliable cDNA sequences are currently only available in [<xref ref-type="bibr" rid="CR19">19</xref>, <xref ref-type="bibr" rid="CR22">22</xref>]. Similar to the leptin gene, we chose these four genes precisely because they are among the most difficult to assemble into cDNA models. Indeed, their DNA sequences are not sufficiently conserved for interspecific comparisons between bird species (as was done the remaining 2132 cDNAs [<xref ref-type="bibr" rid="CR21">21</xref>, <xref ref-type="bibr" rid="CR22">22</xref>]). Therefore, a description of gene copy numbers using Pacbio reads is currently the best way to confirm their presence in the RJF genome.</p></sec><sec id="Sec2"><title>Results</title><p id="Par35">PacBio technology has been demonstrated to be more efficient for sequencing genomic regions that Illumina machines struggle with [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR31">31</xref>, <xref ref-type="bibr" rid="CR32">32</xref>]. However, it remains unclear if this applies to sequence fragments with &#x0003e;&#x02009;60% GC content; specifically whether read depth sequence errors are within acceptable limits using PacBio technology. Furthermore, at the same time as this manuscript was reviewed another study was published [<xref ref-type="bibr" rid="CR33">33</xref>] which evaluated the efficiency of PacBio reads for sequencing non-B DNA regions in human and mouse genomes. This study showed that non-B DNA regions modified the polymerization speed and the error rate, i.e. they decreased the reliability of Pacbio reads obtained from these specific regions. Second, they showed that non-B DNA regions (i.e. regions able to determine non-B structures plus their single-stranded DNA environment due to transitions between B and non-B DNA) represent about 13% of the studied genomes [<xref ref-type="bibr" rid="CR34">34</xref>, <xref ref-type="bibr" rid="CR35">35</xref>]. Among the seven kinds of non-B DNA determinants that were investigated, they showed [<xref ref-type="bibr" rid="CR33">33</xref>] that mammalian regions containing stretches of direct repeats and were composed mostly of G-quadruplexes (G4; i.e. regions containing the motif G<sub>3</sub>&#x02009;+&#x02009;<sub>N1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub> where N is any base including G) had pronounced effects on read depth and the error rate. Here, the impact of non-B DNA on RJF data was investigated.</p><sec id="Sec3"><title>PacBio reads and RJF GC-rich sequences</title><p id="Par36">In an effort to benchmark the reliability of the PacBio technology on RJF GC-rich sequences, we used two RJF GC-rich gene models: 1) a region coding the 18S&#x02013;5.8S-28S ribosomal RNA (rDNA) which is transcribed from clustered genes repeated in tandem on chromosome 16 [<xref ref-type="bibr" rid="CR36">36</xref>] and 2) the 5 exon gene encoding synaptic vesicle glycoprotein 2A (SV2A) which is located on microchromosome 25 [<xref ref-type="bibr" rid="CR37">37</xref>]. Using these two sequences as queries we searched the two RJF PacBio projects (accession numbers SRR2028042-SRR2028057 and SRR2028138-SRR2028233, 190 Gb., ~180X genome coverage, PacBio RS 2.3.0.0.140640) and SRR5444488-SRR5444513 (63 Gb., ~60X genome coverage, PacBio RS II 2.3.0.3.154799) that were available as databases for the the RJF.</p><p id="Par37">Our approach for this benchmarking effort was to compare the percent of PacBio reads we could match to the ribosomal and SV2A gene sequences using blastn and blasr, to the expected value based on the existing genome assembly and PacBio read genome coverage. For the ribosomal gene sequence (accession number KT445834, 11,863&#x02009;bp, 71.09% GC, repeated 350 times per genome [<xref ref-type="bibr" rid="CR38">38</xref>]) we found that only 37,925 kbp of the PacBio read sequences matched the gene sequence (identity match threshold 75% and above), while we expected 747,609&#x02009;bp (i.e. about 5% of the expected value) for a PacBio data set with 180X coverage. The equivalent numbers for the SV2A gene (accession NC_006112.4 from positions 1,851,072 to 1,865,776, 14,705&#x02009;bp, 62.25% GC, single copy gene) were 294,881&#x02009;bp coverage while we expected 2,646,720&#x02009;bp (i.e. about 11% of the expected value). This test was repeated on a second PacBio dataset with lower (60X) coverage with similar results (98,755&#x02009;bp coverage while we expected 882,300&#x02009;bp, that is approximately 11.2% of the expected value). In addition to this depletion in coverage, it should be noted that the read alignment within these two sequences were very patchy, some regions of the KT445834 sequence were not even covered.</p><p id="Par38">This indicated that for these two GC-rich sequence, queries were found 10&#x02013;20 fold below what would be expected in PacBio datasets. When similar searches were performed using the CPT1C and RNL3 gene sequences (36.24 and 54.24%GC respectively) no such coverage deficit was observed. With respect to sequence error rates, we observed that over the entire length of the CPT1C and RNL3 genes, it was close to that previously described when benchmarking this technology (error rate ranging from 11 to 15%, depending on the sequence; for review see [<xref ref-type="bibr" rid="CR22">22</xref>]). This was in striking contrast to the rDNA and SV2A genes for which error rates were between 15 to 25% (because the sequences were GC rich, sequences with error rates above 25% could not be properly aligned [<xref ref-type="bibr" rid="CR33">33</xref>]). Furthermore, we observed that only certain regions in long reads had an error rate below 25%. This explained why we obtained fragmented hits with these PacBio reads, regions in these reads with error rate above 25% being impossible to align.</p></sec><sec id="Sec4"><title>Presence of cDNA models missing in public datasets related to galGal5 and galGal6 except in Pacbio reads</title><p id="Par39">In order to evaluate the number of existing orphan cDNA models, we verified the status of those described in the literature during the last 5&#x000a0;years in the most recent releases of the RJF genome and cDNA models (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). We first gathered several sources of cDNA sequences that were previously validated [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR19">19</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>] as being absent from avian models and having an average GC content above that of genes in the galGal4 model [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR19">19</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>]. The majority of these cDNAs originated from a batch of 2323 avian cDNA models that were characterized from public transcriptomic data based on their interspecific conservation. For 2132 of them (91.7%), the presence of an orthologous gene was verified in human (<italic>Homo sapiens</italic>) and the Chinese soft-shell turtle (<italic>Pelodiscus sinensis</italic>) whilst they were absent from the galGal4 model and from the collared flycatcher genome model (<italic>Ficedula albicollis;</italic> FicAlb_1.4 genome model [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR20">20</xref>]; Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>a). Of these models, 1587 were detected in chicken transcriptomic data [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR20">20</xref>]. In addition, 16 other GC-rich chicken cDNA that were absent from the galGal5 and GalGal6 datasets were assembled from chicken transcriptomic data [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR22">22</xref>].
<fig id="Fig1"><label>Fig. 1</label><caption><p>Analysis process used to filter orphan cDNA available in the literature and to identify the presence among Pacbio reads in databases</p></caption><graphic xlink:href="12864_2019_6131_Fig1_HTML" id="MO1"/></fig></p><p id="Par40">Using Blast we looked for the presence of cDNAs for these 1603 genes in the most recent chicken datasets. Furthermore, we also examined: 1) whether each of these genes was annotated as a protein-coding sequence in galGal5 and galGal6, 2) whether each was present in the genomic sequence of galGal5 and galGal6 but not annotated as a CDS, and 3) whether each was present in the galGal5 and galGal6 CDS database from Ensembl (releases 94 and 96).</p><p id="Par41">We found that only 1579 cDNA models were present in the four NCBI and Ensembl (galGal5 and galGal6) CDS sources and in the genomic sequences of galGal5 and galGal6. Twenty one of the 1603 chicken cDNA models were absent from the galGal5 and GalGal6 annotations, only two sets of cDNA models were missing from galGal5 (model 1515_GALgal) and galGal6 (1962_GALgal, 5115_GALgal) Ensembl CDS (Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>b). Finally, we searched the whole genome sequence (WGS) and Transcription Shotgun Assembly (TSA) datasets at NCBI to verify whether some of these 24 chicken cDNA models might be present in other chicken sequencing projects. We found that none of these 24 chicken cDNA models were present in these datasets.</p><p id="Par42">These 370 orphan models were then used as queries to search the two chicken PacBio projects mentioned above. We found that 17 of the 22 models (77%) had hits to both PacBio projects (Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>c). With respect to the datasets used, these results highlighted the important progress achieved between the galGal4 and galGal6 models.</p><p id="Par43">The analysis of the GC content of these 21 models was compared to those of CDS in the galGal5 and galGal6 Ensembl datasets and 1603 chicken cDNAs from [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR19">19</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>]. Results revealed that orphan cDNAs displayed a median GC content of about 70% while those of both Ensembl CDS datasets were about 52% (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). However, it also revealed that there were two groups of cDNA models: 18 in the first cDNA group with sequences displaying GC content ranging from 60 to 80%; 3 in the second group of moderate GC content (42&#x02013;49%; 1515_GALgal, 3153_GALgal, 8985_GALgal). Together, these results support that GC content was not the main factor explaining the absence of some genes in genome models assembled from deeply sequenced genomes and transcriptomes.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Distribution of GC contents of sequences contained in galGal5 and 6 ensembl CDS, the 1603 cDNA described in [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR19">19</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>], and the remaining 24 orphan cDNA in all <italic>Gallus gallus</italic> datasets. Red lines and bars located median, quartile 1 and 3 values</p></caption><graphic xlink:href="12864_2019_6131_Fig2_HTML" id="MO2"/></fig></p><p id="Par44">The above data led us to conclude that there is yet unexploited information within PacBio datasets to hunt for genes with GC rich sequences. To find these genes one needs a reliable set of cDNA sequence queries. An expected the most difficult part of this process is to align the PacBio sequence reads to each other over their entire length. For a typical chromosomal region, estimates are that a 15 pass coverage with PacBio reads or 15 aligned reads yields sequence accuracy of over 99% [<xref ref-type="bibr" rid="CR25">25</xref>]. However, for GC-rich sequences we would expect that a higher number of aligned reads would be necessary to achieve the 99% reliability threshold since error rate of PacBio reads might be more elevated. Because of the under-representation of at least a part of GC rich sequences in PacBio datasets, a 15-pass coverage is probably insufficient and difficult to achieve. This will likely lead to gene models with lower accuracy rate. Such models would however still be useful in order to examine which sequence features are associated with sequencing and assembly difficulties.</p></sec><sec id="Sec5"><title>The RJF leptin gene</title><p id="Par45">The sequence of the RJF leptin gene was used to develop an approach to reconstruct GC-rich gene models from PacBio reads, then to evaluate them using RT-PCR and RNA-seq studies, and finally to verify that the observations done with the RJF could be generalized to most avian species.</p><sec id="Sec6"><title>Definition of a gene model with Illumina and PacBio reads</title><p id="Par46">The KT970642 sequence model of the RJF leptin gene was used for searching two Illumina libraries composed of 600&#x02009;bp genomic fragments of the RJF (each with a genome coverage ~10X). We extracted 4 reads from the region overlapping the first coding exons and 14 reads overlapping the second exon. These Illumina reads, along with those previously described [<xref ref-type="bibr" rid="CR11">11</xref>] were aligned using MUSCLE [<xref ref-type="bibr" rid="CR39">39</xref>] to improve the KT970642 model and to create a new gene model with extended sequences at both model extremities. This second model was used to search the two PacBio datasets described above using blastn and blasr. We extracted 11 PacBio reads from each dataset that fully or partly overlapped the sequence model and displayed sequence identities ranging from 70 to 87% with the model. These reads were oriented and aligned with MUSCLE. Sequence alignments were checked manually before adding aligned Illumina sequences (Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>). This resulted in a 2577&#x02009;bp genomic sequence model that completely overlapped both coding exons (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a), and in a cDNA sequence model that included both coding exons and 95 nucleotides downstream of the stop codon that extended into a putative polyadenylation signal (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>b). The final genomic model was used to search the PacBio datasets a second time but no new reads with sequence identity of 70% or above were found. This suggests that the sequence accuracy of PacBio reads was limited on such a GC rich sequence (64.4% GC for the genomic model and 68.5% for the cDNA model). We estimated that our model was 15 to 20-fold under-represented in PacBio reads. Whatever the enzymatic procedure used, the intron and the 5&#x02032; and 3&#x02032; regions of this model prevented their amplification by PCR from genomic DNA (gDNA) for verification by Sanger sequencing.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Nucleic acid sequence models of (<bold>a</bold>) region containing the coding exons split by a 1533&#x02009;bp intron in the RJF leptin gene and (<bold>b</bold>) tcoding region within its mRNA. The start and stop codons, the dinucleotides at the RNA splicing site and the putative polyadenylation signal are shown in bold and indicated in blue, red and green respectively. Annealing sites for primers designed for PCR and RT-PCR are boxed. In (<bold>a</bold>), the protein sequence of the RJF leptin is shown in italics below the nucleic acid sequence of the coding regions. Complementary &#x0201c;CCCCCCCan&#x0201d; and &#x0201c;ttGGGGGGG&#x0201d; motifs are indicated in grey and yellow respectively. Single nucleotide polymorphisms with KT970642 sequence are indicated in red. In (<bold>b</bold>), motifs matching with the consensus of G4 structures (G<sub>3</sub>&#x02009;+&#x02009;<sub>N1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>) are underlined and involved guanine stretches are shown in red</p></caption><graphic xlink:href="12864_2019_6131_Fig3_HTML" id="MO3"/></fig></p><p id="Par47">Analysis of the RJF leptin gene model revealed that it has sequence characteristics not found for this gene in other vertebrate species. First, the intron between the two coding exons was longer than in other avian species (1497-bp in the RJF, while the largest previously described such intron was only 900&#x02009;bp in <italic>Melopsittacus undulatus</italic>, accession KJ196275.1). Second, this gene had splice sites CG/AG that were atypical, but not unheard of [<xref ref-type="bibr" rid="CR40">40</xref>]. Third, this gene contains two types of GC rich repeated motifs that are complementary in sequence and not located in the same genomic regions. The &#x0201c;CCCCCCCAN&#x0201d; consensus motif spans the exonic regions while the &#x0201c;TTGGGGGGG&#x0201d; consensus motif is concentrated on the exon separating both coding exons and in the 3&#x02032; region of the second coding exon. Interestingly, the sequence of these motifs were reverse complements and similar to the (TTAGGG)<sub>n</sub> telomeric repeats [<xref ref-type="bibr" rid="CR41">41</xref>]. This suggests they may be related to the subtelomeric location of this gene in chromosome 1 [<xref ref-type="bibr" rid="CR4">4</xref>]. Finally, numerous stretches of guanines in the sequence of the genomic and cDNA models (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>b) matched with the consensus of the G4 structure described above (G<sub>3</sub>&#x02009;+&#x02009;<sub>N1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>) where N is any base including G [<xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR42">42</xref>]. This feature is important because these structures are known to prevent or diminish the capacity of DNA and RNA polymerases and reverse transcriptases to replicate nucleic acids [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR43">43</xref>&#x02013;<xref ref-type="bibr" rid="CR45">45</xref>]. Similarly, the complementarity of &#x0201c;CCCCCCCAN&#x0201d; and &#x0201c;TTGGGGGGG&#x0201d; repeated motifs in the putative 5&#x02032; and 3&#x02032; untranslated regions and in exons suggested that the leptin mRNA were able to assemble complex intrastrand secondary structures that might be very stable because of their elevated GC content.</p></sec><sec id="Sec7"><title>Impact of the choice of replication enzymes used in assays</title><p id="Par48">In order to verify whether motifs capable of assembling into secondary structures in the leptin gene could alter its expression using RT-qPCR we developed two PCR assays. The first assay amplified an inner fragment of the second coding exons using RJF gDNA. It used several primer pairs including those previously published [<xref ref-type="bibr" rid="CR11">11</xref>], and two thermostable DNA polymerases. The first polymerase was the routine GoTaq&#x000ae; (Promega), the second an enzyme designed to be efficient in GC-rich regions, the Taq KAPA HiFi (Kapa Biosystems). An amplification product with a suitable size and sequence was only obtained with the primer pair CTACTGCTGCAGCTGCGAAG and CTTCAACTCAGGCTCCAATTG and the GoTaq&#x000ae; enzyme. The specificity of this PCR assays was tested using gDNA samples from chicken lines and related or domesticated species (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a). Results showed that the inner fragment of the leptin exon 2 could only be amplified from animals in the <italic>Gallus</italic> genus. In the second assay, three reverse transcriptases (RT) were tested for their ability to synthesize suitable cDNA for our PCR assay. The first RT was the classic Moloney Murine Leukemia Virus (M-MLV) RT (Promega), while the other two RT were enzymes engineered to deconstruct the intra-strand structures in messenger RNA (mRNA) molecules, the Opti M-MLV RT (Eurobio) and the SuperScript IV RT (Invitrogen). Whatever the priming strategy used (oligo-dT, hexanucleotides or both), only the engineered RTs achieved successful reverse transcription of PCR (RT-PCR) products. Because only a very faint band was obtained on agarose gel with the SuperScript IV RT, the Opti M-MLV RT was thereafter used to follow RNA expression of the leptin gene in various tissue samples. This included abdominal fat where the leptin gene was recently found to be absent from, based on RNA-seq data [<xref ref-type="bibr" rid="CR21">21</xref>]. cDNA synthesized using the Opti M-MLV RT (Eurobio) showed that there was strong leptin expression in the hen cerebellum and in the ovary as previously shown [<xref ref-type="bibr" rid="CR11">11</xref>], but also in abdominal adipose and subcutaneous adipose tissues (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>b). These results confirmed that the efficiency of cDNA synthesis widely depends on the replication ability the RT.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Products obtained with PCR and RT-qPCR assays targeted on the chicken leptin gene. In (<bold>a</bold>), amplification products obtained using a PCR assay on the chicken leptin gene. Genomic DNA samples were purified from a blood samples of single females belonging to the RJF species (1), the alsacian old French chicken line (2), the Araucana chicken line (3), a white leghorn chicken line (4), the <italic>Gallus sonneratii</italic> species (5), the quail species <italic>Coturnix japonica</italic> (6), the turkey species <italic>Meleagris gallopavo</italic> (7), the pekin duck species <italic>Anas platyrhynchos domesticus</italic> (8) and the duck of barbary species <italic>Cairina moschata</italic> (9). Lane 10 shows a control sample without gDNA. Lane MW shows a 100-bp ladder with molecular sizes in base pairs indicated on the right. In (<bold>b</bold>), RT-qPCR results indicating the relative expression of the leptin gene</p></caption><graphic xlink:href="12864_2019_6131_Fig4_HTML" id="MO4"/></fig></p><p id="Par49">Following these findings, we searched PacBio RNA-seq projects (PRJEB13246, PRJEB13248 and PRJEB12891 [<xref ref-type="bibr" rid="CR46">46</xref>]) in order to locate leptin sequences. These datasets were partly produced from brain mRNA, including those of the cerebellum that are appropriate for locating leptin transcripts. Our searches were in vain, probably because these datasets were obtained from cDNA synthesized with a classical M-MLV RT that is unable to synthesize through stable secondary intrastrand structures in RNA molecules.</p><p id="Par50">The efficiency of RT-PCR amplifications of the leptin gene did not seem as high as for other genes such as the glyceraldehyde 3-phosphate dehydrogenase (GAPDH), likely because of the elevated GC content in the leptin gene. Therefore, our quantitative reverse transcription PCR (RT-qPCR) assay can be used to compare leptin mRNA amounts between RNA samples, but is not suitable for comparing leptin mRNA amounts with those of other genes with a similar or lower GC content.</p></sec><sec id="Sec8"><title>Genomic features of leptin genes in other avian species</title><p id="Par51">We investigated whether the unusual sequence features of the RJF leptin gene (GC content, presence of direct and inverted repeats, and motifs susceptible to assemble intrastrand G4 structures) were conserved among bird genomes. These features are of particular interest because they may limit the ability of researchers to sequence and annotate some bird genes.</p><p id="Par52">We searched avian genomes in whole-genome shotgun contigs databases using tblastn at NCBI. In some species the leptin gene was found successfully assembled into contigs from Illumina reads. However, in other species, such as the mallard duck or the Japanese quail, no hits were found. Phylogenetically birds differentiated into two main lineages during evolution, the Palaeognathae and the Neognathae. Our investigation revealed that the genomic copies of avian leptin genes were available in databases only for species belonging to the Neognathae lineage, such as the zebra finch, the wavy parakeet, the collared flycatcher, the bald eagle and the golden eagle and the double-crested cormorant (Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>a and f). The gene sequences from all of these species displayed an average GC content 8&#x02013;10% above that of the RJF, had introns ranging from 400 to 700&#x02009;bp in length with GT/AG splice sites (except in the double-crested cormorant that displayed a CG/AG splice site) and (G<sub>3</sub>&#x02009;+&#x02009;<sub>N1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>) motifs and direct repeats.</p><p id="Par53">In order to further our understanding of leptin genes in both bird lineages, we enlarged our searches to raw sequences. We first searched in our own Illumina datasets for two neognathae species, the African fisher eagle and the black-chinned hummingbird, and one palaeognathae species, the ostrich. In addition, we used public Illumina datasets for five palaeognathae species, the white-throated tinamou, the brown kiwi, the mallard duck, the muscovy duck and the Japanese quail. We successfully reconstructed the leptin gene of the African fisher eagle (Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>&#x02009;g) and obtained a partial sequence for the bird with the smallest known genome size, the black-chinned hummingbird (Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>&#x02009;h [<xref ref-type="bibr" rid="CR47">47</xref>]). These genomic copies displayed similar sequence features to those of other neognathae species (GC content, presence of potential G4 motifs), but the GC content of the black-chinned hummingbird was the most elevated (82.49%). We also reconstructed the leptin gene of the ostrich (Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>i), that of the muscovy duck (Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>j) with the inner region of the intron lacking, and two partial sequences of the white-throated tinamou and the brown kiwi (Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>&#x02009;k and l). In these four palaeognathae species and in the RJF, we did not find any feature difference with those of neognathae, excepted that all the genes displayed a GC/AG splice site and a larger intron (800 to 1500&#x02009;bp). However, we did not find Illumina or PacBio reads for the mallard duck or the Japanese quail. This suggested that the leptin gene sequence in these two species may be particularly difficult to isolate in Illumina and PacBio libraries and/or sequencing.</p><p id="Par54">We concluded that palaeognathae leptin genes might be more difficult to assemble because they may be slightly larger due their intron size. This in turn means that they would likely contain more tandem repeats and motifs able to assemble into G4 structures which also might be more or less stable, depending on the biochemical environment [<xref ref-type="bibr" rid="CR48">48</xref>].</p></sec></sec><sec id="Sec9"><title>Sequence features of other orphan cDNAs from RJF</title><p id="Par55">Because we were unable to find a solution to develop an automated alignment pipeline for GC-rich PacBio reads (even with a learning algorithm since the datasets for the learning are not available) we investigated orphan cDNAs manually for four cDNAs, chosen among Hron&#x02019;s RJF cDNA (Hron et al. 2015) that were found not to be highly conserved in DNA sequence in other avian species [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR20">20</xref>]. The four cDNA candidates had at least two exons and a gene size below 10&#x02013;15 kbp in other non-avian species. Indeed, the size and the sequence features of introns of these GC-rich cDNA models is a factor limiting the reliability of such investigations.</p><sec id="Sec10"><title>TNF&#x003b1; gene</title><p id="Par56">TNF&#x003b1; is a cell signaling protein (cytokine) involved in systemic inflammation and is one of the cytokines that make up the acute phase reaction. The features of a TNF&#x003b1; gene recently described in the crow <italic>Corvus cornix cornix</italic> [<xref ref-type="bibr" rid="CR22">22</xref>] were first investigated before to elucidate the organization of RJF TNF&#x003b1; gene. The gene in this species is short, 1680&#x02009;bp in length, and has a GC content of 69.58%, lower than that of its coding exons (77.81%). This gene contains four direct repeats in its first intron (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>a, regions in black boxes) and one in the reverse orientation at the beginning of the third intron (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>a, region in dark grey box). This last intron also contains two other types of inserted tandem repeats (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>a, region in green and blue boxes). In this third intron and in the 3&#x02032; region, several G-rich motifs were found that are capable of assembling into G4 structures.
<fig id="Fig5"><label>Fig. 5</label><caption><p>Sequence of the crow (<italic>C. cornix cornix</italic>) gene coding for TNF&#x003b1; (<bold>b</bold>) and organization of orthologous genes in the RJF (b). In (<bold>a</bold>), the crow sequence was extracted between positions 44,354 to 46,058 from the MVNZ01000346.1 contig [<xref ref-type="bibr" rid="CR22">22</xref>], a sequence that was present in the original version of the <italic>C. cornix cornix</italic> genome model, but which was later split into several contigs in the second version. Above the DNA sequence is the translation into amino acids in all three frames of the plus strand. The crow TNF&#x003b1; sequence is shown in yellow. This sequence contained frame shifts in exons that resulted from errors in the assembly of the PacBio and Illumina reads. The start and stop codons are shown in bold and blue. Statistically significant blocks in direct and inverted repeats were identified with MEME at <ext-link ext-link-type="uri" xlink:href="http://meme-suite.org/tools/meme">http://meme-suite.org/tools/meme</ext-link> and are indicated in black or dark grey boxes (depending on their orientation), green and blue boxes respectively. Motifs matching with the consensus of G4 structures (G<sub>3</sub>&#x02009;+&#x02009;<sub>N1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>) are indicated in red. In (<bold>b</bold>) sequence features of the RJF gene are summarized, these were deduced from the alignment of PacBio reads (Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>). The start and end positions of the PacBio read alignments for the four exons are indicated. Motifs in parentheses indicate tandem repeats, these are the only components of introns and the 5&#x02032; and 3&#x02032; regions. n, n&#x02019;, n&#x0201d;, n&#x0201d;&#x02018;, n&#x0201d;&#x0201c; and n&#x0201d;&#x0201c;&#x02018;describe different numbers of tandem repetition between motifs</p></caption><graphic xlink:href="12864_2019_6131_Fig5_HTML" id="MO5"/></fig></p><p id="Par57">A model of the RJF TNF&#x003b1; gene was obtained by aligning 18 reads (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>) extracted from the two PacBio datasets described above using the MF000729.1 sequence [<xref ref-type="bibr" rid="CR22">22</xref>] as a query in blastn and blasr searches. We calculated that the read coverage overlapping the query was at least 25 to 30 fold below that expected in files with a theoretical coverage of 180X and 60X respectively. The alignment was 24,277&#x02009;bp long and included four coding exons, all regions covered by at least 5 reads. We did not find any Illumina reads overlapping exon-intron junctions in the chicken datasets described above. Because of low coverage and the large error rate (&#x0003e;&#x02009;20%), only a consensus model of sequence organisation was setup for the RJF TNF&#x003b1; gene (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>b). Because approximately one third of positions in the alignment were used to manage mismatches and indels, we estimate that the RJF TNF&#x003b1; gene is approximately 14,400&#x02009;bp long (i.e. about 10 fold longer than that of the crow). The TNF&#x003b1; gene is known to display 2 to 3-fold size variations among vertebrates (2769&#x02009;bp in human, 5244&#x02009;bp in the coelacanth, 2817&#x02009;bp in <italic>Xenopus tropicalis</italic>, 6236&#x02009;bp in the anole lizard and 2346&#x02009;bp in the Chinese softshell turtle) but the RJF one is the longest described one. The RJF TNF&#x003b1; gene model also revealed that the 5&#x02032; and 3&#x02032; non-coding regions of this gene and its introns were filled with motifs repeated in tandem. Whatever the non-coding region, the sequence of these repeats allowed for the detection of several hundreds of G-rich motifs capable of assembling into G4 structures on the plus or the minus strand.</p></sec><sec id="Sec11"><title>MRPL52 gene</title><p id="Par58">MRPL52 (a.k.a. SLC7A7) is a component of the mitochondrial ribosome. A RJF model of this gene was obtained by aligning 12 reads (Additional&#x000a0;file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>) extracted from the two PacBio datasets described above using its cDNA model as a query in blastn and blasr searches. The alignment was 3 kbp long and included five coding exons. Because of low coverage and low identity rates between PacBio reads (&#x0003c;&#x02009;80%; due to sequencing errors), only a consensus model of sequence organisation was set-up for the RJF MRPL52 gene (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>a). The MRPL52 gene is known to display 2-fold size variations among vertebrates (5158&#x02009;bp in human, 2847&#x02009;bp in the mouse, and 5467&#x02009;bp in the anole lizard). The RJF MRPL52 gene model also revealed that its introns were filled with motifs repeated in tandem that contained numerous G-rich motifs capable of assembling into G4 structures on the plus or the minus strand.
<fig id="Fig6"><label>Fig. 6</label><caption><p>Sequence organization of MRPL52 (<bold>a</bold>), PCP2 (<bold>b</bold>) and PET100 (<bold>c</bold>) genes in the RJF. The start and end positions in the PacBio read alignment of four exons are indicated. Motifs in parentheses indicate tandem repeats, these are the only components of introns and the 5&#x02032; and 3&#x02032; regions. n, n&#x02019;, n&#x0201d;, n&#x0201d;&#x02018;, n&#x0201d;&#x0201c; and n&#x0201d;&#x0201c;&#x02018;describe different numbers of tandem repetition between motifs</p></caption><graphic xlink:href="12864_2019_6131_Fig6_HTML" id="MO6"/></fig></p></sec><sec id="Sec12"><title>PCP2 gene</title><p id="Par59">PCP2 is a member of the GoLoco domain-containing family, and is only found in cerebellar Purkinje cells and retinal ipolar cells in vertebrates. A model of the RJF PCP2 gene was obtained by aligning only 7 reads (Additional&#x000a0;file&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref>) extracted from the two PacBio datasets as described above. The alignment of the PCP2 gene was 3451&#x02009;bp long and included two coding exons. Because of low coverage and identity rate between reads (&#x0003c;&#x02009;80%), only a consensus model of sequence organisation was established (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>b). PCP2 gene sizes do not display large variations among vertebrates (2137&#x02009;bp in human, 2174&#x02009;bp in mouse, and 2821&#x02009;bp in the anole lizard). Therefore, its size in the RJF did not seem to be a distinguishing feature. However, it also displayed introns filled with motifs repeated in tandem that contained numerous G-rich motifs capable of assembling into G4 structures on the plus or the minus strand.</p></sec><sec id="Sec13"><title>PET100 gene</title><p id="Par60">PET100 is involved in mitochondrial Complex IV (cytochrome c oxidase) biogenesis. A model of the RJF PET100 gene was obtained by aligning 10 reads (Additional&#x000a0;file&#x000a0;<xref rid="MOESM9" ref-type="media">9</xref>) extracted from the two PacBio datasets as described above for TNF&#x003b1;. The alignment of the PET100 gene was 5321&#x02009;bp long and included four coding exons. Because of low coverage and the large error rate (&#x0003e;&#x02009;20%), only a consensus model of sequence organisation was set-up for the RJF PET100 gene (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>c). Although the P100 gene has few annotations in sauropsida species, its size does not seem to vary much (2219 in human, 4300 in mouse, and 2660 in zebrafish). Gene size was not a distinguishing feature. Similar to the other four genes, its introns displayed numerous motifs repeated in tandem that contained numerous G-rich motifs capable of assembling into G4 structures on the plus or the minus strand.</p><p id="Par61">Together, these five case studies suggest that neither GC content nor gene size were likely the reason why these genes were difficult to sequence and assemble. Our hypothesis was that the presence of numerous tandem repeated motifs containing abundant G-rich motifs capable of assembling into G4 structures in introns (and in some cases within the CDS) had features that probably made them difficult to sequence and assemble using second generation sequencing technology.</p></sec></sec></sec><sec id="Sec14"><title>Discussion</title><p id="Par62">The difficulty of sequencing and assembling missing regions of avian genome models has typically been interpreted as the result of two sequence characteristics, elevated GC contents and high rates of interspersed and tandem repeats [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR49">49</xref>]. In an effort to address these issues Tilak et al. [<xref ref-type="bibr" rid="CR50">50</xref>] suggested that Illumina libraries be made that were enriched in GC-rich sequences. Here, using 5 gene cDNA sequences, a complete set of 21 orphan cDNAs, and genomic copies of the SV2A and rDNA genes as queries we searched such enriched datasets (IDs: ERX2234588 to ERX2234598) from RJF gDNA fragments. However, we did not find any more reads with these enriched data sets than with standard libraries. This indicated that for these genes, GC content was not the main factor limiting their presence in Illumina gDNA libraries and/or sequencing. Furthermore, we would note that some avian cDNAs with higher GC content than either RJF leptin or TNF&#x003b1; genes have successfully been sequences and assembled using Illumina technology (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>).</p><p id="Par63">Rather than just GC richness, we find that the presence of motifs capable of assembling into G4 structures has the most impact on the ability to sequence a region by Illumina technology (26&#x02013;30). RJF leptin and TNF&#x003b1; gene size were larger than their orthologues in other vertebrate species, their non-coding regions rich were mostly composed of short tandem repeats, sequences ideal for preventing assembly and that were absent in non avian orthologs that were so far assembled. Furthermore, these non-coding regions were not represented among Illumina gDNA library reads. This suggests either that these reads were not integrated during library creation, or could not be sequenced using Illumina technology.</p><p id="Par64">On the other hand, searching PacBio reads revealed this technology produced reads overlapping the RJF leptin, TNF&#x003b1;, MRPL52, PCP2 and PET100 genes. However, coverage of GC rich regions investigated here was lower than expected and reads displayed an error rate that was significantly higher than expected [<xref ref-type="bibr" rid="CR33">33</xref>]. Therefore, PacBio technology allowed for sequencing of regions with a GC content &#x0003e;&#x02009;60%, regions that were often fully absent from Illumina datasets. However, it did this less efficiently than for regions with a GC content &#x0003c;&#x02009;60%. We hypothesize that this was due to the presence of G4 structures and dyadic intrastrand structures in GC-rich regions.</p><p id="Par65">We provided genomic sequence models for five RJF genes, as well for the leptin genes of several avian species. Such genomic models, reconstructed from under-represented reads, should to be validated by PCR and sequencing. Unfortunately, and in spite of huge efforts, we could not get validation for the intronic sequences because suitable primers could not be designed because of their high GC content. We only succeeded in amplifying an inner fragment of the second coding exon from RJF gDNA (as described above) and from the pekin duck (using as forward and reverse primers, CAGCGGCTGCAGCTTTTC and CAACCGTCCCATGGCCAAA and PCR conditions similar to those used here for the RJF). The need to validate cDNA models by PCR is essential because trace reads originating from SRA files may be contaminated by outside sources. An example of such contamination is the 27 RNA-seq datasets of the Chickpress project (PREJB4677) that contained 20&#x02013;30% human cDNAs. Here, we only used cDNA sequences as queries that had previously been validated [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR22">22</xref>]. These five genes should, at a later point, have the sequence of their non-coding regions verified, once appropriate amplification and sequencing procedures become available. Nevertheless, the gene models presented here should be of great interest to the development of solutions aiming at deconstructing secondary intrastrand DNA structures to improve sequencing efficiency of Illumina and PacBio technologies. To our knowledge, these RJF gene models are the first public dataset of such sequences that are very reluctant to sequencing. These could be used for evaluating the efficiency of high throughput sequencing technologies<italic>.</italic></p></sec><sec id="Sec15"><title>Conclusions</title><p id="Par66">High GC content was not the principal reason why certain regions of avian genomes are missing from genome assembly models. Instead, it is the presence of tandem repeats containing motifs capable of assembling into very stable secondary structures that is likely responsible. To our knowledge, our work is the first study dealing with this issue in the context of avian genomics. Our results are also in agreement with the recent literature in this field about the reliability of the Pacbio Technology with regard to the sequencing of non-B genomic regions [<xref ref-type="bibr" rid="CR33">33</xref>]. Because G-quadruplexes most likely explain the absence of some genes and regions in genome and transcript models (e.g. in Additional&#x000a0;file&#x000a0;<xref rid="MOESM10" ref-type="media">10</xref>), solutions to circumvent these problems and to obtain their sequence will likely be dependent sequencing technique innovations. Our study was also the first one to bring datasets of genomic sequences that could be useful to benchmark sequencing technique innovations dedicated to circumvent problems raised by very stable secondary structures.</p></sec><sec id="Sec16"><title>Methods</title><sec id="Sec17"><title>Biological samples</title><p id="Par67">All biological samples used for DNA extracts were females. Blood samples from chicken lines, araucana, alsacian and white leghorn, japanese quail, turkey, pekin duck and duck of barbary were obtained from breeds maintained at the INRA UE1295 PEAT experimental facilities (P&#x000f4;le96 d&#x02019;Exp&#x000e9;rimentation Animale de Tours, Agreement N&#x000b0; C37&#x02013;175-1). Those from the RJF <italic>Gallus gallus</italic> and the grey jungle fowl <italic>Gallus sonneratii</italic> were supplied by Christophe Bec, Parc des oiseaux (Villars les Dombes 01330 France) et Christophe Auzou (Grand Champs 89,350 France). Those from the ostrich and African fisher eagle were supplied respectively by la R&#x000e9;serve de Beaumarchais (Autr&#x000e8;che 37,110 France) and Zooparc de Beauval (Saint Aignan 41,198 France). Biopsies of pectoral skeletal muscles from black-chinned hummingbird were supplied by the Department of Biology &#x00026; Museum of Southwestern Biology of University of New Mexico (USA). For RNA extracts, tissues biopsies were supplied by Hendrix Genetics (Saint Laurent de la Plaine, France) and were from broiler breeder female chicks (Cobb 500), 35&#x02009;weeks of age.</p></sec><sec id="Sec18"><title>Nucleic acid purifications</title><p id="Par68">gDNA samples were prepared from 100&#x02009;&#x003bc;L of fresh red blood cells using the Nucleospin Tissue kit (Macherey-Nagel). Total RNA was extracted from abdominal adipose tissue, subcutaneous adipose tissue, liver and Pecto skeletal muscle from 35&#x02009;weeks-old hens by homogenization in TRIzol reagent using an Ultraturax, according to the manufacturer&#x02019;s recommendations (Invitrogen by Life Technologies, Villebon sur Yvette, France). The quality and concentration of nucleic acid samples were evaluated using a NanoDrop&#x02122; 2000 spectrophotometer.</p></sec><sec id="Sec19"><title>Illumina sequencing of RJF genome</title><p id="Par69">Library construction and sequencing were performed at the Plateforme de S&#x000e9;quen&#x000e7;age Haut D&#x000e9;bit I2BC (Gif-sur-Yvette 91,198 France). Illumina libraries of 600&#x02009;bp genomic fragments were prepared without PCR amplification, as recommended, to optimize for the presence GC and AT-rich sequences (Aird et al. 2011, Oyola et al. 2012). Paired-end sequencing with reads of 75 and 250 nucleotides in length were performed with at least 10X coverage. All raw and processed data are available through the European Nucleotide Archive under accession numbers PRJEB22479 and PRJEB25675 for the RJF, PRJEB24169 for the ostrich, PRJEB27669 for the african fisher eagle and PRJEB27670, for the back-chinned hummingbird.</p></sec><sec id="Sec20"><title>Searching databases</title><p id="Par70">Files containing the 2323 cDNA models were downloaded from 10.6084/m9.figshare.5202853 [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR20">20</xref>]. 2132 of these were validated [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR20">20</xref>]. Their functional annotation must be carefully reviewed (Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>a.1), among these a few errors were found. For example, cDNA models in file aves_ENSPSIG00000014155_Hmm5_gapClean40.fasta was annotated as coding piwiL4 while it should have been piwiL1; data corresponding to orthologues of the ENSPSIG00000011968 <italic>Pelodicus</italic> sequence and describing cDNA coding the SLC2A4/GLUT4 protein were not released by the authors. The galGal5 and galGal6 genome sequences and coding sequences were downloaded from <ext-link ext-link-type="uri" xlink:href="ftp://ftp.ncbi.nlm.nih.gov/genomes/Gallus_gallus">ftp://ftp.ncbi.nlm.nih.gov/genomes/Gallus_gallus</ext-link>. Release 94 and 96 of the Ensembl chiken CDS were downloaded from <ext-link ext-link-type="uri" xlink:href="ftp://ftp.ensembl.org/pub/release-94/fasta/gallus_gallus/">ftp://ftp.ensembl.org/pub/release-94/fasta/gallus_gallus/</ext-link> and <ext-link ext-link-type="uri" xlink:href="ftp://ftp.ensembl.org/pub/release-96/fasta/gallus_gallus/">ftp://ftp.ensembl.org/pub/release-96/fasta/gallus_gallus/</ext-link>. Searches with cDNA models as queries for galGal5 and galGal6 CDSs available at NCBI Ensembl release 94 &#x00026; 96. These were done using blastn within the script blastfasta.pl as described [<xref ref-type="bibr" rid="CR51">51</xref>]. Searches for polyexonic genes using cDNA models as queries in the galGal5 and galGal6 genome models were done using blastn and HSPs were fused using agregfilter.pl as described [<xref ref-type="bibr" rid="CR51">51</xref>]. Searches for polyexonic genes using cDNA models as queries in Gallus WGS and TSA datasets were done using the &#x0201c;Remote BLAST&#x0201d; plugin at NCBI as recommended (<ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/books/NBK279668/">https://www.ncbi.nlm.nih.gov/books/NBK279668/</ext-link>). Hits were defined as positive when more than 90% of the query was aligned with the subject sequence with an identity above 95%. Such a rate of identity was used because numerous cDNA models contained long stretches of Ns.</p><p id="Par71">To reconstruct leptin cDNAs in five palaeognathae species, we used public Illumina datasets for the white-throated tinamou (dataset ID: SRR952232 to SRR952238), the brown kiwi (dataset ID: ERR519283 to ERR519288 and ERR522063 to ERR5220668), the mallard duck (dataset ID: SRR7194749 to SRR7194798), the muscovy duck (dataset ID: SRR6300650 to SRR6300675 and SRR6305144) and the Japanese quail (24 Illumina datasets of PRJNA292031 plus about 20 Gbp of PacBio reads kindly supplied by Dr. J. Gros (Pasteur Institute, Paris, France).</p></sec><sec id="Sec21"><title>Construction of gene models from Pacbio reads</title><p id="Par72">Files containing Illumina and PacBio reads from RJF available from public databases were downloaded using the sra toolkit (downloaded at <ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/sra/docs/toolkitsoft/">https://www.ncbi.nlm.nih.gov/sra/docs/toolkitsoft/</ext-link>) to produce fasta formatted files. Presence of contamination in Illumina reads was tested for each file by aligning it to the genomes of the most commonly studied genetic models (<italic>Escherichia coli</italic>, <italic>Saccharomyces cerevisiae</italic>, <italic>Drosophila melanogaster</italic>, <italic>Danio rerio</italic>, <italic>Mus musculus</italic> and <italic>Homo sapiens</italic>) using HISAT2 or bowtie2, depending on the mRNA or the genomic origin of the nucleic acids. Below 2.5% of aligned reads per file or alignment, we considered that there was no significant contamination by these species. Illumina reads were thereafter searched using blastn. PacBio reads were searched using blastn and blasr [<xref ref-type="bibr" rid="CR52">52</xref>] and produced similar results once parameters were optimized (blastn command line: blastn -db [name of the database for blast] -query [query.fa] -out [name of the file containing results] -evalue 100 -task blastn -word_size 5 -dust no -num_threads 1; blasr command line: blasr [PacBio reads file.fa] [query.fa] --header --minReadLength 400 --minAlnLength 400 --bestn 100 --out Resoutput -m 0 --nproc 1). Reads were extracted and oriented before alignment with MUSCLE for Illumina reads. Pacbio reads were aligned one by one using the --add option of mafft and as an alignment seed one cDNA model previously aligned with Illumina reads (as for the leptin gene) or one cDNA model manually aligned with a Pacbio reads, generally the best hit in blasr and blastn results was used. The alignment of each Pacbio read aligned with mafft was verified and adjusted manually in order to properly align poorly sequenced regions as well as to identify polypurine or polypyrimidine tracts insertions that are sporadically found in sequenced GC-rich reads. The five alignments produced here can be visualized using Aliview (<ext-link ext-link-type="uri" xlink:href="https://github.com/AliView/AliView">https://github.com/AliView/AliView</ext-link>) or Seaview (<ext-link ext-link-type="uri" xlink:href="http://www.seaviewfishing.com/DownloadSoftware.html">http://www.seaviewfishing.com/DownloadSoftware.html</ext-link>) freeware packages. Additional files&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref> and&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>,&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>,&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref>,&#x000a0;<xref rid="MOESM9" ref-type="media">9</xref> are in fasta format and contain the alignment of all Illumina and/or PacBio reads used to calculate the genomic models of the RJF leptin, TNF&#x003b1;, MRPL52, PCP2 and PET100 genes. In Additional file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>, the 10 sequences at the bottom of the alignment correspond to the Seroussi&#x02019;s et al. (2016) partial cDNA model and the 9 reads used to calculate it. Sequence names of each read are indicated. Label &#x0201c;strand (+)&#x0201d; or &#x0201c; strand (-)&#x0201d; at the end of the name indicated that the read was aligned in the forward or reverse-complement orientation. Sequence sections that were impossible to align were represented as N tracts. Features of orthologous genes in vertebrates were investigated using genomicus 94.01 at <ext-link ext-link-type="uri" xlink:href="http://www.genomicus.biologie.ens.fr/genomicus-94.01/cgi-bin/search.pl">http://www.genomicus.biologie.ens.fr/genomicus-94.01/cgi-bin/search.pl</ext-link>.</p></sec><sec id="Sec22"><title>Detection of non-B DNA G4 motifs</title><p id="Par73">To detect candidate motifs putatively able to assemble G4 and other non-B DNA motifs, we used facilities available at <ext-link ext-link-type="uri" xlink:href="https://nonb-abcc.ncifcrf.gov/apps/site/default">https://nonb-abcc.ncifcrf.gov/apps/site/default</ext-link> [<xref ref-type="bibr" rid="CR35">35</xref>]. The detection mode of the G4 candidates is done by text mining for the detection of a strict motif G<sub>3</sub>&#x02009;+&#x02009;<sub>N1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>&#x02009;+&#x02009;N<sub>1-12</sub>G<sub>3</sub>. Because this motif did not stand any sequence degeneracy when a motif is detected, its detection probability was 100%.</p></sec><sec id="Sec23"><title>PCR on gDNA</title><p id="Par74">Our optimal procedure for PCR amplification was performed on 60&#x02009;ng of avian gDNA in 10&#x02009;mM Tris-HCl, pH&#x02009;9, 4&#x02009;mM MgCl2, 50&#x02009;mM KCl, 0.1% TritonX100, 150&#x02009;&#x003bc;M of each dNTP, and 0.1&#x02009;mM of each oligonucleotide in a 50&#x02009;&#x003bc;l reaction volume with 1 unit GoTaq DNA Polymerase (Promega). Each PCR was carried out in a programmable temperature controller (Eppendorf) for 30&#x02009;cycles. After initial denaturation (5&#x02009;min at 98&#x02009;&#x000b0;C), the cycle was as follows: denaturing at 98&#x02009;&#x000b0;C for 20&#x02033;, annealing at 60&#x02009;&#x000b0;C for 15&#x02033;, and extension at 72&#x02009;&#x000b0;C for 1&#x02032;. At the end of the 30th cycle, the heat denaturing step was omitted, and extension was allowed to proceed at 72&#x02009;&#x000b0;C for 5&#x02032;. Each amplified sample could then be purified using a QIAquick PCR purification kit (Qiagen) and sent to Eurofins Genomics for a Sanger sequencing in order to verify its leptin origin.</p></sec><sec id="Sec24"><title>RT-PCR assay</title><p id="Par75">The classic way we used to generate cDNA was by reverse transcription (RT) of total RNA (1&#x02009;&#x003bc;g) in a mixture comprising 0.5&#x02009;mM of each deoxyribonucleotide triphosphate (dATP, dGTP, dCTP and DTTP), 2&#x02009;M of RT buffer, 15&#x02009;&#x003bc;g/&#x003bc;L of oligodT, 0.125&#x02009;U of ribonuclease inhibitor, and 0.05&#x02009;U&#x02009;M-MLV RT for one hour at 37&#x02009;&#x000b0;C. Our optimal procedure to synthesize cDNA was performed from 0.1&#x02009;&#x003bc;g total RNA using a mix of oligodT and hexanucleotides as primers and the Opti M-MLV RT under conditions recommended by the supplier (Eurobio). Real-time PCR was performed using the MyiQ Cycle device (Bio-Rad, Marnes-la-Coquette, France), in a mixture containing SYBR Green Supermix 1X reagent (Bio-Rad, Marnes la Coquette, France), 250&#x02009;nM specific primers (Invitrogen by Life Technologies, Villebon sur Yvette, France) and 5&#x02009;&#x003bc;L of cDNA (diluted five-fold) for a total volume of 20&#x02009;&#x003bc;L. Samples were duplicated on the same plate and the following PCR procedure used: after an incubation of 2&#x02009;min at 50&#x02009;&#x000b0;C and a denaturation step of 10&#x02009;min at 95&#x02009;&#x000b0;C, samples were subjected to 40&#x02009;cycles (30&#x02009;s at 95&#x02009;&#x000b0;C, 30&#x02009;s at 60&#x02009;&#x000b0;C and 30&#x02009;s at 72&#x02009;&#x000b0;C). The levels of expression of messenger RNA were standardized to the GAPDH reference gene. For the leptin gene, the relative abundance of transcription was determined by the calculation of e<sup>-ct</sup>. Relative expression of the gene of interest was then related to the relative expression of the geometric mean of the GAPDH reference gene.</p></sec></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec25"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12864_2019_6131_MOESM1_ESM.docx"><caption><p><bold>Additional file 1.</bold> Sequences of one avian mRNA (a) and exons of one avian gene (b) coding for an intellectin-like protein (ITLN1), and their amino acid sequence comparison (c).</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12864_2019_6131_MOESM2_ESM.docx"><caption><p><bold>Additional file 2.</bold> The sequence of RJF genes coding for the carnitine O-palmitoyltransferase 1 (CPT1C) and the insulin-like peptide 5 (ISNL5).</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12864_2019_6131_MOESM3_ESM.xls"><caption><p><bold>Additional file 3.</bold> cDNA models described in [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR20">20</xref>].</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12864_2019_6131_MOESM4_ESM.fa"><caption><p><bold>Additional file 4.</bold> Alignment of Illumina and Pacbio reads used to define the genomic sequence of the RJF leptin gene.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12864_2019_6131_MOESM5_ESM.docx"><caption><p><bold>Additional file 5.</bold> Genomic sequences of the avian leptin gene</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="12864_2019_6131_MOESM6_ESM.txt"><caption><p><bold>Additional file 6.</bold> Alignment of Pacbio reads used to define the genomic sequence of the RJF TNF&#x003b1;gene.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="12864_2019_6131_MOESM7_ESM.txt"><caption><p><bold>Additional file 7.</bold> Alignment of Pacbio reads used to define the genomic sequence of the RJF MRPL52gene.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="12864_2019_6131_MOESM8_ESM.txt"><caption><p><bold>Additional file 8.</bold> Alignment of Pacbio reads used to define the genomic sequence of the RJF TNF gene.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="12864_2019_6131_MOESM9_ESM.txt"><caption><p><bold>Additional file 9.</bold> Alignment of Pacbio reads used to define the genomic sequence of the RJF PET100gene.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM10"><media xlink:href="12864_2019_6131_MOESM10_ESM.docx"><caption><p><bold>Additional file 10.</bold> Location of G-quadruplex structures in the 14 cDNA models described in [<xref ref-type="bibr" rid="CR19">19</xref>].</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>bp</term><def><p id="Par4">base pair</p></def></def-item><def-item><term>cDNA</term><def><p id="Par5">complementary DNA</p></def></def-item><def-item><term>CDS</term><def><p id="Par6">Coding sequence</p></def></def-item><def-item><term>CPT1C</term><def><p id="Par7">Carnitine O-palmitoyltransferase 1</p></def></def-item><def-item><term>DNA</term><def><p id="Par8">Deoxyribonucleic acid</p></def></def-item><def-item><term>G4</term><def><p id="Par9">G-quadruplex</p></def></def-item><def-item><term>GAPDH</term><def><p id="Par10">Glyceraldehyde 3-phosphate dehydrogenase</p></def></def-item><def-item><term>gDNA</term><def><p id="Par11">Genomic DNA</p></def></def-item><def-item><term>ITLN1</term><def><p id="Par12">Intellectin 1</p></def></def-item><def-item><term>M-MLV</term><def><p id="Par13">Moloney murine leukemia virus</p></def></def-item><def-item><term>mRNA</term><def><p id="Par14">messenger RNA</p></def></def-item><def-item><term>MRPL52</term><def><p id="Par15">Mitochondrial ribosomal protein L52</p></def></def-item><def-item><term>ORF</term><def><p id="Par16">Open reading frame</p></def></def-item><def-item><term>PCP2</term><def><p id="Par17">Purkinje cell protein 2,</p></def></def-item><def-item><term>PCR</term><def><p id="Par18">Polymerase chain reaction</p></def></def-item><def-item><term>PET100</term><def><p id="Par19">Protein homolog to yeast mitochondrial PET100</p></def></def-item><def-item><term>rDNA</term><def><p id="Par20">Region coding the 18S&#x02013;5.8S-28S ribosomal RNA</p></def></def-item><def-item><term>RLN3</term><def><p id="Par21">Relaxin 3</p></def></def-item><def-item><term>RNA</term><def><p id="Par22">Ribonucleic acid</p></def></def-item><def-item><term>RNA-seq</term><def><p id="Par23">RNA sequencing</p></def></def-item><def-item><term>rRNA</term><def><p id="Par24">18S&#x02013;5.8S-28S ribosomal RNA</p></def></def-item><def-item><term>RT</term><def><p id="Par25">Reverse transcription/transcriptase</p></def></def-item><def-item><term>RT-PCR</term><def><p id="Par26">Reverse transcription PCR</p></def></def-item><def-item><term>RT-qPCR</term><def><p id="Par27">Quantitative reverse transcription PCR</p></def></def-item><def-item><term>SV2A</term><def><p id="Par28">Synaptic vesicle glycoprotein 2A</p></def></def-item><def-item><term>TNFa</term><def><p id="Par29">Tumor necrosis factor &#x003b1;</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s12864-019-6131-1.</p></sec><ack><p>We thank colleagues of the CITES networks, Dr. Christopher Witt and DR Andy Johnson (Dept. of Biology &#x00026; Museum of Southwestern Biology, University of New Mexico, USA), Dr. T. Ryan Gregory (University of Guelph, Ontario, Canada), and Dr. Jacques Rigoulet, Dr. Dario Zuccon and Dr. Yann Locatelli who allowed us accessing to biopsies of black-chinned hummingbirds.</p></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>YB., BP and JD designed the study; LB and CR generated the data; YB, BP, FG and JD analysed de data; YB, PA, FG and JD wrote the manuscript. All authors have read and approved the manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>This work was supported by recurrent funds of the I.N.R.A., the C.N.R.S., and the project R&#x000e9;gion Centre AVIGES. The funding bodies have had no role in the design of the study and collection, analysis, and interpretation of data and in writing the manuscript.</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>The Illumina raw reads generated and analysed during the current study have been submitted to the European Nucleotide Archive (<ext-link ext-link-type="uri" xlink:href="https://www.ebi.ac.uk/ena/submit/sra/#home">https://www.ebi.ac.uk/ena/submit/sra/#home</ext-link>) and are available under accession numbers PRJEB22479, PRJEB24169, PRJEB25675, PRJEB27669 and PRJEB27670. Public datasets are available in the NCBI repository (<ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/sra/">https://www.ncbi.nlm.nih.gov/sra/</ext-link>) and their accession numbers were cited in the main text.</p></notes><notes><title>Ethics approval</title><p id="Par76">Tissues were collected from chicken reared at the PEAT experimental unit (INRA, Centre Val de Loire, Nouzilly, France). All experiments were approved by the Ethics Committee in Animal Experimentation of Val de Loire CEEA Vdl (permit number 01607.02). The CEEA vdl is registered by the National Committee &#x02018;Comit&#x000e9; National de R&#x000e9;flexion Ethique sur l&#x02019;Exp&#x000e9;rimentation Animale&#x02019; under the number 19.</p></notes><notes><title>Consent for publication</title><p id="Par77">Not applicable.</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p id="Par78">The authors declare that they have no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>International Chicken Genome Sequencing Consortium</collab></person-group><article-title>Sequence and comparative analysis of the chicken genome provide unique perspectives on vertebrate evolution</article-title><source>Nature.</source><year>2004</year><volume>432</volume><fpage>695</fpage><lpage>716</lpage><pub-id pub-id-type="doi">10.1038/nature03154</pub-id><pub-id pub-id-type="pmid">15592404</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Warren</surname><given-names>WC</given-names></name><name><surname>Hillier</surname><given-names>LW</given-names></name><name><surname>Tomlinson</surname><given-names>C</given-names></name><name><surname>Minx</surname><given-names>P</given-names></name><name><surname>Kremitzki</surname><given-names>M</given-names></name><name><surname>Graves</surname><given-names>T</given-names></name><name><surname>Markovic</surname><given-names>C</given-names></name><name><surname>Bouk</surname><given-names>N</given-names></name><name><surname>Pruitt</surname><given-names>KD</given-names></name><name><surname>Thibaud-Nissen</surname><given-names>F</given-names></name><name><surname>Schneider</surname><given-names>V</given-names></name><name><surname>Mansour</surname><given-names>TA</given-names></name><name><surname>Brown</surname><given-names>CT</given-names></name><name><surname>Zimin</surname><given-names>A</given-names></name><name><surname>Hawken</surname><given-names>R</given-names></name><name><surname>Abrahamsen</surname><given-names>M</given-names></name><name><surname>Pyrkosz</surname><given-names>AB</given-names></name><name><surname>Morisson</surname><given-names>M</given-names></name><name><surname>Fillon</surname><given-names>V</given-names></name><name><surname>Vignal</surname><given-names>A</given-names></name><name><surname>Chow</surname><given-names>W</given-names></name><name><surname>Howe</surname><given-names>K</given-names></name><name><surname>Fulton</surname><given-names>JE</given-names></name><name><surname>Miller</surname><given-names>MM</given-names></name><name><surname>Lovell</surname><given-names>P</given-names></name><name><surname>Mello</surname><given-names>CV</given-names></name><name><surname>Wirthlin</surname><given-names>M</given-names></name><name><surname>Mason</surname><given-names>AS</given-names></name><name><surname>Kuo</surname><given-names>R</given-names></name><name><surname>Burt</surname><given-names>DW</given-names></name><name><surname>Dodgson</surname><given-names>JB</given-names></name><name><surname>Cheng</surname><given-names>HH</given-names></name></person-group><article-title>A new chicken genome assembly provides insight into avian genome structure</article-title><source>G3</source><year>2017</year><volume>7</volume><fpage>109</fpage><lpage>117</lpage><pub-id pub-id-type="doi">10.1534/g3.116.035923</pub-id><pub-id pub-id-type="pmid">27852011</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guizard</surname><given-names>S</given-names></name><name><surname>Pi&#x000e9;gu</surname><given-names>B</given-names></name><name><surname>Arensburger</surname><given-names>P</given-names></name><name><surname>Guillou</surname><given-names>F</given-names></name><name><surname>Bigot</surname><given-names>Y</given-names></name></person-group><article-title>Deep landscape update of dispersed and tandem repeats in the genome model of the red jungle fowl, Gallus gallus, using a series of de novo investigating tools</article-title><source>BMC Genomics</source><year>2016</year><volume>17</volume><fpage>659</fpage><pub-id pub-id-type="doi">10.1186/s12864-016-3015-5</pub-id><pub-id pub-id-type="pmid">27542599</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Seroussi</surname><given-names>E</given-names></name><name><surname>Pitel</surname><given-names>F</given-names></name><name><surname>Leroux</surname><given-names>S</given-names></name><name><surname>Morisson</surname><given-names>M</given-names></name><name><surname>Bornel&#x000f6;v</surname><given-names>S</given-names></name><name><surname>Miyara</surname><given-names>S</given-names></name><name><surname>Yosefi</surname><given-names>S</given-names></name><name><surname>Cogburn</surname><given-names>LA</given-names></name><name><surname>Burt</surname><given-names>DW</given-names></name><name><surname>Anderson</surname><given-names>L</given-names></name><name><surname>Friedman-Einat</surname><given-names>M</given-names></name></person-group><article-title>Mapping of leptin and its syntenic genes to chicken chromosome 1p</article-title><source>BMC Genet</source><year>2017</year><volume>18</volume><fpage>77</fpage><pub-id pub-id-type="doi">10.1186/s12863-017-0543-1</pub-id><pub-id pub-id-type="pmid">28793857</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Botero-Castro</surname><given-names>F</given-names></name><name><surname>Figuet</surname><given-names>E</given-names></name><name><surname>Tilak</surname><given-names>MK</given-names></name><name><surname>Nabholz</surname><given-names>B</given-names></name><name><surname>Galtier</surname><given-names>N</given-names></name></person-group><article-title>Avian genomes revisited: hidden genes uncovered and the rates versus traits paradox in birds</article-title><source>Mol Biol Evol</source><year>2017</year><volume>34</volume><fpage>3123</fpage><lpage>3131</lpage><pub-id pub-id-type="doi">10.1093/molbev/msx236</pub-id><pub-id pub-id-type="pmid">28962031</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>G</given-names></name><name><surname>Li</surname><given-names>C</given-names></name><name><surname>Li</surname><given-names>Q</given-names></name><name><surname>Li</surname><given-names>B</given-names></name><name><surname>Larkin</surname><given-names>DM</given-names></name><name><surname>Lee</surname><given-names>C</given-names></name><name><surname>Storz</surname><given-names>JF</given-names></name><name><surname>Antunes</surname><given-names>A</given-names></name><name><surname>Greenwold</surname><given-names>MJ</given-names></name><name><surname>Meredith</surname><given-names>RW</given-names></name><name><surname>&#x000d6;deen</surname><given-names>A</given-names></name><name><surname>Cui</surname><given-names>J</given-names></name><name><surname>Zhou</surname><given-names>Q</given-names></name><name><surname>Xu</surname><given-names>L</given-names></name><name><surname>Pan</surname><given-names>H</given-names></name><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Jin</surname><given-names>L</given-names></name><name><surname>Zhang</surname><given-names>P</given-names></name><name><surname>Hu</surname><given-names>H</given-names></name><name><surname>Yang</surname><given-names>W</given-names></name><name><surname>Hu</surname><given-names>J</given-names></name><name><surname>Xiao</surname><given-names>J</given-names></name><name><surname>Yang</surname><given-names>Z</given-names></name><name><surname>Liu</surname><given-names>Y</given-names></name><name><surname>Xie</surname><given-names>Q</given-names></name><name><surname>Yu</surname><given-names>H</given-names></name><name><surname>Lian</surname><given-names>J</given-names></name><name><surname>Wen</surname><given-names>P</given-names></name><name><surname>Zhang</surname><given-names>F</given-names></name><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Zeng</surname><given-names>Y</given-names></name><name><surname>Xiong</surname><given-names>Z</given-names></name><name><surname>Liu</surname><given-names>S</given-names></name><name><surname>Zhou</surname><given-names>L</given-names></name><name><surname>Huang</surname><given-names>Z</given-names></name><name><surname>An</surname><given-names>N</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Zheng</surname><given-names>Q</given-names></name><name><surname>Xiong</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>G</given-names></name><name><surname>Wang</surname><given-names>B</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Fan</surname><given-names>Y</given-names></name><name><surname>da Fonseca</surname><given-names>RR</given-names></name><name><surname>Alfaro-N&#x000fa;&#x000f1;ez</surname><given-names>A</given-names></name><name><surname>Schubert</surname><given-names>M</given-names></name><name><surname>Orlando</surname><given-names>L</given-names></name><name><surname>Mourier</surname><given-names>T</given-names></name><name><surname>Howard</surname><given-names>JT</given-names></name><name><surname>Ganapathy</surname><given-names>G</given-names></name><name><surname>Pfenning</surname><given-names>A</given-names></name><name><surname>Whitney</surname><given-names>O</given-names></name><name><surname>Rivas</surname><given-names>MV</given-names></name><name><surname>Hara</surname><given-names>E</given-names></name><name><surname>Smith</surname><given-names>J</given-names></name><name><surname>Farr&#x000e9;</surname><given-names>M</given-names></name><name><surname>Narayan</surname><given-names>J</given-names></name><name><surname>Slavov</surname><given-names>G</given-names></name><name><surname>Romanov</surname><given-names>MN</given-names></name><name><surname>Borges</surname><given-names>R</given-names></name><name><surname>Machado</surname><given-names>JP</given-names></name><name><surname>Khan</surname><given-names>I</given-names></name><name><surname>Springer</surname><given-names>MS</given-names></name><name><surname>Gatesy</surname><given-names>J</given-names></name><name><surname>Hoffmann</surname><given-names>FG</given-names></name><name><surname>Opazo</surname><given-names>JC</given-names></name><name><surname>H&#x000e5;stad</surname><given-names>O</given-names></name><name><surname>Sawyer</surname><given-names>RH</given-names></name><name><surname>Kim</surname><given-names>H</given-names></name><name><surname>Kim</surname><given-names>KW</given-names></name><name><surname>Kim</surname><given-names>HJ</given-names></name><name><surname>Cho</surname><given-names>S</given-names></name><name><surname>Li</surname><given-names>N</given-names></name><name><surname>Huang</surname><given-names>Y</given-names></name><name><surname>Bruford</surname><given-names>MW</given-names></name><name><surname>Zhan</surname><given-names>X</given-names></name><name><surname>Dixon</surname><given-names>A</given-names></name><name><surname>Bertelsen</surname><given-names>MF</given-names></name><name><surname>Derryberry</surname><given-names>E</given-names></name><name><surname>Warren</surname><given-names>W</given-names></name><name><surname>Wilson</surname><given-names>RK</given-names></name><name><surname>Li</surname><given-names>S</given-names></name><name><surname>Ray</surname><given-names>DA</given-names></name><name><surname>Green</surname><given-names>RE</given-names></name><name><surname>O'Brien</surname><given-names>SJ</given-names></name><name><surname>Griffin</surname><given-names>D</given-names></name><name><surname>Johnson</surname><given-names>WE</given-names></name><name><surname>Haussler</surname><given-names>D</given-names></name><name><surname>Ryder</surname><given-names>OA</given-names></name><name><surname>Willerslev</surname><given-names>E</given-names></name><name><surname>Graves</surname><given-names>GR</given-names></name><name><surname>Alstr&#x000f6;m</surname><given-names>P</given-names></name><name><surname>Fjelds&#x000e5;</surname><given-names>J</given-names></name><name><surname>Mindell</surname><given-names>DP</given-names></name><name><surname>Edwards</surname><given-names>SV</given-names></name><name><surname>Braun</surname><given-names>EL</given-names></name><name><surname>Rahbek</surname><given-names>C</given-names></name><name><surname>Burt</surname><given-names>DW</given-names></name><name><surname>Houde</surname><given-names>P</given-names></name><name><surname>Zhang</surname><given-names>Y</given-names></name><name><surname>Yang</surname><given-names>H</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Consortium</surname><given-names>AG</given-names></name><name><surname>Jarvis</surname><given-names>ED</given-names></name><name><surname>Gilbert</surname><given-names>MT</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name></person-group><article-title>(2014) Comparative genomics reveals insights into avian genome evolution and adaptation</article-title><source>Science.</source><year>2015</year><volume>346</volume><fpage>1311</fpage><lpage>1320</lpage><pub-id pub-id-type="doi">10.1126/science.1251385</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dakovi&#x00107;</surname><given-names>N</given-names></name><name><surname>T&#x000e9;r&#x000e9;zol</surname><given-names>M</given-names></name><name><surname>Pitel</surname><given-names>F</given-names></name><name><surname>Maillard</surname><given-names>V</given-names></name><name><surname>Elis</surname><given-names>S</given-names></name><name><surname>Leroux</surname><given-names>S</given-names></name><name><surname>Lagarrigue</surname><given-names>S</given-names></name><name><surname>Gondret</surname><given-names>F</given-names></name><name><surname>Klopp</surname><given-names>C</given-names></name><name><surname>Baeza</surname><given-names>E</given-names></name><name><surname>Duclos</surname><given-names>MJ</given-names></name><name><surname>Roest Crollius</surname><given-names>H</given-names></name><name><surname>Monget</surname><given-names>P</given-names></name></person-group><article-title>The loss of adipokine genes in the chicken genome and implications for insulin metabolism</article-title><source>Mol Biol Evol</source><year>2014</year><volume>31</volume><fpage>2637</fpage><lpage>1646</lpage><pub-id pub-id-type="doi">10.1093/molbev/msu208</pub-id><pub-id pub-id-type="pmid">25015647</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lovell</surname><given-names>PV</given-names></name><name><surname>Wirthlin</surname><given-names>M</given-names></name><name><surname>Wilhelm</surname><given-names>L</given-names></name><name><surname>Minx</surname><given-names>P</given-names></name><name><surname>Lazar</surname><given-names>NH</given-names></name><name><surname>Carbone</surname><given-names>L</given-names></name><name><surname>Warren</surname><given-names>WC</given-names></name><name><surname>Mello</surname><given-names>CV</given-names></name></person-group><article-title>Conserved syntenic clusters of protein coding genes are missing in birds</article-title><source>Genome Biol</source><year>2014</year><volume>15</volume><fpage>565</fpage><pub-id pub-id-type="doi">10.1186/s13059-014-0565-1</pub-id><pub-id pub-id-type="pmid">25518852</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mello</surname><given-names>CV</given-names></name><name><surname>Lovell</surname><given-names>PV</given-names></name></person-group><article-title>Avian genomics lends insights into endocrine function in birds</article-title><source>Gen Comp Endocrinol</source><year>2018</year><volume>256</volume><fpage>123</fpage><lpage>129</lpage><pub-id pub-id-type="doi">10.1016/j.ygcen.2017.05.023</pub-id><pub-id pub-id-type="pmid">28596079</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Friedman-Einat</surname><given-names>M</given-names></name><name><surname>Seroussi</surname><given-names>E</given-names></name></person-group><article-title>Quack leptin</article-title><source>BMC Genomics</source><year>2014</year><volume>15</volume><fpage>551</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-15-551</pub-id><pub-id pub-id-type="pmid">24992969</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Seroussi</surname><given-names>E</given-names></name><name><surname>Cinnamon</surname><given-names>Y</given-names></name><name><surname>Yosefi</surname><given-names>S</given-names></name><name><surname>Genin</surname><given-names>O</given-names></name><name><surname>Smith</surname><given-names>JG</given-names></name><name><surname>Rafati</surname><given-names>N</given-names></name><name><surname>Bornel&#x000f6;v</surname><given-names>S</given-names></name><name><surname>Andersson</surname><given-names>L</given-names></name><name><surname>Friedman-Einat</surname><given-names>M</given-names></name></person-group><article-title>Identification of the long-sought leptin in chicken and duck: expression pattern of the highly GC-rich avian leptin fits an autocrine/paracrine rather than endocrine function</article-title><source>Endocrinology.</source><year>2016</year><volume>157</volume><fpage>737</fpage><lpage>751</lpage><pub-id pub-id-type="doi">10.1210/en.2015-1634</pub-id><pub-id pub-id-type="pmid">26587783</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Aird</surname><given-names>D</given-names></name><name><surname>Ross</surname><given-names>MG</given-names></name><name><surname>Chen</surname><given-names>WS</given-names></name><name><surname>Danielsson</surname><given-names>M</given-names></name><name><surname>Fennell</surname><given-names>T</given-names></name><name><surname>Russ</surname><given-names>C</given-names></name><name><surname>Jaffe</surname><given-names>DB</given-names></name><name><surname>Nusbaum</surname><given-names>C</given-names></name><name><surname>Gnirke</surname><given-names>A</given-names></name></person-group><article-title>Analyzing and minimizing PCR amplification bias in Illumina sequencing libraries</article-title><source>Genome Biol</source><year>2011</year><volume>12</volume><fpage>R18</fpage><pub-id pub-id-type="doi">10.1186/gb-2011-12-2-r18</pub-id><pub-id pub-id-type="pmid">21338519</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nakamura</surname><given-names>K</given-names></name><name><surname>Oshima</surname><given-names>T</given-names></name><name><surname>Morimoto</surname><given-names>T</given-names></name><name><surname>Ikeda</surname><given-names>S</given-names></name><name><surname>Yoshikawa</surname><given-names>H</given-names></name><name><surname>Shiwa</surname><given-names>Y</given-names></name><name><surname>Ishikawa</surname><given-names>S</given-names></name><name><surname>Linak</surname><given-names>MC</given-names></name><name><surname>Hirai</surname><given-names>A</given-names></name><name><surname>Takahashi</surname><given-names>H</given-names></name><name><surname>Altaf-Ul-Amin</surname><given-names>M</given-names></name><name><surname>Ogasawara</surname><given-names>N</given-names></name><name><surname>Kanaya</surname><given-names>S</given-names></name></person-group><article-title>Sequence-specific error profile of Illumina sequencers</article-title><source>Nucleic Acids Res</source><year>2011</year><volume>39</volume><fpage>e90</fpage><pub-id pub-id-type="doi">10.1093/nar/gkr344</pub-id><pub-id pub-id-type="pmid">21576222</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Benjamini</surname><given-names>Y</given-names></name><name><surname>Speed</surname><given-names>TP</given-names></name></person-group><article-title>Summarizing and correcting the GC content bias in high-throughput sequencing</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><fpage>e72</fpage><pub-id pub-id-type="doi">10.1093/nar/gks001</pub-id><pub-id pub-id-type="pmid">22323520</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dabney</surname><given-names>J</given-names></name><name><surname>Meyer</surname><given-names>M</given-names></name></person-group><article-title>Length and GC-biases during sequencing library amplification: a comparison of various polymerase-buffer systems with ancient and modern DNA sequencing libraries</article-title><source>Biotechniques.</source><year>2012</year><volume>52</volume><fpage>87</fpage><lpage>94</lpage><pub-id pub-id-type="doi">10.2144/000113809</pub-id><pub-id pub-id-type="pmid">22313406</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Oyola</surname><given-names>SO</given-names></name><name><surname>Otto</surname><given-names>TD</given-names></name><name><surname>Gu</surname><given-names>Y</given-names></name><name><surname>Maslen</surname><given-names>G</given-names></name><name><surname>Manske</surname><given-names>M</given-names></name><name><surname>Campino</surname><given-names>S</given-names></name><name><surname>Turner</surname><given-names>DJ</given-names></name><name><surname>Macinnis</surname><given-names>B</given-names></name><name><surname>Kwiatkowski</surname><given-names>DP</given-names></name><name><surname>Swerdlow</surname><given-names>HP</given-names></name><name><surname>Quail</surname><given-names>MA</given-names></name></person-group><article-title>Optimizing Illumina next-generation sequencing library preparation for extremely AT-biased genomes</article-title><source>BMC Genomics</source><year>2012</year><volume>13</volume><fpage>1</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-13-1</pub-id><pub-id pub-id-type="pmid">22214261</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ross</surname><given-names>MG</given-names></name><name><surname>Russ</surname><given-names>C</given-names></name><name><surname>Costello</surname><given-names>M</given-names></name><name><surname>Hollinger</surname><given-names>A</given-names></name><name><surname>Lennon</surname><given-names>NJ</given-names></name><name><surname>Hegarty</surname><given-names>R</given-names></name><name><surname>Nusbaum</surname><given-names>C</given-names></name><name><surname>Jaffe</surname><given-names>DB</given-names></name></person-group><article-title>Characterizing and measuring bias in sequence data</article-title><source>Genome Biol</source><year>2013</year><volume>14</volume><fpage>R51</fpage><pub-id pub-id-type="doi">10.1186/gb-2013-14-5-r51</pub-id><pub-id pub-id-type="pmid">23718773</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Farka&#x00161;ov&#x000e1;</surname><given-names>H</given-names></name><name><surname>Hron</surname><given-names>T</given-names></name><name><surname>Pa&#x0010d;es</surname><given-names>J</given-names></name><name><surname>Pajer</surname><given-names>P</given-names></name><name><surname>Elleder</surname><given-names>D</given-names></name></person-group><article-title>Identification of a GC-rich leptin gene in chicken</article-title><source>Agri Gene</source><year>2016</year><volume>1</volume><fpage>88</fpage><lpage>92</lpage><pub-id pub-id-type="doi">10.1016/j.aggene.2016.04.001</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hron</surname><given-names>T</given-names></name><name><surname>Pajer</surname><given-names>P</given-names></name><name><surname>Pa&#x0010d;es</surname><given-names>J</given-names></name><name><surname>Bart&#x0016f;n&#x0011b;k</surname><given-names>P</given-names></name><name><surname>Elleder</surname><given-names>D</given-names></name></person-group><article-title>Hidden genes in birds</article-title><source>Genome Biol</source><year>2015</year><volume>16</volume><fpage>164</fpage><pub-id pub-id-type="doi">10.1186/s13059-015-0724-z</pub-id><pub-id pub-id-type="pmid">26283656</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Figuet</surname><given-names>E</given-names></name><name><surname>Nabholz</surname><given-names>B</given-names></name><name><surname>Bonneau</surname><given-names>M</given-names></name><name><surname>Carrio</surname><given-names>EM</given-names></name><name><surname>Nadachowska-Brzyska</surname><given-names>K</given-names></name><name><surname>Ellegren</surname><given-names>H</given-names></name><name><surname>Galtier</surname><given-names>N</given-names></name></person-group><article-title>Life history traits, protein evolution, and the nearly neutral theory in amniotes</article-title><source>Mol Biol Evol</source><year>2016</year><volume>33</volume><fpage>1517</fpage><lpage>1527</lpage><pub-id pub-id-type="doi">10.1093/molbev/msw033</pub-id><pub-id pub-id-type="pmid">26944704</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bornel&#x000f6;v</surname><given-names>S</given-names></name><name><surname>Seroussi</surname><given-names>E</given-names></name><name><surname>Yosefi</surname><given-names>S</given-names></name><name><surname>Benjamini</surname><given-names>S</given-names></name><name><surname>Miyara</surname><given-names>S</given-names></name><name><surname>Ruzal</surname><given-names>M</given-names></name><name><surname>Grabherr</surname><given-names>M</given-names></name><name><surname>Rafati</surname><given-names>N</given-names></name><name><surname>Molin</surname><given-names>AM</given-names></name><name><surname>Pendavis</surname><given-names>K</given-names></name><name><surname>Burgess</surname><given-names>SC</given-names></name><name><surname>Andersson</surname><given-names>L</given-names></name><name><surname>Friedman-Einat</surname><given-names>M</given-names></name></person-group><article-title>Comparative omics and feeding manipulations in chicken indicate a shift of the endocrine role of visceral fat towards reproduction</article-title><source>BMC Genomics</source><year>2017</year><volume>19</volume><fpage>295</fpage><pub-id pub-id-type="doi">10.1186/s12864-018-4675-0</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rohde</surname><given-names>F</given-names></name><name><surname>Schusser</surname><given-names>B</given-names></name><name><surname>Hron</surname><given-names>T</given-names></name><name><surname>Farka&#x00161;ov&#x000e1;</surname><given-names>H</given-names></name><name><surname>Plach&#x000fd;</surname><given-names>J</given-names></name><name><surname>H&#x000e4;rtle</surname><given-names>S</given-names></name><name><surname>Hejnar</surname><given-names>J</given-names></name><name><surname>Elleder</surname><given-names>D</given-names></name><name><surname>Kaspers</surname><given-names>B</given-names></name></person-group><article-title>Characterization of chicken tumor necrosis factor-&#x003b1;, a long missed cytokine in birds</article-title><source>Front Immunol</source><year>2018</year><volume>9</volume><fpage>605</fpage><pub-id pub-id-type="doi">10.3389/fimmu.2018.00605</pub-id><pub-id pub-id-type="pmid">29719531</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pasquier</surname><given-names>J</given-names></name><name><surname>Lafont</surname><given-names>AG</given-names></name><name><surname>Rousseau</surname><given-names>K</given-names></name><name><surname>Qu&#x000e9;rat</surname><given-names>B</given-names></name><name><surname>Chemineau</surname><given-names>P</given-names></name><name><surname>Dufour</surname><given-names>S</given-names></name></person-group><article-title>Looking for the bird kiss: evolutionary scenario in sauropsids</article-title><source>BMC Evol Biol</source><year>2014</year><volume>14</volume><fpage>30</fpage><pub-id pub-id-type="doi">10.1186/1471-2148-14-30</pub-id><pub-id pub-id-type="pmid">24552453</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lim</surname><given-names>SL</given-names></name><name><surname>Tsend-Ayush</surname><given-names>E</given-names></name><name><surname>Kortschak</surname><given-names>RD</given-names></name><name><surname>Jacob</surname><given-names>R</given-names></name><name><surname>Ricciardelli</surname><given-names>C</given-names></name><name><surname>Oehler</surname><given-names>MK</given-names></name><name><surname>Gr&#x000fc;tzner</surname><given-names>F</given-names></name></person-group><article-title>Conservation and expression of PIWI-interacting RNA pathway genes in male and female adult gonad of amniotes</article-title><source>Biol Reprod</source><year>2013</year><volume>89</volume><fpage>136</fpage><pub-id pub-id-type="doi">10.1095/biolreprod.113.111211</pub-id><pub-id pub-id-type="pmid">24108303</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rhoads</surname><given-names>A</given-names></name><name><surname>Au</surname><given-names>KF</given-names></name></person-group><article-title>PacBio sequencing and its applications</article-title><source>Genomics Proteomics Bioinformatics</source><year>2015</year><volume>13</volume><fpage>278</fpage><lpage>289</lpage><pub-id pub-id-type="doi">10.1016/j.gpb.2015.08.002</pub-id><pub-id pub-id-type="pmid">26542840</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bochman</surname><given-names>ML</given-names></name><name><surname>Paeschke</surname><given-names>K</given-names></name><name><surname>Zakian</surname><given-names>VA</given-names></name></person-group><article-title>DNA secondary structures: stability and function of G-quadruplex structures</article-title><source>Nat Rev Genet</source><year>2012</year><volume>13</volume><fpage>770</fpage><lpage>780</lpage><pub-id pub-id-type="doi">10.1038/nrg3296</pub-id><pub-id pub-id-type="pmid">23032257</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><mixed-citation publication-type="other">Biffi G, Tannahill D, McCafferty J, Balasubramanian S. Quantitative visualization of DNA G-quadruplex structures in human cells. Nat Chem. 2013;5:182&#x02013;6.</mixed-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maizels</surname><given-names>N</given-names></name><name><surname>Gray</surname><given-names>LT</given-names></name></person-group><article-title>The G4 genome</article-title><source>PLoS Genet</source><year>2013</year><volume>9</volume><fpage>e1003468</fpage><pub-id pub-id-type="doi">10.1371/journal.pgen.1003468</pub-id><pub-id pub-id-type="pmid">23637633</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><mixed-citation publication-type="other">Kejnovsky E, Lexa M. Quadruplex-forming DNA sequences spread by retrotransposons may serve as genome regulators. Mob Genet Elements. 2014;4:e28084.</mixed-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>H&#x000e4;nsel-Hertsch</surname><given-names>R</given-names></name><name><surname>Di Antonio</surname><given-names>M</given-names></name><name><surname>Balasubramanian</surname><given-names>S</given-names></name></person-group><article-title>DNA G-quadruplexes in the human genome: detection, functions and therapeutic potential</article-title><source>Nat Rev Mol Cell Biol</source><year>2014</year><volume>18</volume><fpage>279</fpage><lpage>284</lpage><pub-id pub-id-type="doi">10.1038/nrm.2017.3</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shin</surname><given-names>SC</given-names></name><name><surname>Ahn</surname><given-names>DH</given-names></name><name><surname>Kim</surname><given-names>SJ</given-names></name><name><surname>Lee</surname><given-names>H</given-names></name><name><surname>Oh</surname><given-names>TJ</given-names></name><name><surname>Lee</surname><given-names>JE</given-names></name><name><surname>Park</surname><given-names>H</given-names></name></person-group><article-title>Advantages of single-molecule real-time sequencing in high-GC content genomes</article-title><source>PLoS One</source><year>2013</year><volume>8</volume><fpage>e68824</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0068824</pub-id><pub-id pub-id-type="pmid">23894349</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Teng</surname><given-names>JLL</given-names></name><name><surname>Yeung</surname><given-names>ML</given-names></name><name><surname>Chan</surname><given-names>E</given-names></name><name><surname>Jia</surname><given-names>L</given-names></name><name><surname>Lin</surname><given-names>CH</given-names></name><name><surname>Huang</surname><given-names>Y</given-names></name><name><surname>Tse</surname><given-names>H</given-names></name><name><surname>Wong</surname><given-names>SSY</given-names></name><name><surname>Sham</surname><given-names>PC</given-names></name><name><surname>Lau</surname><given-names>SKP</given-names></name><name><surname>Woo</surname><given-names>PCY</given-names></name></person-group><article-title>PacBio but not Illumina technology can achieve fast, accurate and complete closure of the high GC, complex <italic>Burkholderia pseudomallei</italic> two-chromosome genome</article-title><source>Front Microbiol</source><year>2017</year><volume>8</volume><fpage>1448</fpage><pub-id pub-id-type="doi">10.3389/fmicb.2017.01448</pub-id><pub-id pub-id-type="pmid">28824579</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guiblet</surname><given-names>WM</given-names></name><name><surname>Cremona</surname><given-names>MA</given-names></name><name><surname>Cechova</surname><given-names>M</given-names></name><name><surname>Harris</surname><given-names>RS</given-names></name><name><surname>Kejnovsk&#x000e1;</surname><given-names>I</given-names></name><name><surname>Kejnovsky</surname><given-names>E</given-names></name><name><surname>Eckert</surname><given-names>K</given-names></name><name><surname>Chiaromonte</surname><given-names>F</given-names></name><name><surname>Makova</surname><given-names>KD</given-names></name></person-group><article-title>Long-read sequencing technology indicates genome-wide effects of non-B DNA on polymerization speed and error rate</article-title><source>Genome Res</source><year>2018</year><volume>28</volume><fpage>1767</fpage><lpage>1778</lpage><pub-id pub-id-type="doi">10.1101/gr.241257.118</pub-id><pub-id pub-id-type="pmid">30401733</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><mixed-citation publication-type="other">Kouzine F, Wojtowicz D, Baranello L, Yamane A, Nelson S, Resch W, Kieffer-Kwon KR, Benham CJ, Casellas R, Przytycka TM, Levens D. Permanganate/S1 Nuclease Footprinting Reveals Non-B DNA Structures with Regulatory Potential across a Mammalian Genome. Cell Syst. 2017;4:4344&#x02013;356.e7.</mixed-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cer</surname><given-names>RZ</given-names></name><name><surname>Bruce</surname><given-names>KH</given-names></name><name><surname>Donohue</surname><given-names>DE</given-names></name><name><surname>Temiz</surname><given-names>NA</given-names></name><name><surname>Mudunuri</surname><given-names>US</given-names></name><name><surname>Yi</surname><given-names>M</given-names></name><name><surname>Volfovsky</surname><given-names>N</given-names></name><name><surname>Bacolla</surname><given-names>A</given-names></name><name><surname>Luke</surname><given-names>BT</given-names></name><name><surname>Collins</surname><given-names>JR</given-names></name><name><surname>Stephens</surname><given-names>RM</given-names></name></person-group><article-title>Searching for non-B DNA-forming motifs using nBMST (non-B DNA motif search tool)</article-title><source>Curr Protoc Hum Genet</source><year>2012</year><volume>Chapter 18</volume><fpage>Unit 18.7.1</fpage><lpage>Unit 18.722</lpage><pub-id pub-id-type="doi">10.1002/0471142905.hg1807s73</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dyomin</surname><given-names>AG</given-names></name><name><surname>Koshel</surname><given-names>EI</given-names></name><name><surname>Kiselev</surname><given-names>AM</given-names></name><name><surname>Saifitdinova</surname><given-names>AF</given-names></name><name><surname>Galkina</surname><given-names>SA</given-names></name><name><surname>Fukagawa</surname><given-names>T</given-names></name><name><surname>Kostareva</surname><given-names>AA</given-names></name><name><surname>Gaginskaya</surname><given-names>ER</given-names></name></person-group><article-title>Chicken rRNA Gene Cluster Structure</article-title><source>PLoS One</source><year>2016</year><volume>11</volume><fpage>e0157464</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0157464</pub-id><pub-id pub-id-type="pmid">27299357</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Douaud</surname><given-names>M</given-names></name><name><surname>Feve</surname><given-names>K</given-names></name><name><surname>Pituello</surname><given-names>F</given-names></name><name><surname>Gourichon</surname><given-names>D</given-names></name><name><surname>Boitard</surname><given-names>S</given-names></name><name><surname>Leguern</surname><given-names>E</given-names></name><name><surname>Coquerelle</surname><given-names>G</given-names></name><name><surname>Vieaud</surname><given-names>A</given-names></name><name><surname>Batini</surname><given-names>C</given-names></name><name><surname>Naquet</surname><given-names>R</given-names></name><name><surname>Vignal</surname><given-names>A</given-names></name><name><surname>Tixier-Boichard</surname><given-names>M</given-names></name><name><surname>Pitel</surname><given-names>F</given-names></name></person-group><article-title>Epilepsy caused by an abnormal alternative splicing with dosage effect of the SV2A gene in a chicken model</article-title><source>PLoS One</source><year>2011</year><volume>6</volume><fpage>e26932</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0026932</pub-id><pub-id pub-id-type="pmid">22046416</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Su</surname><given-names>M</given-names></name><name><surname>Delany</surname><given-names>ME</given-names></name></person-group><article-title>Ribosomal RNA gene copy number and nucleolar-size polymorphisms within and among chicken lines selected for enhanced growth</article-title><source>Poult Sci</source><year>1998</year><volume>77</volume><fpage>1748</fpage><lpage>1754</lpage><pub-id pub-id-type="doi">10.1093/ps/77.12.1748</pub-id><pub-id pub-id-type="pmid">9872573</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edgar</surname><given-names>RC</given-names></name></person-group><article-title>MUSCLE: multiple sequence alignment with high accuracy and high throughput</article-title><source>Nucleic Acids Res</source><year>2004</year><volume>32</volume><fpage>1792</fpage><lpage>1797</lpage><pub-id pub-id-type="doi">10.1093/nar/gkh340</pub-id><pub-id pub-id-type="pmid">15034147</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Burset</surname><given-names>M</given-names></name><name><surname>Seledtsov</surname><given-names>IA</given-names></name><name><surname>Solovyeva</surname><given-names>VV</given-names></name></person-group><article-title>Analysis of canonical and non-canonical splice sites in mammalian genomes</article-title><source>Nucleic Acids Res</source><year>2000</year><volume>28</volume><fpage>4364</fpage><lpage>4375</lpage><pub-id pub-id-type="doi">10.1093/nar/28.21.4364</pub-id><pub-id pub-id-type="pmid">11058137</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nanda</surname><given-names>I</given-names></name><name><surname>Schmid</surname><given-names>M</given-names></name></person-group><article-title>Localization of the telomeric (TTAGGG) n sequence in chicken (Gallus domesticus) chromosomes</article-title><source>Cytogenet Cell Genet</source><year>1994</year><volume>65</volume><fpage>190</fpage><lpage>193</lpage><pub-id pub-id-type="doi">10.1159/000133630</pub-id><pub-id pub-id-type="pmid">8222759</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sahakyan</surname><given-names>AB</given-names></name><name><surname>Murat</surname><given-names>P</given-names></name><name><surname>Mayer</surname><given-names>C</given-names></name><name><surname>Balasubramanian</surname><given-names>S</given-names></name></person-group><article-title>G-quadruplex structures within the 3&#x02032; UTR of LINE-1 elements stimulate retrotransposition</article-title><source>Nat Struct Mol Biol</source><year>2017</year><volume>24</volume><fpage>243</fpage><lpage>247</lpage><pub-id pub-id-type="doi">10.1038/nsmb.3367</pub-id><pub-id pub-id-type="pmid">28134931</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chambers</surname><given-names>VS</given-names></name><name><surname>Marsico</surname><given-names>G</given-names></name><name><surname>Boutell</surname><given-names>JM</given-names></name><name><surname>Di Antonio</surname><given-names>M</given-names></name><name><surname>Smith</surname><given-names>GP</given-names></name><name><surname>Balasubramanian</surname><given-names>S</given-names></name></person-group><article-title>High-throughput sequencing of DNA G-quadruplex structures in the human genome</article-title><source>Nat Biotechnol</source><year>2015</year><volume>33</volume><fpage>877</fpage><lpage>881</lpage><pub-id pub-id-type="doi">10.1038/nbt.3295</pub-id><pub-id pub-id-type="pmid">26192317</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wickramasinghe</surname><given-names>CM</given-names></name><name><surname>Arzouk</surname><given-names>H</given-names></name><name><surname>Frey</surname><given-names>A</given-names></name><name><surname>Maiter</surname><given-names>A</given-names></name><name><surname>Sale</surname><given-names>JE</given-names></name></person-group><article-title>Contributions of the specialised DNA polymerases to replication of structured DNA</article-title><source>DNA Repair</source><year>2015</year><volume>29</volume><fpage>83</fpage><lpage>90</lpage><pub-id pub-id-type="doi">10.1016/j.dnarep.2015.01.004</pub-id><pub-id pub-id-type="pmid">25704659</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kwok</surname><given-names>CK</given-names></name><name><surname>Marsico</surname><given-names>G</given-names></name><name><surname>Sahakyan</surname><given-names>AB</given-names></name><name><surname>Chambers</surname><given-names>VS</given-names></name><name><surname>Balasubramanian</surname><given-names>S</given-names></name></person-group><article-title>rG4-seq reveals widespread formation of G-quadruplex structures in the human transcriptome</article-title><source>Nat Methods</source><year>2016</year><volume>13</volume><fpage>841</fpage><lpage>844</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3965</pub-id><pub-id pub-id-type="pmid">27571552</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kuo</surname><given-names>RI</given-names></name><name><surname>Tseng</surname><given-names>E</given-names></name><name><surname>Eory</surname><given-names>L</given-names></name><name><surname>Paton</surname><given-names>IR</given-names></name><name><surname>Archibald</surname><given-names>AL</given-names></name><name><surname>Burt</surname><given-names>DW</given-names></name></person-group><article-title>Normalized long read RNA sequencing in chicken reveals transcriptome complexity similar to human</article-title><source>BMC Genomics</source><year>2017</year><volume>18</volume><fpage>323</fpage><pub-id pub-id-type="doi">10.1186/s12864-017-3691-9</pub-id><pub-id pub-id-type="pmid">28438136</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gregory</surname><given-names>TR</given-names></name><name><surname>Andrews</surname><given-names>CB</given-names></name><name><surname>McGuire</surname><given-names>JA</given-names></name><name><surname>Witt</surname><given-names>CC</given-names></name></person-group><article-title>The smallest avian genomes are found in hummingbirds</article-title><source>Proc. Royal Soc. London B</source><year>2009</year><volume>276</volume><fpage>3753</fpage><lpage>3757</lpage><pub-id pub-id-type="doi">10.1098/rspb.2009.1004</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bhattacharyya</surname><given-names>D</given-names></name><name><surname>Mirihana Arachchilage</surname><given-names>G</given-names></name><name><surname>Basu</surname><given-names>S</given-names></name></person-group><article-title>Metal cations in G-Quadruplex folding and stability</article-title><source>Front Chem</source><year>2016</year><volume>4</volume><fpage>38</fpage><pub-id pub-id-type="doi">10.3389/fchem.2016.00038</pub-id><pub-id pub-id-type="pmid">27668212</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><mixed-citation publication-type="other">Peona V, Weissensteiner MH, Suh A. How complete are &#x02018;complete&#x02019; genome assemblies? - An avian perspective. Mol Ecol Resour. 2018; [Epub ahead of print].</mixed-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tilak</surname><given-names>MK</given-names></name><name><surname>Botero-Castro</surname><given-names>F</given-names></name><name><surname>Galtier</surname><given-names>N</given-names></name><name><surname>Nabholz</surname><given-names>B</given-names></name></person-group><article-title>Illumina library preparation for sequencing the GC-rich fraction of heterogeneous genomic DNA</article-title><source>Genome Biol Evol</source><year>2018</year><volume>10</volume><fpage>616</fpage><lpage>622</lpage><pub-id pub-id-type="doi">10.1093/gbe/evy022</pub-id><pub-id pub-id-type="pmid">29385572</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Salse</surname><given-names>J</given-names></name><name><surname>Bolot</surname><given-names>S</given-names></name><name><surname>Throude</surname><given-names>M</given-names></name><name><surname>Jouffe</surname><given-names>V</given-names></name><name><surname>Piegu</surname><given-names>B</given-names></name><name><surname>Quraishi</surname><given-names>UM</given-names></name><name><surname>Calcagno</surname><given-names>T</given-names></name><name><surname>Cooke</surname><given-names>R</given-names></name><name><surname>Delseny</surname><given-names>M</given-names></name><name><surname>Feuillet</surname><given-names>C</given-names></name></person-group><article-title>Identification and characterization of shared duplications between rice and wheat provide new insight into grass genome evolution</article-title><source>Plant Cell</source><year>2008</year><volume>20</volume><issue>1</issue><fpage>11</fpage><lpage>24</lpage><pub-id pub-id-type="doi">10.1105/tpc.107.056309</pub-id><pub-id pub-id-type="pmid">18178768</pub-id></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chaisson</surname><given-names>MJ</given-names></name><name><surname>Tesler</surname><given-names>G</given-names></name></person-group><article-title>Mapping single molecule sequencing reads using basic local alignment with successive refinement (BLASR): application and theory</article-title><source>BMC Bioinformatics</source><year>2012</year><volume>13</volume><fpage>238</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-13-238</pub-id><pub-id pub-id-type="pmid">22988817</pub-id></element-citation></ref></ref-list></back></article>