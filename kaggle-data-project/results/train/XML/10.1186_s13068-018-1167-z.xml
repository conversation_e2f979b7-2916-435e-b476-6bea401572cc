<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Biotechnol Biofuels</journal-id><journal-id journal-id-type="iso-abbrev">Biotechnol Biofuels</journal-id><journal-title-group><journal-title>Biotechnology for Biofuels</journal-title></journal-title-group><issn pub-type="epub">1754-6834</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6009963</article-id><article-id pub-id-type="publisher-id">1167</article-id><article-id pub-id-type="doi">10.1186/s13068-018-1167-z</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>Revealing the transcriptomic complexity of switchgrass by PacBio long-read sequencing</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Zuo</surname><given-names>Chunman</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Blow</surname><given-names>Matthew</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Sreedasyam</surname><given-names>Avinash</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Kuo</surname><given-names>Rita C.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Ramamoorthy</surname><given-names>Govindarajan Kunde</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Torres-Jerez</surname><given-names>Ivone</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Li</surname><given-names>Guifen</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Wang</surname><given-names>Mei</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Dilworth</surname><given-names>David</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Barry</surname><given-names>Kerrie</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Udvardi</surname><given-names>Michael</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Schmutz</surname><given-names>Jeremy</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Tang</surname><given-names>Yuhong</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Xu</surname><given-names>Ying</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1760 5735</institution-id><institution-id institution-id-type="GRID">grid.64924.3d</institution-id><institution>College of Computer Science and Technology, </institution><institution>Jilin University, </institution></institution-wrap>Changchun, China </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 738X</institution-id><institution-id institution-id-type="GRID">grid.213876.9</institution-id><institution>Department of Biochemistry and Molecular Biology and Institute of Bioinformatics, </institution><institution>University of Georgia, </institution></institution-wrap>Athens, GA USA </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0446 2659</institution-id><institution-id institution-id-type="GRID">grid.135519.a</institution-id><institution>BESC BioEnergy Research Center, </institution><institution>Oak Ridge National Lab, </institution></institution-wrap>Oak Ridge, TN USA </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0449 479X</institution-id><institution-id institution-id-type="GRID">grid.451309.a</institution-id><institution>Department of Energy Joint Genome Institute, </institution></institution-wrap>Walnut Creek, CA USA </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0408 3720</institution-id><institution-id institution-id-type="GRID">grid.417691.c</institution-id><institution>HudsonAlpha Institute for Biotechnology, </institution></institution-wrap>Huntsville, AL USA </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0370 5663</institution-id><institution-id institution-id-type="GRID">grid.419447.b</institution-id><institution>Noble Research Institute, LLC, </institution></institution-wrap>Ardmore, OK USA </aff></contrib-group><pub-date pub-type="epub"><day>20</day><month>6</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>20</day><month>6</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>11</volume><elocation-id>170</elocation-id><history><date date-type="received"><day>9</day><month>4</month><year>2018</year></date><date date-type="accepted"><day>8</day><month>6</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Switchgrass (<italic>Panicum virgatum</italic> L.) is an important bioenergy crop widely used for lignocellulosic research. While extensive transcriptomic analyses have been conducted on this species using short read-based sequencing techniques, very little has been reliably derived regarding alternatively spliced (AS) transcripts.</p></sec><sec><title>Results</title><p id="Par2">We present an analysis of transcriptomes of six switchgrass tissue types pooled together, sequenced using Pacific Biosciences (PacBio) single-molecular long-read technology. Our analysis identified 105,419 unique transcripts covering 43,570 known genes and 8795 previously unknown genes. 45,168 are novel transcripts of known genes. A total of 60,096 AS transcripts are identified, 45,628 being novel. We have also predicted 1549 transcripts of genes involved in cell wall construction and remodeling, 639 being novel transcripts of known cell wall genes. Most of the predicted transcripts are validated against Illumina-based short reads. Specifically, 96% of the splice junction sites in all the unique transcripts are validated by at least five Illumina reads. Comparisons between genes derived from our identified transcripts and the current genome annotation revealed that among the gene set predicted by both analyses, 16,640 have different exon&#x02013;intron structures.</p></sec><sec><title>Conclusions</title><p id="Par3">Overall, substantial amount of new information is derived from the PacBio RNA data regarding both the transcriptome and the genome of switchgrass.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s13068-018-1167-z) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Switchgrass</kwd><kwd>PacBio sequencing</kwd><kwd>Transcriptomic analysis</kwd><kwd>Alternative splicing</kwd><kwd>Plant cell wall</kwd></kwd-group><funding-group><award-group><funding-source><institution>BioEnergy Science Center</institution></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par19">Switchgrass (<italic>Panicum virgatum</italic> L.) is a perennial grass native to North America and considered a major biofuel crop for cellulosic ethanol production, because of its strong adaptability and high biomass production [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR4">4</xref>]. Research on its net energy production and sustainability supports the economic feasibility in using the plant as a long-term biomass crop [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>]. As for any biofuel crop, overcoming biomass recalcitrance to deconstruction prior to conversion is the key challenge for this plant [<xref ref-type="bibr" rid="CR7">7</xref>&#x02013;<xref ref-type="bibr" rid="CR9">9</xref>], to make its biofuel production economically feasible and competitive. In the past few decades, substantial efforts have been invested into genetic and genomic research of the plant [<xref ref-type="bibr" rid="CR10">10</xref>, <xref ref-type="bibr" rid="CR11">11</xref>]. As of now, its genome has been sequenced although it is yet to be fully assembled into complete chromosomes. The most recent version of the genome (Pvir_v4) is 1165.7&#x000a0;Mb long, consisting of 139,331 sequentially ordered contigs [<xref ref-type="bibr" rid="CR12">12</xref>]. Genes were annotated using both evidence-based approaches, i.e., cDNA, ESTs, and RNA-seq, and ab initio prediction [<xref ref-type="bibr" rid="CR13">13</xref>], which have identified 91,838 distinct genes. 123,242 unique transcripts, including 31,404 AS transcripts, have been identified as of now [<xref ref-type="bibr" rid="CR12">12</xref>].</p><p id="Par20">Previous studies have suggested that&#x02009;~&#x02009;60% of the multi-exon genes in plants harbor AS transcripts [<xref ref-type="bibr" rid="CR14">14</xref>]. Compared to this number, substantially more AS transcripts are yet to be uncovered in the transcriptome of switchgrass. A key challenge lies in accurate reconstruction of full-length (FL) splicing transcripts from short sequencing reads [<xref ref-type="bibr" rid="CR15">15</xref>].</p><p id="Par21">The emergence of long-read sequencing techniques such as PacBio single-molecular technology promises more accurate elucidation of FL transcripts of all organisms, especially heterozygous polyploids like switchgrass [<xref ref-type="bibr" rid="CR16">16</xref>]. Specifically, the PacBio technique eliminates the need for sequence assembly [<xref ref-type="bibr" rid="CR17">17</xref>, <xref ref-type="bibr" rid="CR18">18</xref>] because of its ability to sequence reads up to 50&#x000a0;kbp long, hence providing direct evidence for splicing transcripts for the vast majority of plant genes. The technology has proven highly effective for unraveling the transcript diversity at complex loci [<xref ref-type="bibr" rid="CR17">17</xref>], for accurate mapping of RNA sequences to the host genome [<xref ref-type="bibr" rid="CR19">19</xref>] and for determining allele-specific expressions [<xref ref-type="bibr" rid="CR20">20</xref>]. However, the technology has its own limitations: (a) it has high sequencing-error rates (~&#x02009;15%), predominantly <italic>indel</italic>s, compared to Illumina sequencers (~&#x02009;1%); and (b) it is of relatively low throughput, making it difficult to provide quantitative information about gene-expression levels at this point. Fortunately, the strengths and limitations of Illumina and PacBio techniques are highly complementary to each other. Together, they can potentially provide more accurate information about the transcriptome of a plant than either one alone [<xref ref-type="bibr" rid="CR21">21</xref>].</p><p id="Par22">Here, we present a transcriptomic analysis conducted using PacBio Iso-Seq technology [<xref ref-type="bibr" rid="CR22">22</xref>], generated from six pooled tissue types: root, leaf blade, leaf sheath, internode, node, and flower. In parallel, Illumina paired short RNA reads, generated separately from ten un-pooled tissue types, were used as supporting data for our PacBio-based analyses. They are specifically used for (a) validation of splice junctions and AS events in PacBio transcripts; and (b) providing quantitative information for expression analysis.</p><p id="Par23">Our analyses have generated the following information, which is also compared with the genes and transcripts annotated in switchgrass genome version 3 (Pvir_v3, a prefinished draft genome): (1) identification of 105,419 unique transcripts, covering 43,570 genes (42.7%) of Pvir_v3 (Note: we started our analysis when Pvir_v4 was not available) and 8795 non-Pvir_v3 genes, referred to as <italic>previously unknown</italic> genes, that are revealed by 9487 transcripts, 42.2% of which have homologous proteins in the NR database; (2) 60,096 AS transcripts of 16,642 genes; (3) 45,168 novel transcripts of 18,520 known genes; (4) 16,640 genes with exon&#x02013;intron structures that differ to those predicted in Pvir_v3; 11,703 fusion transcripts [<xref ref-type="bibr" rid="CR23">23</xref>] are predicted based on our PacBio data over the Pvir_v3 draft genome; (6) 1296 FL and numerous non-FL transcripts are not map-able to the switchgrass genome but together they are homologous to 7771 distinct proteins in one of the following organisms: sorghum, foxtail millet, and maize; (7) 96% of our predicted splice junctions are consistent with the Illumina data; and (8) 1549 distinct transcripts are predicted to be cell wall (CW) related, 639 of which are previously unknown. Overall, this is the first study of PacBio-based transcriptomic data of switchgrass, to the best of our knowledge.</p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>Mapping of FL transcripts to genomic DNA</title><p id="Par24">We have identified 3042,460 reads of insert (ROI) (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>, Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S1) using the Iso-Seq Tofu pipeline (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S2) from the PacBio RNA data. After removing 322,896 short reads (&#x0003c;&#x02009;300&#x000a0;bp) and 17,119 artificial concatemers [<xref ref-type="bibr" rid="CR24">24</xref>], 859,117 were identified as FL transcripts based on the presence of both 5&#x02032; and 3&#x02032; signals plus the polyA tails, and 1843,328 as non-FL transcripts. We noted that 47, 21, and 22% of the non-FL transcripts each miss all 5&#x02032;, 3&#x02032; and polyA signals; 3&#x02032; and polyA signal; and 5&#x02032; signal, respectively (Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Table S1). The following summarizes our analysis results of the FL transcripts.<table-wrap id="Tab1"><label>Table&#x000a0;1</label><caption><p>A summary of the initial and processed Iso-Seq data from six pooled tissue types</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Library</th><th align="left"># SMRT cell</th><th align="left">#ROI</th><th align="left">#FL</th><th align="left">#Non-FL</th><th align="left">#Reads&#x02009;&#x0003c;&#x02009;300&#x000a0;bp</th><th align="left">#Artificial concatemers</th><th align="left">Mean size of FL (bp)</th><th align="left">% FL</th><th align="left">#Consensus sequences</th></tr></thead><tbody><tr><td align="left" colspan="10">Gel</td></tr><tr><td align="left">&#x000a0;PB0938</td><td char="." align="char">2</td><td char="." align="char">57,613</td><td align="left">17,273</td><td align="left">33,863</td><td align="left">6455</td><td align="left">22</td><td align="left">2019</td><td align="left">30</td><td char="." align="char">5337</td></tr><tr><td align="left">&#x000a0;PB0939</td><td char="." align="char">2</td><td char="." align="char">60,270</td><td align="left">10,318</td><td align="left">41,604</td><td align="left">8325</td><td align="left">23</td><td align="left">2911</td><td align="left">17</td><td char="." align="char">2393</td></tr><tr><td align="left">&#x000a0;PB0940</td><td char="." align="char">2</td><td char="." align="char">35,226</td><td align="left">485</td><td align="left">2371</td><td align="left">32,345</td><td align="left">25</td><td align="left">406</td><td align="left">10</td><td char="." align="char">208</td></tr><tr><td align="left">&#x000a0;PB0941</td><td char="." align="char">2</td><td char="." align="char">60,887</td><td align="left">17,463</td><td align="left">11,806</td><td align="left">31,566</td><td align="left">52</td><td align="left">524</td><td align="left">29</td><td char="." align="char">5984</td></tr><tr><td align="left">&#x000a0;PB0942</td><td char="." align="char">2</td><td char="." align="char">103,211</td><td align="left">31,359</td><td align="left">48,990</td><td align="left">22,817</td><td align="left">45</td><td align="left">1115</td><td align="left">30</td><td char="." align="char">8779</td></tr><tr><td align="left" colspan="10">SageELF</td></tr><tr><td align="left">&#x000a0;PB0988</td><td char="." align="char">2</td><td char="." align="char">264,829</td><td align="left">90,710</td><td align="left">156,094</td><td align="left">16,832</td><td align="left">1193</td><td align="left">2079</td><td align="left">34</td><td char="." align="char">34,066</td></tr><tr><td align="left">&#x000a0;PB0989</td><td char="." align="char">2</td><td char="." align="char">313,987</td><td align="left">91,557</td><td align="left">205,554</td><td align="left">15,603</td><td align="left">1273</td><td align="left">2640</td><td align="left">29</td><td char="." align="char">30,392</td></tr><tr><td align="left">&#x000a0;PB0990</td><td char="." align="char">2</td><td char="." align="char">330,259</td><td align="left">105,964</td><td align="left">205,387</td><td align="left">17,295</td><td align="left">1613</td><td align="left">2462</td><td align="left">32</td><td char="." align="char">35,342</td></tr><tr><td align="left">&#x000a0;PB0991</td><td char="." align="char">2</td><td char="." align="char">266,928</td><td align="left">89,911</td><td align="left">154,177</td><td align="left">21,440</td><td align="left">1400</td><td align="left">2012</td><td align="left">34</td><td char="." align="char">29,121</td></tr><tr><td align="left">&#x000a0;PB0992</td><td char="." align="char">2</td><td char="." align="char">270,609</td><td align="left">99,371</td><td align="left">140,062</td><td align="left">29,964</td><td align="left">1212</td><td align="left">1541</td><td align="left">37</td><td char="." align="char">29,266</td></tr><tr><td align="left">&#x000a0;PB0993</td><td char="." align="char">2</td><td char="." align="char">290,900</td><td align="left">109,275</td><td align="left">138,236</td><td align="left">41,563</td><td align="left">1826</td><td align="left">1118</td><td align="left">38</td><td char="." align="char">30,534</td></tr><tr><td align="left">&#x000a0;PB0994</td><td char="." align="char">2</td><td char="." align="char">276,923</td><td align="left">105,743</td><td align="left">116,597</td><td align="left">51,857</td><td align="left">2726</td><td align="left">823</td><td align="left">38</td><td char="." align="char">27,245</td></tr><tr><td align="left">&#x000a0;PB0995</td><td char="." align="char">2</td><td char="." align="char">67,946</td><td align="left">37,849</td><td align="left">19,610</td><td align="left">10,281</td><td align="left">206</td><td align="left">537</td><td align="left">56</td><td char="." align="char">14,243</td></tr><tr><td align="left">&#x000a0;PB0996</td><td char="." align="char">2</td><td char="." align="char">273,102</td><td align="left">15,683</td><td align="left">249,333</td><td align="left">6013</td><td align="left">2073</td><td align="left">6407</td><td align="left">6</td><td char="." align="char">3169</td></tr><tr><td align="left">&#x000a0;PB0997</td><td char="." align="char">2</td><td char="." align="char">369,770</td><td align="left">36,156</td><td align="left">319,644</td><td align="left">10,540</td><td align="left">3430</td><td align="left">4262</td><td align="left">10</td><td char="." align="char">9694</td></tr><tr><td align="left" colspan="10">Summary</td></tr><tr><td align="left">&#x000a0;Total: 15</td><td char="." align="char">30</td><td char="." align="char">3042,460</td><td align="left">859,117 (28.2%)</td><td align="left">1843,328 (60.6%)</td><td align="left">322,896 (10.6%)</td><td align="left">17,119 (0.6%)</td><td align="left">N/A</td><td align="left">N/A</td><td char="." align="char">265,773</td></tr></tbody></table></table-wrap>
</p><p id="Par25">265,773 distinct FL consensus transcripts resulted from further processing using the Iso-Seq pipeline, which merges each group of highly similar sequences into one consensus sequence using ICE, followed by refinement of the consensus transcripts using Quiver in conjunction with 544,150 non-FL transcripts. These sequences were then mapped onto the Pvir_v3 genome [<xref ref-type="bibr" rid="CR25">25</xref>]. The mapping results fall into three non-overlapping groups (as shown in Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S3):<def-list><def-item><term>G1:</term><def><p id="Par26">245,758 transcripts (92.5%) each being uniquely mapped to one genomic locus;</p></def></def-item><def-item><term>G2:</term><def><p id="Par27">18,719 transcripts (7.0%) each split mapped to two distinct genomic loci; and</p></def></def-item><def-item><term>G3:</term><def><p id="Par28">1296 transcripts (0.5%) each showing no significant match to any genomic location, hence not mapped.</p></def></def-item></def-list>
</p><p id="Par29">Figure&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> shows that the percentages of the G1 transcripts mapped to the Pvir_v3 genome using six different thresholds for the sequence-alignment similarity and sequence coverage, which were regarded as high-quality sequence alignments [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR27">27</xref>]. It is noteworthy that it may reflect either errors in the assembled genome or sequencing errors that some G1 transcripts do not have high-quality alignments with the genome. Further analyses are done only on those transcripts with high-quality alignments with the genome, specifically the 238,621 with at least 90% alignment identity and 85% sequence coverage.<fig id="Fig1"><label>Fig.&#x000a0;1</label><caption><p>The percentage of the G1 transcripts satisfying each of the six thresholds defined along the <italic>x</italic>-axis. The four thresholds marked as a, b, c, d, are for high-quality alignment in human, maize, Amborella, and PacBio tutorial Iso-Seq analysis, respectively [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR27">27</xref>]</p></caption><graphic xlink:href="13068_2018_1167_Fig1_HTML" id="MO1"/></fig>
</p><p id="Par30">We have also examined the 18,719 G2 transcripts, referred to as <italic>fusion</italic> transcripts [<xref ref-type="bibr" rid="CR23">23</xref>] (see &#x0201c;<xref rid="Sec16" ref-type="sec">Methods</xref>&#x0201d;), which give rise to 8850 distinct transcripts after removing redundant ones. Specifically, the 18,719 transcripts fall into 8850 groups of transcripts having approximately the same genomic coordinates by their alignment boundaries as shown in Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S4a; and only the longest transcript in each group is kept for further analyses. Of the 8850 transcripts, 6878 and 1972 are each split mapped to two inter-chromosomal and intra-chromosomal loci (Additional file <xref rid="MOESM1" ref-type="media">1</xref>), respectively. They cover a total of 6195 unique paired genomic loci, 2754 of which each have at least five Illumina pair-end reads linking the two genomic loci, hence indicating that these transcripts each represent one gene rather than fusion transcripts. In addition, we have also compared these fusion transcripts with available Sanger sequence data (see &#x0201c;<xref rid="Sec16" ref-type="sec">Methods</xref>&#x0201d;), and found that 524 of them each match at least one Sanger-based transcript with its 5&#x02032; and 3&#x02032; ends linking the two genomic loci. Furthermore, 1360 of the 6195 pairs can be each mapped to one gene in the genomes of the three related species, having 90% alignment identity and 85% sequence coverage. In total, 55.4% of the 6195 genomic pairs each have at least one supporting evidence for being one gene rather than a true fusion gene, hence suggesting possible errors in the assembled genome. Detailed information about how the 8850 transcripts could be used to improve the Pvir_v3 genome assembly/annotation (shown in Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S4b) is given as follows:<list list-type="order"><list-item><p id="Par31">Of the 4811 unique regions covered by the 6878 inter-chromosomal fusion transcripts, 1005 each link two chromatids of the same chromosome; 2090 each link two different chromosomes; 1639 each link a chromosome and a scaffold, and 77 each link two different scaffolds;</p></list-item><list-item><p id="Par32">1384 unique regions covered by the 1972 intra-chromosomal fusion transcripts link different regions in the same chromosome with the interval between the linked regions containing at least one gene in Pvir_v3, which is not part of the regions mapped by such transcripts; and</p></list-item><list-item><p id="Par33">Of the 6878 and 1972 transcripts, 6068 and 1811 are homologous to 2526 and 805 distinct proteins, respectively, in at least one of three related genomes, sorghum, foxtail millet, and maize, determined by BLASTX 2.4.0&#x02009;+&#x02009;[<xref ref-type="bibr" rid="CR28">28</xref>] (<italic>e</italic> value&#x02009;&#x0003c;&#x02009;1 <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\text{E}}^{ - 10} $$\end{document}</tex-math><mml:math id="M2"><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:msup></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq1.gif"/></alternatives></inline-formula>) (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S5a, b). Overall, 289 and 114 are chloroplast and mitochondrial genes, based on the NCBI definition [<xref ref-type="bibr" rid="CR29">29</xref>], respectively.</p></list-item></list>
</p><p id="Par34">The current understanding about this issue is while fusion genes have been found in microorganisms, they are believed to be rare in plants [<xref ref-type="bibr" rid="CR30">30</xref>&#x02013;<xref ref-type="bibr" rid="CR33">33</xref>]. Based on this information and our comparison of the predicted transcripts with Illumina and Sanger-based transcript sequences as well as their homologs in other plants, we posit that most of the predicted fusion transcripts are probably not correct. Clearly, further validation work is needed to clarify this issue.</p><p id="Par35">To assess if the G3 transcripts may encode proteins, BLASTX was used to compare these transcripts against protein sequences of the three species (<italic>e</italic> value&#x02009;&#x0003c;&#x02009;1 <inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\text{E}}^{ - 10} $$\end{document}</tex-math><mml:math id="M4"><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:msup></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq2.gif"/></alternatives></inline-formula>). 369 of the 1296 transcripts (28.5%) were found to be homologous to 137 distinct proteins in at least one related genome (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S6).</p></sec><sec id="Sec4"><title>Analyses of non-FL transcripts</title><p id="Par36">We have also examined the 1843,328 non-FL transcripts identified by our pipeline (as shown in Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S3), aiming to derive additional information about the transcriptome as well as the genome of switchgrass. Note that the main difference between the FL and non-FL transcripts is if they each contain the 5&#x02032;, 3&#x02032; signals and the polyA tails or not. The error rates in the non-FL transcripts are higher than those of the FL ones as no consensus-based error correction is applied to them [<xref ref-type="bibr" rid="CR22">22</xref>].</p><p id="Par37">We noted that the lengths of the non-FL transcripts are slightly shorter than those of the FL ones as shown in Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S7. Of the non-FL transcripts, 1274,642 (69%) can be mapped to the Pvir_v3 genome using GMAP, while 568,686 had no significant match to any genomic locations and, hence, were not mapped. Only the best alignment for each transcript is kept for further analyses, as described in the previous section. It is noteworthy that the medium base-pair mismatch rate across all such alignments range from 0.72 to 5.5% for the different sequencing libraries. We noted that the longer the library sequences, the higher the error rates, as expected (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S8a, b). While the following analyses are focused on the 1274,642 transcripts, we noted that 10.9% of the 568,686 transcripts are homologous to 7743 distinct proteins in one of the three related genomes, determined using BLASTX and <italic>e</italic> value&#x02009;&#x0003c;&#x02009;1 <inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\text{E}}^{ - 10} $$\end{document}</tex-math><mml:math id="M6"><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:msup></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq3.gif"/></alternatives></inline-formula> as cutoff, 7634 of which are not homologous to any of the 137 proteins mapped by the G3 transcripts, which together gives rise to 7771 distinct proteins. Of these proteins, 411 and 157 are encoded by chloroplast and mitochondrial genes, respectively, and 1044 are uncharacterized proteins.</p><p id="Par38">We have developed a statistical model for assessing if a transcript has a reliable mapping in the Pvir_v3 genome (see &#x0201c;<xref rid="Sec16" ref-type="sec">Methods</xref>&#x0201d;), based on the known error rates of the sequencing libraries. Specifically, if the mismatch rate between a transcript and the mapped genomic segment is outside the range based on our model, we predict that this segment is not the DNA sequence of this transcript. Among the best alignments between the 1274,642 non-FL transcripts and their matched DNA, 176,328 (13.8%) are predicted not aligned with the correct DNA sequence, giving rise to 1,098,314 transcripts aligned with the correct DNA, based on our model. These transcripts were further corrected by their aligned genomic sequences using TAPIS and the default parameters [<xref ref-type="bibr" rid="CR34">34</xref>]. At the end, 657,991 non-FL transcripts were kept after filtering out those alignments having inconsistent splice junctions between our prediction and Pvir_v3 annotation, determined using an SVM model trained based on SpliceGrapher [<xref ref-type="bibr" rid="CR35">35</xref>]. Of these, 628,290 are each mapped to one genomic location and 29,701 are each split mapped to two loci as in G2. Following the same analyses on the G2 transcripts, the predicted fusion transcripts, representing 2853 unique ones, are mapped to 2549 genomic loci (Additional file <xref rid="MOESM2" ref-type="media">2</xref>) with 2191 being inter-chromosomal and 662 intra-chromosomal, respectively. Of the two sets, 1591 of 2549 are mapped to novel genomic locations compared to those mapped from the FL fusion transcripts. 979 of the 2549 (38.4%) each have at least five Illumina pair-end reads connecting the paired loci, strongly supporting our prediction.</p></sec><sec id="Sec5"><title>Analyses of the identified unique transcripts</title><p id="Par39">Overall, 238,621 FL consensus transcripts and 628,044 non-FL transcripts are considered to have high-quality alignments with their matching DNA (with at least 90% sequence identity and 85% sequence coverage), giving rise to a total of 866,665 transcripts. Of these, 840,002 passed all criteria of the PASA pipeline for sequence assembly [<xref ref-type="bibr" rid="CR36">36</xref>]. After further processing using the pipeline and removing 1908 short transcripts (&#x0003c;&#x02009;100&#x000a0;bp, the minimum transcript length in Pvir_v3), 105,419 unique transcripts covering 60,616 unique genes resulted, 68,737 being FL and 36,682 non-FL ones, respectively (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). Among them, 80,005 are intron-containing transcripts. To ensure that our prediction is of high quality, an independent program &#x0201c;collapse_isoforms_by_sam.py&#x0201d; [<xref ref-type="bibr" rid="CR37">37</xref>] was used to validate our predictions. We found that 99.1% of the transcripts identified by this program were consistent with our prediction of unique transcripts.<table-wrap id="Tab2"><label>Table&#x000a0;2</label><caption><p>A summary of PacBio transcripts</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Genome version</th><th align="left">Type</th><th align="left">#Transcripts</th><th align="left">#AS transcripts</th><th align="left">#Genes from PacBio transcripts</th><th align="left">#Pvir_v3/4 genes covered by PacBio data</th><th align="left">#Pvir_v3/4 genes not covered by PacBio data</th></tr></thead><tbody><tr><td align="left" rowspan="3">Pvir_v3</td><td align="left">FL</td><td char="(" align="char">68,737 (43,487)</td><td char="(" align="char">44,905 (31,773)</td><td char="(" align="char">38,291 (1673)</td><td char="." align="char">34,111</td><td align="left"/></tr><tr><td align="left">Non-FL</td><td char="(" align="char">36,682 (33,300)</td><td char="(" align="char">15,191 (13,855)</td><td char="(" align="char">29,856 (7262)</td><td char="." align="char">19,933</td><td align="left"/></tr><tr><td align="left">Total</td><td char="(" align="char">105,419 (76,787)</td><td char="(" align="char">60,096 (45,628)</td><td char="(" align="char">60,616 (8795)</td><td char="." align="char">43,570</td><td char="." align="char">58,495</td></tr><tr><td align="left" rowspan="3">Pvir_v4</td><td align="left">FL</td><td char="(" align="char">68,742 (43,562)</td><td char="(" align="char">44,932 (31,839)</td><td char="(" align="char">38,258 (1825)</td><td char="." align="char">33,939</td><td align="left"/></tr><tr><td align="left">Non-FL</td><td char="(" align="char">36,663 (33,318)</td><td char="(" align="char">15,177 (13,846)</td><td char="(" align="char">29,842 (7490)</td><td char="." align="char">19,699</td><td align="left"/></tr><tr><td align="left">Total</td><td char="(" align="char">105,405 (76,880)</td><td char="(" align="char">60,109 (45,685)</td><td char="(" align="char">60,573 (9146)</td><td char="." align="char">43,201</td><td char="." align="char">48,637</td></tr></tbody></table><table-wrap-foot><p>The number inside each pair of parentheses represents the number of genes or transcripts not covered in Pvir_v3 or 4</p></table-wrap-foot></table-wrap>
</p><p id="Par40">Illumina short reads were mapped onto the Pvir_v3 genome using Tophat2 [<xref ref-type="bibr" rid="CR38">38</xref>] and were compared with the genomic alignments of our 105,419 transcripts. 83.0 and 96.4% of the splice junctions in these transcripts were supported by at least five Illumina reads averaged over 66 Illumina samples and in at least one Illumina sample, respectively (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S9a, b), hence offering a strong evidence for the high quality of these PacBio transcripts.</p><p id="Par41">Overall, the 105,419 transcripts cover 43,570 Pvir_v3 genes, which were classified into nine distinct groups (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S10), based on the type of the overlap between the aligned genomic regions and the exon&#x02013;intron structure of the matched Pvir_v3 gene using Cuffcompare v2.2.1 [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR41">41</xref>]:<list list-type="order"><list-item><p id="Par42">9487 transcripts (9.0%) are mapped to 8795 genomic loci not overlapping with any Pvir_v3 genes. To examine if these transcripts may indeed encode proteins, BLASTX was used to compare each with protein sequences in the NR database (<italic>e</italic> value&#x02009;&#x0003c;&#x02009;1 <inline-formula id="IEq4"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\text{E}}^{ - 5} $$\end{document}</tex-math><mml:math id="M8"><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>5</mml:mn></mml:mrow></mml:msup></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq4.gif"/></alternatives></inline-formula>), resulting in 4004 (42.2%) homologous proteins, of which 61, 20, 933, and 158 are chloroplast, mitochondrial genes, retrotransposons, and uncharacterized proteins, respectively (Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Table S2);</p></list-item><list-item><p id="Par43">2500 transcripts (2.4%) each properly contain a Pvir_v3 gene;</p></list-item><list-item><p id="Par44">1463 transcripts (1.4%) are each located inside an intron of a Pvir_v3 gene;</p></list-item><list-item><p id="Par45">The mapped genomic loci of 1228 transcripts (1.2%) each have one intron that contains a Pvir_v3 gene;</p></list-item><list-item><p id="Par46">28,632 transcripts (27.2%) each have the same exon&#x02013;intron structures as the matching Pvir_v3 genes;</p></list-item><list-item><p id="Par47">6984 transcripts (6.6%) each overlap with part of but not the whole exon of a Pvir_v3 gene on the opposite genomic strand;</p></list-item><list-item><p id="Par48">2450 transcripts (2.3%) each overlap with part of but not the whole exon of a Pvir_v3 gene on the same strand;</p></list-item><list-item><p id="Par49">45,168 transcripts (42.8%) each share at least one splice junction with 18,520 Pvir_v3 genes, but differ at other splice sites; and</p></list-item><list-item><p id="Par50">5340 transcripts (5.1%) are each properly contained in the coding region of a Pvir_v3 gene.</p></list-item></list>
</p><p id="Par51">In sum, (1&#x02013;2) suggest the possibility that some genes might be missed or incorrectly predicted. (3&#x02013;4) suggest the possibility that some regions in the current genome might be mis-assembled or some genes are missed or incorrectly predicted. (5) indicates that over one quarter of our predicted transcripts are consistent with the Pvir_v3 transcripts. (6&#x02013;9) suggest that&#x02009;~&#x02009;57% of our predicted transcripts are potentially novel splicing transcripts compared with Pvir_v3 transcripts.</p><p id="Par52">Overall, our 76,787 transcripts each do not have exactly the same transcript in Pvir_v3. 59,942 of these transcripts are potentially novel AS transcripts (see &#x0201c;<xref rid="Sec6" ref-type="sec">Identification of AS transcripts</xref>&#x0201d;) of known genes, and the remaining represent novel or mis-predicted genes. Furthermore, 21,072 of our identified transcripts are longer than the corresponding Pvir_v3 transcripts, predominantly at the two ends. As comparison, 104,731 Pvir_v3 transcripts are not identified by our analyses, and 22,423 Pvir_v3 transcripts are longer than our matching transcripts. We have also noted that (a) the number of distinct transcripts per gene derived by our analyses is higher than that by the Pvir_3 annotation (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a); and (b) our identified transcripts tend to be longer than those in GenBank as well as by Pvir_v3 (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b), specifically the mean length being 1,166&#x000a0;bp (switchgrass in GenBank), 1,569&#x000a0;bp (Pvir_v3), and 2,360&#x000a0;bp (PacBio), respectively.<fig id="Fig2"><label>Fig.&#x000a0;2</label><caption><p>Characterization of the unique transcripts. <bold>a</bold> The number of transcripts per gene by PacBio data vs. those in Pvir_v3. <bold>b</bold> Length distribution of transcripts from PacBio sequencing, GenBank, and Pvir_v3, respectively. <bold>c</bold> The numbers of AS events, AS transcripts, and the relevant genes identified based on PacBio data. <bold>d</bold> An example of one gene with 11 AS transcripts. The transcripts in Pvir_v3 and PacBio are marked with red and blue color, respectively</p></caption><graphic xlink:href="13068_2018_1167_Fig2_HTML" id="MO2"/></fig>
</p><p id="Par53">At the gene level, 8795 of our predicted 60,616 distinct genes are not in the Pvir_v3 annotation, while 58,495 of the Pvir_v3 genes are not covered by the PacBio data (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). The other 51,821 PacBio genes overlap with 43,570 Pvir_v3 genes. Of these, 16,640 have substantial overlaps by both predictions, and hence can be considered as the same genes. Some differences exist even among these predicted genes. Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref> summarizes the main differences, including (i) different exon&#x02013;intron structures; (ii) different genomic locations of the 5&#x02032; or/and 3&#x02032; boundaries; (iii) a PacBio transcript spanning more than one Pvir_v3 gene; and (vi) multiple non-overlapping PacBio transcripts covered by one Pvir_v3 gene.<table-wrap id="Tab3"><label>Table&#x000a0;3</label><caption><p>Difference in genes as annotated in Pvir_v3 and Pvir_v4 compared with PacBio transcripts, respectively</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left"/><th align="left">#Genes in Pvir_v3</th><th align="left">#Genes in Pvir_v4</th></tr></thead><tbody><tr><td align="left">Sequence span</td><td char="(" align="char">348 (355)</td><td char="(" align="char">340 (347)</td></tr><tr><td align="left">Gene structures</td><td char="(" align="char">6628 (8443)</td><td char="(" align="char">6621 (8456)</td></tr><tr><td align="left">UTR</td><td char="(" align="char">10,447 (11,100)</td><td char="(" align="char">10,365 (11,012)</td></tr><tr><td align="left">Span multiple loci</td><td char="(" align="char">291 (142)</td><td char="(" align="char">288 (141)</td></tr><tr><td align="left">Split</td><td char="(" align="char">7 (14)</td><td char="(" align="char">7 (14)</td></tr><tr><td align="left">Total</td><td char="(" align="char">16,640 (20,018)</td><td char="(" align="char">16,544 (19,903)</td></tr></tbody></table><table-wrap-foot><p>&#x0201c;Sequence span&#x0201d; refers to genes with different starting or ending exon; &#x0201c;Gene structures&#x0201d; refers to genes with different exon&#x02013;intron structures; &#x0201c;UTR&#x0201d; refers to genes with different UTRs; and &#x0201c;Span multiple loci&#x0201d; refers to multiple genes covered by one transcript. Other terms defined similarly. The number inside each pair of parentheses represents the number of PacBio transcripts used for making the comparison</p></table-wrap-foot></table-wrap>
</p></sec><sec id="Sec6"><title>Identification of AS transcripts</title><p id="Par54">We have applied the PASA pipeline [<xref ref-type="bibr" rid="CR36">36</xref>] to infer AS events, namely intron retention (IR), alternative 3&#x02032; splice site (A3SS), alternative 5&#x02032; splice site (A5SS), exon skipping (ES), and other events (starts or ends in an intron, or alternative terminal exon) in the 105,419 transcripts. A total of 82,959 AS events are identified in 60,096 transcripts covering 16,642 genes (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>c). IR represents the predominant AS event (42%), A3SS is the second, and ES the least frequent, which are consistent with the published data on Arabidopsis and rice [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR42">42</xref>].</p><p id="Par55">Illumina reads, aligned to the switchgrass genome by Tophat2 [<xref ref-type="bibr" rid="CR38">38</xref>] and processed by Miso [<xref ref-type="bibr" rid="CR43">43</xref>], have been used to validate our predicted AS events. Overall, 85.7, 70.0, 60.0, and 85.8% of the predicted IR, A3SS, A5SS, and ES events have Illumina data support, respectively (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S11). It is noteworthy that these numbers are not low, considering that the Illumina data used are collected from a different set of switchgrass samples. In addition, we have also used Sanger sequence data to validate the predicted AS events (see &#x0201c;<xref rid="Sec16" ref-type="sec">Methods</xref>&#x0201d;). Overall, 2039 and 699 of the predicted IR and ES events have Sanger data support, 290 and 109 of which are different from Illumina validated, respectively.</p><p id="Par56">Using the criterion that an AS transcript is a transcript containing at least one AS event, we predicted a total of 60,096 AS transcripts (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>), 76% being novel. Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>d shows one example of a gene annotated to produce a single transcript but found to have 11 AS transcripts.</p></sec><sec id="Sec7"><title>Quantification of PacBio transcripts</title><p id="Par57">We have used the more quantitative Illumina data to estimate the expression levels of the 105,419 transcripts in each of the ten tissue types, from which the Illumina data are collected. The basic idea is to use the quantitative information of the Illumina reads that match each of the 105,419 transcripts to estimate the expression level of the transcript in each tissue type. Specifically, (i) for each transcript in each tissue type, the mapped Illumina reads were assembled by StringTie [<xref ref-type="bibr" rid="CR44">44</xref>], using the exon&#x02013;intron structure of the transcript as the reference, to estimate its FPKM; (ii) only those assembled transcripts sharing the same intron chains with those of the reference were kept; and (iii) only transcripts with FPKM <inline-formula id="IEq5"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \ge $$\end{document}</tex-math><mml:math id="M10"><mml:mo>&#x02265;</mml:mo></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq5.gif"/></alternatives></inline-formula> 0.01 (a value chosen based on transcript coverage saturation analysis; Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S12a) in at least two replicates are regarded as successfully assembled.</p><p id="Par58">At the end, 52,809 transcripts were assigned with expression levels across the ten tissue types with Illumina data. The first two principal components of these transcripts show that these transcripts can characterize the specificity and similarity between different tissue types (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S12b). We have examined 3190 tissue-specific transcripts, 2601 which are novel. Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref> summarizes the number of PacBio transcripts expressed in each of the ten tissue types.<table-wrap id="Tab4"><label>Table&#x000a0;4</label><caption><p>A summary of transcripts predicted to be expressed in each tissue type</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Tissue type</th><th align="left">#Expressed genes</th><th align="left">#Transcripts</th><th align="left">#AS transcript</th><th align="left"># Tissue-specific transcripts</th><th align="left">#CW-related transcripts</th><th align="left">#lncRNAs</th><th align="left">#TFs</th></tr></thead><tbody><tr><td align="left">Crown</td><td char="(" align="char">31,256 (1177)</td><td char="(" align="char">41,772 (17,769)</td><td char="(" align="char">23,620 (11,506)</td><td char="(" align="char">128 (106)</td><td char="(" align="char">916 (155)</td><td char="(" align="char">1156 (1053)</td><td char="(" align="char">1813 (617)</td></tr><tr><td align="left">Leaf blade</td><td char="(" align="char">29,081 (1219)</td><td char="(" align="char">39,747 (17,626)</td><td char="(" align="char">23,306 (11,654)</td><td char="(" align="char">248 (213)</td><td char="(" align="char">651 (115)</td><td char="(" align="char">1190 (1090)</td><td char="(" align="char">1642 (592)</td></tr><tr><td align="left">Leaf sheath</td><td char="(" align="char">30,517 (1222)</td><td char="(" align="char">41,689 (18,418)</td><td char="(" align="char">24,251 (12,198)</td><td char="(" align="char">215 (181)</td><td char="(" align="char">787 (143)</td><td char="(" align="char">1191 (1086)</td><td char="(" align="char">1731 (616)</td></tr><tr><td align="left">Node</td><td char="(" align="char">31,294 (1243)</td><td char="(" align="char">42,329 (18,421)</td><td char="(" align="char">24,262 (12,070)</td><td char="(" align="char">170 (138)</td><td char="(" align="char">889 (153)</td><td char="(" align="char">1197 (1090)</td><td char="(" align="char">1798 (626)</td></tr><tr><td align="left">VB</td><td char="(" align="char">28,003 (1006)</td><td char="(" align="char">36,926 (15,261)</td><td char="(" align="char">21,247 (9934)</td><td char="(" align="char">114 (94)</td><td char="(" align="char">721 (120)</td><td char="(" align="char">949 (858)</td><td char="(" align="char">1526 (523)</td></tr><tr><td align="left">Inflorescence</td><td char="(" align="char">31,365 (1258)</td><td char="(" align="char">42,298 (18,468)</td><td char="(" align="char">23,966 (11,900)</td><td char="(" align="char">219 (188)</td><td char="(" align="char">890 (143)</td><td char="(" align="char">1260 (1151)</td><td char="(" align="char">1817 (614)</td></tr><tr><td align="left">Root</td><td char="(" align="char">32,133 (1255)</td><td char="(" align="char">43,787 (19,288)</td><td char="(" align="char">24,951 (15,563)</td><td char="(" align="char">407 (294)</td><td char="(" align="char">966 (180)</td><td char="(" align="char">1227 (1126)</td><td char="(" align="char">1867 (663)</td></tr><tr><td align="left">Seed DAP</td><td char="(" align="char">33,174 (1458)</td><td char="(" align="char">45,985 (21,229)</td><td char="(" align="char">26,328 (13,768)</td><td char="(" align="char">1007 (839)</td><td char="(" align="char">885 (163)</td><td char="(" align="char">1460 (1335)</td><td char="(" align="char">1925 (679)</td></tr><tr><td align="left">Seed germ</td><td char="(" align="char">30,905 (1212)</td><td char="(" align="char">42,015 (18,519)</td><td char="(" align="char">24,101 (12,100)</td><td char="(" align="char">317 (260)</td><td char="(" align="char">867 (162)</td><td char="(" align="char">1199 (1094)</td><td char="(" align="char">1708 (593)</td></tr><tr><td align="left">Shoot</td><td char="(" align="char">33,067 (1350)</td><td char="(" align="char">45,553 (20,350)</td><td char="(" align="char">26,095 (13,330)</td><td char="(" align="char">365 (288)</td><td char="(" align="char">979 (167)</td><td char="(" align="char">1342 (1224)</td><td char="(" align="char">1962 (691)</td></tr><tr><td align="left">Shared by all tissue types</td><td char="(" align="char">22,948 (729)</td><td char="(" align="char">28,623 (10,666)</td><td char="(" align="char">16,245 (6743)</td><td char="(" align="char">N/A</td><td char="(" align="char">490 (67)</td><td char="(" align="char">686 (614)</td><td char="(" align="char">1088 (351)</td></tr></tbody></table><table-wrap-foot><p>The number inside each pair of parentheses represents the number of genes or transcripts not covered in Pvir_v3</p></table-wrap-foot></table-wrap>
</p></sec><sec id="Sec8"><title>Identification of CW-related transcripts</title><p id="Par59">Tblastn was used to homology-map known CW-related proteins involved in cell wall construction and remodeling in Arabidopsis, maize, and rice to the 105,419 transcripts, respectively, as stored in the Cell Wall Genomics database [<xref ref-type="bibr" rid="CR45">45</xref>] (<italic>e</italic> value <inline-formula id="IEq6"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \le 1{\text{E}}^{ - 20} $$\end{document}</tex-math><mml:math id="M12"><mml:mrow><mml:mo>&#x02264;</mml:mo><mml:mn>1</mml:mn><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>20</mml:mn></mml:mrow></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq6.gif"/></alternatives></inline-formula> and b-score <inline-formula id="IEq7"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \ge 93{\text{\% }} $$\end{document}</tex-math><mml:math id="M14"><mml:mrow><mml:mo>&#x02265;</mml:mo><mml:mn>93</mml:mn><mml:mrow><mml:mtext>\%</mml:mtext><mml:mspace width="0.333333em"/></mml:mrow></mml:mrow></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq7.gif"/></alternatives></inline-formula> as cutoffs) [<xref ref-type="bibr" rid="CR46">46</xref>] (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S13). 1549 distinct PacBio transcripts of 1077 genes are homologous to CW-related transcripts (Additional file <xref rid="MOESM3" ref-type="media">3</xref>), of which 639 are novel transcripts of known genes of Pvir_v3 and 464 are novel AS transcripts. 553 of the 1077 genes (51%) significantly enrich the lignin, xyloglucan, and hemicellulose metabolic processes (<italic>p value&#x02009;</italic>&#x0003c;&#x02009;0.01) (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S14a, b, c). Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref> summarizes the five CW synthesis-related functional categories that the 1549 transcripts fall into, with Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref> detailing the CW-related transcripts expressed in each tissue type.<table-wrap id="Tab5"><label>Table&#x000a0;5</label><caption><p>Functional categories into which the 1549 predicted CW-related transcripts fall</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Functionality</th><th align="left">#Arabidopsis homologs</th><th align="left">#Maize homologs</th><th align="left">#Rice homologs</th><th align="left">Total</th></tr></thead><tbody><tr><td align="left">Substrate generation</td><td char="(" align="char">184 (10)</td><td align="left">220 (14)</td><td align="left">190 (14)</td><td char="(" align="char">252 (19)</td></tr><tr><td align="left">Polysaccharide syntheses and glycosyl transferase</td><td char="(" align="char">253 (16)</td><td align="left">411 (37)</td><td align="left">336 (27)</td><td char="(" align="char">442 (45)</td></tr><tr><td align="left">Secretion and targeting pathways</td><td char="(" align="char">81 (1)</td><td align="left">N/A</td><td align="left">N/A</td><td char="(" align="char">81 (1)</td></tr><tr><td align="left">Assembly, architecture and growth</td><td char="(" align="char">285 (31)</td><td align="left">357 (43)</td><td align="left">322 (38)</td><td char="(" align="char">436 (50)</td></tr><tr><td align="left">Differentiation and secondary wall formation</td><td char="(" align="char">94 (6)</td><td align="left">158 (14)</td><td align="left">26 (3)</td><td char="(" align="char">166 (16)</td></tr><tr><td align="left">Signaling and response mechanisms</td><td char="(" align="char">115 (23)</td><td align="left">135 (19)</td><td align="left">20 (3)</td><td char="(" align="char">185 (35)</td></tr><tr><td align="left">Overall</td><td char="(" align="char">1008 (87)</td><td align="left">1272 (126)</td><td align="left">889 (85)</td><td char="(" align="char">1549 (165)</td></tr></tbody></table><table-wrap-foot><p>The number inside each pair of parentheses represents the number of non-FL transcripts. Note: N/A indicates there is no matching proteins in the database</p></table-wrap-foot></table-wrap>
</p><p id="Par60">We anticipate that these novel CW-related transcripts will provide useful candidates for studying plant cell wall synthesis and remodeling processes.</p></sec><sec id="Sec9"><title>Prediction of lncRNA</title><p id="Par61">We have predicted which of the 105,419 transcripts may encode lncRNAs using the following procedure: (a) select from the 105,419 transcripts 13,021 candidates of at most 350&#x000a0;bp long each contained inside one open reading frame in the genome [<xref ref-type="bibr" rid="CR47">47</xref>]; (b) remove from the candidate list all 6602 transcripts that encode proteins, as determined using BLASTX (<italic>e</italic> value&#x02009;&#x0003c;&#x02009;1 <inline-formula id="IEq8"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\text{E}}^{ - 10} $$\end{document}</tex-math><mml:math id="M16"><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:msup></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq8.gif"/></alternatives></inline-formula>) against the protein sequences in the three related species (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S15a); and (c) select those having high non-coding RNA scores given by coding potential calculator (CPC) [<xref ref-type="bibr" rid="CR48">48</xref>], resulting in 5165 strong and 1119 weak candidates for lncRNA. Further analysis is conducted on the 5165 strong candidates.</p><p id="Par62">We noted that (i) 40% of the predicted lncRNAs were in intergenic regions, 9% in introns, 14% on the antisense strand, and 37% on the sense strand of protein-encoding regions [note: these are not homologous to those in the above (b)]; (ii) 2% of the predicted lncRNAs are homologous to known lncRNA in the three related genomes (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S15b), which is not surprising knowing that RNA genes tend to not have sequence-level conservation; (iii) 71% are single-exon genes (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S15c); and (iv) their FPKM values were lower than those of protein-coding genes (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S15d). All are consistent with published studies [<xref ref-type="bibr" rid="CR49">49</xref>&#x02013;<xref ref-type="bibr" rid="CR53">53</xref>]. Additional file <xref rid="MOESM4" ref-type="media">4</xref> gives the list of the coordinates of the predicted lncRNAs in Pvir_v3. The expression levels of these IncRNAs in each tissue type are summarized in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>.</p></sec><sec id="Sec10"><title>Prediction of transcription factor (TF)</title><p id="Par63">3205 transcripts are predicted to be TFs by scanning the HMMs of all the 56 families of TFs given in PlantTFDB 4.0 [<xref ref-type="bibr" rid="CR54">54</xref>] against the 105,419 transcripts, which fall into 54 TF families (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S16 and Additional file <xref rid="MOESM5" ref-type="media">5</xref>). To validate our prediction, 489 switchgrass TFs were collected from the literature [<xref ref-type="bibr" rid="CR55">55</xref>&#x02013;<xref ref-type="bibr" rid="CR57">57</xref>], 331 of which are in our prediction, providing strong evidence for our prediction. The TFs expressed in each tissue type are summarized in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>.</p></sec></sec><sec id="Sec11"><title>Discussion</title><sec id="Sec12"><title>PacBio long reads vs. Illumina high-throughput short reads</title><p id="Par64">From our analyses and comparisons, we see strengths as well as limitations of the PacBio data. Reliable identification of FL transcripts is clearly an advantage of the PacBio data, which has enabled us to infer considerably more AS transcripts than before. On the other hand, the relative lack of quantitative information in PacBio data vs. Illumina data is a weakness of the technology. Here, we have made some effort to integrate the information derivable from both the PacBio and the Illumina data as the two data types available to us are not collected from the same tissue samples. This represents an area where bioinformatics techniques can play a major role in optimally integrating information from the two data types, to offer both reliable and quantitative information for transcriptomic analyses of plants.</p></sec><sec id="Sec13"><title>Identification of alternative splicing in switchgrass</title><p id="Par65">Our analysis detected 213,678 splice junctions in 105,419 transcripts, 32,508 of which (15%) were novel compared to the Pvir_v3 annotation. Over 96% of these junctions are supported by Illumina data, providing high confidence of our identification. We have estimated the percentage of the intron-containing genes that may encode AS transcripts by calculating the ratio between the number of such genes with at least one AS transcript and the number of such genes in our PacBio data, which is 47.4% having AS transcripts. In addition, 57.4% of the PacBio genes with at least two introns encode AS transcripts. Although this number is consistent with data provided in published studies [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR58">58</xref>], we suspect that it is still an underestimate of the actual percentage as we used only six tissue types at the R1 stage under normal growth conditions. We expect that more AS transcripts-harboring genes may be revealed when more tissue types across more developmental stages under stressful conditions are subject to transcriptomic analyses.</p></sec><sec id="Sec14"><title>Pvir_v3 <italic>vs.</italic> Pvir_v4 of the switchgrass genome</title><p id="Par66">JGI has recently released a new version, Pvir_v4, of the assembled genome along with its new annotation [<xref ref-type="bibr" rid="CR12">12</xref>]. Since a vast amount of the computing and analysis work presented here was conducted before the public release of Pvir_v4, our presentation was focused on Pvir_v3.</p><p id="Par67">We have run PASA [<xref ref-type="bibr" rid="CR36">36</xref>] on the 866,665 PacBio transcripts (238,621 FL and 628,044 non-FL) against Pvir_v4 using the same parameters as against Pvir_v3, and got 105,405 unique transcripts (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). The following differences between the two versions are observed:<list list-type="order"><list-item><p id="Par68">104,238 of the 105,419 transcripts (98.9%) match the same genomic sequences in Pvir_v4 vs. Pvir_v3, except for changes in their sequence coordinates. Among the remaining 1181 transcripts, the majority (878) have some minor differences in their sequence alignments and 296 are largely the results of regions that were N-ed out between the two versions (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S17). Additional file <xref rid="MOESM6" ref-type="media">6</xref> provides a mapping of the genomic coordinates between genes in the two versions, Additional file <xref rid="MOESM7" ref-type="media">7</xref> gives the mapping between the corresponding transcripts in Pvir_3 and v4, and Additional files <xref rid="MOESM8" ref-type="media">8</xref> and <xref rid="MOESM9" ref-type="media">9</xref> provide the coordinates of 105,419 and 105,405 assembled transcripts against Pvir_v3 and Pvir_v4, respectively; and</p></list-item><list-item><p id="Par69">We have also examined the 18,719 FL and 29,701 non-FL transcripts that are each split mapped to two distinct loci in Pvir_v3 and how they are mapped to Pvir_v4. We noted that 8803 such FL transcripts remain as fusion transcripts covering 6123 unique paired genomic loc, 41% of which have Illumina reads support, with 1962 being intra-chromosomal and 6841 inter-chromosomal, while 47 FL transcripts are each now mapped to one genomic locus or unmapped due to genomic rearrangements or regions that were N-ed out. Similarly, 2834 non-FL transcripts remain as fusion transcripts covering 2531 unique genomic loci, 43% of which are validated by Illumina reads, 643 and 2191 being intra-chromosomal and inter-chromosomal, respectively, while 19 non-FL transcripts are each now mapped to one genomic locus or unmapped due to genomic rearrangements or regions that were N-ed out. Furthermore, 10,083 Pvir_v3 genes are removed in Pvir_v4 due to their low prediction confidence scores and 144 are removed due to sequence changes in the reassembled genome of Pvir_v4.</p></list-item></list>
</p></sec></sec><sec id="Sec15"><title>Conclusion</title><p id="Par70">Through integrative analyses of PacBio- and Illumina (and limited Sanger)-based transcriptomic data, we were able to reliably infer splicing isoforms and their expression levels at a genome scale, by taking advantage of the strong complementary nature of the two data types. In addition, such analyses also provide highly useful&#x000a0;information for guiding further improvement in the partially assembled genome of switchgrass.</p></sec><sec id="Sec16"><title>Methods</title><sec id="Sec17"><title>Plant samples</title><p id="Par71">Vegetative clones of switchgrass genotype Alamo AP13 obtained by splitting tillers were grown in 3-gallon pots with Metro-Mix 830 soil in a greenhouse with a 16-h light photoperiod (6:00&#x000a0;am&#x02013;10:00&#x000a0;pm) with supplementary lighting from parabolic aluminized reflector lamps (average 390&#x000a0;&#x003bc;E/m2/S1) and relative humidity 77&#x02013;22% (average 51%). The temperature in the greenhouse ranged from 25 to 29&#x000a0;&#x000b0;C (average 26&#x000a0;&#x000b0;C). Plants were watered three times per week, and fertilizer (Peter&#x02019;s Fert 20-10-20, 100&#x000a0;ppm) was applied during the last watering each week. A whole tiller consisting of leaves, leaf sheaths, internodes, nodes, and flowers and roots were collected at the R1 developmental stage [<xref ref-type="bibr" rid="CR59">59</xref>] and used to do PacBio sequencing. Ten tissue types&#x02014;seed across three germination stages, root, shoot, leaf shade, leaf sheath, nodes, vascular bundle, crown, inflorescence, seed and flower across seven developmental stages of seed according to the same criteria described in [<xref ref-type="bibr" rid="CR59">59</xref>]&#x02014;were used to do Illumina sequencing (Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Table S3).</p></sec><sec id="Sec18"><title>RNA preparation for PacBio sequencing</title><p id="Par72">A whole tiller, including leaves, leaf sheaths, internodes, nodes, and flowers, was collected at the R1 development stage [<xref ref-type="bibr" rid="CR59">59</xref>] and frozen immediately after resection in liquid nitrogen. Roots were collected and frozen in liquid nitrogen separately. Total RNA of the above-ground tissues was isolated using RNeasy Plant Mini Kit [<xref ref-type="bibr" rid="CR60">60</xref>]. Total RNA of roots was isolated from roots using TRI REAGENT [<xref ref-type="bibr" rid="CR61">61</xref>]. To eliminate residual genomic DNA contamination, RNA samples were treated using Turbo DNase and then cleaned up using RNeasy MinElute Cleanup Kit [<xref ref-type="bibr" rid="CR60">60</xref>]. The cleaned RNA of the above-ground tissues and roots was mixed together.</p></sec><sec id="Sec19"><title>PacBio library construction</title><p id="Par73">The first-strand cDNA was synthesized using SuperScript II (Invitrogen). The 3&#x02032; dT primer (5&#x02032;-TAGTCGAACTGAGATCTCCAGCAGT<sub>30</sub>VN -3&#x02032;) and total RNA were first incubated at 72&#x000a0;&#x000b0;C for 3&#x000a0;min and then the 5&#x02032; primer (5&#x02032;-TAGTCGAACTGAGATCTCCAGCAGTACrGrGrG-3&#x02032;) and SuperScript II were added into the reaction mix for reverse transcription and template switching. The reaction was performed at 42&#x000a0;&#x000b0;C for 90&#x000a0;min, followed by 50&#x000a0;&#x000b0;C for 2&#x000a0;min and 42&#x000a0;&#x000b0;C for 2&#x000a0;min for 10 cycles on a thermocycler. After the first-strand reaction, the cDNA (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S18a) was amplified using a PCR primer (5&#x02032;-TAGTCGAACTGAGATCTCCAGCAG-3&#x02032;) and KAPA HiFi HotSart ReadyMix (KAPA).</p><p id="Par74">Two size-selection procedures were used: (1) a set of libraries was made from the cDNA generated from five cycles of PCR and gel selection, and different size fractions (cDNA&#x02009;&#x0003c;&#x02009;0.5&#x000a0;kb, 0.5&#x02013;1&#x000a0;kb, 1&#x02013;2&#x000a0;kb, 2&#x02013;3&#x000a0;kb, and 3&#x02013;6&#x000a0;kb) were collected from a 0.8% agarose gel and purified using the Zymoclean Large Fragment DNA Recovery Kit (Zymo) (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S18b); and (2) to reduce bias towards short reads, a second procedure of size selection was performed on a Sage Science Electrophoretic Lateral Fractionator (ELF), resulting in the isolation of 10 discrete size fractions. cDNA from 10 cycles of PCR was loaded onto a 0.75% cassette (Sage), and size-based separation mode was used to select cDNA from 500&#x000a0;bp. cDNA fractions&#x02009;&#x0003e;&#x02009;5 Kbp were collected for additional 10 PCR cycles and ELF selection to enrich long reads (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S18c).</p><p id="Par75">After size selection, the collected cDNA fractions were treated with DNA damage repair mix [<xref ref-type="bibr" rid="CR62">62</xref>], followed by end repair and ligation of SMRT adapters using the PacBio SMRTbell Template Prep Kit to create PacBio libraries. These two size-fractional library sets selected by gel and SageELF were sequenced on the PacBio RSII platform using P5-C3 and P6-C4 chemistry with 4&#x000a0;h movies, respectively.</p></sec><sec id="Sec20"><title>Illumina RNA-Seq library construction</title><p id="Par76">Plate-based RNA sample prep was performed on the PerkinElmer Sciclone NGS robotic liquid handling system using Illumina&#x02019;s TruSeq Stranded mRNA HT sample prep kit utilizing polyA selection of the mRNA, following the protocol outlined in Illumina&#x02019;s user guide [<xref ref-type="bibr" rid="CR63">63</xref>]. This is done with the following conditions: the total RNA starting material was 1&#x000a0;&#x003bc;g per sample and 10 cycles of PCR was used for library amplification. The prepared libraries were then quantified using KAPA Biosystem&#x02019;s next-generation sequencing library qPCR kit and run on a Roche LightCycler 480 real-time PCR instrument. The quantified libraries were then multiplexed into pools of six libraries each, and the pool was then prepared for sequencing on the Illumina HiSeq sequencing platform utilizing a TruSeq paired-end cluster kit (v4) and Illumina&#x02019;s cBot instrument to generate a clustered flow cell for sequencing. Sequencing of the flow cell was performed on the Illumina HiSeq&#x000a0;2500 sequencer using TruSeq SBS sequencing kits, v4, following a 2&#x02009;&#x000d7;&#x02009;150 indexed run recipe. These 66 libraries are of high quality since the biological replicates have strong correlations among themselves, achieving Spearman&#x02019;s correlation&#x02009;&#x0003e;&#x02009;0.9 (Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Table S4).</p><p id="Par77">Illumina data were used to validate and quantify the PacBio-based transcripts. Although the Illumina data were collected from samples different from those from which the PacBio data were collected, we argue that such validation is valid for the following reasons: (1) the Illumina samples cover the types of switchgrass tissues for the PacBio data collection; and (2) the types of proteins, hence their mRNA types, including AS transcripts, in the two matching tissue types, possibly at different developmental stages and detailed nutrient conditions, should be largely the same.</p></sec><sec id="Sec21"><title>Data collected from the public databases</title><p id="Par78">The switchgrass genomic sequences Pvir_v3.1 and v4 [<xref ref-type="bibr" rid="CR12">12</xref>] were downloaded from JGI. Protein sequences, transcript sequences, and the genomic sequences of the three related species&#x02014;sorghum (v3.1.1), foxtail millet (v2.2), and maize (5b+)&#x02014;were also downloaded from JGI [<xref ref-type="bibr" rid="CR64">64</xref>&#x02013;<xref ref-type="bibr" rid="CR66">66</xref>]. In addition, 130 published FL transcripts of switchgrass were collected from GenBank [<xref ref-type="bibr" rid="CR67">67</xref>]. 35,660, 28,588, and 104,831 high-quality Sanger sequences of switchgrass genotype Alamo AP13 were collected from the literature [<xref ref-type="bibr" rid="CR68">68</xref>] and used in our validation analyses. The sequences of 489 TFs of switchgrass were collected from the literature [<xref ref-type="bibr" rid="CR55">55</xref>&#x02013;<xref ref-type="bibr" rid="CR57">57</xref>]. The lncRNA sequences of three related species were downloaded from the GREENC database [<xref ref-type="bibr" rid="CR69">69</xref>, <xref ref-type="bibr" rid="CR70">70</xref>] and the PNRD database [<xref ref-type="bibr" rid="CR71">71</xref>, <xref ref-type="bibr" rid="CR72">72</xref>], respectively. The Gene Ontology (GO) annotation for the three related species (sorghum v3.1.1, foxtail millet v2.2, maize 5b+) were downloaded from PlantTFDB 4.0 [<xref ref-type="bibr" rid="CR54">54</xref>, <xref ref-type="bibr" rid="CR73">73</xref>], since which has latest gene annotation. The sequences of 922, 987 and 705 CW-related proteins of Arabidopsis, rice, and maize were downloaded from Purdue Cell-Wall-Genomics Database [<xref ref-type="bibr" rid="CR45">45</xref>, <xref ref-type="bibr" rid="CR74">74</xref>], respectively.</p></sec><sec id="Sec22"><title>A computational pipeline for identification of consensus FL transcripts</title><p id="Par79">The sequenced PacBio ROIs were selected using ToFu (version 2.3.0) [<xref ref-type="bibr" rid="CR22">22</xref>] with the following parameters: minimum full pass&#x02009;&#x0003e;&#x02009;0, minimum length&#x02009;&#x0003e;&#x02009;300 and prediction accuracy&#x02009;&#x0003e;&#x02009;75%. Then, the ROIs were classified into circular consensus sequences (CCS) and non-CCS subreads by ToFu based on the presence of sequencing adapters or not. The CCS subreads were deemed to be FL transcripts if they each have both the primer sequences (the 5&#x02032; and 3&#x02032; sequences) and the polyA tail signal. It is noteworthy that the mean number of sequencing passes of the FL transcripts for each PacBio library ranges from 5 to 25 (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S19). Then, a clustering algorithm, ICE, was applied to the all FL transcripts to get consensus transcripts, which groups them into clusters based on the sequence similarity and generate a consensus sequence for each cluster. Then Quiver was used to polish the consensus transcripts to give rise to the high-quality FL transcripts with <inline-formula id="IEq9"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \ge $$\end{document}</tex-math><mml:math id="M18"><mml:mo>&#x02265;</mml:mo></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq9.gif"/></alternatives></inline-formula> 99% post-correction accuracy.</p></sec><sec id="Sec23"><title>A statistical model for estimation of the mismatch error rate</title><p id="Par80">We have designed a statistical model to check if a given transcript is correctly mapped to its DNA sequence in the genome, based on the known mismatch rate in the raw PacBio sequences. We have the following assumptions in the model: (i) mismatch errors are independent of each other [<xref ref-type="bibr" rid="CR75">75</xref>]; and (ii) the probability <italic>p</italic> in having a sequencing error in any position is a fixed value between 0.0072 and 0.055 in our study, based on the error-rate distribution observed for size-fractional libraries (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S8a). The following model is used to calculate the probability (<italic>p</italic> value) that the number of errors in a sequence of <italic>L</italic> bps is at least <italic>K</italic>:<disp-formula id="Equa"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ 1 - \varPhi \left( {\frac{K - Lp}{Lp(1 - p)}} \right), $$\end{document}</tex-math><mml:math id="M20" display="block"><mml:mrow><mml:mn>1</mml:mn><mml:mo>-</mml:mo><mml:mi>&#x003a6;</mml:mi><mml:mfenced close=")" open="(" separators=""><mml:mfrac><mml:mrow><mml:mi>K</mml:mi><mml:mo>-</mml:mo><mml:mi>L</mml:mi><mml:mi>p</mml:mi></mml:mrow><mml:mrow><mml:mi>L</mml:mi><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mn>1</mml:mn><mml:mo>-</mml:mo><mml:mi>p</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfrac></mml:mfenced><mml:mo>,</mml:mo></mml:mrow></mml:math><graphic xlink:href="13068_2018_1167_Article_Equa.gif" position="anchor"/></alternatives></disp-formula>where <inline-formula id="IEq101"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \varPhi(t)$$\end{document}</tex-math><mml:math id="M22"><mml:mrow><mml:mi>&#x003a6;</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>t</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq101.gif"/></alternatives></inline-formula> is the standard Gaussian distribution and <italic>K</italic> is a user-specified positive value. Based on our calculation, the probability (<italic>p</italic> value) that <italic>K</italic> is larger than <inline-formula id="IEq10"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ L(p + 0.03) $$\end{document}</tex-math><mml:math id="M24"><mml:mrow><mml:mi>L</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>p</mml:mi><mml:mo>+</mml:mo><mml:mn>0.03</mml:mn><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq10.gif"/></alternatives></inline-formula> was small enough to be regarded as mis-alignment (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S20).</p><p id="Par81">We have assessed the performance of the model using Sanger-based RNA sequences collected on switchgrass tissues (see Data) as follows. We first mapped the Sanger-based RNA sequences to the genome of Pvir_v3 using GMAP (version 2015-1-20) [<xref ref-type="bibr" rid="CR25">25</xref>] (parameters: -f samse -t 30 -B 5 &#x02013;sam-use-0M), which are known to have 99.999% per-base accuracy [<xref ref-type="bibr" rid="CR76">76</xref>]. Hence, such mapping results are considered as correct mapping (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S21). Then for each of the nine chromosomes, we mapped the Sanger sequences of the other eight chromosomes to this chromosome by our model, which gives rise to <italic>false positive</italic> rate at 17.3% across all nine chromosomes (Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Table S5). Similarly, we mapped the Sanger sequences to their correct chromosomes, and we found that the <italic>false negative</italic> rate at 4.74%.</p></sec><sec id="Sec24"><title>A computational pipeline for processing non-FL transcripts</title><p id="Par82">The best alignment for each non-FL transcript against genomic DNA of Pvir_v3, determined by GMAP (2015-1-20) [<xref ref-type="bibr" rid="CR25">25</xref>] with the following parameters: -f samse -t 30 -B 5 &#x02013;sam-use-0M, was kept for further analyses. A transcript is filtered out if it was mapped to a homologous region rather than its correct genomic location, assessed using our error-rate estimation model. A process is then applied to remove low-quantity PacBio transcripts, including those each consisting of &#x02018;N&#x02019;s, ambiguous junction sites, or&#x02009;&#x0003e;&#x02009;10% of sequencing errors (indel or mismatch). Then, the TAPIS (version 1.2.1) pipeline [<xref ref-type="bibr" rid="CR77">77</xref>] with the default parameters is applied to the remaining transcripts for error correction based on the aligned genomic sequence. Furthermore, a SVM-based model is trained and used to filter out transcripts containing false splice junctions in the Pvir_v3 genome using &#x0201c;build_classifiers.py&#x0201d; in SpliceGrapher (version 0.25) [<xref ref-type="bibr" rid="CR35">35</xref>] with the following parameters: -d gt, gc -n 5000. It is noteworthy that the ROC scores for splice site consensus&#x02014;AG, GC, and GT&#x02014;are 0.94, 0.93, and 0.95, respectively, based on our prediction assessment of splice junctions in Pvir_v3, where 1.0 is the highest possible score for such a prediction.</p></sec><sec id="Sec25"><title>De novo identification of unique transcripts and annotation comparisons using PASA</title><p id="Par83">The high-quality transcripts, FL or non-FL, are fed into the PASA (version 2.0.2) pipeline [<xref ref-type="bibr" rid="CR36">36</xref>] with the following parameters: MIN_PERCENT_ALIGNED&#x02009;=&#x02009;85, MIN_AVG_PER_ID&#x02009;=&#x02009;90, and &#x02013;ALIGNERS gmap, for identification of unique transcripts. Firstly, these transcripts are aligned to the genomic DNA of Pvir_v3 (4) by GMAP [<xref ref-type="bibr" rid="CR25">25</xref>], and then each valid alignment satisfying the following criteria: at least 90% sequence identity and 85% transcript length aligned; consensus splice sites at all inferred intron boundaries are used for the assembly of spliced alignments. Overall, 73% of the PASA assemblies are each the result of collapsing at least two transcripts (Additional file <xref rid="MOESM10" ref-type="media">10</xref>: Figure S22), indicating that each splice junction was not detected by accident. Each assembly that contains at least one FL transcript is termed FL-assembly, otherwise, non-FL-assembly.</p><p id="Par84">The annotation comparison module in PASA was applied to conduct transcript-level comparison. Gene models from Pvir_v3 (4) are loaded as the original annotation on the first cycle of annotation comparison. PASA-assembled transcripts were then used as input transcripts. Three cycles (locus difference coverage saturation analysis at Additional file <xref rid="MOESM11" ref-type="media">11</xref>: Table S6) of transcript loading, annotation comparison were conducted to maximize the integration of the information about transcript alignments into the prediction of gene structures.</p></sec><sec id="Sec26"><title>Illumina data analysis</title><p id="Par85">Tophat (version 2.1.1) [<xref ref-type="bibr" rid="CR38">38</xref>] was used, with default parameters, to map Illumina reads from ten tissue types onto the Pvir_v3 genome. The mapped short reads were assembled into transcripts using StringTie (version 1.0.4) [<xref ref-type="bibr" rid="CR44">44</xref>] with the default parameters and the identified PacBio transcript as the template. The FPKM values were used to quantify each assembled transcript. A transcript was regarded as successfully assembled if its exon structure was the same as matching PacBio transcript and FPKM value <inline-formula id="IEq11"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \ge $$\end{document}</tex-math><mml:math id="M26"><mml:mo>&#x02265;</mml:mo></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq11.gif"/></alternatives></inline-formula> 0.01.</p></sec><sec id="Sec27"><title>Identification of AS events</title><p id="Par86">Illumina reads were used as validation for each predicted AS event using Miso (version 0.5.4) [<xref ref-type="bibr" rid="CR43">43</xref>] with the following options: &#x02013;overhang-len 8 &#x02013;read-len 150, which provides information about which Illumina reads are included and which are excluded to be consistent with each predicted AS, referred to as the <italic>inclusio</italic>n and <italic>exclusion</italic> reads for the AS event. The following criteria are used to determine if an AS event is supported by the Illumina reads: #inclusion reads <inline-formula id="IEq12"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \ge $$\end{document}</tex-math><mml:math id="M28"><mml:mo>&#x02265;</mml:mo></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq12.gif"/></alternatives></inline-formula> 1, #exclusion reads <inline-formula id="IEq13"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \ge $$\end{document}</tex-math><mml:math id="M30"><mml:mo>&#x02265;</mml:mo></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq13.gif"/></alternatives></inline-formula> 1, and #inclusion reads&#x02009;+&#x02009;exclusion reads <inline-formula id="IEq14"><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \ge $$\end{document}</tex-math><mml:math id="M32"><mml:mo>&#x02265;</mml:mo></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq14.gif"/></alternatives></inline-formula> 10.</p><p id="Par87">In addition, Sanger-based RNA reads, mapped by GMAP [<xref ref-type="bibr" rid="CR25">25</xref>] against Pvir_v3, were also used to validate the predicted AS events. Each IR event whose intron region covered by one exon of a Sanger transcript or each ES event whose exon region covered by one intron of a Sanger transcript is regarded as validated by Sanger data.</p></sec><sec id="Sec28"><title>Prediction of fusion transcripts</title><p id="Par88">A transcript is considered a <italic>fusion</italic> transcript if the following criteria were met: (1) the transcript is mapped to two or more distinct protein-encoding loci in the genome; (2) each such locus aligns with at least 5% of the relevant transcripts; (3) the combined alignment coverage across all the matched loci should be at least 85% of the transcript; and (4) two mapped loci are at least 100&#x000a0;kbp apart (value used to detect fusion transcripts in maize PacBio data analysis [<xref ref-type="bibr" rid="CR27">27</xref>]).</p><p id="Par89">Predicted fusion transcripts are validated against Illumina short reads. Specifically, a fusion transcript is considered as validated if for each pair of predicted fusion regions there are paired Illumina reads that are mapped to the corresponding two regions, determined by Tophat2 [<xref ref-type="bibr" rid="CR38">38</xref>].</p></sec><sec id="Sec29"><title>Functional prediction of PacBio transcripts</title><p id="Par90">We have predicted the function of each selected PacBio transcript as follows: (i) blastn was used to bi-directionally map the transcript to the transcript sequence of the three related species; and (ii) functional prediction of each mapped PacBio transcript using the function of its mapped genes; specifically, only bi-directional best hits with <italic>e</italic> value&#x02009;&#x0003c;&#x02009;1 <inline-formula id="IEq15"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\text{E}}^{ - 10} $$\end{document}</tex-math><mml:math id="M34"><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:msup></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq15.gif"/></alternatives></inline-formula> and b-score&#x02009;&#x0003e;&#x02009;90% were considered as having the same GO function. The R package topGO [<xref ref-type="bibr" rid="CR78">78</xref>] was used to do GO-based function prediction.</p></sec><sec id="Sec30"><title>Identification of lncRNA from PacBio transcripts</title><p id="Par91">Transcripts with CPC score&#x02009;&#x0003c;&#x02009;-1 (predicted by CPC [<xref ref-type="bibr" rid="CR79">79</xref>], regarded as strong non-coding RNA) were predicted as lncRNAs, where CPC score for these lncRNA candidates range from &#x02212;&#x02009;1.5515 to &#x02212;&#x02009;1.00007. To check if a predicted lncRNA is species specific, we have BLASTed it against the annotated lncRNAs in the reference species (<italic>e</italic> value <inline-formula id="IEq16"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \le $$\end{document}</tex-math><mml:math id="M36"><mml:mo>&#x02264;</mml:mo></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq16.gif"/></alternatives></inline-formula> 1 <inline-formula id="IEq17"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {\text{E}}^{ - 10} $$\end{document}</tex-math><mml:math id="M38"><mml:msup><mml:mrow><mml:mtext>E</mml:mtext></mml:mrow><mml:mrow><mml:mo>-</mml:mo><mml:mn>10</mml:mn></mml:mrow></mml:msup></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq17.gif"/></alternatives></inline-formula>).</p></sec></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec31"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13068_2018_1167_MOESM1_ESM.xlsx"><caption><p><bold>Additional file 1.</bold> Genomic locations mapped by 8850 FL fusion transcripts.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13068_2018_1167_MOESM2_ESM.xlsx"><caption><p><bold>Additional file 2.</bold> Genomic locations mapped by 2853 non-FL fusion transcripts.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13068_2018_1167_MOESM3_ESM.xlsx"><caption><p><bold>Additional file 3.</bold> CW-related transcript list and functional prediction.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13068_2018_1167_MOESM4_ESM.xlsx"><caption><p><bold>Additional file 4.</bold> Genomic coordinates of the predicted lncRNA transcripts.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13068_2018_1167_MOESM5_ESM.xlsx"><caption><p><bold>Additional file 5.</bold> The TF list and matching families.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="13068_2018_1167_MOESM6_ESM.xlsx"><caption><p><bold>Additional file 6.</bold> Mapping of the genomic coordinates between the same genes in Pvir_3 and v4.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="13068_2018_1167_MOESM7_ESM.xlsx"><caption><p><bold>Additional file 7.</bold> Mapping between the corresponding transcripts in Pvir_3 and v4.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="13068_2018_1167_MOESM8_ESM.gff3"><caption><p><bold>Additional file 8.</bold> The coordinates of 105,419 assembled transcripts in Pvir_v3.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="13068_2018_1167_MOESM9_ESM.gff3"><caption><p><bold>Additional file 9.</bold> The coordinates of 105,405 assembled transcripts in Pvir_v4.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM10"><media xlink:href="13068_2018_1167_MOESM10_ESM.docx"><caption><p><bold>Additional file 10: Figure S1.</bold> The distribution of the sequence lengths of ROI for each size-fractional library. <bold>Figure S2.</bold> The workflow of the Iso-seq Tofu pipeline. <bold>Figure S3.</bold> The workflow of the transcriptome analysis and assembly pipeline for PacBio transcripts. <bold>Figure S4.</bold> An illustrative example of fusion transcripts. <bold>Figure S5.</bold> A Venn diagram of our predicted 8850 fusion transcripts showing homology to proteins of the three related species. <bold>Figure S6.</bold> A Venn diagram of 1296 HQ FL transcripts showing homology to proteins of the three related species. <bold>Figure S7.</bold> Length distribution of FL and non-FL transcripts in each library. <bold>Figure S8.</bold> The base-pair mismatch rate of non-FL PacBio sequences in each size-fractional library. <bold>Figure S9.</bold> Validation of PacBio transcripts by Illumina-based RNA-seq reads. <bold>Figure S10.</bold> Illustrative examples of nine groups of PacBio transcripts. <bold>Figure S11.</bold> Validation of AS events by Illumina reads. <bold>Figure S12.</bold> Quantification of PacBio transcripts. <bold>Figure S13.</bold> Thresholds for selection of two parameters: the <italic>e</italic> value and the proportion of best-hit bit-score. <bold>Figure S14.</bold> GO-based pathway enrichment analysis of our predicted CW transcripts. <bold>Figure S15.</bold> Characterization of our predicted lncRNAs. <bold>Figure S16.</bold> 54 TF families identified from FL and non-FL transcripts. <bold>Figure S17.</bold> The distribution of the ratio between sequence lengths of 1174 transcripts in Pvir_v3 vs. Pvir_v4. <bold>Figure S18.</bold> Size-dependent cDNA libraries of Switchgrass. <bold>Figure S19.</bold> Number of sequencing pass for FL transcripts in each library. <bold>Figure S20.</bold> The probability (<italic>p</italic> value) that the number of errors (<italic>K</italic>) in a sequence of length <italic>L</italic> is larger than (<inline-formula id="IEq18"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ L(p + 3{\text{\% }}) $$\end{document}</tex-math><mml:math id="M40"><mml:mrow><mml:mi>L</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>p</mml:mi><mml:mo>+</mml:mo><mml:mn>3</mml:mn><mml:mrow><mml:mtext>\%</mml:mtext><mml:mspace width="0.333333em"/></mml:mrow><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13068_2018_1167_Article_IEq18.gif"/></alternatives></inline-formula>). <bold>Figure S21.</bold> The distribution of base-pair mismatch rate of Sanger sequences. <bold>Figure S22.</bold> The bar plot of the number of transcripts collapsed by each PASA assembly.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM11"><media xlink:href="13068_2018_1167_MOESM11_ESM.docx"><caption><p><bold>Additional file 11: Table S1.</bold> A summary of six classifications based on the presence of 5&#x02032; primer, 3&#x02032; primer and polyA signal in each non-FL transcripts of different size-fractional library sets. <bold>Table S2.</bold> A summary of the functional classification of the 4004 hits based on the NCBI definition. <bold>Table S3.</bold> A summary of 66 samples of ten tissue types across different developmental stages sequenced by Illumina sequencer. <bold>Table S4.</bold> A summary of Spearman correlation coefficient between biological replicates for each of the ten tissue types. <bold>Table S5.</bold> A summary of the false positively aligned Sanger sequences to the genome based on our model. <bold>Table S6.</bold> A summary of the difference in genes annotated in Pvir_v3 and Pvir_v4 by PacBio transcripts after each comparison cycle.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>PacBio</term><def><p id="Par4">Pacific Biosciences</p></def></def-item><def-item><term>AS</term><def><p id="Par5">alternatively spliced</p></def></def-item><def-item><term>FL</term><def><p id="Par6">full length</p></def></def-item><def-item><term>Pvir_v3</term><def><p id="Par7">switchgrass genome version 3</p></def></def-item><def-item><term>Pvir_v4</term><def><p id="Par8">switchgrass genome version 4</p></def></def-item><def-item><term>CW</term><def><p id="Par9">cell wall</p></def></def-item><def-item><term>ROI</term><def><p id="Par10">reads of insert</p></def></def-item><def-item><term>IR</term><def><p id="Par11">intron retention</p></def></def-item><def-item><term>A3SS</term><def><p id="Par12">alternative 3&#x02032; splice site</p></def></def-item><def-item><term>A5SS</term><def><p id="Par13">alternative 5&#x02032; splice site</p></def></def-item><def-item><term>ES</term><def><p id="Par14">exon skipping</p></def></def-item><def-item><term>CPC</term><def><p id="Par15">coding potential calculator</p></def></def-item><def-item><term>TF</term><def><p id="Par16">transcription factor</p></def></def-item><def-item><term>GO</term><def><p id="Par17">Gene Ontology</p></def></def-item><def-item><term>CCS</term><def><p id="Par18">circular consensus sequences</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Electronic supplementary material</bold></p><p>The online version of this article (10.1186/s13068-018-1167-z) contains supplementary material, which is available to authorized users.</p></fn></fn-group><ack><title>Authors&#x02019; contributions</title><p>CZ designed and performed all the transcriptomic analysis work, and drafted the manuscript. MB provided all the analysis result of the iso-seq pipeline. AS performed blast analysis work for transcripts that are not map-able to known genes. IT, GL, and MU conducted cultivation, sample collection, and extraction of the cDNA libraries. RK, GK, MW, DD, and KB constructed the PacBio library and Illumina library. JS reviewed and edited the paper and assisted in data interpretation. YT provided all the transcriptomic data, co-designed the project, and revised the manuscript. YX conceived the study, participated in its design and coordination, and revised the manuscript. All authors read and approved the final manuscript.</p><sec id="FPar1"><title>Acknowledgements</title><p id="Par92">The authors thank the GACRC group of the University of Georgia for providing computing resources. We thank the JGI for pre-publication access to the genome of <italic>Panicum virgatum</italic>. We also thank Dr. Victor Olman for advices regarding the development of the error-rate estimation model.</p></sec><sec id="FPar2"><title>Competing interests</title><p id="Par93">The authors declare that they have no competing interests.</p></sec><sec id="FPar3"><title>Availability of data and materials</title><p id="Par94">All PacBio FL, non-FL, and assembled transcript data used in this manuscript have been submitted to European Nucleotide Archive at EMBL database with Accession Number: PRJEB25632. New Illumina data in this manuscript have been submitted to SRA database with Accession Number: PRJNA265584.</p></sec><sec id="FPar4"><title>Consent for publication</title><p id="Par95">Not applicable.</p></sec><sec id="FPar5"><title>Ethics approval and consent to participate</title><p id="Par96">Not applicable.</p></sec><sec id="FPar6"><title>Funding</title><p id="Par97">The authors thank BESC for funding support for the presented study here. The work conducted by the US Department of Energy Joint Genome Institute is supported by the Office of Science of the US Department of Energy under Contract No. DE-AC02-05CH11231. The BioEnergy Center [<xref ref-type="bibr" rid="CR80">80</xref>] is a US Department Research Center supported by the office of Biological and Environmental Research in the DOE Office of Science.</p></sec><sec id="FPar7"><title>Publisher&#x02019;s Note</title><p id="Par98">Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Parrish</surname><given-names>DJ</given-names></name><name><surname>Fike</surname><given-names>JH</given-names></name></person-group><article-title>Selecting, establishing, and managing switchgrass (<italic>Panicum virgatum</italic>) for Biofuels</article-title><source>Biofuels Methods Protoc.</source><year>2009</year><volume>581</volume><fpage>27</fpage><lpage>40</lpage></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Keshwani</surname><given-names>DR</given-names></name><name><surname>Cheng</surname><given-names>JJ</given-names></name></person-group><article-title>Switchgrass for bioethanol and other value-added applications: a review</article-title><source>Bioresour Technol</source><year>2009</year><volume>100</volume><issue>4</issue><fpage>1515</fpage><lpage>1523</lpage><pub-id pub-id-type="doi">10.1016/j.biortech.2008.09.035</pub-id><pub-id pub-id-type="pmid">18976902</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bouton</surname><given-names>JH</given-names></name></person-group><article-title>Molecular breeding of switchgrass for use as a biofuel crop</article-title><source>Curr Opin Genet Dev</source><year>2007</year><volume>17</volume><issue>6</issue><fpage>553</fpage><lpage>558</lpage><pub-id pub-id-type="doi">10.1016/j.gde.2007.08.012</pub-id><pub-id pub-id-type="pmid">17933511</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mclaughlin</surname><given-names>SB</given-names></name><name><surname>Kszos</surname><given-names>LA</given-names></name></person-group><article-title>Development of switchgrass (<italic>Panicum virgatum</italic>) as a bioenergy feedstock in the United States</article-title><source>Biomass Bioenergy</source><year>2005</year><volume>28</volume><issue>6</issue><fpage>515</fpage><lpage>535</lpage><pub-id pub-id-type="doi">10.1016/j.biombioe.2004.05.006</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mitchell</surname><given-names>R</given-names></name><name><surname>Vogel</surname><given-names>KP</given-names></name><name><surname>Uden</surname><given-names>DR</given-names></name></person-group><article-title>The feasibility of switchgrass for biofuel production</article-title><source>Biofuels.</source><year>2012</year><volume>3</volume><issue>1</issue><fpage>47</fpage><lpage>59</lpage><pub-id pub-id-type="doi">10.4155/bfs.11.153</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mitchell</surname><given-names>R</given-names></name><name><surname>Vogel</surname><given-names>KP</given-names></name><name><surname>Sarath</surname><given-names>G</given-names></name></person-group><article-title>Managing and enhancing switchgrass as a bioenergy feedstock</article-title><source>Biofuels Bioprod Biorefining</source><year>2008</year><volume>2</volume><issue>6</issue><fpage>530</fpage><lpage>539</lpage><pub-id pub-id-type="doi">10.1002/bbb.106</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Himmel</surname><given-names>ME</given-names></name><name><surname>Ding</surname><given-names>SY</given-names></name><name><surname>Johnson</surname><given-names>DK</given-names></name><name><surname>Adney</surname><given-names>WS</given-names></name><name><surname>Nimlos</surname><given-names>MR</given-names></name><name><surname>Brady</surname><given-names>JW</given-names></name><etal/></person-group><article-title>Biomass recalcitrance: engineering plants and enzymes for biofuels production</article-title><source>Science</source><year>2007</year><volume>315</volume><issue>5813</issue><fpage>804</fpage><lpage>807</lpage><pub-id pub-id-type="doi">10.1126/science.1137016</pub-id><pub-id pub-id-type="pmid">17289988</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lynd</surname><given-names>LR</given-names></name><name><surname>Laser</surname><given-names>MS</given-names></name><name><surname>Brandsby</surname><given-names>D</given-names></name><name><surname>Dale</surname><given-names>BE</given-names></name><name><surname>Davison</surname><given-names>B</given-names></name><name><surname>Hamilton</surname><given-names>R</given-names></name><etal/></person-group><article-title>How biotech can transform biofuels</article-title><source>Nat Biotechnol</source><year>2008</year><volume>26</volume><issue>2</issue><fpage>169</fpage><lpage>172</lpage><pub-id pub-id-type="doi">10.1038/nbt0208-169</pub-id><pub-id pub-id-type="pmid">18259168</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Demain</surname><given-names>AL</given-names></name></person-group><article-title>Biosolutions to the energy problem</article-title><source>J Ind Microbiol Biotechnol</source><year>2009</year><volume>36</volume><issue>3</issue><fpage>319</fpage><lpage>332</lpage><pub-id pub-id-type="doi">10.1007/s10295-008-0521-8</pub-id><pub-id pub-id-type="pmid">19137336</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Casler</surname><given-names>MD</given-names></name><name><surname>Tobias</surname><given-names>CM</given-names></name><name><surname>Kaeppler</surname><given-names>SM</given-names></name><name><surname>Buell</surname><given-names>CR</given-names></name><name><surname>Wang</surname><given-names>ZY</given-names></name><name><surname>Cao</surname><given-names>PJ</given-names></name><etal/></person-group><article-title>The switchgrass genome: tools and strategies</article-title><source>Plant Genome.</source><year>2011</year><volume>4</volume><issue>3</issue><fpage>273</fpage><lpage>282</lpage><pub-id pub-id-type="doi">10.3835/plantgenome2011.10.0026</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nageswara-Rao</surname><given-names>M</given-names></name><name><surname>Soneji</surname><given-names>JR</given-names></name><name><surname>Kwit</surname><given-names>C</given-names></name><name><surname>Stewart</surname><given-names>CN</given-names></name></person-group><article-title>Advances in biotechnology and genomics of switchgrass</article-title><source>Biotechnol Biofuels.</source><year>2013</year><volume>6</volume><fpage>77</fpage><pub-id pub-id-type="doi">10.1186/1754-6834-6-77</pub-id><pub-id pub-id-type="pmid">23663491</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><mixed-citation publication-type="other">Doe J. <italic>Panicum virgatum</italic> v4.1 (Switchgrass). 2017. <ext-link ext-link-type="uri" xlink:href="https://phytozome.jgi.doe.gov/pz/portal.html%23!info%3falias%3dOrg_Pvirgatum_er">https://phytozome.jgi.doe.gov/pz/portal.html#!info?alias=Org_Pvirgatum_er</ext-link>. Accessed 29 Dec 2017.</mixed-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ekblom</surname><given-names>R</given-names></name><name><surname>Wolf</surname><given-names>JBW</given-names></name></person-group><article-title>A field guide to whole-genome sequencing, assembly and annotation</article-title><source>Evol Appl</source><year>2014</year><volume>7</volume><issue>9</issue><fpage>1026</fpage><lpage>1042</lpage><pub-id pub-id-type="doi">10.1111/eva.12178</pub-id><pub-id pub-id-type="pmid">25553065</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Marquez</surname><given-names>Y</given-names></name><name><surname>Brown</surname><given-names>JWS</given-names></name><name><surname>Simpson</surname><given-names>C</given-names></name><name><surname>Barta</surname><given-names>A</given-names></name><name><surname>Kalyna</surname><given-names>M</given-names></name></person-group><article-title>Transcriptome survey reveals increased complexity of the alternative splicing landscape in <italic>Arabidopsis</italic></article-title><source>Genome Res</source><year>2012</year><volume>22</volume><issue>6</issue><fpage>1184</fpage><lpage>1195</lpage><pub-id pub-id-type="doi">10.1101/gr.134106.111</pub-id><pub-id pub-id-type="pmid">22391557</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Steijger</surname><given-names>T</given-names></name><name><surname>Abril</surname><given-names>JF</given-names></name><name><surname>Engstrom</surname><given-names>PG</given-names></name><name><surname>Kokocinski</surname><given-names>F</given-names></name><name><surname>Hubbard</surname><given-names>TJ</given-names></name><name><surname>Guigo</surname><given-names>R</given-names></name><etal/></person-group><article-title>Assessment of transcript reconstruction methods for RNA-seq</article-title><source>Nat Methods.</source><year>2013</year><volume>10</volume><issue>12</issue><fpage>1177</fpage><pub-id pub-id-type="doi">10.1038/nmeth.2714</pub-id><pub-id pub-id-type="pmid">24185837</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schadt</surname><given-names>EE</given-names></name><name><surname>Turner</surname><given-names>S</given-names></name><name><surname>Kasarskis</surname><given-names>A</given-names></name></person-group><article-title>A window into third-generation sequencing</article-title><source>Hum Mol Genet</source><year>2010</year><volume>19</volume><fpage>R227</fpage><lpage>R240</lpage><pub-id pub-id-type="doi">10.1093/hmg/ddq416</pub-id><pub-id pub-id-type="pmid">20858600</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Au</surname><given-names>KF</given-names></name><name><surname>Sebastiano</surname><given-names>V</given-names></name><name><surname>Afshar</surname><given-names>PT</given-names></name><name><surname>Durruthy</surname><given-names>JD</given-names></name><name><surname>Lee</surname><given-names>L</given-names></name><name><surname>Williams</surname><given-names>BA</given-names></name><etal/></person-group><article-title>Characterization of the human ESC transcriptome by hybrid sequencing</article-title><source>Proc Natl Acad Sci USA</source><year>2013</year><volume>110</volume><issue>50</issue><fpage>E4821</fpage><lpage>E4830</lpage><pub-id pub-id-type="doi">10.1073/pnas.1320101110</pub-id><pub-id pub-id-type="pmid">24282307</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sharon</surname><given-names>D</given-names></name><name><surname>Tilgner</surname><given-names>H</given-names></name><name><surname>Grubert</surname><given-names>F</given-names></name><name><surname>Snyder</surname><given-names>M</given-names></name></person-group><article-title>A single-molecule long-read survey of the human transcriptome</article-title><source>Nat Biotechnol.</source><year>2013</year><volume>31</volume><issue>11</issue><fpage>1009</fpage><pub-id pub-id-type="doi">10.1038/nbt.2705</pub-id><pub-id pub-id-type="pmid">24108091</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><mixed-citation publication-type="other">Korlach J. Understanding accuracy in SMRT<sup>&#x000ae;</sup> sequencing. 2013. <ext-link ext-link-type="uri" xlink:href="https://www.pacb.com/wp-content/uploads/2015/09/Perspective_UnderstandingAccuracySMRTSequencing.pdf">https://www.pacb.com/wp-content/uploads/2015/09/Perspective_UnderstandingAccuracySMRTSequencing.pdf</ext-link>. Accessed 25 May 2017.</mixed-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tilgner</surname><given-names>H</given-names></name><name><surname>Grubert</surname><given-names>F</given-names></name><name><surname>Sharon</surname><given-names>D</given-names></name><name><surname>Snyder</surname><given-names>MP</given-names></name></person-group><article-title>Defining a personal, allele-specific, and single-molecule long-read transcriptome</article-title><source>Proc Natl Acad Sci USA</source><year>2014</year><volume>111</volume><fpage>9869</fpage><lpage>9874</lpage><pub-id pub-id-type="doi">10.1073/pnas.1400447111</pub-id><pub-id pub-id-type="pmid">24961374</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Conesa</surname><given-names>A</given-names></name><name><surname>Madrigal</surname><given-names>P</given-names></name><name><surname>Tarazona</surname><given-names>S</given-names></name><name><surname>Gomez-Cabrero</surname><given-names>D</given-names></name><name><surname>Cervera</surname><given-names>A</given-names></name><name><surname>Mcpherson</surname><given-names>A</given-names></name><etal/></person-group><article-title>A survey of best practices for RNA-seq data analysis</article-title><source>Genome Biol.</source><year>2016</year><volume>17</volume><fpage>13</fpage><pub-id pub-id-type="doi">10.1186/s13059-016-0881-8</pub-id><pub-id pub-id-type="pmid">26813401</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gordon</surname><given-names>SP</given-names></name><name><surname>Tseng</surname><given-names>E</given-names></name><name><surname>Salamov</surname><given-names>A</given-names></name><name><surname>Zhang</surname><given-names>J</given-names></name><name><surname>Meng</surname><given-names>X</given-names></name><name><surname>Zhao</surname><given-names>Z</given-names></name><etal/></person-group><article-title>Widespread polycistronic transcripts in fungi revealed by single-molecule mRNA sequencing</article-title><source>PLoS ONE</source><year>2015</year><volume>10</volume><issue>7</issue><fpage>e0132628</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0132628</pub-id><pub-id pub-id-type="pmid">26177194</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Frenkel-Morgenstern</surname><given-names>M</given-names></name><name><surname>Lacroix</surname><given-names>V</given-names></name><name><surname>Ezkurdia</surname><given-names>I</given-names></name><name><surname>Levin</surname><given-names>Y</given-names></name><name><surname>Gabashvili</surname><given-names>A</given-names></name><name><surname>Prilusky</surname><given-names>J</given-names></name><etal/></person-group><article-title>Chimeras taking shape: potential functions of proteins encoded by chimeric RNA transcripts</article-title><source>Genome Res</source><year>2012</year><volume>22</volume><issue>7</issue><fpage>1231</fpage><lpage>1242</lpage><pub-id pub-id-type="doi">10.1101/gr.130062.111</pub-id><pub-id pub-id-type="pmid">22588898</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><mixed-citation publication-type="other">Pacificbiosciences. Artificial concatemers, PCR chimeras, and fusion genes. 2015. <ext-link ext-link-type="uri" xlink:href="https://github.com/PacificBiosciences/cDNA_primer/wiki/Artificial-concatemers%2c-PCR-chimeras%2c-and-fusion-genes">https://github.com/PacificBiosciences/cDNA_primer/wiki/Artificial-concatemers,-PCR-chimeras,-and-fusion-genes</ext-link>. Accessed 15 May 2017.</mixed-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>TD</given-names></name><name><surname>Watanabe</surname><given-names>CK</given-names></name></person-group><article-title>GMAP: a genomic mapping and alignment program for mRNA and EST sequences</article-title><source>Bioinformatics</source><year>2005</year><volume>21</volume><issue>9</issue><fpage>1859</fpage><lpage>1875</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bti310</pub-id><pub-id pub-id-type="pmid">15728110</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>X</given-names></name><name><surname>Mei</surname><given-names>W</given-names></name><name><surname>Soltis</surname><given-names>PS</given-names></name><name><surname>Soltis</surname><given-names>DE</given-names></name><name><surname>Barbazuk</surname><given-names>WB</given-names></name></person-group><article-title>Detecting alternatively spliced transcript isoforms from single-molecule long-read sequences without a reference genome</article-title><source>Mol Ecol Resour.</source><year>2017</year><volume>17</volume><fpage>1243</fpage><lpage>1256</lpage><pub-id pub-id-type="doi">10.1111/1755-0998.12670</pub-id><pub-id pub-id-type="pmid">28316149</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>B</given-names></name><name><surname>Tseng</surname><given-names>E</given-names></name><name><surname>Regulski</surname><given-names>M</given-names></name><name><surname>Clark</surname><given-names>TA</given-names></name><name><surname>Hon</surname><given-names>T</given-names></name><name><surname>Jiao</surname><given-names>YP</given-names></name><etal/></person-group><article-title>Unveiling the complexity of the maize transcriptome by single-molecule long-read sequencing</article-title><source>Nat Commun.</source><year>2016</year><volume>7</volume><fpage>11708</fpage><pub-id pub-id-type="doi">10.1038/ncomms11708</pub-id><pub-id pub-id-type="pmid">27339440</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Camacho</surname><given-names>C</given-names></name><name><surname>Coulouris</surname><given-names>G</given-names></name><name><surname>Avagyan</surname><given-names>V</given-names></name><name><surname>Ma</surname><given-names>N</given-names></name><name><surname>Papadopoulos</surname><given-names>J</given-names></name><name><surname>Bealer</surname><given-names>K</given-names></name><etal/></person-group><article-title>BLAST plus: architecture and applications</article-title><source>BMC Bioinf.</source><year>2009</year><volume>10</volume><fpage>421</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-10-421</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>O&#x02019;leary</surname><given-names>NA</given-names></name><name><surname>Wright</surname><given-names>MW</given-names></name><name><surname>Brister</surname><given-names>JR</given-names></name><name><surname>Ciufo</surname><given-names>S</given-names></name><name><surname>Haddad</surname><given-names>D</given-names></name><name><surname>Mcveigh</surname><given-names>R</given-names></name><etal/></person-group><article-title>Reference sequence (RefSeq) database at NCBI: current status, taxonomic expansion, and functional annotation</article-title><source>Nucleic Acids Res.</source><year>2015</year><volume>44</volume><issue>D1</issue><fpage>D733</fpage><lpage>D745</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1189</pub-id><pub-id pub-id-type="pmid">26553804</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hagel</surname><given-names>JM</given-names></name><name><surname>Facchini</surname><given-names>PJ</given-names></name></person-group><article-title>Tying the knot: occurrence and possible significance of gene fusions in plant metabolism and beyond</article-title><source>J Exp Bot</source><year>2017</year><volume>68</volume><issue>15</issue><fpage>4029</fpage><lpage>4043</lpage><pub-id pub-id-type="doi">10.1093/jxb/erx152</pub-id><pub-id pub-id-type="pmid">28521055</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Meheust</surname><given-names>R</given-names></name><name><surname>Zelzion</surname><given-names>E</given-names></name><name><surname>Bhattacharya</surname><given-names>D</given-names></name><name><surname>Lopez</surname><given-names>P</given-names></name><name><surname>Bapteste</surname><given-names>E</given-names></name></person-group><article-title>Protein networks identify novel symbiogenetic genes resulting from plastid endosymbiosis</article-title><source>Proc Natl Acad Sci USA</source><year>2016</year><volume>113</volume><issue>13</issue><fpage>3579</fpage><lpage>3584</lpage><pub-id pub-id-type="doi">10.1073/pnas.1517551113</pub-id><pub-id pub-id-type="pmid">26976593</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nutzmann</surname><given-names>HW</given-names></name><name><surname>Osbourn</surname><given-names>A</given-names></name></person-group><article-title>Gene clustering in plant specialized metabolism</article-title><source>Curr Opin Biotechnol</source><year>2014</year><volume>26</volume><fpage>91</fpage><lpage>99</lpage><pub-id pub-id-type="doi">10.1016/j.copbio.2013.10.009</pub-id><pub-id pub-id-type="pmid">24679264</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Boycheva</surname><given-names>S</given-names></name><name><surname>Daviet</surname><given-names>L</given-names></name><name><surname>Wolfender</surname><given-names>JL</given-names></name><name><surname>Fitzpatrick</surname><given-names>TB</given-names></name></person-group><article-title>The rise of operon-like gene clusters in plants</article-title><source>Trends Plant Sci</source><year>2014</year><volume>19</volume><issue>7</issue><fpage>447</fpage><lpage>459</lpage><pub-id pub-id-type="doi">10.1016/j.tplants.2014.01.013</pub-id><pub-id pub-id-type="pmid">24582794</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abdel-Ghany</surname><given-names>SE</given-names></name><name><surname>Hamilton</surname><given-names>M</given-names></name><name><surname>Jacobi</surname><given-names>JL</given-names></name><name><surname>Ngam</surname><given-names>P</given-names></name><name><surname>Devitt</surname><given-names>N</given-names></name><name><surname>Schilkey</surname><given-names>F</given-names></name><etal/></person-group><article-title>A survey of the sorghum transcriptome using single-molecule long reads</article-title><source>Nat Commun.</source><year>2016</year><volume>7</volume><fpage>11706</fpage><pub-id pub-id-type="doi">10.1038/ncomms11706</pub-id><pub-id pub-id-type="pmid">27339290</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rogers</surname><given-names>MF</given-names></name><name><surname>Thomas</surname><given-names>J</given-names></name><name><surname>Reddy</surname><given-names>ASN</given-names></name><name><surname>Ben-Hur</surname><given-names>A</given-names></name></person-group><article-title>SpliceGrapher: detecting patterns of alternative splicing from RNA-Seq data in the context of gene models and EST data</article-title><source>Genome Biol</source><year>2012</year><volume>13</volume><issue>1</issue><fpage>R4</fpage><pub-id pub-id-type="doi">10.1186/gb-2012-13-1-r4</pub-id><pub-id pub-id-type="pmid">22293517</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Haas</surname><given-names>BJ</given-names></name><name><surname>Delcher</surname><given-names>AL</given-names></name><name><surname>Mount</surname><given-names>SM</given-names></name><name><surname>Wortman</surname><given-names>JR</given-names></name><name><surname>Smith</surname><given-names>RK</given-names></name><name><surname>Hannick</surname><given-names>LI</given-names></name><etal/></person-group><article-title>Improving the Arabidopsis genome annotation using maximal transcript alignment assemblies</article-title><source>Nucleic Acids Res</source><year>2003</year><volume>31</volume><issue>19</issue><fpage>5654</fpage><lpage>5666</lpage><pub-id pub-id-type="doi">10.1093/nar/gkg770</pub-id><pub-id pub-id-type="pmid">14500829</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><mixed-citation publication-type="other">Magdoll. Cupcake ToFU: supporting scripts for Iso Seq after clustering step. 2017. <ext-link ext-link-type="uri" xlink:href="https://github.com/Magdoll/cDNA_Cupcake/wiki/Cupcake-ToFU:-supporting-scripts-for-Iso-Seq-after-clustering-step">https://github.com/Magdoll/cDNA_Cupcake/wiki/Cupcake-ToFU:-supporting-scripts-for-Iso-Seq-after-clustering-step</ext-link>. Accessed 17 Mar 2017.</mixed-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>D</given-names></name><name><surname>Pertea</surname><given-names>G</given-names></name><name><surname>Trapnell</surname><given-names>C</given-names></name><name><surname>Pimentel</surname><given-names>H</given-names></name><name><surname>Kelley</surname><given-names>R</given-names></name><name><surname>Salzberg</surname><given-names>SL</given-names></name></person-group><article-title>TopHat2: accurate alignment of transcriptomes in the presence of insertions, deletions and gene fusions</article-title><source>Genome Biol</source><year>2013</year><volume>14</volume><issue>4</issue><fpage>R36</fpage><pub-id pub-id-type="doi">10.1186/gb-2013-14-4-r36</pub-id><pub-id pub-id-type="pmid">23618408</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Trapnell</surname><given-names>C</given-names></name><name><surname>Roberts</surname><given-names>A</given-names></name><name><surname>Goff</surname><given-names>L</given-names></name><name><surname>Pertea</surname><given-names>G</given-names></name><name><surname>Kim</surname><given-names>D</given-names></name><name><surname>Kelley</surname><given-names>DR</given-names></name><etal/></person-group><article-title>Differential gene and transcript expression analysis of RNA-seq experiments with TopHat and Cufflinks</article-title><source>Nat Protoc.</source><year>2012</year><volume>7</volume><issue>3</issue><fpage>562</fpage><lpage>578</lpage><pub-id pub-id-type="doi">10.1038/nprot.2012.016</pub-id><pub-id pub-id-type="pmid">22383036</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>RL</given-names></name><name><surname>Dickerson</surname><given-names>J</given-names></name></person-group><article-title>Strawberry: fast and accurate genome-guided transcript reconstruction and quantification from RNA-Seq</article-title><source>Plos Comput Biol.</source><year>2017</year><volume>13</volume><issue>11</issue><fpage>e1005851</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1005851</pub-id><pub-id pub-id-type="pmid">29176847</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Trapnell</surname><given-names>C</given-names></name><name><surname>Williams</surname><given-names>BA</given-names></name><name><surname>Pertea</surname><given-names>G</given-names></name><name><surname>Mortazavi</surname><given-names>A</given-names></name><name><surname>Kwan</surname><given-names>G</given-names></name><name><surname>Van Baren</surname><given-names>MJ</given-names></name><etal/></person-group><article-title>Transcript assembly and quantification by RNA-Seq reveals unannotated transcripts and isoform switching during cell differentiation</article-title><source>Nat Biotechnol.</source><year>2010</year><volume>28</volume><issue>5</issue><fpage>511</fpage><lpage>515</lpage><pub-id pub-id-type="doi">10.1038/nbt.1621</pub-id><pub-id pub-id-type="pmid">20436464</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barbazuk</surname><given-names>WB</given-names></name><name><surname>Fu</surname><given-names>Y</given-names></name><name><surname>Mcginnis</surname><given-names>KM</given-names></name></person-group><article-title>Genome-wide analyses of alternative splicing in plants: opportunities and challenges</article-title><source>Genome Res</source><year>2008</year><volume>18</volume><issue>9</issue><fpage>1381</fpage><lpage>1392</lpage><pub-id pub-id-type="doi">10.1101/gr.053678.106</pub-id><pub-id pub-id-type="pmid">18669480</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Katz</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>ET</given-names></name><name><surname>Airoldi</surname><given-names>EM</given-names></name><name><surname>Burge</surname><given-names>CB</given-names></name></person-group><article-title>Analysis and design of RNA sequencing experiments for identifying isoform regulation</article-title><source>Nat Methods</source><year>2010</year><volume>7</volume><issue>12</issue><fpage>1009</fpage><pub-id pub-id-type="doi">10.1038/nmeth.1528</pub-id><pub-id pub-id-type="pmid">21057496</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pertea</surname><given-names>M</given-names></name><name><surname>Pertea</surname><given-names>GM</given-names></name><name><surname>Antonescu</surname><given-names>CM</given-names></name><name><surname>Chang</surname><given-names>T-C</given-names></name><name><surname>Mendell</surname><given-names>JT</given-names></name><name><surname>Salzberg</surname><given-names>SL</given-names></name></person-group><article-title>StringTie enables improved reconstruction of a transcriptome from RNA-seq reads</article-title><source>Nat Biotechnol.</source><year>2015</year><volume>33</volume><issue>3</issue><fpage>290</fpage><lpage>295</lpage><pub-id pub-id-type="doi">10.1038/nbt.3122</pub-id><pub-id pub-id-type="pmid">25690850</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yong</surname><given-names>WD</given-names></name><name><surname>Link</surname><given-names>B</given-names></name><name><surname>O&#x02019;malley</surname><given-names>R</given-names></name><name><surname>Tewari</surname><given-names>J</given-names></name><name><surname>Hunter</surname><given-names>CT</given-names></name><name><surname>Lu</surname><given-names>CA</given-names></name><etal/></person-group><article-title>Genomics of plant cell wall biogenesis</article-title><source>Planta</source><year>2005</year><volume>221</volume><issue>6</issue><fpage>747</fpage><lpage>751</lpage><pub-id pub-id-type="doi">10.1007/s00425-005-1563-z</pub-id><pub-id pub-id-type="pmid">15981004</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pearson</surname><given-names>WR</given-names></name></person-group><article-title>An introduction to sequence similarity (&#x0201c;homology&#x0201d;) searching</article-title><source>Curr Protoc Bioinf.</source><year>2013</year></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ng</surname><given-names>SY</given-names></name><name><surname>Lin</surname><given-names>L</given-names></name><name><surname>Soh</surname><given-names>BS</given-names></name><name><surname>Stanton</surname><given-names>LW</given-names></name></person-group><article-title>Long noncoding RNAs in development and disease of the central nervous system</article-title><source>Trends Genet</source><year>2013</year><volume>29</volume><issue>8</issue><fpage>461</fpage><lpage>468</lpage><pub-id pub-id-type="doi">10.1016/j.tig.2013.03.002</pub-id><pub-id pub-id-type="pmid">23562612</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kong</surname><given-names>L</given-names></name><name><surname>Zhang</surname><given-names>Y</given-names></name><name><surname>Ye</surname><given-names>Z-Q</given-names></name><name><surname>Liu</surname><given-names>X-Q</given-names></name><name><surname>Zhao</surname><given-names>S-Q</given-names></name><name><surname>Wei</surname><given-names>L</given-names></name><etal/></person-group><article-title>CPC: assess the protein-coding potential of transcripts using sequence features and support vector machine</article-title><source>Nucleic Acids Res.</source><year>2007</year><volume>35</volume><issue>Web Server issue</issue><fpage>W345</fpage><lpage>W349</lpage><pub-id pub-id-type="doi">10.1093/nar/gkm391</pub-id><pub-id pub-id-type="pmid">17631615</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>YY</given-names></name><name><surname>Xue</surname><given-names>SY</given-names></name><name><surname>Liu</surname><given-names>XR</given-names></name><name><surname>Liu</surname><given-names>H</given-names></name><name><surname>Hu</surname><given-names>T</given-names></name><name><surname>Qiu</surname><given-names>XT</given-names></name><etal/></person-group><article-title>Analyses of long non-coding RNA and mRNA profiling using RNA sequencing during the pre-implantation phases in pig endometrium</article-title><source>Sci Rep.</source><year>2016</year><volume>6</volume><fpage>20238</fpage><pub-id pub-id-type="doi">10.1038/srep20238</pub-id><pub-id pub-id-type="pmid">26822553</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>J</given-names></name><name><surname>Wang</surname><given-names>H</given-names></name><name><surname>Chua</surname><given-names>NH</given-names></name></person-group><article-title>Long noncoding RNA transcriptome of plants</article-title><source>Plant Biotechnol J.</source><year>2015</year><volume>13</volume><fpage>319</fpage><lpage>328</lpage><pub-id pub-id-type="doi">10.1111/pbi.12336</pub-id><pub-id pub-id-type="pmid">25615265</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>YC</given-names></name><name><surname>Liao</surname><given-names>JY</given-names></name><name><surname>Li</surname><given-names>ZY</given-names></name><name><surname>Yu</surname><given-names>Y</given-names></name><name><surname>Zhang</surname><given-names>JP</given-names></name><name><surname>Li</surname><given-names>QF</given-names></name><etal/></person-group><article-title>Genome-wide screening and functional analysis identify a large number of long noncoding RNAs involved in the sexual reproduction of rice</article-title><source>Genome Biol.</source><year>2014</year><volume>15</volume><issue>12</issue><fpage>512</fpage><pub-id pub-id-type="doi">10.1186/s13059-014-0512-1</pub-id><pub-id pub-id-type="pmid">25517485</pub-id></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kapranov</surname><given-names>P</given-names></name><name><surname>Cheng</surname><given-names>J</given-names></name><name><surname>Dike</surname><given-names>S</given-names></name><name><surname>Nix</surname><given-names>DA</given-names></name><name><surname>Duttagupta</surname><given-names>R</given-names></name><name><surname>Willingham</surname><given-names>AT</given-names></name><etal/></person-group><article-title>RNA maps reveal new RNA classes and a possible function for pervasive transcription</article-title><source>Science</source><year>2007</year><volume>316</volume><issue>5830</issue><fpage>1484</fpage><lpage>1488</lpage><pub-id pub-id-type="doi">10.1126/science.1138341</pub-id><pub-id pub-id-type="pmid">17510325</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ravasi</surname><given-names>T</given-names></name><name><surname>Suzuki</surname><given-names>H</given-names></name><name><surname>Pang</surname><given-names>KC</given-names></name><name><surname>Katayama</surname><given-names>S</given-names></name><name><surname>Furuno</surname><given-names>M</given-names></name><name><surname>Okunishi</surname><given-names>R</given-names></name><etal/></person-group><article-title>Experimental validation of the regulated expression of large numbers of non-coding RNAs from the mouse genome</article-title><source>Genome Res</source><year>2006</year><volume>16</volume><issue>1</issue><fpage>11</fpage><lpage>19</lpage><pub-id pub-id-type="doi">10.1101/gr.4200206</pub-id><pub-id pub-id-type="pmid">16344565</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jin</surname><given-names>JP</given-names></name><name><surname>Tian</surname><given-names>F</given-names></name><name><surname>Yang</surname><given-names>DC</given-names></name><name><surname>Meng</surname><given-names>YQ</given-names></name><name><surname>Kong</surname><given-names>L</given-names></name><name><surname>Luo</surname><given-names>JC</given-names></name><etal/></person-group><article-title>PlantTFDB 4.0: toward a central hub for transcription factors and regulatory interactions in plants</article-title><source>Nucleic Acids Res</source><year>2017</year><volume>45</volume><issue>D1</issue><fpage>D1040</fpage><lpage>D1045</lpage><pub-id pub-id-type="doi">10.1093/nar/gkw982</pub-id><pub-id pub-id-type="pmid">27924042</pub-id></element-citation></ref><ref id="CR55"><label>55.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rinerson</surname><given-names>CI</given-names></name><name><surname>Scully</surname><given-names>ED</given-names></name><name><surname>Palmer</surname><given-names>NA</given-names></name><name><surname>Donze-Reiner</surname><given-names>T</given-names></name><name><surname>Rabara</surname><given-names>RC</given-names></name><name><surname>Tripathi</surname><given-names>P</given-names></name><etal/></person-group><article-title>The WRKY transcription factor family and senescence in switchgrass</article-title><source>BMC Genomics.</source><year>2015</year><volume>16</volume><fpage>912</fpage><pub-id pub-id-type="doi">10.1186/s12864-015-2057-4</pub-id><pub-id pub-id-type="pmid">26552372</pub-id></element-citation></ref><ref id="CR56"><label>56.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>Z</given-names></name><name><surname>Cao</surname><given-names>Y</given-names></name><name><surname>Yang</surname><given-names>R</given-names></name><name><surname>Qi</surname><given-names>T</given-names></name><name><surname>Hang</surname><given-names>Y</given-names></name><name><surname>Lin</surname><given-names>H</given-names></name><etal/></person-group><article-title>Switchgrass SBP-box transcription factors PvSPL1 and 2 function redundantly to initiate side tillers and affect biomass yield of energy crop</article-title><source>Biotechnol Biofuels</source><year>2016</year><volume>9</volume><issue>1</issue><fpage>101</fpage><pub-id pub-id-type="doi">10.1186/s13068-016-0516-z</pub-id><pub-id pub-id-type="pmid">27158262</pub-id></element-citation></ref><ref id="CR57"><label>57.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wuddineh</surname><given-names>WA</given-names></name><name><surname>Mazarei</surname><given-names>M</given-names></name><name><surname>Turner</surname><given-names>GB</given-names></name><name><surname>Sykes</surname><given-names>RW</given-names></name><name><surname>Decker</surname><given-names>SR</given-names></name><name><surname>Davis</surname><given-names>MF</given-names></name><etal/></person-group><article-title>Identification and molecular characterization of the switchgrass AP2/ERF transcription factor superfamily, and overexpression of PvERF001 for improvement of biomass characteristics for biofuel</article-title><source>Front Bioeng Biotechnol.</source><year>2015</year><volume>3</volume><fpage>101</fpage><pub-id pub-id-type="doi">10.3389/fbioe.2015.00101</pub-id><pub-id pub-id-type="pmid">26258121</pub-id></element-citation></ref><ref id="CR58"><label>58.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>XX</given-names></name><name><surname>Mei</surname><given-names>WB</given-names></name><name><surname>Soltis</surname><given-names>PS</given-names></name><name><surname>Soltis</surname><given-names>DE</given-names></name><name><surname>Barbazuk</surname><given-names>WB</given-names></name></person-group><article-title>Detecting alternatively spliced transcript isoforms from single-molecule long-read sequences without a reference genome</article-title><source>Mol Ecol Resour.</source><year>2017</year><volume>17</volume><issue>6</issue><fpage>1243</fpage><lpage>1256</lpage><pub-id pub-id-type="doi">10.1111/1755-0998.12670</pub-id><pub-id pub-id-type="pmid">28316149</pub-id></element-citation></ref><ref id="CR59"><label>59.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Moore</surname><given-names>KJ</given-names></name><name><surname>Moser</surname><given-names>LE</given-names></name><name><surname>Vogel</surname><given-names>KP</given-names></name><name><surname>Waller</surname><given-names>SS</given-names></name><name><surname>Johnson</surname><given-names>BE</given-names></name><name><surname>Pedersen</surname><given-names>JF</given-names></name></person-group><article-title>Describing and quantifying growth-stages of perennial forage grasses</article-title><source>Agron J</source><year>1991</year><volume>83</volume><issue>6</issue><fpage>1073</fpage><lpage>1077</lpage><pub-id pub-id-type="doi">10.2134/agronj1991.00021962008300060027x</pub-id></element-citation></ref><ref id="CR60"><label>60.</label><mixed-citation publication-type="other">Qiagen. Qiagen. 2013. <ext-link ext-link-type="uri" xlink:href="https://www.qiagen.com/us/">https://www.qiagen.com/us/</ext-link>. Accessed 20 Mar 2014.</mixed-citation></ref><ref id="CR61"><label>61.</label><mixed-citation publication-type="other">Molecular Research Center I. Molecular Research Center, Inc. 2016. <ext-link ext-link-type="uri" xlink:href="https://www.mrcgene.com/">https://www.mrcgene.com/</ext-link>. Accessed 25 Mar 2014.</mixed-citation></ref><ref id="CR62"><label>62.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Diegoli</surname><given-names>TM</given-names></name><name><surname>Farr</surname><given-names>M</given-names></name><name><surname>Cromartie</surname><given-names>C</given-names></name><name><surname>Coble</surname><given-names>MD</given-names></name><name><surname>Bille</surname><given-names>TW</given-names></name></person-group><article-title>An optimized protocol for forensic application of the PreCR&#x02122; Repair Mix to multiplex STR amplification of UV-damaged DNA</article-title><source>Forensic Sci Int Genet.</source><year>2012</year><volume>6</volume><issue>4</issue><fpage>498</fpage><lpage>503</lpage><pub-id pub-id-type="doi">10.1016/j.fsigen.2011.09.003</pub-id><pub-id pub-id-type="pmid">22001155</pub-id></element-citation></ref><ref id="CR63"><label>63.</label><mixed-citation publication-type="other">Illumina I. TruSeq<sup>&#x000ae;</sup> Stranded mRNA Sample Preparation Guide. 2013. <ext-link ext-link-type="uri" xlink:href="https://support.illumina.com/sequencing/sequencing_kits/truseq_stranded_mrna_ht_sample_prep_kit.html">https://support.illumina.com/sequencing/sequencing_kits/truseq_stranded_mrna_ht_sample_prep_kit.html</ext-link>. Accessed 20 Feb 2014.</mixed-citation></ref><ref id="CR64"><label>64.</label><mixed-citation publication-type="other">Doe J. <italic>Sorghum bicolor</italic> v3.1.1 (Cereal grass). 2017. <ext-link ext-link-type="uri" xlink:href="https://phytozome.jgi.doe.gov/pz/portal.html%23!info%3falias%3dOrg_Sbicolor">https://phytozome.jgi.doe.gov/pz/portal.html#!info?alias=Org_Sbicolor</ext-link>. Accessed 19 May 2017.</mixed-citation></ref><ref id="CR65"><label>65.</label><mixed-citation publication-type="other">Doe J. <italic>Setaria italica</italic> v2.2 (Foxtail millet). 2015. <ext-link ext-link-type="uri" xlink:href="https://phytozome.jgi.doe.gov/pz/portal.html%23!info%3falias%3dOrg_Sitalica">https://phytozome.jgi.doe.gov/pz/portal.html#!info?alias=Org_Sitalica</ext-link>. Accessed 19 May 2017.</mixed-citation></ref><ref id="CR66"><label>66.</label><mixed-citation publication-type="other">Doe J. <italic>Zea mays</italic> Ensembl-18 (Maize). 2010. <ext-link ext-link-type="uri" xlink:href="https://phytozome.jgi.doe.gov/pz/portal.html%23!info%3falias%3dOrg_Zmays">https://phytozome.jgi.doe.gov/pz/portal.html#!info?alias=Org_Zmays</ext-link>. Accessed 19 May 2017.</mixed-citation></ref><ref id="CR67"><label>67.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Clark</surname><given-names>K</given-names></name><name><surname>Karsch-Mizrachi</surname><given-names>I</given-names></name><name><surname>Lipman</surname><given-names>DJ</given-names></name><name><surname>Ostell</surname><given-names>J</given-names></name><name><surname>Sayers</surname><given-names>EW</given-names></name></person-group><article-title>GenBank</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><issue>D1</issue><fpage>D67</fpage><lpage>D72</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1276</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR68"><label>68.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>JY</given-names></name><name><surname>Lee</surname><given-names>YC</given-names></name><name><surname>Torres-Jerez</surname><given-names>I</given-names></name><name><surname>Wang</surname><given-names>MY</given-names></name><name><surname>Yin</surname><given-names>YB</given-names></name><name><surname>Chou</surname><given-names>WC</given-names></name><etal/></person-group><article-title>Development of an integrated transcript sequence database and a gene expression atlas for gene discovery and analysis in switchgrass (<italic>Panicum virgatum</italic> L.)</article-title><source>Plant J.</source><year>2013</year><volume>74</volume><issue>1</issue><fpage>160</fpage><lpage>173</lpage><pub-id pub-id-type="doi">10.1111/tpj.12104</pub-id><pub-id pub-id-type="pmid">23289674</pub-id></element-citation></ref><ref id="CR69"><label>69.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gallart</surname><given-names>AP</given-names></name><name><surname>Pulido</surname><given-names>AH</given-names></name><name><surname>De Lagran</surname><given-names>IAM</given-names></name><name><surname>Sanseverino</surname><given-names>W</given-names></name><name><surname>Cigliano</surname><given-names>RA</given-names></name></person-group><article-title>GREENC: a wiki-based database of plant lncRNAs</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><issue>D1</issue><fpage>D1161</fpage><lpage>D1166</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1215</pub-id><pub-id pub-id-type="pmid">26578586</pub-id></element-citation></ref><ref id="CR70"><label>70.</label><mixed-citation publication-type="other">Gallart AP, Pulido AH, De Lagran IaM, Sanseverino W, Cigliano RA. GreeNC a wiki-database of plant lncRNAs (v1.12). 2016. <ext-link ext-link-type="uri" xlink:href="http://greenc.sciencedesigners.com/wiki/Main_Page">http://greenc.sciencedesigners.com/wiki/Main_Page</ext-link>. Accessed 3 Aug 2017.</mixed-citation></ref><ref id="CR71"><label>71.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yi</surname><given-names>X</given-names></name><name><surname>Zhang</surname><given-names>ZH</given-names></name><name><surname>Ling</surname><given-names>Y</given-names></name><name><surname>Xu</surname><given-names>WY</given-names></name><name><surname>Su</surname><given-names>Z</given-names></name></person-group><article-title>PNRD: a plant non-coding RNA database</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><issue>D1</issue><fpage>D982</fpage><lpage>D989</lpage><pub-id pub-id-type="doi">10.1093/nar/gku1162</pub-id><pub-id pub-id-type="pmid">25398903</pub-id></element-citation></ref><ref id="CR72"><label>72.</label><mixed-citation publication-type="other">University CA. PNRD plant non-coding RNA database. 2013. <ext-link ext-link-type="uri" xlink:href="http://structuralbiology.cau.edu.cn/PNRD/">http://structuralbiology.cau.edu.cn/PNRD/</ext-link>. Accessed 3 Aug 2017.</mixed-citation></ref><ref id="CR73"><label>73.</label><mixed-citation publication-type="other">Center for Bioinformatics PU. PlantRegMap Plant Transcriptional Regulatory Map.&#x000a0;2016. <ext-link ext-link-type="uri" xlink:href="http://plantregmap.cbi.pku.edu.cn/download.php%23go-annotation">http://plantregmap.cbi.pku.edu.cn/download.php#go-annotation</ext-link>. Accessed 6 Jun 2017.</mixed-citation></ref><ref id="CR74"><label>74.</label><mixed-citation publication-type="other">University P. cell wall genomics. 2005. <ext-link ext-link-type="uri" xlink:href="https://cellwall.genomics.purdue.edu/index.html">https://cellwall.genomics.purdue.edu/index.html</ext-link>. Accessed 24 Oct 2016.</mixed-citation></ref><ref id="CR75"><label>75.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ono</surname><given-names>Y</given-names></name><name><surname>Asai</surname><given-names>K</given-names></name><name><surname>Hamada</surname><given-names>M</given-names></name></person-group><article-title>PBSIM: PacBio reads simulator-toward accurate genome assembly</article-title><source>Bioinformatics</source><year>2013</year><volume>29</volume><issue>1</issue><fpage>119</fpage><lpage>121</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bts649</pub-id><pub-id pub-id-type="pmid">23129296</pub-id></element-citation></ref><ref id="CR76"><label>76.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shendure</surname><given-names>J</given-names></name><name><surname>Ji</surname><given-names>H</given-names></name></person-group><article-title>Next-generation DNA sequencing</article-title><source>Nat Biotechnol</source><year>2008</year><volume>26</volume><issue>10</issue><fpage>1135</fpage><pub-id pub-id-type="doi">10.1038/nbt1486</pub-id><pub-id pub-id-type="pmid">18846087</pub-id></element-citation></ref><ref id="CR77"><label>77.</label><mixed-citation publication-type="other">Hamilton M. TAPIS. 2015. <ext-link ext-link-type="uri" xlink:href="https://bitbucket.org/comp_bio/tapis">https://bitbucket.org/comp_bio/tapis</ext-link>. Accessed 19 Jul 2017.</mixed-citation></ref><ref id="CR78"><label>78.</label><mixed-citation publication-type="other">Alexa A, Rahnenfuhrer J. Gene set enrichment analysis with topGO. <ext-link ext-link-type="uri" xlink:href="http://bioconductor.uib.no/2.7/bioc/vignettes/topGO/inst/doc/topGO.pdf">http://bioconductor.uib.no/2.7/bioc/vignettes/topGO/inst/doc/topGO.pdf</ext-link>. Accessed 10 May 2017.</mixed-citation></ref><ref id="CR79"><label>79.</label><mixed-citation publication-type="other">Center for Bioinformatics PU. Coding Potential Calculator. 2006. <ext-link ext-link-type="uri" xlink:href="http://cpc.cbi.pku.edu.cn/programs/run_cpc.jsp">http://cpc.cbi.pku.edu.cn/programs/run_cpc.jsp</ext-link>. Accessed 29 Oct 2017.</mixed-citation></ref><ref id="CR80"><label>80.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Moffatt</surname><given-names>MF</given-names></name><name><surname>Kabesch</surname><given-names>M</given-names></name><name><surname>Liang</surname><given-names>L</given-names></name><name><surname>Dixon</surname><given-names>AL</given-names></name><name><surname>Strachan</surname><given-names>D</given-names></name><name><surname>Heath</surname><given-names>S</given-names></name></person-group><article-title>Genetic variants regulating ORMDL3 expression contribute to the risk of childhood asthma</article-title><source>Nature.</source><year>2007</year><volume>448</volume><fpage>470</fpage><pub-id pub-id-type="doi">10.1038/nature06014</pub-id><pub-id pub-id-type="pmid">17611496</pub-id></element-citation></ref></ref-list></back></article>