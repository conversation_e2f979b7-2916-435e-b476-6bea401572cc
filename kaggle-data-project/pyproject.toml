[project]
name = "kaggle-data-project"
version = "0.1.0"
description = "A comprehensive data analysis toolkit with numpy, pandas, matplotlib, and pypdf"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = ["data-analysis", "pandas", "numpy", "matplotlib", "pdf", "kaggle"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Information Analysis",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "matplotlib>=3.10.3",
    "numpy>=2.3.0",
    "pandas>=2.3.0",
    "pypdf>=5.6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.0",
    "black",
    "flake8",
    "mypy",
]
jupyter = [
    "jupyter",
    "ipykernel",
]

[project.urls]
Homepage = "https://github.com/yourusername/kaggle-data-project"
Repository = "https://github.com/yourusername/kaggle-data-project"
Documentation = "https://github.com/yourusername/kaggle-data-project#readme"
Issues = "https://github.com/yourusername/kaggle-data-project/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[dependency-groups]
dev = [
    "pytest>=8.4.0",
]
