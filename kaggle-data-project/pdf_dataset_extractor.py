"""
Script para extrair dataset_ids de PDFs e as 10 palavras que os antecedem.

Este script:
1. Lê o arquivo train_labels.csv
2. Para cada artigo, carrega o PDF correspondente
3. Busca pelo dataset_id no texto do PDF (considerando variações)
4. Extrai as 10 palavras que antecedem o dataset_id encontrado
5. Para casos "Missing", extrai 10 palavras consecutivas aleatórias
6. Salva os resultados em um novo arquivo CSV na pasta results
"""

import sys
from pathlib import Path
import pandas as pd
import re
import random
from typing import List, Tuple, Optional

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from kaggle_data_project import PDFHandler


class DatasetExtractor:
    """Classe para extrair dataset_ids de PDFs e contexto."""
    
    def __init__(self, data_dir: str = "data", results_dir: str = "results"):
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.pdf_handler = PDFHandler()
        
        # Criar diretório de resultados se não existir
        self.results_dir.mkdir(exist_ok=True)
    
    def load_train_labels(self) -> pd.DataFrame:
        """Carrega o arquivo train_labels.csv."""
        labels_path = self.data_dir / "train_labels.csv"
        return pd.read_csv(labels_path)
    
    def normalize_dataset_id(self, dataset_id: str) -> List[str]:
        """
        Normaliza o dataset_id para diferentes variações que podem aparecer no PDF.
        
        Args:
            dataset_id: O dataset_id original
            
        Returns:
            Lista de variações possíveis do dataset_id
        """
        if dataset_id == "Missing":
            return []
        
        variations = [dataset_id]  # Versão completa
        
        # Se começar com https://, adicionar versão sem https://
        if dataset_id.startswith("https://"):
            without_https = dataset_id.replace("https://", "")
            variations.append(without_https)
            
            # Se contém doi.org, adicionar apenas a parte após doi.org/
            if "doi.org/" in without_https:
                after_doi = without_https.split("doi.org/", 1)[1]
                variations.append(after_doi)
        
        # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
        elif "doi.org/" in dataset_id:
            after_doi = dataset_id.split("doi.org/", 1)[1]
            variations.append(after_doi)
        
        return variations
    
    def extract_preceding_words(self, text: str, target: str, num_words: int = 10) -> Optional[str]:
        """
        Extrai as palavras que antecedem um target no texto.
        
        Args:
            text: Texto completo
            target: String a ser procurada
            num_words: Número de palavras a extrair antes do target
            
        Returns:
            String com as palavras precedentes ou None se não encontrado
        """
        # Escapar caracteres especiais para regex
        escaped_target = re.escape(target)
        
        # Procurar o target no texto (case insensitive)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            # Dividir em palavras e pegar as últimas num_words
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
        
        return None
    
    def extract_random_words(self, text: str, num_words: int = 10) -> str:
        """
        Extrai num_words palavras consecutivas aleatórias do texto.
        
        Args:
            text: Texto completo
            num_words: Número de palavras consecutivas a extrair
            
        Returns:
            String com as palavras consecutivas
        """
        words = re.findall(r'\b\w+\b', text)
        
        if len(words) < num_words:
            return ' '.join(words)
        
        # Escolher posição inicial aleatória
        start_pos = random.randint(0, len(words) - num_words)
        return ' '.join(words[start_pos:start_pos + num_words])
    
    def process_pdf(self, article_id: str, dataset_id: str) -> Tuple[str, str, str]:
        """
        Processa um PDF individual para extrair o contexto do dataset_id.
        
        Args:
            article_id: ID do artigo (nome do arquivo PDF)
            dataset_id: Dataset ID a ser procurado
            
        Returns:
            Tupla (article_id, dataset_id, preceding_words)
        """
        pdf_path = self.data_dir / "train" / "PDF" / f"{article_id}.pdf"
        
        if not pdf_path.exists():
            print(f"PDF não encontrado: {pdf_path}")
            return article_id, dataset_id, "PDF_NOT_FOUND"
        
        try:
            # Carregar PDF
            self.pdf_handler.load_pdf(str(pdf_path))
            
            # Extrair texto completo
            full_text = self.pdf_handler.extract_text()
            
            if dataset_id == "Missing":
                # Para casos Missing, extrair 10 palavras aleatórias
                random_words = self.extract_random_words(full_text)
                return article_id, dataset_id, random_words
            
            # Tentar encontrar o dataset_id e suas variações
            variations = self.normalize_dataset_id(dataset_id)
            
            for variation in variations:
                preceding_words = self.extract_preceding_words(full_text, variation)
                if preceding_words:
                    return article_id, dataset_id, preceding_words
            
            # Se não encontrou nenhuma variação, retornar indicação
            return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
            
        except Exception as e:
            print(f"Erro ao processar {pdf_path}: {e}")
            return article_id, dataset_id, f"ERROR: {str(e)}"
    
    def process_all_pdfs(self, max_files: Optional[int] = None) -> pd.DataFrame:
        """
        Processa todos os PDFs listados no train_labels.csv.
        
        Args:
            max_files: Número máximo de arquivos a processar (para teste)
            
        Returns:
            DataFrame com os resultados
        """
        # Carregar labels
        labels_df = self.load_train_labels()
        
        if max_files:
            labels_df = labels_df.head(max_files)
        
        results = []
        total_files = len(labels_df)
        
        print(f"Processando {total_files} entradas...")
        
        for idx, row in labels_df.iterrows():
            article_id = row['article_id']
            dataset_id = row['dataset_id']
            
            if idx % 50 == 0:  # Progress update a cada 50 arquivos
                print(f"Processando {idx + 1}/{total_files}: {article_id}")
            
            result = self.process_pdf(article_id, dataset_id)
            results.append(result)
        
        # Criar DataFrame com resultados
        results_df = pd.DataFrame(results, columns=[
            'article_id', 'dataset_id', 'preceding_words'
        ])
        
        return results_df
    
    def save_results(self, results_df: pd.DataFrame, filename: str = "pdf_extraction_results.csv"):
        """
        Salva os resultados em um arquivo CSV.
        
        Args:
            results_df: DataFrame com os resultados
            filename: Nome do arquivo de saída
        """
        output_path = self.results_dir / filename
        results_df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"Resultados salvos em: {output_path}")
        
        # Mostrar estatísticas
        total = len(results_df)
        found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
        missing = len(results_df[results_df['dataset_id'] == 'Missing'])
        
        print(f"\nEstatísticas:")
        print(f"Total de entradas: {total}")
        print(f"Dataset IDs encontrados: {found - missing}")
        print(f"Casos Missing: {missing}")
        print(f"Não encontrados/Erros: {total - found}")


def main():
    """Função principal."""
    print("=== Extrator de Dataset IDs de PDFs ===")
    print("Este script analisa PDFs para encontrar dataset_ids e extrair contexto.\n")
    
    # Inicializar extrator
    extractor = DatasetExtractor()
    
    # Perguntar se quer processar todos os arquivos ou apenas alguns para teste
    try:
        response = input("Processar todos os arquivos? (s/n) [padrão: n]: ").strip().lower()
        
        if response in ['s', 'sim', 'y', 'yes']:
            max_files = None
            print("Processando todos os arquivos...")
        else:
            try:
                max_files = int(input("Quantos arquivos processar para teste? [padrão: 10]: ") or "10")
            except ValueError:
                max_files = 10
            print(f"Processando {max_files} arquivos para teste...")
        
        # Processar PDFs
        results_df = extractor.process_all_pdfs(max_files=max_files)
        
        # Salvar resultados
        extractor.save_results(results_df)
        
        # Mostrar alguns exemplos
        print(f"\nPrimeiros 5 resultados:")
        print(results_df.head().to_string(index=False))
        
    except KeyboardInterrupt:
        print("\nProcessamento interrompido pelo usuário.")
    except Exception as e:
        print(f"Erro durante o processamento: {e}")


if __name__ == "__main__":
    main()
