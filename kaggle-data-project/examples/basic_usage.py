"""
Basic usage examples for the kaggle-data-project package.
"""

import sys
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from kaggle_data_project import DataProcessor, Visualizer, PDFHandler
import pandas as pd
import numpy as np


def example_data_processing():
    """Example of using DataProcessor."""
    print("=== Data Processing Example ===")
    
    # Create sample data
    data = {
        'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'Age': [25, 30, 35, 28, 32, 29],
        'City': ['New York', 'London', 'Tokyo', 'Paris', 'Sydney', 'Berlin'],
        'Salary': [50000, 60000, 70000, 55000, 65000, 58000],
        'Department': ['IT', 'HR', 'IT', 'Finance', 'IT', 'HR']
    }
    
    df = pd.DataFrame(data)
    
    # Initialize processor
    processor = DataProcessor()
    processor.data = df  # Assign data directly for this example
    
    # Get basic info
    info = processor.basic_info()
    print("Data Info:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # Get numerical summary
    print("\nNumerical Summary:")
    print(processor.get_numerical_summary())
    
    # Get categorical summary
    print("\nCategorical Summary:")
    cat_summary = processor.get_categorical_summary()
    for col, counts in cat_summary.items():
        print(f"\n{col}:")
        print(counts)
    
    # Filter data
    filtered = processor.filter_data({'Department': 'IT', 'Age': {'min': 30}})
    print(f"\nFiltered data (IT department, age >= 30):")
    print(filtered)
    
    # Save data
    processor.save_data('data/processed_data.csv')
    print("\nData saved to data/processed_data.csv")


def example_visualization():
    """Example of using Visualizer."""
    print("\n=== Visualization Example ===")
    
    # Create sample data
    np.random.seed(42)
    n_points = 100
    
    data = pd.DataFrame({
        'x': np.linspace(0, 10, n_points),
        'y': np.sin(np.linspace(0, 10, n_points)) + np.random.normal(0, 0.1, n_points),
        'category': np.random.choice(['A', 'B', 'C'], n_points),
        'size': np.random.randint(10, 100, n_points)
    })
    
    # Initialize visualizer
    viz = Visualizer(figsize=(12, 8))
    
    # Create line plot
    viz.line_plot(data, 'x', 'y', title='Sine Wave with Noise', 
                  save_path='data/line_plot.png')
    print("Line plot saved to data/line_plot.png")
    
    # Create scatter plot
    viz.scatter_plot(data, 'x', 'y', color='size', 
                     title='Scatter Plot with Size Mapping',
                     save_path='data/scatter_plot.png')
    print("Scatter plot saved to data/scatter_plot.png")
    
    # Create histogram
    viz.histogram(data, 'y', bins=20, title='Distribution of Y Values',
                  save_path='data/histogram.png')
    print("Histogram saved to data/histogram.png")
    
    # Create correlation heatmap
    numeric_data = data.select_dtypes(include=[np.number])
    viz.correlation_heatmap(numeric_data, title='Correlation Matrix',
                           save_path='data/correlation_heatmap.png')
    print("Correlation heatmap saved to data/correlation_heatmap.png")
    
    viz.close()


def example_pdf_handling():
    """Example of using PDFHandler."""
    print("\n=== PDF Handling Example ===")
    
    # First, create a sample PDF using matplotlib
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_pdf import PdfPages
    
    pdf_path = Path("data/sample_document.pdf")
    pdf_path.parent.mkdir(exist_ok=True)
    
    # Create a sample PDF
    with PdfPages(pdf_path) as pdf:
        # Page 1
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.7, 'Sample PDF Document', ha='center', va='center', 
                fontsize=20, fontweight='bold', transform=ax.transAxes)
        ax.text(0.5, 0.5, 'This is page 1 of the sample document.', 
                ha='center', va='center', fontsize=14, transform=ax.transAxes)
        ax.text(0.5, 0.3, 'Created with Python for demonstration purposes.', 
                ha='center', va='center', fontsize=12, transform=ax.transAxes)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
        
        # Page 2
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.7, 'Page 2', ha='center', va='center', 
                fontsize=18, fontweight='bold', transform=ax.transAxes)
        ax.text(0.5, 0.5, 'This document contains multiple pages.', 
                ha='center', va='center', fontsize=14, transform=ax.transAxes)
        ax.text(0.5, 0.3, 'Each page can contain different content.', 
                ha='center', va='center', fontsize=12, transform=ax.transAxes)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    print(f"Sample PDF created at {pdf_path}")
    
    # Now use PDFHandler
    pdf_handler = PDFHandler()
    
    # Load PDF
    pdf_handler.load_pdf(str(pdf_path))
    
    # Get PDF info
    info = pdf_handler.get_pdf_info()
    print("\nPDF Info:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # Extract text
    text = pdf_handler.extract_text()
    print(f"\nExtracted text (first 200 chars):")
    print(text[:200] + "..." if len(text) > 200 else text)
    
    # Extract text by page
    text_by_page = pdf_handler.extract_text_by_page()
    print(f"\nText by page:")
    for page_num, page_text in text_by_page.items():
        print(f"  Page {page_num}: {page_text[:100]}...")
    
    # Search for text
    search_results = pdf_handler.search_text("sample", case_sensitive=False)
    print(f"\nSearch results for 'sample':")
    for result in search_results:
        if 'error' not in result:
            print(f"  Page {result['page']}: '{result['match']}' at position {result['start']}")
    
    # Save text to file
    text_file = pdf_handler.save_text_to_file('data/extracted_text.txt')
    print(f"\nText saved to {text_file}")


def main():
    """Run all examples."""
    print("Running Kaggle Data Project Examples")
    print("=" * 50)
    
    try:
        example_data_processing()
        example_visualization()
        example_pdf_handling()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        print("Check the 'data/' directory for generated files.")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
