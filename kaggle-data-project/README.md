# Kaggle Data Project

A comprehensive data analysis toolkit built with Python, featuring utilities for data processing, visualization, and PDF handling. This project is ready to use with `uv` and includes popular data science libraries: numpy, pandas, matplotlib, and pypdf.

## Features

- **Data Processing**: Comprehensive data manipulation and analysis with pandas and numpy
- **Visualization**: Create beautiful plots and charts with matplotlib
- **PDF Handling**: Extract text, split, merge, and analyze PDF documents
- **Modern Python**: Built with Python 3.12+ and managed with uv
- **Ready to Use**: Pre-configured project structure with examples

## Quick Start

### Prerequisites

- Python 3.12+
- [uv](https://docs.astral.sh/uv/) package manager

### Installation

1. Clone or download this project
2. Navigate to the project directory:
   ```bash
   cd kaggle-data-project
   ```

3. The project is already set up with uv! The virtual environment and dependencies are ready to use.

### Running the Examples

Run the main demonstration:
```bash
uv run python main.py
```

Run the basic usage examples:
```bash
uv run python examples/basic_usage.py
```

## Project Structure

```
kaggle-data-project/
├── src/
│   └── kaggle_data_project/
│       ├── __init__.py
│       ├── data_processor.py    # Data processing utilities
│       ├── visualizer.py        # Visualization tools
│       └── pdf_handler.py       # PDF handling utilities
├── examples/
│   └── basic_usage.py          # Usage examples
├── data/                       # Generated data files
├── notebooks/                  # Jupyter notebooks (empty)
├── tests/                      # Unit tests (empty)
├── main.py                     # Main demonstration script
├── pyproject.toml             # Project configuration
└── README.md                  # This file
```

## Core Components

### DataProcessor

Handle data loading, cleaning, and basic analysis:

```python
from kaggle_data_project import DataProcessor

processor = DataProcessor()
data = processor.load_csv('your_data.csv')
info = processor.basic_info()
cleaned_data = processor.clean_data(drop_duplicates=True)
processor.save_data('cleaned_data.csv')
```

### Visualizer

Create various types of plots and visualizations:

```python
from kaggle_data_project import Visualizer

viz = Visualizer(figsize=(12, 8))
viz.line_plot(data, 'x', 'y', title='My Plot', save_path='plot.png')
viz.scatter_plot(data, 'x', 'y', color='category')
viz.histogram(data, 'column', bins=30)
viz.correlation_heatmap(data)
```

### PDFHandler

Work with PDF documents:

```python
from kaggle_data_project import PDFHandler

pdf = PDFHandler()
pdf.load_pdf('document.pdf')
text = pdf.extract_text()
results = pdf.search_text('keyword')
pdf.split_pdf('output_dir/', pages_per_file=1)
```

## Dependencies

This project includes the following key dependencies:

- **numpy** (≥2.3.0): Numerical computing
- **pandas** (≥2.3.0): Data manipulation and analysis
- **matplotlib** (≥3.10.3): Plotting and visualization
- **pypdf** (≥5.6.0): PDF processing

All dependencies are automatically managed by uv.

## Usage Examples

### Data Analysis Workflow

```python
from kaggle_data_project import DataProcessor, Visualizer

# Load and process data
processor = DataProcessor()
data = processor.load_csv('sales_data.csv')
data = processor.clean_data(drop_duplicates=True, fill_na_method='drop')

# Get insights
print(processor.basic_info())
print(processor.get_numerical_summary())

# Create visualizations
viz = Visualizer()
viz.line_plot(data, 'date', 'sales', title='Sales Over Time')
viz.histogram(data, 'sales', title='Sales Distribution')
viz.correlation_heatmap(data, title='Feature Correlations')
```

### PDF Text Extraction

```python
from kaggle_data_project import PDFHandler

pdf = PDFHandler()
pdf.load_pdf('research_paper.pdf')

# Get document info
info = pdf.get_pdf_info()
print(f"Document has {info['num_pages']} pages")

# Extract text from specific pages
text = pdf.extract_text(page_numbers=[0, 1, 2])  # First 3 pages

# Search for keywords
results = pdf.search_text('machine learning', case_sensitive=False)
for result in results:
    print(f"Found on page {result['page']}: {result['context']}")
```

## Development

### Adding Dependencies

Use uv to add new dependencies:
```bash
uv add package_name
```

### Running Tests

```bash
uv run python -m pytest tests/
```

### Code Style

The project follows Python best practices. You can add linting tools:
```bash
uv add --dev black flake8 mypy
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source. Add your preferred license here.

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure you're running commands with `uv run`
2. **Missing data directory**: The examples will create the `data/` directory automatically
3. **PDF creation issues**: Some examples create PDFs using matplotlib, which requires a display backend

### Getting Help

- Check the examples in the `examples/` directory
- Review the docstrings in the source code
- Run `uv run python main.py` to see all features in action