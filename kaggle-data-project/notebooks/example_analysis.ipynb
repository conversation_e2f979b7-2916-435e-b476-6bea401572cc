{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Kaggle Data Project - Example Analysis\n", "\n", "This notebook demonstrates how to use the kaggle-data-project package for data analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the src directory to the path\n", "sys.path.insert(0, str(Path('..') / 'src'))\n", "\n", "from kaggle_data_project import DataProcessor, Visualizer, PDFHandler\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Processing Example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample data\n", "data = {\n", "    'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],\n", "    'Age': [25, 30, 35, 28, 32],\n", "    'City': ['New York', 'London', 'Tokyo', 'Paris', 'Sydney'],\n", "    'Salary': [50000, 60000, 70000, 55000, 65000]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "print(\"Sample Data:\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize DataProcessor\n", "processor = DataProcessor()\n", "processor.data = df\n", "\n", "# Get basic information\n", "info = processor.basic_info()\n", "print(\"Data Info:\")\n", "for key, value in info.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get numerical summary\n", "print(\"Numerical Summary:\")\n", "processor.get_numerical_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Visualization Example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample data for visualization\n", "np.random.seed(42)\n", "viz_data = pd.DataFrame({\n", "    'x': np.linspace(0, 10, 100),\n", "    'y': np.sin(np.linspace(0, 10, 100)) + np.random.normal(0, 0.1, 100),\n", "    'category': np.random.choice(['A', 'B', 'C'], 100)\n", "})\n", "\n", "# Initialize Visualizer\n", "viz = Visualizer(figsize=(10, 6))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a line plot\n", "viz.line_plot(viz_data, 'x', 'y', title='Sine Wave with Noise')\n", "viz.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a scatter plot\n", "viz.scatter_plot(viz_data, 'x', 'y', title='Scatter Plot Example')\n", "viz.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a histogram\n", "viz.histogram(viz_data, 'y', bins=20, title='Distribution of Y Values')\n", "viz.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PDF Handling Example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize PDF Handler\n", "pdf_handler = PDFHandler()\n", "\n", "# Note: You would load an actual PDF file here\n", "# pdf_handler.load_pdf('path/to/your/document.pdf')\n", "# info = pdf_handler.get_pdf_info()\n", "# text = pdf_handler.extract_text()\n", "\n", "print(\"PDF Handler initialized. Load a PDF file to extract text and analyze documents.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "This notebook demonstrates the basic usage of the kaggle-data-project package. You can:\n", "\n", "1. **Process data** with DataProcessor for loading, cleaning, and analyzing datasets\n", "2. **Create visualizations** with Visualizer for various types of plots\n", "3. **Handle PDF documents** with PDFHandler for text extraction and document analysis\n", "\n", "For more examples, check the `examples/` directory in the project."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}